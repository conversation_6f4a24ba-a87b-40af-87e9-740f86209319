{"ast": null, "code": "import { nextTick } from 'vue';\nimport { createLoadingComponent } from './loading.mjs';\nimport { isClient } from '@vueuse/core';\nimport { isString } from '@vue/shared';\nimport { getStyle, addClass, removeClass } from '../../../utils/dom/style.mjs';\nlet fullscreenInstance = void 0;\nconst Loading = function (options = {}) {\n  if (!isClient) return void 0;\n  const resolved = resolveOptions(options);\n  if (resolved.fullscreen && fullscreenInstance) {\n    return fullscreenInstance;\n  }\n  const instance = createLoadingComponent({\n    ...resolved,\n    closed: () => {\n      var _a;\n      (_a = resolved.closed) == null ? void 0 : _a.call(resolved);\n      if (resolved.fullscreen) fullscreenInstance = void 0;\n    }\n  });\n  addStyle(resolved, resolved.parent, instance);\n  addClassList(resolved, resolved.parent, instance);\n  resolved.parent.vLoadingAddClassList = () => addClassList(resolved, resolved.parent, instance);\n  let loadingNumber = resolved.parent.getAttribute(\"loading-number\");\n  if (!loadingNumber) {\n    loadingNumber = \"1\";\n  } else {\n    loadingNumber = `${Number.parseInt(loadingNumber) + 1}`;\n  }\n  resolved.parent.setAttribute(\"loading-number\", loadingNumber);\n  resolved.parent.appendChild(instance.$el);\n  nextTick(() => instance.visible.value = resolved.visible);\n  if (resolved.fullscreen) {\n    fullscreenInstance = instance;\n  }\n  return instance;\n};\nconst resolveOptions = options => {\n  var _a, _b, _c, _d;\n  let target;\n  if (isString(options.target)) {\n    target = (_a = document.querySelector(options.target)) != null ? _a : document.body;\n  } else {\n    target = options.target || document.body;\n  }\n  return {\n    parent: target === document.body || options.body ? document.body : target,\n    background: options.background || \"\",\n    svg: options.svg || \"\",\n    svgViewBox: options.svgViewBox || \"\",\n    spinner: options.spinner || false,\n    text: options.text || \"\",\n    fullscreen: target === document.body && ((_b = options.fullscreen) != null ? _b : true),\n    lock: (_c = options.lock) != null ? _c : false,\n    customClass: options.customClass || \"\",\n    visible: (_d = options.visible) != null ? _d : true,\n    beforeClose: options.beforeClose,\n    closed: options.closed,\n    target\n  };\n};\nconst addStyle = async (options, parent, instance) => {\n  const {\n    nextZIndex\n  } = instance.vm.zIndex || instance.vm._.exposed.zIndex;\n  const maskStyle = {};\n  if (options.fullscreen) {\n    instance.originalPosition.value = getStyle(document.body, \"position\");\n    instance.originalOverflow.value = getStyle(document.body, \"overflow\");\n    maskStyle.zIndex = nextZIndex();\n  } else if (options.parent === document.body) {\n    instance.originalPosition.value = getStyle(document.body, \"position\");\n    await nextTick();\n    for (const property of [\"top\", \"left\"]) {\n      const scroll = property === \"top\" ? \"scrollTop\" : \"scrollLeft\";\n      maskStyle[property] = `${options.target.getBoundingClientRect()[property] + document.body[scroll] + document.documentElement[scroll] - Number.parseInt(getStyle(document.body, `margin-${property}`), 10)}px`;\n    }\n    for (const property of [\"height\", \"width\"]) {\n      maskStyle[property] = `${options.target.getBoundingClientRect()[property]}px`;\n    }\n  } else {\n    instance.originalPosition.value = getStyle(parent, \"position\");\n  }\n  for (const [key, value] of Object.entries(maskStyle)) {\n    instance.$el.style[key] = value;\n  }\n};\nconst addClassList = (options, parent, instance) => {\n  const ns = instance.vm.ns || instance.vm._.exposed.ns;\n  if (![\"absolute\", \"fixed\", \"sticky\"].includes(instance.originalPosition.value)) {\n    addClass(parent, ns.bm(\"parent\", \"relative\"));\n  } else {\n    removeClass(parent, ns.bm(\"parent\", \"relative\"));\n  }\n  if (options.fullscreen && options.lock) {\n    addClass(parent, ns.bm(\"parent\", \"hidden\"));\n  } else {\n    removeClass(parent, ns.bm(\"parent\", \"hidden\"));\n  }\n};\nexport { Loading };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}