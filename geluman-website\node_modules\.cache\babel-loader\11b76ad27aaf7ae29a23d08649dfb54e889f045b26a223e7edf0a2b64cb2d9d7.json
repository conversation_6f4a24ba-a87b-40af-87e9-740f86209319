{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nimport { isClient } from '@vueuse/core';\nconst messageTypes = [\"success\", \"info\", \"warning\", \"error\"];\nconst messageDefaults = mutable({\n  customClass: \"\",\n  dangerouslyUseHTMLString: false,\n  duration: 3e3,\n  icon: void 0,\n  id: \"\",\n  message: \"\",\n  onClose: void 0,\n  showClose: false,\n  type: \"info\",\n  plain: false,\n  offset: 16,\n  zIndex: 0,\n  grouping: false,\n  repeatNum: 1,\n  appendTo: isClient ? document.body : void 0\n});\nconst messageProps = buildProps({\n  customClass: {\n    type: String,\n    default: messageDefaults.customClass\n  },\n  dangerouslyUseHTMLString: {\n    type: Boolean,\n    default: messageDefaults.dangerouslyUseHTMLString\n  },\n  duration: {\n    type: Number,\n    default: messageDefaults.duration\n  },\n  icon: {\n    type: iconPropType,\n    default: messageDefaults.icon\n  },\n  id: {\n    type: String,\n    default: messageDefaults.id\n  },\n  message: {\n    type: definePropType([String, Object, Function]),\n    default: messageDefaults.message\n  },\n  onClose: {\n    type: definePropType(Function),\n    default: messageDefaults.onClose\n  },\n  showClose: {\n    type: Boolean,\n    default: messageDefaults.showClose\n  },\n  type: {\n    type: String,\n    values: messageTypes,\n    default: messageDefaults.type\n  },\n  plain: {\n    type: Boolean,\n    default: messageDefaults.plain\n  },\n  offset: {\n    type: Number,\n    default: messageDefaults.offset\n  },\n  zIndex: {\n    type: Number,\n    default: messageDefaults.zIndex\n  },\n  grouping: {\n    type: Boolean,\n    default: messageDefaults.grouping\n  },\n  repeatNum: {\n    type: Number,\n    default: messageDefaults.repeatNum\n  }\n});\nconst messageEmits = {\n  destroy: () => true\n};\nexport { messageDefaults, messageEmits, messageProps, messageTypes };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}