{"ast": null, "code": "import Checkbox from './src/checkbox2.mjs';\nimport CheckboxButton from './src/checkbox-button.mjs';\nimport CheckboxGroup from './src/checkbox-group2.mjs';\nexport { checkboxGroupEmits, checkboxGroupProps } from './src/checkbox-group.mjs';\nexport { checkboxEmits, checkboxProps } from './src/checkbox.mjs';\nexport { checkboxGroupContextKey } from './src/constants.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElCheckbox = withInstall(Checkbox, {\n  CheckboxButton,\n  CheckboxGroup\n});\nconst ElCheckboxButton = withNoopInstall(CheckboxButton);\nconst ElCheckboxGroup = withNoopInstall(CheckboxGroup);\nexport { ElCheckbox, ElCheckboxButton, ElCheckboxGroup, ElCheckbox as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}