{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, normalizeStyle, renderSlot, toDisplayString, createCommentVNode, createBlock, withCtx, resolveDynamicComponent } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { WarningFilled, CircleCheck, CircleClose, Check, Close } from '@element-plus/icons-vue';\nimport { progressProps } from './progress2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isFunction, isString } from '@vue/shared';\nconst __default__ = defineComponent({\n  name: \"ElProgress\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: progressProps,\n  setup(__props) {\n    const props = __props;\n    const STATUS_COLOR_MAP = {\n      success: \"#13ce66\",\n      exception: \"#ff4949\",\n      warning: \"#e6a23c\",\n      default: \"#20a0ff\"\n    };\n    const ns = useNamespace(\"progress\");\n    const barStyle = computed(() => {\n      const barStyle2 = {\n        width: `${props.percentage}%`,\n        animationDuration: `${props.duration}s`\n      };\n      const color = getCurrentColor(props.percentage);\n      if (color.includes(\"gradient\")) {\n        barStyle2.background = color;\n      } else {\n        barStyle2.backgroundColor = color;\n      }\n      return barStyle2;\n    });\n    const relativeStrokeWidth = computed(() => (props.strokeWidth / props.width * 100).toFixed(1));\n    const radius = computed(() => {\n      if ([\"circle\", \"dashboard\"].includes(props.type)) {\n        return Number.parseInt(`${50 - Number.parseFloat(relativeStrokeWidth.value) / 2}`, 10);\n      }\n      return 0;\n    });\n    const trackPath = computed(() => {\n      const r = radius.value;\n      const isDashboard = props.type === \"dashboard\";\n      return `\n          M 50 50\n          m 0 ${isDashboard ? \"\" : \"-\"}${r}\n          a ${r} ${r} 0 1 1 0 ${isDashboard ? \"-\" : \"\"}${r * 2}\n          a ${r} ${r} 0 1 1 0 ${isDashboard ? \"\" : \"-\"}${r * 2}\n          `;\n    });\n    const perimeter = computed(() => 2 * Math.PI * radius.value);\n    const rate = computed(() => props.type === \"dashboard\" ? 0.75 : 1);\n    const strokeDashoffset = computed(() => {\n      const offset = -1 * perimeter.value * (1 - rate.value) / 2;\n      return `${offset}px`;\n    });\n    const trailPathStyle = computed(() => ({\n      strokeDasharray: `${perimeter.value * rate.value}px, ${perimeter.value}px`,\n      strokeDashoffset: strokeDashoffset.value\n    }));\n    const circlePathStyle = computed(() => ({\n      strokeDasharray: `${perimeter.value * rate.value * (props.percentage / 100)}px, ${perimeter.value}px`,\n      strokeDashoffset: strokeDashoffset.value,\n      transition: \"stroke-dasharray 0.6s ease 0s, stroke 0.6s ease, opacity ease 0.6s\"\n    }));\n    const stroke = computed(() => {\n      let ret;\n      if (props.color) {\n        ret = getCurrentColor(props.percentage);\n      } else {\n        ret = STATUS_COLOR_MAP[props.status] || STATUS_COLOR_MAP.default;\n      }\n      return ret;\n    });\n    const statusIcon = computed(() => {\n      if (props.status === \"warning\") {\n        return WarningFilled;\n      }\n      if (props.type === \"line\") {\n        return props.status === \"success\" ? CircleCheck : CircleClose;\n      } else {\n        return props.status === \"success\" ? Check : Close;\n      }\n    });\n    const progressTextSize = computed(() => {\n      return props.type === \"line\" ? 12 + props.strokeWidth * 0.4 : props.width * 0.111111 + 2;\n    });\n    const content = computed(() => props.format(props.percentage));\n    function getColors(color) {\n      const span = 100 / color.length;\n      const seriesColors = color.map((seriesColor, index) => {\n        if (isString(seriesColor)) {\n          return {\n            color: seriesColor,\n            percentage: (index + 1) * span\n          };\n        }\n        return seriesColor;\n      });\n      return seriesColors.sort((a, b) => a.percentage - b.percentage);\n    }\n    const getCurrentColor = percentage => {\n      var _a;\n      const {\n        color\n      } = props;\n      if (isFunction(color)) {\n        return color(percentage);\n      } else if (isString(color)) {\n        return color;\n      } else {\n        const colors = getColors(color);\n        for (const color2 of colors) {\n          if (color2.percentage > percentage) return color2.color;\n        }\n        return (_a = colors[colors.length - 1]) == null ? void 0 : _a.color;\n      }\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ns).b(), unref(ns).m(_ctx.type), unref(ns).is(_ctx.status), {\n          [unref(ns).m(\"without-text\")]: !_ctx.showText,\n          [unref(ns).m(\"text-inside\")]: _ctx.textInside\n        }]),\n        role: \"progressbar\",\n        \"aria-valuenow\": _ctx.percentage,\n        \"aria-valuemin\": \"0\",\n        \"aria-valuemax\": \"100\"\n      }, [_ctx.type === \"line\" ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).b(\"bar\"))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).be(\"bar\", \"outer\")),\n        style: normalizeStyle({\n          height: `${_ctx.strokeWidth}px`\n        })\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass([unref(ns).be(\"bar\", \"inner\"), {\n          [unref(ns).bem(\"bar\", \"inner\", \"indeterminate\")]: _ctx.indeterminate\n        }, {\n          [unref(ns).bem(\"bar\", \"inner\", \"striped\")]: _ctx.striped\n        }, {\n          [unref(ns).bem(\"bar\", \"inner\", \"striped-flow\")]: _ctx.stripedFlow\n        }]),\n        style: normalizeStyle(unref(barStyle))\n      }, [(_ctx.showText || _ctx.$slots.default) && _ctx.textInside ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).be(\"bar\", \"innerText\"))\n      }, [renderSlot(_ctx.$slots, \"default\", {\n        percentage: _ctx.percentage\n      }, () => [createElementVNode(\"span\", null, toDisplayString(unref(content)), 1)])], 2)) : createCommentVNode(\"v-if\", true)], 6)], 6)], 2)) : (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(unref(ns).b(\"circle\")),\n        style: normalizeStyle({\n          height: `${_ctx.width}px`,\n          width: `${_ctx.width}px`\n        })\n      }, [(openBlock(), createElementBlock(\"svg\", {\n        viewBox: \"0 0 100 100\"\n      }, [createElementVNode(\"path\", {\n        class: normalizeClass(unref(ns).be(\"circle\", \"track\")),\n        d: unref(trackPath),\n        stroke: `var(${unref(ns).cssVarName(\"fill-color-light\")}, #e5e9f2)`,\n        \"stroke-linecap\": _ctx.strokeLinecap,\n        \"stroke-width\": unref(relativeStrokeWidth),\n        fill: \"none\",\n        style: normalizeStyle(unref(trailPathStyle))\n      }, null, 14, [\"d\", \"stroke\", \"stroke-linecap\", \"stroke-width\"]), createElementVNode(\"path\", {\n        class: normalizeClass(unref(ns).be(\"circle\", \"path\")),\n        d: unref(trackPath),\n        stroke: unref(stroke),\n        fill: \"none\",\n        opacity: _ctx.percentage ? 1 : 0,\n        \"stroke-linecap\": _ctx.strokeLinecap,\n        \"stroke-width\": unref(relativeStrokeWidth),\n        style: normalizeStyle(unref(circlePathStyle))\n      }, null, 14, [\"d\", \"stroke\", \"opacity\", \"stroke-linecap\", \"stroke-width\"])]))], 6)), (_ctx.showText || _ctx.$slots.default) && !_ctx.textInside ? (openBlock(), createElementBlock(\"div\", {\n        key: 2,\n        class: normalizeClass(unref(ns).e(\"text\")),\n        style: normalizeStyle({\n          fontSize: `${unref(progressTextSize)}px`\n        })\n      }, [renderSlot(_ctx.$slots, \"default\", {\n        percentage: _ctx.percentage\n      }, () => [!_ctx.status ? (openBlock(), createElementBlock(\"span\", {\n        key: 0\n      }, toDisplayString(unref(content)), 1)) : (openBlock(), createBlock(unref(ElIcon), {\n        key: 1\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(statusIcon))))]),\n        _: 1\n      }))])], 6)) : createCommentVNode(\"v-if\", true)], 10, [\"aria-valuenow\"]);\n    };\n  }\n});\nvar Progress = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"progress.vue\"]]);\nexport { Progress as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}