{"ast": null, "code": "import { watch, unref, nextTick } from 'vue';\nconst useDelayedRender = ({\n  indicator,\n  intermediateIndicator,\n  shouldSetIntermediate = () => true,\n  beforeShow,\n  afterShow,\n  afterHide,\n  beforeHide\n}) => {\n  watch(() => unref(indicator), val => {\n    if (val) {\n      beforeShow == null ? void 0 : beforeShow();\n      nextTick(() => {\n        if (!unref(indicator)) return;\n        if (shouldSetIntermediate(\"show\")) {\n          intermediateIndicator.value = true;\n        }\n      });\n    } else {\n      beforeHide == null ? void 0 : beforeHide();\n      nextTick(() => {\n        if (unref(indicator)) return;\n        if (shouldSetIntermediate(\"hide\")) {\n          intermediateIndicator.value = false;\n        }\n      });\n    }\n  });\n  watch(() => intermediateIndicator.value, val => {\n    if (val) {\n      afterShow == null ? void 0 : afterShow();\n    } else {\n      afterHide == null ? void 0 : afterHide();\n    }\n  });\n};\nexport { useDelayedRender };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}