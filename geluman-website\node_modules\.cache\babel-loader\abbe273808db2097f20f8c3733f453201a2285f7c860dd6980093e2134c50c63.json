{"ast": null, "code": "import apply from './_apply.js';\nimport baseRest from './_baseRest.js';\nimport isError from './isError.js';\n\n/**\n * Attempts to invoke `func`, returning either the result or the caught error\n * object. Any additional arguments are provided to `func` when it's invoked.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Util\n * @param {Function} func The function to attempt.\n * @param {...*} [args] The arguments to invoke `func` with.\n * @returns {*} Returns the `func` result or error object.\n * @example\n *\n * // Avoid throwing errors for invalid selectors.\n * var elements = _.attempt(function(selector) {\n *   return document.querySelectorAll(selector);\n * }, '>_>');\n *\n * if (_.isError(elements)) {\n *   elements = [];\n * }\n */\nvar attempt = baseRest(function (func, args) {\n  try {\n    return apply(func, undefined, args);\n  } catch (e) {\n    return isError(e) ? e : new Error(e);\n  }\n});\nexport default attempt;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}