{"ast": null, "code": "import { ElInfiniteScroll } from './components/infinite-scroll/index.mjs';\nimport { ElLoading } from './components/loading/index.mjs';\nimport { ElMessage } from './components/message/index.mjs';\nimport { ElMessageBox } from './components/message-box/index.mjs';\nimport { ElNotification } from './components/notification/index.mjs';\nimport { ElPopoverDirective } from './components/popover/index.mjs';\nvar Plugins = [ElInfiniteScroll, ElLoading, ElMessage, ElMessageBox, ElNotification, ElPopoverDirective];\nexport { Plugins as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}