{"ast": null, "code": "import { placements } from '@popperjs/core';\nimport { CircleClose } from '@element-plus/icons-vue';\nimport { disabledTimeListsProps } from '../props/shared.mjs';\nimport { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../../hooks/use-size/index.mjs';\nimport { useEmptyValuesProps } from '../../../../hooks/use-empty-values/index.mjs';\nimport { useAriaProps } from '../../../../hooks/use-aria/index.mjs';\nconst timePickerDefaultProps = buildProps({\n  id: {\n    type: definePropType([Array, String])\n  },\n  name: {\n    type: definePropType([Array, String])\n  },\n  popperClass: {\n    type: String,\n    default: \"\"\n  },\n  format: String,\n  valueFormat: String,\n  dateFormat: String,\n  timeFormat: String,\n  type: {\n    type: String,\n    default: \"\"\n  },\n  clearable: {\n    type: Boolean,\n    default: true\n  },\n  clearIcon: {\n    type: definePropType([String, Object]),\n    default: CircleClose\n  },\n  editable: {\n    type: Boolean,\n    default: true\n  },\n  prefixIcon: {\n    type: definePropType([String, Object]),\n    default: \"\"\n  },\n  size: useSizeProp,\n  readonly: Boolean,\n  disabled: Boolean,\n  placeholder: {\n    type: String,\n    default: \"\"\n  },\n  popperOptions: {\n    type: definePropType(Object),\n    default: () => ({})\n  },\n  modelValue: {\n    type: definePropType([Date, Array, String, Number]),\n    default: \"\"\n  },\n  rangeSeparator: {\n    type: String,\n    default: \"-\"\n  },\n  startPlaceholder: String,\n  endPlaceholder: String,\n  defaultValue: {\n    type: definePropType([Date, Array])\n  },\n  defaultTime: {\n    type: definePropType([Date, Array])\n  },\n  isRange: Boolean,\n  ...disabledTimeListsProps,\n  disabledDate: {\n    type: Function\n  },\n  cellClassName: {\n    type: Function\n  },\n  shortcuts: {\n    type: Array,\n    default: () => []\n  },\n  arrowControl: Boolean,\n  tabindex: {\n    type: definePropType([String, Number]),\n    default: 0\n  },\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  unlinkPanels: Boolean,\n  placement: {\n    type: definePropType(String),\n    values: placements,\n    default: \"bottom\"\n  },\n  fallbackPlacements: {\n    type: definePropType(Array),\n    default: [\"bottom\", \"top\", \"right\", \"left\"]\n  },\n  ...useEmptyValuesProps,\n  ...useAriaProps([\"ariaLabel\"]),\n  showNow: {\n    type: Boolean,\n    default: true\n  }\n});\nconst timePickerRangeTriggerProps = buildProps({\n  id: {\n    type: definePropType(Array)\n  },\n  name: {\n    type: definePropType(Array)\n  },\n  modelValue: {\n    type: definePropType([Array, String])\n  },\n  startPlaceholder: String,\n  endPlaceholder: String,\n  disabled: Boolean\n});\nconst timePickerRngeTriggerProps = timePickerRangeTriggerProps;\nexport { timePickerDefaultProps, timePickerRangeTriggerProps, timePickerRngeTriggerProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}