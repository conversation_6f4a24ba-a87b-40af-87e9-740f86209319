{"ast": null, "code": "import { defineComponent, computed, openBlock, createBlock, resolveDynamicComponent, mergeProps, unref, withCtx, createElementBlock, Fragment, renderSlot, normalizeClass, createCommentVNode } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { useButton } from './use-button.mjs';\nimport { buttonProps, buttonEmits } from './button.mjs';\nimport { useButtonCustomStyle } from './button-custom.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElButton\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: buttonProps,\n  emits: buttonEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const buttonStyle = useButtonCustomStyle(props);\n    const ns = useNamespace(\"button\");\n    const {\n      _ref,\n      _size,\n      _type,\n      _disabled,\n      _props,\n      shouldAddSpace,\n      handleClick\n    } = useButton(props, emit);\n    const buttonKls = computed(() => [ns.b(), ns.m(_type.value), ns.m(_size.value), ns.is(\"disabled\", _disabled.value), ns.is(\"loading\", props.loading), ns.is(\"plain\", props.plain), ns.is(\"round\", props.round), ns.is(\"circle\", props.circle), ns.is(\"text\", props.text), ns.is(\"link\", props.link), ns.is(\"has-bg\", props.bg)]);\n    expose({\n      ref: _ref,\n      size: _size,\n      type: _type,\n      disabled: _disabled,\n      shouldAddSpace\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(resolveDynamicComponent(_ctx.tag), mergeProps({\n        ref_key: \"_ref\",\n        ref: _ref\n      }, unref(_props), {\n        class: unref(buttonKls),\n        style: unref(buttonStyle),\n        onClick: unref(handleClick)\n      }), {\n        default: withCtx(() => [_ctx.loading ? (openBlock(), createElementBlock(Fragment, {\n          key: 0\n        }, [_ctx.$slots.loading ? renderSlot(_ctx.$slots, \"loading\", {\n          key: 0\n        }) : (openBlock(), createBlock(unref(ElIcon), {\n          key: 1,\n          class: normalizeClass(unref(ns).is(\"loading\"))\n        }, {\n          default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.loadingIcon)))]),\n          _: 1\n        }, 8, [\"class\"]))], 64)) : _ctx.icon || _ctx.$slots.icon ? (openBlock(), createBlock(unref(ElIcon), {\n          key: 1\n        }, {\n          default: withCtx(() => [_ctx.icon ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.icon), {\n            key: 0\n          })) : renderSlot(_ctx.$slots, \"icon\", {\n            key: 1\n          })]),\n          _: 3\n        })) : createCommentVNode(\"v-if\", true), _ctx.$slots.default ? (openBlock(), createElementBlock(\"span\", {\n          key: 2,\n          class: normalizeClass({\n            [unref(ns).em(\"text\", \"expand\")]: unref(shouldAddSpace)\n          })\n        }, [renderSlot(_ctx.$slots, \"default\")], 2)) : createCommentVNode(\"v-if\", true)]),\n        _: 3\n      }, 16, [\"class\", \"style\", \"onClick\"]);\n    };\n  }\n});\nvar Button = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"button.vue\"]]);\nexport { Button as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}