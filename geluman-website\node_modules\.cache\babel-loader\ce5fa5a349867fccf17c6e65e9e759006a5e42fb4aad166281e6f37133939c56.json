{"ast": null, "code": "import { defineComponent, provide, h, renderSlot } from 'vue';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst Timeline = defineComponent({\n  name: \"ElTimeline\",\n  setup(_, {\n    slots\n  }) {\n    const ns = useNamespace(\"timeline\");\n    provide(\"timeline\", slots);\n    return () => {\n      return h(\"ul\", {\n        class: [ns.b()]\n      }, [renderSlot(slots, \"default\")]);\n    };\n  }\n});\nexport { Timeline as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}