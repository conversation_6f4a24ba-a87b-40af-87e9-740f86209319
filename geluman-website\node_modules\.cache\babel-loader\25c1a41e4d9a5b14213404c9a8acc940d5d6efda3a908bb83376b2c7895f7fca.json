{"ast": null, "code": "import { defineComponent, toRefs, reactive, openBlock, createBlock, normalizeProps, guardReactiveProps, withCtx, createVNode, mergeProps, renderSlot, unref, Transition, createCommentVNode, createElementBlock, Fragment } from 'vue';\nimport { pick } from 'lodash-unified';\nimport { ElTeleport } from '../../teleport/index.mjs';\nimport { tooltipV2ArrowProps } from './arrow.mjs';\nimport { tooltipV2ContentProps } from './content.mjs';\nimport { tooltipV2RootProps } from './root.mjs';\nimport { tooltipV2Props } from './tooltip.mjs';\nimport { tooltipV2TriggerProps } from './trigger.mjs';\nimport TooltipV2Root from './root2.mjs';\nimport TooltipV2Arrow from './arrow2.mjs';\nimport TooltipV2Content from './content2.mjs';\nimport TooltipV2Trigger from './trigger2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTooltipV2\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: tooltipV2Props,\n  setup(__props) {\n    const props = __props;\n    const refedProps = toRefs(props);\n    const arrowProps = reactive(pick(refedProps, Object.keys(tooltipV2ArrowProps)));\n    const contentProps = reactive(pick(refedProps, Object.keys(tooltipV2ContentProps)));\n    const rootProps = reactive(pick(refedProps, Object.keys(tooltipV2RootProps)));\n    const triggerProps = reactive(pick(refedProps, Object.keys(tooltipV2TriggerProps)));\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(TooltipV2Root, normalizeProps(guardReactiveProps(rootProps)), {\n        default: withCtx(({\n          open\n        }) => [createVNode(TooltipV2Trigger, mergeProps(triggerProps, {\n          nowrap: \"\"\n        }), {\n          default: withCtx(() => [renderSlot(_ctx.$slots, \"trigger\")]),\n          _: 3\n        }, 16), createVNode(unref(ElTeleport), {\n          to: _ctx.to,\n          disabled: !_ctx.teleported\n        }, {\n          default: withCtx(() => [_ctx.fullTransition ? (openBlock(), createBlock(Transition, normalizeProps(mergeProps({\n            key: 0\n          }, _ctx.transitionProps)), {\n            default: withCtx(() => [_ctx.alwaysOn || open ? (openBlock(), createBlock(TooltipV2Content, normalizeProps(mergeProps({\n              key: 0\n            }, contentProps)), {\n              arrow: withCtx(({\n                style,\n                side\n              }) => [_ctx.showArrow ? (openBlock(), createBlock(TooltipV2Arrow, mergeProps({\n                key: 0\n              }, arrowProps, {\n                style,\n                side\n              }), null, 16, [\"style\", \"side\"])) : createCommentVNode(\"v-if\", true)]),\n              default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n              _: 3\n            }, 16)) : createCommentVNode(\"v-if\", true)]),\n            _: 2\n          }, 1040)) : (openBlock(), createElementBlock(Fragment, {\n            key: 1\n          }, [_ctx.alwaysOn || open ? (openBlock(), createBlock(TooltipV2Content, normalizeProps(mergeProps({\n            key: 0\n          }, contentProps)), {\n            arrow: withCtx(({\n              style,\n              side\n            }) => [_ctx.showArrow ? (openBlock(), createBlock(TooltipV2Arrow, mergeProps({\n              key: 0\n            }, arrowProps, {\n              style,\n              side\n            }), null, 16, [\"style\", \"side\"])) : createCommentVNode(\"v-if\", true)]),\n            default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n            _: 3\n          }, 16)) : createCommentVNode(\"v-if\", true)], 64))]),\n          _: 2\n        }, 1032, [\"to\", \"disabled\"])]),\n        _: 3\n      }, 16);\n    };\n  }\n});\nvar TooltipV2 = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tooltip.vue\"]]);\nexport { TooltipV2 as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}