{"ast": null, "code": "import createWrap from './_createWrap.js';\nimport flatRest from './_flatRest.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_REARG_FLAG = 256;\n\n/**\n * Creates a function that invokes `func` with arguments arranged according\n * to the specified `indexes` where the argument value at the first index is\n * provided as the first argument, the argument value at the second index is\n * provided as the second argument, and so on.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {Function} func The function to rearrange arguments for.\n * @param {...(number|number[])} indexes The arranged argument indexes.\n * @returns {Function} Returns the new function.\n * @example\n *\n * var rearged = _.rearg(function(a, b, c) {\n *   return [a, b, c];\n * }, [2, 0, 1]);\n *\n * rearged('b', 'c', 'a')\n * // => ['a', 'b', 'c']\n */\nvar rearg = flatRest(function (func, indexes) {\n  return createWrap(func, WRAP_REARG_FLAG, undefined, undefined, undefined, indexes);\n});\nexport default rearg;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}