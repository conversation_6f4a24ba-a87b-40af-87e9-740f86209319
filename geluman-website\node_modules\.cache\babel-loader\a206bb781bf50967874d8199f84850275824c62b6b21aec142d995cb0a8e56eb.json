{"ast": null, "code": "import { ref, getCurrentInstance, inject, computed } from 'vue';\nimport { checkboxGroupContextKey } from '../constants.mjs';\nimport { isUndefined } from '../../../../utils/types.mjs';\nimport { isArray } from '@vue/shared';\nimport { UPDATE_MODEL_EVENT } from '../../../../constants/event.mjs';\nconst useCheckboxModel = props => {\n  const selfModel = ref(false);\n  const {\n    emit\n  } = getCurrentInstance();\n  const checkboxGroup = inject(checkboxGroupContextKey, void 0);\n  const isGroup = computed(() => isUndefined(checkboxGroup) === false);\n  const isLimitExceeded = ref(false);\n  const model = computed({\n    get() {\n      var _a, _b;\n      return isGroup.value ? (_a = checkboxGroup == null ? void 0 : checkboxGroup.modelValue) == null ? void 0 : _a.value : (_b = props.modelValue) != null ? _b : selfModel.value;\n    },\n    set(val) {\n      var _a, _b;\n      if (isGroup.value && isArray(val)) {\n        isLimitExceeded.value = ((_a = checkboxGroup == null ? void 0 : checkboxGroup.max) == null ? void 0 : _a.value) !== void 0 && val.length > (checkboxGroup == null ? void 0 : checkboxGroup.max.value) && val.length > model.value.length;\n        isLimitExceeded.value === false && ((_b = checkboxGroup == null ? void 0 : checkboxGroup.changeEvent) == null ? void 0 : _b.call(checkboxGroup, val));\n      } else {\n        emit(UPDATE_MODEL_EVENT, val);\n        selfModel.value = val;\n      }\n    }\n  });\n  return {\n    model,\n    isGroup,\n    isLimitExceeded\n  };\n};\nexport { useCheckboxModel };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}