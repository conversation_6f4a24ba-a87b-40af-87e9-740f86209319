{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { inject, provide } from 'vue';\nfunction useNodeExpandEventBroadcast(props) {\n  const parentNodeMap = inject(\"TreeNodeMap\", null);\n  const currentNodeMap = {\n    treeNodeExpand: node => {\n      if (props.node !== node) {\n        props.node.collapse();\n      }\n    },\n    children: []\n  };\n  if (parentNodeMap) {\n    parentNodeMap.children.push(currentNodeMap);\n  }\n  provide(\"TreeNodeMap\", currentNodeMap);\n  return {\n    broadcastExpanded: node => {\n      if (!props.accordion) return;\n      for (const childNode of currentNodeMap.children) {\n        childNode.treeNodeExpand(node);\n      }\n    }\n  };\n}\nexport { useNodeExpandEventBroadcast };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}