{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElement<PERSON><PERSON> as _createElementBlock, normalizeStyle as _normalizeStyle, createElementVNode as _createElementVNode, unref as _unref, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock, withDirectives as _withDirectives, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"home\"\n};\nconst _hoisted_2 = {\n  class: \"hero\"\n};\nconst _hoisted_3 = {\n  class: \"hero-background\"\n};\nconst _hoisted_4 = {\n  class: \"tech-particles\"\n};\nconst _hoisted_5 = {\n  class: \"hero-content\"\n};\nconst _hoisted_6 = {\n  class: \"brand-logo\"\n};\nconst _hoisted_7 = [\"src\"];\nconst _hoisted_8 = {\n  class: \"slogan-container\"\n};\nconst _hoisted_9 = {\n  class: \"dynamic-text\"\n};\nconst _hoisted_10 = {\n  class: \"hero-actions\"\n};\nconst _hoisted_11 = {\n  class: \"products\",\n  id: \"products\"\n};\nconst _hoisted_12 = {\n  class: \"products-carousel-container\"\n};\nconst _hoisted_13 = {\n  class: \"product-card\"\n};\nconst _hoisted_14 = {\n  class: \"product-header\"\n};\nconst _hoisted_15 = {\n  class: \"product-icon\"\n};\nconst _hoisted_16 = {\n  class: \"feature-container\"\n};\nconst _hoisted_17 = {\n  class: \"feature-list\"\n};\nconst _hoisted_18 = {\n  class: \"action-container\"\n};\nconst _hoisted_19 = {\n  class: \"product-card\"\n};\nconst _hoisted_20 = {\n  class: \"product-header\"\n};\nconst _hoisted_21 = {\n  class: \"product-icon\"\n};\nconst _hoisted_22 = {\n  class: \"feature-container\"\n};\nconst _hoisted_23 = {\n  class: \"feature-list\"\n};\nconst _hoisted_24 = {\n  class: \"action-container\"\n};\nconst _hoisted_25 = {\n  class: \"product-card\"\n};\nconst _hoisted_26 = {\n  class: \"product-header\"\n};\nconst _hoisted_27 = {\n  class: \"product-icon\"\n};\nconst _hoisted_28 = {\n  class: \"feature-container\"\n};\nconst _hoisted_29 = {\n  class: \"feature-list\"\n};\nconst _hoisted_30 = {\n  class: \"action-container\"\n};\nconst _hoisted_31 = {\n  class: \"product-card\"\n};\nconst _hoisted_32 = {\n  class: \"product-header\"\n};\nconst _hoisted_33 = {\n  class: \"product-icon\"\n};\nconst _hoisted_34 = {\n  class: \"feature-container\"\n};\nconst _hoisted_35 = {\n  class: \"feature-list\"\n};\nconst _hoisted_36 = {\n  class: \"action-container\"\n};\nconst _hoisted_37 = {\n  href: \"https://clock.geluman.cn\",\n  target: \"_blank\",\n  class: \"action-button\"\n};\nimport { ref, onMounted } from \"vue\";\nimport { useI18n } from \"vue-i18n\";\nimport { ArrowRight, Check, Monitor, View, Files, Brush, Share, MagicStick, Setting, Timer, Microphone, ChatLineRound, Scissors, Connection } from \"@element-plus/icons-vue\";\nimport { highlight, eyeshield, clock, logo, translator } from \"@/assets\";\nconst __default__ = {\n  name: \"HomePage\"\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props) {\n    const {\n      t\n    } = useI18n();\n    const slogans = [\"用魔法打造科技\", \"让你的浏览器变好玩\", \"每天都要元气满满~\", \"用黑科技拯救世界\"];\n    const currentIndex = ref(0);\n    const typeSlogan = async () => {\n      while (true) {\n        currentIndex.value = (currentIndex.value + 1) % slogans.length;\n        await new Promise(resolve => setTimeout(resolve, 3000));\n      }\n    };\n    const scrollToProducts = () => {\n      document.getElementById(\"products\").scrollIntoView({\n        behavior: \"smooth\"\n      });\n    };\n\n    // 自定义波纹指令\n    const vRipple = {\n      mounted(el) {\n        el.addEventListener(\"click\", e => {\n          const ripple = document.createElement(\"span\");\n          ripple.classList.add(\"ripple\");\n          el.appendChild(ripple);\n          const rect = el.getBoundingClientRect();\n          const size = Math.max(rect.width, rect.height);\n          const x = e.clientX - rect.left - size / 2;\n          const y = e.clientY - rect.top - size / 2;\n          ripple.style.width = ripple.style.height = `${size}px`;\n          ripple.style.left = `${x}px`;\n          ripple.style.top = `${y}px`;\n          setTimeout(() => ripple.remove(), 1000);\n        });\n      }\n    };\n    onMounted(() => {\n      typeSlogan();\n    });\n    return (_ctx, _cache) => {\n      const _component_el_icon = _resolveComponent(\"el-icon\");\n      const _component_el_button = _resolveComponent(\"el-button\");\n      const _component_router_link = _resolveComponent(\"router-link\");\n      const _component_el_carousel_item = _resolveComponent(\"el-carousel-item\");\n      const _component_el_carousel = _resolveComponent(\"el-carousel\");\n      return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(50, n => {\n        return _createElementVNode(\"span\", {\n          key: n,\n          class: \"particle\",\n          style: _normalizeStyle({\n            '--delay': `${Math.random() * 5}s`,\n            '--size': `${Math.random() * 3 + 1}px`,\n            '--x': `${Math.random() * 100}%`,\n            '--y': `${Math.random() * 100}%`\n          })\n        }, null, 4);\n      }), 64))]), _cache[0] || (_cache[0] = _createStaticVNode(\"<div class=\\\"grid-background\\\" data-v-4e6f12ef></div><div class=\\\"shooting-stars\\\" data-v-4e6f12ef><div class=\\\"shooting-star star-1\\\" data-v-4e6f12ef></div><div class=\\\"shooting-star star-2\\\" data-v-4e6f12ef></div><div class=\\\"shooting-star star-3\\\" data-v-4e6f12ef></div></div>\", 2))]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"img\", {\n        src: _unref(logo),\n        alt: \"格鲁曼\"\n      }, null, 8, _hoisted_7)]), _cache[3] || (_cache[3] = _createElementVNode(\"h1\", {\n        class: \"title\"\n      }, [_createTextVNode(\" 把科技变得\"), _createElementVNode(\"span\", {\n        class: \"gradient-text\"\n      }, \"萌萌哒~\")], -1)), _createElementVNode(\"div\", _hoisted_8, [_cache[1] || (_cache[1] = _createElementVNode(\"span\", {\n        class: \"static-text\"\n      }, \"我们\", -1)), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"span\", null, _toDisplayString(slogans[currentIndex.value]), 1)])]), _createElementVNode(\"div\", _hoisted_10, [_withDirectives((_openBlock(), _createBlock(_component_el_button, {\n        type: \"primary\",\n        size: \"large\",\n        round: \"\",\n        onClick: scrollToProducts,\n        class: \"primary-button\"\n      }, {\n        default: _withCtx(() => [_cache[2] || (_cache[2] = _createTextVNode(\" 戳这里发现宝藏 \")), _createVNode(_component_el_icon, {\n          class: \"el-icon--right\"\n        }, {\n          default: _withCtx(() => [_createVNode(_unref(ArrowRight))]),\n          _: 1\n        })]),\n        _: 1\n      })), [[vRipple]])])]), _cache[4] || (_cache[4] = _createStaticVNode(\"<div class=\\\"decorative-elements\\\" data-v-4e6f12ef><div class=\\\"tech-circle\\\" data-v-4e6f12ef></div><div class=\\\"floating-shapes\\\" data-v-4e6f12ef><div class=\\\"shape shape-1\\\" data-v-4e6f12ef></div><div class=\\\"shape shape-2\\\" data-v-4e6f12ef></div><div class=\\\"shape shape-3\\\" data-v-4e6f12ef></div></div></div>\", 1))]), _createElementVNode(\"section\", _hoisted_11, [_cache[29] || (_cache[29] = _createElementVNode(\"div\", {\n        class: \"section-header\"\n      }, [_createElementVNode(\"h2\", {\n        class: \"gradient-text\"\n      }, \"创新产品\"), _createElementVNode(\"p\", null, \"打造极致用户体验的数字化工具\")], -1)), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_carousel, {\n        interval: 5000,\n        type: \"card\",\n        height: \"450px\",\n        autoplay: true,\n        \"indicator-position\": \"outside\",\n        arrow: \"never\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_carousel_item, null, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(Monitor))]),\n            _: 1\n          })]), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n            class: \"product-title\"\n          }, [_createElementVNode(\"div\", {\n            class: \"product-badge\"\n          }, \"Browser Extension\"), _createElementVNode(\"h3\", null, \"智能高亮助手\")], -1))]), _cache[10] || (_cache[10] = _createElementVNode(\"p\", {\n            class: \"product-description\"\n          }, \" 专业的网页文本智能高亮工具，让阅读和学习更高效 \", -1)), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"ul\", _hoisted_17, [_createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(Files))]),\n            _: 1\n          }), _cache[6] || (_cache[6] = _createElementVNode(\"span\", null, \"多分类管理\", -1))]), _createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(Brush))]),\n            _: 1\n          }), _cache[7] || (_cache[7] = _createElementVNode(\"span\", null, \"颜色自定义\", -1))]), _createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(Share))]),\n            _: 1\n          }), _cache[8] || (_cache[8] = _createElementVNode(\"span\", null, \"配置分享\", -1))])])]), _createElementVNode(\"div\", _hoisted_18, [_withDirectives((_openBlock(), _createBlock(_component_router_link, {\n            to: \"/products/highlight\",\n            class: \"action-button\"\n          }, {\n            default: _withCtx(() => [_cache[9] || (_cache[9] = _createTextVNode(\" 了解更多 \")), _createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [_createVNode(_unref(ArrowRight))]),\n              _: 1\n            })]),\n            _: 1\n          })), [[vRipple]])])])]),\n          _: 1\n        }), _createVNode(_component_el_carousel_item, null, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(View))]),\n            _: 1\n          })]), _cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n            class: \"product-title\"\n          }, [_createElementVNode(\"div\", {\n            class: \"product-badge\"\n          }, \"Browser Extension\"), _createElementVNode(\"h3\", null, \"网页护眼助手\")], -1))]), _cache[16] || (_cache[16] = _createElementVNode(\"p\", {\n            class: \"product-description\"\n          }, \" 智能护眼工具，让网页浏览更舒适，保护您的眼睛健康 \", -1)), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"ul\", _hoisted_23, [_createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(Monitor))]),\n            _: 1\n          }), _cache[12] || (_cache[12] = _createElementVNode(\"span\", null, \"智能护眼模式\", -1))]), _createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(MagicStick))]),\n            _: 1\n          }), _cache[13] || (_cache[13] = _createElementVNode(\"span\", null, \"场景自适应\", -1))]), _createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(Setting))]),\n            _: 1\n          }), _cache[14] || (_cache[14] = _createElementVNode(\"span\", null, \"全局控制\", -1))])])]), _createElementVNode(\"div\", _hoisted_24, [_withDirectives((_openBlock(), _createBlock(_component_router_link, {\n            to: \"/products/eyeshield\",\n            class: \"action-button\"\n          }, {\n            default: _withCtx(() => [_cache[15] || (_cache[15] = _createTextVNode(\" 了解更多 \")), _createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [_createVNode(_unref(ArrowRight))]),\n              _: 1\n            })]),\n            _: 1\n          })), [[vRipple]])])])]),\n          _: 1\n        }), _createVNode(_component_el_carousel_item, null, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(ChatLineRound))]),\n            _: 1\n          })]), _cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n            class: \"product-title\"\n          }, [_createElementVNode(\"div\", {\n            class: \"product-badge\"\n          }, \"Browser Extension\"), _createElementVNode(\"h3\", null, \"智能翻译助手\")], -1))]), _cache[22] || (_cache[22] = _createElementVNode(\"p\", {\n            class: \"product-description\"\n          }, \" 高效、准确的网页文本翻译工具，帮助您跨越语言障碍 \", -1)), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"ul\", _hoisted_29, [_createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(ChatLineRound))]),\n            _: 1\n          }), _cache[18] || (_cache[18] = _createElementVNode(\"span\", null, \"多语言支持\", -1))]), _createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(Scissors))]),\n            _: 1\n          }), _cache[19] || (_cache[19] = _createElementVNode(\"span\", null, \"划词翻译\", -1))]), _createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(Connection))]),\n            _: 1\n          }), _cache[20] || (_cache[20] = _createElementVNode(\"span\", null, \"实时翻译\", -1))])])]), _createElementVNode(\"div\", _hoisted_30, [_withDirectives((_openBlock(), _createBlock(_component_router_link, {\n            to: \"/products/translator\",\n            class: \"action-button\"\n          }, {\n            default: _withCtx(() => [_cache[21] || (_cache[21] = _createTextVNode(\" 了解更多 \")), _createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [_createVNode(_unref(ArrowRight))]),\n              _: 1\n            })]),\n            _: 1\n          })), [[vRipple]])])])]),\n          _: 1\n        }), _createVNode(_component_el_carousel_item, null, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(Timer))]),\n            _: 1\n          })]), _cache[23] || (_cache[23] = _createElementVNode(\"div\", {\n            class: \"product-title\"\n          }, [_createElementVNode(\"div\", {\n            class: \"product-badge\"\n          }, \"Web App\"), _createElementVNode(\"h3\", null, \"智能语音时钟\")], -1))]), _cache[28] || (_cache[28] = _createElementVNode(\"p\", {\n            class: \"product-description\"\n          }, \" 优雅的时间管理助手，让时间提醒更自然、更贴心 \", -1)), _createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"ul\", _hoisted_35, [_createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(Microphone))]),\n            _: 1\n          }), _cache[24] || (_cache[24] = _createElementVNode(\"span\", null, \"语音播报\", -1))]), _createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(Monitor))]),\n            _: 1\n          }), _cache[25] || (_cache[25] = _createElementVNode(\"span\", null, \"优雅界面\", -1))]), _createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(Setting))]),\n            _: 1\n          }), _cache[26] || (_cache[26] = _createElementVNode(\"span\", null, \"智能管理\", -1))])])]), _createElementVNode(\"div\", _hoisted_36, [_withDirectives((_openBlock(), _createElementBlock(\"a\", _hoisted_37, [_cache[27] || (_cache[27] = _createTextVNode(\" 立即体验 \")), _createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_unref(ArrowRight))]),\n            _: 1\n          })])), [[vRipple]])])])]),\n          _: 1\n        })]),\n        _: 1\n      })])])]);\n    };\n  }\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}