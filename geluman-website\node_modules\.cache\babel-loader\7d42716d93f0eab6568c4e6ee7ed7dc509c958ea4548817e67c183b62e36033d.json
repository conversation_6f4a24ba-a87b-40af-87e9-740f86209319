{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { inject, getCurrentInstance, computed, watch, nextTick } from 'vue';\nimport { checkboxGroupContextKey } from '../constants.mjs';\nimport { useFormItem } from '../../../form/src/hooks/use-form-item.mjs';\nimport { debugWarn } from '../../../../utils/error.mjs';\nimport { CHANGE_EVENT } from '../../../../constants/event.mjs';\nconst useCheckboxEvent = (props, {\n  model,\n  isLimitExceeded,\n  hasOwnLabel,\n  isDisabled,\n  isLabeledByFormItem\n}) => {\n  const checkboxGroup = inject(checkboxGroupContextKey, void 0);\n  const {\n    formItem\n  } = useFormItem();\n  const {\n    emit\n  } = getCurrentInstance();\n  function getLabeledValue(value) {\n    var _a, _b, _c, _d;\n    return [true, props.trueValue, props.trueLabel].includes(value) ? (_b = (_a = props.trueValue) != null ? _a : props.trueLabel) != null ? _b : true : (_d = (_c = props.falseValue) != null ? _c : props.falseLabel) != null ? _d : false;\n  }\n  function emitChangeEvent(checked, e) {\n    emit(CHANGE_EVENT, getLabeledValue(checked), e);\n  }\n  function handleChange(e) {\n    if (isLimitExceeded.value) return;\n    const target = e.target;\n    emit(CHANGE_EVENT, getLabeledValue(target.checked), e);\n  }\n  async function onClickRoot(e) {\n    if (isLimitExceeded.value) return;\n    if (!hasOwnLabel.value && !isDisabled.value && isLabeledByFormItem.value) {\n      const eventTargets = e.composedPath();\n      const hasLabel = eventTargets.some(item => item.tagName === \"LABEL\");\n      if (!hasLabel) {\n        model.value = getLabeledValue([false, props.falseValue, props.falseLabel].includes(model.value));\n        await nextTick();\n        emitChangeEvent(model.value, e);\n      }\n    }\n  }\n  const validateEvent = computed(() => (checkboxGroup == null ? void 0 : checkboxGroup.validateEvent) || props.validateEvent);\n  watch(() => props.modelValue, () => {\n    if (validateEvent.value) {\n      formItem == null ? void 0 : formItem.validate(\"change\").catch(err => debugWarn(err));\n    }\n  });\n  return {\n    handleChange,\n    onClickRoot\n  };\n};\nexport { useCheckboxEvent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}