{"ast": null, "code": "import createGrid from '../builders/build-grid.mjs';\nimport { DEFAULT_DYNAMIC_LIST_ITEM_SIZE, AUTO_ALIGNMENT, CENTERED_ALIGNMENT, END_ALIGNMENT, START_ALIGNMENT, SMART_ALIGNMENT } from '../defaults.mjs';\nimport { isFunction } from '@vue/shared';\nimport { throwError } from '../../../../utils/error.mjs';\nimport { isNumber, isUndefined } from '../../../../utils/types.mjs';\nconst {\n  max,\n  min,\n  floor\n} = Math;\nconst SCOPE = \"ElDynamicSizeGrid\";\nconst ACCESS_SIZER_KEY_MAP = {\n  column: \"columnWidth\",\n  row: \"rowHeight\"\n};\nconst ACCESS_LAST_VISITED_KEY_MAP = {\n  column: \"lastVisitedColumnIndex\",\n  row: \"lastVisitedRowIndex\"\n};\nconst getItemFromCache = (props, index, gridCache, type) => {\n  const [cachedItems, sizer, lastVisited] = [gridCache[type], props[ACCESS_SIZER_KEY_MAP[type]], gridCache[ACCESS_LAST_VISITED_KEY_MAP[type]]];\n  if (index > lastVisited) {\n    let offset = 0;\n    if (lastVisited >= 0) {\n      const item = cachedItems[lastVisited];\n      offset = item.offset + item.size;\n    }\n    for (let i = lastVisited + 1; i <= index; i++) {\n      const size = sizer(i);\n      cachedItems[i] = {\n        offset,\n        size\n      };\n      offset += size;\n    }\n    gridCache[ACCESS_LAST_VISITED_KEY_MAP[type]] = index;\n  }\n  return cachedItems[index];\n};\nconst bs = (props, gridCache, low, high, offset, type) => {\n  while (low <= high) {\n    const mid = low + floor((high - low) / 2);\n    const currentOffset = getItemFromCache(props, mid, gridCache, type).offset;\n    if (currentOffset === offset) {\n      return mid;\n    } else if (currentOffset < offset) {\n      low = mid + 1;\n    } else {\n      high = mid - 1;\n    }\n  }\n  return max(0, low - 1);\n};\nconst es = (props, gridCache, idx, offset, type) => {\n  const total = type === \"column\" ? props.totalColumn : props.totalRow;\n  let exponent = 1;\n  while (idx < total && getItemFromCache(props, idx, gridCache, type).offset < offset) {\n    idx += exponent;\n    exponent *= 2;\n  }\n  return bs(props, gridCache, floor(idx / 2), min(idx, total - 1), offset, type);\n};\nconst findItem = (props, gridCache, offset, type) => {\n  const [cache, lastVisitedIndex] = [gridCache[type], gridCache[ACCESS_LAST_VISITED_KEY_MAP[type]]];\n  const lastVisitedItemOffset = lastVisitedIndex > 0 ? cache[lastVisitedIndex].offset : 0;\n  if (lastVisitedItemOffset >= offset) {\n    return bs(props, gridCache, 0, lastVisitedIndex, offset, type);\n  }\n  return es(props, gridCache, max(0, lastVisitedIndex), offset, type);\n};\nconst getEstimatedTotalHeight = ({\n  totalRow\n}, {\n  estimatedRowHeight,\n  lastVisitedRowIndex,\n  row\n}) => {\n  let sizeOfVisitedRows = 0;\n  if (lastVisitedRowIndex >= totalRow) {\n    lastVisitedRowIndex = totalRow - 1;\n  }\n  if (lastVisitedRowIndex >= 0) {\n    const item = row[lastVisitedRowIndex];\n    sizeOfVisitedRows = item.offset + item.size;\n  }\n  const unvisitedItems = totalRow - lastVisitedRowIndex - 1;\n  const sizeOfUnvisitedItems = unvisitedItems * estimatedRowHeight;\n  return sizeOfVisitedRows + sizeOfUnvisitedItems;\n};\nconst getEstimatedTotalWidth = ({\n  totalColumn\n}, {\n  column,\n  estimatedColumnWidth,\n  lastVisitedColumnIndex\n}) => {\n  let sizeOfVisitedColumns = 0;\n  if (lastVisitedColumnIndex > totalColumn) {\n    lastVisitedColumnIndex = totalColumn - 1;\n  }\n  if (lastVisitedColumnIndex >= 0) {\n    const item = column[lastVisitedColumnIndex];\n    sizeOfVisitedColumns = item.offset + item.size;\n  }\n  const unvisitedItems = totalColumn - lastVisitedColumnIndex - 1;\n  const sizeOfUnvisitedItems = unvisitedItems * estimatedColumnWidth;\n  return sizeOfVisitedColumns + sizeOfUnvisitedItems;\n};\nconst ACCESS_ESTIMATED_SIZE_KEY_MAP = {\n  column: getEstimatedTotalWidth,\n  row: getEstimatedTotalHeight\n};\nconst getOffset = (props, index, alignment, scrollOffset, cache, type, scrollBarWidth) => {\n  const [size, estimatedSizeAssociates] = [type === \"row\" ? props.height : props.width, ACCESS_ESTIMATED_SIZE_KEY_MAP[type]];\n  const item = getItemFromCache(props, index, cache, type);\n  const estimatedSize = estimatedSizeAssociates(props, cache);\n  const maxOffset = max(0, min(estimatedSize - size, item.offset));\n  const minOffset = max(0, item.offset - size + scrollBarWidth + item.size);\n  if (alignment === SMART_ALIGNMENT) {\n    if (scrollOffset >= minOffset - size && scrollOffset <= maxOffset + size) {\n      alignment = AUTO_ALIGNMENT;\n    } else {\n      alignment = CENTERED_ALIGNMENT;\n    }\n  }\n  switch (alignment) {\n    case START_ALIGNMENT:\n      {\n        return maxOffset;\n      }\n    case END_ALIGNMENT:\n      {\n        return minOffset;\n      }\n    case CENTERED_ALIGNMENT:\n      {\n        return Math.round(minOffset + (maxOffset - minOffset) / 2);\n      }\n    case AUTO_ALIGNMENT:\n    default:\n      {\n        if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n          return scrollOffset;\n        } else if (minOffset > maxOffset) {\n          return minOffset;\n        } else if (scrollOffset < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n      }\n  }\n};\nconst DynamicSizeGrid = createGrid({\n  name: \"ElDynamicSizeGrid\",\n  getColumnPosition: (props, idx, cache) => {\n    const item = getItemFromCache(props, idx, cache, \"column\");\n    return [item.size, item.offset];\n  },\n  getRowPosition: (props, idx, cache) => {\n    const item = getItemFromCache(props, idx, cache, \"row\");\n    return [item.size, item.offset];\n  },\n  getColumnOffset: (props, columnIndex, alignment, scrollLeft, cache, scrollBarWidth) => getOffset(props, columnIndex, alignment, scrollLeft, cache, \"column\", scrollBarWidth),\n  getRowOffset: (props, rowIndex, alignment, scrollTop, cache, scrollBarWidth) => getOffset(props, rowIndex, alignment, scrollTop, cache, \"row\", scrollBarWidth),\n  getColumnStartIndexForOffset: (props, scrollLeft, cache) => findItem(props, cache, scrollLeft, \"column\"),\n  getColumnStopIndexForStartIndex: (props, startIndex, scrollLeft, cache) => {\n    const item = getItemFromCache(props, startIndex, cache, \"column\");\n    const maxOffset = scrollLeft + props.width;\n    let offset = item.offset + item.size;\n    let stopIndex = startIndex;\n    while (stopIndex < props.totalColumn - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemFromCache(props, startIndex, cache, \"column\").size;\n    }\n    return stopIndex;\n  },\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n  getRowStartIndexForOffset: (props, scrollTop, cache) => findItem(props, cache, scrollTop, \"row\"),\n  getRowStopIndexForStartIndex: (props, startIndex, scrollTop, cache) => {\n    const {\n      totalRow,\n      height\n    } = props;\n    const item = getItemFromCache(props, startIndex, cache, \"row\");\n    const maxOffset = scrollTop + height;\n    let offset = item.size + item.offset;\n    let stopIndex = startIndex;\n    while (stopIndex < totalRow - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemFromCache(props, stopIndex, cache, \"row\").size;\n    }\n    return stopIndex;\n  },\n  injectToInstance: (instance, cache) => {\n    const resetAfter = ({\n      columnIndex,\n      rowIndex\n    }, forceUpdate) => {\n      var _a, _b;\n      forceUpdate = isUndefined(forceUpdate) ? true : forceUpdate;\n      if (isNumber(columnIndex)) {\n        cache.value.lastVisitedColumnIndex = Math.min(cache.value.lastVisitedColumnIndex, columnIndex - 1);\n      }\n      if (isNumber(rowIndex)) {\n        cache.value.lastVisitedRowIndex = Math.min(cache.value.lastVisitedRowIndex, rowIndex - 1);\n      }\n      (_a = instance.exposed) == null ? void 0 : _a.getItemStyleCache.value(-1, null, null);\n      if (forceUpdate) (_b = instance.proxy) == null ? void 0 : _b.$forceUpdate();\n    };\n    const resetAfterColumnIndex = (columnIndex, forceUpdate) => {\n      resetAfter({\n        columnIndex\n      }, forceUpdate);\n    };\n    const resetAfterRowIndex = (rowIndex, forceUpdate) => {\n      resetAfter({\n        rowIndex\n      }, forceUpdate);\n    };\n    Object.assign(instance.proxy, {\n      resetAfterColumnIndex,\n      resetAfterRowIndex,\n      resetAfter\n    });\n  },\n  initCache: ({\n    estimatedColumnWidth = DEFAULT_DYNAMIC_LIST_ITEM_SIZE,\n    estimatedRowHeight = DEFAULT_DYNAMIC_LIST_ITEM_SIZE\n  }) => {\n    const cache = {\n      column: {},\n      estimatedColumnWidth,\n      estimatedRowHeight,\n      lastVisitedColumnIndex: -1,\n      lastVisitedRowIndex: -1,\n      row: {}\n    };\n    return cache;\n  },\n  clearCache: false,\n  validateProps: ({\n    columnWidth,\n    rowHeight\n  }) => {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!isFunction(columnWidth)) {\n        throwError(SCOPE, `\n          \"columnWidth\" must be passed as function,\n            instead ${typeof columnWidth} was given.\n        `);\n      }\n      if (!isFunction(rowHeight)) {\n        throwError(SCOPE, `\n          \"rowHeight\" must be passed as function,\n            instead ${typeof rowHeight} was given.\n        `);\n      }\n    }\n  }\n});\nexport { DynamicSizeGrid as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}