{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nimport { isBoolean } from '../../../utils/types.mjs';\nconst ROOT_TREE_INJECTION_KEY = Symbol();\nconst EMPTY_NODE = {\n  key: -1,\n  level: -1,\n  data: {}\n};\nvar TreeOptionsEnum = /* @__PURE__ */(TreeOptionsEnum2 => {\n  TreeOptionsEnum2[\"KEY\"] = \"id\";\n  TreeOptionsEnum2[\"LABEL\"] = \"label\";\n  TreeOptionsEnum2[\"CHILDREN\"] = \"children\";\n  TreeOptionsEnum2[\"DISABLED\"] = \"disabled\";\n  TreeOptionsEnum2[\"CLASS\"] = \"\";\n  return TreeOptionsEnum2;\n})(TreeOptionsEnum || {});\nvar SetOperationEnum = /* @__PURE__ */(SetOperationEnum2 => {\n  SetOperationEnum2[\"ADD\"] = \"add\";\n  SetOperationEnum2[\"DELETE\"] = \"delete\";\n  return SetOperationEnum2;\n})(SetOperationEnum || {});\nconst itemSize = {\n  type: Number,\n  default: 26\n};\nconst treeProps = buildProps({\n  data: {\n    type: definePropType(Array),\n    default: () => mutable([])\n  },\n  emptyText: {\n    type: String\n  },\n  height: {\n    type: Number,\n    default: 200\n  },\n  props: {\n    type: definePropType(Object),\n    default: () => mutable({\n      children: \"children\" /* CHILDREN */,\n      label: \"label\" /* LABEL */,\n      disabled: \"disabled\" /* DISABLED */,\n      value: \"id\" /* KEY */,\n      class: \"\" /* CLASS */\n    })\n  },\n  highlightCurrent: {\n    type: Boolean,\n    default: false\n  },\n  showCheckbox: {\n    type: Boolean,\n    default: false\n  },\n  defaultCheckedKeys: {\n    type: definePropType(Array),\n    default: () => mutable([])\n  },\n  checkStrictly: {\n    type: Boolean,\n    default: false\n  },\n  defaultExpandedKeys: {\n    type: definePropType(Array),\n    default: () => mutable([])\n  },\n  indent: {\n    type: Number,\n    default: 16\n  },\n  itemSize,\n  icon: {\n    type: iconPropType\n  },\n  expandOnClickNode: {\n    type: Boolean,\n    default: true\n  },\n  checkOnClickNode: {\n    type: Boolean,\n    default: false\n  },\n  checkOnClickLeaf: {\n    type: Boolean,\n    default: true\n  },\n  currentNodeKey: {\n    type: definePropType([String, Number])\n  },\n  accordion: {\n    type: Boolean,\n    default: false\n  },\n  filterMethod: {\n    type: definePropType(Function)\n  },\n  perfMode: {\n    type: Boolean,\n    default: true\n  }\n});\nconst treeNodeProps = buildProps({\n  node: {\n    type: definePropType(Object),\n    default: () => mutable(EMPTY_NODE)\n  },\n  expanded: {\n    type: Boolean,\n    default: false\n  },\n  checked: {\n    type: Boolean,\n    default: false\n  },\n  indeterminate: {\n    type: Boolean,\n    default: false\n  },\n  showCheckbox: {\n    type: Boolean,\n    default: false\n  },\n  disabled: {\n    type: Boolean,\n    default: false\n  },\n  current: {\n    type: Boolean,\n    default: false\n  },\n  hiddenExpandIcon: {\n    type: Boolean,\n    default: false\n  },\n  itemSize\n});\nconst treeNodeContentProps = buildProps({\n  node: {\n    type: definePropType(Object),\n    required: true\n  }\n});\nconst NODE_CLICK = \"node-click\";\nconst NODE_DROP = \"node-drop\";\nconst NODE_EXPAND = \"node-expand\";\nconst NODE_COLLAPSE = \"node-collapse\";\nconst CURRENT_CHANGE = \"current-change\";\nconst NODE_CHECK = \"check\";\nconst NODE_CHECK_CHANGE = \"check-change\";\nconst NODE_CONTEXTMENU = \"node-contextmenu\";\nconst treeEmits = {\n  [NODE_CLICK]: (data, node, e) => data && node && e,\n  [NODE_DROP]: (data, node, e) => data && node && e,\n  [NODE_EXPAND]: (data, node) => data && node,\n  [NODE_COLLAPSE]: (data, node) => data && node,\n  [CURRENT_CHANGE]: (data, node) => data && node,\n  [NODE_CHECK]: (data, checkedInfo) => data && checkedInfo,\n  [NODE_CHECK_CHANGE]: (data, checked) => data && isBoolean(checked),\n  [NODE_CONTEXTMENU]: (evt, data, node) => evt && data && node\n};\nconst treeNodeEmits = {\n  click: (node, e) => !!(node && e),\n  drop: (node, e) => !!(node && e),\n  toggle: node => !!node,\n  check: (node, checked) => node && isBoolean(checked)\n};\nexport { CURRENT_CHANGE, NODE_CHECK, NODE_CHECK_CHANGE, NODE_CLICK, NODE_COLLAPSE, NODE_CONTEXTMENU, NODE_DROP, NODE_EXPAND, ROOT_TREE_INJECTION_KEY, SetOperationEnum, TreeOptionsEnum, treeEmits, treeNodeContentProps, treeNodeEmits, treeNodeProps, treeProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}