{"ast": null, "code": "import { createVNode } from 'vue';\nconst Overlay = (props, {\n  slots\n}) => {\n  var _a;\n  return createVNode(\"div\", {\n    \"class\": props.class,\n    \"style\": props.style\n  }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n};\nOverlay.displayName = \"ElTableV2Overlay\";\nvar Overlay$1 = Overlay;\nexport { Overlay$1 as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}