{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, inject, ref, computed, unref, onUpdated, createVNode, nextTick } from 'vue';\nimport { tableV2HeaderProps } from '../header.mjs';\nimport { enforceUnit } from '../utils.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { castArray } from 'lodash-unified';\nconst COMPONENT_NAME = \"ElTableV2Header\";\nconst TableV2Header = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2HeaderProps,\n  setup(props, {\n    slots,\n    expose\n  }) {\n    const ns = useNamespace(\"table-v2\");\n    const scrollLeftInfo = inject(\"tableV2GridScrollLeft\");\n    const headerRef = ref();\n    const headerStyle = computed(() => enforceUnit({\n      width: props.width,\n      height: props.height\n    }));\n    const rowStyle = computed(() => enforceUnit({\n      width: props.rowWidth,\n      height: props.height\n    }));\n    const headerHeights = computed(() => castArray(unref(props.headerHeight)));\n    const scrollToLeft = left => {\n      const headerEl = unref(headerRef);\n      nextTick(() => {\n        (headerEl == null ? void 0 : headerEl.scroll) && headerEl.scroll({\n          left\n        });\n      });\n    };\n    const renderFixedRows = () => {\n      const fixedRowClassName = ns.e(\"fixed-header-row\");\n      const {\n        columns,\n        fixedHeaderData,\n        rowHeight\n      } = props;\n      return fixedHeaderData == null ? void 0 : fixedHeaderData.map((fixedRowData, fixedRowIndex) => {\n        var _a;\n        const style = enforceUnit({\n          height: rowHeight,\n          width: \"100%\"\n        });\n        return (_a = slots.fixed) == null ? void 0 : _a.call(slots, {\n          class: fixedRowClassName,\n          columns,\n          rowData: fixedRowData,\n          rowIndex: -(fixedRowIndex + 1),\n          style\n        });\n      });\n    };\n    const renderDynamicRows = () => {\n      const dynamicRowClassName = ns.e(\"dynamic-header-row\");\n      const {\n        columns\n      } = props;\n      return unref(headerHeights).map((rowHeight, rowIndex) => {\n        var _a;\n        const style = enforceUnit({\n          width: \"100%\",\n          height: rowHeight\n        });\n        return (_a = slots.dynamic) == null ? void 0 : _a.call(slots, {\n          class: dynamicRowClassName,\n          columns,\n          headerIndex: rowIndex,\n          style\n        });\n      });\n    };\n    onUpdated(() => {\n      if (scrollLeftInfo == null ? void 0 : scrollLeftInfo.value) {\n        scrollToLeft(scrollLeftInfo.value);\n      }\n    });\n    expose({\n      scrollToLeft\n    });\n    return () => {\n      if (props.height <= 0) return;\n      return createVNode(\"div\", {\n        \"ref\": headerRef,\n        \"class\": props.class,\n        \"style\": unref(headerStyle),\n        \"role\": \"rowgroup\"\n      }, [createVNode(\"div\", {\n        \"style\": unref(rowStyle),\n        \"class\": ns.e(\"header\")\n      }, [renderDynamicRows(), renderFixedRows()])]);\n    };\n  }\n});\nvar Header = TableV2Header;\nexport { Header as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}