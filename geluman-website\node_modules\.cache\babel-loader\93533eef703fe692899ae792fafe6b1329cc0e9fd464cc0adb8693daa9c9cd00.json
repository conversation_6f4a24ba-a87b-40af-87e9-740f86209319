{"ast": null, "code": "import { dialogProps, dialogEmits } from '../../dialog/src/dialog.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst drawerProps = buildProps({\n  ...dialogProps,\n  direction: {\n    type: String,\n    default: \"rtl\",\n    values: [\"ltr\", \"rtl\", \"ttb\", \"btt\"]\n  },\n  size: {\n    type: [String, Number],\n    default: \"30%\"\n  },\n  withHeader: {\n    type: Boolean,\n    default: true\n  },\n  modalFade: {\n    type: Boolean,\n    default: true\n  },\n  headerAriaLevel: {\n    type: String,\n    default: \"2\"\n  }\n});\nconst drawerEmits = dialogEmits;\nexport { drawerEmits, drawerProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}