{"ast": null, "code": "/**\n * Take input from [0, n] and return it as [0, 1]\n * @hidden\n */\nexport function bound01(n, max) {\n  if (isOnePointZero(n)) {\n    n = '100%';\n  }\n  var isPercent = isPercentage(n);\n  n = max === 360 ? n : Math.min(max, Math.max(0, parseFloat(n)));\n  // Automatically convert percentage into number\n  if (isPercent) {\n    n = parseInt(String(n * max), 10) / 100;\n  }\n  // Handle floating point rounding errors\n  if (Math.abs(n - max) < 0.000001) {\n    return 1;\n  }\n  // Convert into [0, 1] range if it isn't already\n  if (max === 360) {\n    // If n is a hue given in degrees,\n    // wrap around out-of-range values into [0, 360] range\n    // then convert into [0, 1].\n    n = (n < 0 ? n % max + max : n % max) / parseFloat(String(max));\n  } else {\n    // If n not a hue given in degrees\n    // Convert into [0, 1] range if it isn't already.\n    n = n % max / parseFloat(String(max));\n  }\n  return n;\n}\n/**\n * Force a number between 0 and 1\n * @hidden\n */\nexport function clamp01(val) {\n  return Math.min(1, Math.max(0, val));\n}\n/**\n * Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n * <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\n * @hidden\n */\nexport function isOnePointZero(n) {\n  return typeof n === 'string' && n.indexOf('.') !== -1 && parseFloat(n) === 1;\n}\n/**\n * Check to see if string passed in is a percentage\n * @hidden\n */\nexport function isPercentage(n) {\n  return typeof n === 'string' && n.indexOf('%') !== -1;\n}\n/**\n * Return a valid alpha value [0,1] with all invalid values being set to 1\n * @hidden\n */\nexport function boundAlpha(a) {\n  a = parseFloat(a);\n  if (isNaN(a) || a < 0 || a > 1) {\n    a = 1;\n  }\n  return a;\n}\n/**\n * Replace a decimal with it's percentage value\n * @hidden\n */\nexport function convertToPercentage(n) {\n  if (n <= 1) {\n    return \"\".concat(Number(n) * 100, \"%\");\n  }\n  return n;\n}\n/**\n * Force a hex value to have 2 characters\n * @hidden\n */\nexport function pad2(c) {\n  return c.length === 1 ? '0' + c : String(c);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}