{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { defineComponent, useSlots, computed, provide, getCurrentInstance, openBlock, createElementBlock, normalizeClass, unref, createBlock, withCtx, normalizeStyle, renderSlot, createElementVNode, toDisplayString } from 'vue';\nimport { useTree } from './composables/useTree.mjs';\nimport ElTreeNode from './tree-node.mjs';\nimport { treeProps, treeEmits, ROOT_TREE_INJECTION_KEY } from './virtual-tree.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport FixedSizeList from '../../virtual-list/src/components/fixed-size-list.mjs';\nimport { formItemContextKey } from '../../form/src/constants.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTreeV2\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: treeProps,\n  emits: treeEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const slots = useSlots();\n    const treeNodeSize = computed(() => props.itemSize);\n    provide(ROOT_TREE_INJECTION_KEY, {\n      ctx: {\n        emit,\n        slots\n      },\n      props,\n      instance: getCurrentInstance()\n    });\n    provide(formItemContextKey, void 0);\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"tree\");\n    const {\n      flattenTree,\n      isNotEmpty,\n      listRef,\n      toggleExpand,\n      isExpanded,\n      isIndeterminate,\n      isChecked,\n      isDisabled,\n      isCurrent,\n      isForceHiddenExpandIcon,\n      handleNodeClick,\n      handleNodeDrop,\n      handleNodeCheck,\n      toggleCheckbox,\n      getCurrentNode,\n      getCurrentKey,\n      setCurrentKey,\n      getCheckedKeys,\n      getCheckedNodes,\n      getHalfCheckedKeys,\n      getHalfCheckedNodes,\n      setChecked,\n      setCheckedKeys,\n      filter,\n      setData,\n      getNode,\n      expandNode,\n      collapseNode,\n      setExpandedKeys,\n      scrollToNode,\n      scrollTo\n    } = useTree(props, emit);\n    expose({\n      toggleCheckbox,\n      getCurrentNode,\n      getCurrentKey,\n      setCurrentKey,\n      getCheckedKeys,\n      getCheckedNodes,\n      getHalfCheckedKeys,\n      getHalfCheckedNodes,\n      setChecked,\n      setCheckedKeys,\n      filter,\n      setData,\n      getNode,\n      expandNode,\n      collapseNode,\n      setExpandedKeys,\n      scrollToNode,\n      scrollTo\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ns).b(), {\n          [unref(ns).m(\"highlight-current\")]: _ctx.highlightCurrent\n        }]),\n        role: \"tree\"\n      }, [unref(isNotEmpty) ? (openBlock(), createBlock(unref(FixedSizeList), {\n        key: 0,\n        ref_key: \"listRef\",\n        ref: listRef,\n        \"class-name\": unref(ns).b(\"virtual-list\"),\n        data: unref(flattenTree),\n        total: unref(flattenTree).length,\n        height: _ctx.height,\n        \"item-size\": unref(treeNodeSize),\n        \"perf-mode\": _ctx.perfMode\n      }, {\n        default: withCtx(({\n          data,\n          index,\n          style\n        }) => [(openBlock(), createBlock(ElTreeNode, {\n          key: data[index].key,\n          style: normalizeStyle(style),\n          node: data[index],\n          expanded: unref(isExpanded)(data[index]),\n          \"show-checkbox\": _ctx.showCheckbox,\n          checked: unref(isChecked)(data[index]),\n          indeterminate: unref(isIndeterminate)(data[index]),\n          \"item-size\": unref(treeNodeSize),\n          disabled: unref(isDisabled)(data[index]),\n          current: unref(isCurrent)(data[index]),\n          \"hidden-expand-icon\": unref(isForceHiddenExpandIcon)(data[index]),\n          onClick: unref(handleNodeClick),\n          onToggle: unref(toggleExpand),\n          onCheck: unref(handleNodeCheck),\n          onDrop: unref(handleNodeDrop)\n        }, null, 8, [\"style\", \"node\", \"expanded\", \"show-checkbox\", \"checked\", \"indeterminate\", \"item-size\", \"disabled\", \"current\", \"hidden-expand-icon\", \"onClick\", \"onToggle\", \"onCheck\", \"onDrop\"]))]),\n        _: 1\n      }, 8, [\"class-name\", \"data\", \"total\", \"height\", \"item-size\", \"perf-mode\"])) : (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(unref(ns).e(\"empty-block\"))\n      }, [renderSlot(_ctx.$slots, \"empty\", {}, () => {\n        var _a;\n        return [createElementVNode(\"span\", {\n          class: normalizeClass(unref(ns).e(\"empty-text\"))\n        }, toDisplayString((_a = _ctx.emptyText) != null ? _a : unref(t)(\"el.tree.emptyText\")), 3)];\n      })], 2))], 2);\n    };\n  }\n});\nvar TreeV2 = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tree.vue\"]]);\nexport { TreeV2 as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}