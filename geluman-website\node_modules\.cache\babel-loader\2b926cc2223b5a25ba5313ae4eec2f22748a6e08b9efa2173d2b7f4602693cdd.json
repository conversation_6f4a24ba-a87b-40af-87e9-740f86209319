{"ast": null, "code": "import LazyWrapper from './_LazyWrapper.js';\nimport LodashWrapper from './_LodashWrapper.js';\nimport copyArray from './_copyArray.js';\n\n/**\n * Creates a clone of `wrapper`.\n *\n * @private\n * @param {Object} wrapper The wrapper to clone.\n * @returns {Object} Returns the cloned wrapper.\n */\nfunction wrapperClone(wrapper) {\n  if (wrapper instanceof LazyWrapper) {\n    return wrapper.clone();\n  }\n  var result = new LodashWrapper(wrapper.__wrapped__, wrapper.__chain__);\n  result.__actions__ = copyArray(wrapper.__actions__);\n  result.__index__ = wrapper.__index__;\n  result.__values__ = wrapper.__values__;\n  return result;\n}\nexport default wrapperClone;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}