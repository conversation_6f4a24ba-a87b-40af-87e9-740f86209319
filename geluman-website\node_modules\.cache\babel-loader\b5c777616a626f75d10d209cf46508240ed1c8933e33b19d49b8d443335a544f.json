{"ast": null, "code": "import MessageBox from './src/messageBox.mjs';\nconst _MessageBox = MessageBox;\n_MessageBox.install = app => {\n  _MessageBox._context = app._context;\n  app.config.globalProperties.$msgbox = _MessageBox;\n  app.config.globalProperties.$messageBox = _MessageBox;\n  app.config.globalProperties.$alert = _MessageBox.alert;\n  app.config.globalProperties.$confirm = _MessageBox.confirm;\n  app.config.globalProperties.$prompt = _MessageBox.prompt;\n};\nconst ElMessageBox = _MessageBox;\nexport { ElMessageBox, _MessageBox as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}