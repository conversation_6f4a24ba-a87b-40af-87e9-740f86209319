{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { shallowRef, isVNode } from 'vue';\nimport { flattedChildren } from '../../utils/vue/vnode.mjs';\nconst getOrderedChildren = (vm, childComponentName, children) => {\n  const nodes = flattedChildren(vm.subTree).filter(n => {\n    var _a;\n    return isVNode(n) && ((_a = n.type) == null ? void 0 : _a.name) === childComponentName && !!n.component;\n  });\n  const uids = nodes.map(n => n.component.uid);\n  return uids.map(uid => children[uid]).filter(p => !!p);\n};\nconst useOrderedChildren = (vm, childComponentName) => {\n  const children = {};\n  const orderedChildren = shallowRef([]);\n  const addChild = child => {\n    children[child.uid] = child;\n    orderedChildren.value = getOrderedChildren(vm, childComponentName, children);\n  };\n  const removeChild = uid => {\n    delete children[uid];\n    orderedChildren.value = orderedChildren.value.filter(children2 => children2.uid !== uid);\n  };\n  return {\n    children: orderedChildren,\n    addChild,\n    removeChild\n  };\n};\nexport { useOrderedChildren };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}