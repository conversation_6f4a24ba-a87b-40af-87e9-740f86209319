{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { watch } from 'vue';\nimport { isClient, useEventListener } from '@vueuse/core';\nimport { EVENT_CODE } from '../../constants/aria.mjs';\nconst modalStack = [];\nconst closeModal = e => {\n  if (modalStack.length === 0) return;\n  if (e.code === EVENT_CODE.esc) {\n    e.stopPropagation();\n    const topModal = modalStack[modalStack.length - 1];\n    topModal.handleClose();\n  }\n};\nconst useModal = (instance, visibleRef) => {\n  watch(visibleRef, val => {\n    if (val) {\n      modalStack.push(instance);\n    } else {\n      modalStack.splice(modalStack.indexOf(instance), 1);\n    }\n  });\n};\nif (isClient) useEventListener(document, \"keydown\", closeModal);\nexport { useModal };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}