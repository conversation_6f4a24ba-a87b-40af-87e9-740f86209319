{"ast": null, "code": "import { inject, getCurrentInstance, ref, unref, onMounted, reactive, onUnmounted } from 'vue';\nimport { carouselContextKey, CAROUSEL_ITEM_NAME } from './constants.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { isUndefined } from '../../../utils/types.mjs';\nconst useCarouselItem = props => {\n  const carouselContext = inject(carouselContextKey);\n  const instance = getCurrentInstance();\n  if (!carouselContext) {\n    debugWarn(CAROUSEL_ITEM_NAME, \"usage: <el-carousel></el-carousel-item></el-carousel>\");\n  }\n  if (!instance) {\n    debugWarn(CAROUSEL_ITEM_NAME, \"compositional hook can only be invoked inside setups\");\n  }\n  const carouselItemRef = ref();\n  const hover = ref(false);\n  const translate = ref(0);\n  const scale = ref(1);\n  const active = ref(false);\n  const ready = ref(false);\n  const inStage = ref(false);\n  const animating = ref(false);\n  const {\n    isCardType,\n    isVertical,\n    cardScale\n  } = carouselContext;\n  function processIndex(index, activeIndex, length) {\n    const lastItemIndex = length - 1;\n    const prevItemIndex = activeIndex - 1;\n    const nextItemIndex = activeIndex + 1;\n    const halfItemIndex = length / 2;\n    if (activeIndex === 0 && index === lastItemIndex) {\n      return -1;\n    } else if (activeIndex === lastItemIndex && index === 0) {\n      return length;\n    } else if (index < prevItemIndex && activeIndex - index >= halfItemIndex) {\n      return length + 1;\n    } else if (index > nextItemIndex && index - activeIndex >= halfItemIndex) {\n      return -2;\n    }\n    return index;\n  }\n  function calcCardTranslate(index, activeIndex) {\n    var _a, _b;\n    const parentWidth = unref(isVertical) ? ((_a = carouselContext.root.value) == null ? void 0 : _a.offsetHeight) || 0 : ((_b = carouselContext.root.value) == null ? void 0 : _b.offsetWidth) || 0;\n    if (inStage.value) {\n      return parentWidth * ((2 - cardScale) * (index - activeIndex) + 1) / 4;\n    } else if (index < activeIndex) {\n      return -(1 + cardScale) * parentWidth / 4;\n    } else {\n      return (3 + cardScale) * parentWidth / 4;\n    }\n  }\n  function calcTranslate(index, activeIndex, isVertical2) {\n    const rootEl = carouselContext.root.value;\n    if (!rootEl) return 0;\n    const distance = (isVertical2 ? rootEl.offsetHeight : rootEl.offsetWidth) || 0;\n    return distance * (index - activeIndex);\n  }\n  const translateItem = (index, activeIndex, oldIndex) => {\n    var _a;\n    const _isCardType = unref(isCardType);\n    const carouselItemLength = (_a = carouselContext.items.value.length) != null ? _a : Number.NaN;\n    const isActive = index === activeIndex;\n    if (!_isCardType && !isUndefined(oldIndex)) {\n      animating.value = isActive || index === oldIndex;\n    }\n    if (!isActive && carouselItemLength > 2 && carouselContext.loop) {\n      index = processIndex(index, activeIndex, carouselItemLength);\n    }\n    const _isVertical = unref(isVertical);\n    active.value = isActive;\n    if (_isCardType) {\n      inStage.value = Math.round(Math.abs(index - activeIndex)) <= 1;\n      translate.value = calcCardTranslate(index, activeIndex);\n      scale.value = unref(active) ? 1 : cardScale;\n    } else {\n      translate.value = calcTranslate(index, activeIndex, _isVertical);\n    }\n    ready.value = true;\n    if (isActive && carouselItemRef.value) {\n      carouselContext.setContainerHeight(carouselItemRef.value.offsetHeight);\n    }\n  };\n  function handleItemClick() {\n    if (carouselContext && unref(isCardType)) {\n      const index = carouselContext.items.value.findIndex(({\n        uid\n      }) => uid === instance.uid);\n      carouselContext.setActiveItem(index);\n    }\n  }\n  onMounted(() => {\n    carouselContext.addItem({\n      props,\n      states: reactive({\n        hover,\n        translate,\n        scale,\n        active,\n        ready,\n        inStage,\n        animating\n      }),\n      uid: instance.uid,\n      translateItem\n    });\n  });\n  onUnmounted(() => {\n    carouselContext.removeItem(instance.uid);\n  });\n  return {\n    carouselItemRef,\n    active,\n    animating,\n    hover,\n    inStage,\n    isVertical,\n    translate,\n    isCardType,\n    scale,\n    ready,\n    handleItemClick\n  };\n};\nexport { useCarouselItem };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}