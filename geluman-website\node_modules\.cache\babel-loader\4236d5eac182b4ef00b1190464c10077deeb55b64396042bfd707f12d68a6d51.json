{"ast": null, "code": "import { defineComponent, renderSlot } from 'vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  inheritAttrs: false\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return renderSlot(_ctx.$slots, \"default\");\n}\nvar Collection = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"collection.vue\"]]);\nexport { Collection as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}