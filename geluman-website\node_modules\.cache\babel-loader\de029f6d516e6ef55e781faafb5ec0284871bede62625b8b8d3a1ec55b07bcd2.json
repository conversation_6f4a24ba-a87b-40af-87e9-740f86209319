{"ast": null, "code": "import baseSlice from './_baseSlice.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Creates a slice of `array` from `start` up to, but not including, `end`.\n *\n * **Note:** This method is used instead of\n * [`Array#slice`](https://mdn.io/Array/slice) to ensure dense arrays are\n * returned.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to slice.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns the slice of `array`.\n */\nfunction slice(array, start, end) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return [];\n  }\n  if (end && typeof end != 'number' && isIterateeCall(array, start, end)) {\n    start = 0;\n    end = length;\n  } else {\n    start = start == null ? 0 : toInteger(start);\n    end = end === undefined ? length : toInteger(end);\n  }\n  return baseSlice(array, start, end);\n}\nexport default slice;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}