{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { h } from 'vue';\nimport { ElCheckbox } from '../../checkbox/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { ArrowRight, Loading } from '@element-plus/icons-vue';\nimport { isNumber, isBoolean } from '../../../utils/types.mjs';\nimport { isFunction } from '@vue/shared';\nimport { getProp } from '../../../utils/objects.mjs';\nconst defaultClassNames = {\n  selection: \"table-column--selection\",\n  expand: \"table__expand-column\"\n};\nconst cellStarts = {\n  default: {\n    order: \"\"\n  },\n  selection: {\n    width: 48,\n    minWidth: 48,\n    realWidth: 48,\n    order: \"\"\n  },\n  expand: {\n    width: 48,\n    minWidth: 48,\n    realWidth: 48,\n    order: \"\"\n  },\n  index: {\n    width: 48,\n    minWidth: 48,\n    realWidth: 48,\n    order: \"\"\n  }\n};\nconst getDefaultClassName = type => {\n  return defaultClassNames[type] || \"\";\n};\nconst cellForced = {\n  selection: {\n    renderHeader({\n      store,\n      column\n    }) {\n      function isDisabled() {\n        return store.states.data.value && store.states.data.value.length === 0;\n      }\n      return h(ElCheckbox, {\n        disabled: isDisabled(),\n        size: store.states.tableSize.value,\n        indeterminate: store.states.selection.value.length > 0 && !store.states.isAllSelected.value,\n        \"onUpdate:modelValue\": store.toggleAllSelection,\n        modelValue: store.states.isAllSelected.value,\n        ariaLabel: column.label\n      });\n    },\n    renderCell({\n      row,\n      column,\n      store,\n      $index\n    }) {\n      return h(ElCheckbox, {\n        disabled: column.selectable ? !column.selectable.call(null, row, $index) : false,\n        size: store.states.tableSize.value,\n        onChange: () => {\n          store.commit(\"rowSelectedChanged\", row);\n        },\n        onClick: event => event.stopPropagation(),\n        modelValue: store.isSelected(row),\n        ariaLabel: column.label\n      });\n    },\n    sortable: false,\n    resizable: false\n  },\n  index: {\n    renderHeader({\n      column\n    }) {\n      return column.label || \"#\";\n    },\n    renderCell({\n      column,\n      $index\n    }) {\n      let i = $index + 1;\n      const index = column.index;\n      if (isNumber(index)) {\n        i = $index + index;\n      } else if (isFunction(index)) {\n        i = index($index);\n      }\n      return h(\"div\", {}, [i]);\n    },\n    sortable: false\n  },\n  expand: {\n    renderHeader({\n      column\n    }) {\n      return column.label || \"\";\n    },\n    renderCell({\n      row,\n      store,\n      expanded\n    }) {\n      const {\n        ns\n      } = store;\n      const classes = [ns.e(\"expand-icon\")];\n      if (expanded) {\n        classes.push(ns.em(\"expand-icon\", \"expanded\"));\n      }\n      const callback = function (e) {\n        e.stopPropagation();\n        store.toggleRowExpansion(row);\n      };\n      return h(\"div\", {\n        class: classes,\n        onClick: callback\n      }, {\n        default: () => {\n          return [h(ElIcon, null, {\n            default: () => {\n              return [h(ArrowRight)];\n            }\n          })];\n        }\n      });\n    },\n    sortable: false,\n    resizable: false\n  }\n};\nfunction defaultRenderCell({\n  row,\n  column,\n  $index\n}) {\n  var _a;\n  const property = column.property;\n  const value = property && getProp(row, property).value;\n  if (column && column.formatter) {\n    return column.formatter(row, column, value, $index);\n  }\n  return ((_a = value == null ? void 0 : value.toString) == null ? void 0 : _a.call(value)) || \"\";\n}\nfunction treeCellPrefix({\n  row,\n  treeNode,\n  store\n}, createPlaceholder = false) {\n  const {\n    ns\n  } = store;\n  if (!treeNode) {\n    if (createPlaceholder) {\n      return [h(\"span\", {\n        class: ns.e(\"placeholder\")\n      })];\n    }\n    return null;\n  }\n  const ele = [];\n  const callback = function (e) {\n    e.stopPropagation();\n    if (treeNode.loading) {\n      return;\n    }\n    store.loadOrToggle(row);\n  };\n  if (treeNode.indent) {\n    ele.push(h(\"span\", {\n      class: ns.e(\"indent\"),\n      style: {\n        \"padding-left\": `${treeNode.indent}px`\n      }\n    }));\n  }\n  if (isBoolean(treeNode.expanded) && !treeNode.noLazyChildren) {\n    const expandClasses = [ns.e(\"expand-icon\"), treeNode.expanded ? ns.em(\"expand-icon\", \"expanded\") : \"\"];\n    let icon = ArrowRight;\n    if (treeNode.loading) {\n      icon = Loading;\n    }\n    ele.push(h(\"div\", {\n      class: expandClasses,\n      onClick: callback\n    }, {\n      default: () => {\n        return [h(ElIcon, {\n          class: {\n            [ns.is(\"loading\")]: treeNode.loading\n          }\n        }, {\n          default: () => [h(icon)]\n        })];\n      }\n    }));\n  } else {\n    ele.push(h(\"span\", {\n      class: ns.e(\"placeholder\")\n    }));\n  }\n  return ele;\n}\nexport { cellForced, cellStarts, defaultRenderCell, getDefaultClassName, treeCellPrefix };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}