{"ast": null, "code": "import TooltipV2 from './src/tooltip2.mjs';\nexport { tooltipV2ArrowProps, tooltipV2ArrowSpecialProps } from './src/arrow.mjs';\nexport { tooltipV2ContentProps } from './src/content.mjs';\nexport { tooltipV2RootProps } from './src/root.mjs';\nexport { tooltipV2Props } from './src/tooltip.mjs';\nexport { tooltipV2TriggerProps } from './src/trigger.mjs';\nexport { TOOLTIP_V2_OPEN, tooltipV2ContentKey, tooltipV2RootKey } from './src/constants.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElTooltipV2 = withInstall(TooltipV2);\nexport { ElTooltipV2, ElTooltipV2 as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}