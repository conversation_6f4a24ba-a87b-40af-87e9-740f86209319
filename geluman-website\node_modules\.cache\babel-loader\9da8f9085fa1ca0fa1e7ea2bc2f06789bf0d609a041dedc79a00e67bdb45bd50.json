{"ast": null, "code": "import { ref, onMounted, onBeforeUnmount, watch } from 'vue';\nimport { useResizeObserver } from '@vueuse/core';\nconst useAutoResize = props => {\n  const sizer = ref();\n  const width$ = ref(0);\n  const height$ = ref(0);\n  let resizerStopper;\n  onMounted(() => {\n    resizerStopper = useResizeObserver(sizer, ([entry]) => {\n      const {\n        width,\n        height\n      } = entry.contentRect;\n      const {\n        paddingLeft,\n        paddingRight,\n        paddingTop,\n        paddingBottom\n      } = getComputedStyle(entry.target);\n      const left = Number.parseInt(paddingLeft) || 0;\n      const right = Number.parseInt(paddingRight) || 0;\n      const top = Number.parseInt(paddingTop) || 0;\n      const bottom = Number.parseInt(paddingBottom) || 0;\n      width$.value = width - left - right;\n      height$.value = height - top - bottom;\n    }).stop;\n  });\n  onBeforeUnmount(() => {\n    resizerStopper == null ? void 0 : resizerStopper();\n  });\n  watch([width$, height$], ([width, height]) => {\n    var _a;\n    (_a = props.onResize) == null ? void 0 : _a.call(props, {\n      width,\n      height\n    });\n  });\n  return {\n    sizer,\n    width: width$,\n    height: height$\n  };\n};\nexport { useAutoResize };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}