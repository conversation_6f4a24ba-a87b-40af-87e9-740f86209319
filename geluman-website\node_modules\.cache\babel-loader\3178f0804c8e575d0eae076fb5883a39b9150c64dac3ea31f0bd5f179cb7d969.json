{"ast": null, "code": "import { Back } from '@element-plus/icons-vue';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nconst pageHeaderProps = buildProps({\n  icon: {\n    type: iconPropType,\n    default: () => Back\n  },\n  title: String,\n  content: {\n    type: String,\n    default: \"\"\n  }\n});\nconst pageHeaderEmits = {\n  back: () => true\n};\nexport { pageHeaderEmits, pageHeaderProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}