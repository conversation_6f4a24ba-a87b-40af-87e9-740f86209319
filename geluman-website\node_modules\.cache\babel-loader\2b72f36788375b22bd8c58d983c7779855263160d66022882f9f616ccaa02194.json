{"ast": null, "code": "import DatePicker from './src/date-picker.mjs';\nexport { ROOT_PICKER_INJECTION_KEY } from './src/constants.mjs';\nexport { datePickerProps } from './src/props/date-picker.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElDatePicker = withInstall(DatePicker);\nexport { ElDatePicker, ElDatePicker as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}