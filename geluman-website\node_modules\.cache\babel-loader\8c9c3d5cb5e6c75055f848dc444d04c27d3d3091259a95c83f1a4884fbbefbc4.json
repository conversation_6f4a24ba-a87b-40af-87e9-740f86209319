{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, useSlots, provide, computed, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, renderSlot, createTextVNode, toDisplayString, createCommentVNode, Fragment, renderList, createBlock } from 'vue';\nimport ElDescriptionsRow from './descriptions-row2.mjs';\nimport { descriptionsKey } from './token.mjs';\nimport { descriptionProps } from './description.mjs';\nimport { COMPONENT_NAME } from './constants.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormSize } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { flattedChildren } from '../../../utils/vue/vnode.mjs';\nconst __default__ = defineComponent({\n  name: \"ElDescriptions\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: descriptionProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"descriptions\");\n    const descriptionsSize = useFormSize();\n    const slots = useSlots();\n    provide(descriptionsKey, props);\n    const descriptionKls = computed(() => [ns.b(), ns.m(descriptionsSize.value)]);\n    const filledNode = (node, span, count, isLast = false) => {\n      if (!node.props) {\n        node.props = {};\n      }\n      if (span > count) {\n        node.props.span = count;\n      }\n      if (isLast) {\n        node.props.span = span;\n      }\n      return node;\n    };\n    const getRows = () => {\n      if (!slots.default) return [];\n      const children = flattedChildren(slots.default()).filter(node => {\n        var _a;\n        return ((_a = node == null ? void 0 : node.type) == null ? void 0 : _a.name) === COMPONENT_NAME;\n      });\n      const rows = [];\n      let temp = [];\n      let count = props.column;\n      let totalSpan = 0;\n      const rowspanTemp = [];\n      children.forEach((node, index) => {\n        var _a, _b, _c;\n        const span = ((_a = node.props) == null ? void 0 : _a.span) || 1;\n        const rowspan = ((_b = node.props) == null ? void 0 : _b.rowspan) || 1;\n        const rowNo = rows.length;\n        rowspanTemp[rowNo] || (rowspanTemp[rowNo] = 0);\n        if (rowspan > 1) {\n          for (let i = 1; i < rowspan; i++) {\n            rowspanTemp[_c = rowNo + i] || (rowspanTemp[_c] = 0);\n            rowspanTemp[rowNo + i]++;\n            totalSpan++;\n          }\n        }\n        if (rowspanTemp[rowNo] > 0) {\n          count -= rowspanTemp[rowNo];\n          rowspanTemp[rowNo] = 0;\n        }\n        if (index < children.length - 1) {\n          totalSpan += span > count ? count : span;\n        }\n        if (index === children.length - 1) {\n          const lastSpan = props.column - totalSpan % props.column;\n          temp.push(filledNode(node, lastSpan, count, true));\n          rows.push(temp);\n          return;\n        }\n        if (span < count) {\n          count -= span;\n          temp.push(node);\n        } else {\n          temp.push(filledNode(node, span, count));\n          rows.push(temp);\n          count = props.column;\n          temp = [];\n        }\n      });\n      return rows;\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(descriptionKls))\n      }, [_ctx.title || _ctx.extra || _ctx.$slots.title || _ctx.$slots.extra ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"header\"))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"title\"))\n      }, [renderSlot(_ctx.$slots, \"title\", {}, () => [createTextVNode(toDisplayString(_ctx.title), 1)])], 2), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"extra\"))\n      }, [renderSlot(_ctx.$slots, \"extra\", {}, () => [createTextVNode(toDisplayString(_ctx.extra), 1)])], 2)], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"body\"))\n      }, [createElementVNode(\"table\", {\n        class: normalizeClass([unref(ns).e(\"table\"), unref(ns).is(\"bordered\", _ctx.border)])\n      }, [createElementVNode(\"tbody\", null, [(openBlock(true), createElementBlock(Fragment, null, renderList(getRows(), (row, _index) => {\n        return openBlock(), createBlock(ElDescriptionsRow, {\n          key: _index,\n          row\n        }, null, 8, [\"row\"]);\n      }), 128))])], 2)], 2)], 2);\n    };\n  }\n});\nvar Descriptions = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"description.vue\"]]);\nexport { Descriptions as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}