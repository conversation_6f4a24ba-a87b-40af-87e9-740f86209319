{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, getCurrentInstance, computed, watch, unref } from 'vue';\nimport { getRowIdentity, walkTreeNode } from '../util.mjs';\nimport { isArray } from '@vue/shared';\nimport { isUndefined } from '../../../../utils/types.mjs';\nfunction useTree(watcherData) {\n  const expandRowKeys = ref([]);\n  const treeData = ref({});\n  const indent = ref(16);\n  const lazy = ref(false);\n  const lazyTreeNodeMap = ref({});\n  const lazyColumnIdentifier = ref(\"hasChildren\");\n  const childrenColumnName = ref(\"children\");\n  const checkStrictly = ref(false);\n  const instance = getCurrentInstance();\n  const normalizedData = computed(() => {\n    if (!watcherData.rowKey.value) return {};\n    const data = watcherData.data.value || [];\n    return normalize(data);\n  });\n  const normalizedLazyNode = computed(() => {\n    const rowKey = watcherData.rowKey.value;\n    const keys = Object.keys(lazyTreeNodeMap.value);\n    const res = {};\n    if (!keys.length) return res;\n    keys.forEach(key => {\n      if (lazyTreeNodeMap.value[key].length) {\n        const item = {\n          children: []\n        };\n        lazyTreeNodeMap.value[key].forEach(row => {\n          const currentRowKey = getRowIdentity(row, rowKey);\n          item.children.push(currentRowKey);\n          if (row[lazyColumnIdentifier.value] && !res[currentRowKey]) {\n            res[currentRowKey] = {\n              children: []\n            };\n          }\n        });\n        res[key] = item;\n      }\n    });\n    return res;\n  });\n  const normalize = data => {\n    const rowKey = watcherData.rowKey.value;\n    const res = {};\n    walkTreeNode(data, (parent, children, level) => {\n      const parentId = getRowIdentity(parent, rowKey);\n      if (isArray(children)) {\n        res[parentId] = {\n          children: children.map(row => getRowIdentity(row, rowKey)),\n          level\n        };\n      } else if (lazy.value) {\n        res[parentId] = {\n          children: [],\n          lazy: true,\n          level\n        };\n      }\n    }, childrenColumnName.value, lazyColumnIdentifier.value);\n    return res;\n  };\n  const updateTreeData = (ifChangeExpandRowKeys = false, ifExpandAll = (_a => (_a = instance.store) == null ? void 0 : _a.states.defaultExpandAll.value)()) => {\n    var _a2;\n    const nested = normalizedData.value;\n    const normalizedLazyNode_ = normalizedLazyNode.value;\n    const keys = Object.keys(nested);\n    const newTreeData = {};\n    if (keys.length) {\n      const oldTreeData = unref(treeData);\n      const rootLazyRowKeys = [];\n      const getExpanded = (oldValue, key) => {\n        if (ifChangeExpandRowKeys) {\n          if (expandRowKeys.value) {\n            return ifExpandAll || expandRowKeys.value.includes(key);\n          } else {\n            return !!(ifExpandAll || (oldValue == null ? void 0 : oldValue.expanded));\n          }\n        } else {\n          const included = ifExpandAll || expandRowKeys.value && expandRowKeys.value.includes(key);\n          return !!((oldValue == null ? void 0 : oldValue.expanded) || included);\n        }\n      };\n      keys.forEach(key => {\n        const oldValue = oldTreeData[key];\n        const newValue = {\n          ...nested[key]\n        };\n        newValue.expanded = getExpanded(oldValue, key);\n        if (newValue.lazy) {\n          const {\n            loaded = false,\n            loading = false\n          } = oldValue || {};\n          newValue.loaded = !!loaded;\n          newValue.loading = !!loading;\n          rootLazyRowKeys.push(key);\n        }\n        newTreeData[key] = newValue;\n      });\n      const lazyKeys = Object.keys(normalizedLazyNode_);\n      if (lazy.value && lazyKeys.length && rootLazyRowKeys.length) {\n        lazyKeys.forEach(key => {\n          const oldValue = oldTreeData[key];\n          const lazyNodeChildren = normalizedLazyNode_[key].children;\n          if (rootLazyRowKeys.includes(key)) {\n            if (newTreeData[key].children.length !== 0) {\n              throw new Error(\"[ElTable]children must be an empty array.\");\n            }\n            newTreeData[key].children = lazyNodeChildren;\n          } else {\n            const {\n              loaded = false,\n              loading = false\n            } = oldValue || {};\n            newTreeData[key] = {\n              lazy: true,\n              loaded: !!loaded,\n              loading: !!loading,\n              expanded: getExpanded(oldValue, key),\n              children: lazyNodeChildren,\n              level: \"\"\n            };\n          }\n        });\n      }\n    }\n    treeData.value = newTreeData;\n    (_a2 = instance.store) == null ? void 0 : _a2.updateTableScrollY();\n  };\n  watch(() => expandRowKeys.value, () => {\n    updateTreeData(true);\n  });\n  watch(() => normalizedData.value, () => {\n    updateTreeData();\n  });\n  watch(() => normalizedLazyNode.value, () => {\n    updateTreeData();\n  });\n  const updateTreeExpandKeys = value => {\n    expandRowKeys.value = value;\n    updateTreeData();\n  };\n  const isUseLazy = data => {\n    return lazy.value && data && \"loaded\" in data && !data.loaded;\n  };\n  const toggleTreeExpansion = (row, expanded) => {\n    instance.store.assertRowKey();\n    const rowKey = watcherData.rowKey.value;\n    const id = getRowIdentity(row, rowKey);\n    const data = id && treeData.value[id];\n    if (id && data && \"expanded\" in data) {\n      const oldExpanded = data.expanded;\n      expanded = isUndefined(expanded) ? !data.expanded : expanded;\n      treeData.value[id].expanded = expanded;\n      if (oldExpanded !== expanded) {\n        instance.emit(\"expand-change\", row, expanded);\n      }\n      isUseLazy(data) && loadData(row, id, data);\n      instance.store.updateTableScrollY();\n    }\n  };\n  const loadOrToggle = row => {\n    instance.store.assertRowKey();\n    const rowKey = watcherData.rowKey.value;\n    const id = getRowIdentity(row, rowKey);\n    const data = treeData.value[id];\n    if (isUseLazy(data)) {\n      loadData(row, id, data);\n    } else {\n      toggleTreeExpansion(row, void 0);\n    }\n  };\n  const loadData = (row, key, treeNode) => {\n    const {\n      load\n    } = instance.props;\n    if (load && !treeData.value[key].loaded) {\n      treeData.value[key].loading = true;\n      load(row, treeNode, data => {\n        if (!isArray(data)) {\n          throw new TypeError(\"[ElTable] data must be an array\");\n        }\n        treeData.value[key].loading = false;\n        treeData.value[key].loaded = true;\n        treeData.value[key].expanded = true;\n        if (data.length) {\n          lazyTreeNodeMap.value[key] = data;\n        }\n        instance.emit(\"expand-change\", row, true);\n      });\n    }\n  };\n  const updateKeyChildren = (key, data) => {\n    const {\n      lazy: lazy2,\n      rowKey\n    } = instance.props;\n    if (!lazy2) return;\n    if (!rowKey) throw new Error(\"[Table] rowKey is required in updateKeyChild\");\n    if (lazyTreeNodeMap.value[key]) {\n      lazyTreeNodeMap.value[key] = data;\n    }\n  };\n  return {\n    loadData,\n    loadOrToggle,\n    toggleTreeExpansion,\n    updateTreeExpandKeys,\n    updateTreeData,\n    updateKeyChildren,\n    normalize,\n    states: {\n      expandRowKeys,\n      treeData,\n      indent,\n      lazy,\n      lazyTreeNodeMap,\n      lazyColumnIdentifier,\n      childrenColumnName,\n      checkStrictly\n    }\n  };\n}\nexport { useTree as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}