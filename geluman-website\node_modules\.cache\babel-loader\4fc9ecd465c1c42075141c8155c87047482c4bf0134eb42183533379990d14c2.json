{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { defineComponent, useAttrs, inject, ref, computed, watch, nextTick, unref, onBeforeUnmount, provide, openBlock, createBlock, mergeProps, withCtx, normalizeClass, normalizeStyle, withModifiers, resolveDynamicComponent, createCommentVNode, renderSlot, createElementVNode, toDisplayString } from 'vue';\nimport { isEqual } from 'lodash-unified';\nimport { onClickOutside, unrefElement } from '@vueuse/core';\nimport { ElInput } from '../../../input/index.mjs';\nimport { ElIcon } from '../../../icon/index.mjs';\nimport { ElTooltip } from '../../../tooltip/index.mjs';\nimport { Clock, Calendar } from '@element-plus/icons-vue';\nimport { valueEquals, parseDate, dayOrDaysToDate, formatter } from '../utils.mjs';\nimport { timePickerDefaultProps } from './props.mjs';\nimport PickerRangeTrigger from './picker-range-trigger.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useEmptyValues } from '../../../../hooks/use-empty-values/index.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../../constants/event.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { useFormItem } from '../../../form/src/hooks/use-form-item.mjs';\nimport { useFocusController } from '../../../../hooks/use-focus-controller/index.mjs';\nimport { debugWarn } from '../../../../utils/error.mjs';\nimport { isArray, NOOP } from '@vue/shared';\nimport { useFormSize } from '../../../form/src/hooks/use-form-common-props.mjs';\nimport { EVENT_CODE } from '../../../../constants/aria.mjs';\nconst __default__ = defineComponent({\n  name: \"Picker\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: timePickerDefaultProps,\n  emits: [UPDATE_MODEL_EVENT, CHANGE_EVENT, \"focus\", \"blur\", \"clear\", \"calendar-change\", \"panel-change\", \"visible-change\", \"keydown\"],\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const attrs = useAttrs();\n    const {\n      lang\n    } = useLocale();\n    const nsDate = useNamespace(\"date\");\n    const nsInput = useNamespace(\"input\");\n    const nsRange = useNamespace(\"range\");\n    const {\n      form,\n      formItem\n    } = useFormItem();\n    const elPopperOptions = inject(\"ElPopperOptions\", {});\n    const {\n      valueOnClear\n    } = useEmptyValues(props, null);\n    const refPopper = ref();\n    const inputRef = ref();\n    const pickerVisible = ref(false);\n    const pickerActualVisible = ref(false);\n    const valueOnOpen = ref(null);\n    let hasJustTabExitedInput = false;\n    const {\n      isFocused,\n      handleFocus,\n      handleBlur\n    } = useFocusController(inputRef, {\n      beforeFocus() {\n        return props.readonly || pickerDisabled.value;\n      },\n      afterFocus() {\n        pickerVisible.value = true;\n      },\n      beforeBlur(event) {\n        var _a;\n        return !hasJustTabExitedInput && ((_a = refPopper.value) == null ? void 0 : _a.isFocusInsideContent(event));\n      },\n      afterBlur() {\n        handleChange();\n        pickerVisible.value = false;\n        hasJustTabExitedInput = false;\n        props.validateEvent && (formItem == null ? void 0 : formItem.validate(\"blur\").catch(err => debugWarn(err)));\n      }\n    });\n    const rangeInputKls = computed(() => [nsDate.b(\"editor\"), nsDate.bm(\"editor\", props.type), nsInput.e(\"wrapper\"), nsDate.is(\"disabled\", pickerDisabled.value), nsDate.is(\"active\", pickerVisible.value), nsRange.b(\"editor\"), pickerSize ? nsRange.bm(\"editor\", pickerSize.value) : \"\", attrs.class]);\n    const clearIconKls = computed(() => [nsInput.e(\"icon\"), nsRange.e(\"close-icon\"), !showClose.value ? nsRange.e(\"close-icon--hidden\") : \"\"]);\n    watch(pickerVisible, val => {\n      if (!val) {\n        userInput.value = null;\n        nextTick(() => {\n          emitChange(props.modelValue);\n        });\n      } else {\n        nextTick(() => {\n          if (val) {\n            valueOnOpen.value = props.modelValue;\n          }\n        });\n      }\n    });\n    const emitChange = (val, isClear) => {\n      if (isClear || !valueEquals(val, valueOnOpen.value)) {\n        emit(CHANGE_EVENT, val);\n        isClear && (valueOnOpen.value = val);\n        props.validateEvent && (formItem == null ? void 0 : formItem.validate(\"change\").catch(err => debugWarn(err)));\n      }\n    };\n    const emitInput = input => {\n      if (!valueEquals(props.modelValue, input)) {\n        let formatted;\n        if (isArray(input)) {\n          formatted = input.map(item => formatter(item, props.valueFormat, lang.value));\n        } else if (input) {\n          formatted = formatter(input, props.valueFormat, lang.value);\n        }\n        emit(UPDATE_MODEL_EVENT, input ? formatted : input, lang.value);\n      }\n    };\n    const emitKeydown = e => {\n      emit(\"keydown\", e);\n    };\n    const refInput = computed(() => {\n      if (inputRef.value) {\n        return Array.from(inputRef.value.$el.querySelectorAll(\"input\"));\n      }\n      return [];\n    });\n    const setSelectionRange = (start, end, pos) => {\n      const _inputs = refInput.value;\n      if (!_inputs.length) return;\n      if (!pos || pos === \"min\") {\n        _inputs[0].setSelectionRange(start, end);\n        _inputs[0].focus();\n      } else if (pos === \"max\") {\n        _inputs[1].setSelectionRange(start, end);\n        _inputs[1].focus();\n      }\n    };\n    const onPick = (date = \"\", visible = false) => {\n      pickerVisible.value = visible;\n      let result;\n      if (isArray(date)) {\n        result = date.map(_ => _.toDate());\n      } else {\n        result = date ? date.toDate() : date;\n      }\n      userInput.value = null;\n      emitInput(result);\n    };\n    const onBeforeShow = () => {\n      pickerActualVisible.value = true;\n    };\n    const onShow = () => {\n      emit(\"visible-change\", true);\n    };\n    const onHide = () => {\n      pickerActualVisible.value = false;\n      pickerVisible.value = false;\n      emit(\"visible-change\", false);\n    };\n    const handleOpen = () => {\n      pickerVisible.value = true;\n    };\n    const handleClose = () => {\n      pickerVisible.value = false;\n    };\n    const pickerDisabled = computed(() => {\n      return props.disabled || (form == null ? void 0 : form.disabled);\n    });\n    const parsedValue = computed(() => {\n      let dayOrDays;\n      if (valueIsEmpty.value) {\n        if (pickerOptions.value.getDefaultValue) {\n          dayOrDays = pickerOptions.value.getDefaultValue();\n        }\n      } else {\n        if (isArray(props.modelValue)) {\n          dayOrDays = props.modelValue.map(d => parseDate(d, props.valueFormat, lang.value));\n        } else {\n          dayOrDays = parseDate(props.modelValue, props.valueFormat, lang.value);\n        }\n      }\n      if (pickerOptions.value.getRangeAvailableTime) {\n        const availableResult = pickerOptions.value.getRangeAvailableTime(dayOrDays);\n        if (!isEqual(availableResult, dayOrDays)) {\n          dayOrDays = availableResult;\n          if (!valueIsEmpty.value) {\n            emitInput(dayOrDaysToDate(dayOrDays));\n          }\n        }\n      }\n      if (isArray(dayOrDays) && dayOrDays.some(day => !day)) {\n        dayOrDays = [];\n      }\n      return dayOrDays;\n    });\n    const displayValue = computed(() => {\n      if (!pickerOptions.value.panelReady) return \"\";\n      const formattedValue = formatDayjsToString(parsedValue.value);\n      if (isArray(userInput.value)) {\n        return [userInput.value[0] || formattedValue && formattedValue[0] || \"\", userInput.value[1] || formattedValue && formattedValue[1] || \"\"];\n      } else if (userInput.value !== null) {\n        return userInput.value;\n      }\n      if (!isTimePicker.value && valueIsEmpty.value) return \"\";\n      if (!pickerVisible.value && valueIsEmpty.value) return \"\";\n      if (formattedValue) {\n        return isDatesPicker.value || isMonthsPicker.value || isYearsPicker.value ? formattedValue.join(\", \") : formattedValue;\n      }\n      return \"\";\n    });\n    const isTimeLikePicker = computed(() => props.type.includes(\"time\"));\n    const isTimePicker = computed(() => props.type.startsWith(\"time\"));\n    const isDatesPicker = computed(() => props.type === \"dates\");\n    const isMonthsPicker = computed(() => props.type === \"months\");\n    const isYearsPicker = computed(() => props.type === \"years\");\n    const triggerIcon = computed(() => props.prefixIcon || (isTimeLikePicker.value ? Clock : Calendar));\n    const showClose = ref(false);\n    const onClearIconClick = event => {\n      if (props.readonly || pickerDisabled.value) return;\n      if (showClose.value) {\n        event.stopPropagation();\n        if (pickerOptions.value.handleClear) {\n          pickerOptions.value.handleClear();\n        } else {\n          emitInput(valueOnClear.value);\n        }\n        emitChange(valueOnClear.value, true);\n        showClose.value = false;\n        onHide();\n      }\n      emit(\"clear\");\n    };\n    const valueIsEmpty = computed(() => {\n      const {\n        modelValue\n      } = props;\n      return !modelValue || isArray(modelValue) && !modelValue.filter(Boolean).length;\n    });\n    const onMouseDownInput = async event => {\n      var _a;\n      if (props.readonly || pickerDisabled.value) return;\n      if (((_a = event.target) == null ? void 0 : _a.tagName) !== \"INPUT\" || isFocused.value) {\n        pickerVisible.value = true;\n      }\n    };\n    const onMouseEnter = () => {\n      if (props.readonly || pickerDisabled.value) return;\n      if (!valueIsEmpty.value && props.clearable) {\n        showClose.value = true;\n      }\n    };\n    const onMouseLeave = () => {\n      showClose.value = false;\n    };\n    const onTouchStartInput = event => {\n      var _a;\n      if (props.readonly || pickerDisabled.value) return;\n      if (((_a = event.touches[0].target) == null ? void 0 : _a.tagName) !== \"INPUT\" || isFocused.value) {\n        pickerVisible.value = true;\n      }\n    };\n    const isRangeInput = computed(() => {\n      return props.type.includes(\"range\");\n    });\n    const pickerSize = useFormSize();\n    const popperEl = computed(() => {\n      var _a, _b;\n      return (_b = (_a = unref(refPopper)) == null ? void 0 : _a.popperRef) == null ? void 0 : _b.contentRef;\n    });\n    const stophandle = onClickOutside(inputRef, e => {\n      const unrefedPopperEl = unref(popperEl);\n      const inputEl = unrefElement(inputRef);\n      if (unrefedPopperEl && (e.target === unrefedPopperEl || e.composedPath().includes(unrefedPopperEl)) || e.target === inputEl || inputEl && e.composedPath().includes(inputEl)) return;\n      pickerVisible.value = false;\n    });\n    onBeforeUnmount(() => {\n      stophandle == null ? void 0 : stophandle();\n    });\n    const userInput = ref(null);\n    const handleChange = () => {\n      if (userInput.value) {\n        const value = parseUserInputToDayjs(displayValue.value);\n        if (value) {\n          if (isValidValue(value)) {\n            emitInput(dayOrDaysToDate(value));\n            userInput.value = null;\n          }\n        }\n      }\n      if (userInput.value === \"\") {\n        emitInput(valueOnClear.value);\n        emitChange(valueOnClear.value, true);\n        userInput.value = null;\n      }\n    };\n    const parseUserInputToDayjs = value => {\n      if (!value) return null;\n      return pickerOptions.value.parseUserInput(value);\n    };\n    const formatDayjsToString = value => {\n      if (!value) return null;\n      return pickerOptions.value.formatToString(value);\n    };\n    const isValidValue = value => {\n      return pickerOptions.value.isValidValue(value);\n    };\n    const handleKeydownInput = async event => {\n      if (props.readonly || pickerDisabled.value) return;\n      const {\n        code\n      } = event;\n      emitKeydown(event);\n      if (code === EVENT_CODE.esc) {\n        if (pickerVisible.value === true) {\n          pickerVisible.value = false;\n          event.preventDefault();\n          event.stopPropagation();\n        }\n        return;\n      }\n      if (code === EVENT_CODE.down) {\n        if (pickerOptions.value.handleFocusPicker) {\n          event.preventDefault();\n          event.stopPropagation();\n        }\n        if (pickerVisible.value === false) {\n          pickerVisible.value = true;\n          await nextTick();\n        }\n        if (pickerOptions.value.handleFocusPicker) {\n          pickerOptions.value.handleFocusPicker();\n          return;\n        }\n      }\n      if (code === EVENT_CODE.tab) {\n        hasJustTabExitedInput = true;\n        return;\n      }\n      if (code === EVENT_CODE.enter || code === EVENT_CODE.numpadEnter) {\n        if (userInput.value === null || userInput.value === \"\" || isValidValue(parseUserInputToDayjs(displayValue.value))) {\n          handleChange();\n          pickerVisible.value = false;\n        }\n        event.stopPropagation();\n        return;\n      }\n      if (userInput.value) {\n        event.stopPropagation();\n        return;\n      }\n      if (pickerOptions.value.handleKeydownInput) {\n        pickerOptions.value.handleKeydownInput(event);\n      }\n    };\n    const onUserInput = e => {\n      userInput.value = e;\n      if (!pickerVisible.value) {\n        pickerVisible.value = true;\n      }\n    };\n    const handleStartInput = event => {\n      const target = event.target;\n      if (userInput.value) {\n        userInput.value = [target.value, userInput.value[1]];\n      } else {\n        userInput.value = [target.value, null];\n      }\n    };\n    const handleEndInput = event => {\n      const target = event.target;\n      if (userInput.value) {\n        userInput.value = [userInput.value[0], target.value];\n      } else {\n        userInput.value = [null, target.value];\n      }\n    };\n    const handleStartChange = () => {\n      var _a;\n      const values = userInput.value;\n      const value = parseUserInputToDayjs(values && values[0]);\n      const parsedVal = unref(parsedValue);\n      if (value && value.isValid()) {\n        userInput.value = [formatDayjsToString(value), ((_a = displayValue.value) == null ? void 0 : _a[1]) || null];\n        const newValue = [value, parsedVal && (parsedVal[1] || null)];\n        if (isValidValue(newValue)) {\n          emitInput(dayOrDaysToDate(newValue));\n          userInput.value = null;\n        }\n      }\n    };\n    const handleEndChange = () => {\n      var _a;\n      const values = unref(userInput);\n      const value = parseUserInputToDayjs(values && values[1]);\n      const parsedVal = unref(parsedValue);\n      if (value && value.isValid()) {\n        userInput.value = [((_a = unref(displayValue)) == null ? void 0 : _a[0]) || null, formatDayjsToString(value)];\n        const newValue = [parsedVal && parsedVal[0], value];\n        if (isValidValue(newValue)) {\n          emitInput(dayOrDaysToDate(newValue));\n          userInput.value = null;\n        }\n      }\n    };\n    const pickerOptions = ref({});\n    const onSetPickerOption = e => {\n      pickerOptions.value[e[0]] = e[1];\n      pickerOptions.value.panelReady = true;\n    };\n    const onCalendarChange = e => {\n      emit(\"calendar-change\", e);\n    };\n    const onPanelChange = (value, mode, view) => {\n      emit(\"panel-change\", value, mode, view);\n    };\n    const focus = () => {\n      var _a;\n      (_a = inputRef.value) == null ? void 0 : _a.focus();\n    };\n    const blur = () => {\n      var _a;\n      (_a = inputRef.value) == null ? void 0 : _a.blur();\n    };\n    provide(\"EP_PICKER_BASE\", {\n      props\n    });\n    expose({\n      focus,\n      blur,\n      handleOpen,\n      handleClose,\n      onPick\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElTooltip), mergeProps({\n        ref_key: \"refPopper\",\n        ref: refPopper,\n        visible: pickerVisible.value,\n        effect: \"light\",\n        pure: \"\",\n        trigger: \"click\"\n      }, _ctx.$attrs, {\n        role: \"dialog\",\n        teleported: \"\",\n        transition: `${unref(nsDate).namespace.value}-zoom-in-top`,\n        \"popper-class\": [`${unref(nsDate).namespace.value}-picker__popper`, _ctx.popperClass],\n        \"popper-options\": unref(elPopperOptions),\n        \"fallback-placements\": _ctx.fallbackPlacements,\n        \"gpu-acceleration\": false,\n        placement: _ctx.placement,\n        \"stop-popper-mouse-event\": false,\n        \"hide-after\": 0,\n        persistent: \"\",\n        onBeforeShow,\n        onShow,\n        onHide\n      }), {\n        default: withCtx(() => [!unref(isRangeInput) ? (openBlock(), createBlock(unref(ElInput), {\n          key: 0,\n          id: _ctx.id,\n          ref_key: \"inputRef\",\n          ref: inputRef,\n          \"container-role\": \"combobox\",\n          \"model-value\": unref(displayValue),\n          name: _ctx.name,\n          size: unref(pickerSize),\n          disabled: unref(pickerDisabled),\n          placeholder: _ctx.placeholder,\n          class: normalizeClass([unref(nsDate).b(\"editor\"), unref(nsDate).bm(\"editor\", _ctx.type), _ctx.$attrs.class]),\n          style: normalizeStyle(_ctx.$attrs.style),\n          readonly: !_ctx.editable || _ctx.readonly || unref(isDatesPicker) || unref(isMonthsPicker) || unref(isYearsPicker) || _ctx.type === \"week\",\n          \"aria-label\": _ctx.ariaLabel,\n          tabindex: _ctx.tabindex,\n          \"validate-event\": false,\n          onInput: onUserInput,\n          onFocus: unref(handleFocus),\n          onBlur: unref(handleBlur),\n          onKeydown: handleKeydownInput,\n          onChange: handleChange,\n          onMousedown: onMouseDownInput,\n          onMouseenter: onMouseEnter,\n          onMouseleave: onMouseLeave,\n          onTouchstartPassive: onTouchStartInput,\n          onClick: withModifiers(() => {}, [\"stop\"])\n        }, {\n          prefix: withCtx(() => [unref(triggerIcon) ? (openBlock(), createBlock(unref(ElIcon), {\n            key: 0,\n            class: normalizeClass(unref(nsInput).e(\"icon\")),\n            onMousedown: withModifiers(onMouseDownInput, [\"prevent\"]),\n            onTouchstartPassive: onTouchStartInput\n          }, {\n            default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(triggerIcon))))]),\n            _: 1\n          }, 8, [\"class\", \"onMousedown\"])) : createCommentVNode(\"v-if\", true)]),\n          suffix: withCtx(() => [showClose.value && _ctx.clearIcon ? (openBlock(), createBlock(unref(ElIcon), {\n            key: 0,\n            class: normalizeClass(`${unref(nsInput).e(\"icon\")} clear-icon`),\n            onMousedown: withModifiers(unref(NOOP), [\"prevent\"]),\n            onClick: onClearIconClick\n          }, {\n            default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.clearIcon)))]),\n            _: 1\n          }, 8, [\"class\", \"onMousedown\"])) : createCommentVNode(\"v-if\", true)]),\n          _: 1\n        }, 8, [\"id\", \"model-value\", \"name\", \"size\", \"disabled\", \"placeholder\", \"class\", \"style\", \"readonly\", \"aria-label\", \"tabindex\", \"onFocus\", \"onBlur\", \"onClick\"])) : (openBlock(), createBlock(PickerRangeTrigger, {\n          key: 1,\n          id: _ctx.id,\n          ref_key: \"inputRef\",\n          ref: inputRef,\n          \"model-value\": unref(displayValue),\n          name: _ctx.name,\n          disabled: unref(pickerDisabled),\n          readonly: !_ctx.editable || _ctx.readonly,\n          \"start-placeholder\": _ctx.startPlaceholder,\n          \"end-placeholder\": _ctx.endPlaceholder,\n          class: normalizeClass(unref(rangeInputKls)),\n          style: normalizeStyle(_ctx.$attrs.style),\n          \"aria-label\": _ctx.ariaLabel,\n          tabindex: _ctx.tabindex,\n          autocomplete: \"off\",\n          role: \"combobox\",\n          onClick: onMouseDownInput,\n          onFocus: unref(handleFocus),\n          onBlur: unref(handleBlur),\n          onStartInput: handleStartInput,\n          onStartChange: handleStartChange,\n          onEndInput: handleEndInput,\n          onEndChange: handleEndChange,\n          onMousedown: onMouseDownInput,\n          onMouseenter: onMouseEnter,\n          onMouseleave: onMouseLeave,\n          onTouchstartPassive: onTouchStartInput,\n          onKeydown: handleKeydownInput\n        }, {\n          prefix: withCtx(() => [unref(triggerIcon) ? (openBlock(), createBlock(unref(ElIcon), {\n            key: 0,\n            class: normalizeClass([unref(nsInput).e(\"icon\"), unref(nsRange).e(\"icon\")])\n          }, {\n            default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(triggerIcon))))]),\n            _: 1\n          }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true)]),\n          \"range-separator\": withCtx(() => [renderSlot(_ctx.$slots, \"range-separator\", {}, () => [createElementVNode(\"span\", {\n            class: normalizeClass(unref(nsRange).b(\"separator\"))\n          }, toDisplayString(_ctx.rangeSeparator), 3)])]),\n          suffix: withCtx(() => [_ctx.clearIcon ? (openBlock(), createBlock(unref(ElIcon), {\n            key: 0,\n            class: normalizeClass(unref(clearIconKls)),\n            onMousedown: withModifiers(unref(NOOP), [\"prevent\"]),\n            onClick: onClearIconClick\n          }, {\n            default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.clearIcon)))]),\n            _: 1\n          }, 8, [\"class\", \"onMousedown\"])) : createCommentVNode(\"v-if\", true)]),\n          _: 3\n        }, 8, [\"id\", \"model-value\", \"name\", \"disabled\", \"readonly\", \"start-placeholder\", \"end-placeholder\", \"class\", \"style\", \"aria-label\", \"tabindex\", \"onFocus\", \"onBlur\"]))]),\n        content: withCtx(() => [renderSlot(_ctx.$slots, \"default\", {\n          visible: pickerVisible.value,\n          actualVisible: pickerActualVisible.value,\n          parsedValue: unref(parsedValue),\n          format: _ctx.format,\n          dateFormat: _ctx.dateFormat,\n          timeFormat: _ctx.timeFormat,\n          unlinkPanels: _ctx.unlinkPanels,\n          type: _ctx.type,\n          defaultValue: _ctx.defaultValue,\n          showNow: _ctx.showNow,\n          onPick,\n          onSelectRange: setSelectionRange,\n          onSetPickerOption,\n          onCalendarChange,\n          onPanelChange,\n          onMousedown: withModifiers(() => {}, [\"stop\"])\n        })]),\n        _: 3\n      }, 16, [\"visible\", \"transition\", \"popper-class\", \"popper-options\", \"fallback-placements\", \"placement\"]);\n    };\n  }\n});\nvar CommonPicker = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"picker.vue\"]]);\nexport { CommonPicker as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}