{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { ref, computed } from 'vue';\nimport { isFunction } from '@vue/shared';\nfunction useFilter(props, tree) {\n  const hiddenNodeKeySet = ref(/* @__PURE__ */new Set([]));\n  const hiddenExpandIconKeySet = ref(/* @__PURE__ */new Set([]));\n  const filterable = computed(() => {\n    return isFunction(props.filterMethod);\n  });\n  function doFilter(query) {\n    var _a;\n    if (!filterable.value) {\n      return;\n    }\n    const expandKeySet = /* @__PURE__ */new Set();\n    const hiddenExpandIconKeys = hiddenExpandIconKeySet.value;\n    const hiddenKeys = hiddenNodeKeySet.value;\n    const family = [];\n    const nodes = ((_a = tree.value) == null ? void 0 : _a.treeNodes) || [];\n    const filter = props.filterMethod;\n    hiddenKeys.clear();\n    function traverse(nodes2) {\n      nodes2.forEach(node => {\n        family.push(node);\n        if (filter == null ? void 0 : filter(query, node.data, node)) {\n          family.forEach(member => {\n            expandKeySet.add(member.key);\n          });\n        } else if (node.isLeaf) {\n          hiddenKeys.add(node.key);\n        }\n        const children = node.children;\n        if (children) {\n          traverse(children);\n        }\n        if (!node.isLeaf) {\n          if (!expandKeySet.has(node.key)) {\n            hiddenKeys.add(node.key);\n          } else if (children) {\n            let allHidden = true;\n            for (const childNode of children) {\n              if (!hiddenKeys.has(childNode.key)) {\n                allHidden = false;\n                break;\n              }\n            }\n            if (allHidden) {\n              hiddenExpandIconKeys.add(node.key);\n            } else {\n              hiddenExpandIconKeys.delete(node.key);\n            }\n          }\n        }\n        family.pop();\n      });\n    }\n    traverse(nodes);\n    return expandKeySet;\n  }\n  function isForceHiddenExpandIcon(node) {\n    return hiddenExpandIconKeySet.value.has(node.key);\n  }\n  return {\n    hiddenExpandIconKeySet,\n    hiddenNodeKeySet,\n    doFilter,\n    isForceHiddenExpandIcon\n  };\n}\nexport { useFilter };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}