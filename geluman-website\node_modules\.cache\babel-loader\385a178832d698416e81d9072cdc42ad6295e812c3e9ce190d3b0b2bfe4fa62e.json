{"ast": null, "code": "import { defineComponent, toRef, unref, openBlock, createElementBlock, mergeProps, Fragment, renderList, renderSlot, createVNode, normalizeClass, createBlock, createCommentVNode, normalizeProps } from 'vue';\nimport { skeletonProps } from './skeleton2.mjs';\nimport SkeletonItem from './skeleton-item2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useThrottleRender } from '../../../hooks/use-throttle-render/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElSkeleton\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: skeletonProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"skeleton\");\n    const uiLoading = useThrottleRender(toRef(props, \"loading\"), props.throttle);\n    expose({\n      uiLoading\n    });\n    return (_ctx, _cache) => {\n      return unref(uiLoading) ? (openBlock(), createElementBlock(\"div\", mergeProps({\n        key: 0,\n        class: [unref(ns).b(), unref(ns).is(\"animated\", _ctx.animated)]\n      }, _ctx.$attrs), [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.count, i => {\n        return openBlock(), createElementBlock(Fragment, {\n          key: i\n        }, [unref(uiLoading) ? renderSlot(_ctx.$slots, \"template\", {\n          key: i\n        }, () => [createVNode(SkeletonItem, {\n          class: normalizeClass(unref(ns).is(\"first\")),\n          variant: \"p\"\n        }, null, 8, [\"class\"]), (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.rows, item => {\n          return openBlock(), createBlock(SkeletonItem, {\n            key: item,\n            class: normalizeClass([unref(ns).e(\"paragraph\"), unref(ns).is(\"last\", item === _ctx.rows && _ctx.rows > 1)]),\n            variant: \"p\"\n          }, null, 8, [\"class\"]);\n        }), 128))]) : createCommentVNode(\"v-if\", true)], 64);\n      }), 128))], 16)) : renderSlot(_ctx.$slots, \"default\", normalizeProps(mergeProps({\n        key: 1\n      }, _ctx.$attrs)));\n    };\n  }\n});\nvar Skeleton = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"skeleton.vue\"]]);\nexport { Skeleton as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}