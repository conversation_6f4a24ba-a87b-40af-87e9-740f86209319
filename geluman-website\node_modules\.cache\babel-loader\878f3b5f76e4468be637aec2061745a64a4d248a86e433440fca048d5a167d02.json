{"ast": null, "code": "import { getCurrentInstance, useAttrs, useSlots } from 'vue';\nimport dayjs from 'dayjs';\nimport { isFunction } from '@vue/shared';\nconst useShortcut = lang => {\n  const {\n    emit\n  } = getCurrentInstance();\n  const attrs = useAttrs();\n  const slots = useSlots();\n  const handleShortcutClick = shortcut => {\n    const shortcutValues = isFunction(shortcut.value) ? shortcut.value() : shortcut.value;\n    if (shortcutValues) {\n      emit(\"pick\", [dayjs(shortcutValues[0]).locale(lang.value), dayjs(shortcutValues[1]).locale(lang.value)]);\n      return;\n    }\n    if (shortcut.onClick) {\n      shortcut.onClick({\n        attrs,\n        slots,\n        emit\n      });\n    }\n  };\n  return handleShortcutClick;\n};\nexport { useShortcut };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}