{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, createElementVNode, renderSlot, createBlock, withModifiers, withCtx, createVNode, createCommentVNode, Transition } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { Close } from '@element-plus/icons-vue';\nimport { tagProps, tagEmits } from './tag.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useFormSize } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTag\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: tagProps,\n  emits: tagEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const tagSize = useFormSize();\n    const ns = useNamespace(\"tag\");\n    const containerKls = computed(() => {\n      const {\n        type,\n        hit,\n        effect,\n        closable,\n        round\n      } = props;\n      return [ns.b(), ns.is(\"closable\", closable), ns.m(type || \"primary\"), ns.m(tagSize.value), ns.m(effect), ns.is(\"hit\", hit), ns.is(\"round\", round)];\n    });\n    const handleClose = event => {\n      emit(\"close\", event);\n    };\n    const handleClick = event => {\n      emit(\"click\", event);\n    };\n    const handleVNodeMounted = vnode => {\n      var _a, _b, _c;\n      if ((_c = (_b = (_a = vnode == null ? void 0 : vnode.component) == null ? void 0 : _a.subTree) == null ? void 0 : _b.component) == null ? void 0 : _c.bum) {\n        vnode.component.subTree.component.bum = null;\n      }\n    };\n    return (_ctx, _cache) => {\n      return _ctx.disableTransitions ? (openBlock(), createElementBlock(\"span\", {\n        key: 0,\n        class: normalizeClass(unref(containerKls)),\n        style: normalizeStyle({\n          backgroundColor: _ctx.color\n        }),\n        onClick: handleClick\n      }, [createElementVNode(\"span\", {\n        class: normalizeClass(unref(ns).e(\"content\"))\n      }, [renderSlot(_ctx.$slots, \"default\")], 2), _ctx.closable ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"close\")),\n        onClick: withModifiers(handleClose, [\"stop\"])\n      }, {\n        default: withCtx(() => [createVNode(unref(Close))]),\n        _: 1\n      }, 8, [\"class\", \"onClick\"])) : createCommentVNode(\"v-if\", true)], 6)) : (openBlock(), createBlock(Transition, {\n        key: 1,\n        name: `${unref(ns).namespace.value}-zoom-in-center`,\n        appear: \"\",\n        onVnodeMounted: handleVNodeMounted\n      }, {\n        default: withCtx(() => [createElementVNode(\"span\", {\n          class: normalizeClass(unref(containerKls)),\n          style: normalizeStyle({\n            backgroundColor: _ctx.color\n          }),\n          onClick: handleClick\n        }, [createElementVNode(\"span\", {\n          class: normalizeClass(unref(ns).e(\"content\"))\n        }, [renderSlot(_ctx.$slots, \"default\")], 2), _ctx.closable ? (openBlock(), createBlock(unref(ElIcon), {\n          key: 0,\n          class: normalizeClass(unref(ns).e(\"close\")),\n          onClick: withModifiers(handleClose, [\"stop\"])\n        }, {\n          default: withCtx(() => [createVNode(unref(Close))]),\n          _: 1\n        }, 8, [\"class\", \"onClick\"])) : createCommentVNode(\"v-if\", true)], 6)]),\n        _: 3\n      }, 8, [\"name\"]));\n    };\n  }\n});\nvar Tag = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tag.vue\"]]);\nexport { Tag as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}