{"ast": null, "code": "import { renderSlot, createVNode } from 'vue';\nimport { ElEmpty } from '../../../empty/index.mjs';\nconst Footer = (props, {\n  slots\n}) => {\n  const defaultSlot = renderSlot(slots, \"default\", {}, () => [createVNode(ElEmpty, null, null)]);\n  return createVNode(\"div\", {\n    \"class\": props.class,\n    \"style\": props.style\n  }, [defaultSlot]);\n};\nFooter.displayName = \"ElTableV2Empty\";\nvar Empty = Footer;\nexport { Empty as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}