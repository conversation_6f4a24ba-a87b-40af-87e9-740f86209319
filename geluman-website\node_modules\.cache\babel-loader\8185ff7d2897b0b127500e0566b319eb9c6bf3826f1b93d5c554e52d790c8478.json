{"ast": null, "code": "import { disabledTimeListsProps } from './shared.mjs';\nimport { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nconst basicTimeSpinnerProps = buildProps({\n  role: {\n    type: String,\n    required: true\n  },\n  spinnerDate: {\n    type: definePropType(Object),\n    required: true\n  },\n  showSeconds: {\n    type: Boolean,\n    default: true\n  },\n  arrowControl: Boolean,\n  amPmMode: {\n    type: definePropType(String),\n    default: \"\"\n  },\n  ...disabledTimeListsProps\n});\nexport { basicTimeSpinnerProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}