{"ast": null, "code": "import { isNil } from 'lodash-unified';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nimport { isArray } from '@vue/shared';\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nconst LEFT_CHECK_CHANGE_EVENT = \"left-check-change\";\nconst RIGHT_CHECK_CHANGE_EVENT = \"right-check-change\";\nconst transferProps = buildProps({\n  data: {\n    type: definePropType(Array),\n    default: () => []\n  },\n  titles: {\n    type: definePropType(Array),\n    default: () => []\n  },\n  buttonTexts: {\n    type: definePropType(Array),\n    default: () => []\n  },\n  filterPlaceholder: String,\n  filterMethod: {\n    type: definePropType(Function)\n  },\n  leftDefaultChecked: {\n    type: definePropType(Array),\n    default: () => []\n  },\n  rightDefaultChecked: {\n    type: definePropType(Array),\n    default: () => []\n  },\n  renderContent: {\n    type: definePropType(Function)\n  },\n  modelValue: {\n    type: definePropType(Array),\n    default: () => []\n  },\n  format: {\n    type: definePropType(Object),\n    default: () => ({})\n  },\n  filterable: Boolean,\n  props: {\n    type: definePropType(Object),\n    default: () => mutable({\n      label: \"label\",\n      key: \"key\",\n      disabled: \"disabled\"\n    })\n  },\n  targetOrder: {\n    type: String,\n    values: [\"original\", \"push\", \"unshift\"],\n    default: \"original\"\n  },\n  validateEvent: {\n    type: Boolean,\n    default: true\n  }\n});\nconst transferCheckedChangeFn = (value, movedKeys) => [value, movedKeys].every(isArray) || isArray(value) && isNil(movedKeys);\nconst transferEmits = {\n  [CHANGE_EVENT]: (value, direction, movedKeys) => [value, movedKeys].every(isArray) && [\"left\", \"right\"].includes(direction),\n  [UPDATE_MODEL_EVENT]: value => isArray(value),\n  [LEFT_CHECK_CHANGE_EVENT]: transferCheckedChangeFn,\n  [RIGHT_CHECK_CHANGE_EVENT]: transferCheckedChangeFn\n};\nexport { LEFT_CHECK_CHANGE_EVENT, RIGHT_CHECK_CHANGE_EVENT, transferCheckedChangeFn, transferEmits, transferProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}