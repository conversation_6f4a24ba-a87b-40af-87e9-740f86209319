{"ast": null, "code": "import { defineComponent, inject, computed, onMounted, watch, onBeforeUnmount, openBlock, createBlock, unref, mergeProps, withCtx, renderSlot, createCommentVNode } from 'vue';\nimport { isNil } from 'lodash-unified';\nimport { unrefElement } from '@vueuse/core';\nimport { POPPER_INJECTION_KEY } from './constants.mjs';\nimport { popperTriggerProps } from './trigger.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useForwardRef } from '../../../hooks/use-forward-ref/index.mjs';\nimport { isFocusable } from '../../../utils/dom/aria.mjs';\nimport { OnlyChild } from '../../slot/src/only-child.mjs';\nimport { isElement } from '../../../utils/types.mjs';\nconst __default__ = defineComponent({\n  name: \"ElPopperTrigger\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: popperTriggerProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const {\n      role,\n      triggerRef\n    } = inject(POPPER_INJECTION_KEY, void 0);\n    useForwardRef(triggerRef);\n    const ariaControls = computed(() => {\n      return ariaHaspopup.value ? props.id : void 0;\n    });\n    const ariaDescribedby = computed(() => {\n      if (role && role.value === \"tooltip\") {\n        return props.open && props.id ? props.id : void 0;\n      }\n      return void 0;\n    });\n    const ariaHaspopup = computed(() => {\n      if (role && role.value !== \"tooltip\") {\n        return role.value;\n      }\n      return void 0;\n    });\n    const ariaExpanded = computed(() => {\n      return ariaHaspopup.value ? `${props.open}` : void 0;\n    });\n    let virtualTriggerAriaStopWatch = void 0;\n    const TRIGGER_ELE_EVENTS = [\"onMouseenter\", \"onMouseleave\", \"onClick\", \"onKeydown\", \"onFocus\", \"onBlur\", \"onContextmenu\"];\n    onMounted(() => {\n      watch(() => props.virtualRef, virtualEl => {\n        if (virtualEl) {\n          triggerRef.value = unrefElement(virtualEl);\n        }\n      }, {\n        immediate: true\n      });\n      watch(triggerRef, (el, prevEl) => {\n        virtualTriggerAriaStopWatch == null ? void 0 : virtualTriggerAriaStopWatch();\n        virtualTriggerAriaStopWatch = void 0;\n        if (isElement(el)) {\n          TRIGGER_ELE_EVENTS.forEach(eventName => {\n            var _a;\n            const handler = props[eventName];\n            if (handler) {\n              el.addEventListener(eventName.slice(2).toLowerCase(), handler);\n              (_a = prevEl == null ? void 0 : prevEl.removeEventListener) == null ? void 0 : _a.call(prevEl, eventName.slice(2).toLowerCase(), handler);\n            }\n          });\n          if (isFocusable(el)) {\n            virtualTriggerAriaStopWatch = watch([ariaControls, ariaDescribedby, ariaHaspopup, ariaExpanded], watches => {\n              [\"aria-controls\", \"aria-describedby\", \"aria-haspopup\", \"aria-expanded\"].forEach((key, idx) => {\n                isNil(watches[idx]) ? el.removeAttribute(key) : el.setAttribute(key, watches[idx]);\n              });\n            }, {\n              immediate: true\n            });\n          }\n        }\n        if (isElement(prevEl) && isFocusable(prevEl)) {\n          [\"aria-controls\", \"aria-describedby\", \"aria-haspopup\", \"aria-expanded\"].forEach(key => prevEl.removeAttribute(key));\n        }\n      }, {\n        immediate: true\n      });\n    });\n    onBeforeUnmount(() => {\n      virtualTriggerAriaStopWatch == null ? void 0 : virtualTriggerAriaStopWatch();\n      virtualTriggerAriaStopWatch = void 0;\n      if (triggerRef.value && isElement(triggerRef.value)) {\n        const el = triggerRef.value;\n        TRIGGER_ELE_EVENTS.forEach(eventName => {\n          const handler = props[eventName];\n          if (handler) {\n            el.removeEventListener(eventName.slice(2).toLowerCase(), handler);\n          }\n        });\n        triggerRef.value = void 0;\n      }\n    });\n    expose({\n      triggerRef\n    });\n    return (_ctx, _cache) => {\n      return !_ctx.virtualTriggering ? (openBlock(), createBlock(unref(OnlyChild), mergeProps({\n        key: 0\n      }, _ctx.$attrs, {\n        \"aria-controls\": unref(ariaControls),\n        \"aria-describedby\": unref(ariaDescribedby),\n        \"aria-expanded\": unref(ariaExpanded),\n        \"aria-haspopup\": unref(ariaHaspopup)\n      }), {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 16, [\"aria-controls\", \"aria-describedby\", \"aria-expanded\", \"aria-haspopup\"])) : createCommentVNode(\"v-if\", true);\n    };\n  }\n});\nvar ElPopperTrigger = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"trigger.vue\"]]);\nexport { ElPopperTrigger as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}