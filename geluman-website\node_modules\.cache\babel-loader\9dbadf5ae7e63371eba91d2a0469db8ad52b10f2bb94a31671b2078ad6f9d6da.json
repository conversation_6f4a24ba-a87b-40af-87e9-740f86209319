{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { isVNode, createVNode, render } from 'vue';\nimport NotificationConstructor from './notification2.mjs';\nimport { notificationTypes } from './notification.mjs';\nimport { isClient } from '@vueuse/core';\nimport { isString, isFunction } from '@vue/shared';\nimport { isElement, isUndefined } from '../../../utils/types.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nconst notifications = {\n  \"top-left\": [],\n  \"top-right\": [],\n  \"bottom-left\": [],\n  \"bottom-right\": []\n};\nconst GAP_SIZE = 16;\nlet seed = 1;\nconst notify = function (options = {}, context) {\n  if (!isClient) return {\n    close: () => void 0\n  };\n  if (isString(options) || isVNode(options)) {\n    options = {\n      message: options\n    };\n  }\n  const position = options.position || \"top-right\";\n  let verticalOffset = options.offset || 0;\n  notifications[position].forEach(({\n    vm: vm2\n  }) => {\n    var _a;\n    verticalOffset += (((_a = vm2.el) == null ? void 0 : _a.offsetHeight) || 0) + GAP_SIZE;\n  });\n  verticalOffset += GAP_SIZE;\n  const id = `notification_${seed++}`;\n  const userOnClose = options.onClose;\n  const props = {\n    ...options,\n    offset: verticalOffset,\n    id,\n    onClose: () => {\n      close(id, position, userOnClose);\n    }\n  };\n  let appendTo = document.body;\n  if (isElement(options.appendTo)) {\n    appendTo = options.appendTo;\n  } else if (isString(options.appendTo)) {\n    appendTo = document.querySelector(options.appendTo);\n  }\n  if (!isElement(appendTo)) {\n    debugWarn(\"ElNotification\", \"the appendTo option is not an HTMLElement. Falling back to document.body.\");\n    appendTo = document.body;\n  }\n  const container = document.createElement(\"div\");\n  const vm = createVNode(NotificationConstructor, props, isFunction(props.message) ? props.message : isVNode(props.message) ? () => props.message : null);\n  vm.appContext = isUndefined(context) ? notify._context : context;\n  vm.props.onDestroy = () => {\n    render(null, container);\n  };\n  render(vm, container);\n  notifications[position].push({\n    vm\n  });\n  appendTo.appendChild(container.firstElementChild);\n  return {\n    close: () => {\n      vm.component.exposed.visible.value = false;\n    }\n  };\n};\nnotificationTypes.forEach(type => {\n  notify[type] = (options = {}, appContext) => {\n    if (isString(options) || isVNode(options)) {\n      options = {\n        message: options\n      };\n    }\n    return notify({\n      ...options,\n      type\n    }, appContext);\n  };\n});\nfunction close(id, position, userOnClose) {\n  const orientedNotifications = notifications[position];\n  const idx = orientedNotifications.findIndex(({\n    vm: vm2\n  }) => {\n    var _a;\n    return ((_a = vm2.component) == null ? void 0 : _a.props.id) === id;\n  });\n  if (idx === -1) return;\n  const {\n    vm\n  } = orientedNotifications[idx];\n  if (!vm) return;\n  userOnClose == null ? void 0 : userOnClose(vm);\n  const removedHeight = vm.el.offsetHeight;\n  const verticalPos = position.split(\"-\")[0];\n  orientedNotifications.splice(idx, 1);\n  const len = orientedNotifications.length;\n  if (len < 1) return;\n  for (let i = idx; i < len; i++) {\n    const {\n      el,\n      component\n    } = orientedNotifications[i].vm;\n    const pos = Number.parseInt(el.style[verticalPos], 10) - removedHeight - GAP_SIZE;\n    component.props.offset = pos;\n  }\n}\nfunction closeAll() {\n  for (const orientedNotifications of Object.values(notifications)) {\n    orientedNotifications.forEach(({\n      vm\n    }) => {\n      vm.component.exposed.visible.value = false;\n    });\n  }\n}\nnotify.closeAll = closeAll;\nnotify._context = null;\nexport { close, closeAll, notify as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}