{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { isBoolean } from '../../../utils/types.mjs';\nimport { CHANGE_EVENT } from '../../../constants/event.mjs';\nconst checkTagProps = buildProps({\n  checked: <PERSON><PERSON>an,\n  disabled: Boolean,\n  type: {\n    type: String,\n    values: [\"primary\", \"success\", \"info\", \"warning\", \"danger\"],\n    default: \"primary\"\n  }\n});\nconst checkTagEmits = {\n  \"update:checked\": value => isBoolean(value),\n  [CHANGE_EVENT]: value => isBoolean(value)\n};\nexport { checkTagEmits, checkTagProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}