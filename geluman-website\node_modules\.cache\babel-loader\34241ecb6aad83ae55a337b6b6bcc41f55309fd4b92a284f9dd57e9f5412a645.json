{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, normalizeClass, unref, toDisplayString } from 'vue';\nimport { usePagination } from '../usePagination.mjs';\nimport { paginationTotalProps } from './total.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElPaginationTotal\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: paginationTotalProps,\n  setup(__props) {\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"pagination\");\n    const {\n      disabled\n    } = usePagination();\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"span\", {\n        class: normalizeClass(unref(ns).e(\"total\")),\n        disabled: unref(disabled)\n      }, toDisplayString(unref(t)(\"el.pagination.total\", {\n        total: _ctx.total\n      })), 11, [\"disabled\"]);\n    };\n  }\n});\nvar Total = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"total.vue\"]]);\nexport { Total as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}