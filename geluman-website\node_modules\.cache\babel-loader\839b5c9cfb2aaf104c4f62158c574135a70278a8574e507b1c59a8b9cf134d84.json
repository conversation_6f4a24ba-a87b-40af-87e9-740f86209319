{"ast": null, "code": "import { useDelayedToggleProps } from '../../../hooks/use-delayed-toggle/index.mjs';\nimport { popperContentProps } from '../../popper/src/content.mjs';\nimport { teleportProps } from '../../teleport/src/teleport.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nconst useTooltipContentProps = buildProps({\n  ...useDelayedToggleProps,\n  ...popperContentProps,\n  appendTo: {\n    type: teleportProps.to.type\n  },\n  content: {\n    type: String,\n    default: \"\"\n  },\n  rawContent: Boolean,\n  persistent: Boolean,\n  visible: {\n    type: definePropType(Boolean),\n    default: null\n  },\n  transition: String,\n  teleported: {\n    type: Boolean,\n    default: true\n  },\n  disabled: <PERSON>olean,\n  ...useAriaProps([\"ariaLabel\"])\n});\nexport { useTooltipContentProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}