{"ast": null, "code": "import { cAF, rAF } from './raf.mjs';\nfunction throttleByRaf(cb) {\n  let timer = 0;\n  const throttle = (...args) => {\n    if (timer) {\n      cAF(timer);\n    }\n    timer = rAF(() => {\n      cb(...args);\n      timer = 0;\n    });\n  };\n  throttle.cancel = () => {\n    cAF(timer);\n    timer = 0;\n  };\n  return throttle;\n}\nexport { throttleByRaf };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}