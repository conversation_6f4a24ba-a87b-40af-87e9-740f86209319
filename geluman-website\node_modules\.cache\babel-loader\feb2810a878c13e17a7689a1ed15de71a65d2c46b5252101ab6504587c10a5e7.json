{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, normalizeStyle, createBlock, withCtx, resolveDynamicComponent, createCommentVNode, renderSlot, toDisplayString } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { timelineItemProps } from './timeline-item.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTimelineItem\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: timelineItemProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"timeline-item\");\n    const defaultNodeKls = computed(() => [ns.e(\"node\"), ns.em(\"node\", props.size || \"\"), ns.em(\"node\", props.type || \"\"), ns.is(\"hollow\", props.hollow)]);\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"li\", {\n        class: normalizeClass([unref(ns).b(), {\n          [unref(ns).e(\"center\")]: _ctx.center\n        }])\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"tail\"))\n      }, null, 2), !_ctx.$slots.dot ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(defaultNodeKls)),\n        style: normalizeStyle({\n          backgroundColor: _ctx.color\n        })\n      }, [_ctx.icon ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"icon\"))\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.icon)))]),\n        _: 1\n      }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true)], 6)) : createCommentVNode(\"v-if\", true), _ctx.$slots.dot ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(unref(ns).e(\"dot\"))\n      }, [renderSlot(_ctx.$slots, \"dot\")], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"wrapper\"))\n      }, [!_ctx.hideTimestamp && _ctx.placement === \"top\" ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass([unref(ns).e(\"timestamp\"), unref(ns).is(\"top\")])\n      }, toDisplayString(_ctx.timestamp), 3)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"content\"))\n      }, [renderSlot(_ctx.$slots, \"default\")], 2), !_ctx.hideTimestamp && _ctx.placement === \"bottom\" ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass([unref(ns).e(\"timestamp\"), unref(ns).is(\"bottom\")])\n      }, toDisplayString(_ctx.timestamp), 3)) : createCommentVNode(\"v-if\", true)], 2)], 2);\n    };\n  }\n});\nvar TimelineItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"timeline-item.vue\"]]);\nexport { TimelineItem as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}