{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\n/*!\n  * core-base v9.14.2\n  * (c) 2024 kazuya kawa<PERSON>\n  * Released under the MIT License.\n  */\nimport { getGlobalThis, isObject, isFunction, isString, create, isNumber, isPlainObject, assign, join, toDisplayString, isArray, incrementer, format as format$1, isPromise, isBoolean, warn, isRegExp, warnOnce, hasOwn, escapeHtml, inBrowser, mark, measure, isEmptyObject, generateCodeFrame, generateFormatCacheKey, isDate } from '@intlify/shared';\nimport { CompileWarnCodes, CompileErrorCodes, createCompileError, detectHtmlTag, defaultOnError, baseCompile as baseCompile$1 } from '@intlify/message-compiler';\nexport { CompileErrorCodes, createCompileError } from '@intlify/message-compiler';\n\n/**\n * This is only called in esm-bundler builds.\n * istanbul-ignore-next\n */\nfunction initFeatureFlags() {\n  if (typeof __INTLIFY_PROD_DEVTOOLS__ !== 'boolean') {\n    getGlobalThis().__INTLIFY_PROD_DEVTOOLS__ = false;\n  }\n  if (typeof __INTLIFY_JIT_COMPILATION__ !== 'boolean') {\n    getGlobalThis().__INTLIFY_JIT_COMPILATION__ = false;\n  }\n  if (typeof __INTLIFY_DROP_MESSAGE_COMPILER__ !== 'boolean') {\n    getGlobalThis().__INTLIFY_DROP_MESSAGE_COMPILER__ = false;\n  }\n}\nconst pathStateMachine = [];\npathStateMachine[0 /* States.BEFORE_PATH */] = {\n  [\"w\" /* PathCharTypes.WORKSPACE */]: [0 /* States.BEFORE_PATH */],\n  [\"i\" /* PathCharTypes.IDENT */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */],\n  [\"[\" /* PathCharTypes.LEFT_BRACKET */]: [4 /* States.IN_SUB_PATH */],\n  [\"o\" /* PathCharTypes.END_OF_FAIL */]: [7 /* States.AFTER_PATH */]\n};\npathStateMachine[1 /* States.IN_PATH */] = {\n  [\"w\" /* PathCharTypes.WORKSPACE */]: [1 /* States.IN_PATH */],\n  [\".\" /* PathCharTypes.DOT */]: [2 /* States.BEFORE_IDENT */],\n  [\"[\" /* PathCharTypes.LEFT_BRACKET */]: [4 /* States.IN_SUB_PATH */],\n  [\"o\" /* PathCharTypes.END_OF_FAIL */]: [7 /* States.AFTER_PATH */]\n};\npathStateMachine[2 /* States.BEFORE_IDENT */] = {\n  [\"w\" /* PathCharTypes.WORKSPACE */]: [2 /* States.BEFORE_IDENT */],\n  [\"i\" /* PathCharTypes.IDENT */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */],\n  [\"0\" /* PathCharTypes.ZERO */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */]\n};\npathStateMachine[3 /* States.IN_IDENT */] = {\n  [\"i\" /* PathCharTypes.IDENT */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */],\n  [\"0\" /* PathCharTypes.ZERO */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */],\n  [\"w\" /* PathCharTypes.WORKSPACE */]: [1 /* States.IN_PATH */, 1 /* Actions.PUSH */],\n  [\".\" /* PathCharTypes.DOT */]: [2 /* States.BEFORE_IDENT */, 1 /* Actions.PUSH */],\n  [\"[\" /* PathCharTypes.LEFT_BRACKET */]: [4 /* States.IN_SUB_PATH */, 1 /* Actions.PUSH */],\n  [\"o\" /* PathCharTypes.END_OF_FAIL */]: [7 /* States.AFTER_PATH */, 1 /* Actions.PUSH */]\n};\npathStateMachine[4 /* States.IN_SUB_PATH */] = {\n  [\"'\" /* PathCharTypes.SINGLE_QUOTE */]: [5 /* States.IN_SINGLE_QUOTE */, 0 /* Actions.APPEND */],\n  [\"\\\"\" /* PathCharTypes.DOUBLE_QUOTE */]: [6 /* States.IN_DOUBLE_QUOTE */, 0 /* Actions.APPEND */],\n  [\"[\" /* PathCharTypes.LEFT_BRACKET */]: [4 /* States.IN_SUB_PATH */, 2 /* Actions.INC_SUB_PATH_DEPTH */],\n  [\"]\" /* PathCharTypes.RIGHT_BRACKET */]: [1 /* States.IN_PATH */, 3 /* Actions.PUSH_SUB_PATH */],\n  [\"o\" /* PathCharTypes.END_OF_FAIL */]: 8 /* States.ERROR */,\n  [\"l\" /* PathCharTypes.ELSE */]: [4 /* States.IN_SUB_PATH */, 0 /* Actions.APPEND */]\n};\npathStateMachine[5 /* States.IN_SINGLE_QUOTE */] = {\n  [\"'\" /* PathCharTypes.SINGLE_QUOTE */]: [4 /* States.IN_SUB_PATH */, 0 /* Actions.APPEND */],\n  [\"o\" /* PathCharTypes.END_OF_FAIL */]: 8 /* States.ERROR */,\n  [\"l\" /* PathCharTypes.ELSE */]: [5 /* States.IN_SINGLE_QUOTE */, 0 /* Actions.APPEND */]\n};\npathStateMachine[6 /* States.IN_DOUBLE_QUOTE */] = {\n  [\"\\\"\" /* PathCharTypes.DOUBLE_QUOTE */]: [4 /* States.IN_SUB_PATH */, 0 /* Actions.APPEND */],\n  [\"o\" /* PathCharTypes.END_OF_FAIL */]: 8 /* States.ERROR */,\n  [\"l\" /* PathCharTypes.ELSE */]: [6 /* States.IN_DOUBLE_QUOTE */, 0 /* Actions.APPEND */]\n};\n/**\n * Check if an expression is a literal value.\n */\nconst literalValueRE = /^\\s?(?:true|false|-?[\\d.]+|'[^']*'|\"[^\"]*\")\\s?$/;\nfunction isLiteral(exp) {\n  return literalValueRE.test(exp);\n}\n/**\n * Strip quotes from a string\n */\nfunction stripQuotes(str) {\n  const a = str.charCodeAt(0);\n  const b = str.charCodeAt(str.length - 1);\n  return a === b && (a === 0x22 || a === 0x27) ? str.slice(1, -1) : str;\n}\n/**\n * Determine the type of a character in a keypath.\n */\nfunction getPathCharType(ch) {\n  if (ch === undefined || ch === null) {\n    return \"o\" /* PathCharTypes.END_OF_FAIL */;\n  }\n  const code = ch.charCodeAt(0);\n  switch (code) {\n    case 0x5b: // [\n    case 0x5d: // ]\n    case 0x2e: // .\n    case 0x22: // \"\n    case 0x27:\n      // '\n      return ch;\n    case 0x5f: // _\n    case 0x24: // $\n    case 0x2d:\n      // -\n      return \"i\" /* PathCharTypes.IDENT */;\n    case 0x09: // Tab (HT)\n    case 0x0a: // Newline (LF)\n    case 0x0d: // Return (CR)\n    case 0xa0: // No-break space (NBSP)\n    case 0xfeff: // Byte Order Mark (BOM)\n    case 0x2028: // Line Separator (LS)\n    case 0x2029:\n      // Paragraph Separator (PS)\n      return \"w\" /* PathCharTypes.WORKSPACE */;\n  }\n  return \"i\" /* PathCharTypes.IDENT */;\n}\n/**\n * Format a subPath, return its plain form if it is\n * a literal string or number. Otherwise prepend the\n * dynamic indicator (*).\n */\nfunction formatSubPath(path) {\n  const trimmed = path.trim();\n  // invalid leading 0\n  if (path.charAt(0) === '0' && isNaN(parseInt(path))) {\n    return false;\n  }\n  return isLiteral(trimmed) ? stripQuotes(trimmed) : \"*\" /* PathCharTypes.ASTARISK */ + trimmed;\n}\n/**\n * Parse a string path into an array of segments\n */\nfunction parse(path) {\n  const keys = [];\n  let index = -1;\n  let mode = 0 /* States.BEFORE_PATH */;\n  let subPathDepth = 0;\n  let c;\n  let key; // eslint-disable-line\n  let newChar;\n  let type;\n  let transition;\n  let action;\n  let typeMap;\n  const actions = [];\n  actions[0 /* Actions.APPEND */] = () => {\n    if (key === undefined) {\n      key = newChar;\n    } else {\n      key += newChar;\n    }\n  };\n  actions[1 /* Actions.PUSH */] = () => {\n    if (key !== undefined) {\n      keys.push(key);\n      key = undefined;\n    }\n  };\n  actions[2 /* Actions.INC_SUB_PATH_DEPTH */] = () => {\n    actions[0 /* Actions.APPEND */]();\n    subPathDepth++;\n  };\n  actions[3 /* Actions.PUSH_SUB_PATH */] = () => {\n    if (subPathDepth > 0) {\n      subPathDepth--;\n      mode = 4 /* States.IN_SUB_PATH */;\n      actions[0 /* Actions.APPEND */]();\n    } else {\n      subPathDepth = 0;\n      if (key === undefined) {\n        return false;\n      }\n      key = formatSubPath(key);\n      if (key === false) {\n        return false;\n      } else {\n        actions[1 /* Actions.PUSH */]();\n      }\n    }\n  };\n  function maybeUnescapeQuote() {\n    const nextChar = path[index + 1];\n    if (mode === 5 /* States.IN_SINGLE_QUOTE */ && nextChar === \"'\" /* PathCharTypes.SINGLE_QUOTE */ || mode === 6 /* States.IN_DOUBLE_QUOTE */ && nextChar === \"\\\"\" /* PathCharTypes.DOUBLE_QUOTE */) {\n      index++;\n      newChar = '\\\\' + nextChar;\n      actions[0 /* Actions.APPEND */]();\n      return true;\n    }\n  }\n  while (mode !== null) {\n    index++;\n    c = path[index];\n    if (c === '\\\\' && maybeUnescapeQuote()) {\n      continue;\n    }\n    type = getPathCharType(c);\n    typeMap = pathStateMachine[mode];\n    transition = typeMap[type] || typeMap[\"l\" /* PathCharTypes.ELSE */] || 8 /* States.ERROR */;\n    // check parse error\n    if (transition === 8 /* States.ERROR */) {\n      return;\n    }\n    mode = transition[0];\n    if (transition[1] !== undefined) {\n      action = actions[transition[1]];\n      if (action) {\n        newChar = c;\n        if (action() === false) {\n          return;\n        }\n      }\n    }\n    // check parse finish\n    if (mode === 7 /* States.AFTER_PATH */) {\n      return keys;\n    }\n  }\n}\n// path token cache\nconst cache = new Map();\n/**\n * key-value message resolver\n *\n * @remarks\n * Resolves messages with the key-value structure. Note that messages with a hierarchical structure such as objects cannot be resolved\n *\n * @param obj - A target object to be resolved with path\n * @param path - A {@link Path | path} to resolve the value of message\n *\n * @returns A resolved {@link PathValue | path value}\n *\n * @VueI18nGeneral\n */\nfunction resolveWithKeyValue(obj, path) {\n  return isObject(obj) ? obj[path] : null;\n}\n/**\n * message resolver\n *\n * @remarks\n * Resolves messages. messages with a hierarchical structure such as objects can be resolved. This resolver is used in VueI18n as default.\n *\n * @param obj - A target object to be resolved with path\n * @param path - A {@link Path | path} to resolve the value of message\n *\n * @returns A resolved {@link PathValue | path value}\n *\n * @VueI18nGeneral\n */\nfunction resolveValue$1(obj, path) {\n  // check object\n  if (!isObject(obj)) {\n    return null;\n  }\n  // parse path\n  let hit = cache.get(path);\n  if (!hit) {\n    hit = parse(path);\n    if (hit) {\n      cache.set(path, hit);\n    }\n  }\n  // check hit\n  if (!hit) {\n    return null;\n  }\n  // resolve path value\n  const len = hit.length;\n  let last = obj;\n  let i = 0;\n  while (i < len) {\n    const val = last[hit[i]];\n    if (val === undefined) {\n      return null;\n    }\n    if (isFunction(last)) {\n      return null;\n    }\n    last = val;\n    i++;\n  }\n  return last;\n}\nconst DEFAULT_MODIFIER = str => str;\nconst DEFAULT_MESSAGE = ctx => ''; // eslint-disable-line\nconst DEFAULT_MESSAGE_DATA_TYPE = 'text';\nconst DEFAULT_NORMALIZE = values => values.length === 0 ? '' : join(values);\nconst DEFAULT_INTERPOLATE = toDisplayString;\nfunction pluralDefault(choice, choicesLength) {\n  choice = Math.abs(choice);\n  if (choicesLength === 2) {\n    // prettier-ignore\n    return choice ? choice > 1 ? 1 : 0 : 1;\n  }\n  return choice ? Math.min(choice, 2) : 0;\n}\nfunction getPluralIndex(options) {\n  // prettier-ignore\n  const index = isNumber(options.pluralIndex) ? options.pluralIndex : -1;\n  // prettier-ignore\n  return options.named && (isNumber(options.named.count) || isNumber(options.named.n)) ? isNumber(options.named.count) ? options.named.count : isNumber(options.named.n) ? options.named.n : index : index;\n}\nfunction normalizeNamed(pluralIndex, props) {\n  if (!props.count) {\n    props.count = pluralIndex;\n  }\n  if (!props.n) {\n    props.n = pluralIndex;\n  }\n}\nfunction createMessageContext(options = {}) {\n  const locale = options.locale;\n  const pluralIndex = getPluralIndex(options);\n  const pluralRule = isObject(options.pluralRules) && isString(locale) && isFunction(options.pluralRules[locale]) ? options.pluralRules[locale] : pluralDefault;\n  const orgPluralRule = isObject(options.pluralRules) && isString(locale) && isFunction(options.pluralRules[locale]) ? pluralDefault : undefined;\n  const plural = messages => {\n    return messages[pluralRule(pluralIndex, messages.length, orgPluralRule)];\n  };\n  const _list = options.list || [];\n  const list = index => _list[index];\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const _named = options.named || create();\n  isNumber(options.pluralIndex) && normalizeNamed(pluralIndex, _named);\n  const named = key => _named[key];\n  function message(key) {\n    // prettier-ignore\n    const msg = isFunction(options.messages) ? options.messages(key) : isObject(options.messages) ? options.messages[key] : false;\n    return !msg ? options.parent ? options.parent.message(key) // resolve from parent messages\n    : DEFAULT_MESSAGE : msg;\n  }\n  const _modifier = name => options.modifiers ? options.modifiers[name] : DEFAULT_MODIFIER;\n  const normalize = isPlainObject(options.processor) && isFunction(options.processor.normalize) ? options.processor.normalize : DEFAULT_NORMALIZE;\n  const interpolate = isPlainObject(options.processor) && isFunction(options.processor.interpolate) ? options.processor.interpolate : DEFAULT_INTERPOLATE;\n  const type = isPlainObject(options.processor) && isString(options.processor.type) ? options.processor.type : DEFAULT_MESSAGE_DATA_TYPE;\n  const linked = (key, ...args) => {\n    const [arg1, arg2] = args;\n    let type = 'text';\n    let modifier = '';\n    if (args.length === 1) {\n      if (isObject(arg1)) {\n        modifier = arg1.modifier || modifier;\n        type = arg1.type || type;\n      } else if (isString(arg1)) {\n        modifier = arg1 || modifier;\n      }\n    } else if (args.length === 2) {\n      if (isString(arg1)) {\n        modifier = arg1 || modifier;\n      }\n      if (isString(arg2)) {\n        type = arg2 || type;\n      }\n    }\n    const ret = message(key)(ctx);\n    const msg =\n    // The message in vnode resolved with linked are returned as an array by processor.nomalize\n    type === 'vnode' && isArray(ret) && modifier ? ret[0] : ret;\n    return modifier ? _modifier(modifier)(msg, type) : msg;\n  };\n  const ctx = {\n    [\"list\" /* HelperNameMap.LIST */]: list,\n    [\"named\" /* HelperNameMap.NAMED */]: named,\n    [\"plural\" /* HelperNameMap.PLURAL */]: plural,\n    [\"linked\" /* HelperNameMap.LINKED */]: linked,\n    [\"message\" /* HelperNameMap.MESSAGE */]: message,\n    [\"type\" /* HelperNameMap.TYPE */]: type,\n    [\"interpolate\" /* HelperNameMap.INTERPOLATE */]: interpolate,\n    [\"normalize\" /* HelperNameMap.NORMALIZE */]: normalize,\n    [\"values\" /* HelperNameMap.VALUES */]: assign(create(), _list, _named)\n  };\n  return ctx;\n}\nlet devtools = null;\nfunction setDevToolsHook(hook) {\n  devtools = hook;\n}\nfunction getDevToolsHook() {\n  return devtools;\n}\nfunction initI18nDevTools(i18n, version, meta) {\n  // TODO: queue if devtools is undefined\n  devtools && devtools.emit(\"i18n:init\" /* IntlifyDevToolsHooks.I18nInit */, {\n    timestamp: Date.now(),\n    i18n,\n    version,\n    meta\n  });\n}\nconst translateDevTools = /* #__PURE__*/createDevToolsHook(\"function:translate\" /* IntlifyDevToolsHooks.FunctionTranslate */);\nfunction createDevToolsHook(hook) {\n  return payloads => devtools && devtools.emit(hook, payloads);\n}\nconst code$1 = CompileWarnCodes.__EXTEND_POINT__;\nconst inc$1 = incrementer(code$1);\nconst CoreWarnCodes = {\n  NOT_FOUND_KEY: code$1,\n  // 2\n  FALLBACK_TO_TRANSLATE: inc$1(),\n  // 3\n  CANNOT_FORMAT_NUMBER: inc$1(),\n  // 4\n  FALLBACK_TO_NUMBER_FORMAT: inc$1(),\n  // 5\n  CANNOT_FORMAT_DATE: inc$1(),\n  // 6\n  FALLBACK_TO_DATE_FORMAT: inc$1(),\n  // 7\n  EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER: inc$1(),\n  // 8\n  __EXTEND_POINT__: inc$1() // 9\n};\n/** @internal */\nconst warnMessages = {\n  [CoreWarnCodes.NOT_FOUND_KEY]: `Not found '{key}' key in '{locale}' locale messages.`,\n  [CoreWarnCodes.FALLBACK_TO_TRANSLATE]: `Fall back to translate '{key}' key with '{target}' locale.`,\n  [CoreWarnCodes.CANNOT_FORMAT_NUMBER]: `Cannot format a number value due to not supported Intl.NumberFormat.`,\n  [CoreWarnCodes.FALLBACK_TO_NUMBER_FORMAT]: `Fall back to number format '{key}' key with '{target}' locale.`,\n  [CoreWarnCodes.CANNOT_FORMAT_DATE]: `Cannot format a date value due to not supported Intl.DateTimeFormat.`,\n  [CoreWarnCodes.FALLBACK_TO_DATE_FORMAT]: `Fall back to datetime format '{key}' key with '{target}' locale.`,\n  [CoreWarnCodes.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER]: `This project is using Custom Message Compiler, which is an experimental feature. It may receive breaking changes or be removed in the future.`\n};\nfunction getWarnMessage(code, ...args) {\n  return format$1(warnMessages[code], ...args);\n}\nconst code = CompileErrorCodes.__EXTEND_POINT__;\nconst inc = incrementer(code);\nconst CoreErrorCodes = {\n  INVALID_ARGUMENT: code,\n  // 17\n  INVALID_DATE_ARGUMENT: inc(),\n  // 18\n  INVALID_ISO_DATE_ARGUMENT: inc(),\n  // 19\n  NOT_SUPPORT_NON_STRING_MESSAGE: inc(),\n  // 20\n  NOT_SUPPORT_LOCALE_PROMISE_VALUE: inc(),\n  // 21\n  NOT_SUPPORT_LOCALE_ASYNC_FUNCTION: inc(),\n  // 22\n  NOT_SUPPORT_LOCALE_TYPE: inc(),\n  // 23\n  __EXTEND_POINT__: inc() // 24\n};\nfunction createCoreError(code) {\n  return createCompileError(code, null, process.env.NODE_ENV !== 'production' ? {\n    messages: errorMessages\n  } : undefined);\n}\n/** @internal */\nconst errorMessages = {\n  [CoreErrorCodes.INVALID_ARGUMENT]: 'Invalid arguments',\n  [CoreErrorCodes.INVALID_DATE_ARGUMENT]: 'The date provided is an invalid Date object.' + 'Make sure your Date represents a valid date.',\n  [CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT]: 'The argument provided is not a valid ISO date string',\n  [CoreErrorCodes.NOT_SUPPORT_NON_STRING_MESSAGE]: 'Not support non-string message',\n  [CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE]: 'cannot support promise value',\n  [CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION]: 'cannot support async function',\n  [CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE]: 'cannot support locale type'\n};\n\n/** @internal */\nfunction getLocale(context, options) {\n  return options.locale != null ? resolveLocale(options.locale) : resolveLocale(context.locale);\n}\nlet _resolveLocale;\n/** @internal */\nfunction resolveLocale(locale) {\n  if (isString(locale)) {\n    return locale;\n  } else {\n    if (isFunction(locale)) {\n      if (locale.resolvedOnce && _resolveLocale != null) {\n        return _resolveLocale;\n      } else if (locale.constructor.name === 'Function') {\n        const resolve = locale();\n        if (isPromise(resolve)) {\n          throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE);\n        }\n        return _resolveLocale = resolve;\n      } else {\n        throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION);\n      }\n    } else {\n      throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE);\n    }\n  }\n}\n/**\n * Fallback with simple implemenation\n *\n * @remarks\n * A fallback locale function implemented with a simple fallback algorithm.\n *\n * Basically, it returns the value as specified in the `fallbackLocale` props, and is processed with the fallback inside intlify.\n *\n * @param ctx - A {@link CoreContext | context}\n * @param fallback - A {@link FallbackLocale | fallback locale}\n * @param start - A starting {@link Locale | locale}\n *\n * @returns Fallback locales\n *\n * @VueI18nGeneral\n */\nfunction fallbackWithSimple(ctx, fallback, start // eslint-disable-line @typescript-eslint/no-unused-vars\n) {\n  // prettier-ignore\n  return [...new Set([start, ...(isArray(fallback) ? fallback : isObject(fallback) ? Object.keys(fallback) : isString(fallback) ? [fallback] : [start])])];\n}\n/**\n * Fallback with locale chain\n *\n * @remarks\n * A fallback locale function implemented with a fallback chain algorithm. It's used in VueI18n as default.\n *\n * @param ctx - A {@link CoreContext | context}\n * @param fallback - A {@link FallbackLocale | fallback locale}\n * @param start - A starting {@link Locale | locale}\n *\n * @returns Fallback locales\n *\n * @VueI18nSee [Fallbacking](../guide/essentials/fallback)\n *\n * @VueI18nGeneral\n */\nfunction fallbackWithLocaleChain(ctx, fallback, start) {\n  const startLocale = isString(start) ? start : DEFAULT_LOCALE;\n  const context = ctx;\n  if (!context.__localeChainCache) {\n    context.__localeChainCache = new Map();\n  }\n  let chain = context.__localeChainCache.get(startLocale);\n  if (!chain) {\n    chain = [];\n    // first block defined by start\n    let block = [start];\n    // while any intervening block found\n    while (isArray(block)) {\n      block = appendBlockToChain(chain, block, fallback);\n    }\n    // prettier-ignore\n    // last block defined by default\n    const defaults = isArray(fallback) || !isPlainObject(fallback) ? fallback : fallback['default'] ? fallback['default'] : null;\n    // convert defaults to array\n    block = isString(defaults) ? [defaults] : defaults;\n    if (isArray(block)) {\n      appendBlockToChain(chain, block, false);\n    }\n    context.__localeChainCache.set(startLocale, chain);\n  }\n  return chain;\n}\nfunction appendBlockToChain(chain, block, blocks) {\n  let follow = true;\n  for (let i = 0; i < block.length && isBoolean(follow); i++) {\n    const locale = block[i];\n    if (isString(locale)) {\n      follow = appendLocaleToChain(chain, block[i], blocks);\n    }\n  }\n  return follow;\n}\nfunction appendLocaleToChain(chain, locale, blocks) {\n  let follow;\n  const tokens = locale.split('-');\n  do {\n    const target = tokens.join('-');\n    follow = appendItemToChain(chain, target, blocks);\n    tokens.splice(-1, 1);\n  } while (tokens.length && follow === true);\n  return follow;\n}\nfunction appendItemToChain(chain, target, blocks) {\n  let follow = false;\n  if (!chain.includes(target)) {\n    follow = true;\n    if (target) {\n      follow = target[target.length - 1] !== '!';\n      const locale = target.replace(/!/g, '');\n      chain.push(locale);\n      if ((isArray(blocks) || isPlainObject(blocks)) && blocks[locale] // eslint-disable-line @typescript-eslint/no-explicit-any\n      ) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        follow = blocks[locale];\n      }\n    }\n  }\n  return follow;\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Intlify core-base version\n * @internal\n */\nconst VERSION = '9.14.2';\nconst NOT_REOSLVED = -1;\nconst DEFAULT_LOCALE = 'en-US';\nconst MISSING_RESOLVE_VALUE = '';\nconst capitalize = str => `${str.charAt(0).toLocaleUpperCase()}${str.substr(1)}`;\nfunction getDefaultLinkedModifiers() {\n  return {\n    upper: (val, type) => {\n      // prettier-ignore\n      return type === 'text' && isString(val) ? val.toUpperCase() : type === 'vnode' && isObject(val) && '__v_isVNode' in val ? val.children.toUpperCase() : val;\n    },\n    lower: (val, type) => {\n      // prettier-ignore\n      return type === 'text' && isString(val) ? val.toLowerCase() : type === 'vnode' && isObject(val) && '__v_isVNode' in val ? val.children.toLowerCase() : val;\n    },\n    capitalize: (val, type) => {\n      // prettier-ignore\n      return type === 'text' && isString(val) ? capitalize(val) : type === 'vnode' && isObject(val) && '__v_isVNode' in val ? capitalize(val.children) : val;\n    }\n  };\n}\nlet _compiler;\nfunction registerMessageCompiler(compiler) {\n  _compiler = compiler;\n}\nlet _resolver;\n/**\n * Register the message resolver\n *\n * @param resolver - A {@link MessageResolver} function\n *\n * @VueI18nGeneral\n */\nfunction registerMessageResolver(resolver) {\n  _resolver = resolver;\n}\nlet _fallbacker;\n/**\n * Register the locale fallbacker\n *\n * @param fallbacker - A {@link LocaleFallbacker} function\n *\n * @VueI18nGeneral\n */\nfunction registerLocaleFallbacker(fallbacker) {\n  _fallbacker = fallbacker;\n}\n// Additional Meta for Intlify DevTools\nlet _additionalMeta = null;\n/* #__NO_SIDE_EFFECTS__ */\nconst setAdditionalMeta = meta => {\n  _additionalMeta = meta;\n};\n/* #__NO_SIDE_EFFECTS__ */\nconst getAdditionalMeta = () => _additionalMeta;\nlet _fallbackContext = null;\nconst setFallbackContext = context => {\n  _fallbackContext = context;\n};\nconst getFallbackContext = () => _fallbackContext;\n// ID for CoreContext\nlet _cid = 0;\nfunction createCoreContext(options = {}) {\n  // setup options\n  const onWarn = isFunction(options.onWarn) ? options.onWarn : warn;\n  const version = isString(options.version) ? options.version : VERSION;\n  const locale = isString(options.locale) || isFunction(options.locale) ? options.locale : DEFAULT_LOCALE;\n  const _locale = isFunction(locale) ? DEFAULT_LOCALE : locale;\n  const fallbackLocale = isArray(options.fallbackLocale) || isPlainObject(options.fallbackLocale) || isString(options.fallbackLocale) || options.fallbackLocale === false ? options.fallbackLocale : _locale;\n  const messages = isPlainObject(options.messages) ? options.messages : createResources(_locale);\n  const datetimeFormats = isPlainObject(options.datetimeFormats) ? options.datetimeFormats : createResources(_locale);\n  const numberFormats = isPlainObject(options.numberFormats) ? options.numberFormats : createResources(_locale);\n  const modifiers = assign(create(), options.modifiers, getDefaultLinkedModifiers());\n  const pluralRules = options.pluralRules || create();\n  const missing = isFunction(options.missing) ? options.missing : null;\n  const missingWarn = isBoolean(options.missingWarn) || isRegExp(options.missingWarn) ? options.missingWarn : true;\n  const fallbackWarn = isBoolean(options.fallbackWarn) || isRegExp(options.fallbackWarn) ? options.fallbackWarn : true;\n  const fallbackFormat = !!options.fallbackFormat;\n  const unresolving = !!options.unresolving;\n  const postTranslation = isFunction(options.postTranslation) ? options.postTranslation : null;\n  const processor = isPlainObject(options.processor) ? options.processor : null;\n  const warnHtmlMessage = isBoolean(options.warnHtmlMessage) ? options.warnHtmlMessage : true;\n  const escapeParameter = !!options.escapeParameter;\n  const messageCompiler = isFunction(options.messageCompiler) ? options.messageCompiler : _compiler;\n  if (process.env.NODE_ENV !== 'production' && !false && !false && isFunction(options.messageCompiler)) {\n    warnOnce(getWarnMessage(CoreWarnCodes.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER));\n  }\n  const messageResolver = isFunction(options.messageResolver) ? options.messageResolver : _resolver || resolveWithKeyValue;\n  const localeFallbacker = isFunction(options.localeFallbacker) ? options.localeFallbacker : _fallbacker || fallbackWithSimple;\n  const fallbackContext = isObject(options.fallbackContext) ? options.fallbackContext : undefined;\n  // setup internal options\n  const internalOptions = options;\n  const __datetimeFormatters = isObject(internalOptions.__datetimeFormatters) ? internalOptions.__datetimeFormatters : new Map();\n  const __numberFormatters = isObject(internalOptions.__numberFormatters) ? internalOptions.__numberFormatters : new Map();\n  const __meta = isObject(internalOptions.__meta) ? internalOptions.__meta : {};\n  _cid++;\n  const context = {\n    version,\n    cid: _cid,\n    locale,\n    fallbackLocale,\n    messages,\n    modifiers,\n    pluralRules,\n    missing,\n    missingWarn,\n    fallbackWarn,\n    fallbackFormat,\n    unresolving,\n    postTranslation,\n    processor,\n    warnHtmlMessage,\n    escapeParameter,\n    messageCompiler,\n    messageResolver,\n    localeFallbacker,\n    fallbackContext,\n    onWarn,\n    __meta\n  };\n  {\n    context.datetimeFormats = datetimeFormats;\n    context.numberFormats = numberFormats;\n    context.__datetimeFormatters = __datetimeFormatters;\n    context.__numberFormatters = __numberFormatters;\n  }\n  // for vue-devtools timeline event\n  if (process.env.NODE_ENV !== 'production') {\n    context.__v_emitter = internalOptions.__v_emitter != null ? internalOptions.__v_emitter : undefined;\n  }\n  // NOTE: experimental !!\n  if (process.env.NODE_ENV !== 'production' || __INTLIFY_PROD_DEVTOOLS__) {\n    initI18nDevTools(context, version, __meta);\n  }\n  return context;\n}\nconst createResources = locale => ({\n  [locale]: create()\n});\n/** @internal */\nfunction isTranslateFallbackWarn(fallback, key) {\n  return fallback instanceof RegExp ? fallback.test(key) : fallback;\n}\n/** @internal */\nfunction isTranslateMissingWarn(missing, key) {\n  return missing instanceof RegExp ? missing.test(key) : missing;\n}\n/** @internal */\nfunction handleMissing(context, key, locale, missingWarn, type) {\n  const {\n    missing,\n    onWarn\n  } = context;\n  // for vue-devtools timeline event\n  if (process.env.NODE_ENV !== 'production') {\n    const emitter = context.__v_emitter;\n    if (emitter) {\n      emitter.emit(\"missing\" /* VueDevToolsTimelineEvents.MISSING */, {\n        locale,\n        key,\n        type,\n        groupId: `${type}:${key}`\n      });\n    }\n  }\n  if (missing !== null) {\n    const ret = missing(context, locale, key, type);\n    return isString(ret) ? ret : key;\n  } else {\n    if (process.env.NODE_ENV !== 'production' && isTranslateMissingWarn(missingWarn, key)) {\n      onWarn(getWarnMessage(CoreWarnCodes.NOT_FOUND_KEY, {\n        key,\n        locale\n      }));\n    }\n    return key;\n  }\n}\n/** @internal */\nfunction updateFallbackLocale(ctx, locale, fallback) {\n  const context = ctx;\n  context.__localeChainCache = new Map();\n  ctx.localeFallbacker(ctx, fallback, locale);\n}\n/** @internal */\nfunction isAlmostSameLocale(locale, compareLocale) {\n  if (locale === compareLocale) return false;\n  return locale.split('-')[0] === compareLocale.split('-')[0];\n}\n/** @internal */\nfunction isImplicitFallback(targetLocale, locales) {\n  const index = locales.indexOf(targetLocale);\n  if (index === -1) {\n    return false;\n  }\n  for (let i = index + 1; i < locales.length; i++) {\n    if (isAlmostSameLocale(targetLocale, locales[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\nfunction format(ast) {\n  const msg = ctx => formatParts(ctx, ast);\n  return msg;\n}\nfunction formatParts(ctx, ast) {\n  const body = resolveBody(ast);\n  if (body == null) {\n    throw createUnhandleNodeError(0 /* NodeTypes.Resource */);\n  }\n  const type = resolveType(body);\n  if (type === 1 /* NodeTypes.Plural */) {\n    const plural = body;\n    const cases = resolveCases(plural);\n    return ctx.plural(cases.reduce((messages, c) => [...messages, formatMessageParts(ctx, c)], []));\n  } else {\n    return formatMessageParts(ctx, body);\n  }\n}\nconst PROPS_BODY = ['b', 'body'];\nfunction resolveBody(node) {\n  return resolveProps(node, PROPS_BODY);\n}\nconst PROPS_CASES = ['c', 'cases'];\nfunction resolveCases(node) {\n  return resolveProps(node, PROPS_CASES, []);\n}\nfunction formatMessageParts(ctx, node) {\n  const static_ = resolveStatic(node);\n  if (static_ != null) {\n    return ctx.type === 'text' ? static_ : ctx.normalize([static_]);\n  } else {\n    const messages = resolveItems(node).reduce((acm, c) => [...acm, formatMessagePart(ctx, c)], []);\n    return ctx.normalize(messages);\n  }\n}\nconst PROPS_STATIC = ['s', 'static'];\nfunction resolveStatic(node) {\n  return resolveProps(node, PROPS_STATIC);\n}\nconst PROPS_ITEMS = ['i', 'items'];\nfunction resolveItems(node) {\n  return resolveProps(node, PROPS_ITEMS, []);\n}\nfunction formatMessagePart(ctx, node) {\n  const type = resolveType(node);\n  switch (type) {\n    case 3 /* NodeTypes.Text */:\n      {\n        return resolveValue(node, type);\n      }\n    case 9 /* NodeTypes.Literal */:\n      {\n        return resolveValue(node, type);\n      }\n    case 4 /* NodeTypes.Named */:\n      {\n        const named = node;\n        if (hasOwn(named, 'k') && named.k) {\n          return ctx.interpolate(ctx.named(named.k));\n        }\n        if (hasOwn(named, 'key') && named.key) {\n          return ctx.interpolate(ctx.named(named.key));\n        }\n        throw createUnhandleNodeError(type);\n      }\n    case 5 /* NodeTypes.List */:\n      {\n        const list = node;\n        if (hasOwn(list, 'i') && isNumber(list.i)) {\n          return ctx.interpolate(ctx.list(list.i));\n        }\n        if (hasOwn(list, 'index') && isNumber(list.index)) {\n          return ctx.interpolate(ctx.list(list.index));\n        }\n        throw createUnhandleNodeError(type);\n      }\n    case 6 /* NodeTypes.Linked */:\n      {\n        const linked = node;\n        const modifier = resolveLinkedModifier(linked);\n        const key = resolveLinkedKey(linked);\n        return ctx.linked(formatMessagePart(ctx, key), modifier ? formatMessagePart(ctx, modifier) : undefined, ctx.type);\n      }\n    case 7 /* NodeTypes.LinkedKey */:\n      {\n        return resolveValue(node, type);\n      }\n    case 8 /* NodeTypes.LinkedModifier */:\n      {\n        return resolveValue(node, type);\n      }\n    default:\n      throw new Error(`unhandled node on format message part: ${type}`);\n  }\n}\nconst PROPS_TYPE = ['t', 'type'];\nfunction resolveType(node) {\n  return resolveProps(node, PROPS_TYPE);\n}\nconst PROPS_VALUE = ['v', 'value'];\nfunction resolveValue(node, type) {\n  const resolved = resolveProps(node, PROPS_VALUE);\n  if (resolved) {\n    return resolved;\n  } else {\n    throw createUnhandleNodeError(type);\n  }\n}\nconst PROPS_MODIFIER = ['m', 'modifier'];\nfunction resolveLinkedModifier(node) {\n  return resolveProps(node, PROPS_MODIFIER);\n}\nconst PROPS_KEY = ['k', 'key'];\nfunction resolveLinkedKey(node) {\n  const resolved = resolveProps(node, PROPS_KEY);\n  if (resolved) {\n    return resolved;\n  } else {\n    throw createUnhandleNodeError(6 /* NodeTypes.Linked */);\n  }\n}\nfunction resolveProps(node, props, defaultValue) {\n  for (let i = 0; i < props.length; i++) {\n    const prop = props[i];\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    if (hasOwn(node, prop) && node[prop] != null) {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return node[prop];\n    }\n  }\n  return defaultValue;\n}\nfunction createUnhandleNodeError(type) {\n  return new Error(`unhandled node type: ${type}`);\n}\nconst WARN_MESSAGE = `Detected HTML in '{source}' message. Recommend not using HTML messages to avoid XSS.`;\nfunction checkHtmlMessage(source, warnHtmlMessage) {\n  if (warnHtmlMessage && detectHtmlTag(source)) {\n    warn(format$1(WARN_MESSAGE, {\n      source\n    }));\n  }\n}\nconst defaultOnCacheKey = message => message;\nlet compileCache = create();\nfunction onCompileWarn(_warn) {\n  if (_warn.code === CompileWarnCodes.USE_MODULO_SYNTAX) {\n    warn(`The use of named interpolation with modulo syntax is deprecated. ` + `It will be removed in v10.\\n` + `reference: https://vue-i18n.intlify.dev/guide/essentials/syntax#rails-i18n-format \\n` + `(message compiler warning message: ${_warn.message})`);\n  }\n}\nfunction clearCompileCache() {\n  compileCache = create();\n}\nfunction isMessageAST(val) {\n  return isObject(val) && resolveType(val) === 0 && (hasOwn(val, 'b') || hasOwn(val, 'body'));\n}\nfunction baseCompile(message, options = {}) {\n  // error detecting on compile\n  let detectError = false;\n  const onError = options.onError || defaultOnError;\n  options.onError = err => {\n    detectError = true;\n    onError(err);\n  };\n  // compile with mesasge-compiler\n  return {\n    ...baseCompile$1(message, options),\n    detectError\n  };\n}\n/* #__NO_SIDE_EFFECTS__ */\nconst compileToFunction = (message, context) => {\n  if (!isString(message)) {\n    throw createCoreError(CoreErrorCodes.NOT_SUPPORT_NON_STRING_MESSAGE);\n  }\n  // set onWarn\n  if (process.env.NODE_ENV !== 'production') {\n    context.onWarn = onCompileWarn;\n  }\n  {\n    // check HTML message\n    const warnHtmlMessage = isBoolean(context.warnHtmlMessage) ? context.warnHtmlMessage : true;\n    process.env.NODE_ENV !== 'production' && checkHtmlMessage(message, warnHtmlMessage);\n    // check caches\n    const onCacheKey = context.onCacheKey || defaultOnCacheKey;\n    const cacheKey = onCacheKey(message);\n    const cached = compileCache[cacheKey];\n    if (cached) {\n      return cached;\n    }\n    // compile\n    const {\n      code,\n      detectError\n    } = baseCompile(message, context);\n    // evaluate function\n    const msg = new Function(`return ${code}`)();\n    // if occurred compile error, don't cache\n    return !detectError ? compileCache[cacheKey] = msg : msg;\n  }\n};\nfunction compile(message, context) {\n  // set onWarn\n  if (process.env.NODE_ENV !== 'production') {\n    context.onWarn = onCompileWarn;\n  }\n  if (__INTLIFY_JIT_COMPILATION__ && !__INTLIFY_DROP_MESSAGE_COMPILER__ && isString(message)) {\n    // check HTML message\n    const warnHtmlMessage = isBoolean(context.warnHtmlMessage) ? context.warnHtmlMessage : true;\n    process.env.NODE_ENV !== 'production' && checkHtmlMessage(message, warnHtmlMessage);\n    // check caches\n    const onCacheKey = context.onCacheKey || defaultOnCacheKey;\n    const cacheKey = onCacheKey(message);\n    const cached = compileCache[cacheKey];\n    if (cached) {\n      return cached;\n    }\n    // compile with JIT mode\n    const {\n      ast,\n      detectError\n    } = baseCompile(message, {\n      ...context,\n      location: process.env.NODE_ENV !== 'production',\n      jit: true\n    });\n    // compose message function from AST\n    const msg = format(ast);\n    // if occurred compile error, don't cache\n    return !detectError ? compileCache[cacheKey] = msg : msg;\n  } else {\n    if (process.env.NODE_ENV !== 'production' && !isMessageAST(message)) {\n      warn(`the message that is resolve with key '${context.key}' is not supported for jit compilation`);\n      return () => message;\n    }\n    // AST case (passed from bundler)\n    const cacheKey = message.cacheKey;\n    if (cacheKey) {\n      const cached = compileCache[cacheKey];\n      if (cached) {\n        return cached;\n      }\n      // compose message function from message (AST)\n      return compileCache[cacheKey] = format(message);\n    } else {\n      return format(message);\n    }\n  }\n}\nconst NOOP_MESSAGE_FUNCTION = () => '';\nconst isMessageFunction = val => isFunction(val);\n// implementation of `translate` function\nfunction translate(context, ...args) {\n  const {\n    fallbackFormat,\n    postTranslation,\n    unresolving,\n    messageCompiler,\n    fallbackLocale,\n    messages\n  } = context;\n  const [key, options] = parseTranslateArgs(...args);\n  const missingWarn = isBoolean(options.missingWarn) ? options.missingWarn : context.missingWarn;\n  const fallbackWarn = isBoolean(options.fallbackWarn) ? options.fallbackWarn : context.fallbackWarn;\n  const escapeParameter = isBoolean(options.escapeParameter) ? options.escapeParameter : context.escapeParameter;\n  const resolvedMessage = !!options.resolvedMessage;\n  // prettier-ignore\n  const defaultMsgOrKey = isString(options.default) || isBoolean(options.default) // default by function option\n  ? !isBoolean(options.default) ? options.default : !messageCompiler ? () => key : key : fallbackFormat // default by `fallbackFormat` option\n  ? !messageCompiler ? () => key : key : '';\n  const enableDefaultMsg = fallbackFormat || defaultMsgOrKey !== '';\n  const locale = getLocale(context, options);\n  // escape params\n  escapeParameter && escapeParams(options);\n  // resolve message format\n  // eslint-disable-next-line prefer-const\n  let [formatScope, targetLocale, message] = !resolvedMessage ? resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn) : [key, locale, messages[locale] || create()];\n  // NOTE:\n  //  Fix to work around `ssrTransfrom` bug in Vite.\n  //  https://github.com/vitejs/vite/issues/4306\n  //  To get around this, use temporary variables.\n  //  https://github.com/nuxt/framework/issues/1461#issuecomment-954606243\n  let format = formatScope;\n  // if you use default message, set it as message format!\n  let cacheBaseKey = key;\n  if (!resolvedMessage && !(isString(format) || isMessageAST(format) || isMessageFunction(format))) {\n    if (enableDefaultMsg) {\n      format = defaultMsgOrKey;\n      cacheBaseKey = format;\n    }\n  }\n  // checking message format and target locale\n  if (!resolvedMessage && (!(isString(format) || isMessageAST(format) || isMessageFunction(format)) || !isString(targetLocale))) {\n    return unresolving ? NOT_REOSLVED : key;\n  }\n  // TODO: refactor\n  if (process.env.NODE_ENV !== 'production' && isString(format) && context.messageCompiler == null) {\n    warn(`The message format compilation is not supported in this build. ` + `Because message compiler isn't included. ` + `You need to pre-compilation all message format. ` + `So translate function return '${key}'.`);\n    return key;\n  }\n  // setup compile error detecting\n  let occurred = false;\n  const onError = () => {\n    occurred = true;\n  };\n  // compile message format\n  const msg = !isMessageFunction(format) ? compileMessageFormat(context, key, targetLocale, format, cacheBaseKey, onError) : format;\n  // if occurred compile error, return the message format\n  if (occurred) {\n    return format;\n  }\n  // evaluate message with context\n  const ctxOptions = getMessageContextOptions(context, targetLocale, message, options);\n  const msgContext = createMessageContext(ctxOptions);\n  const messaged = evaluateMessage(context, msg, msgContext);\n  // if use post translation option, proceed it with handler\n  const ret = postTranslation ? postTranslation(messaged, key) : messaged;\n  // NOTE: experimental !!\n  if (process.env.NODE_ENV !== 'production' || __INTLIFY_PROD_DEVTOOLS__) {\n    // prettier-ignore\n    const payloads = {\n      timestamp: Date.now(),\n      key: isString(key) ? key : isMessageFunction(format) ? format.key : '',\n      locale: targetLocale || (isMessageFunction(format) ? format.locale : ''),\n      format: isString(format) ? format : isMessageFunction(format) ? format.source : '',\n      message: ret\n    };\n    payloads.meta = assign({}, context.__meta, getAdditionalMeta() || {});\n    translateDevTools(payloads);\n  }\n  return ret;\n}\nfunction escapeParams(options) {\n  if (isArray(options.list)) {\n    options.list = options.list.map(item => isString(item) ? escapeHtml(item) : item);\n  } else if (isObject(options.named)) {\n    Object.keys(options.named).forEach(key => {\n      if (isString(options.named[key])) {\n        options.named[key] = escapeHtml(options.named[key]);\n      }\n    });\n  }\n}\nfunction resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn) {\n  const {\n    messages,\n    onWarn,\n    messageResolver: resolveValue,\n    localeFallbacker\n  } = context;\n  const locales = localeFallbacker(context, fallbackLocale, locale); // eslint-disable-line @typescript-eslint/no-explicit-any\n  let message = create();\n  let targetLocale;\n  let format = null;\n  let from = locale;\n  let to = null;\n  const type = 'translate';\n  for (let i = 0; i < locales.length; i++) {\n    targetLocale = to = locales[i];\n    if (process.env.NODE_ENV !== 'production' && locale !== targetLocale && !isAlmostSameLocale(locale, targetLocale) && isTranslateFallbackWarn(fallbackWarn, key)) {\n      onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_TRANSLATE, {\n        key,\n        target: targetLocale\n      }));\n    }\n    // for vue-devtools timeline event\n    if (process.env.NODE_ENV !== 'production' && locale !== targetLocale) {\n      const emitter = context.__v_emitter;\n      if (emitter) {\n        emitter.emit(\"fallback\" /* VueDevToolsTimelineEvents.FALBACK */, {\n          type,\n          key,\n          from,\n          to,\n          groupId: `${type}:${key}`\n        });\n      }\n    }\n    message = messages[targetLocale] || create();\n    // for vue-devtools timeline event\n    let start = null;\n    let startTag;\n    let endTag;\n    if (process.env.NODE_ENV !== 'production' && inBrowser) {\n      start = window.performance.now();\n      startTag = 'intlify-message-resolve-start';\n      endTag = 'intlify-message-resolve-end';\n      mark && mark(startTag);\n    }\n    if ((format = resolveValue(message, key)) === null) {\n      // if null, resolve with object key path\n      format = message[key]; // eslint-disable-line @typescript-eslint/no-explicit-any\n    }\n    // for vue-devtools timeline event\n    if (process.env.NODE_ENV !== 'production' && inBrowser) {\n      const end = window.performance.now();\n      const emitter = context.__v_emitter;\n      if (emitter && start && format) {\n        emitter.emit(\"message-resolve\" /* VueDevToolsTimelineEvents.MESSAGE_RESOLVE */, {\n          type: \"message-resolve\" /* VueDevToolsTimelineEvents.MESSAGE_RESOLVE */,\n          key,\n          message: format,\n          time: end - start,\n          groupId: `${type}:${key}`\n        });\n      }\n      if (startTag && endTag && mark && measure) {\n        mark(endTag);\n        measure('intlify message resolve', startTag, endTag);\n      }\n    }\n    if (isString(format) || isMessageAST(format) || isMessageFunction(format)) {\n      break;\n    }\n    if (!isImplicitFallback(targetLocale, locales)) {\n      const missingRet = handleMissing(context,\n      // eslint-disable-line @typescript-eslint/no-explicit-any\n      key, targetLocale, missingWarn, type);\n      if (missingRet !== key) {\n        format = missingRet;\n      }\n    }\n    from = to;\n  }\n  return [format, targetLocale, message];\n}\nfunction compileMessageFormat(context, key, targetLocale, format, cacheBaseKey, onError) {\n  const {\n    messageCompiler,\n    warnHtmlMessage\n  } = context;\n  if (isMessageFunction(format)) {\n    const msg = format;\n    msg.locale = msg.locale || targetLocale;\n    msg.key = msg.key || key;\n    return msg;\n  }\n  if (messageCompiler == null) {\n    const msg = () => format;\n    msg.locale = targetLocale;\n    msg.key = key;\n    return msg;\n  }\n  // for vue-devtools timeline event\n  let start = null;\n  let startTag;\n  let endTag;\n  if (process.env.NODE_ENV !== 'production' && inBrowser) {\n    start = window.performance.now();\n    startTag = 'intlify-message-compilation-start';\n    endTag = 'intlify-message-compilation-end';\n    mark && mark(startTag);\n  }\n  const msg = messageCompiler(format, getCompileContext(context, targetLocale, cacheBaseKey, format, warnHtmlMessage, onError));\n  // for vue-devtools timeline event\n  if (process.env.NODE_ENV !== 'production' && inBrowser) {\n    const end = window.performance.now();\n    const emitter = context.__v_emitter;\n    if (emitter && start) {\n      emitter.emit(\"message-compilation\" /* VueDevToolsTimelineEvents.MESSAGE_COMPILATION */, {\n        type: \"message-compilation\" /* VueDevToolsTimelineEvents.MESSAGE_COMPILATION */,\n        message: format,\n        time: end - start,\n        groupId: `${'translate'}:${key}`\n      });\n    }\n    if (startTag && endTag && mark && measure) {\n      mark(endTag);\n      measure('intlify message compilation', startTag, endTag);\n    }\n  }\n  msg.locale = targetLocale;\n  msg.key = key;\n  msg.source = format;\n  return msg;\n}\nfunction evaluateMessage(context, msg, msgCtx) {\n  // for vue-devtools timeline event\n  let start = null;\n  let startTag;\n  let endTag;\n  if (process.env.NODE_ENV !== 'production' && inBrowser) {\n    start = window.performance.now();\n    startTag = 'intlify-message-evaluation-start';\n    endTag = 'intlify-message-evaluation-end';\n    mark && mark(startTag);\n  }\n  const messaged = msg(msgCtx);\n  // for vue-devtools timeline event\n  if (process.env.NODE_ENV !== 'production' && inBrowser) {\n    const end = window.performance.now();\n    const emitter = context.__v_emitter;\n    if (emitter && start) {\n      emitter.emit(\"message-evaluation\" /* VueDevToolsTimelineEvents.MESSAGE_EVALUATION */, {\n        type: \"message-evaluation\" /* VueDevToolsTimelineEvents.MESSAGE_EVALUATION */,\n        value: messaged,\n        time: end - start,\n        groupId: `${'translate'}:${msg.key}`\n      });\n    }\n    if (startTag && endTag && mark && measure) {\n      mark(endTag);\n      measure('intlify message evaluation', startTag, endTag);\n    }\n  }\n  return messaged;\n}\n/** @internal */\nfunction parseTranslateArgs(...args) {\n  const [arg1, arg2, arg3] = args;\n  const options = create();\n  if (!isString(arg1) && !isNumber(arg1) && !isMessageFunction(arg1) && !isMessageAST(arg1)) {\n    throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);\n  }\n  // prettier-ignore\n  const key = isNumber(arg1) ? String(arg1) : isMessageFunction(arg1) ? arg1 : arg1;\n  if (isNumber(arg2)) {\n    options.plural = arg2;\n  } else if (isString(arg2)) {\n    options.default = arg2;\n  } else if (isPlainObject(arg2) && !isEmptyObject(arg2)) {\n    options.named = arg2;\n  } else if (isArray(arg2)) {\n    options.list = arg2;\n  }\n  if (isNumber(arg3)) {\n    options.plural = arg3;\n  } else if (isString(arg3)) {\n    options.default = arg3;\n  } else if (isPlainObject(arg3)) {\n    assign(options, arg3);\n  }\n  return [key, options];\n}\nfunction getCompileContext(context, locale, key, source, warnHtmlMessage, onError) {\n  return {\n    locale,\n    key,\n    warnHtmlMessage,\n    onError: err => {\n      onError && onError(err);\n      if (process.env.NODE_ENV !== 'production') {\n        const _source = getSourceForCodeFrame(source);\n        const message = `Message compilation error: ${err.message}`;\n        const codeFrame = err.location && _source && generateCodeFrame(_source, err.location.start.offset, err.location.end.offset);\n        const emitter = context.__v_emitter;\n        if (emitter && _source) {\n          emitter.emit(\"compile-error\" /* VueDevToolsTimelineEvents.COMPILE_ERROR */, {\n            message: _source,\n            error: err.message,\n            start: err.location && err.location.start.offset,\n            end: err.location && err.location.end.offset,\n            groupId: `${'translate'}:${key}`\n          });\n        }\n        console.error(codeFrame ? `${message}\\n${codeFrame}` : message);\n      } else {\n        throw err;\n      }\n    },\n    onCacheKey: source => generateFormatCacheKey(locale, key, source)\n  };\n}\nfunction getSourceForCodeFrame(source) {\n  if (isString(source)) {\n    return source;\n  } else {\n    if (source.loc && source.loc.source) {\n      return source.loc.source;\n    }\n  }\n}\nfunction getMessageContextOptions(context, locale, message, options) {\n  const {\n    modifiers,\n    pluralRules,\n    messageResolver: resolveValue,\n    fallbackLocale,\n    fallbackWarn,\n    missingWarn,\n    fallbackContext\n  } = context;\n  const resolveMessage = key => {\n    let val = resolveValue(message, key);\n    // fallback to root context\n    if (val == null && fallbackContext) {\n      const [,, message] = resolveMessageFormat(fallbackContext, key, locale, fallbackLocale, fallbackWarn, missingWarn);\n      val = resolveValue(message, key);\n    }\n    if (isString(val) || isMessageAST(val)) {\n      let occurred = false;\n      const onError = () => {\n        occurred = true;\n      };\n      const msg = compileMessageFormat(context, key, locale, val, key, onError);\n      return !occurred ? msg : NOOP_MESSAGE_FUNCTION;\n    } else if (isMessageFunction(val)) {\n      return val;\n    } else {\n      // TODO: should be implemented warning message\n      return NOOP_MESSAGE_FUNCTION;\n    }\n  };\n  const ctxOptions = {\n    locale,\n    modifiers,\n    pluralRules,\n    messages: resolveMessage\n  };\n  if (context.processor) {\n    ctxOptions.processor = context.processor;\n  }\n  if (options.list) {\n    ctxOptions.list = options.list;\n  }\n  if (options.named) {\n    ctxOptions.named = options.named;\n  }\n  if (isNumber(options.plural)) {\n    ctxOptions.pluralIndex = options.plural;\n  }\n  return ctxOptions;\n}\nconst intlDefined = typeof Intl !== 'undefined';\nconst Availabilities = {\n  dateTimeFormat: intlDefined && typeof Intl.DateTimeFormat !== 'undefined',\n  numberFormat: intlDefined && typeof Intl.NumberFormat !== 'undefined'\n};\n\n// implementation of `datetime` function\nfunction datetime(context, ...args) {\n  const {\n    datetimeFormats,\n    unresolving,\n    fallbackLocale,\n    onWarn,\n    localeFallbacker\n  } = context;\n  const {\n    __datetimeFormatters\n  } = context;\n  if (process.env.NODE_ENV !== 'production' && !Availabilities.dateTimeFormat) {\n    onWarn(getWarnMessage(CoreWarnCodes.CANNOT_FORMAT_DATE));\n    return MISSING_RESOLVE_VALUE;\n  }\n  const [key, value, options, overrides] = parseDateTimeArgs(...args);\n  const missingWarn = isBoolean(options.missingWarn) ? options.missingWarn : context.missingWarn;\n  const fallbackWarn = isBoolean(options.fallbackWarn) ? options.fallbackWarn : context.fallbackWarn;\n  const part = !!options.part;\n  const locale = getLocale(context, options);\n  const locales = localeFallbacker(context,\n  // eslint-disable-line @typescript-eslint/no-explicit-any\n  fallbackLocale, locale);\n  if (!isString(key) || key === '') {\n    return new Intl.DateTimeFormat(locale, overrides).format(value);\n  }\n  // resolve format\n  let datetimeFormat = {};\n  let targetLocale;\n  let format = null;\n  let from = locale;\n  let to = null;\n  const type = 'datetime format';\n  for (let i = 0; i < locales.length; i++) {\n    targetLocale = to = locales[i];\n    if (process.env.NODE_ENV !== 'production' && locale !== targetLocale && isTranslateFallbackWarn(fallbackWarn, key)) {\n      onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_DATE_FORMAT, {\n        key,\n        target: targetLocale\n      }));\n    }\n    // for vue-devtools timeline event\n    if (process.env.NODE_ENV !== 'production' && locale !== targetLocale) {\n      const emitter = context.__v_emitter;\n      if (emitter) {\n        emitter.emit(\"fallback\" /* VueDevToolsTimelineEvents.FALBACK */, {\n          type,\n          key,\n          from,\n          to,\n          groupId: `${type}:${key}`\n        });\n      }\n    }\n    datetimeFormat = datetimeFormats[targetLocale] || {};\n    format = datetimeFormat[key];\n    if (isPlainObject(format)) break;\n    handleMissing(context, key, targetLocale, missingWarn, type); // eslint-disable-line @typescript-eslint/no-explicit-any\n    from = to;\n  }\n  // checking format and target locale\n  if (!isPlainObject(format) || !isString(targetLocale)) {\n    return unresolving ? NOT_REOSLVED : key;\n  }\n  let id = `${targetLocale}__${key}`;\n  if (!isEmptyObject(overrides)) {\n    id = `${id}__${JSON.stringify(overrides)}`;\n  }\n  let formatter = __datetimeFormatters.get(id);\n  if (!formatter) {\n    formatter = new Intl.DateTimeFormat(targetLocale, assign({}, format, overrides));\n    __datetimeFormatters.set(id, formatter);\n  }\n  return !part ? formatter.format(value) : formatter.formatToParts(value);\n}\n/** @internal */\nconst DATETIME_FORMAT_OPTIONS_KEYS = ['localeMatcher', 'weekday', 'era', 'year', 'month', 'day', 'hour', 'minute', 'second', 'timeZoneName', 'formatMatcher', 'hour12', 'timeZone', 'dateStyle', 'timeStyle', 'calendar', 'dayPeriod', 'numberingSystem', 'hourCycle', 'fractionalSecondDigits'];\n/** @internal */\nfunction parseDateTimeArgs(...args) {\n  const [arg1, arg2, arg3, arg4] = args;\n  const options = create();\n  let overrides = create();\n  let value;\n  if (isString(arg1)) {\n    // Only allow ISO strings - other date formats are often supported,\n    // but may cause different results in different browsers.\n    const matches = arg1.match(/(\\d{4}-\\d{2}-\\d{2})(T|\\s)?(.*)/);\n    if (!matches) {\n      throw createCoreError(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);\n    }\n    // Some browsers can not parse the iso datetime separated by space,\n    // this is a compromise solution by replace the 'T'/' ' with 'T'\n    const dateTime = matches[3] ? matches[3].trim().startsWith('T') ? `${matches[1].trim()}${matches[3].trim()}` : `${matches[1].trim()}T${matches[3].trim()}` : matches[1].trim();\n    value = new Date(dateTime);\n    try {\n      // This will fail if the date is not valid\n      value.toISOString();\n    } catch (e) {\n      throw createCoreError(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);\n    }\n  } else if (isDate(arg1)) {\n    if (isNaN(arg1.getTime())) {\n      throw createCoreError(CoreErrorCodes.INVALID_DATE_ARGUMENT);\n    }\n    value = arg1;\n  } else if (isNumber(arg1)) {\n    value = arg1;\n  } else {\n    throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);\n  }\n  if (isString(arg2)) {\n    options.key = arg2;\n  } else if (isPlainObject(arg2)) {\n    Object.keys(arg2).forEach(key => {\n      if (DATETIME_FORMAT_OPTIONS_KEYS.includes(key)) {\n        overrides[key] = arg2[key];\n      } else {\n        options[key] = arg2[key];\n      }\n    });\n  }\n  if (isString(arg3)) {\n    options.locale = arg3;\n  } else if (isPlainObject(arg3)) {\n    overrides = arg3;\n  }\n  if (isPlainObject(arg4)) {\n    overrides = arg4;\n  }\n  return [options.key || '', value, options, overrides];\n}\n/** @internal */\nfunction clearDateTimeFormat(ctx, locale, format) {\n  const context = ctx;\n  for (const key in format) {\n    const id = `${locale}__${key}`;\n    if (!context.__datetimeFormatters.has(id)) {\n      continue;\n    }\n    context.__datetimeFormatters.delete(id);\n  }\n}\n\n// implementation of `number` function\nfunction number(context, ...args) {\n  const {\n    numberFormats,\n    unresolving,\n    fallbackLocale,\n    onWarn,\n    localeFallbacker\n  } = context;\n  const {\n    __numberFormatters\n  } = context;\n  if (process.env.NODE_ENV !== 'production' && !Availabilities.numberFormat) {\n    onWarn(getWarnMessage(CoreWarnCodes.CANNOT_FORMAT_NUMBER));\n    return MISSING_RESOLVE_VALUE;\n  }\n  const [key, value, options, overrides] = parseNumberArgs(...args);\n  const missingWarn = isBoolean(options.missingWarn) ? options.missingWarn : context.missingWarn;\n  const fallbackWarn = isBoolean(options.fallbackWarn) ? options.fallbackWarn : context.fallbackWarn;\n  const part = !!options.part;\n  const locale = getLocale(context, options);\n  const locales = localeFallbacker(context,\n  // eslint-disable-line @typescript-eslint/no-explicit-any\n  fallbackLocale, locale);\n  if (!isString(key) || key === '') {\n    return new Intl.NumberFormat(locale, overrides).format(value);\n  }\n  // resolve format\n  let numberFormat = {};\n  let targetLocale;\n  let format = null;\n  let from = locale;\n  let to = null;\n  const type = 'number format';\n  for (let i = 0; i < locales.length; i++) {\n    targetLocale = to = locales[i];\n    if (process.env.NODE_ENV !== 'production' && locale !== targetLocale && isTranslateFallbackWarn(fallbackWarn, key)) {\n      onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_NUMBER_FORMAT, {\n        key,\n        target: targetLocale\n      }));\n    }\n    // for vue-devtools timeline event\n    if (process.env.NODE_ENV !== 'production' && locale !== targetLocale) {\n      const emitter = context.__v_emitter;\n      if (emitter) {\n        emitter.emit(\"fallback\" /* VueDevToolsTimelineEvents.FALBACK */, {\n          type,\n          key,\n          from,\n          to,\n          groupId: `${type}:${key}`\n        });\n      }\n    }\n    numberFormat = numberFormats[targetLocale] || {};\n    format = numberFormat[key];\n    if (isPlainObject(format)) break;\n    handleMissing(context, key, targetLocale, missingWarn, type); // eslint-disable-line @typescript-eslint/no-explicit-any\n    from = to;\n  }\n  // checking format and target locale\n  if (!isPlainObject(format) || !isString(targetLocale)) {\n    return unresolving ? NOT_REOSLVED : key;\n  }\n  let id = `${targetLocale}__${key}`;\n  if (!isEmptyObject(overrides)) {\n    id = `${id}__${JSON.stringify(overrides)}`;\n  }\n  let formatter = __numberFormatters.get(id);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(targetLocale, assign({}, format, overrides));\n    __numberFormatters.set(id, formatter);\n  }\n  return !part ? formatter.format(value) : formatter.formatToParts(value);\n}\n/** @internal */\nconst NUMBER_FORMAT_OPTIONS_KEYS = ['localeMatcher', 'style', 'currency', 'currencyDisplay', 'currencySign', 'useGrouping', 'minimumIntegerDigits', 'minimumFractionDigits', 'maximumFractionDigits', 'minimumSignificantDigits', 'maximumSignificantDigits', 'compactDisplay', 'notation', 'signDisplay', 'unit', 'unitDisplay', 'roundingMode', 'roundingPriority', 'roundingIncrement', 'trailingZeroDisplay'];\n/** @internal */\nfunction parseNumberArgs(...args) {\n  const [arg1, arg2, arg3, arg4] = args;\n  const options = create();\n  let overrides = create();\n  if (!isNumber(arg1)) {\n    throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);\n  }\n  const value = arg1;\n  if (isString(arg2)) {\n    options.key = arg2;\n  } else if (isPlainObject(arg2)) {\n    Object.keys(arg2).forEach(key => {\n      if (NUMBER_FORMAT_OPTIONS_KEYS.includes(key)) {\n        overrides[key] = arg2[key];\n      } else {\n        options[key] = arg2[key];\n      }\n    });\n  }\n  if (isString(arg3)) {\n    options.locale = arg3;\n  } else if (isPlainObject(arg3)) {\n    overrides = arg3;\n  }\n  if (isPlainObject(arg4)) {\n    overrides = arg4;\n  }\n  return [options.key || '', value, options, overrides];\n}\n/** @internal */\nfunction clearNumberFormat(ctx, locale, format) {\n  const context = ctx;\n  for (const key in format) {\n    const id = `${locale}__${key}`;\n    if (!context.__numberFormatters.has(id)) {\n      continue;\n    }\n    context.__numberFormatters.delete(id);\n  }\n}\n{\n  initFeatureFlags();\n}\nexport { CoreErrorCodes, CoreWarnCodes, DATETIME_FORMAT_OPTIONS_KEYS, DEFAULT_LOCALE, DEFAULT_MESSAGE_DATA_TYPE, MISSING_RESOLVE_VALUE, NOT_REOSLVED, NUMBER_FORMAT_OPTIONS_KEYS, VERSION, clearCompileCache, clearDateTimeFormat, clearNumberFormat, compile, compileToFunction, createCoreContext, createCoreError, createMessageContext, datetime, fallbackWithLocaleChain, fallbackWithSimple, getAdditionalMeta, getDevToolsHook, getFallbackContext, getLocale, getWarnMessage, handleMissing, initI18nDevTools, isAlmostSameLocale, isImplicitFallback, isMessageAST, isMessageFunction, isTranslateFallbackWarn, isTranslateMissingWarn, number, parse, parseDateTimeArgs, parseNumberArgs, parseTranslateArgs, registerLocaleFallbacker, registerMessageCompiler, registerMessageResolver, resolveLocale, resolveValue$1 as resolveValue, resolveWithKeyValue, setAdditionalMeta, setDevToolsHook, setFallbackContext, translate, translateDevTools, updateFallbackLocale };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}