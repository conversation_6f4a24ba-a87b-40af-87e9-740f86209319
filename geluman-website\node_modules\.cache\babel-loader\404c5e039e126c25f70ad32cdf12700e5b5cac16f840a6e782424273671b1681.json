{"ast": null, "code": "import { defineComponent, watch, renderSlot } from 'vue';\nimport { provideGlobalConfig } from './hooks/use-global-config.mjs';\nimport { configProviderProps } from './config-provider-props.mjs';\nconst messageConfig = {};\nconst ConfigProvider = defineComponent({\n  name: \"ElConfigProvider\",\n  props: configProviderProps,\n  setup(props, {\n    slots\n  }) {\n    watch(() => props.message, val => {\n      Object.assign(messageConfig, val != null ? val : {});\n    }, {\n      immediate: true,\n      deep: true\n    });\n    const config = provideGlobalConfig(props);\n    return () => renderSlot(slots, \"default\", {\n      config: config == null ? void 0 : config.value\n    });\n  }\n});\nexport { ConfigProvider as default, messageConfig };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}