{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { getCurrentInstance, unref, nextTick } from 'vue';\nimport { isNull } from 'lodash-unified';\nimport useWatcher from './watcher.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nfunction replaceColumn(array, column) {\n  return array.map(item => {\n    var _a;\n    if (item.id === column.id) {\n      return column;\n    } else if ((_a = item.children) == null ? void 0 : _a.length) {\n      item.children = replaceColumn(item.children, column);\n    }\n    return item;\n  });\n}\nfunction sortColumn(array) {\n  array.forEach(item => {\n    var _a, _b;\n    item.no = (_a = item.getColumnIndex) == null ? void 0 : _a.call(item);\n    if ((_b = item.children) == null ? void 0 : _b.length) {\n      sortColumn(item.children);\n    }\n  });\n  array.sort((cur, pre) => cur.no - pre.no);\n}\nfunction useStore() {\n  const instance = getCurrentInstance();\n  const watcher = useWatcher();\n  const ns = useNamespace(\"table\");\n  const mutations = {\n    setData(states, data) {\n      const dataInstanceChanged = unref(states._data) !== data;\n      states.data.value = data;\n      states._data.value = data;\n      instance.store.execQuery();\n      instance.store.updateCurrentRowData();\n      instance.store.updateExpandRows();\n      instance.store.updateTreeData(instance.store.states.defaultExpandAll.value);\n      if (unref(states.reserveSelection)) {\n        instance.store.assertRowKey();\n        instance.store.updateSelectionByRowKey();\n      } else {\n        if (dataInstanceChanged) {\n          instance.store.clearSelection();\n        } else {\n          instance.store.cleanSelection();\n        }\n      }\n      instance.store.updateAllSelected();\n      if (instance.$ready) {\n        instance.store.scheduleLayout();\n      }\n    },\n    insertColumn(states, column, parent, updateColumnOrder) {\n      const array = unref(states._columns);\n      let newColumns = [];\n      if (!parent) {\n        array.push(column);\n        newColumns = array;\n      } else {\n        if (parent && !parent.children) {\n          parent.children = [];\n        }\n        parent.children.push(column);\n        newColumns = replaceColumn(array, parent);\n      }\n      sortColumn(newColumns);\n      states._columns.value = newColumns;\n      states.updateOrderFns.push(updateColumnOrder);\n      if (column.type === \"selection\") {\n        states.selectable.value = column.selectable;\n        states.reserveSelection.value = column.reserveSelection;\n      }\n      if (instance.$ready) {\n        instance.store.updateColumns();\n        instance.store.scheduleLayout();\n      }\n    },\n    updateColumnOrder(states, column) {\n      var _a;\n      const newColumnIndex = (_a = column.getColumnIndex) == null ? void 0 : _a.call(column);\n      if (newColumnIndex === column.no) return;\n      sortColumn(states._columns.value);\n      if (instance.$ready) {\n        instance.store.updateColumns();\n      }\n    },\n    removeColumn(states, column, parent, updateColumnOrder) {\n      const array = unref(states._columns) || [];\n      if (parent) {\n        parent.children.splice(parent.children.findIndex(item => item.id === column.id), 1);\n        nextTick(() => {\n          var _a;\n          if (((_a = parent.children) == null ? void 0 : _a.length) === 0) {\n            delete parent.children;\n          }\n        });\n        states._columns.value = replaceColumn(array, parent);\n      } else {\n        const index = array.indexOf(column);\n        if (index > -1) {\n          array.splice(index, 1);\n          states._columns.value = array;\n        }\n      }\n      const updateFnIndex = states.updateOrderFns.indexOf(updateColumnOrder);\n      updateFnIndex > -1 && states.updateOrderFns.splice(updateFnIndex, 1);\n      if (instance.$ready) {\n        instance.store.updateColumns();\n        instance.store.scheduleLayout();\n      }\n    },\n    sort(states, options) {\n      const {\n        prop,\n        order,\n        init\n      } = options;\n      if (prop) {\n        const column = unref(states.columns).find(column2 => column2.property === prop);\n        if (column) {\n          column.order = order;\n          instance.store.updateSort(column, prop, order);\n          instance.store.commit(\"changeSortCondition\", {\n            init\n          });\n        }\n      }\n    },\n    changeSortCondition(states, options) {\n      const {\n        sortingColumn,\n        sortProp,\n        sortOrder\n      } = states;\n      const columnValue = unref(sortingColumn),\n        propValue = unref(sortProp),\n        orderValue = unref(sortOrder);\n      if (isNull(orderValue)) {\n        states.sortingColumn.value = null;\n        states.sortProp.value = null;\n      }\n      const ignore = {\n        filter: true\n      };\n      instance.store.execQuery(ignore);\n      if (!options || !(options.silent || options.init)) {\n        instance.emit(\"sort-change\", {\n          column: columnValue,\n          prop: propValue,\n          order: orderValue\n        });\n      }\n      instance.store.updateTableScrollY();\n    },\n    filterChange(_states, options) {\n      const {\n        column,\n        values,\n        silent\n      } = options;\n      const newFilters = instance.store.updateFilters(column, values);\n      instance.store.execQuery();\n      if (!silent) {\n        instance.emit(\"filter-change\", newFilters);\n      }\n      instance.store.updateTableScrollY();\n    },\n    toggleAllSelection() {\n      instance.store.toggleAllSelection();\n    },\n    rowSelectedChanged(_states, row) {\n      instance.store.toggleRowSelection(row);\n      instance.store.updateAllSelected();\n    },\n    setHoverRow(states, row) {\n      states.hoverRow.value = row;\n    },\n    setCurrentRow(_states, row) {\n      instance.store.updateCurrentRow(row);\n    }\n  };\n  const commit = function (name, ...args) {\n    const mutations2 = instance.store.mutations;\n    if (mutations2[name]) {\n      mutations2[name].apply(instance, [instance.store.states].concat(args));\n    } else {\n      throw new Error(`Action not found: ${name}`);\n    }\n  };\n  const updateTableScrollY = function () {\n    nextTick(() => instance.layout.updateScrollY.apply(instance.layout));\n  };\n  return {\n    ns,\n    ...watcher,\n    mutations,\n    commit,\n    updateTableScrollY\n  };\n}\nexport { useStore as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}