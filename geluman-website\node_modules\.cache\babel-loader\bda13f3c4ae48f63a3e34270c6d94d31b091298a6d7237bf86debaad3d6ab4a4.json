{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { isString } from '@vue/shared';\nimport { isNumber, isBoolean } from '../../../utils/types.mjs';\nconst radioPropsBase = buildProps({\n  modelValue: {\n    type: [String, Number, Boolean],\n    default: void 0\n  },\n  size: useSizeProp,\n  disabled: Boolean,\n  label: {\n    type: [String, Number, Boolean],\n    default: void 0\n  },\n  value: {\n    type: [String, Number, Boolean],\n    default: void 0\n  },\n  name: {\n    type: String,\n    default: void 0\n  }\n});\nconst radioProps = buildProps({\n  ...radioPropsBase,\n  border: Boolean\n});\nconst radioEmits = {\n  [UPDATE_MODEL_EVENT]: val => isString(val) || isNumber(val) || isBoolean(val),\n  [CHANGE_EVENT]: val => isString(val) || isNumber(val) || isBoolean(val)\n};\nexport { radioEmits, radioProps, radioPropsBase };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}