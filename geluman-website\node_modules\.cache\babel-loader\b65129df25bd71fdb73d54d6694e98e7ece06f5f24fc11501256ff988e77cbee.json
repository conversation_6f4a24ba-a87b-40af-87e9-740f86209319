{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, getCurrentInstance, ref, computed, watch, watchEffect, provide, reactive, onMounted, h, withDirectives, nextTick } from 'vue';\nimport { useResizeObserver } from '@vueuse/core';\nimport { isNil } from 'lodash-unified';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { More } from '@element-plus/icons-vue';\nimport Menu$1 from './utils/menu-bar.mjs';\nimport ElMenuCollapseTransition from './menu-collapse-transition.mjs';\nimport SubMenu from './sub-menu.mjs';\nimport { useMenuCssVar } from './use-menu-css-var.mjs';\nimport ClickOutside from '../../../directives/click-outside/index.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { flattedChildren } from '../../../utils/vue/vnode.mjs';\nimport { isString, isArray, isObject } from '@vue/shared';\nimport { isUndefined } from '../../../utils/types.mjs';\nconst menuProps = buildProps({\n  mode: {\n    type: String,\n    values: [\"horizontal\", \"vertical\"],\n    default: \"vertical\"\n  },\n  defaultActive: {\n    type: String,\n    default: \"\"\n  },\n  defaultOpeneds: {\n    type: definePropType(Array),\n    default: () => mutable([])\n  },\n  uniqueOpened: Boolean,\n  router: Boolean,\n  menuTrigger: {\n    type: String,\n    values: [\"hover\", \"click\"],\n    default: \"hover\"\n  },\n  collapse: Boolean,\n  backgroundColor: String,\n  textColor: String,\n  activeTextColor: String,\n  closeOnClickOutside: Boolean,\n  collapseTransition: {\n    type: Boolean,\n    default: true\n  },\n  ellipsis: {\n    type: Boolean,\n    default: true\n  },\n  popperOffset: {\n    type: Number,\n    default: 6\n  },\n  ellipsisIcon: {\n    type: iconPropType,\n    default: () => More\n  },\n  popperEffect: {\n    type: definePropType(String),\n    default: \"dark\"\n  },\n  popperClass: String,\n  showTimeout: {\n    type: Number,\n    default: 300\n  },\n  hideTimeout: {\n    type: Number,\n    default: 300\n  },\n  persistent: {\n    type: Boolean,\n    default: true\n  }\n});\nconst checkIndexPath = indexPath => isArray(indexPath) && indexPath.every(path => isString(path));\nconst menuEmits = {\n  close: (index, indexPath) => isString(index) && checkIndexPath(indexPath),\n  open: (index, indexPath) => isString(index) && checkIndexPath(indexPath),\n  select: (index, indexPath, item, routerResult) => isString(index) && checkIndexPath(indexPath) && isObject(item) && (isUndefined(routerResult) || routerResult instanceof Promise)\n};\nvar Menu = defineComponent({\n  name: \"ElMenu\",\n  props: menuProps,\n  emits: menuEmits,\n  setup(props, {\n    emit,\n    slots,\n    expose\n  }) {\n    const instance = getCurrentInstance();\n    const router = instance.appContext.config.globalProperties.$router;\n    const menu = ref();\n    const nsMenu = useNamespace(\"menu\");\n    const nsSubMenu = useNamespace(\"sub-menu\");\n    const sliceIndex = ref(-1);\n    const openedMenus = ref(props.defaultOpeneds && !props.collapse ? props.defaultOpeneds.slice(0) : []);\n    const activeIndex = ref(props.defaultActive);\n    const items = ref({});\n    const subMenus = ref({});\n    const isMenuPopup = computed(() => props.mode === \"horizontal\" || props.mode === \"vertical\" && props.collapse);\n    const initMenu = () => {\n      const activeItem = activeIndex.value && items.value[activeIndex.value];\n      if (!activeItem || props.mode === \"horizontal\" || props.collapse) return;\n      const indexPath = activeItem.indexPath;\n      indexPath.forEach(index => {\n        const subMenu = subMenus.value[index];\n        subMenu && openMenu(index, subMenu.indexPath);\n      });\n    };\n    const openMenu = (index, indexPath) => {\n      if (openedMenus.value.includes(index)) return;\n      if (props.uniqueOpened) {\n        openedMenus.value = openedMenus.value.filter(index2 => indexPath.includes(index2));\n      }\n      openedMenus.value.push(index);\n      emit(\"open\", index, indexPath);\n    };\n    const close = index => {\n      const i = openedMenus.value.indexOf(index);\n      if (i !== -1) {\n        openedMenus.value.splice(i, 1);\n      }\n    };\n    const closeMenu = (index, indexPath) => {\n      close(index);\n      emit(\"close\", index, indexPath);\n    };\n    const handleSubMenuClick = ({\n      index,\n      indexPath\n    }) => {\n      const isOpened = openedMenus.value.includes(index);\n      isOpened ? closeMenu(index, indexPath) : openMenu(index, indexPath);\n    };\n    const handleMenuItemClick = menuItem => {\n      if (props.mode === \"horizontal\" || props.collapse) {\n        openedMenus.value = [];\n      }\n      const {\n        index,\n        indexPath\n      } = menuItem;\n      if (isNil(index) || isNil(indexPath)) return;\n      if (props.router && router) {\n        const route = menuItem.route || index;\n        const routerResult = router.push(route).then(res => {\n          if (!res) activeIndex.value = index;\n          return res;\n        });\n        emit(\"select\", index, indexPath, {\n          index,\n          indexPath,\n          route\n        }, routerResult);\n      } else {\n        activeIndex.value = index;\n        emit(\"select\", index, indexPath, {\n          index,\n          indexPath\n        });\n      }\n    };\n    const updateActiveIndex = val => {\n      var _a;\n      const itemsInData = items.value;\n      const item = itemsInData[val] || activeIndex.value && itemsInData[activeIndex.value] || itemsInData[props.defaultActive];\n      activeIndex.value = (_a = item == null ? void 0 : item.index) != null ? _a : val;\n    };\n    const calcMenuItemWidth = menuItem => {\n      const computedStyle = getComputedStyle(menuItem);\n      const marginLeft = Number.parseInt(computedStyle.marginLeft, 10);\n      const marginRight = Number.parseInt(computedStyle.marginRight, 10);\n      return menuItem.offsetWidth + marginLeft + marginRight || 0;\n    };\n    const calcSliceIndex = () => {\n      var _a, _b;\n      if (!menu.value) return -1;\n      const items2 = Array.from((_b = (_a = menu.value) == null ? void 0 : _a.childNodes) != null ? _b : []).filter(item => item.nodeName !== \"#text\" || item.nodeValue);\n      const moreItemWidth = 64;\n      const computedMenuStyle = getComputedStyle(menu.value);\n      const paddingLeft = Number.parseInt(computedMenuStyle.paddingLeft, 10);\n      const paddingRight = Number.parseInt(computedMenuStyle.paddingRight, 10);\n      const menuWidth = menu.value.clientWidth - paddingLeft - paddingRight;\n      let calcWidth = 0;\n      let sliceIndex2 = 0;\n      items2.forEach((item, index) => {\n        if (item.nodeName === \"#comment\") return;\n        calcWidth += calcMenuItemWidth(item);\n        if (calcWidth <= menuWidth - moreItemWidth) {\n          sliceIndex2 = index + 1;\n        }\n      });\n      return sliceIndex2 === items2.length ? -1 : sliceIndex2;\n    };\n    const getIndexPath = index => subMenus.value[index].indexPath;\n    const debounce = (fn, wait = 33.34) => {\n      let timmer;\n      return () => {\n        timmer && clearTimeout(timmer);\n        timmer = setTimeout(() => {\n          fn();\n        }, wait);\n      };\n    };\n    let isFirstTimeRender = true;\n    const handleResize = () => {\n      if (sliceIndex.value === calcSliceIndex()) return;\n      const callback = () => {\n        sliceIndex.value = -1;\n        nextTick(() => {\n          sliceIndex.value = calcSliceIndex();\n        });\n      };\n      isFirstTimeRender ? callback() : debounce(callback)();\n      isFirstTimeRender = false;\n    };\n    watch(() => props.defaultActive, currentActive => {\n      if (!items.value[currentActive]) {\n        activeIndex.value = \"\";\n      }\n      updateActiveIndex(currentActive);\n    });\n    watch(() => props.collapse, value => {\n      if (value) openedMenus.value = [];\n    });\n    watch(items.value, initMenu);\n    let resizeStopper;\n    watchEffect(() => {\n      if (props.mode === \"horizontal\" && props.ellipsis) resizeStopper = useResizeObserver(menu, handleResize).stop;else resizeStopper == null ? void 0 : resizeStopper();\n    });\n    const mouseInChild = ref(false);\n    {\n      const addSubMenu = item => {\n        subMenus.value[item.index] = item;\n      };\n      const removeSubMenu = item => {\n        delete subMenus.value[item.index];\n      };\n      const addMenuItem = item => {\n        items.value[item.index] = item;\n      };\n      const removeMenuItem = item => {\n        delete items.value[item.index];\n      };\n      provide(\"rootMenu\", reactive({\n        props,\n        openedMenus,\n        items,\n        subMenus,\n        activeIndex,\n        isMenuPopup,\n        addMenuItem,\n        removeMenuItem,\n        addSubMenu,\n        removeSubMenu,\n        openMenu,\n        closeMenu,\n        handleMenuItemClick,\n        handleSubMenuClick\n      }));\n      provide(`subMenu:${instance.uid}`, {\n        addSubMenu,\n        removeSubMenu,\n        mouseInChild,\n        level: 0\n      });\n    }\n    onMounted(() => {\n      if (props.mode === \"horizontal\") {\n        new Menu$1(instance.vnode.el, nsMenu.namespace.value);\n      }\n    });\n    {\n      const open = index => {\n        const {\n          indexPath\n        } = subMenus.value[index];\n        indexPath.forEach(i => openMenu(i, indexPath));\n      };\n      expose({\n        open,\n        close,\n        updateActiveIndex,\n        handleResize\n      });\n    }\n    const ulStyle = useMenuCssVar(props, 0);\n    return () => {\n      var _a, _b;\n      let slot = (_b = (_a = slots.default) == null ? void 0 : _a.call(slots)) != null ? _b : [];\n      const vShowMore = [];\n      if (props.mode === \"horizontal\" && menu.value) {\n        const originalSlot = flattedChildren(slot);\n        const slotDefault = sliceIndex.value === -1 ? originalSlot : originalSlot.slice(0, sliceIndex.value);\n        const slotMore = sliceIndex.value === -1 ? [] : originalSlot.slice(sliceIndex.value);\n        if ((slotMore == null ? void 0 : slotMore.length) && props.ellipsis) {\n          slot = slotDefault;\n          vShowMore.push(h(SubMenu, {\n            index: \"sub-menu-more\",\n            class: nsSubMenu.e(\"hide-arrow\"),\n            popperOffset: props.popperOffset\n          }, {\n            title: () => h(ElIcon, {\n              class: nsSubMenu.e(\"icon-more\")\n            }, {\n              default: () => h(props.ellipsisIcon)\n            }),\n            default: () => slotMore\n          }));\n        }\n      }\n      const directives = props.closeOnClickOutside ? [[ClickOutside, () => {\n        if (!openedMenus.value.length) return;\n        if (!mouseInChild.value) {\n          openedMenus.value.forEach(openedMenu => emit(\"close\", openedMenu, getIndexPath(openedMenu)));\n          openedMenus.value = [];\n        }\n      }]] : [];\n      const vMenu = withDirectives(h(\"ul\", {\n        key: String(props.collapse),\n        role: \"menubar\",\n        ref: menu,\n        style: ulStyle.value,\n        class: {\n          [nsMenu.b()]: true,\n          [nsMenu.m(props.mode)]: true,\n          [nsMenu.m(\"collapse\")]: props.collapse\n        }\n      }, [...slot, ...vShowMore]), directives);\n      if (props.collapseTransition && props.mode === \"vertical\") {\n        return h(ElMenuCollapseTransition, () => vMenu);\n      }\n      return vMenu;\n    };\n  }\n});\nexport { Menu as default, menuEmits, menuProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}