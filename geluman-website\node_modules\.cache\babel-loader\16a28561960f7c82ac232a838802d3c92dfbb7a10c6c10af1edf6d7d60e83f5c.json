{"ast": null, "code": "import baseLodash from './_baseLodash.js';\nimport wrapperClone from './_wrapperClone.js';\n\n/**\n * Creates a clone of the chain sequence planting `value` as the wrapped value.\n *\n * @name plant\n * @memberOf _\n * @since 3.2.0\n * @category Seq\n * @param {*} value The value to plant.\n * @returns {Object} Returns the new `lodash` wrapper instance.\n * @example\n *\n * function square(n) {\n *   return n * n;\n * }\n *\n * var wrapped = _([1, 2]).map(square);\n * var other = wrapped.plant([3, 4]);\n *\n * other.value();\n * // => [9, 16]\n *\n * wrapped.value();\n * // => [1, 4]\n */\nfunction wrapperPlant(value) {\n  var result,\n    parent = this;\n  while (parent instanceof baseLodash) {\n    var clone = wrapperClone(parent);\n    clone.__index__ = 0;\n    clone.__values__ = undefined;\n    if (result) {\n      previous.__wrapped__ = clone;\n    } else {\n      result = clone;\n    }\n    var previous = clone;\n    parent = parent.__wrapped__;\n  }\n  previous.__wrapped__ = value;\n  return result;\n}\nexport default wrapperPlant;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}