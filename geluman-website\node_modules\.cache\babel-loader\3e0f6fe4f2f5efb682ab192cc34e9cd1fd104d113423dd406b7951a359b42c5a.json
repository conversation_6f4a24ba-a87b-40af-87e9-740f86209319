{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\n/**\n* @vue/runtime-core v3.5.13\n* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors\n* @license MIT\n**/\nimport { pauseTracking, resetTracking, isRef, toRaw, traverse, shallowRef, readonly, isReactive, ref, isShallow, shallowReadArray, toReactive, shallowReadonly, track, reactive, shallowReactive, trigger, ReactiveEffect, watch as watch$1, customRef, isProxy, proxyRefs, markRaw, EffectScope, computed as computed$1, isReadonly } from '@vue/reactivity';\nexport { EffectScope, ReactiveEffect, TrackOpTypes, TriggerOpTypes, customRef, effect, effectScope, getCurrentScope, getCurrentWatcher, isProxy, isReactive, isReadonly, isRef, isShallow, markRaw, onScopeDispose, onWatcherCleanup, proxyRefs, reactive, readonly, ref, shallowReactive, shallowReadonly, shallowRef, stop, toRaw, toRef, toRefs, toValue, triggerRef, unref } from '@vue/reactivity';\nimport { isString, isFunction, isPromise, isArray, EMPTY_OBJ, NOOP, getGlobalThis, extend, isBuiltInDirective, hasOwn, remove, def, isOn, isReservedProp, normalizeClass, stringifyStyle, normalizeStyle, isKnownSvgAttr, isBooleanAttr, isKnownHtmlAttr, includeBooleanAttr, isRenderableAttrValue, getEscapedCssVarName, isObject, isRegExp, invokeArrayFns, toHandlerKey, capitalize, camelize, isSymbol, isGloballyAllowed, NO, hyphenate, EMPTY_ARR, toRawType, makeMap, hasChanged, looseToNumber, isModelListener, toNumber } from '@vue/shared';\nexport { camelize, capitalize, normalizeClass, normalizeProps, normalizeStyle, toDisplayString, toHandlerKey } from '@vue/shared';\nconst stack = [];\nfunction pushWarningContext(vnode) {\n  stack.push(vnode);\n}\nfunction popWarningContext() {\n  stack.pop();\n}\nlet isWarning = false;\nfunction warn$1(msg, ...args) {\n  if (isWarning) return;\n  isWarning = true;\n  pauseTracking();\n  const instance = stack.length ? stack[stack.length - 1].component : null;\n  const appWarnHandler = instance && instance.appContext.config.warnHandler;\n  const trace = getComponentTrace();\n  if (appWarnHandler) {\n    callWithErrorHandling(appWarnHandler, instance, 11, [\n    // eslint-disable-next-line no-restricted-syntax\n    msg + args.map(a => {\n      var _a, _b;\n      return (_b = (_a = a.toString) == null ? void 0 : _a.call(a)) != null ? _b : JSON.stringify(a);\n    }).join(\"\"), instance && instance.proxy, trace.map(({\n      vnode\n    }) => `at <${formatComponentName(instance, vnode.type)}>`).join(\"\\n\"), trace]);\n  } else {\n    const warnArgs = [`[Vue warn]: ${msg}`, ...args];\n    if (trace.length &&\n    // avoid spamming console during tests\n    true) {\n      warnArgs.push(`\n`, ...formatTrace(trace));\n    }\n    console.warn(...warnArgs);\n  }\n  resetTracking();\n  isWarning = false;\n}\nfunction getComponentTrace() {\n  let currentVNode = stack[stack.length - 1];\n  if (!currentVNode) {\n    return [];\n  }\n  const normalizedStack = [];\n  while (currentVNode) {\n    const last = normalizedStack[0];\n    if (last && last.vnode === currentVNode) {\n      last.recurseCount++;\n    } else {\n      normalizedStack.push({\n        vnode: currentVNode,\n        recurseCount: 0\n      });\n    }\n    const parentInstance = currentVNode.component && currentVNode.component.parent;\n    currentVNode = parentInstance && parentInstance.vnode;\n  }\n  return normalizedStack;\n}\nfunction formatTrace(trace) {\n  const logs = [];\n  trace.forEach((entry, i) => {\n    logs.push(...(i === 0 ? [] : [`\n`]), ...formatTraceEntry(entry));\n  });\n  return logs;\n}\nfunction formatTraceEntry({\n  vnode,\n  recurseCount\n}) {\n  const postfix = recurseCount > 0 ? `... (${recurseCount} recursive calls)` : ``;\n  const isRoot = vnode.component ? vnode.component.parent == null : false;\n  const open = ` at <${formatComponentName(vnode.component, vnode.type, isRoot)}`;\n  const close = `>` + postfix;\n  return vnode.props ? [open, ...formatProps(vnode.props), close] : [open + close];\n}\nfunction formatProps(props) {\n  const res = [];\n  const keys = Object.keys(props);\n  keys.slice(0, 3).forEach(key => {\n    res.push(...formatProp(key, props[key]));\n  });\n  if (keys.length > 3) {\n    res.push(` ...`);\n  }\n  return res;\n}\nfunction formatProp(key, value, raw) {\n  if (isString(value)) {\n    value = JSON.stringify(value);\n    return raw ? value : [`${key}=${value}`];\n  } else if (typeof value === \"number\" || typeof value === \"boolean\" || value == null) {\n    return raw ? value : [`${key}=${value}`];\n  } else if (isRef(value)) {\n    value = formatProp(key, toRaw(value.value), true);\n    return raw ? value : [`${key}=Ref<`, value, `>`];\n  } else if (isFunction(value)) {\n    return [`${key}=fn${value.name ? `<${value.name}>` : ``}`];\n  } else {\n    value = toRaw(value);\n    return raw ? value : [`${key}=`, value];\n  }\n}\nfunction assertNumber(val, type) {\n  if (!!!(process.env.NODE_ENV !== \"production\")) return;\n  if (val === void 0) {\n    return;\n  } else if (typeof val !== \"number\") {\n    warn$1(`${type} is not a valid number - got ${JSON.stringify(val)}.`);\n  } else if (isNaN(val)) {\n    warn$1(`${type} is NaN - the duration expression might be incorrect.`);\n  }\n}\nconst ErrorCodes = {\n  \"SETUP_FUNCTION\": 0,\n  \"0\": \"SETUP_FUNCTION\",\n  \"RENDER_FUNCTION\": 1,\n  \"1\": \"RENDER_FUNCTION\",\n  \"NATIVE_EVENT_HANDLER\": 5,\n  \"5\": \"NATIVE_EVENT_HANDLER\",\n  \"COMPONENT_EVENT_HANDLER\": 6,\n  \"6\": \"COMPONENT_EVENT_HANDLER\",\n  \"VNODE_HOOK\": 7,\n  \"7\": \"VNODE_HOOK\",\n  \"DIRECTIVE_HOOK\": 8,\n  \"8\": \"DIRECTIVE_HOOK\",\n  \"TRANSITION_HOOK\": 9,\n  \"9\": \"TRANSITION_HOOK\",\n  \"APP_ERROR_HANDLER\": 10,\n  \"10\": \"APP_ERROR_HANDLER\",\n  \"APP_WARN_HANDLER\": 11,\n  \"11\": \"APP_WARN_HANDLER\",\n  \"FUNCTION_REF\": 12,\n  \"12\": \"FUNCTION_REF\",\n  \"ASYNC_COMPONENT_LOADER\": 13,\n  \"13\": \"ASYNC_COMPONENT_LOADER\",\n  \"SCHEDULER\": 14,\n  \"14\": \"SCHEDULER\",\n  \"COMPONENT_UPDATE\": 15,\n  \"15\": \"COMPONENT_UPDATE\",\n  \"APP_UNMOUNT_CLEANUP\": 16,\n  \"16\": \"APP_UNMOUNT_CLEANUP\"\n};\nconst ErrorTypeStrings$1 = {\n  [\"sp\"]: \"serverPrefetch hook\",\n  [\"bc\"]: \"beforeCreate hook\",\n  [\"c\"]: \"created hook\",\n  [\"bm\"]: \"beforeMount hook\",\n  [\"m\"]: \"mounted hook\",\n  [\"bu\"]: \"beforeUpdate hook\",\n  [\"u\"]: \"updated\",\n  [\"bum\"]: \"beforeUnmount hook\",\n  [\"um\"]: \"unmounted hook\",\n  [\"a\"]: \"activated hook\",\n  [\"da\"]: \"deactivated hook\",\n  [\"ec\"]: \"errorCaptured hook\",\n  [\"rtc\"]: \"renderTracked hook\",\n  [\"rtg\"]: \"renderTriggered hook\",\n  [0]: \"setup function\",\n  [1]: \"render function\",\n  [2]: \"watcher getter\",\n  [3]: \"watcher callback\",\n  [4]: \"watcher cleanup function\",\n  [5]: \"native event handler\",\n  [6]: \"component event handler\",\n  [7]: \"vnode hook\",\n  [8]: \"directive hook\",\n  [9]: \"transition hook\",\n  [10]: \"app errorHandler\",\n  [11]: \"app warnHandler\",\n  [12]: \"ref function\",\n  [13]: \"async component loader\",\n  [14]: \"scheduler flush\",\n  [15]: \"component update\",\n  [16]: \"app unmount cleanup function\"\n};\nfunction callWithErrorHandling(fn, instance, type, args) {\n  try {\n    return args ? fn(...args) : fn();\n  } catch (err) {\n    handleError(err, instance, type);\n  }\n}\nfunction callWithAsyncErrorHandling(fn, instance, type, args) {\n  if (isFunction(fn)) {\n    const res = callWithErrorHandling(fn, instance, type, args);\n    if (res && isPromise(res)) {\n      res.catch(err => {\n        handleError(err, instance, type);\n      });\n    }\n    return res;\n  }\n  if (isArray(fn)) {\n    const values = [];\n    for (let i = 0; i < fn.length; i++) {\n      values.push(callWithAsyncErrorHandling(fn[i], instance, type, args));\n    }\n    return values;\n  } else if (!!(process.env.NODE_ENV !== \"production\")) {\n    warn$1(`Invalid value type passed to callWithAsyncErrorHandling(): ${typeof fn}`);\n  }\n}\nfunction handleError(err, instance, type, throwInDev = true) {\n  const contextVNode = instance ? instance.vnode : null;\n  const {\n    errorHandler,\n    throwUnhandledErrorInProduction\n  } = instance && instance.appContext.config || EMPTY_OBJ;\n  if (instance) {\n    let cur = instance.parent;\n    const exposedInstance = instance.proxy;\n    const errorInfo = !!(process.env.NODE_ENV !== \"production\") ? ErrorTypeStrings$1[type] : `https://vuejs.org/error-reference/#runtime-${type}`;\n    while (cur) {\n      const errorCapturedHooks = cur.ec;\n      if (errorCapturedHooks) {\n        for (let i = 0; i < errorCapturedHooks.length; i++) {\n          if (errorCapturedHooks[i](err, exposedInstance, errorInfo) === false) {\n            return;\n          }\n        }\n      }\n      cur = cur.parent;\n    }\n    if (errorHandler) {\n      pauseTracking();\n      callWithErrorHandling(errorHandler, null, 10, [err, exposedInstance, errorInfo]);\n      resetTracking();\n      return;\n    }\n  }\n  logError(err, type, contextVNode, throwInDev, throwUnhandledErrorInProduction);\n}\nfunction logError(err, type, contextVNode, throwInDev = true, throwInProd = false) {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    const info = ErrorTypeStrings$1[type];\n    if (contextVNode) {\n      pushWarningContext(contextVNode);\n    }\n    warn$1(`Unhandled error${info ? ` during execution of ${info}` : ``}`);\n    if (contextVNode) {\n      popWarningContext();\n    }\n    if (throwInDev) {\n      throw err;\n    } else {\n      console.error(err);\n    }\n  } else if (throwInProd) {\n    throw err;\n  } else {\n    console.error(err);\n  }\n}\nconst queue = [];\nlet flushIndex = -1;\nconst pendingPostFlushCbs = [];\nlet activePostFlushCbs = null;\nlet postFlushIndex = 0;\nconst resolvedPromise = /* @__PURE__ */Promise.resolve();\nlet currentFlushPromise = null;\nconst RECURSION_LIMIT = 100;\nfunction nextTick(fn) {\n  const p = currentFlushPromise || resolvedPromise;\n  return fn ? p.then(this ? fn.bind(this) : fn) : p;\n}\nfunction findInsertionIndex(id) {\n  let start = flushIndex + 1;\n  let end = queue.length;\n  while (start < end) {\n    const middle = start + end >>> 1;\n    const middleJob = queue[middle];\n    const middleJobId = getId(middleJob);\n    if (middleJobId < id || middleJobId === id && middleJob.flags & 2) {\n      start = middle + 1;\n    } else {\n      end = middle;\n    }\n  }\n  return start;\n}\nfunction queueJob(job) {\n  if (!(job.flags & 1)) {\n    const jobId = getId(job);\n    const lastJob = queue[queue.length - 1];\n    if (!lastJob ||\n    // fast path when the job id is larger than the tail\n    !(job.flags & 2) && jobId >= getId(lastJob)) {\n      queue.push(job);\n    } else {\n      queue.splice(findInsertionIndex(jobId), 0, job);\n    }\n    job.flags |= 1;\n    queueFlush();\n  }\n}\nfunction queueFlush() {\n  if (!currentFlushPromise) {\n    currentFlushPromise = resolvedPromise.then(flushJobs);\n  }\n}\nfunction queuePostFlushCb(cb) {\n  if (!isArray(cb)) {\n    if (activePostFlushCbs && cb.id === -1) {\n      activePostFlushCbs.splice(postFlushIndex + 1, 0, cb);\n    } else if (!(cb.flags & 1)) {\n      pendingPostFlushCbs.push(cb);\n      cb.flags |= 1;\n    }\n  } else {\n    pendingPostFlushCbs.push(...cb);\n  }\n  queueFlush();\n}\nfunction flushPreFlushCbs(instance, seen, i = flushIndex + 1) {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    seen = seen || /* @__PURE__ */new Map();\n  }\n  for (; i < queue.length; i++) {\n    const cb = queue[i];\n    if (cb && cb.flags & 2) {\n      if (instance && cb.id !== instance.uid) {\n        continue;\n      }\n      if (!!(process.env.NODE_ENV !== \"production\") && checkRecursiveUpdates(seen, cb)) {\n        continue;\n      }\n      queue.splice(i, 1);\n      i--;\n      if (cb.flags & 4) {\n        cb.flags &= ~1;\n      }\n      cb();\n      if (!(cb.flags & 4)) {\n        cb.flags &= ~1;\n      }\n    }\n  }\n}\nfunction flushPostFlushCbs(seen) {\n  if (pendingPostFlushCbs.length) {\n    const deduped = [...new Set(pendingPostFlushCbs)].sort((a, b) => getId(a) - getId(b));\n    pendingPostFlushCbs.length = 0;\n    if (activePostFlushCbs) {\n      activePostFlushCbs.push(...deduped);\n      return;\n    }\n    activePostFlushCbs = deduped;\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      seen = seen || /* @__PURE__ */new Map();\n    }\n    for (postFlushIndex = 0; postFlushIndex < activePostFlushCbs.length; postFlushIndex++) {\n      const cb = activePostFlushCbs[postFlushIndex];\n      if (!!(process.env.NODE_ENV !== \"production\") && checkRecursiveUpdates(seen, cb)) {\n        continue;\n      }\n      if (cb.flags & 4) {\n        cb.flags &= ~1;\n      }\n      if (!(cb.flags & 8)) cb();\n      cb.flags &= ~1;\n    }\n    activePostFlushCbs = null;\n    postFlushIndex = 0;\n  }\n}\nconst getId = job => job.id == null ? job.flags & 2 ? -1 : Infinity : job.id;\nfunction flushJobs(seen) {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    seen = seen || /* @__PURE__ */new Map();\n  }\n  const check = !!(process.env.NODE_ENV !== \"production\") ? job => checkRecursiveUpdates(seen, job) : NOOP;\n  try {\n    for (flushIndex = 0; flushIndex < queue.length; flushIndex++) {\n      const job = queue[flushIndex];\n      if (job && !(job.flags & 8)) {\n        if (!!(process.env.NODE_ENV !== \"production\") && check(job)) {\n          continue;\n        }\n        if (job.flags & 4) {\n          job.flags &= ~1;\n        }\n        callWithErrorHandling(job, job.i, job.i ? 15 : 14);\n        if (!(job.flags & 4)) {\n          job.flags &= ~1;\n        }\n      }\n    }\n  } finally {\n    for (; flushIndex < queue.length; flushIndex++) {\n      const job = queue[flushIndex];\n      if (job) {\n        job.flags &= ~1;\n      }\n    }\n    flushIndex = -1;\n    queue.length = 0;\n    flushPostFlushCbs(seen);\n    currentFlushPromise = null;\n    if (queue.length || pendingPostFlushCbs.length) {\n      flushJobs(seen);\n    }\n  }\n}\nfunction checkRecursiveUpdates(seen, fn) {\n  const count = seen.get(fn) || 0;\n  if (count > RECURSION_LIMIT) {\n    const instance = fn.i;\n    const componentName = instance && getComponentName(instance.type);\n    handleError(`Maximum recursive updates exceeded${componentName ? ` in component <${componentName}>` : ``}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`, null, 10);\n    return true;\n  }\n  seen.set(fn, count + 1);\n  return false;\n}\nlet isHmrUpdating = false;\nconst hmrDirtyComponents = /* @__PURE__ */new Map();\nif (!!(process.env.NODE_ENV !== \"production\")) {\n  getGlobalThis().__VUE_HMR_RUNTIME__ = {\n    createRecord: tryWrap(createRecord),\n    rerender: tryWrap(rerender),\n    reload: tryWrap(reload)\n  };\n}\nconst map = /* @__PURE__ */new Map();\nfunction registerHMR(instance) {\n  const id = instance.type.__hmrId;\n  let record = map.get(id);\n  if (!record) {\n    createRecord(id, instance.type);\n    record = map.get(id);\n  }\n  record.instances.add(instance);\n}\nfunction unregisterHMR(instance) {\n  map.get(instance.type.__hmrId).instances.delete(instance);\n}\nfunction createRecord(id, initialDef) {\n  if (map.has(id)) {\n    return false;\n  }\n  map.set(id, {\n    initialDef: normalizeClassComponent(initialDef),\n    instances: /* @__PURE__ */new Set()\n  });\n  return true;\n}\nfunction normalizeClassComponent(component) {\n  return isClassComponent(component) ? component.__vccOpts : component;\n}\nfunction rerender(id, newRender) {\n  const record = map.get(id);\n  if (!record) {\n    return;\n  }\n  record.initialDef.render = newRender;\n  [...record.instances].forEach(instance => {\n    if (newRender) {\n      instance.render = newRender;\n      normalizeClassComponent(instance.type).render = newRender;\n    }\n    instance.renderCache = [];\n    isHmrUpdating = true;\n    instance.update();\n    isHmrUpdating = false;\n  });\n}\nfunction reload(id, newComp) {\n  const record = map.get(id);\n  if (!record) return;\n  newComp = normalizeClassComponent(newComp);\n  updateComponentDef(record.initialDef, newComp);\n  const instances = [...record.instances];\n  for (let i = 0; i < instances.length; i++) {\n    const instance = instances[i];\n    const oldComp = normalizeClassComponent(instance.type);\n    let dirtyInstances = hmrDirtyComponents.get(oldComp);\n    if (!dirtyInstances) {\n      if (oldComp !== record.initialDef) {\n        updateComponentDef(oldComp, newComp);\n      }\n      hmrDirtyComponents.set(oldComp, dirtyInstances = /* @__PURE__ */new Set());\n    }\n    dirtyInstances.add(instance);\n    instance.appContext.propsCache.delete(instance.type);\n    instance.appContext.emitsCache.delete(instance.type);\n    instance.appContext.optionsCache.delete(instance.type);\n    if (instance.ceReload) {\n      dirtyInstances.add(instance);\n      instance.ceReload(newComp.styles);\n      dirtyInstances.delete(instance);\n    } else if (instance.parent) {\n      queueJob(() => {\n        isHmrUpdating = true;\n        instance.parent.update();\n        isHmrUpdating = false;\n        dirtyInstances.delete(instance);\n      });\n    } else if (instance.appContext.reload) {\n      instance.appContext.reload();\n    } else if (typeof window !== \"undefined\") {\n      window.location.reload();\n    } else {\n      console.warn(\"[HMR] Root or manually mounted instance modified. Full reload required.\");\n    }\n    if (instance.root.ce && instance !== instance.root) {\n      instance.root.ce._removeChildStyle(oldComp);\n    }\n  }\n  queuePostFlushCb(() => {\n    hmrDirtyComponents.clear();\n  });\n}\nfunction updateComponentDef(oldComp, newComp) {\n  extend(oldComp, newComp);\n  for (const key in oldComp) {\n    if (key !== \"__file\" && !(key in newComp)) {\n      delete oldComp[key];\n    }\n  }\n}\nfunction tryWrap(fn) {\n  return (id, arg) => {\n    try {\n      return fn(id, arg);\n    } catch (e) {\n      console.error(e);\n      console.warn(`[HMR] Something went wrong during Vue component hot-reload. Full reload required.`);\n    }\n  };\n}\nlet devtools$1;\nlet buffer = [];\nlet devtoolsNotInstalled = false;\nfunction emit$1(event, ...args) {\n  if (devtools$1) {\n    devtools$1.emit(event, ...args);\n  } else if (!devtoolsNotInstalled) {\n    buffer.push({\n      event,\n      args\n    });\n  }\n}\nfunction setDevtoolsHook$1(hook, target) {\n  var _a, _b;\n  devtools$1 = hook;\n  if (devtools$1) {\n    devtools$1.enabled = true;\n    buffer.forEach(({\n      event,\n      args\n    }) => devtools$1.emit(event, ...args));\n    buffer = [];\n  } else if (\n  // handle late devtools injection - only do this if we are in an actual\n  // browser environment to avoid the timer handle stalling test runner exit\n  // (#4815)\n  typeof window !== \"undefined\" &&\n  // some envs mock window but not fully\n  window.HTMLElement &&\n  // also exclude jsdom\n  // eslint-disable-next-line no-restricted-syntax\n  !((_b = (_a = window.navigator) == null ? void 0 : _a.userAgent) == null ? void 0 : _b.includes(\"jsdom\"))) {\n    const replay = target.__VUE_DEVTOOLS_HOOK_REPLAY__ = target.__VUE_DEVTOOLS_HOOK_REPLAY__ || [];\n    replay.push(newHook => {\n      setDevtoolsHook$1(newHook, target);\n    });\n    setTimeout(() => {\n      if (!devtools$1) {\n        target.__VUE_DEVTOOLS_HOOK_REPLAY__ = null;\n        devtoolsNotInstalled = true;\n        buffer = [];\n      }\n    }, 3e3);\n  } else {\n    devtoolsNotInstalled = true;\n    buffer = [];\n  }\n}\nfunction devtoolsInitApp(app, version) {\n  emit$1(\"app:init\" /* APP_INIT */, app, version, {\n    Fragment,\n    Text,\n    Comment,\n    Static\n  });\n}\nfunction devtoolsUnmountApp(app) {\n  emit$1(\"app:unmount\" /* APP_UNMOUNT */, app);\n}\nconst devtoolsComponentAdded = /* @__PURE__ */createDevtoolsComponentHook(\"component:added\" /* COMPONENT_ADDED */);\nconst devtoolsComponentUpdated = /* @__PURE__ */createDevtoolsComponentHook(\"component:updated\" /* COMPONENT_UPDATED */);\nconst _devtoolsComponentRemoved = /* @__PURE__ */createDevtoolsComponentHook(\"component:removed\" /* COMPONENT_REMOVED */);\nconst devtoolsComponentRemoved = component => {\n  if (devtools$1 && typeof devtools$1.cleanupBuffer === \"function\" &&\n  // remove the component if it wasn't buffered\n  !devtools$1.cleanupBuffer(component)) {\n    _devtoolsComponentRemoved(component);\n  }\n};\n/*! #__NO_SIDE_EFFECTS__ */\n// @__NO_SIDE_EFFECTS__\nfunction createDevtoolsComponentHook(hook) {\n  return component => {\n    emit$1(hook, component.appContext.app, component.uid, component.parent ? component.parent.uid : void 0, component);\n  };\n}\nconst devtoolsPerfStart = /* @__PURE__ */createDevtoolsPerformanceHook(\"perf:start\" /* PERFORMANCE_START */);\nconst devtoolsPerfEnd = /* @__PURE__ */createDevtoolsPerformanceHook(\"perf:end\" /* PERFORMANCE_END */);\nfunction createDevtoolsPerformanceHook(hook) {\n  return (component, type, time) => {\n    emit$1(hook, component.appContext.app, component.uid, component, type, time);\n  };\n}\nfunction devtoolsComponentEmit(component, event, params) {\n  emit$1(\"component:emit\" /* COMPONENT_EMIT */, component.appContext.app, component, event, params);\n}\nlet currentRenderingInstance = null;\nlet currentScopeId = null;\nfunction setCurrentRenderingInstance(instance) {\n  const prev = currentRenderingInstance;\n  currentRenderingInstance = instance;\n  currentScopeId = instance && instance.type.__scopeId || null;\n  return prev;\n}\nfunction pushScopeId(id) {\n  currentScopeId = id;\n}\nfunction popScopeId() {\n  currentScopeId = null;\n}\nconst withScopeId = _id => withCtx;\nfunction withCtx(fn, ctx = currentRenderingInstance, isNonScopedSlot) {\n  if (!ctx) return fn;\n  if (fn._n) {\n    return fn;\n  }\n  const renderFnWithContext = (...args) => {\n    if (renderFnWithContext._d) {\n      setBlockTracking(-1);\n    }\n    const prevInstance = setCurrentRenderingInstance(ctx);\n    let res;\n    try {\n      res = fn(...args);\n    } finally {\n      setCurrentRenderingInstance(prevInstance);\n      if (renderFnWithContext._d) {\n        setBlockTracking(1);\n      }\n    }\n    if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n      devtoolsComponentUpdated(ctx);\n    }\n    return res;\n  };\n  renderFnWithContext._n = true;\n  renderFnWithContext._c = true;\n  renderFnWithContext._d = true;\n  return renderFnWithContext;\n}\nfunction validateDirectiveName(name) {\n  if (isBuiltInDirective(name)) {\n    warn$1(\"Do not use built-in directive ids as custom directive id: \" + name);\n  }\n}\nfunction withDirectives(vnode, directives) {\n  if (currentRenderingInstance === null) {\n    !!(process.env.NODE_ENV !== \"production\") && warn$1(`withDirectives can only be used inside render functions.`);\n    return vnode;\n  }\n  const instance = getComponentPublicInstance(currentRenderingInstance);\n  const bindings = vnode.dirs || (vnode.dirs = []);\n  for (let i = 0; i < directives.length; i++) {\n    let [dir, value, arg, modifiers = EMPTY_OBJ] = directives[i];\n    if (dir) {\n      if (isFunction(dir)) {\n        dir = {\n          mounted: dir,\n          updated: dir\n        };\n      }\n      if (dir.deep) {\n        traverse(value);\n      }\n      bindings.push({\n        dir,\n        instance,\n        value,\n        oldValue: void 0,\n        arg,\n        modifiers\n      });\n    }\n  }\n  return vnode;\n}\nfunction invokeDirectiveHook(vnode, prevVNode, instance, name) {\n  const bindings = vnode.dirs;\n  const oldBindings = prevVNode && prevVNode.dirs;\n  for (let i = 0; i < bindings.length; i++) {\n    const binding = bindings[i];\n    if (oldBindings) {\n      binding.oldValue = oldBindings[i].value;\n    }\n    let hook = binding.dir[name];\n    if (hook) {\n      pauseTracking();\n      callWithAsyncErrorHandling(hook, instance, 8, [vnode.el, binding, vnode, prevVNode]);\n      resetTracking();\n    }\n  }\n}\nconst TeleportEndKey = Symbol(\"_vte\");\nconst isTeleport = type => type.__isTeleport;\nconst isTeleportDisabled = props => props && (props.disabled || props.disabled === \"\");\nconst isTeleportDeferred = props => props && (props.defer || props.defer === \"\");\nconst isTargetSVG = target => typeof SVGElement !== \"undefined\" && target instanceof SVGElement;\nconst isTargetMathML = target => typeof MathMLElement === \"function\" && target instanceof MathMLElement;\nconst resolveTarget = (props, select) => {\n  const targetSelector = props && props.to;\n  if (isString(targetSelector)) {\n    if (!select) {\n      !!(process.env.NODE_ENV !== \"production\") && warn$1(`Current renderer does not support string target for Teleports. (missing querySelector renderer option)`);\n      return null;\n    } else {\n      const target = select(targetSelector);\n      if (!!(process.env.NODE_ENV !== \"production\") && !target && !isTeleportDisabled(props)) {\n        warn$1(`Failed to locate Teleport target with selector \"${targetSelector}\". Note the target element must exist before the component is mounted - i.e. the target cannot be rendered by the component itself, and ideally should be outside of the entire Vue component tree.`);\n      }\n      return target;\n    }\n  } else {\n    if (!!(process.env.NODE_ENV !== \"production\") && !targetSelector && !isTeleportDisabled(props)) {\n      warn$1(`Invalid Teleport target: ${targetSelector}`);\n    }\n    return targetSelector;\n  }\n};\nconst TeleportImpl = {\n  name: \"Teleport\",\n  __isTeleport: true,\n  process(n1, n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized, internals) {\n    const {\n      mc: mountChildren,\n      pc: patchChildren,\n      pbc: patchBlockChildren,\n      o: {\n        insert,\n        querySelector,\n        createText,\n        createComment\n      }\n    } = internals;\n    const disabled = isTeleportDisabled(n2.props);\n    let {\n      shapeFlag,\n      children,\n      dynamicChildren\n    } = n2;\n    if (!!(process.env.NODE_ENV !== \"production\") && isHmrUpdating) {\n      optimized = false;\n      dynamicChildren = null;\n    }\n    if (n1 == null) {\n      const placeholder = n2.el = !!(process.env.NODE_ENV !== \"production\") ? createComment(\"teleport start\") : createText(\"\");\n      const mainAnchor = n2.anchor = !!(process.env.NODE_ENV !== \"production\") ? createComment(\"teleport end\") : createText(\"\");\n      insert(placeholder, container, anchor);\n      insert(mainAnchor, container, anchor);\n      const mount = (container2, anchor2) => {\n        if (shapeFlag & 16) {\n          if (parentComponent && parentComponent.isCE) {\n            parentComponent.ce._teleportTarget = container2;\n          }\n          mountChildren(children, container2, anchor2, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n        }\n      };\n      const mountToTarget = () => {\n        const target = n2.target = resolveTarget(n2.props, querySelector);\n        const targetAnchor = prepareAnchor(target, n2, createText, insert);\n        if (target) {\n          if (namespace !== \"svg\" && isTargetSVG(target)) {\n            namespace = \"svg\";\n          } else if (namespace !== \"mathml\" && isTargetMathML(target)) {\n            namespace = \"mathml\";\n          }\n          if (!disabled) {\n            mount(target, targetAnchor);\n            updateCssVars(n2, false);\n          }\n        } else if (!!(process.env.NODE_ENV !== \"production\") && !disabled) {\n          warn$1(\"Invalid Teleport target on mount:\", target, `(${typeof target})`);\n        }\n      };\n      if (disabled) {\n        mount(container, mainAnchor);\n        updateCssVars(n2, true);\n      }\n      if (isTeleportDeferred(n2.props)) {\n        queuePostRenderEffect(() => {\n          mountToTarget();\n          n2.el.__isMounted = true;\n        }, parentSuspense);\n      } else {\n        mountToTarget();\n      }\n    } else {\n      if (isTeleportDeferred(n2.props) && !n1.el.__isMounted) {\n        queuePostRenderEffect(() => {\n          TeleportImpl.process(n1, n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized, internals);\n          delete n1.el.__isMounted;\n        }, parentSuspense);\n        return;\n      }\n      n2.el = n1.el;\n      n2.targetStart = n1.targetStart;\n      const mainAnchor = n2.anchor = n1.anchor;\n      const target = n2.target = n1.target;\n      const targetAnchor = n2.targetAnchor = n1.targetAnchor;\n      const wasDisabled = isTeleportDisabled(n1.props);\n      const currentContainer = wasDisabled ? container : target;\n      const currentAnchor = wasDisabled ? mainAnchor : targetAnchor;\n      if (namespace === \"svg\" || isTargetSVG(target)) {\n        namespace = \"svg\";\n      } else if (namespace === \"mathml\" || isTargetMathML(target)) {\n        namespace = \"mathml\";\n      }\n      if (dynamicChildren) {\n        patchBlockChildren(n1.dynamicChildren, dynamicChildren, currentContainer, parentComponent, parentSuspense, namespace, slotScopeIds);\n        traverseStaticChildren(n1, n2, true);\n      } else if (!optimized) {\n        patchChildren(n1, n2, currentContainer, currentAnchor, parentComponent, parentSuspense, namespace, slotScopeIds, false);\n      }\n      if (disabled) {\n        if (!wasDisabled) {\n          moveTeleport(n2, container, mainAnchor, internals, 1);\n        } else {\n          if (n2.props && n1.props && n2.props.to !== n1.props.to) {\n            n2.props.to = n1.props.to;\n          }\n        }\n      } else {\n        if ((n2.props && n2.props.to) !== (n1.props && n1.props.to)) {\n          const nextTarget = n2.target = resolveTarget(n2.props, querySelector);\n          if (nextTarget) {\n            moveTeleport(n2, nextTarget, null, internals, 0);\n          } else if (!!(process.env.NODE_ENV !== \"production\")) {\n            warn$1(\"Invalid Teleport target on update:\", target, `(${typeof target})`);\n          }\n        } else if (wasDisabled) {\n          moveTeleport(n2, target, targetAnchor, internals, 1);\n        }\n      }\n      updateCssVars(n2, disabled);\n    }\n  },\n  remove(vnode, parentComponent, parentSuspense, {\n    um: unmount,\n    o: {\n      remove: hostRemove\n    }\n  }, doRemove) {\n    const {\n      shapeFlag,\n      children,\n      anchor,\n      targetStart,\n      targetAnchor,\n      target,\n      props\n    } = vnode;\n    if (target) {\n      hostRemove(targetStart);\n      hostRemove(targetAnchor);\n    }\n    doRemove && hostRemove(anchor);\n    if (shapeFlag & 16) {\n      const shouldRemove = doRemove || !isTeleportDisabled(props);\n      for (let i = 0; i < children.length; i++) {\n        const child = children[i];\n        unmount(child, parentComponent, parentSuspense, shouldRemove, !!child.dynamicChildren);\n      }\n    }\n  },\n  move: moveTeleport,\n  hydrate: hydrateTeleport\n};\nfunction moveTeleport(vnode, container, parentAnchor, {\n  o: {\n    insert\n  },\n  m: move\n}, moveType = 2) {\n  if (moveType === 0) {\n    insert(vnode.targetAnchor, container, parentAnchor);\n  }\n  const {\n    el,\n    anchor,\n    shapeFlag,\n    children,\n    props\n  } = vnode;\n  const isReorder = moveType === 2;\n  if (isReorder) {\n    insert(el, container, parentAnchor);\n  }\n  if (!isReorder || isTeleportDisabled(props)) {\n    if (shapeFlag & 16) {\n      for (let i = 0; i < children.length; i++) {\n        move(children[i], container, parentAnchor, 2);\n      }\n    }\n  }\n  if (isReorder) {\n    insert(anchor, container, parentAnchor);\n  }\n}\nfunction hydrateTeleport(node, vnode, parentComponent, parentSuspense, slotScopeIds, optimized, {\n  o: {\n    nextSibling,\n    parentNode,\n    querySelector,\n    insert,\n    createText\n  }\n}, hydrateChildren) {\n  const target = vnode.target = resolveTarget(vnode.props, querySelector);\n  if (target) {\n    const disabled = isTeleportDisabled(vnode.props);\n    const targetNode = target._lpa || target.firstChild;\n    if (vnode.shapeFlag & 16) {\n      if (disabled) {\n        vnode.anchor = hydrateChildren(nextSibling(node), vnode, parentNode(node), parentComponent, parentSuspense, slotScopeIds, optimized);\n        vnode.targetStart = targetNode;\n        vnode.targetAnchor = targetNode && nextSibling(targetNode);\n      } else {\n        vnode.anchor = nextSibling(node);\n        let targetAnchor = targetNode;\n        while (targetAnchor) {\n          if (targetAnchor && targetAnchor.nodeType === 8) {\n            if (targetAnchor.data === \"teleport start anchor\") {\n              vnode.targetStart = targetAnchor;\n            } else if (targetAnchor.data === \"teleport anchor\") {\n              vnode.targetAnchor = targetAnchor;\n              target._lpa = vnode.targetAnchor && nextSibling(vnode.targetAnchor);\n              break;\n            }\n          }\n          targetAnchor = nextSibling(targetAnchor);\n        }\n        if (!vnode.targetAnchor) {\n          prepareAnchor(target, vnode, createText, insert);\n        }\n        hydrateChildren(targetNode && nextSibling(targetNode), vnode, target, parentComponent, parentSuspense, slotScopeIds, optimized);\n      }\n    }\n    updateCssVars(vnode, disabled);\n  }\n  return vnode.anchor && nextSibling(vnode.anchor);\n}\nconst Teleport = TeleportImpl;\nfunction updateCssVars(vnode, isDisabled) {\n  const ctx = vnode.ctx;\n  if (ctx && ctx.ut) {\n    let node, anchor;\n    if (isDisabled) {\n      node = vnode.el;\n      anchor = vnode.anchor;\n    } else {\n      node = vnode.targetStart;\n      anchor = vnode.targetAnchor;\n    }\n    while (node && node !== anchor) {\n      if (node.nodeType === 1) node.setAttribute(\"data-v-owner\", ctx.uid);\n      node = node.nextSibling;\n    }\n    ctx.ut();\n  }\n}\nfunction prepareAnchor(target, vnode, createText, insert) {\n  const targetStart = vnode.targetStart = createText(\"\");\n  const targetAnchor = vnode.targetAnchor = createText(\"\");\n  targetStart[TeleportEndKey] = targetAnchor;\n  if (target) {\n    insert(targetStart, target);\n    insert(targetAnchor, target);\n  }\n  return targetAnchor;\n}\nconst leaveCbKey = Symbol(\"_leaveCb\");\nconst enterCbKey = Symbol(\"_enterCb\");\nfunction useTransitionState() {\n  const state = {\n    isMounted: false,\n    isLeaving: false,\n    isUnmounting: false,\n    leavingVNodes: /* @__PURE__ */new Map()\n  };\n  onMounted(() => {\n    state.isMounted = true;\n  });\n  onBeforeUnmount(() => {\n    state.isUnmounting = true;\n  });\n  return state;\n}\nconst TransitionHookValidator = [Function, Array];\nconst BaseTransitionPropsValidators = {\n  mode: String,\n  appear: Boolean,\n  persisted: Boolean,\n  // enter\n  onBeforeEnter: TransitionHookValidator,\n  onEnter: TransitionHookValidator,\n  onAfterEnter: TransitionHookValidator,\n  onEnterCancelled: TransitionHookValidator,\n  // leave\n  onBeforeLeave: TransitionHookValidator,\n  onLeave: TransitionHookValidator,\n  onAfterLeave: TransitionHookValidator,\n  onLeaveCancelled: TransitionHookValidator,\n  // appear\n  onBeforeAppear: TransitionHookValidator,\n  onAppear: TransitionHookValidator,\n  onAfterAppear: TransitionHookValidator,\n  onAppearCancelled: TransitionHookValidator\n};\nconst recursiveGetSubtree = instance => {\n  const subTree = instance.subTree;\n  return subTree.component ? recursiveGetSubtree(subTree.component) : subTree;\n};\nconst BaseTransitionImpl = {\n  name: `BaseTransition`,\n  props: BaseTransitionPropsValidators,\n  setup(props, {\n    slots\n  }) {\n    const instance = getCurrentInstance();\n    const state = useTransitionState();\n    return () => {\n      const children = slots.default && getTransitionRawChildren(slots.default(), true);\n      if (!children || !children.length) {\n        return;\n      }\n      const child = findNonCommentChild(children);\n      const rawProps = toRaw(props);\n      const {\n        mode\n      } = rawProps;\n      if (!!(process.env.NODE_ENV !== \"production\") && mode && mode !== \"in-out\" && mode !== \"out-in\" && mode !== \"default\") {\n        warn$1(`invalid <transition> mode: ${mode}`);\n      }\n      if (state.isLeaving) {\n        return emptyPlaceholder(child);\n      }\n      const innerChild = getInnerChild$1(child);\n      if (!innerChild) {\n        return emptyPlaceholder(child);\n      }\n      let enterHooks = resolveTransitionHooks(innerChild, rawProps, state, instance,\n      // #11061, ensure enterHooks is fresh after clone\n      hooks => enterHooks = hooks);\n      if (innerChild.type !== Comment) {\n        setTransitionHooks(innerChild, enterHooks);\n      }\n      let oldInnerChild = instance.subTree && getInnerChild$1(instance.subTree);\n      if (oldInnerChild && oldInnerChild.type !== Comment && !isSameVNodeType(innerChild, oldInnerChild) && recursiveGetSubtree(instance).type !== Comment) {\n        let leavingHooks = resolveTransitionHooks(oldInnerChild, rawProps, state, instance);\n        setTransitionHooks(oldInnerChild, leavingHooks);\n        if (mode === \"out-in\" && innerChild.type !== Comment) {\n          state.isLeaving = true;\n          leavingHooks.afterLeave = () => {\n            state.isLeaving = false;\n            if (!(instance.job.flags & 8)) {\n              instance.update();\n            }\n            delete leavingHooks.afterLeave;\n            oldInnerChild = void 0;\n          };\n          return emptyPlaceholder(child);\n        } else if (mode === \"in-out\" && innerChild.type !== Comment) {\n          leavingHooks.delayLeave = (el, earlyRemove, delayedLeave) => {\n            const leavingVNodesCache = getLeavingNodesForType(state, oldInnerChild);\n            leavingVNodesCache[String(oldInnerChild.key)] = oldInnerChild;\n            el[leaveCbKey] = () => {\n              earlyRemove();\n              el[leaveCbKey] = void 0;\n              delete enterHooks.delayedLeave;\n              oldInnerChild = void 0;\n            };\n            enterHooks.delayedLeave = () => {\n              delayedLeave();\n              delete enterHooks.delayedLeave;\n              oldInnerChild = void 0;\n            };\n          };\n        } else {\n          oldInnerChild = void 0;\n        }\n      } else if (oldInnerChild) {\n        oldInnerChild = void 0;\n      }\n      return child;\n    };\n  }\n};\nfunction findNonCommentChild(children) {\n  let child = children[0];\n  if (children.length > 1) {\n    let hasFound = false;\n    for (const c of children) {\n      if (c.type !== Comment) {\n        if (!!(process.env.NODE_ENV !== \"production\") && hasFound) {\n          warn$1(\"<transition> can only be used on a single element or component. Use <transition-group> for lists.\");\n          break;\n        }\n        child = c;\n        hasFound = true;\n        if (!!!(process.env.NODE_ENV !== \"production\")) break;\n      }\n    }\n  }\n  return child;\n}\nconst BaseTransition = BaseTransitionImpl;\nfunction getLeavingNodesForType(state, vnode) {\n  const {\n    leavingVNodes\n  } = state;\n  let leavingVNodesCache = leavingVNodes.get(vnode.type);\n  if (!leavingVNodesCache) {\n    leavingVNodesCache = /* @__PURE__ */Object.create(null);\n    leavingVNodes.set(vnode.type, leavingVNodesCache);\n  }\n  return leavingVNodesCache;\n}\nfunction resolveTransitionHooks(vnode, props, state, instance, postClone) {\n  const {\n    appear,\n    mode,\n    persisted = false,\n    onBeforeEnter,\n    onEnter,\n    onAfterEnter,\n    onEnterCancelled,\n    onBeforeLeave,\n    onLeave,\n    onAfterLeave,\n    onLeaveCancelled,\n    onBeforeAppear,\n    onAppear,\n    onAfterAppear,\n    onAppearCancelled\n  } = props;\n  const key = String(vnode.key);\n  const leavingVNodesCache = getLeavingNodesForType(state, vnode);\n  const callHook = (hook, args) => {\n    hook && callWithAsyncErrorHandling(hook, instance, 9, args);\n  };\n  const callAsyncHook = (hook, args) => {\n    const done = args[1];\n    callHook(hook, args);\n    if (isArray(hook)) {\n      if (hook.every(hook2 => hook2.length <= 1)) done();\n    } else if (hook.length <= 1) {\n      done();\n    }\n  };\n  const hooks = {\n    mode,\n    persisted,\n    beforeEnter(el) {\n      let hook = onBeforeEnter;\n      if (!state.isMounted) {\n        if (appear) {\n          hook = onBeforeAppear || onBeforeEnter;\n        } else {\n          return;\n        }\n      }\n      if (el[leaveCbKey]) {\n        el[leaveCbKey](true\n        /* cancelled */);\n      }\n      const leavingVNode = leavingVNodesCache[key];\n      if (leavingVNode && isSameVNodeType(vnode, leavingVNode) && leavingVNode.el[leaveCbKey]) {\n        leavingVNode.el[leaveCbKey]();\n      }\n      callHook(hook, [el]);\n    },\n    enter(el) {\n      let hook = onEnter;\n      let afterHook = onAfterEnter;\n      let cancelHook = onEnterCancelled;\n      if (!state.isMounted) {\n        if (appear) {\n          hook = onAppear || onEnter;\n          afterHook = onAfterAppear || onAfterEnter;\n          cancelHook = onAppearCancelled || onEnterCancelled;\n        } else {\n          return;\n        }\n      }\n      let called = false;\n      const done = el[enterCbKey] = cancelled => {\n        if (called) return;\n        called = true;\n        if (cancelled) {\n          callHook(cancelHook, [el]);\n        } else {\n          callHook(afterHook, [el]);\n        }\n        if (hooks.delayedLeave) {\n          hooks.delayedLeave();\n        }\n        el[enterCbKey] = void 0;\n      };\n      if (hook) {\n        callAsyncHook(hook, [el, done]);\n      } else {\n        done();\n      }\n    },\n    leave(el, remove) {\n      const key2 = String(vnode.key);\n      if (el[enterCbKey]) {\n        el[enterCbKey](true\n        /* cancelled */);\n      }\n      if (state.isUnmounting) {\n        return remove();\n      }\n      callHook(onBeforeLeave, [el]);\n      let called = false;\n      const done = el[leaveCbKey] = cancelled => {\n        if (called) return;\n        called = true;\n        remove();\n        if (cancelled) {\n          callHook(onLeaveCancelled, [el]);\n        } else {\n          callHook(onAfterLeave, [el]);\n        }\n        el[leaveCbKey] = void 0;\n        if (leavingVNodesCache[key2] === vnode) {\n          delete leavingVNodesCache[key2];\n        }\n      };\n      leavingVNodesCache[key2] = vnode;\n      if (onLeave) {\n        callAsyncHook(onLeave, [el, done]);\n      } else {\n        done();\n      }\n    },\n    clone(vnode2) {\n      const hooks2 = resolveTransitionHooks(vnode2, props, state, instance, postClone);\n      if (postClone) postClone(hooks2);\n      return hooks2;\n    }\n  };\n  return hooks;\n}\nfunction emptyPlaceholder(vnode) {\n  if (isKeepAlive(vnode)) {\n    vnode = cloneVNode(vnode);\n    vnode.children = null;\n    return vnode;\n  }\n}\nfunction getInnerChild$1(vnode) {\n  if (!isKeepAlive(vnode)) {\n    if (isTeleport(vnode.type) && vnode.children) {\n      return findNonCommentChild(vnode.children);\n    }\n    return vnode;\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") && vnode.component) {\n    return vnode.component.subTree;\n  }\n  const {\n    shapeFlag,\n    children\n  } = vnode;\n  if (children) {\n    if (shapeFlag & 16) {\n      return children[0];\n    }\n    if (shapeFlag & 32 && isFunction(children.default)) {\n      return children.default();\n    }\n  }\n}\nfunction setTransitionHooks(vnode, hooks) {\n  if (vnode.shapeFlag & 6 && vnode.component) {\n    vnode.transition = hooks;\n    setTransitionHooks(vnode.component.subTree, hooks);\n  } else if (vnode.shapeFlag & 128) {\n    vnode.ssContent.transition = hooks.clone(vnode.ssContent);\n    vnode.ssFallback.transition = hooks.clone(vnode.ssFallback);\n  } else {\n    vnode.transition = hooks;\n  }\n}\nfunction getTransitionRawChildren(children, keepComment = false, parentKey) {\n  let ret = [];\n  let keyedFragmentCount = 0;\n  for (let i = 0; i < children.length; i++) {\n    let child = children[i];\n    const key = parentKey == null ? child.key : String(parentKey) + String(child.key != null ? child.key : i);\n    if (child.type === Fragment) {\n      if (child.patchFlag & 128) keyedFragmentCount++;\n      ret = ret.concat(getTransitionRawChildren(child.children, keepComment, key));\n    } else if (keepComment || child.type !== Comment) {\n      ret.push(key != null ? cloneVNode(child, {\n        key\n      }) : child);\n    }\n  }\n  if (keyedFragmentCount > 1) {\n    for (let i = 0; i < ret.length; i++) {\n      ret[i].patchFlag = -2;\n    }\n  }\n  return ret;\n}\n\n/*! #__NO_SIDE_EFFECTS__ */\n// @__NO_SIDE_EFFECTS__\nfunction defineComponent(options, extraOptions) {\n  return isFunction(options) ?\n  // #8236: extend call and options.name access are considered side-effects\n  // by Rollup, so we have to wrap it in a pure-annotated IIFE.\n  /* @__PURE__ */\n  (() => extend({\n    name: options.name\n  }, extraOptions, {\n    setup: options\n  }))() : options;\n}\nfunction useId() {\n  const i = getCurrentInstance();\n  if (i) {\n    return (i.appContext.config.idPrefix || \"v\") + \"-\" + i.ids[0] + i.ids[1]++;\n  } else if (!!(process.env.NODE_ENV !== \"production\")) {\n    warn$1(`useId() is called when there is no active component instance to be associated with.`);\n  }\n  return \"\";\n}\nfunction markAsyncBoundary(instance) {\n  instance.ids = [instance.ids[0] + instance.ids[2]++ + \"-\", 0, 0];\n}\nconst knownTemplateRefs = /* @__PURE__ */new WeakSet();\nfunction useTemplateRef(key) {\n  const i = getCurrentInstance();\n  const r = shallowRef(null);\n  if (i) {\n    const refs = i.refs === EMPTY_OBJ ? i.refs = {} : i.refs;\n    let desc;\n    if (!!(process.env.NODE_ENV !== \"production\") && (desc = Object.getOwnPropertyDescriptor(refs, key)) && !desc.configurable) {\n      warn$1(`useTemplateRef('${key}') already exists.`);\n    } else {\n      Object.defineProperty(refs, key, {\n        enumerable: true,\n        get: () => r.value,\n        set: val => r.value = val\n      });\n    }\n  } else if (!!(process.env.NODE_ENV !== \"production\")) {\n    warn$1(`useTemplateRef() is called when there is no active component instance to be associated with.`);\n  }\n  const ret = !!(process.env.NODE_ENV !== \"production\") ? readonly(r) : r;\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    knownTemplateRefs.add(ret);\n  }\n  return ret;\n}\nfunction setRef(rawRef, oldRawRef, parentSuspense, vnode, isUnmount = false) {\n  if (isArray(rawRef)) {\n    rawRef.forEach((r, i) => setRef(r, oldRawRef && (isArray(oldRawRef) ? oldRawRef[i] : oldRawRef), parentSuspense, vnode, isUnmount));\n    return;\n  }\n  if (isAsyncWrapper(vnode) && !isUnmount) {\n    if (vnode.shapeFlag & 512 && vnode.type.__asyncResolved && vnode.component.subTree.component) {\n      setRef(rawRef, oldRawRef, parentSuspense, vnode.component.subTree);\n    }\n    return;\n  }\n  const refValue = vnode.shapeFlag & 4 ? getComponentPublicInstance(vnode.component) : vnode.el;\n  const value = isUnmount ? null : refValue;\n  const {\n    i: owner,\n    r: ref\n  } = rawRef;\n  if (!!(process.env.NODE_ENV !== \"production\") && !owner) {\n    warn$1(`Missing ref owner context. ref cannot be used on hoisted vnodes. A vnode with ref must be created inside the render function.`);\n    return;\n  }\n  const oldRef = oldRawRef && oldRawRef.r;\n  const refs = owner.refs === EMPTY_OBJ ? owner.refs = {} : owner.refs;\n  const setupState = owner.setupState;\n  const rawSetupState = toRaw(setupState);\n  const canSetSetupRef = setupState === EMPTY_OBJ ? () => false : key => {\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      if (hasOwn(rawSetupState, key) && !isRef(rawSetupState[key])) {\n        warn$1(`Template ref \"${key}\" used on a non-ref value. It will not work in the production build.`);\n      }\n      if (knownTemplateRefs.has(rawSetupState[key])) {\n        return false;\n      }\n    }\n    return hasOwn(rawSetupState, key);\n  };\n  if (oldRef != null && oldRef !== ref) {\n    if (isString(oldRef)) {\n      refs[oldRef] = null;\n      if (canSetSetupRef(oldRef)) {\n        setupState[oldRef] = null;\n      }\n    } else if (isRef(oldRef)) {\n      oldRef.value = null;\n    }\n  }\n  if (isFunction(ref)) {\n    callWithErrorHandling(ref, owner, 12, [value, refs]);\n  } else {\n    const _isString = isString(ref);\n    const _isRef = isRef(ref);\n    if (_isString || _isRef) {\n      const doSet = () => {\n        if (rawRef.f) {\n          const existing = _isString ? canSetSetupRef(ref) ? setupState[ref] : refs[ref] : ref.value;\n          if (isUnmount) {\n            isArray(existing) && remove(existing, refValue);\n          } else {\n            if (!isArray(existing)) {\n              if (_isString) {\n                refs[ref] = [refValue];\n                if (canSetSetupRef(ref)) {\n                  setupState[ref] = refs[ref];\n                }\n              } else {\n                ref.value = [refValue];\n                if (rawRef.k) refs[rawRef.k] = ref.value;\n              }\n            } else if (!existing.includes(refValue)) {\n              existing.push(refValue);\n            }\n          }\n        } else if (_isString) {\n          refs[ref] = value;\n          if (canSetSetupRef(ref)) {\n            setupState[ref] = value;\n          }\n        } else if (_isRef) {\n          ref.value = value;\n          if (rawRef.k) refs[rawRef.k] = value;\n        } else if (!!(process.env.NODE_ENV !== \"production\")) {\n          warn$1(\"Invalid template ref type:\", ref, `(${typeof ref})`);\n        }\n      };\n      if (value) {\n        doSet.id = -1;\n        queuePostRenderEffect(doSet, parentSuspense);\n      } else {\n        doSet();\n      }\n    } else if (!!(process.env.NODE_ENV !== \"production\")) {\n      warn$1(\"Invalid template ref type:\", ref, `(${typeof ref})`);\n    }\n  }\n}\nlet hasLoggedMismatchError = false;\nconst logMismatchError = () => {\n  if (hasLoggedMismatchError) {\n    return;\n  }\n  console.error(\"Hydration completed but contains mismatches.\");\n  hasLoggedMismatchError = true;\n};\nconst isSVGContainer = container => container.namespaceURI.includes(\"svg\") && container.tagName !== \"foreignObject\";\nconst isMathMLContainer = container => container.namespaceURI.includes(\"MathML\");\nconst getContainerType = container => {\n  if (container.nodeType !== 1) return void 0;\n  if (isSVGContainer(container)) return \"svg\";\n  if (isMathMLContainer(container)) return \"mathml\";\n  return void 0;\n};\nconst isComment = node => node.nodeType === 8;\nfunction createHydrationFunctions(rendererInternals) {\n  const {\n    mt: mountComponent,\n    p: patch,\n    o: {\n      patchProp,\n      createText,\n      nextSibling,\n      parentNode,\n      remove,\n      insert,\n      createComment\n    }\n  } = rendererInternals;\n  const hydrate = (vnode, container) => {\n    if (!container.hasChildNodes()) {\n      (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_HYDRATION_MISMATCH_DETAILS__) && warn$1(`Attempting to hydrate existing markup but container is empty. Performing full mount instead.`);\n      patch(null, vnode, container);\n      flushPostFlushCbs();\n      container._vnode = vnode;\n      return;\n    }\n    hydrateNode(container.firstChild, vnode, null, null, null);\n    flushPostFlushCbs();\n    container._vnode = vnode;\n  };\n  const hydrateNode = (node, vnode, parentComponent, parentSuspense, slotScopeIds, optimized = false) => {\n    optimized = optimized || !!vnode.dynamicChildren;\n    const isFragmentStart = isComment(node) && node.data === \"[\";\n    const onMismatch = () => handleMismatch(node, vnode, parentComponent, parentSuspense, slotScopeIds, isFragmentStart);\n    const {\n      type,\n      ref,\n      shapeFlag,\n      patchFlag\n    } = vnode;\n    let domType = node.nodeType;\n    vnode.el = node;\n    if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n      def(node, \"__vnode\", vnode, true);\n      def(node, \"__vueParentComponent\", parentComponent, true);\n    }\n    if (patchFlag === -2) {\n      optimized = false;\n      vnode.dynamicChildren = null;\n    }\n    let nextNode = null;\n    switch (type) {\n      case Text:\n        if (domType !== 3) {\n          if (vnode.children === \"\") {\n            insert(vnode.el = createText(\"\"), parentNode(node), node);\n            nextNode = node;\n          } else {\n            nextNode = onMismatch();\n          }\n        } else {\n          if (node.data !== vnode.children) {\n            (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_HYDRATION_MISMATCH_DETAILS__) && warn$1(`Hydration text mismatch in`, node.parentNode, `\n  - rendered on server: ${JSON.stringify(node.data)}\n  - expected on client: ${JSON.stringify(vnode.children)}`);\n            logMismatchError();\n            node.data = vnode.children;\n          }\n          nextNode = nextSibling(node);\n        }\n        break;\n      case Comment:\n        if (isTemplateNode(node)) {\n          nextNode = nextSibling(node);\n          replaceNode(vnode.el = node.content.firstChild, node, parentComponent);\n        } else if (domType !== 8 || isFragmentStart) {\n          nextNode = onMismatch();\n        } else {\n          nextNode = nextSibling(node);\n        }\n        break;\n      case Static:\n        if (isFragmentStart) {\n          node = nextSibling(node);\n          domType = node.nodeType;\n        }\n        if (domType === 1 || domType === 3) {\n          nextNode = node;\n          const needToAdoptContent = !vnode.children.length;\n          for (let i = 0; i < vnode.staticCount; i++) {\n            if (needToAdoptContent) vnode.children += nextNode.nodeType === 1 ? nextNode.outerHTML : nextNode.data;\n            if (i === vnode.staticCount - 1) {\n              vnode.anchor = nextNode;\n            }\n            nextNode = nextSibling(nextNode);\n          }\n          return isFragmentStart ? nextSibling(nextNode) : nextNode;\n        } else {\n          onMismatch();\n        }\n        break;\n      case Fragment:\n        if (!isFragmentStart) {\n          nextNode = onMismatch();\n        } else {\n          nextNode = hydrateFragment(node, vnode, parentComponent, parentSuspense, slotScopeIds, optimized);\n        }\n        break;\n      default:\n        if (shapeFlag & 1) {\n          if ((domType !== 1 || vnode.type.toLowerCase() !== node.tagName.toLowerCase()) && !isTemplateNode(node)) {\n            nextNode = onMismatch();\n          } else {\n            nextNode = hydrateElement(node, vnode, parentComponent, parentSuspense, slotScopeIds, optimized);\n          }\n        } else if (shapeFlag & 6) {\n          vnode.slotScopeIds = slotScopeIds;\n          const container = parentNode(node);\n          if (isFragmentStart) {\n            nextNode = locateClosingAnchor(node);\n          } else if (isComment(node) && node.data === \"teleport start\") {\n            nextNode = locateClosingAnchor(node, node.data, \"teleport end\");\n          } else {\n            nextNode = nextSibling(node);\n          }\n          mountComponent(vnode, container, null, parentComponent, parentSuspense, getContainerType(container), optimized);\n          if (isAsyncWrapper(vnode) && !vnode.type.__asyncResolved) {\n            let subTree;\n            if (isFragmentStart) {\n              subTree = createVNode(Fragment);\n              subTree.anchor = nextNode ? nextNode.previousSibling : container.lastChild;\n            } else {\n              subTree = node.nodeType === 3 ? createTextVNode(\"\") : createVNode(\"div\");\n            }\n            subTree.el = node;\n            vnode.component.subTree = subTree;\n          }\n        } else if (shapeFlag & 64) {\n          if (domType !== 8) {\n            nextNode = onMismatch();\n          } else {\n            nextNode = vnode.type.hydrate(node, vnode, parentComponent, parentSuspense, slotScopeIds, optimized, rendererInternals, hydrateChildren);\n          }\n        } else if (shapeFlag & 128) {\n          nextNode = vnode.type.hydrate(node, vnode, parentComponent, parentSuspense, getContainerType(parentNode(node)), slotScopeIds, optimized, rendererInternals, hydrateNode);\n        } else if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_HYDRATION_MISMATCH_DETAILS__) {\n          warn$1(\"Invalid HostVNode type:\", type, `(${typeof type})`);\n        }\n    }\n    if (ref != null) {\n      setRef(ref, null, parentSuspense, vnode);\n    }\n    return nextNode;\n  };\n  const hydrateElement = (el, vnode, parentComponent, parentSuspense, slotScopeIds, optimized) => {\n    optimized = optimized || !!vnode.dynamicChildren;\n    const {\n      type,\n      props,\n      patchFlag,\n      shapeFlag,\n      dirs,\n      transition\n    } = vnode;\n    const forcePatch = type === \"input\" || type === \"option\";\n    if (!!(process.env.NODE_ENV !== \"production\") || forcePatch || patchFlag !== -1) {\n      if (dirs) {\n        invokeDirectiveHook(vnode, null, parentComponent, \"created\");\n      }\n      let needCallTransitionHooks = false;\n      if (isTemplateNode(el)) {\n        needCallTransitionHooks = needTransition(null,\n        // no need check parentSuspense in hydration\n        transition) && parentComponent && parentComponent.vnode.props && parentComponent.vnode.props.appear;\n        const content = el.content.firstChild;\n        if (needCallTransitionHooks) {\n          transition.beforeEnter(content);\n        }\n        replaceNode(content, el, parentComponent);\n        vnode.el = el = content;\n      }\n      if (shapeFlag & 16 &&\n      // skip if element has innerHTML / textContent\n      !(props && (props.innerHTML || props.textContent))) {\n        let next = hydrateChildren(el.firstChild, vnode, el, parentComponent, parentSuspense, slotScopeIds, optimized);\n        let hasWarned = false;\n        while (next) {\n          if (!isMismatchAllowed(el, 1 /* CHILDREN */)) {\n            if ((!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_HYDRATION_MISMATCH_DETAILS__) && !hasWarned) {\n              warn$1(`Hydration children mismatch on`, el, `\nServer rendered element contains more child nodes than client vdom.`);\n              hasWarned = true;\n            }\n            logMismatchError();\n          }\n          const cur = next;\n          next = next.nextSibling;\n          remove(cur);\n        }\n      } else if (shapeFlag & 8) {\n        let clientText = vnode.children;\n        if (clientText[0] === \"\\n\" && (el.tagName === \"PRE\" || el.tagName === \"TEXTAREA\")) {\n          clientText = clientText.slice(1);\n        }\n        if (el.textContent !== clientText) {\n          if (!isMismatchAllowed(el, 0 /* TEXT */)) {\n            (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_HYDRATION_MISMATCH_DETAILS__) && warn$1(`Hydration text content mismatch on`, el, `\n  - rendered on server: ${el.textContent}\n  - expected on client: ${vnode.children}`);\n            logMismatchError();\n          }\n          el.textContent = vnode.children;\n        }\n      }\n      if (props) {\n        if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_HYDRATION_MISMATCH_DETAILS__ || forcePatch || !optimized || patchFlag & (16 | 32)) {\n          const isCustomElement = el.tagName.includes(\"-\");\n          for (const key in props) {\n            if ((!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_HYDRATION_MISMATCH_DETAILS__) &&\n            // #11189 skip if this node has directives that have created hooks\n            // as it could have mutated the DOM in any possible way\n            !(dirs && dirs.some(d => d.dir.created)) && propHasMismatch(el, key, props[key], vnode, parentComponent)) {\n              logMismatchError();\n            }\n            if (forcePatch && (key.endsWith(\"value\") || key === \"indeterminate\") || isOn(key) && !isReservedProp(key) ||\n            // force hydrate v-bind with .prop modifiers\n            key[0] === \".\" || isCustomElement) {\n              patchProp(el, key, null, props[key], void 0, parentComponent);\n            }\n          }\n        } else if (props.onClick) {\n          patchProp(el, \"onClick\", null, props.onClick, void 0, parentComponent);\n        } else if (patchFlag & 4 && isReactive(props.style)) {\n          for (const key in props.style) props.style[key];\n        }\n      }\n      let vnodeHooks;\n      if (vnodeHooks = props && props.onVnodeBeforeMount) {\n        invokeVNodeHook(vnodeHooks, parentComponent, vnode);\n      }\n      if (dirs) {\n        invokeDirectiveHook(vnode, null, parentComponent, \"beforeMount\");\n      }\n      if ((vnodeHooks = props && props.onVnodeMounted) || dirs || needCallTransitionHooks) {\n        queueEffectWithSuspense(() => {\n          vnodeHooks && invokeVNodeHook(vnodeHooks, parentComponent, vnode);\n          needCallTransitionHooks && transition.enter(el);\n          dirs && invokeDirectiveHook(vnode, null, parentComponent, \"mounted\");\n        }, parentSuspense);\n      }\n    }\n    return el.nextSibling;\n  };\n  const hydrateChildren = (node, parentVNode, container, parentComponent, parentSuspense, slotScopeIds, optimized) => {\n    optimized = optimized || !!parentVNode.dynamicChildren;\n    const children = parentVNode.children;\n    const l = children.length;\n    let hasWarned = false;\n    for (let i = 0; i < l; i++) {\n      const vnode = optimized ? children[i] : children[i] = normalizeVNode(children[i]);\n      const isText = vnode.type === Text;\n      if (node) {\n        if (isText && !optimized) {\n          if (i + 1 < l && normalizeVNode(children[i + 1]).type === Text) {\n            insert(createText(node.data.slice(vnode.children.length)), container, nextSibling(node));\n            node.data = vnode.children;\n          }\n        }\n        node = hydrateNode(node, vnode, parentComponent, parentSuspense, slotScopeIds, optimized);\n      } else if (isText && !vnode.children) {\n        insert(vnode.el = createText(\"\"), container);\n      } else {\n        if (!isMismatchAllowed(container, 1 /* CHILDREN */)) {\n          if ((!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_HYDRATION_MISMATCH_DETAILS__) && !hasWarned) {\n            warn$1(`Hydration children mismatch on`, container, `\nServer rendered element contains fewer child nodes than client vdom.`);\n            hasWarned = true;\n          }\n          logMismatchError();\n        }\n        patch(null, vnode, container, null, parentComponent, parentSuspense, getContainerType(container), slotScopeIds);\n      }\n    }\n    return node;\n  };\n  const hydrateFragment = (node, vnode, parentComponent, parentSuspense, slotScopeIds, optimized) => {\n    const {\n      slotScopeIds: fragmentSlotScopeIds\n    } = vnode;\n    if (fragmentSlotScopeIds) {\n      slotScopeIds = slotScopeIds ? slotScopeIds.concat(fragmentSlotScopeIds) : fragmentSlotScopeIds;\n    }\n    const container = parentNode(node);\n    const next = hydrateChildren(nextSibling(node), vnode, container, parentComponent, parentSuspense, slotScopeIds, optimized);\n    if (next && isComment(next) && next.data === \"]\") {\n      return nextSibling(vnode.anchor = next);\n    } else {\n      logMismatchError();\n      insert(vnode.anchor = createComment(`]`), container, next);\n      return next;\n    }\n  };\n  const handleMismatch = (node, vnode, parentComponent, parentSuspense, slotScopeIds, isFragment) => {\n    if (!isMismatchAllowed(node.parentElement, 1 /* CHILDREN */)) {\n      (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_HYDRATION_MISMATCH_DETAILS__) && warn$1(`Hydration node mismatch:\n- rendered on server:`, node, node.nodeType === 3 ? `(text)` : isComment(node) && node.data === \"[\" ? `(start of fragment)` : ``, `\n- expected on client:`, vnode.type);\n      logMismatchError();\n    }\n    vnode.el = null;\n    if (isFragment) {\n      const end = locateClosingAnchor(node);\n      while (true) {\n        const next2 = nextSibling(node);\n        if (next2 && next2 !== end) {\n          remove(next2);\n        } else {\n          break;\n        }\n      }\n    }\n    const next = nextSibling(node);\n    const container = parentNode(node);\n    remove(node);\n    patch(null, vnode, container, next, parentComponent, parentSuspense, getContainerType(container), slotScopeIds);\n    if (parentComponent) {\n      parentComponent.vnode.el = vnode.el;\n      updateHOCHostEl(parentComponent, vnode.el);\n    }\n    return next;\n  };\n  const locateClosingAnchor = (node, open = \"[\", close = \"]\") => {\n    let match = 0;\n    while (node) {\n      node = nextSibling(node);\n      if (node && isComment(node)) {\n        if (node.data === open) match++;\n        if (node.data === close) {\n          if (match === 0) {\n            return nextSibling(node);\n          } else {\n            match--;\n          }\n        }\n      }\n    }\n    return node;\n  };\n  const replaceNode = (newNode, oldNode, parentComponent) => {\n    const parentNode2 = oldNode.parentNode;\n    if (parentNode2) {\n      parentNode2.replaceChild(newNode, oldNode);\n    }\n    let parent = parentComponent;\n    while (parent) {\n      if (parent.vnode.el === oldNode) {\n        parent.vnode.el = parent.subTree.el = newNode;\n      }\n      parent = parent.parent;\n    }\n  };\n  const isTemplateNode = node => {\n    return node.nodeType === 1 && node.tagName === \"TEMPLATE\";\n  };\n  return [hydrate, hydrateNode];\n}\nfunction propHasMismatch(el, key, clientValue, vnode, instance) {\n  let mismatchType;\n  let mismatchKey;\n  let actual;\n  let expected;\n  if (key === \"class\") {\n    actual = el.getAttribute(\"class\");\n    expected = normalizeClass(clientValue);\n    if (!isSetEqual(toClassSet(actual || \"\"), toClassSet(expected))) {\n      mismatchType = 2 /* CLASS */;\n      mismatchKey = `class`;\n    }\n  } else if (key === \"style\") {\n    actual = el.getAttribute(\"style\") || \"\";\n    expected = isString(clientValue) ? clientValue : stringifyStyle(normalizeStyle(clientValue));\n    const actualMap = toStyleMap(actual);\n    const expectedMap = toStyleMap(expected);\n    if (vnode.dirs) {\n      for (const {\n        dir,\n        value\n      } of vnode.dirs) {\n        if (dir.name === \"show\" && !value) {\n          expectedMap.set(\"display\", \"none\");\n        }\n      }\n    }\n    if (instance) {\n      resolveCssVars(instance, vnode, expectedMap);\n    }\n    if (!isMapEqual(actualMap, expectedMap)) {\n      mismatchType = 3 /* STYLE */;\n      mismatchKey = \"style\";\n    }\n  } else if (el instanceof SVGElement && isKnownSvgAttr(key) || el instanceof HTMLElement && (isBooleanAttr(key) || isKnownHtmlAttr(key))) {\n    if (isBooleanAttr(key)) {\n      actual = el.hasAttribute(key);\n      expected = includeBooleanAttr(clientValue);\n    } else if (clientValue == null) {\n      actual = el.hasAttribute(key);\n      expected = false;\n    } else {\n      if (el.hasAttribute(key)) {\n        actual = el.getAttribute(key);\n      } else if (key === \"value\" && el.tagName === \"TEXTAREA\") {\n        actual = el.value;\n      } else {\n        actual = false;\n      }\n      expected = isRenderableAttrValue(clientValue) ? String(clientValue) : false;\n    }\n    if (actual !== expected) {\n      mismatchType = 4 /* ATTRIBUTE */;\n      mismatchKey = key;\n    }\n  }\n  if (mismatchType != null && !isMismatchAllowed(el, mismatchType)) {\n    const format = v => v === false ? `(not rendered)` : `${mismatchKey}=\"${v}\"`;\n    const preSegment = `Hydration ${MismatchTypeString[mismatchType]} mismatch on`;\n    const postSegment = `\n  - rendered on server: ${format(actual)}\n  - expected on client: ${format(expected)}\n  Note: this mismatch is check-only. The DOM will not be rectified in production due to performance overhead.\n  You should fix the source of the mismatch.`;\n    {\n      warn$1(preSegment, el, postSegment);\n    }\n    return true;\n  }\n  return false;\n}\nfunction toClassSet(str) {\n  return new Set(str.trim().split(/\\s+/));\n}\nfunction isSetEqual(a, b) {\n  if (a.size !== b.size) {\n    return false;\n  }\n  for (const s of a) {\n    if (!b.has(s)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction toStyleMap(str) {\n  const styleMap = /* @__PURE__ */new Map();\n  for (const item of str.split(\";\")) {\n    let [key, value] = item.split(\":\");\n    key = key.trim();\n    value = value && value.trim();\n    if (key && value) {\n      styleMap.set(key, value);\n    }\n  }\n  return styleMap;\n}\nfunction isMapEqual(a, b) {\n  if (a.size !== b.size) {\n    return false;\n  }\n  for (const [key, value] of a) {\n    if (value !== b.get(key)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction resolveCssVars(instance, vnode, expectedMap) {\n  const root = instance.subTree;\n  if (instance.getCssVars && (vnode === root || root && root.type === Fragment && root.children.includes(vnode))) {\n    const cssVars = instance.getCssVars();\n    for (const key in cssVars) {\n      expectedMap.set(`--${getEscapedCssVarName(key, false)}`, String(cssVars[key]));\n    }\n  }\n  if (vnode === root && instance.parent) {\n    resolveCssVars(instance.parent, instance.vnode, expectedMap);\n  }\n}\nconst allowMismatchAttr = \"data-allow-mismatch\";\nconst MismatchTypeString = {\n  [0 /* TEXT */]: \"text\",\n  [1 /* CHILDREN */]: \"children\",\n  [2 /* CLASS */]: \"class\",\n  [3 /* STYLE */]: \"style\",\n  [4 /* ATTRIBUTE */]: \"attribute\"\n};\nfunction isMismatchAllowed(el, allowedType) {\n  if (allowedType === 0 /* TEXT */ || allowedType === 1 /* CHILDREN */) {\n    while (el && !el.hasAttribute(allowMismatchAttr)) {\n      el = el.parentElement;\n    }\n  }\n  const allowedAttr = el && el.getAttribute(allowMismatchAttr);\n  if (allowedAttr == null) {\n    return false;\n  } else if (allowedAttr === \"\") {\n    return true;\n  } else {\n    const list = allowedAttr.split(\",\");\n    if (allowedType === 0 /* TEXT */ && list.includes(\"children\")) {\n      return true;\n    }\n    return allowedAttr.split(\",\").includes(MismatchTypeString[allowedType]);\n  }\n}\nconst requestIdleCallback = getGlobalThis().requestIdleCallback || (cb => setTimeout(cb, 1));\nconst cancelIdleCallback = getGlobalThis().cancelIdleCallback || (id => clearTimeout(id));\nconst hydrateOnIdle = (timeout = 1e4) => hydrate => {\n  const id = requestIdleCallback(hydrate, {\n    timeout\n  });\n  return () => cancelIdleCallback(id);\n};\nfunction elementIsVisibleInViewport(el) {\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = el.getBoundingClientRect();\n  const {\n    innerHeight,\n    innerWidth\n  } = window;\n  return (top > 0 && top < innerHeight || bottom > 0 && bottom < innerHeight) && (left > 0 && left < innerWidth || right > 0 && right < innerWidth);\n}\nconst hydrateOnVisible = opts => (hydrate, forEach) => {\n  const ob = new IntersectionObserver(entries => {\n    for (const e of entries) {\n      if (!e.isIntersecting) continue;\n      ob.disconnect();\n      hydrate();\n      break;\n    }\n  }, opts);\n  forEach(el => {\n    if (!(el instanceof Element)) return;\n    if (elementIsVisibleInViewport(el)) {\n      hydrate();\n      ob.disconnect();\n      return false;\n    }\n    ob.observe(el);\n  });\n  return () => ob.disconnect();\n};\nconst hydrateOnMediaQuery = query => hydrate => {\n  if (query) {\n    const mql = matchMedia(query);\n    if (mql.matches) {\n      hydrate();\n    } else {\n      mql.addEventListener(\"change\", hydrate, {\n        once: true\n      });\n      return () => mql.removeEventListener(\"change\", hydrate);\n    }\n  }\n};\nconst hydrateOnInteraction = (interactions = []) => (hydrate, forEach) => {\n  if (isString(interactions)) interactions = [interactions];\n  let hasHydrated = false;\n  const doHydrate = e => {\n    if (!hasHydrated) {\n      hasHydrated = true;\n      teardown();\n      hydrate();\n      e.target.dispatchEvent(new e.constructor(e.type, e));\n    }\n  };\n  const teardown = () => {\n    forEach(el => {\n      for (const i of interactions) {\n        el.removeEventListener(i, doHydrate);\n      }\n    });\n  };\n  forEach(el => {\n    for (const i of interactions) {\n      el.addEventListener(i, doHydrate, {\n        once: true\n      });\n    }\n  });\n  return teardown;\n};\nfunction forEachElement(node, cb) {\n  if (isComment(node) && node.data === \"[\") {\n    let depth = 1;\n    let next = node.nextSibling;\n    while (next) {\n      if (next.nodeType === 1) {\n        const result = cb(next);\n        if (result === false) {\n          break;\n        }\n      } else if (isComment(next)) {\n        if (next.data === \"]\") {\n          if (--depth === 0) break;\n        } else if (next.data === \"[\") {\n          depth++;\n        }\n      }\n      next = next.nextSibling;\n    }\n  } else {\n    cb(node);\n  }\n}\nconst isAsyncWrapper = i => !!i.type.__asyncLoader;\n/*! #__NO_SIDE_EFFECTS__ */\n// @__NO_SIDE_EFFECTS__\nfunction defineAsyncComponent(source) {\n  if (isFunction(source)) {\n    source = {\n      loader: source\n    };\n  }\n  const {\n    loader,\n    loadingComponent,\n    errorComponent,\n    delay = 200,\n    hydrate: hydrateStrategy,\n    timeout,\n    // undefined = never times out\n    suspensible = true,\n    onError: userOnError\n  } = source;\n  let pendingRequest = null;\n  let resolvedComp;\n  let retries = 0;\n  const retry = () => {\n    retries++;\n    pendingRequest = null;\n    return load();\n  };\n  const load = () => {\n    let thisRequest;\n    return pendingRequest || (thisRequest = pendingRequest = loader().catch(err => {\n      err = err instanceof Error ? err : new Error(String(err));\n      if (userOnError) {\n        return new Promise((resolve, reject) => {\n          const userRetry = () => resolve(retry());\n          const userFail = () => reject(err);\n          userOnError(err, userRetry, userFail, retries + 1);\n        });\n      } else {\n        throw err;\n      }\n    }).then(comp => {\n      if (thisRequest !== pendingRequest && pendingRequest) {\n        return pendingRequest;\n      }\n      if (!!(process.env.NODE_ENV !== \"production\") && !comp) {\n        warn$1(`Async component loader resolved to undefined. If you are using retry(), make sure to return its return value.`);\n      }\n      if (comp && (comp.__esModule || comp[Symbol.toStringTag] === \"Module\")) {\n        comp = comp.default;\n      }\n      if (!!(process.env.NODE_ENV !== \"production\") && comp && !isObject(comp) && !isFunction(comp)) {\n        throw new Error(`Invalid async component load result: ${comp}`);\n      }\n      resolvedComp = comp;\n      return comp;\n    }));\n  };\n  return defineComponent({\n    name: \"AsyncComponentWrapper\",\n    __asyncLoader: load,\n    __asyncHydrate(el, instance, hydrate) {\n      const doHydrate = hydrateStrategy ? () => {\n        const teardown = hydrateStrategy(hydrate, cb => forEachElement(el, cb));\n        if (teardown) {\n          (instance.bum || (instance.bum = [])).push(teardown);\n        }\n      } : hydrate;\n      if (resolvedComp) {\n        doHydrate();\n      } else {\n        load().then(() => !instance.isUnmounted && doHydrate());\n      }\n    },\n    get __asyncResolved() {\n      return resolvedComp;\n    },\n    setup() {\n      const instance = currentInstance;\n      markAsyncBoundary(instance);\n      if (resolvedComp) {\n        return () => createInnerComp(resolvedComp, instance);\n      }\n      const onError = err => {\n        pendingRequest = null;\n        handleError(err, instance, 13, !errorComponent);\n      };\n      if (suspensible && instance.suspense || isInSSRComponentSetup) {\n        return load().then(comp => {\n          return () => createInnerComp(comp, instance);\n        }).catch(err => {\n          onError(err);\n          return () => errorComponent ? createVNode(errorComponent, {\n            error: err\n          }) : null;\n        });\n      }\n      const loaded = ref(false);\n      const error = ref();\n      const delayed = ref(!!delay);\n      if (delay) {\n        setTimeout(() => {\n          delayed.value = false;\n        }, delay);\n      }\n      if (timeout != null) {\n        setTimeout(() => {\n          if (!loaded.value && !error.value) {\n            const err = new Error(`Async component timed out after ${timeout}ms.`);\n            onError(err);\n            error.value = err;\n          }\n        }, timeout);\n      }\n      load().then(() => {\n        loaded.value = true;\n        if (instance.parent && isKeepAlive(instance.parent.vnode)) {\n          instance.parent.update();\n        }\n      }).catch(err => {\n        onError(err);\n        error.value = err;\n      });\n      return () => {\n        if (loaded.value && resolvedComp) {\n          return createInnerComp(resolvedComp, instance);\n        } else if (error.value && errorComponent) {\n          return createVNode(errorComponent, {\n            error: error.value\n          });\n        } else if (loadingComponent && !delayed.value) {\n          return createVNode(loadingComponent);\n        }\n      };\n    }\n  });\n}\nfunction createInnerComp(comp, parent) {\n  const {\n    ref: ref2,\n    props,\n    children,\n    ce\n  } = parent.vnode;\n  const vnode = createVNode(comp, props, children);\n  vnode.ref = ref2;\n  vnode.ce = ce;\n  delete parent.vnode.ce;\n  return vnode;\n}\nconst isKeepAlive = vnode => vnode.type.__isKeepAlive;\nconst KeepAliveImpl = {\n  name: `KeepAlive`,\n  // Marker for special handling inside the renderer. We are not using a ===\n  // check directly on KeepAlive in the renderer, because importing it directly\n  // would prevent it from being tree-shaken.\n  __isKeepAlive: true,\n  props: {\n    include: [String, RegExp, Array],\n    exclude: [String, RegExp, Array],\n    max: [String, Number]\n  },\n  setup(props, {\n    slots\n  }) {\n    const instance = getCurrentInstance();\n    const sharedContext = instance.ctx;\n    if (!sharedContext.renderer) {\n      return () => {\n        const children = slots.default && slots.default();\n        return children && children.length === 1 ? children[0] : children;\n      };\n    }\n    const cache = /* @__PURE__ */new Map();\n    const keys = /* @__PURE__ */new Set();\n    let current = null;\n    if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n      instance.__v_cache = cache;\n    }\n    const parentSuspense = instance.suspense;\n    const {\n      renderer: {\n        p: patch,\n        m: move,\n        um: _unmount,\n        o: {\n          createElement\n        }\n      }\n    } = sharedContext;\n    const storageContainer = createElement(\"div\");\n    sharedContext.activate = (vnode, container, anchor, namespace, optimized) => {\n      const instance2 = vnode.component;\n      move(vnode, container, anchor, 0, parentSuspense);\n      patch(instance2.vnode, vnode, container, anchor, instance2, parentSuspense, namespace, vnode.slotScopeIds, optimized);\n      queuePostRenderEffect(() => {\n        instance2.isDeactivated = false;\n        if (instance2.a) {\n          invokeArrayFns(instance2.a);\n        }\n        const vnodeHook = vnode.props && vnode.props.onVnodeMounted;\n        if (vnodeHook) {\n          invokeVNodeHook(vnodeHook, instance2.parent, vnode);\n        }\n      }, parentSuspense);\n      if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n        devtoolsComponentAdded(instance2);\n      }\n    };\n    sharedContext.deactivate = vnode => {\n      const instance2 = vnode.component;\n      invalidateMount(instance2.m);\n      invalidateMount(instance2.a);\n      move(vnode, storageContainer, null, 1, parentSuspense);\n      queuePostRenderEffect(() => {\n        if (instance2.da) {\n          invokeArrayFns(instance2.da);\n        }\n        const vnodeHook = vnode.props && vnode.props.onVnodeUnmounted;\n        if (vnodeHook) {\n          invokeVNodeHook(vnodeHook, instance2.parent, vnode);\n        }\n        instance2.isDeactivated = true;\n      }, parentSuspense);\n      if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n        devtoolsComponentAdded(instance2);\n      }\n    };\n    function unmount(vnode) {\n      resetShapeFlag(vnode);\n      _unmount(vnode, instance, parentSuspense, true);\n    }\n    function pruneCache(filter) {\n      cache.forEach((vnode, key) => {\n        const name = getComponentName(vnode.type);\n        if (name && !filter(name)) {\n          pruneCacheEntry(key);\n        }\n      });\n    }\n    function pruneCacheEntry(key) {\n      const cached = cache.get(key);\n      if (cached && (!current || !isSameVNodeType(cached, current))) {\n        unmount(cached);\n      } else if (current) {\n        resetShapeFlag(current);\n      }\n      cache.delete(key);\n      keys.delete(key);\n    }\n    watch(() => [props.include, props.exclude], ([include, exclude]) => {\n      include && pruneCache(name => matches(include, name));\n      exclude && pruneCache(name => !matches(exclude, name));\n    },\n    // prune post-render after `current` has been updated\n    {\n      flush: \"post\",\n      deep: true\n    });\n    let pendingCacheKey = null;\n    const cacheSubtree = () => {\n      if (pendingCacheKey != null) {\n        if (isSuspense(instance.subTree.type)) {\n          queuePostRenderEffect(() => {\n            cache.set(pendingCacheKey, getInnerChild(instance.subTree));\n          }, instance.subTree.suspense);\n        } else {\n          cache.set(pendingCacheKey, getInnerChild(instance.subTree));\n        }\n      }\n    };\n    onMounted(cacheSubtree);\n    onUpdated(cacheSubtree);\n    onBeforeUnmount(() => {\n      cache.forEach(cached => {\n        const {\n          subTree,\n          suspense\n        } = instance;\n        const vnode = getInnerChild(subTree);\n        if (cached.type === vnode.type && cached.key === vnode.key) {\n          resetShapeFlag(vnode);\n          const da = vnode.component.da;\n          da && queuePostRenderEffect(da, suspense);\n          return;\n        }\n        unmount(cached);\n      });\n    });\n    return () => {\n      pendingCacheKey = null;\n      if (!slots.default) {\n        return current = null;\n      }\n      const children = slots.default();\n      const rawVNode = children[0];\n      if (children.length > 1) {\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          warn$1(`KeepAlive should contain exactly one component child.`);\n        }\n        current = null;\n        return children;\n      } else if (!isVNode(rawVNode) || !(rawVNode.shapeFlag & 4) && !(rawVNode.shapeFlag & 128)) {\n        current = null;\n        return rawVNode;\n      }\n      let vnode = getInnerChild(rawVNode);\n      if (vnode.type === Comment) {\n        current = null;\n        return vnode;\n      }\n      const comp = vnode.type;\n      const name = getComponentName(isAsyncWrapper(vnode) ? vnode.type.__asyncResolved || {} : comp);\n      const {\n        include,\n        exclude,\n        max\n      } = props;\n      if (include && (!name || !matches(include, name)) || exclude && name && matches(exclude, name)) {\n        vnode.shapeFlag &= ~256;\n        current = vnode;\n        return rawVNode;\n      }\n      const key = vnode.key == null ? comp : vnode.key;\n      const cachedVNode = cache.get(key);\n      if (vnode.el) {\n        vnode = cloneVNode(vnode);\n        if (rawVNode.shapeFlag & 128) {\n          rawVNode.ssContent = vnode;\n        }\n      }\n      pendingCacheKey = key;\n      if (cachedVNode) {\n        vnode.el = cachedVNode.el;\n        vnode.component = cachedVNode.component;\n        if (vnode.transition) {\n          setTransitionHooks(vnode, vnode.transition);\n        }\n        vnode.shapeFlag |= 512;\n        keys.delete(key);\n        keys.add(key);\n      } else {\n        keys.add(key);\n        if (max && keys.size > parseInt(max, 10)) {\n          pruneCacheEntry(keys.values().next().value);\n        }\n      }\n      vnode.shapeFlag |= 256;\n      current = vnode;\n      return isSuspense(rawVNode.type) ? rawVNode : vnode;\n    };\n  }\n};\nconst KeepAlive = KeepAliveImpl;\nfunction matches(pattern, name) {\n  if (isArray(pattern)) {\n    return pattern.some(p => matches(p, name));\n  } else if (isString(pattern)) {\n    return pattern.split(\",\").includes(name);\n  } else if (isRegExp(pattern)) {\n    pattern.lastIndex = 0;\n    return pattern.test(name);\n  }\n  return false;\n}\nfunction onActivated(hook, target) {\n  registerKeepAliveHook(hook, \"a\", target);\n}\nfunction onDeactivated(hook, target) {\n  registerKeepAliveHook(hook, \"da\", target);\n}\nfunction registerKeepAliveHook(hook, type, target = currentInstance) {\n  const wrappedHook = hook.__wdc || (hook.__wdc = () => {\n    let current = target;\n    while (current) {\n      if (current.isDeactivated) {\n        return;\n      }\n      current = current.parent;\n    }\n    return hook();\n  });\n  injectHook(type, wrappedHook, target);\n  if (target) {\n    let current = target.parent;\n    while (current && current.parent) {\n      if (isKeepAlive(current.parent.vnode)) {\n        injectToKeepAliveRoot(wrappedHook, type, target, current);\n      }\n      current = current.parent;\n    }\n  }\n}\nfunction injectToKeepAliveRoot(hook, type, target, keepAliveRoot) {\n  const injected = injectHook(type, hook, keepAliveRoot, true\n  /* prepend */);\n  onUnmounted(() => {\n    remove(keepAliveRoot[type], injected);\n  }, target);\n}\nfunction resetShapeFlag(vnode) {\n  vnode.shapeFlag &= ~256;\n  vnode.shapeFlag &= ~512;\n}\nfunction getInnerChild(vnode) {\n  return vnode.shapeFlag & 128 ? vnode.ssContent : vnode;\n}\nfunction injectHook(type, hook, target = currentInstance, prepend = false) {\n  if (target) {\n    const hooks = target[type] || (target[type] = []);\n    const wrappedHook = hook.__weh || (hook.__weh = (...args) => {\n      pauseTracking();\n      const reset = setCurrentInstance(target);\n      const res = callWithAsyncErrorHandling(hook, target, type, args);\n      reset();\n      resetTracking();\n      return res;\n    });\n    if (prepend) {\n      hooks.unshift(wrappedHook);\n    } else {\n      hooks.push(wrappedHook);\n    }\n    return wrappedHook;\n  } else if (!!(process.env.NODE_ENV !== \"production\")) {\n    const apiName = toHandlerKey(ErrorTypeStrings$1[type].replace(/ hook$/, \"\"));\n    warn$1(`${apiName} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup().` + ` If you are using async setup(), make sure to register lifecycle hooks before the first await statement.`);\n  }\n}\nconst createHook = lifecycle => (hook, target = currentInstance) => {\n  if (!isInSSRComponentSetup || lifecycle === \"sp\") {\n    injectHook(lifecycle, (...args) => hook(...args), target);\n  }\n};\nconst onBeforeMount = createHook(\"bm\");\nconst onMounted = createHook(\"m\");\nconst onBeforeUpdate = createHook(\"bu\");\nconst onUpdated = createHook(\"u\");\nconst onBeforeUnmount = createHook(\"bum\");\nconst onUnmounted = createHook(\"um\");\nconst onServerPrefetch = createHook(\"sp\");\nconst onRenderTriggered = createHook(\"rtg\");\nconst onRenderTracked = createHook(\"rtc\");\nfunction onErrorCaptured(hook, target = currentInstance) {\n  injectHook(\"ec\", hook, target);\n}\nconst COMPONENTS = \"components\";\nconst DIRECTIVES = \"directives\";\nfunction resolveComponent(name, maybeSelfReference) {\n  return resolveAsset(COMPONENTS, name, true, maybeSelfReference) || name;\n}\nconst NULL_DYNAMIC_COMPONENT = Symbol.for(\"v-ndc\");\nfunction resolveDynamicComponent(component) {\n  if (isString(component)) {\n    return resolveAsset(COMPONENTS, component, false) || component;\n  } else {\n    return component || NULL_DYNAMIC_COMPONENT;\n  }\n}\nfunction resolveDirective(name) {\n  return resolveAsset(DIRECTIVES, name);\n}\nfunction resolveAsset(type, name, warnMissing = true, maybeSelfReference = false) {\n  const instance = currentRenderingInstance || currentInstance;\n  if (instance) {\n    const Component = instance.type;\n    if (type === COMPONENTS) {\n      const selfName = getComponentName(Component, false);\n      if (selfName && (selfName === name || selfName === camelize(name) || selfName === capitalize(camelize(name)))) {\n        return Component;\n      }\n    }\n    const res =\n    // local registration\n    // check instance[type] first which is resolved for options API\n    resolve(instance[type] || Component[type], name) ||\n    // global registration\n    resolve(instance.appContext[type], name);\n    if (!res && maybeSelfReference) {\n      return Component;\n    }\n    if (!!(process.env.NODE_ENV !== \"production\") && warnMissing && !res) {\n      const extra = type === COMPONENTS ? `\nIf this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.` : ``;\n      warn$1(`Failed to resolve ${type.slice(0, -1)}: ${name}${extra}`);\n    }\n    return res;\n  } else if (!!(process.env.NODE_ENV !== \"production\")) {\n    warn$1(`resolve${capitalize(type.slice(0, -1))} can only be used in render() or setup().`);\n  }\n}\nfunction resolve(registry, name) {\n  return registry && (registry[name] || registry[camelize(name)] || registry[capitalize(camelize(name))]);\n}\nfunction renderList(source, renderItem, cache, index) {\n  let ret;\n  const cached = cache && cache[index];\n  const sourceIsArray = isArray(source);\n  if (sourceIsArray || isString(source)) {\n    const sourceIsReactiveArray = sourceIsArray && isReactive(source);\n    let needsWrap = false;\n    if (sourceIsReactiveArray) {\n      needsWrap = !isShallow(source);\n      source = shallowReadArray(source);\n    }\n    ret = new Array(source.length);\n    for (let i = 0, l = source.length; i < l; i++) {\n      ret[i] = renderItem(needsWrap ? toReactive(source[i]) : source[i], i, void 0, cached && cached[i]);\n    }\n  } else if (typeof source === \"number\") {\n    if (!!(process.env.NODE_ENV !== \"production\") && !Number.isInteger(source)) {\n      warn$1(`The v-for range expect an integer value but got ${source}.`);\n    }\n    ret = new Array(source);\n    for (let i = 0; i < source; i++) {\n      ret[i] = renderItem(i + 1, i, void 0, cached && cached[i]);\n    }\n  } else if (isObject(source)) {\n    if (source[Symbol.iterator]) {\n      ret = Array.from(source, (item, i) => renderItem(item, i, void 0, cached && cached[i]));\n    } else {\n      const keys = Object.keys(source);\n      ret = new Array(keys.length);\n      for (let i = 0, l = keys.length; i < l; i++) {\n        const key = keys[i];\n        ret[i] = renderItem(source[key], key, i, cached && cached[i]);\n      }\n    }\n  } else {\n    ret = [];\n  }\n  if (cache) {\n    cache[index] = ret;\n  }\n  return ret;\n}\nfunction createSlots(slots, dynamicSlots) {\n  for (let i = 0; i < dynamicSlots.length; i++) {\n    const slot = dynamicSlots[i];\n    if (isArray(slot)) {\n      for (let j = 0; j < slot.length; j++) {\n        slots[slot[j].name] = slot[j].fn;\n      }\n    } else if (slot) {\n      slots[slot.name] = slot.key ? (...args) => {\n        const res = slot.fn(...args);\n        if (res) res.key = slot.key;\n        return res;\n      } : slot.fn;\n    }\n  }\n  return slots;\n}\nfunction renderSlot(slots, name, props = {}, fallback, noSlotted) {\n  if (currentRenderingInstance.ce || currentRenderingInstance.parent && isAsyncWrapper(currentRenderingInstance.parent) && currentRenderingInstance.parent.ce) {\n    if (name !== \"default\") props.name = name;\n    return openBlock(), createBlock(Fragment, null, [createVNode(\"slot\", props, fallback && fallback())], 64);\n  }\n  let slot = slots[name];\n  if (!!(process.env.NODE_ENV !== \"production\") && slot && slot.length > 1) {\n    warn$1(`SSR-optimized slot function detected in a non-SSR-optimized render function. You need to mark this component with $dynamic-slots in the parent template.`);\n    slot = () => [];\n  }\n  if (slot && slot._c) {\n    slot._d = false;\n  }\n  openBlock();\n  const validSlotContent = slot && ensureValidVNode(slot(props));\n  const slotKey = props.key ||\n  // slot content array of a dynamic conditional slot may have a branch\n  // key attached in the `createSlots` helper, respect that\n  validSlotContent && validSlotContent.key;\n  const rendered = createBlock(Fragment, {\n    key: (slotKey && !isSymbol(slotKey) ? slotKey : `_${name}`) + (\n    // #7256 force differentiate fallback content from actual content\n    !validSlotContent && fallback ? \"_fb\" : \"\")\n  }, validSlotContent || (fallback ? fallback() : []), validSlotContent && slots._ === 1 ? 64 : -2);\n  if (!noSlotted && rendered.scopeId) {\n    rendered.slotScopeIds = [rendered.scopeId + \"-s\"];\n  }\n  if (slot && slot._c) {\n    slot._d = true;\n  }\n  return rendered;\n}\nfunction ensureValidVNode(vnodes) {\n  return vnodes.some(child => {\n    if (!isVNode(child)) return true;\n    if (child.type === Comment) return false;\n    if (child.type === Fragment && !ensureValidVNode(child.children)) return false;\n    return true;\n  }) ? vnodes : null;\n}\nfunction toHandlers(obj, preserveCaseIfNecessary) {\n  const ret = {};\n  if (!!(process.env.NODE_ENV !== \"production\") && !isObject(obj)) {\n    warn$1(`v-on with no argument expects an object value.`);\n    return ret;\n  }\n  for (const key in obj) {\n    ret[preserveCaseIfNecessary && /[A-Z]/.test(key) ? `on:${key}` : toHandlerKey(key)] = obj[key];\n  }\n  return ret;\n}\nconst getPublicInstance = i => {\n  if (!i) return null;\n  if (isStatefulComponent(i)) return getComponentPublicInstance(i);\n  return getPublicInstance(i.parent);\n};\nconst publicPropertiesMap =\n// Move PURE marker to new line to workaround compiler discarding it\n// due to type annotation\n/* @__PURE__ */\nextend(/* @__PURE__ */Object.create(null), {\n  $: i => i,\n  $el: i => i.vnode.el,\n  $data: i => i.data,\n  $props: i => !!(process.env.NODE_ENV !== \"production\") ? shallowReadonly(i.props) : i.props,\n  $attrs: i => !!(process.env.NODE_ENV !== \"production\") ? shallowReadonly(i.attrs) : i.attrs,\n  $slots: i => !!(process.env.NODE_ENV !== \"production\") ? shallowReadonly(i.slots) : i.slots,\n  $refs: i => !!(process.env.NODE_ENV !== \"production\") ? shallowReadonly(i.refs) : i.refs,\n  $parent: i => getPublicInstance(i.parent),\n  $root: i => getPublicInstance(i.root),\n  $host: i => i.ce,\n  $emit: i => i.emit,\n  $options: i => __VUE_OPTIONS_API__ ? resolveMergedOptions(i) : i.type,\n  $forceUpdate: i => i.f || (i.f = () => {\n    queueJob(i.update);\n  }),\n  $nextTick: i => i.n || (i.n = nextTick.bind(i.proxy)),\n  $watch: i => __VUE_OPTIONS_API__ ? instanceWatch.bind(i) : NOOP\n});\nconst isReservedPrefix = key => key === \"_\" || key === \"$\";\nconst hasSetupBinding = (state, key) => state !== EMPTY_OBJ && !state.__isScriptSetup && hasOwn(state, key);\nconst PublicInstanceProxyHandlers = {\n  get({\n    _: instance\n  }, key) {\n    if (key === \"__v_skip\") {\n      return true;\n    }\n    const {\n      ctx,\n      setupState,\n      data,\n      props,\n      accessCache,\n      type,\n      appContext\n    } = instance;\n    if (!!(process.env.NODE_ENV !== \"production\") && key === \"__isVue\") {\n      return true;\n    }\n    let normalizedProps;\n    if (key[0] !== \"$\") {\n      const n = accessCache[key];\n      if (n !== void 0) {\n        switch (n) {\n          case 1 /* SETUP */:\n            return setupState[key];\n          case 2 /* DATA */:\n            return data[key];\n          case 4 /* CONTEXT */:\n            return ctx[key];\n          case 3 /* PROPS */:\n            return props[key];\n        }\n      } else if (hasSetupBinding(setupState, key)) {\n        accessCache[key] = 1 /* SETUP */;\n        return setupState[key];\n      } else if (data !== EMPTY_OBJ && hasOwn(data, key)) {\n        accessCache[key] = 2 /* DATA */;\n        return data[key];\n      } else if (\n      // only cache other properties when instance has declared (thus stable)\n      // props\n      (normalizedProps = instance.propsOptions[0]) && hasOwn(normalizedProps, key)) {\n        accessCache[key] = 3 /* PROPS */;\n        return props[key];\n      } else if (ctx !== EMPTY_OBJ && hasOwn(ctx, key)) {\n        accessCache[key] = 4 /* CONTEXT */;\n        return ctx[key];\n      } else if (!__VUE_OPTIONS_API__ || shouldCacheAccess) {\n        accessCache[key] = 0 /* OTHER */;\n      }\n    }\n    const publicGetter = publicPropertiesMap[key];\n    let cssModule, globalProperties;\n    if (publicGetter) {\n      if (key === \"$attrs\") {\n        track(instance.attrs, \"get\", \"\");\n        !!(process.env.NODE_ENV !== \"production\") && markAttrsAccessed();\n      } else if (!!(process.env.NODE_ENV !== \"production\") && key === \"$slots\") {\n        track(instance, \"get\", key);\n      }\n      return publicGetter(instance);\n    } else if (\n    // css module (injected by vue-loader)\n    (cssModule = type.__cssModules) && (cssModule = cssModule[key])) {\n      return cssModule;\n    } else if (ctx !== EMPTY_OBJ && hasOwn(ctx, key)) {\n      accessCache[key] = 4 /* CONTEXT */;\n      return ctx[key];\n    } else if (\n    // global properties\n    globalProperties = appContext.config.globalProperties, hasOwn(globalProperties, key)) {\n      {\n        return globalProperties[key];\n      }\n    } else if (!!(process.env.NODE_ENV !== \"production\") && currentRenderingInstance && (!isString(key) ||\n    // #1091 avoid internal isRef/isVNode checks on component instance leading\n    // to infinite warning loop\n    key.indexOf(\"__v\") !== 0)) {\n      if (data !== EMPTY_OBJ && isReservedPrefix(key[0]) && hasOwn(data, key)) {\n        warn$1(`Property ${JSON.stringify(key)} must be accessed via $data because it starts with a reserved character (\"$\" or \"_\") and is not proxied on the render context.`);\n      } else if (instance === currentRenderingInstance) {\n        warn$1(`Property ${JSON.stringify(key)} was accessed during render but is not defined on instance.`);\n      }\n    }\n  },\n  set({\n    _: instance\n  }, key, value) {\n    const {\n      data,\n      setupState,\n      ctx\n    } = instance;\n    if (hasSetupBinding(setupState, key)) {\n      setupState[key] = value;\n      return true;\n    } else if (!!(process.env.NODE_ENV !== \"production\") && setupState.__isScriptSetup && hasOwn(setupState, key)) {\n      warn$1(`Cannot mutate <script setup> binding \"${key}\" from Options API.`);\n      return false;\n    } else if (data !== EMPTY_OBJ && hasOwn(data, key)) {\n      data[key] = value;\n      return true;\n    } else if (hasOwn(instance.props, key)) {\n      !!(process.env.NODE_ENV !== \"production\") && warn$1(`Attempting to mutate prop \"${key}\". Props are readonly.`);\n      return false;\n    }\n    if (key[0] === \"$\" && key.slice(1) in instance) {\n      !!(process.env.NODE_ENV !== \"production\") && warn$1(`Attempting to mutate public property \"${key}\". Properties starting with $ are reserved and readonly.`);\n      return false;\n    } else {\n      if (!!(process.env.NODE_ENV !== \"production\") && key in instance.appContext.config.globalProperties) {\n        Object.defineProperty(ctx, key, {\n          enumerable: true,\n          configurable: true,\n          value\n        });\n      } else {\n        ctx[key] = value;\n      }\n    }\n    return true;\n  },\n  has({\n    _: {\n      data,\n      setupState,\n      accessCache,\n      ctx,\n      appContext,\n      propsOptions\n    }\n  }, key) {\n    let normalizedProps;\n    return !!accessCache[key] || data !== EMPTY_OBJ && hasOwn(data, key) || hasSetupBinding(setupState, key) || (normalizedProps = propsOptions[0]) && hasOwn(normalizedProps, key) || hasOwn(ctx, key) || hasOwn(publicPropertiesMap, key) || hasOwn(appContext.config.globalProperties, key);\n  },\n  defineProperty(target, key, descriptor) {\n    if (descriptor.get != null) {\n      target._.accessCache[key] = 0;\n    } else if (hasOwn(descriptor, \"value\")) {\n      this.set(target, key, descriptor.value, null);\n    }\n    return Reflect.defineProperty(target, key, descriptor);\n  }\n};\nif (!!(process.env.NODE_ENV !== \"production\") && true) {\n  PublicInstanceProxyHandlers.ownKeys = target => {\n    warn$1(`Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead.`);\n    return Reflect.ownKeys(target);\n  };\n}\nconst RuntimeCompiledPublicInstanceProxyHandlers = /* @__PURE__ */extend({}, PublicInstanceProxyHandlers, {\n  get(target, key) {\n    if (key === Symbol.unscopables) {\n      return;\n    }\n    return PublicInstanceProxyHandlers.get(target, key, target);\n  },\n  has(_, key) {\n    const has = key[0] !== \"_\" && !isGloballyAllowed(key);\n    if (!!(process.env.NODE_ENV !== \"production\") && !has && PublicInstanceProxyHandlers.has(_, key)) {\n      warn$1(`Property ${JSON.stringify(key)} should not start with _ which is a reserved prefix for Vue internals.`);\n    }\n    return has;\n  }\n});\nfunction createDevRenderContext(instance) {\n  const target = {};\n  Object.defineProperty(target, `_`, {\n    configurable: true,\n    enumerable: false,\n    get: () => instance\n  });\n  Object.keys(publicPropertiesMap).forEach(key => {\n    Object.defineProperty(target, key, {\n      configurable: true,\n      enumerable: false,\n      get: () => publicPropertiesMap[key](instance),\n      // intercepted by the proxy so no need for implementation,\n      // but needed to prevent set errors\n      set: NOOP\n    });\n  });\n  return target;\n}\nfunction exposePropsOnRenderContext(instance) {\n  const {\n    ctx,\n    propsOptions: [propsOptions]\n  } = instance;\n  if (propsOptions) {\n    Object.keys(propsOptions).forEach(key => {\n      Object.defineProperty(ctx, key, {\n        enumerable: true,\n        configurable: true,\n        get: () => instance.props[key],\n        set: NOOP\n      });\n    });\n  }\n}\nfunction exposeSetupStateOnRenderContext(instance) {\n  const {\n    ctx,\n    setupState\n  } = instance;\n  Object.keys(toRaw(setupState)).forEach(key => {\n    if (!setupState.__isScriptSetup) {\n      if (isReservedPrefix(key[0])) {\n        warn$1(`setup() return property ${JSON.stringify(key)} should not start with \"$\" or \"_\" which are reserved prefixes for Vue internals.`);\n        return;\n      }\n      Object.defineProperty(ctx, key, {\n        enumerable: true,\n        configurable: true,\n        get: () => setupState[key],\n        set: NOOP\n      });\n    }\n  });\n}\nconst warnRuntimeUsage = method => warn$1(`${method}() is a compiler-hint helper that is only usable inside <script setup> of a single file component. Its arguments should be compiled away and passing it at runtime has no effect.`);\nfunction defineProps() {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    warnRuntimeUsage(`defineProps`);\n  }\n  return null;\n}\nfunction defineEmits() {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    warnRuntimeUsage(`defineEmits`);\n  }\n  return null;\n}\nfunction defineExpose(exposed) {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    warnRuntimeUsage(`defineExpose`);\n  }\n}\nfunction defineOptions(options) {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    warnRuntimeUsage(`defineOptions`);\n  }\n}\nfunction defineSlots() {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    warnRuntimeUsage(`defineSlots`);\n  }\n  return null;\n}\nfunction defineModel() {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    warnRuntimeUsage(\"defineModel\");\n  }\n}\nfunction withDefaults(props, defaults) {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    warnRuntimeUsage(`withDefaults`);\n  }\n  return null;\n}\nfunction useSlots() {\n  return getContext().slots;\n}\nfunction useAttrs() {\n  return getContext().attrs;\n}\nfunction getContext() {\n  const i = getCurrentInstance();\n  if (!!(process.env.NODE_ENV !== \"production\") && !i) {\n    warn$1(`useContext() called without active instance.`);\n  }\n  return i.setupContext || (i.setupContext = createSetupContext(i));\n}\nfunction normalizePropsOrEmits(props) {\n  return isArray(props) ? props.reduce((normalized, p) => (normalized[p] = null, normalized), {}) : props;\n}\nfunction mergeDefaults(raw, defaults) {\n  const props = normalizePropsOrEmits(raw);\n  for (const key in defaults) {\n    if (key.startsWith(\"__skip\")) continue;\n    let opt = props[key];\n    if (opt) {\n      if (isArray(opt) || isFunction(opt)) {\n        opt = props[key] = {\n          type: opt,\n          default: defaults[key]\n        };\n      } else {\n        opt.default = defaults[key];\n      }\n    } else if (opt === null) {\n      opt = props[key] = {\n        default: defaults[key]\n      };\n    } else if (!!(process.env.NODE_ENV !== \"production\")) {\n      warn$1(`props default key \"${key}\" has no corresponding declaration.`);\n    }\n    if (opt && defaults[`__skip_${key}`]) {\n      opt.skipFactory = true;\n    }\n  }\n  return props;\n}\nfunction mergeModels(a, b) {\n  if (!a || !b) return a || b;\n  if (isArray(a) && isArray(b)) return a.concat(b);\n  return extend({}, normalizePropsOrEmits(a), normalizePropsOrEmits(b));\n}\nfunction createPropsRestProxy(props, excludedKeys) {\n  const ret = {};\n  for (const key in props) {\n    if (!excludedKeys.includes(key)) {\n      Object.defineProperty(ret, key, {\n        enumerable: true,\n        get: () => props[key]\n      });\n    }\n  }\n  return ret;\n}\nfunction withAsyncContext(getAwaitable) {\n  const ctx = getCurrentInstance();\n  if (!!(process.env.NODE_ENV !== \"production\") && !ctx) {\n    warn$1(`withAsyncContext called without active current instance. This is likely a bug.`);\n  }\n  let awaitable = getAwaitable();\n  unsetCurrentInstance();\n  if (isPromise(awaitable)) {\n    awaitable = awaitable.catch(e => {\n      setCurrentInstance(ctx);\n      throw e;\n    });\n  }\n  return [awaitable, () => setCurrentInstance(ctx)];\n}\nfunction createDuplicateChecker() {\n  const cache = /* @__PURE__ */Object.create(null);\n  return (type, key) => {\n    if (cache[key]) {\n      warn$1(`${type} property \"${key}\" is already defined in ${cache[key]}.`);\n    } else {\n      cache[key] = type;\n    }\n  };\n}\nlet shouldCacheAccess = true;\nfunction applyOptions(instance) {\n  const options = resolveMergedOptions(instance);\n  const publicThis = instance.proxy;\n  const ctx = instance.ctx;\n  shouldCacheAccess = false;\n  if (options.beforeCreate) {\n    callHook(options.beforeCreate, instance, \"bc\");\n  }\n  const {\n    // state\n    data: dataOptions,\n    computed: computedOptions,\n    methods,\n    watch: watchOptions,\n    provide: provideOptions,\n    inject: injectOptions,\n    // lifecycle\n    created,\n    beforeMount,\n    mounted,\n    beforeUpdate,\n    updated,\n    activated,\n    deactivated,\n    beforeDestroy,\n    beforeUnmount,\n    destroyed,\n    unmounted,\n    render,\n    renderTracked,\n    renderTriggered,\n    errorCaptured,\n    serverPrefetch,\n    // public API\n    expose,\n    inheritAttrs,\n    // assets\n    components,\n    directives,\n    filters\n  } = options;\n  const checkDuplicateProperties = !!(process.env.NODE_ENV !== \"production\") ? createDuplicateChecker() : null;\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    const [propsOptions] = instance.propsOptions;\n    if (propsOptions) {\n      for (const key in propsOptions) {\n        checkDuplicateProperties(\"Props\" /* PROPS */, key);\n      }\n    }\n  }\n  if (injectOptions) {\n    resolveInjections(injectOptions, ctx, checkDuplicateProperties);\n  }\n  if (methods) {\n    for (const key in methods) {\n      const methodHandler = methods[key];\n      if (isFunction(methodHandler)) {\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          Object.defineProperty(ctx, key, {\n            value: methodHandler.bind(publicThis),\n            configurable: true,\n            enumerable: true,\n            writable: true\n          });\n        } else {\n          ctx[key] = methodHandler.bind(publicThis);\n        }\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          checkDuplicateProperties(\"Methods\" /* METHODS */, key);\n        }\n      } else if (!!(process.env.NODE_ENV !== \"production\")) {\n        warn$1(`Method \"${key}\" has type \"${typeof methodHandler}\" in the component definition. Did you reference the function correctly?`);\n      }\n    }\n  }\n  if (dataOptions) {\n    if (!!(process.env.NODE_ENV !== \"production\") && !isFunction(dataOptions)) {\n      warn$1(`The data option must be a function. Plain object usage is no longer supported.`);\n    }\n    const data = dataOptions.call(publicThis, publicThis);\n    if (!!(process.env.NODE_ENV !== \"production\") && isPromise(data)) {\n      warn$1(`data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>.`);\n    }\n    if (!isObject(data)) {\n      !!(process.env.NODE_ENV !== \"production\") && warn$1(`data() should return an object.`);\n    } else {\n      instance.data = reactive(data);\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        for (const key in data) {\n          checkDuplicateProperties(\"Data\" /* DATA */, key);\n          if (!isReservedPrefix(key[0])) {\n            Object.defineProperty(ctx, key, {\n              configurable: true,\n              enumerable: true,\n              get: () => data[key],\n              set: NOOP\n            });\n          }\n        }\n      }\n    }\n  }\n  shouldCacheAccess = true;\n  if (computedOptions) {\n    for (const key in computedOptions) {\n      const opt = computedOptions[key];\n      const get = isFunction(opt) ? opt.bind(publicThis, publicThis) : isFunction(opt.get) ? opt.get.bind(publicThis, publicThis) : NOOP;\n      if (!!(process.env.NODE_ENV !== \"production\") && get === NOOP) {\n        warn$1(`Computed property \"${key}\" has no getter.`);\n      }\n      const set = !isFunction(opt) && isFunction(opt.set) ? opt.set.bind(publicThis) : !!(process.env.NODE_ENV !== \"production\") ? () => {\n        warn$1(`Write operation failed: computed property \"${key}\" is readonly.`);\n      } : NOOP;\n      const c = computed({\n        get,\n        set\n      });\n      Object.defineProperty(ctx, key, {\n        enumerable: true,\n        configurable: true,\n        get: () => c.value,\n        set: v => c.value = v\n      });\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        checkDuplicateProperties(\"Computed\" /* COMPUTED */, key);\n      }\n    }\n  }\n  if (watchOptions) {\n    for (const key in watchOptions) {\n      createWatcher(watchOptions[key], ctx, publicThis, key);\n    }\n  }\n  if (provideOptions) {\n    const provides = isFunction(provideOptions) ? provideOptions.call(publicThis) : provideOptions;\n    Reflect.ownKeys(provides).forEach(key => {\n      provide(key, provides[key]);\n    });\n  }\n  if (created) {\n    callHook(created, instance, \"c\");\n  }\n  function registerLifecycleHook(register, hook) {\n    if (isArray(hook)) {\n      hook.forEach(_hook => register(_hook.bind(publicThis)));\n    } else if (hook) {\n      register(hook.bind(publicThis));\n    }\n  }\n  registerLifecycleHook(onBeforeMount, beforeMount);\n  registerLifecycleHook(onMounted, mounted);\n  registerLifecycleHook(onBeforeUpdate, beforeUpdate);\n  registerLifecycleHook(onUpdated, updated);\n  registerLifecycleHook(onActivated, activated);\n  registerLifecycleHook(onDeactivated, deactivated);\n  registerLifecycleHook(onErrorCaptured, errorCaptured);\n  registerLifecycleHook(onRenderTracked, renderTracked);\n  registerLifecycleHook(onRenderTriggered, renderTriggered);\n  registerLifecycleHook(onBeforeUnmount, beforeUnmount);\n  registerLifecycleHook(onUnmounted, unmounted);\n  registerLifecycleHook(onServerPrefetch, serverPrefetch);\n  if (isArray(expose)) {\n    if (expose.length) {\n      const exposed = instance.exposed || (instance.exposed = {});\n      expose.forEach(key => {\n        Object.defineProperty(exposed, key, {\n          get: () => publicThis[key],\n          set: val => publicThis[key] = val\n        });\n      });\n    } else if (!instance.exposed) {\n      instance.exposed = {};\n    }\n  }\n  if (render && instance.render === NOOP) {\n    instance.render = render;\n  }\n  if (inheritAttrs != null) {\n    instance.inheritAttrs = inheritAttrs;\n  }\n  if (components) instance.components = components;\n  if (directives) instance.directives = directives;\n  if (serverPrefetch) {\n    markAsyncBoundary(instance);\n  }\n}\nfunction resolveInjections(injectOptions, ctx, checkDuplicateProperties = NOOP) {\n  if (isArray(injectOptions)) {\n    injectOptions = normalizeInject(injectOptions);\n  }\n  for (const key in injectOptions) {\n    const opt = injectOptions[key];\n    let injected;\n    if (isObject(opt)) {\n      if (\"default\" in opt) {\n        injected = inject(opt.from || key, opt.default, true);\n      } else {\n        injected = inject(opt.from || key);\n      }\n    } else {\n      injected = inject(opt);\n    }\n    if (isRef(injected)) {\n      Object.defineProperty(ctx, key, {\n        enumerable: true,\n        configurable: true,\n        get: () => injected.value,\n        set: v => injected.value = v\n      });\n    } else {\n      ctx[key] = injected;\n    }\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      checkDuplicateProperties(\"Inject\" /* INJECT */, key);\n    }\n  }\n}\nfunction callHook(hook, instance, type) {\n  callWithAsyncErrorHandling(isArray(hook) ? hook.map(h => h.bind(instance.proxy)) : hook.bind(instance.proxy), instance, type);\n}\nfunction createWatcher(raw, ctx, publicThis, key) {\n  let getter = key.includes(\".\") ? createPathGetter(publicThis, key) : () => publicThis[key];\n  if (isString(raw)) {\n    const handler = ctx[raw];\n    if (isFunction(handler)) {\n      {\n        watch(getter, handler);\n      }\n    } else if (!!(process.env.NODE_ENV !== \"production\")) {\n      warn$1(`Invalid watch handler specified by key \"${raw}\"`, handler);\n    }\n  } else if (isFunction(raw)) {\n    {\n      watch(getter, raw.bind(publicThis));\n    }\n  } else if (isObject(raw)) {\n    if (isArray(raw)) {\n      raw.forEach(r => createWatcher(r, ctx, publicThis, key));\n    } else {\n      const handler = isFunction(raw.handler) ? raw.handler.bind(publicThis) : ctx[raw.handler];\n      if (isFunction(handler)) {\n        watch(getter, handler, raw);\n      } else if (!!(process.env.NODE_ENV !== \"production\")) {\n        warn$1(`Invalid watch handler specified by key \"${raw.handler}\"`, handler);\n      }\n    }\n  } else if (!!(process.env.NODE_ENV !== \"production\")) {\n    warn$1(`Invalid watch option: \"${key}\"`, raw);\n  }\n}\nfunction resolveMergedOptions(instance) {\n  const base = instance.type;\n  const {\n    mixins,\n    extends: extendsOptions\n  } = base;\n  const {\n    mixins: globalMixins,\n    optionsCache: cache,\n    config: {\n      optionMergeStrategies\n    }\n  } = instance.appContext;\n  const cached = cache.get(base);\n  let resolved;\n  if (cached) {\n    resolved = cached;\n  } else if (!globalMixins.length && !mixins && !extendsOptions) {\n    {\n      resolved = base;\n    }\n  } else {\n    resolved = {};\n    if (globalMixins.length) {\n      globalMixins.forEach(m => mergeOptions(resolved, m, optionMergeStrategies, true));\n    }\n    mergeOptions(resolved, base, optionMergeStrategies);\n  }\n  if (isObject(base)) {\n    cache.set(base, resolved);\n  }\n  return resolved;\n}\nfunction mergeOptions(to, from, strats, asMixin = false) {\n  const {\n    mixins,\n    extends: extendsOptions\n  } = from;\n  if (extendsOptions) {\n    mergeOptions(to, extendsOptions, strats, true);\n  }\n  if (mixins) {\n    mixins.forEach(m => mergeOptions(to, m, strats, true));\n  }\n  for (const key in from) {\n    if (asMixin && key === \"expose\") {\n      !!(process.env.NODE_ENV !== \"production\") && warn$1(`\"expose\" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.`);\n    } else {\n      const strat = internalOptionMergeStrats[key] || strats && strats[key];\n      to[key] = strat ? strat(to[key], from[key]) : from[key];\n    }\n  }\n  return to;\n}\nconst internalOptionMergeStrats = {\n  data: mergeDataFn,\n  props: mergeEmitsOrPropsOptions,\n  emits: mergeEmitsOrPropsOptions,\n  // objects\n  methods: mergeObjectOptions,\n  computed: mergeObjectOptions,\n  // lifecycle\n  beforeCreate: mergeAsArray,\n  created: mergeAsArray,\n  beforeMount: mergeAsArray,\n  mounted: mergeAsArray,\n  beforeUpdate: mergeAsArray,\n  updated: mergeAsArray,\n  beforeDestroy: mergeAsArray,\n  beforeUnmount: mergeAsArray,\n  destroyed: mergeAsArray,\n  unmounted: mergeAsArray,\n  activated: mergeAsArray,\n  deactivated: mergeAsArray,\n  errorCaptured: mergeAsArray,\n  serverPrefetch: mergeAsArray,\n  // assets\n  components: mergeObjectOptions,\n  directives: mergeObjectOptions,\n  // watch\n  watch: mergeWatchOptions,\n  // provide / inject\n  provide: mergeDataFn,\n  inject: mergeInject\n};\nfunction mergeDataFn(to, from) {\n  if (!from) {\n    return to;\n  }\n  if (!to) {\n    return from;\n  }\n  return function mergedDataFn() {\n    return extend(isFunction(to) ? to.call(this, this) : to, isFunction(from) ? from.call(this, this) : from);\n  };\n}\nfunction mergeInject(to, from) {\n  return mergeObjectOptions(normalizeInject(to), normalizeInject(from));\n}\nfunction normalizeInject(raw) {\n  if (isArray(raw)) {\n    const res = {};\n    for (let i = 0; i < raw.length; i++) {\n      res[raw[i]] = raw[i];\n    }\n    return res;\n  }\n  return raw;\n}\nfunction mergeAsArray(to, from) {\n  return to ? [...new Set([].concat(to, from))] : from;\n}\nfunction mergeObjectOptions(to, from) {\n  return to ? extend(/* @__PURE__ */Object.create(null), to, from) : from;\n}\nfunction mergeEmitsOrPropsOptions(to, from) {\n  if (to) {\n    if (isArray(to) && isArray(from)) {\n      return [... /* @__PURE__ */new Set([...to, ...from])];\n    }\n    return extend(/* @__PURE__ */Object.create(null), normalizePropsOrEmits(to), normalizePropsOrEmits(from != null ? from : {}));\n  } else {\n    return from;\n  }\n}\nfunction mergeWatchOptions(to, from) {\n  if (!to) return from;\n  if (!from) return to;\n  const merged = extend(/* @__PURE__ */Object.create(null), to);\n  for (const key in from) {\n    merged[key] = mergeAsArray(to[key], from[key]);\n  }\n  return merged;\n}\nfunction createAppContext() {\n  return {\n    app: null,\n    config: {\n      isNativeTag: NO,\n      performance: false,\n      globalProperties: {},\n      optionMergeStrategies: {},\n      errorHandler: void 0,\n      warnHandler: void 0,\n      compilerOptions: {}\n    },\n    mixins: [],\n    components: {},\n    directives: {},\n    provides: /* @__PURE__ */Object.create(null),\n    optionsCache: /* @__PURE__ */new WeakMap(),\n    propsCache: /* @__PURE__ */new WeakMap(),\n    emitsCache: /* @__PURE__ */new WeakMap()\n  };\n}\nlet uid$1 = 0;\nfunction createAppAPI(render, hydrate) {\n  return function createApp(rootComponent, rootProps = null) {\n    if (!isFunction(rootComponent)) {\n      rootComponent = extend({}, rootComponent);\n    }\n    if (rootProps != null && !isObject(rootProps)) {\n      !!(process.env.NODE_ENV !== \"production\") && warn$1(`root props passed to app.mount() must be an object.`);\n      rootProps = null;\n    }\n    const context = createAppContext();\n    const installedPlugins = /* @__PURE__ */new WeakSet();\n    const pluginCleanupFns = [];\n    let isMounted = false;\n    const app = context.app = {\n      _uid: uid$1++,\n      _component: rootComponent,\n      _props: rootProps,\n      _container: null,\n      _context: context,\n      _instance: null,\n      version,\n      get config() {\n        return context.config;\n      },\n      set config(v) {\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          warn$1(`app.config cannot be replaced. Modify individual options instead.`);\n        }\n      },\n      use(plugin, ...options) {\n        if (installedPlugins.has(plugin)) {\n          !!(process.env.NODE_ENV !== \"production\") && warn$1(`Plugin has already been applied to target app.`);\n        } else if (plugin && isFunction(plugin.install)) {\n          installedPlugins.add(plugin);\n          plugin.install(app, ...options);\n        } else if (isFunction(plugin)) {\n          installedPlugins.add(plugin);\n          plugin(app, ...options);\n        } else if (!!(process.env.NODE_ENV !== \"production\")) {\n          warn$1(`A plugin must either be a function or an object with an \"install\" function.`);\n        }\n        return app;\n      },\n      mixin(mixin) {\n        if (__VUE_OPTIONS_API__) {\n          if (!context.mixins.includes(mixin)) {\n            context.mixins.push(mixin);\n          } else if (!!(process.env.NODE_ENV !== \"production\")) {\n            warn$1(\"Mixin has already been applied to target app\" + (mixin.name ? `: ${mixin.name}` : \"\"));\n          }\n        } else if (!!(process.env.NODE_ENV !== \"production\")) {\n          warn$1(\"Mixins are only available in builds supporting Options API\");\n        }\n        return app;\n      },\n      component(name, component) {\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          validateComponentName(name, context.config);\n        }\n        if (!component) {\n          return context.components[name];\n        }\n        if (!!(process.env.NODE_ENV !== \"production\") && context.components[name]) {\n          warn$1(`Component \"${name}\" has already been registered in target app.`);\n        }\n        context.components[name] = component;\n        return app;\n      },\n      directive(name, directive) {\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          validateDirectiveName(name);\n        }\n        if (!directive) {\n          return context.directives[name];\n        }\n        if (!!(process.env.NODE_ENV !== \"production\") && context.directives[name]) {\n          warn$1(`Directive \"${name}\" has already been registered in target app.`);\n        }\n        context.directives[name] = directive;\n        return app;\n      },\n      mount(rootContainer, isHydrate, namespace) {\n        if (!isMounted) {\n          if (!!(process.env.NODE_ENV !== \"production\") && rootContainer.__vue_app__) {\n            warn$1(`There is already an app instance mounted on the host container.\n If you want to mount another app on the same host container, you need to unmount the previous app by calling \\`app.unmount()\\` first.`);\n          }\n          const vnode = app._ceVNode || createVNode(rootComponent, rootProps);\n          vnode.appContext = context;\n          if (namespace === true) {\n            namespace = \"svg\";\n          } else if (namespace === false) {\n            namespace = void 0;\n          }\n          if (!!(process.env.NODE_ENV !== \"production\")) {\n            context.reload = () => {\n              render(cloneVNode(vnode), rootContainer, namespace);\n            };\n          }\n          if (isHydrate && hydrate) {\n            hydrate(vnode, rootContainer);\n          } else {\n            render(vnode, rootContainer, namespace);\n          }\n          isMounted = true;\n          app._container = rootContainer;\n          rootContainer.__vue_app__ = app;\n          if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n            app._instance = vnode.component;\n            devtoolsInitApp(app, version);\n          }\n          return getComponentPublicInstance(vnode.component);\n        } else if (!!(process.env.NODE_ENV !== \"production\")) {\n          warn$1(`App has already been mounted.\nIf you want to remount the same app, move your app creation logic into a factory function and create fresh app instances for each mount - e.g. \\`const createMyApp = () => createApp(App)\\``);\n        }\n      },\n      onUnmount(cleanupFn) {\n        if (!!(process.env.NODE_ENV !== \"production\") && typeof cleanupFn !== \"function\") {\n          warn$1(`Expected function as first argument to app.onUnmount(), but got ${typeof cleanupFn}`);\n        }\n        pluginCleanupFns.push(cleanupFn);\n      },\n      unmount() {\n        if (isMounted) {\n          callWithAsyncErrorHandling(pluginCleanupFns, app._instance, 16);\n          render(null, app._container);\n          if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n            app._instance = null;\n            devtoolsUnmountApp(app);\n          }\n          delete app._container.__vue_app__;\n        } else if (!!(process.env.NODE_ENV !== \"production\")) {\n          warn$1(`Cannot unmount an app that is not mounted.`);\n        }\n      },\n      provide(key, value) {\n        if (!!(process.env.NODE_ENV !== \"production\") && key in context.provides) {\n          warn$1(`App already provides property with key \"${String(key)}\". It will be overwritten with the new value.`);\n        }\n        context.provides[key] = value;\n        return app;\n      },\n      runWithContext(fn) {\n        const lastApp = currentApp;\n        currentApp = app;\n        try {\n          return fn();\n        } finally {\n          currentApp = lastApp;\n        }\n      }\n    };\n    return app;\n  };\n}\nlet currentApp = null;\nfunction provide(key, value) {\n  if (!currentInstance) {\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      warn$1(`provide() can only be used inside setup().`);\n    }\n  } else {\n    let provides = currentInstance.provides;\n    const parentProvides = currentInstance.parent && currentInstance.parent.provides;\n    if (parentProvides === provides) {\n      provides = currentInstance.provides = Object.create(parentProvides);\n    }\n    provides[key] = value;\n  }\n}\nfunction inject(key, defaultValue, treatDefaultAsFactory = false) {\n  const instance = currentInstance || currentRenderingInstance;\n  if (instance || currentApp) {\n    const provides = currentApp ? currentApp._context.provides : instance ? instance.parent == null ? instance.vnode.appContext && instance.vnode.appContext.provides : instance.parent.provides : void 0;\n    if (provides && key in provides) {\n      return provides[key];\n    } else if (arguments.length > 1) {\n      return treatDefaultAsFactory && isFunction(defaultValue) ? defaultValue.call(instance && instance.proxy) : defaultValue;\n    } else if (!!(process.env.NODE_ENV !== \"production\")) {\n      warn$1(`injection \"${String(key)}\" not found.`);\n    }\n  } else if (!!(process.env.NODE_ENV !== \"production\")) {\n    warn$1(`inject() can only be used inside setup() or functional components.`);\n  }\n}\nfunction hasInjectionContext() {\n  return !!(currentInstance || currentRenderingInstance || currentApp);\n}\nconst internalObjectProto = {};\nconst createInternalObject = () => Object.create(internalObjectProto);\nconst isInternalObject = obj => Object.getPrototypeOf(obj) === internalObjectProto;\nfunction initProps(instance, rawProps, isStateful, isSSR = false) {\n  const props = {};\n  const attrs = createInternalObject();\n  instance.propsDefaults = /* @__PURE__ */Object.create(null);\n  setFullProps(instance, rawProps, props, attrs);\n  for (const key in instance.propsOptions[0]) {\n    if (!(key in props)) {\n      props[key] = void 0;\n    }\n  }\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    validateProps(rawProps || {}, props, instance);\n  }\n  if (isStateful) {\n    instance.props = isSSR ? props : shallowReactive(props);\n  } else {\n    if (!instance.type.props) {\n      instance.props = attrs;\n    } else {\n      instance.props = props;\n    }\n  }\n  instance.attrs = attrs;\n}\nfunction isInHmrContext(instance) {\n  while (instance) {\n    if (instance.type.__hmrId) return true;\n    instance = instance.parent;\n  }\n}\nfunction updateProps(instance, rawProps, rawPrevProps, optimized) {\n  const {\n    props,\n    attrs,\n    vnode: {\n      patchFlag\n    }\n  } = instance;\n  const rawCurrentProps = toRaw(props);\n  const [options] = instance.propsOptions;\n  let hasAttrsChanged = false;\n  if (\n  // always force full diff in dev\n  // - #1942 if hmr is enabled with sfc component\n  // - vite#872 non-sfc component used by sfc component\n  !(!!(process.env.NODE_ENV !== \"production\") && isInHmrContext(instance)) && (optimized || patchFlag > 0) && !(patchFlag & 16)) {\n    if (patchFlag & 8) {\n      const propsToUpdate = instance.vnode.dynamicProps;\n      for (let i = 0; i < propsToUpdate.length; i++) {\n        let key = propsToUpdate[i];\n        if (isEmitListener(instance.emitsOptions, key)) {\n          continue;\n        }\n        const value = rawProps[key];\n        if (options) {\n          if (hasOwn(attrs, key)) {\n            if (value !== attrs[key]) {\n              attrs[key] = value;\n              hasAttrsChanged = true;\n            }\n          } else {\n            const camelizedKey = camelize(key);\n            props[camelizedKey] = resolvePropValue(options, rawCurrentProps, camelizedKey, value, instance, false);\n          }\n        } else {\n          if (value !== attrs[key]) {\n            attrs[key] = value;\n            hasAttrsChanged = true;\n          }\n        }\n      }\n    }\n  } else {\n    if (setFullProps(instance, rawProps, props, attrs)) {\n      hasAttrsChanged = true;\n    }\n    let kebabKey;\n    for (const key in rawCurrentProps) {\n      if (!rawProps ||\n      // for camelCase\n      !hasOwn(rawProps, key) && (\n      // it's possible the original props was passed in as kebab-case\n      // and converted to camelCase (#955)\n      (kebabKey = hyphenate(key)) === key || !hasOwn(rawProps, kebabKey))) {\n        if (options) {\n          if (rawPrevProps && (\n          // for camelCase\n          rawPrevProps[key] !== void 0 ||\n          // for kebab-case\n          rawPrevProps[kebabKey] !== void 0)) {\n            props[key] = resolvePropValue(options, rawCurrentProps, key, void 0, instance, true);\n          }\n        } else {\n          delete props[key];\n        }\n      }\n    }\n    if (attrs !== rawCurrentProps) {\n      for (const key in attrs) {\n        if (!rawProps || !hasOwn(rawProps, key) && true) {\n          delete attrs[key];\n          hasAttrsChanged = true;\n        }\n      }\n    }\n  }\n  if (hasAttrsChanged) {\n    trigger(instance.attrs, \"set\", \"\");\n  }\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    validateProps(rawProps || {}, props, instance);\n  }\n}\nfunction setFullProps(instance, rawProps, props, attrs) {\n  const [options, needCastKeys] = instance.propsOptions;\n  let hasAttrsChanged = false;\n  let rawCastValues;\n  if (rawProps) {\n    for (let key in rawProps) {\n      if (isReservedProp(key)) {\n        continue;\n      }\n      const value = rawProps[key];\n      let camelKey;\n      if (options && hasOwn(options, camelKey = camelize(key))) {\n        if (!needCastKeys || !needCastKeys.includes(camelKey)) {\n          props[camelKey] = value;\n        } else {\n          (rawCastValues || (rawCastValues = {}))[camelKey] = value;\n        }\n      } else if (!isEmitListener(instance.emitsOptions, key)) {\n        if (!(key in attrs) || value !== attrs[key]) {\n          attrs[key] = value;\n          hasAttrsChanged = true;\n        }\n      }\n    }\n  }\n  if (needCastKeys) {\n    const rawCurrentProps = toRaw(props);\n    const castValues = rawCastValues || EMPTY_OBJ;\n    for (let i = 0; i < needCastKeys.length; i++) {\n      const key = needCastKeys[i];\n      props[key] = resolvePropValue(options, rawCurrentProps, key, castValues[key], instance, !hasOwn(castValues, key));\n    }\n  }\n  return hasAttrsChanged;\n}\nfunction resolvePropValue(options, props, key, value, instance, isAbsent) {\n  const opt = options[key];\n  if (opt != null) {\n    const hasDefault = hasOwn(opt, \"default\");\n    if (hasDefault && value === void 0) {\n      const defaultValue = opt.default;\n      if (opt.type !== Function && !opt.skipFactory && isFunction(defaultValue)) {\n        const {\n          propsDefaults\n        } = instance;\n        if (key in propsDefaults) {\n          value = propsDefaults[key];\n        } else {\n          const reset = setCurrentInstance(instance);\n          value = propsDefaults[key] = defaultValue.call(null, props);\n          reset();\n        }\n      } else {\n        value = defaultValue;\n      }\n      if (instance.ce) {\n        instance.ce._setProp(key, value);\n      }\n    }\n    if (opt[0 /* shouldCast */]) {\n      if (isAbsent && !hasDefault) {\n        value = false;\n      } else if (opt[1 /* shouldCastTrue */] && (value === \"\" || value === hyphenate(key))) {\n        value = true;\n      }\n    }\n  }\n  return value;\n}\nconst mixinPropsCache = /* @__PURE__ */new WeakMap();\nfunction normalizePropsOptions(comp, appContext, asMixin = false) {\n  const cache = __VUE_OPTIONS_API__ && asMixin ? mixinPropsCache : appContext.propsCache;\n  const cached = cache.get(comp);\n  if (cached) {\n    return cached;\n  }\n  const raw = comp.props;\n  const normalized = {};\n  const needCastKeys = [];\n  let hasExtends = false;\n  if (__VUE_OPTIONS_API__ && !isFunction(comp)) {\n    const extendProps = raw2 => {\n      hasExtends = true;\n      const [props, keys] = normalizePropsOptions(raw2, appContext, true);\n      extend(normalized, props);\n      if (keys) needCastKeys.push(...keys);\n    };\n    if (!asMixin && appContext.mixins.length) {\n      appContext.mixins.forEach(extendProps);\n    }\n    if (comp.extends) {\n      extendProps(comp.extends);\n    }\n    if (comp.mixins) {\n      comp.mixins.forEach(extendProps);\n    }\n  }\n  if (!raw && !hasExtends) {\n    if (isObject(comp)) {\n      cache.set(comp, EMPTY_ARR);\n    }\n    return EMPTY_ARR;\n  }\n  if (isArray(raw)) {\n    for (let i = 0; i < raw.length; i++) {\n      if (!!(process.env.NODE_ENV !== \"production\") && !isString(raw[i])) {\n        warn$1(`props must be strings when using array syntax.`, raw[i]);\n      }\n      const normalizedKey = camelize(raw[i]);\n      if (validatePropName(normalizedKey)) {\n        normalized[normalizedKey] = EMPTY_OBJ;\n      }\n    }\n  } else if (raw) {\n    if (!!(process.env.NODE_ENV !== \"production\") && !isObject(raw)) {\n      warn$1(`invalid props options`, raw);\n    }\n    for (const key in raw) {\n      const normalizedKey = camelize(key);\n      if (validatePropName(normalizedKey)) {\n        const opt = raw[key];\n        const prop = normalized[normalizedKey] = isArray(opt) || isFunction(opt) ? {\n          type: opt\n        } : extend({}, opt);\n        const propType = prop.type;\n        let shouldCast = false;\n        let shouldCastTrue = true;\n        if (isArray(propType)) {\n          for (let index = 0; index < propType.length; ++index) {\n            const type = propType[index];\n            const typeName = isFunction(type) && type.name;\n            if (typeName === \"Boolean\") {\n              shouldCast = true;\n              break;\n            } else if (typeName === \"String\") {\n              shouldCastTrue = false;\n            }\n          }\n        } else {\n          shouldCast = isFunction(propType) && propType.name === \"Boolean\";\n        }\n        prop[0 /* shouldCast */] = shouldCast;\n        prop[1 /* shouldCastTrue */] = shouldCastTrue;\n        if (shouldCast || hasOwn(prop, \"default\")) {\n          needCastKeys.push(normalizedKey);\n        }\n      }\n    }\n  }\n  const res = [normalized, needCastKeys];\n  if (isObject(comp)) {\n    cache.set(comp, res);\n  }\n  return res;\n}\nfunction validatePropName(key) {\n  if (key[0] !== \"$\" && !isReservedProp(key)) {\n    return true;\n  } else if (!!(process.env.NODE_ENV !== \"production\")) {\n    warn$1(`Invalid prop name: \"${key}\" is a reserved property.`);\n  }\n  return false;\n}\nfunction getType(ctor) {\n  if (ctor === null) {\n    return \"null\";\n  }\n  if (typeof ctor === \"function\") {\n    return ctor.name || \"\";\n  } else if (typeof ctor === \"object\") {\n    const name = ctor.constructor && ctor.constructor.name;\n    return name || \"\";\n  }\n  return \"\";\n}\nfunction validateProps(rawProps, props, instance) {\n  const resolvedValues = toRaw(props);\n  const options = instance.propsOptions[0];\n  const camelizePropsKey = Object.keys(rawProps).map(key => camelize(key));\n  for (const key in options) {\n    let opt = options[key];\n    if (opt == null) continue;\n    validateProp(key, resolvedValues[key], opt, !!(process.env.NODE_ENV !== \"production\") ? shallowReadonly(resolvedValues) : resolvedValues, !camelizePropsKey.includes(key));\n  }\n}\nfunction validateProp(name, value, prop, props, isAbsent) {\n  const {\n    type,\n    required,\n    validator,\n    skipCheck\n  } = prop;\n  if (required && isAbsent) {\n    warn$1('Missing required prop: \"' + name + '\"');\n    return;\n  }\n  if (value == null && !required) {\n    return;\n  }\n  if (type != null && type !== true && !skipCheck) {\n    let isValid = false;\n    const types = isArray(type) ? type : [type];\n    const expectedTypes = [];\n    for (let i = 0; i < types.length && !isValid; i++) {\n      const {\n        valid,\n        expectedType\n      } = assertType(value, types[i]);\n      expectedTypes.push(expectedType || \"\");\n      isValid = valid;\n    }\n    if (!isValid) {\n      warn$1(getInvalidTypeMessage(name, value, expectedTypes));\n      return;\n    }\n  }\n  if (validator && !validator(value, props)) {\n    warn$1('Invalid prop: custom validator check failed for prop \"' + name + '\".');\n  }\n}\nconst isSimpleType = /* @__PURE__ */makeMap(\"String,Number,Boolean,Function,Symbol,BigInt\");\nfunction assertType(value, type) {\n  let valid;\n  const expectedType = getType(type);\n  if (expectedType === \"null\") {\n    valid = value === null;\n  } else if (isSimpleType(expectedType)) {\n    const t = typeof value;\n    valid = t === expectedType.toLowerCase();\n    if (!valid && t === \"object\") {\n      valid = value instanceof type;\n    }\n  } else if (expectedType === \"Object\") {\n    valid = isObject(value);\n  } else if (expectedType === \"Array\") {\n    valid = isArray(value);\n  } else {\n    valid = value instanceof type;\n  }\n  return {\n    valid,\n    expectedType\n  };\n}\nfunction getInvalidTypeMessage(name, value, expectedTypes) {\n  if (expectedTypes.length === 0) {\n    return `Prop type [] for prop \"${name}\" won't match anything. Did you mean to use type Array instead?`;\n  }\n  let message = `Invalid prop: type check failed for prop \"${name}\". Expected ${expectedTypes.map(capitalize).join(\" | \")}`;\n  const expectedType = expectedTypes[0];\n  const receivedType = toRawType(value);\n  const expectedValue = styleValue(value, expectedType);\n  const receivedValue = styleValue(value, receivedType);\n  if (expectedTypes.length === 1 && isExplicable(expectedType) && !isBoolean(expectedType, receivedType)) {\n    message += ` with value ${expectedValue}`;\n  }\n  message += `, got ${receivedType} `;\n  if (isExplicable(receivedType)) {\n    message += `with value ${receivedValue}.`;\n  }\n  return message;\n}\nfunction styleValue(value, type) {\n  if (type === \"String\") {\n    return `\"${value}\"`;\n  } else if (type === \"Number\") {\n    return `${Number(value)}`;\n  } else {\n    return `${value}`;\n  }\n}\nfunction isExplicable(type) {\n  const explicitTypes = [\"string\", \"number\", \"boolean\"];\n  return explicitTypes.some(elem => type.toLowerCase() === elem);\n}\nfunction isBoolean(...args) {\n  return args.some(elem => elem.toLowerCase() === \"boolean\");\n}\nconst isInternalKey = key => key[0] === \"_\" || key === \"$stable\";\nconst normalizeSlotValue = value => isArray(value) ? value.map(normalizeVNode) : [normalizeVNode(value)];\nconst normalizeSlot = (key, rawSlot, ctx) => {\n  if (rawSlot._n) {\n    return rawSlot;\n  }\n  const normalized = withCtx((...args) => {\n    if (!!(process.env.NODE_ENV !== \"production\") && currentInstance && (!ctx || ctx.root === currentInstance.root)) {\n      warn$1(`Slot \"${key}\" invoked outside of the render function: this will not track dependencies used in the slot. Invoke the slot function inside the render function instead.`);\n    }\n    return normalizeSlotValue(rawSlot(...args));\n  }, ctx);\n  normalized._c = false;\n  return normalized;\n};\nconst normalizeObjectSlots = (rawSlots, slots, instance) => {\n  const ctx = rawSlots._ctx;\n  for (const key in rawSlots) {\n    if (isInternalKey(key)) continue;\n    const value = rawSlots[key];\n    if (isFunction(value)) {\n      slots[key] = normalizeSlot(key, value, ctx);\n    } else if (value != null) {\n      if (!!(process.env.NODE_ENV !== \"production\") && true) {\n        warn$1(`Non-function value encountered for slot \"${key}\". Prefer function slots for better performance.`);\n      }\n      const normalized = normalizeSlotValue(value);\n      slots[key] = () => normalized;\n    }\n  }\n};\nconst normalizeVNodeSlots = (instance, children) => {\n  if (!!(process.env.NODE_ENV !== \"production\") && !isKeepAlive(instance.vnode) && true) {\n    warn$1(`Non-function value encountered for default slot. Prefer function slots for better performance.`);\n  }\n  const normalized = normalizeSlotValue(children);\n  instance.slots.default = () => normalized;\n};\nconst assignSlots = (slots, children, optimized) => {\n  for (const key in children) {\n    if (optimized || key !== \"_\") {\n      slots[key] = children[key];\n    }\n  }\n};\nconst initSlots = (instance, children, optimized) => {\n  const slots = instance.slots = createInternalObject();\n  if (instance.vnode.shapeFlag & 32) {\n    const type = children._;\n    if (type) {\n      assignSlots(slots, children, optimized);\n      if (optimized) {\n        def(slots, \"_\", type, true);\n      }\n    } else {\n      normalizeObjectSlots(children, slots);\n    }\n  } else if (children) {\n    normalizeVNodeSlots(instance, children);\n  }\n};\nconst updateSlots = (instance, children, optimized) => {\n  const {\n    vnode,\n    slots\n  } = instance;\n  let needDeletionCheck = true;\n  let deletionComparisonTarget = EMPTY_OBJ;\n  if (vnode.shapeFlag & 32) {\n    const type = children._;\n    if (type) {\n      if (!!(process.env.NODE_ENV !== \"production\") && isHmrUpdating) {\n        assignSlots(slots, children, optimized);\n        trigger(instance, \"set\", \"$slots\");\n      } else if (optimized && type === 1) {\n        needDeletionCheck = false;\n      } else {\n        assignSlots(slots, children, optimized);\n      }\n    } else {\n      needDeletionCheck = !children.$stable;\n      normalizeObjectSlots(children, slots);\n    }\n    deletionComparisonTarget = children;\n  } else if (children) {\n    normalizeVNodeSlots(instance, children);\n    deletionComparisonTarget = {\n      default: 1\n    };\n  }\n  if (needDeletionCheck) {\n    for (const key in slots) {\n      if (!isInternalKey(key) && deletionComparisonTarget[key] == null) {\n        delete slots[key];\n      }\n    }\n  }\n};\nlet supported;\nlet perf;\nfunction startMeasure(instance, type) {\n  if (instance.appContext.config.performance && isSupported()) {\n    perf.mark(`vue-${type}-${instance.uid}`);\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n    devtoolsPerfStart(instance, type, isSupported() ? perf.now() : Date.now());\n  }\n}\nfunction endMeasure(instance, type) {\n  if (instance.appContext.config.performance && isSupported()) {\n    const startTag = `vue-${type}-${instance.uid}`;\n    const endTag = startTag + `:end`;\n    perf.mark(endTag);\n    perf.measure(`<${formatComponentName(instance, instance.type)}> ${type}`, startTag, endTag);\n    perf.clearMarks(startTag);\n    perf.clearMarks(endTag);\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n    devtoolsPerfEnd(instance, type, isSupported() ? perf.now() : Date.now());\n  }\n}\nfunction isSupported() {\n  if (supported !== void 0) {\n    return supported;\n  }\n  if (typeof window !== \"undefined\" && window.performance) {\n    supported = true;\n    perf = window.performance;\n  } else {\n    supported = false;\n  }\n  return supported;\n}\nfunction initFeatureFlags() {\n  const needWarn = [];\n  if (typeof __VUE_OPTIONS_API__ !== \"boolean\") {\n    !!(process.env.NODE_ENV !== \"production\") && needWarn.push(`__VUE_OPTIONS_API__`);\n    getGlobalThis().__VUE_OPTIONS_API__ = true;\n  }\n  if (typeof __VUE_PROD_DEVTOOLS__ !== \"boolean\") {\n    !!(process.env.NODE_ENV !== \"production\") && needWarn.push(`__VUE_PROD_DEVTOOLS__`);\n    getGlobalThis().__VUE_PROD_DEVTOOLS__ = false;\n  }\n  if (typeof __VUE_PROD_HYDRATION_MISMATCH_DETAILS__ !== \"boolean\") {\n    !!(process.env.NODE_ENV !== \"production\") && needWarn.push(`__VUE_PROD_HYDRATION_MISMATCH_DETAILS__`);\n    getGlobalThis().__VUE_PROD_HYDRATION_MISMATCH_DETAILS__ = false;\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") && needWarn.length) {\n    const multi = needWarn.length > 1;\n    console.warn(`Feature flag${multi ? `s` : ``} ${needWarn.join(\", \")} ${multi ? `are` : `is`} not explicitly defined. You are running the esm-bundler build of Vue, which expects these compile-time feature flags to be globally injected via the bundler config in order to get better tree-shaking in the production bundle.\n\nFor more details, see https://link.vuejs.org/feature-flags.`);\n  }\n}\nconst queuePostRenderEffect = queueEffectWithSuspense;\nfunction createRenderer(options) {\n  return baseCreateRenderer(options);\n}\nfunction createHydrationRenderer(options) {\n  return baseCreateRenderer(options, createHydrationFunctions);\n}\nfunction baseCreateRenderer(options, createHydrationFns) {\n  {\n    initFeatureFlags();\n  }\n  const target = getGlobalThis();\n  target.__VUE__ = true;\n  if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n    setDevtoolsHook$1(target.__VUE_DEVTOOLS_GLOBAL_HOOK__, target);\n  }\n  const {\n    insert: hostInsert,\n    remove: hostRemove,\n    patchProp: hostPatchProp,\n    createElement: hostCreateElement,\n    createText: hostCreateText,\n    createComment: hostCreateComment,\n    setText: hostSetText,\n    setElementText: hostSetElementText,\n    parentNode: hostParentNode,\n    nextSibling: hostNextSibling,\n    setScopeId: hostSetScopeId = NOOP,\n    insertStaticContent: hostInsertStaticContent\n  } = options;\n  const patch = (n1, n2, container, anchor = null, parentComponent = null, parentSuspense = null, namespace = void 0, slotScopeIds = null, optimized = !!(process.env.NODE_ENV !== \"production\") && isHmrUpdating ? false : !!n2.dynamicChildren) => {\n    if (n1 === n2) {\n      return;\n    }\n    if (n1 && !isSameVNodeType(n1, n2)) {\n      anchor = getNextHostNode(n1);\n      unmount(n1, parentComponent, parentSuspense, true);\n      n1 = null;\n    }\n    if (n2.patchFlag === -2) {\n      optimized = false;\n      n2.dynamicChildren = null;\n    }\n    const {\n      type,\n      ref,\n      shapeFlag\n    } = n2;\n    switch (type) {\n      case Text:\n        processText(n1, n2, container, anchor);\n        break;\n      case Comment:\n        processCommentNode(n1, n2, container, anchor);\n        break;\n      case Static:\n        if (n1 == null) {\n          mountStaticNode(n2, container, anchor, namespace);\n        } else if (!!(process.env.NODE_ENV !== \"production\")) {\n          patchStaticNode(n1, n2, container, namespace);\n        }\n        break;\n      case Fragment:\n        processFragment(n1, n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n        break;\n      default:\n        if (shapeFlag & 1) {\n          processElement(n1, n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n        } else if (shapeFlag & 6) {\n          processComponent(n1, n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n        } else if (shapeFlag & 64) {\n          type.process(n1, n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized, internals);\n        } else if (shapeFlag & 128) {\n          type.process(n1, n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized, internals);\n        } else if (!!(process.env.NODE_ENV !== \"production\")) {\n          warn$1(\"Invalid VNode type:\", type, `(${typeof type})`);\n        }\n    }\n    if (ref != null && parentComponent) {\n      setRef(ref, n1 && n1.ref, parentSuspense, n2 || n1, !n2);\n    }\n  };\n  const processText = (n1, n2, container, anchor) => {\n    if (n1 == null) {\n      hostInsert(n2.el = hostCreateText(n2.children), container, anchor);\n    } else {\n      const el = n2.el = n1.el;\n      if (n2.children !== n1.children) {\n        hostSetText(el, n2.children);\n      }\n    }\n  };\n  const processCommentNode = (n1, n2, container, anchor) => {\n    if (n1 == null) {\n      hostInsert(n2.el = hostCreateComment(n2.children || \"\"), container, anchor);\n    } else {\n      n2.el = n1.el;\n    }\n  };\n  const mountStaticNode = (n2, container, anchor, namespace) => {\n    [n2.el, n2.anchor] = hostInsertStaticContent(n2.children, container, anchor, namespace, n2.el, n2.anchor);\n  };\n  const patchStaticNode = (n1, n2, container, namespace) => {\n    if (n2.children !== n1.children) {\n      const anchor = hostNextSibling(n1.anchor);\n      removeStaticNode(n1);\n      [n2.el, n2.anchor] = hostInsertStaticContent(n2.children, container, anchor, namespace);\n    } else {\n      n2.el = n1.el;\n      n2.anchor = n1.anchor;\n    }\n  };\n  const moveStaticNode = ({\n    el,\n    anchor\n  }, container, nextSibling) => {\n    let next;\n    while (el && el !== anchor) {\n      next = hostNextSibling(el);\n      hostInsert(el, container, nextSibling);\n      el = next;\n    }\n    hostInsert(anchor, container, nextSibling);\n  };\n  const removeStaticNode = ({\n    el,\n    anchor\n  }) => {\n    let next;\n    while (el && el !== anchor) {\n      next = hostNextSibling(el);\n      hostRemove(el);\n      el = next;\n    }\n    hostRemove(anchor);\n  };\n  const processElement = (n1, n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized) => {\n    if (n2.type === \"svg\") {\n      namespace = \"svg\";\n    } else if (n2.type === \"math\") {\n      namespace = \"mathml\";\n    }\n    if (n1 == null) {\n      mountElement(n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n    } else {\n      patchElement(n1, n2, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n    }\n  };\n  const mountElement = (vnode, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized) => {\n    let el;\n    let vnodeHook;\n    const {\n      props,\n      shapeFlag,\n      transition,\n      dirs\n    } = vnode;\n    el = vnode.el = hostCreateElement(vnode.type, namespace, props && props.is, props);\n    if (shapeFlag & 8) {\n      hostSetElementText(el, vnode.children);\n    } else if (shapeFlag & 16) {\n      mountChildren(vnode.children, el, null, parentComponent, parentSuspense, resolveChildrenNamespace(vnode, namespace), slotScopeIds, optimized);\n    }\n    if (dirs) {\n      invokeDirectiveHook(vnode, null, parentComponent, \"created\");\n    }\n    setScopeId(el, vnode, vnode.scopeId, slotScopeIds, parentComponent);\n    if (props) {\n      for (const key in props) {\n        if (key !== \"value\" && !isReservedProp(key)) {\n          hostPatchProp(el, key, null, props[key], namespace, parentComponent);\n        }\n      }\n      if (\"value\" in props) {\n        hostPatchProp(el, \"value\", null, props.value, namespace);\n      }\n      if (vnodeHook = props.onVnodeBeforeMount) {\n        invokeVNodeHook(vnodeHook, parentComponent, vnode);\n      }\n    }\n    if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n      def(el, \"__vnode\", vnode, true);\n      def(el, \"__vueParentComponent\", parentComponent, true);\n    }\n    if (dirs) {\n      invokeDirectiveHook(vnode, null, parentComponent, \"beforeMount\");\n    }\n    const needCallTransitionHooks = needTransition(parentSuspense, transition);\n    if (needCallTransitionHooks) {\n      transition.beforeEnter(el);\n    }\n    hostInsert(el, container, anchor);\n    if ((vnodeHook = props && props.onVnodeMounted) || needCallTransitionHooks || dirs) {\n      queuePostRenderEffect(() => {\n        vnodeHook && invokeVNodeHook(vnodeHook, parentComponent, vnode);\n        needCallTransitionHooks && transition.enter(el);\n        dirs && invokeDirectiveHook(vnode, null, parentComponent, \"mounted\");\n      }, parentSuspense);\n    }\n  };\n  const setScopeId = (el, vnode, scopeId, slotScopeIds, parentComponent) => {\n    if (scopeId) {\n      hostSetScopeId(el, scopeId);\n    }\n    if (slotScopeIds) {\n      for (let i = 0; i < slotScopeIds.length; i++) {\n        hostSetScopeId(el, slotScopeIds[i]);\n      }\n    }\n    if (parentComponent) {\n      let subTree = parentComponent.subTree;\n      if (!!(process.env.NODE_ENV !== \"production\") && subTree.patchFlag > 0 && subTree.patchFlag & 2048) {\n        subTree = filterSingleRoot(subTree.children) || subTree;\n      }\n      if (vnode === subTree || isSuspense(subTree.type) && (subTree.ssContent === vnode || subTree.ssFallback === vnode)) {\n        const parentVNode = parentComponent.vnode;\n        setScopeId(el, parentVNode, parentVNode.scopeId, parentVNode.slotScopeIds, parentComponent.parent);\n      }\n    }\n  };\n  const mountChildren = (children, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized, start = 0) => {\n    for (let i = start; i < children.length; i++) {\n      const child = children[i] = optimized ? cloneIfMounted(children[i]) : normalizeVNode(children[i]);\n      patch(null, child, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n    }\n  };\n  const patchElement = (n1, n2, parentComponent, parentSuspense, namespace, slotScopeIds, optimized) => {\n    const el = n2.el = n1.el;\n    if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n      el.__vnode = n2;\n    }\n    let {\n      patchFlag,\n      dynamicChildren,\n      dirs\n    } = n2;\n    patchFlag |= n1.patchFlag & 16;\n    const oldProps = n1.props || EMPTY_OBJ;\n    const newProps = n2.props || EMPTY_OBJ;\n    let vnodeHook;\n    parentComponent && toggleRecurse(parentComponent, false);\n    if (vnodeHook = newProps.onVnodeBeforeUpdate) {\n      invokeVNodeHook(vnodeHook, parentComponent, n2, n1);\n    }\n    if (dirs) {\n      invokeDirectiveHook(n2, n1, parentComponent, \"beforeUpdate\");\n    }\n    parentComponent && toggleRecurse(parentComponent, true);\n    if (!!(process.env.NODE_ENV !== \"production\") && isHmrUpdating) {\n      patchFlag = 0;\n      optimized = false;\n      dynamicChildren = null;\n    }\n    if (oldProps.innerHTML && newProps.innerHTML == null || oldProps.textContent && newProps.textContent == null) {\n      hostSetElementText(el, \"\");\n    }\n    if (dynamicChildren) {\n      patchBlockChildren(n1.dynamicChildren, dynamicChildren, el, parentComponent, parentSuspense, resolveChildrenNamespace(n2, namespace), slotScopeIds);\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        traverseStaticChildren(n1, n2);\n      }\n    } else if (!optimized) {\n      patchChildren(n1, n2, el, null, parentComponent, parentSuspense, resolveChildrenNamespace(n2, namespace), slotScopeIds, false);\n    }\n    if (patchFlag > 0) {\n      if (patchFlag & 16) {\n        patchProps(el, oldProps, newProps, parentComponent, namespace);\n      } else {\n        if (patchFlag & 2) {\n          if (oldProps.class !== newProps.class) {\n            hostPatchProp(el, \"class\", null, newProps.class, namespace);\n          }\n        }\n        if (patchFlag & 4) {\n          hostPatchProp(el, \"style\", oldProps.style, newProps.style, namespace);\n        }\n        if (patchFlag & 8) {\n          const propsToUpdate = n2.dynamicProps;\n          for (let i = 0; i < propsToUpdate.length; i++) {\n            const key = propsToUpdate[i];\n            const prev = oldProps[key];\n            const next = newProps[key];\n            if (next !== prev || key === \"value\") {\n              hostPatchProp(el, key, prev, next, namespace, parentComponent);\n            }\n          }\n        }\n      }\n      if (patchFlag & 1) {\n        if (n1.children !== n2.children) {\n          hostSetElementText(el, n2.children);\n        }\n      }\n    } else if (!optimized && dynamicChildren == null) {\n      patchProps(el, oldProps, newProps, parentComponent, namespace);\n    }\n    if ((vnodeHook = newProps.onVnodeUpdated) || dirs) {\n      queuePostRenderEffect(() => {\n        vnodeHook && invokeVNodeHook(vnodeHook, parentComponent, n2, n1);\n        dirs && invokeDirectiveHook(n2, n1, parentComponent, \"updated\");\n      }, parentSuspense);\n    }\n  };\n  const patchBlockChildren = (oldChildren, newChildren, fallbackContainer, parentComponent, parentSuspense, namespace, slotScopeIds) => {\n    for (let i = 0; i < newChildren.length; i++) {\n      const oldVNode = oldChildren[i];\n      const newVNode = newChildren[i];\n      const container =\n      // oldVNode may be an errored async setup() component inside Suspense\n      // which will not have a mounted element\n      oldVNode.el && (\n      // - In the case of a Fragment, we need to provide the actual parent\n      // of the Fragment itself so it can move its children.\n      oldVNode.type === Fragment ||\n      // - In the case of different nodes, there is going to be a replacement\n      // which also requires the correct parent container\n      !isSameVNodeType(oldVNode, newVNode) ||\n      // - In the case of a component, it could contain anything.\n      oldVNode.shapeFlag & (6 | 64)) ? hostParentNode(oldVNode.el) :\n      // In other cases, the parent container is not actually used so we\n      // just pass the block element here to avoid a DOM parentNode call.\n      fallbackContainer;\n      patch(oldVNode, newVNode, container, null, parentComponent, parentSuspense, namespace, slotScopeIds, true);\n    }\n  };\n  const patchProps = (el, oldProps, newProps, parentComponent, namespace) => {\n    if (oldProps !== newProps) {\n      if (oldProps !== EMPTY_OBJ) {\n        for (const key in oldProps) {\n          if (!isReservedProp(key) && !(key in newProps)) {\n            hostPatchProp(el, key, oldProps[key], null, namespace, parentComponent);\n          }\n        }\n      }\n      for (const key in newProps) {\n        if (isReservedProp(key)) continue;\n        const next = newProps[key];\n        const prev = oldProps[key];\n        if (next !== prev && key !== \"value\") {\n          hostPatchProp(el, key, prev, next, namespace, parentComponent);\n        }\n      }\n      if (\"value\" in newProps) {\n        hostPatchProp(el, \"value\", oldProps.value, newProps.value, namespace);\n      }\n    }\n  };\n  const processFragment = (n1, n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized) => {\n    const fragmentStartAnchor = n2.el = n1 ? n1.el : hostCreateText(\"\");\n    const fragmentEndAnchor = n2.anchor = n1 ? n1.anchor : hostCreateText(\"\");\n    let {\n      patchFlag,\n      dynamicChildren,\n      slotScopeIds: fragmentSlotScopeIds\n    } = n2;\n    if (!!(process.env.NODE_ENV !== \"production\") && (\n    // #5523 dev root fragment may inherit directives\n    isHmrUpdating || patchFlag & 2048)) {\n      patchFlag = 0;\n      optimized = false;\n      dynamicChildren = null;\n    }\n    if (fragmentSlotScopeIds) {\n      slotScopeIds = slotScopeIds ? slotScopeIds.concat(fragmentSlotScopeIds) : fragmentSlotScopeIds;\n    }\n    if (n1 == null) {\n      hostInsert(fragmentStartAnchor, container, anchor);\n      hostInsert(fragmentEndAnchor, container, anchor);\n      mountChildren(\n      // #10007\n      // such fragment like `<></>` will be compiled into\n      // a fragment which doesn't have a children.\n      // In this case fallback to an empty array\n      n2.children || [], container, fragmentEndAnchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n    } else {\n      if (patchFlag > 0 && patchFlag & 64 && dynamicChildren &&\n      // #2715 the previous fragment could've been a BAILed one as a result\n      // of renderSlot() with no valid children\n      n1.dynamicChildren) {\n        patchBlockChildren(n1.dynamicChildren, dynamicChildren, container, parentComponent, parentSuspense, namespace, slotScopeIds);\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          traverseStaticChildren(n1, n2);\n        } else if (\n        // #2080 if the stable fragment has a key, it's a <template v-for> that may\n        //  get moved around. Make sure all root level vnodes inherit el.\n        // #2134 or if it's a component root, it may also get moved around\n        // as the component is being moved.\n        n2.key != null || parentComponent && n2 === parentComponent.subTree) {\n          traverseStaticChildren(n1, n2, true\n          /* shallow */);\n        }\n      } else {\n        patchChildren(n1, n2, container, fragmentEndAnchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n      }\n    }\n  };\n  const processComponent = (n1, n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized) => {\n    n2.slotScopeIds = slotScopeIds;\n    if (n1 == null) {\n      if (n2.shapeFlag & 512) {\n        parentComponent.ctx.activate(n2, container, anchor, namespace, optimized);\n      } else {\n        mountComponent(n2, container, anchor, parentComponent, parentSuspense, namespace, optimized);\n      }\n    } else {\n      updateComponent(n1, n2, optimized);\n    }\n  };\n  const mountComponent = (initialVNode, container, anchor, parentComponent, parentSuspense, namespace, optimized) => {\n    const instance = initialVNode.component = createComponentInstance(initialVNode, parentComponent, parentSuspense);\n    if (!!(process.env.NODE_ENV !== \"production\") && instance.type.__hmrId) {\n      registerHMR(instance);\n    }\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      pushWarningContext(initialVNode);\n      startMeasure(instance, `mount`);\n    }\n    if (isKeepAlive(initialVNode)) {\n      instance.ctx.renderer = internals;\n    }\n    {\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        startMeasure(instance, `init`);\n      }\n      setupComponent(instance, false, optimized);\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        endMeasure(instance, `init`);\n      }\n    }\n    if (instance.asyncDep) {\n      if (!!(process.env.NODE_ENV !== \"production\") && isHmrUpdating) initialVNode.el = null;\n      parentSuspense && parentSuspense.registerDep(instance, setupRenderEffect, optimized);\n      if (!initialVNode.el) {\n        const placeholder = instance.subTree = createVNode(Comment);\n        processCommentNode(null, placeholder, container, anchor);\n      }\n    } else {\n      setupRenderEffect(instance, initialVNode, container, anchor, parentSuspense, namespace, optimized);\n    }\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      popWarningContext();\n      endMeasure(instance, `mount`);\n    }\n  };\n  const updateComponent = (n1, n2, optimized) => {\n    const instance = n2.component = n1.component;\n    if (shouldUpdateComponent(n1, n2, optimized)) {\n      if (instance.asyncDep && !instance.asyncResolved) {\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          pushWarningContext(n2);\n        }\n        updateComponentPreRender(instance, n2, optimized);\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          popWarningContext();\n        }\n        return;\n      } else {\n        instance.next = n2;\n        instance.update();\n      }\n    } else {\n      n2.el = n1.el;\n      instance.vnode = n2;\n    }\n  };\n  const setupRenderEffect = (instance, initialVNode, container, anchor, parentSuspense, namespace, optimized) => {\n    const componentUpdateFn = () => {\n      if (!instance.isMounted) {\n        let vnodeHook;\n        const {\n          el,\n          props\n        } = initialVNode;\n        const {\n          bm,\n          m,\n          parent,\n          root,\n          type\n        } = instance;\n        const isAsyncWrapperVNode = isAsyncWrapper(initialVNode);\n        toggleRecurse(instance, false);\n        if (bm) {\n          invokeArrayFns(bm);\n        }\n        if (!isAsyncWrapperVNode && (vnodeHook = props && props.onVnodeBeforeMount)) {\n          invokeVNodeHook(vnodeHook, parent, initialVNode);\n        }\n        toggleRecurse(instance, true);\n        if (el && hydrateNode) {\n          const hydrateSubTree = () => {\n            if (!!(process.env.NODE_ENV !== \"production\")) {\n              startMeasure(instance, `render`);\n            }\n            instance.subTree = renderComponentRoot(instance);\n            if (!!(process.env.NODE_ENV !== \"production\")) {\n              endMeasure(instance, `render`);\n            }\n            if (!!(process.env.NODE_ENV !== \"production\")) {\n              startMeasure(instance, `hydrate`);\n            }\n            hydrateNode(el, instance.subTree, instance, parentSuspense, null);\n            if (!!(process.env.NODE_ENV !== \"production\")) {\n              endMeasure(instance, `hydrate`);\n            }\n          };\n          if (isAsyncWrapperVNode && type.__asyncHydrate) {\n            type.__asyncHydrate(el, instance, hydrateSubTree);\n          } else {\n            hydrateSubTree();\n          }\n        } else {\n          if (root.ce) {\n            root.ce._injectChildStyle(type);\n          }\n          if (!!(process.env.NODE_ENV !== \"production\")) {\n            startMeasure(instance, `render`);\n          }\n          const subTree = instance.subTree = renderComponentRoot(instance);\n          if (!!(process.env.NODE_ENV !== \"production\")) {\n            endMeasure(instance, `render`);\n          }\n          if (!!(process.env.NODE_ENV !== \"production\")) {\n            startMeasure(instance, `patch`);\n          }\n          patch(null, subTree, container, anchor, instance, parentSuspense, namespace);\n          if (!!(process.env.NODE_ENV !== \"production\")) {\n            endMeasure(instance, `patch`);\n          }\n          initialVNode.el = subTree.el;\n        }\n        if (m) {\n          queuePostRenderEffect(m, parentSuspense);\n        }\n        if (!isAsyncWrapperVNode && (vnodeHook = props && props.onVnodeMounted)) {\n          const scopedInitialVNode = initialVNode;\n          queuePostRenderEffect(() => invokeVNodeHook(vnodeHook, parent, scopedInitialVNode), parentSuspense);\n        }\n        if (initialVNode.shapeFlag & 256 || parent && isAsyncWrapper(parent.vnode) && parent.vnode.shapeFlag & 256) {\n          instance.a && queuePostRenderEffect(instance.a, parentSuspense);\n        }\n        instance.isMounted = true;\n        if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n          devtoolsComponentAdded(instance);\n        }\n        initialVNode = container = anchor = null;\n      } else {\n        let {\n          next,\n          bu,\n          u,\n          parent,\n          vnode\n        } = instance;\n        {\n          const nonHydratedAsyncRoot = locateNonHydratedAsyncRoot(instance);\n          if (nonHydratedAsyncRoot) {\n            if (next) {\n              next.el = vnode.el;\n              updateComponentPreRender(instance, next, optimized);\n            }\n            nonHydratedAsyncRoot.asyncDep.then(() => {\n              if (!instance.isUnmounted) {\n                componentUpdateFn();\n              }\n            });\n            return;\n          }\n        }\n        let originNext = next;\n        let vnodeHook;\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          pushWarningContext(next || instance.vnode);\n        }\n        toggleRecurse(instance, false);\n        if (next) {\n          next.el = vnode.el;\n          updateComponentPreRender(instance, next, optimized);\n        } else {\n          next = vnode;\n        }\n        if (bu) {\n          invokeArrayFns(bu);\n        }\n        if (vnodeHook = next.props && next.props.onVnodeBeforeUpdate) {\n          invokeVNodeHook(vnodeHook, parent, next, vnode);\n        }\n        toggleRecurse(instance, true);\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          startMeasure(instance, `render`);\n        }\n        const nextTree = renderComponentRoot(instance);\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          endMeasure(instance, `render`);\n        }\n        const prevTree = instance.subTree;\n        instance.subTree = nextTree;\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          startMeasure(instance, `patch`);\n        }\n        patch(prevTree, nextTree,\n        // parent may have changed if it's in a teleport\n        hostParentNode(prevTree.el),\n        // anchor may have changed if it's in a fragment\n        getNextHostNode(prevTree), instance, parentSuspense, namespace);\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          endMeasure(instance, `patch`);\n        }\n        next.el = nextTree.el;\n        if (originNext === null) {\n          updateHOCHostEl(instance, nextTree.el);\n        }\n        if (u) {\n          queuePostRenderEffect(u, parentSuspense);\n        }\n        if (vnodeHook = next.props && next.props.onVnodeUpdated) {\n          queuePostRenderEffect(() => invokeVNodeHook(vnodeHook, parent, next, vnode), parentSuspense);\n        }\n        if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n          devtoolsComponentUpdated(instance);\n        }\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          popWarningContext();\n        }\n      }\n    };\n    instance.scope.on();\n    const effect = instance.effect = new ReactiveEffect(componentUpdateFn);\n    instance.scope.off();\n    const update = instance.update = effect.run.bind(effect);\n    const job = instance.job = effect.runIfDirty.bind(effect);\n    job.i = instance;\n    job.id = instance.uid;\n    effect.scheduler = () => queueJob(job);\n    toggleRecurse(instance, true);\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      effect.onTrack = instance.rtc ? e => invokeArrayFns(instance.rtc, e) : void 0;\n      effect.onTrigger = instance.rtg ? e => invokeArrayFns(instance.rtg, e) : void 0;\n    }\n    update();\n  };\n  const updateComponentPreRender = (instance, nextVNode, optimized) => {\n    nextVNode.component = instance;\n    const prevProps = instance.vnode.props;\n    instance.vnode = nextVNode;\n    instance.next = null;\n    updateProps(instance, nextVNode.props, prevProps, optimized);\n    updateSlots(instance, nextVNode.children, optimized);\n    pauseTracking();\n    flushPreFlushCbs(instance);\n    resetTracking();\n  };\n  const patchChildren = (n1, n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized = false) => {\n    const c1 = n1 && n1.children;\n    const prevShapeFlag = n1 ? n1.shapeFlag : 0;\n    const c2 = n2.children;\n    const {\n      patchFlag,\n      shapeFlag\n    } = n2;\n    if (patchFlag > 0) {\n      if (patchFlag & 128) {\n        patchKeyedChildren(c1, c2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n        return;\n      } else if (patchFlag & 256) {\n        patchUnkeyedChildren(c1, c2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n        return;\n      }\n    }\n    if (shapeFlag & 8) {\n      if (prevShapeFlag & 16) {\n        unmountChildren(c1, parentComponent, parentSuspense);\n      }\n      if (c2 !== c1) {\n        hostSetElementText(container, c2);\n      }\n    } else {\n      if (prevShapeFlag & 16) {\n        if (shapeFlag & 16) {\n          patchKeyedChildren(c1, c2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n        } else {\n          unmountChildren(c1, parentComponent, parentSuspense, true);\n        }\n      } else {\n        if (prevShapeFlag & 8) {\n          hostSetElementText(container, \"\");\n        }\n        if (shapeFlag & 16) {\n          mountChildren(c2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n        }\n      }\n    }\n  };\n  const patchUnkeyedChildren = (c1, c2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized) => {\n    c1 = c1 || EMPTY_ARR;\n    c2 = c2 || EMPTY_ARR;\n    const oldLength = c1.length;\n    const newLength = c2.length;\n    const commonLength = Math.min(oldLength, newLength);\n    let i;\n    for (i = 0; i < commonLength; i++) {\n      const nextChild = c2[i] = optimized ? cloneIfMounted(c2[i]) : normalizeVNode(c2[i]);\n      patch(c1[i], nextChild, container, null, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n    }\n    if (oldLength > newLength) {\n      unmountChildren(c1, parentComponent, parentSuspense, true, false, commonLength);\n    } else {\n      mountChildren(c2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized, commonLength);\n    }\n  };\n  const patchKeyedChildren = (c1, c2, container, parentAnchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized) => {\n    let i = 0;\n    const l2 = c2.length;\n    let e1 = c1.length - 1;\n    let e2 = l2 - 1;\n    while (i <= e1 && i <= e2) {\n      const n1 = c1[i];\n      const n2 = c2[i] = optimized ? cloneIfMounted(c2[i]) : normalizeVNode(c2[i]);\n      if (isSameVNodeType(n1, n2)) {\n        patch(n1, n2, container, null, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n      } else {\n        break;\n      }\n      i++;\n    }\n    while (i <= e1 && i <= e2) {\n      const n1 = c1[e1];\n      const n2 = c2[e2] = optimized ? cloneIfMounted(c2[e2]) : normalizeVNode(c2[e2]);\n      if (isSameVNodeType(n1, n2)) {\n        patch(n1, n2, container, null, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n      } else {\n        break;\n      }\n      e1--;\n      e2--;\n    }\n    if (i > e1) {\n      if (i <= e2) {\n        const nextPos = e2 + 1;\n        const anchor = nextPos < l2 ? c2[nextPos].el : parentAnchor;\n        while (i <= e2) {\n          patch(null, c2[i] = optimized ? cloneIfMounted(c2[i]) : normalizeVNode(c2[i]), container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n          i++;\n        }\n      }\n    } else if (i > e2) {\n      while (i <= e1) {\n        unmount(c1[i], parentComponent, parentSuspense, true);\n        i++;\n      }\n    } else {\n      const s1 = i;\n      const s2 = i;\n      const keyToNewIndexMap = /* @__PURE__ */new Map();\n      for (i = s2; i <= e2; i++) {\n        const nextChild = c2[i] = optimized ? cloneIfMounted(c2[i]) : normalizeVNode(c2[i]);\n        if (nextChild.key != null) {\n          if (!!(process.env.NODE_ENV !== \"production\") && keyToNewIndexMap.has(nextChild.key)) {\n            warn$1(`Duplicate keys found during update:`, JSON.stringify(nextChild.key), `Make sure keys are unique.`);\n          }\n          keyToNewIndexMap.set(nextChild.key, i);\n        }\n      }\n      let j;\n      let patched = 0;\n      const toBePatched = e2 - s2 + 1;\n      let moved = false;\n      let maxNewIndexSoFar = 0;\n      const newIndexToOldIndexMap = new Array(toBePatched);\n      for (i = 0; i < toBePatched; i++) newIndexToOldIndexMap[i] = 0;\n      for (i = s1; i <= e1; i++) {\n        const prevChild = c1[i];\n        if (patched >= toBePatched) {\n          unmount(prevChild, parentComponent, parentSuspense, true);\n          continue;\n        }\n        let newIndex;\n        if (prevChild.key != null) {\n          newIndex = keyToNewIndexMap.get(prevChild.key);\n        } else {\n          for (j = s2; j <= e2; j++) {\n            if (newIndexToOldIndexMap[j - s2] === 0 && isSameVNodeType(prevChild, c2[j])) {\n              newIndex = j;\n              break;\n            }\n          }\n        }\n        if (newIndex === void 0) {\n          unmount(prevChild, parentComponent, parentSuspense, true);\n        } else {\n          newIndexToOldIndexMap[newIndex - s2] = i + 1;\n          if (newIndex >= maxNewIndexSoFar) {\n            maxNewIndexSoFar = newIndex;\n          } else {\n            moved = true;\n          }\n          patch(prevChild, c2[newIndex], container, null, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n          patched++;\n        }\n      }\n      const increasingNewIndexSequence = moved ? getSequence(newIndexToOldIndexMap) : EMPTY_ARR;\n      j = increasingNewIndexSequence.length - 1;\n      for (i = toBePatched - 1; i >= 0; i--) {\n        const nextIndex = s2 + i;\n        const nextChild = c2[nextIndex];\n        const anchor = nextIndex + 1 < l2 ? c2[nextIndex + 1].el : parentAnchor;\n        if (newIndexToOldIndexMap[i] === 0) {\n          patch(null, nextChild, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized);\n        } else if (moved) {\n          if (j < 0 || i !== increasingNewIndexSequence[j]) {\n            move(nextChild, container, anchor, 2);\n          } else {\n            j--;\n          }\n        }\n      }\n    }\n  };\n  const move = (vnode, container, anchor, moveType, parentSuspense = null) => {\n    const {\n      el,\n      type,\n      transition,\n      children,\n      shapeFlag\n    } = vnode;\n    if (shapeFlag & 6) {\n      move(vnode.component.subTree, container, anchor, moveType);\n      return;\n    }\n    if (shapeFlag & 128) {\n      vnode.suspense.move(container, anchor, moveType);\n      return;\n    }\n    if (shapeFlag & 64) {\n      type.move(vnode, container, anchor, internals);\n      return;\n    }\n    if (type === Fragment) {\n      hostInsert(el, container, anchor);\n      for (let i = 0; i < children.length; i++) {\n        move(children[i], container, anchor, moveType);\n      }\n      hostInsert(vnode.anchor, container, anchor);\n      return;\n    }\n    if (type === Static) {\n      moveStaticNode(vnode, container, anchor);\n      return;\n    }\n    const needTransition2 = moveType !== 2 && shapeFlag & 1 && transition;\n    if (needTransition2) {\n      if (moveType === 0) {\n        transition.beforeEnter(el);\n        hostInsert(el, container, anchor);\n        queuePostRenderEffect(() => transition.enter(el), parentSuspense);\n      } else {\n        const {\n          leave,\n          delayLeave,\n          afterLeave\n        } = transition;\n        const remove2 = () => hostInsert(el, container, anchor);\n        const performLeave = () => {\n          leave(el, () => {\n            remove2();\n            afterLeave && afterLeave();\n          });\n        };\n        if (delayLeave) {\n          delayLeave(el, remove2, performLeave);\n        } else {\n          performLeave();\n        }\n      }\n    } else {\n      hostInsert(el, container, anchor);\n    }\n  };\n  const unmount = (vnode, parentComponent, parentSuspense, doRemove = false, optimized = false) => {\n    const {\n      type,\n      props,\n      ref,\n      children,\n      dynamicChildren,\n      shapeFlag,\n      patchFlag,\n      dirs,\n      cacheIndex\n    } = vnode;\n    if (patchFlag === -2) {\n      optimized = false;\n    }\n    if (ref != null) {\n      setRef(ref, null, parentSuspense, vnode, true);\n    }\n    if (cacheIndex != null) {\n      parentComponent.renderCache[cacheIndex] = void 0;\n    }\n    if (shapeFlag & 256) {\n      parentComponent.ctx.deactivate(vnode);\n      return;\n    }\n    const shouldInvokeDirs = shapeFlag & 1 && dirs;\n    const shouldInvokeVnodeHook = !isAsyncWrapper(vnode);\n    let vnodeHook;\n    if (shouldInvokeVnodeHook && (vnodeHook = props && props.onVnodeBeforeUnmount)) {\n      invokeVNodeHook(vnodeHook, parentComponent, vnode);\n    }\n    if (shapeFlag & 6) {\n      unmountComponent(vnode.component, parentSuspense, doRemove);\n    } else {\n      if (shapeFlag & 128) {\n        vnode.suspense.unmount(parentSuspense, doRemove);\n        return;\n      }\n      if (shouldInvokeDirs) {\n        invokeDirectiveHook(vnode, null, parentComponent, \"beforeUnmount\");\n      }\n      if (shapeFlag & 64) {\n        vnode.type.remove(vnode, parentComponent, parentSuspense, internals, doRemove);\n      } else if (dynamicChildren &&\n      // #5154\n      // when v-once is used inside a block, setBlockTracking(-1) marks the\n      // parent block with hasOnce: true\n      // so that it doesn't take the fast path during unmount - otherwise\n      // components nested in v-once are never unmounted.\n      !dynamicChildren.hasOnce && (\n      // #1153: fast path should not be taken for non-stable (v-for) fragments\n      type !== Fragment || patchFlag > 0 && patchFlag & 64)) {\n        unmountChildren(dynamicChildren, parentComponent, parentSuspense, false, true);\n      } else if (type === Fragment && patchFlag & (128 | 256) || !optimized && shapeFlag & 16) {\n        unmountChildren(children, parentComponent, parentSuspense);\n      }\n      if (doRemove) {\n        remove(vnode);\n      }\n    }\n    if (shouldInvokeVnodeHook && (vnodeHook = props && props.onVnodeUnmounted) || shouldInvokeDirs) {\n      queuePostRenderEffect(() => {\n        vnodeHook && invokeVNodeHook(vnodeHook, parentComponent, vnode);\n        shouldInvokeDirs && invokeDirectiveHook(vnode, null, parentComponent, \"unmounted\");\n      }, parentSuspense);\n    }\n  };\n  const remove = vnode => {\n    const {\n      type,\n      el,\n      anchor,\n      transition\n    } = vnode;\n    if (type === Fragment) {\n      if (!!(process.env.NODE_ENV !== \"production\") && vnode.patchFlag > 0 && vnode.patchFlag & 2048 && transition && !transition.persisted) {\n        vnode.children.forEach(child => {\n          if (child.type === Comment) {\n            hostRemove(child.el);\n          } else {\n            remove(child);\n          }\n        });\n      } else {\n        removeFragment(el, anchor);\n      }\n      return;\n    }\n    if (type === Static) {\n      removeStaticNode(vnode);\n      return;\n    }\n    const performRemove = () => {\n      hostRemove(el);\n      if (transition && !transition.persisted && transition.afterLeave) {\n        transition.afterLeave();\n      }\n    };\n    if (vnode.shapeFlag & 1 && transition && !transition.persisted) {\n      const {\n        leave,\n        delayLeave\n      } = transition;\n      const performLeave = () => leave(el, performRemove);\n      if (delayLeave) {\n        delayLeave(vnode.el, performRemove, performLeave);\n      } else {\n        performLeave();\n      }\n    } else {\n      performRemove();\n    }\n  };\n  const removeFragment = (cur, end) => {\n    let next;\n    while (cur !== end) {\n      next = hostNextSibling(cur);\n      hostRemove(cur);\n      cur = next;\n    }\n    hostRemove(end);\n  };\n  const unmountComponent = (instance, parentSuspense, doRemove) => {\n    if (!!(process.env.NODE_ENV !== \"production\") && instance.type.__hmrId) {\n      unregisterHMR(instance);\n    }\n    const {\n      bum,\n      scope,\n      job,\n      subTree,\n      um,\n      m,\n      a\n    } = instance;\n    invalidateMount(m);\n    invalidateMount(a);\n    if (bum) {\n      invokeArrayFns(bum);\n    }\n    scope.stop();\n    if (job) {\n      job.flags |= 8;\n      unmount(subTree, instance, parentSuspense, doRemove);\n    }\n    if (um) {\n      queuePostRenderEffect(um, parentSuspense);\n    }\n    queuePostRenderEffect(() => {\n      instance.isUnmounted = true;\n    }, parentSuspense);\n    if (parentSuspense && parentSuspense.pendingBranch && !parentSuspense.isUnmounted && instance.asyncDep && !instance.asyncResolved && instance.suspenseId === parentSuspense.pendingId) {\n      parentSuspense.deps--;\n      if (parentSuspense.deps === 0) {\n        parentSuspense.resolve();\n      }\n    }\n    if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n      devtoolsComponentRemoved(instance);\n    }\n  };\n  const unmountChildren = (children, parentComponent, parentSuspense, doRemove = false, optimized = false, start = 0) => {\n    for (let i = start; i < children.length; i++) {\n      unmount(children[i], parentComponent, parentSuspense, doRemove, optimized);\n    }\n  };\n  const getNextHostNode = vnode => {\n    if (vnode.shapeFlag & 6) {\n      return getNextHostNode(vnode.component.subTree);\n    }\n    if (vnode.shapeFlag & 128) {\n      return vnode.suspense.next();\n    }\n    const el = hostNextSibling(vnode.anchor || vnode.el);\n    const teleportEnd = el && el[TeleportEndKey];\n    return teleportEnd ? hostNextSibling(teleportEnd) : el;\n  };\n  let isFlushing = false;\n  const render = (vnode, container, namespace) => {\n    if (vnode == null) {\n      if (container._vnode) {\n        unmount(container._vnode, null, null, true);\n      }\n    } else {\n      patch(container._vnode || null, vnode, container, null, null, null, namespace);\n    }\n    container._vnode = vnode;\n    if (!isFlushing) {\n      isFlushing = true;\n      flushPreFlushCbs();\n      flushPostFlushCbs();\n      isFlushing = false;\n    }\n  };\n  const internals = {\n    p: patch,\n    um: unmount,\n    m: move,\n    r: remove,\n    mt: mountComponent,\n    mc: mountChildren,\n    pc: patchChildren,\n    pbc: patchBlockChildren,\n    n: getNextHostNode,\n    o: options\n  };\n  let hydrate;\n  let hydrateNode;\n  if (createHydrationFns) {\n    [hydrate, hydrateNode] = createHydrationFns(internals);\n  }\n  return {\n    render,\n    hydrate,\n    createApp: createAppAPI(render, hydrate)\n  };\n}\nfunction resolveChildrenNamespace({\n  type,\n  props\n}, currentNamespace) {\n  return currentNamespace === \"svg\" && type === \"foreignObject\" || currentNamespace === \"mathml\" && type === \"annotation-xml\" && props && props.encoding && props.encoding.includes(\"html\") ? void 0 : currentNamespace;\n}\nfunction toggleRecurse({\n  effect,\n  job\n}, allowed) {\n  if (allowed) {\n    effect.flags |= 32;\n    job.flags |= 4;\n  } else {\n    effect.flags &= ~32;\n    job.flags &= ~4;\n  }\n}\nfunction needTransition(parentSuspense, transition) {\n  return (!parentSuspense || parentSuspense && !parentSuspense.pendingBranch) && transition && !transition.persisted;\n}\nfunction traverseStaticChildren(n1, n2, shallow = false) {\n  const ch1 = n1.children;\n  const ch2 = n2.children;\n  if (isArray(ch1) && isArray(ch2)) {\n    for (let i = 0; i < ch1.length; i++) {\n      const c1 = ch1[i];\n      let c2 = ch2[i];\n      if (c2.shapeFlag & 1 && !c2.dynamicChildren) {\n        if (c2.patchFlag <= 0 || c2.patchFlag === 32) {\n          c2 = ch2[i] = cloneIfMounted(ch2[i]);\n          c2.el = c1.el;\n        }\n        if (!shallow && c2.patchFlag !== -2) traverseStaticChildren(c1, c2);\n      }\n      if (c2.type === Text) {\n        c2.el = c1.el;\n      }\n      if (!!(process.env.NODE_ENV !== \"production\") && c2.type === Comment && !c2.el) {\n        c2.el = c1.el;\n      }\n    }\n  }\n}\nfunction getSequence(arr) {\n  const p = arr.slice();\n  const result = [0];\n  let i, j, u, v, c;\n  const len = arr.length;\n  for (i = 0; i < len; i++) {\n    const arrI = arr[i];\n    if (arrI !== 0) {\n      j = result[result.length - 1];\n      if (arr[j] < arrI) {\n        p[i] = j;\n        result.push(i);\n        continue;\n      }\n      u = 0;\n      v = result.length - 1;\n      while (u < v) {\n        c = u + v >> 1;\n        if (arr[result[c]] < arrI) {\n          u = c + 1;\n        } else {\n          v = c;\n        }\n      }\n      if (arrI < arr[result[u]]) {\n        if (u > 0) {\n          p[i] = result[u - 1];\n        }\n        result[u] = i;\n      }\n    }\n  }\n  u = result.length;\n  v = result[u - 1];\n  while (u-- > 0) {\n    result[u] = v;\n    v = p[v];\n  }\n  return result;\n}\nfunction locateNonHydratedAsyncRoot(instance) {\n  const subComponent = instance.subTree.component;\n  if (subComponent) {\n    if (subComponent.asyncDep && !subComponent.asyncResolved) {\n      return subComponent;\n    } else {\n      return locateNonHydratedAsyncRoot(subComponent);\n    }\n  }\n}\nfunction invalidateMount(hooks) {\n  if (hooks) {\n    for (let i = 0; i < hooks.length; i++) hooks[i].flags |= 8;\n  }\n}\nconst ssrContextKey = Symbol.for(\"v-scx\");\nconst useSSRContext = () => {\n  {\n    const ctx = inject(ssrContextKey);\n    if (!ctx) {\n      !!(process.env.NODE_ENV !== \"production\") && warn$1(`Server rendering context not provided. Make sure to only call useSSRContext() conditionally in the server build.`);\n    }\n    return ctx;\n  }\n};\nfunction watchEffect(effect, options) {\n  return doWatch(effect, null, options);\n}\nfunction watchPostEffect(effect, options) {\n  return doWatch(effect, null, !!(process.env.NODE_ENV !== \"production\") ? extend({}, options, {\n    flush: \"post\"\n  }) : {\n    flush: \"post\"\n  });\n}\nfunction watchSyncEffect(effect, options) {\n  return doWatch(effect, null, !!(process.env.NODE_ENV !== \"production\") ? extend({}, options, {\n    flush: \"sync\"\n  }) : {\n    flush: \"sync\"\n  });\n}\nfunction watch(source, cb, options) {\n  if (!!(process.env.NODE_ENV !== \"production\") && !isFunction(cb)) {\n    warn$1(`\\`watch(fn, options?)\\` signature has been moved to a separate API. Use \\`watchEffect(fn, options?)\\` instead. \\`watch\\` now only supports \\`watch(source, cb, options?) signature.`);\n  }\n  return doWatch(source, cb, options);\n}\nfunction doWatch(source, cb, options = EMPTY_OBJ) {\n  const {\n    immediate,\n    deep,\n    flush,\n    once\n  } = options;\n  if (!!(process.env.NODE_ENV !== \"production\") && !cb) {\n    if (immediate !== void 0) {\n      warn$1(`watch() \"immediate\" option is only respected when using the watch(source, callback, options?) signature.`);\n    }\n    if (deep !== void 0) {\n      warn$1(`watch() \"deep\" option is only respected when using the watch(source, callback, options?) signature.`);\n    }\n    if (once !== void 0) {\n      warn$1(`watch() \"once\" option is only respected when using the watch(source, callback, options?) signature.`);\n    }\n  }\n  const baseWatchOptions = extend({}, options);\n  if (!!(process.env.NODE_ENV !== \"production\")) baseWatchOptions.onWarn = warn$1;\n  const runsImmediately = cb && immediate || !cb && flush !== \"post\";\n  let ssrCleanup;\n  if (isInSSRComponentSetup) {\n    if (flush === \"sync\") {\n      const ctx = useSSRContext();\n      ssrCleanup = ctx.__watcherHandles || (ctx.__watcherHandles = []);\n    } else if (!runsImmediately) {\n      const watchStopHandle = () => {};\n      watchStopHandle.stop = NOOP;\n      watchStopHandle.resume = NOOP;\n      watchStopHandle.pause = NOOP;\n      return watchStopHandle;\n    }\n  }\n  const instance = currentInstance;\n  baseWatchOptions.call = (fn, type, args) => callWithAsyncErrorHandling(fn, instance, type, args);\n  let isPre = false;\n  if (flush === \"post\") {\n    baseWatchOptions.scheduler = job => {\n      queuePostRenderEffect(job, instance && instance.suspense);\n    };\n  } else if (flush !== \"sync\") {\n    isPre = true;\n    baseWatchOptions.scheduler = (job, isFirstRun) => {\n      if (isFirstRun) {\n        job();\n      } else {\n        queueJob(job);\n      }\n    };\n  }\n  baseWatchOptions.augmentJob = job => {\n    if (cb) {\n      job.flags |= 4;\n    }\n    if (isPre) {\n      job.flags |= 2;\n      if (instance) {\n        job.id = instance.uid;\n        job.i = instance;\n      }\n    }\n  };\n  const watchHandle = watch$1(source, cb, baseWatchOptions);\n  if (isInSSRComponentSetup) {\n    if (ssrCleanup) {\n      ssrCleanup.push(watchHandle);\n    } else if (runsImmediately) {\n      watchHandle();\n    }\n  }\n  return watchHandle;\n}\nfunction instanceWatch(source, value, options) {\n  const publicThis = this.proxy;\n  const getter = isString(source) ? source.includes(\".\") ? createPathGetter(publicThis, source) : () => publicThis[source] : source.bind(publicThis, publicThis);\n  let cb;\n  if (isFunction(value)) {\n    cb = value;\n  } else {\n    cb = value.handler;\n    options = value;\n  }\n  const reset = setCurrentInstance(this);\n  const res = doWatch(getter, cb.bind(publicThis), options);\n  reset();\n  return res;\n}\nfunction createPathGetter(ctx, path) {\n  const segments = path.split(\".\");\n  return () => {\n    let cur = ctx;\n    for (let i = 0; i < segments.length && cur; i++) {\n      cur = cur[segments[i]];\n    }\n    return cur;\n  };\n}\nfunction useModel(props, name, options = EMPTY_OBJ) {\n  const i = getCurrentInstance();\n  if (!!(process.env.NODE_ENV !== \"production\") && !i) {\n    warn$1(`useModel() called without active instance.`);\n    return ref();\n  }\n  const camelizedName = camelize(name);\n  if (!!(process.env.NODE_ENV !== \"production\") && !i.propsOptions[0][camelizedName]) {\n    warn$1(`useModel() called with prop \"${name}\" which is not declared.`);\n    return ref();\n  }\n  const hyphenatedName = hyphenate(name);\n  const modifiers = getModelModifiers(props, camelizedName);\n  const res = customRef((track, trigger) => {\n    let localValue;\n    let prevSetValue = EMPTY_OBJ;\n    let prevEmittedValue;\n    watchSyncEffect(() => {\n      const propValue = props[camelizedName];\n      if (hasChanged(localValue, propValue)) {\n        localValue = propValue;\n        trigger();\n      }\n    });\n    return {\n      get() {\n        track();\n        return options.get ? options.get(localValue) : localValue;\n      },\n      set(value) {\n        const emittedValue = options.set ? options.set(value) : value;\n        if (!hasChanged(emittedValue, localValue) && !(prevSetValue !== EMPTY_OBJ && hasChanged(value, prevSetValue))) {\n          return;\n        }\n        const rawProps = i.vnode.props;\n        if (!(rawProps && (\n        // check if parent has passed v-model\n        name in rawProps || camelizedName in rawProps || hyphenatedName in rawProps) && (`onUpdate:${name}` in rawProps || `onUpdate:${camelizedName}` in rawProps || `onUpdate:${hyphenatedName}` in rawProps))) {\n          localValue = value;\n          trigger();\n        }\n        i.emit(`update:${name}`, emittedValue);\n        if (hasChanged(value, emittedValue) && hasChanged(value, prevSetValue) && !hasChanged(emittedValue, prevEmittedValue)) {\n          trigger();\n        }\n        prevSetValue = value;\n        prevEmittedValue = emittedValue;\n      }\n    };\n  });\n  res[Symbol.iterator] = () => {\n    let i2 = 0;\n    return {\n      next() {\n        if (i2 < 2) {\n          return {\n            value: i2++ ? modifiers || EMPTY_OBJ : res,\n            done: false\n          };\n        } else {\n          return {\n            done: true\n          };\n        }\n      }\n    };\n  };\n  return res;\n}\nconst getModelModifiers = (props, modelName) => {\n  return modelName === \"modelValue\" || modelName === \"model-value\" ? props.modelModifiers : props[`${modelName}Modifiers`] || props[`${camelize(modelName)}Modifiers`] || props[`${hyphenate(modelName)}Modifiers`];\n};\nfunction emit(instance, event, ...rawArgs) {\n  if (instance.isUnmounted) return;\n  const props = instance.vnode.props || EMPTY_OBJ;\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    const {\n      emitsOptions,\n      propsOptions: [propsOptions]\n    } = instance;\n    if (emitsOptions) {\n      if (!(event in emitsOptions) && true) {\n        if (!propsOptions || !(toHandlerKey(camelize(event)) in propsOptions)) {\n          warn$1(`Component emitted event \"${event}\" but it is neither declared in the emits option nor as an \"${toHandlerKey(camelize(event))}\" prop.`);\n        }\n      } else {\n        const validator = emitsOptions[event];\n        if (isFunction(validator)) {\n          const isValid = validator(...rawArgs);\n          if (!isValid) {\n            warn$1(`Invalid event arguments: event validation failed for event \"${event}\".`);\n          }\n        }\n      }\n    }\n  }\n  let args = rawArgs;\n  const isModelListener = event.startsWith(\"update:\");\n  const modifiers = isModelListener && getModelModifiers(props, event.slice(7));\n  if (modifiers) {\n    if (modifiers.trim) {\n      args = rawArgs.map(a => isString(a) ? a.trim() : a);\n    }\n    if (modifiers.number) {\n      args = rawArgs.map(looseToNumber);\n    }\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n    devtoolsComponentEmit(instance, event, args);\n  }\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    const lowerCaseEvent = event.toLowerCase();\n    if (lowerCaseEvent !== event && props[toHandlerKey(lowerCaseEvent)]) {\n      warn$1(`Event \"${lowerCaseEvent}\" is emitted in component ${formatComponentName(instance, instance.type)} but the handler is registered for \"${event}\". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use \"${hyphenate(event)}\" instead of \"${event}\".`);\n    }\n  }\n  let handlerName;\n  let handler = props[handlerName = toHandlerKey(event)] ||\n  // also try camelCase event handler (#2249)\n  props[handlerName = toHandlerKey(camelize(event))];\n  if (!handler && isModelListener) {\n    handler = props[handlerName = toHandlerKey(hyphenate(event))];\n  }\n  if (handler) {\n    callWithAsyncErrorHandling(handler, instance, 6, args);\n  }\n  const onceHandler = props[handlerName + `Once`];\n  if (onceHandler) {\n    if (!instance.emitted) {\n      instance.emitted = {};\n    } else if (instance.emitted[handlerName]) {\n      return;\n    }\n    instance.emitted[handlerName] = true;\n    callWithAsyncErrorHandling(onceHandler, instance, 6, args);\n  }\n}\nfunction normalizeEmitsOptions(comp, appContext, asMixin = false) {\n  const cache = appContext.emitsCache;\n  const cached = cache.get(comp);\n  if (cached !== void 0) {\n    return cached;\n  }\n  const raw = comp.emits;\n  let normalized = {};\n  let hasExtends = false;\n  if (__VUE_OPTIONS_API__ && !isFunction(comp)) {\n    const extendEmits = raw2 => {\n      const normalizedFromExtend = normalizeEmitsOptions(raw2, appContext, true);\n      if (normalizedFromExtend) {\n        hasExtends = true;\n        extend(normalized, normalizedFromExtend);\n      }\n    };\n    if (!asMixin && appContext.mixins.length) {\n      appContext.mixins.forEach(extendEmits);\n    }\n    if (comp.extends) {\n      extendEmits(comp.extends);\n    }\n    if (comp.mixins) {\n      comp.mixins.forEach(extendEmits);\n    }\n  }\n  if (!raw && !hasExtends) {\n    if (isObject(comp)) {\n      cache.set(comp, null);\n    }\n    return null;\n  }\n  if (isArray(raw)) {\n    raw.forEach(key => normalized[key] = null);\n  } else {\n    extend(normalized, raw);\n  }\n  if (isObject(comp)) {\n    cache.set(comp, normalized);\n  }\n  return normalized;\n}\nfunction isEmitListener(options, key) {\n  if (!options || !isOn(key)) {\n    return false;\n  }\n  key = key.slice(2).replace(/Once$/, \"\");\n  return hasOwn(options, key[0].toLowerCase() + key.slice(1)) || hasOwn(options, hyphenate(key)) || hasOwn(options, key);\n}\nlet accessedAttrs = false;\nfunction markAttrsAccessed() {\n  accessedAttrs = true;\n}\nfunction renderComponentRoot(instance) {\n  const {\n    type: Component,\n    vnode,\n    proxy,\n    withProxy,\n    propsOptions: [propsOptions],\n    slots,\n    attrs,\n    emit,\n    render,\n    renderCache,\n    props,\n    data,\n    setupState,\n    ctx,\n    inheritAttrs\n  } = instance;\n  const prev = setCurrentRenderingInstance(instance);\n  let result;\n  let fallthroughAttrs;\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    accessedAttrs = false;\n  }\n  try {\n    if (vnode.shapeFlag & 4) {\n      const proxyToUse = withProxy || proxy;\n      const thisProxy = !!(process.env.NODE_ENV !== \"production\") && setupState.__isScriptSetup ? new Proxy(proxyToUse, {\n        get(target, key, receiver) {\n          warn$1(`Property '${String(key)}' was accessed via 'this'. Avoid using 'this' in templates.`);\n          return Reflect.get(target, key, receiver);\n        }\n      }) : proxyToUse;\n      result = normalizeVNode(render.call(thisProxy, proxyToUse, renderCache, !!(process.env.NODE_ENV !== \"production\") ? shallowReadonly(props) : props, setupState, data, ctx));\n      fallthroughAttrs = attrs;\n    } else {\n      const render2 = Component;\n      if (!!(process.env.NODE_ENV !== \"production\") && attrs === props) {\n        markAttrsAccessed();\n      }\n      result = normalizeVNode(render2.length > 1 ? render2(!!(process.env.NODE_ENV !== \"production\") ? shallowReadonly(props) : props, !!(process.env.NODE_ENV !== \"production\") ? {\n        get attrs() {\n          markAttrsAccessed();\n          return shallowReadonly(attrs);\n        },\n        slots,\n        emit\n      } : {\n        attrs,\n        slots,\n        emit\n      }) : render2(!!(process.env.NODE_ENV !== \"production\") ? shallowReadonly(props) : props, null));\n      fallthroughAttrs = Component.props ? attrs : getFunctionalFallthrough(attrs);\n    }\n  } catch (err) {\n    blockStack.length = 0;\n    handleError(err, instance, 1);\n    result = createVNode(Comment);\n  }\n  let root = result;\n  let setRoot = void 0;\n  if (!!(process.env.NODE_ENV !== \"production\") && result.patchFlag > 0 && result.patchFlag & 2048) {\n    [root, setRoot] = getChildRoot(result);\n  }\n  if (fallthroughAttrs && inheritAttrs !== false) {\n    const keys = Object.keys(fallthroughAttrs);\n    const {\n      shapeFlag\n    } = root;\n    if (keys.length) {\n      if (shapeFlag & (1 | 6)) {\n        if (propsOptions && keys.some(isModelListener)) {\n          fallthroughAttrs = filterModelListeners(fallthroughAttrs, propsOptions);\n        }\n        root = cloneVNode(root, fallthroughAttrs, false, true);\n      } else if (!!(process.env.NODE_ENV !== \"production\") && !accessedAttrs && root.type !== Comment) {\n        const allAttrs = Object.keys(attrs);\n        const eventAttrs = [];\n        const extraAttrs = [];\n        for (let i = 0, l = allAttrs.length; i < l; i++) {\n          const key = allAttrs[i];\n          if (isOn(key)) {\n            if (!isModelListener(key)) {\n              eventAttrs.push(key[2].toLowerCase() + key.slice(3));\n            }\n          } else {\n            extraAttrs.push(key);\n          }\n        }\n        if (extraAttrs.length) {\n          warn$1(`Extraneous non-props attributes (${extraAttrs.join(\", \")}) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes.`);\n        }\n        if (eventAttrs.length) {\n          warn$1(`Extraneous non-emits event listeners (${eventAttrs.join(\", \")}) were passed to component but could not be automatically inherited because component renders fragment or text root nodes. If the listener is intended to be a component custom event listener only, declare it using the \"emits\" option.`);\n        }\n      }\n    }\n  }\n  if (vnode.dirs) {\n    if (!!(process.env.NODE_ENV !== \"production\") && !isElementRoot(root)) {\n      warn$1(`Runtime directive used on component with non-element root node. The directives will not function as intended.`);\n    }\n    root = cloneVNode(root, null, false, true);\n    root.dirs = root.dirs ? root.dirs.concat(vnode.dirs) : vnode.dirs;\n  }\n  if (vnode.transition) {\n    if (!!(process.env.NODE_ENV !== \"production\") && !isElementRoot(root)) {\n      warn$1(`Component inside <Transition> renders non-element root node that cannot be animated.`);\n    }\n    setTransitionHooks(root, vnode.transition);\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") && setRoot) {\n    setRoot(root);\n  } else {\n    result = root;\n  }\n  setCurrentRenderingInstance(prev);\n  return result;\n}\nconst getChildRoot = vnode => {\n  const rawChildren = vnode.children;\n  const dynamicChildren = vnode.dynamicChildren;\n  const childRoot = filterSingleRoot(rawChildren, false);\n  if (!childRoot) {\n    return [vnode, void 0];\n  } else if (!!(process.env.NODE_ENV !== \"production\") && childRoot.patchFlag > 0 && childRoot.patchFlag & 2048) {\n    return getChildRoot(childRoot);\n  }\n  const index = rawChildren.indexOf(childRoot);\n  const dynamicIndex = dynamicChildren ? dynamicChildren.indexOf(childRoot) : -1;\n  const setRoot = updatedRoot => {\n    rawChildren[index] = updatedRoot;\n    if (dynamicChildren) {\n      if (dynamicIndex > -1) {\n        dynamicChildren[dynamicIndex] = updatedRoot;\n      } else if (updatedRoot.patchFlag > 0) {\n        vnode.dynamicChildren = [...dynamicChildren, updatedRoot];\n      }\n    }\n  };\n  return [normalizeVNode(childRoot), setRoot];\n};\nfunction filterSingleRoot(children, recurse = true) {\n  let singleRoot;\n  for (let i = 0; i < children.length; i++) {\n    const child = children[i];\n    if (isVNode(child)) {\n      if (child.type !== Comment || child.children === \"v-if\") {\n        if (singleRoot) {\n          return;\n        } else {\n          singleRoot = child;\n          if (!!(process.env.NODE_ENV !== \"production\") && recurse && singleRoot.patchFlag > 0 && singleRoot.patchFlag & 2048) {\n            return filterSingleRoot(singleRoot.children);\n          }\n        }\n      }\n    } else {\n      return;\n    }\n  }\n  return singleRoot;\n}\nconst getFunctionalFallthrough = attrs => {\n  let res;\n  for (const key in attrs) {\n    if (key === \"class\" || key === \"style\" || isOn(key)) {\n      (res || (res = {}))[key] = attrs[key];\n    }\n  }\n  return res;\n};\nconst filterModelListeners = (attrs, props) => {\n  const res = {};\n  for (const key in attrs) {\n    if (!isModelListener(key) || !(key.slice(9) in props)) {\n      res[key] = attrs[key];\n    }\n  }\n  return res;\n};\nconst isElementRoot = vnode => {\n  return vnode.shapeFlag & (6 | 1) || vnode.type === Comment;\n};\nfunction shouldUpdateComponent(prevVNode, nextVNode, optimized) {\n  const {\n    props: prevProps,\n    children: prevChildren,\n    component\n  } = prevVNode;\n  const {\n    props: nextProps,\n    children: nextChildren,\n    patchFlag\n  } = nextVNode;\n  const emits = component.emitsOptions;\n  if (!!(process.env.NODE_ENV !== \"production\") && (prevChildren || nextChildren) && isHmrUpdating) {\n    return true;\n  }\n  if (nextVNode.dirs || nextVNode.transition) {\n    return true;\n  }\n  if (optimized && patchFlag >= 0) {\n    if (patchFlag & 1024) {\n      return true;\n    }\n    if (patchFlag & 16) {\n      if (!prevProps) {\n        return !!nextProps;\n      }\n      return hasPropsChanged(prevProps, nextProps, emits);\n    } else if (patchFlag & 8) {\n      const dynamicProps = nextVNode.dynamicProps;\n      for (let i = 0; i < dynamicProps.length; i++) {\n        const key = dynamicProps[i];\n        if (nextProps[key] !== prevProps[key] && !isEmitListener(emits, key)) {\n          return true;\n        }\n      }\n    }\n  } else {\n    if (prevChildren || nextChildren) {\n      if (!nextChildren || !nextChildren.$stable) {\n        return true;\n      }\n    }\n    if (prevProps === nextProps) {\n      return false;\n    }\n    if (!prevProps) {\n      return !!nextProps;\n    }\n    if (!nextProps) {\n      return true;\n    }\n    return hasPropsChanged(prevProps, nextProps, emits);\n  }\n  return false;\n}\nfunction hasPropsChanged(prevProps, nextProps, emitsOptions) {\n  const nextKeys = Object.keys(nextProps);\n  if (nextKeys.length !== Object.keys(prevProps).length) {\n    return true;\n  }\n  for (let i = 0; i < nextKeys.length; i++) {\n    const key = nextKeys[i];\n    if (nextProps[key] !== prevProps[key] && !isEmitListener(emitsOptions, key)) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction updateHOCHostEl({\n  vnode,\n  parent\n}, el) {\n  while (parent) {\n    const root = parent.subTree;\n    if (root.suspense && root.suspense.activeBranch === vnode) {\n      root.el = vnode.el;\n    }\n    if (root === vnode) {\n      (vnode = parent.vnode).el = el;\n      parent = parent.parent;\n    } else {\n      break;\n    }\n  }\n}\nconst isSuspense = type => type.__isSuspense;\nlet suspenseId = 0;\nconst SuspenseImpl = {\n  name: \"Suspense\",\n  // In order to make Suspense tree-shakable, we need to avoid importing it\n  // directly in the renderer. The renderer checks for the __isSuspense flag\n  // on a vnode's type and calls the `process` method, passing in renderer\n  // internals.\n  __isSuspense: true,\n  process(n1, n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized, rendererInternals) {\n    if (n1 == null) {\n      mountSuspense(n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized, rendererInternals);\n    } else {\n      if (parentSuspense && parentSuspense.deps > 0 && !n1.suspense.isInFallback) {\n        n2.suspense = n1.suspense;\n        n2.suspense.vnode = n2;\n        n2.el = n1.el;\n        return;\n      }\n      patchSuspense(n1, n2, container, anchor, parentComponent, namespace, slotScopeIds, optimized, rendererInternals);\n    }\n  },\n  hydrate: hydrateSuspense,\n  normalize: normalizeSuspenseChildren\n};\nconst Suspense = SuspenseImpl;\nfunction triggerEvent(vnode, name) {\n  const eventListener = vnode.props && vnode.props[name];\n  if (isFunction(eventListener)) {\n    eventListener();\n  }\n}\nfunction mountSuspense(vnode, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized, rendererInternals) {\n  const {\n    p: patch,\n    o: {\n      createElement\n    }\n  } = rendererInternals;\n  const hiddenContainer = createElement(\"div\");\n  const suspense = vnode.suspense = createSuspenseBoundary(vnode, parentSuspense, parentComponent, container, hiddenContainer, anchor, namespace, slotScopeIds, optimized, rendererInternals);\n  patch(null, suspense.pendingBranch = vnode.ssContent, hiddenContainer, null, parentComponent, suspense, namespace, slotScopeIds);\n  if (suspense.deps > 0) {\n    triggerEvent(vnode, \"onPending\");\n    triggerEvent(vnode, \"onFallback\");\n    patch(null, vnode.ssFallback, container, anchor, parentComponent, null,\n    // fallback tree will not have suspense context\n    namespace, slotScopeIds);\n    setActiveBranch(suspense, vnode.ssFallback);\n  } else {\n    suspense.resolve(false, true);\n  }\n}\nfunction patchSuspense(n1, n2, container, anchor, parentComponent, namespace, slotScopeIds, optimized, {\n  p: patch,\n  um: unmount,\n  o: {\n    createElement\n  }\n}) {\n  const suspense = n2.suspense = n1.suspense;\n  suspense.vnode = n2;\n  n2.el = n1.el;\n  const newBranch = n2.ssContent;\n  const newFallback = n2.ssFallback;\n  const {\n    activeBranch,\n    pendingBranch,\n    isInFallback,\n    isHydrating\n  } = suspense;\n  if (pendingBranch) {\n    suspense.pendingBranch = newBranch;\n    if (isSameVNodeType(newBranch, pendingBranch)) {\n      patch(pendingBranch, newBranch, suspense.hiddenContainer, null, parentComponent, suspense, namespace, slotScopeIds, optimized);\n      if (suspense.deps <= 0) {\n        suspense.resolve();\n      } else if (isInFallback) {\n        if (!isHydrating) {\n          patch(activeBranch, newFallback, container, anchor, parentComponent, null,\n          // fallback tree will not have suspense context\n          namespace, slotScopeIds, optimized);\n          setActiveBranch(suspense, newFallback);\n        }\n      }\n    } else {\n      suspense.pendingId = suspenseId++;\n      if (isHydrating) {\n        suspense.isHydrating = false;\n        suspense.activeBranch = pendingBranch;\n      } else {\n        unmount(pendingBranch, parentComponent, suspense);\n      }\n      suspense.deps = 0;\n      suspense.effects.length = 0;\n      suspense.hiddenContainer = createElement(\"div\");\n      if (isInFallback) {\n        patch(null, newBranch, suspense.hiddenContainer, null, parentComponent, suspense, namespace, slotScopeIds, optimized);\n        if (suspense.deps <= 0) {\n          suspense.resolve();\n        } else {\n          patch(activeBranch, newFallback, container, anchor, parentComponent, null,\n          // fallback tree will not have suspense context\n          namespace, slotScopeIds, optimized);\n          setActiveBranch(suspense, newFallback);\n        }\n      } else if (activeBranch && isSameVNodeType(newBranch, activeBranch)) {\n        patch(activeBranch, newBranch, container, anchor, parentComponent, suspense, namespace, slotScopeIds, optimized);\n        suspense.resolve(true);\n      } else {\n        patch(null, newBranch, suspense.hiddenContainer, null, parentComponent, suspense, namespace, slotScopeIds, optimized);\n        if (suspense.deps <= 0) {\n          suspense.resolve();\n        }\n      }\n    }\n  } else {\n    if (activeBranch && isSameVNodeType(newBranch, activeBranch)) {\n      patch(activeBranch, newBranch, container, anchor, parentComponent, suspense, namespace, slotScopeIds, optimized);\n      setActiveBranch(suspense, newBranch);\n    } else {\n      triggerEvent(n2, \"onPending\");\n      suspense.pendingBranch = newBranch;\n      if (newBranch.shapeFlag & 512) {\n        suspense.pendingId = newBranch.component.suspenseId;\n      } else {\n        suspense.pendingId = suspenseId++;\n      }\n      patch(null, newBranch, suspense.hiddenContainer, null, parentComponent, suspense, namespace, slotScopeIds, optimized);\n      if (suspense.deps <= 0) {\n        suspense.resolve();\n      } else {\n        const {\n          timeout,\n          pendingId\n        } = suspense;\n        if (timeout > 0) {\n          setTimeout(() => {\n            if (suspense.pendingId === pendingId) {\n              suspense.fallback(newFallback);\n            }\n          }, timeout);\n        } else if (timeout === 0) {\n          suspense.fallback(newFallback);\n        }\n      }\n    }\n  }\n}\nlet hasWarned = false;\nfunction createSuspenseBoundary(vnode, parentSuspense, parentComponent, container, hiddenContainer, anchor, namespace, slotScopeIds, optimized, rendererInternals, isHydrating = false) {\n  if (!!(process.env.NODE_ENV !== \"production\") && true && !hasWarned) {\n    hasWarned = true;\n    console[console.info ? \"info\" : \"log\"](`<Suspense> is an experimental feature and its API will likely change.`);\n  }\n  const {\n    p: patch,\n    m: move,\n    um: unmount,\n    n: next,\n    o: {\n      parentNode,\n      remove\n    }\n  } = rendererInternals;\n  let parentSuspenseId;\n  const isSuspensible = isVNodeSuspensible(vnode);\n  if (isSuspensible) {\n    if (parentSuspense && parentSuspense.pendingBranch) {\n      parentSuspenseId = parentSuspense.pendingId;\n      parentSuspense.deps++;\n    }\n  }\n  const timeout = vnode.props ? toNumber(vnode.props.timeout) : void 0;\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    assertNumber(timeout, `Suspense timeout`);\n  }\n  const initialAnchor = anchor;\n  const suspense = {\n    vnode,\n    parent: parentSuspense,\n    parentComponent,\n    namespace,\n    container,\n    hiddenContainer,\n    deps: 0,\n    pendingId: suspenseId++,\n    timeout: typeof timeout === \"number\" ? timeout : -1,\n    activeBranch: null,\n    pendingBranch: null,\n    isInFallback: !isHydrating,\n    isHydrating,\n    isUnmounted: false,\n    effects: [],\n    resolve(resume = false, sync = false) {\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        if (!resume && !suspense.pendingBranch) {\n          throw new Error(`suspense.resolve() is called without a pending branch.`);\n        }\n        if (suspense.isUnmounted) {\n          throw new Error(`suspense.resolve() is called on an already unmounted suspense boundary.`);\n        }\n      }\n      const {\n        vnode: vnode2,\n        activeBranch,\n        pendingBranch,\n        pendingId,\n        effects,\n        parentComponent: parentComponent2,\n        container: container2\n      } = suspense;\n      let delayEnter = false;\n      if (suspense.isHydrating) {\n        suspense.isHydrating = false;\n      } else if (!resume) {\n        delayEnter = activeBranch && pendingBranch.transition && pendingBranch.transition.mode === \"out-in\";\n        if (delayEnter) {\n          activeBranch.transition.afterLeave = () => {\n            if (pendingId === suspense.pendingId) {\n              move(pendingBranch, container2, anchor === initialAnchor ? next(activeBranch) : anchor, 0);\n              queuePostFlushCb(effects);\n            }\n          };\n        }\n        if (activeBranch) {\n          if (parentNode(activeBranch.el) === container2) {\n            anchor = next(activeBranch);\n          }\n          unmount(activeBranch, parentComponent2, suspense, true);\n        }\n        if (!delayEnter) {\n          move(pendingBranch, container2, anchor, 0);\n        }\n      }\n      setActiveBranch(suspense, pendingBranch);\n      suspense.pendingBranch = null;\n      suspense.isInFallback = false;\n      let parent = suspense.parent;\n      let hasUnresolvedAncestor = false;\n      while (parent) {\n        if (parent.pendingBranch) {\n          parent.effects.push(...effects);\n          hasUnresolvedAncestor = true;\n          break;\n        }\n        parent = parent.parent;\n      }\n      if (!hasUnresolvedAncestor && !delayEnter) {\n        queuePostFlushCb(effects);\n      }\n      suspense.effects = [];\n      if (isSuspensible) {\n        if (parentSuspense && parentSuspense.pendingBranch && parentSuspenseId === parentSuspense.pendingId) {\n          parentSuspense.deps--;\n          if (parentSuspense.deps === 0 && !sync) {\n            parentSuspense.resolve();\n          }\n        }\n      }\n      triggerEvent(vnode2, \"onResolve\");\n    },\n    fallback(fallbackVNode) {\n      if (!suspense.pendingBranch) {\n        return;\n      }\n      const {\n        vnode: vnode2,\n        activeBranch,\n        parentComponent: parentComponent2,\n        container: container2,\n        namespace: namespace2\n      } = suspense;\n      triggerEvent(vnode2, \"onFallback\");\n      const anchor2 = next(activeBranch);\n      const mountFallback = () => {\n        if (!suspense.isInFallback) {\n          return;\n        }\n        patch(null, fallbackVNode, container2, anchor2, parentComponent2, null,\n        // fallback tree will not have suspense context\n        namespace2, slotScopeIds, optimized);\n        setActiveBranch(suspense, fallbackVNode);\n      };\n      const delayEnter = fallbackVNode.transition && fallbackVNode.transition.mode === \"out-in\";\n      if (delayEnter) {\n        activeBranch.transition.afterLeave = mountFallback;\n      }\n      suspense.isInFallback = true;\n      unmount(activeBranch, parentComponent2, null,\n      // no suspense so unmount hooks fire now\n      true\n      // shouldRemove\n      );\n      if (!delayEnter) {\n        mountFallback();\n      }\n    },\n    move(container2, anchor2, type) {\n      suspense.activeBranch && move(suspense.activeBranch, container2, anchor2, type);\n      suspense.container = container2;\n    },\n    next() {\n      return suspense.activeBranch && next(suspense.activeBranch);\n    },\n    registerDep(instance, setupRenderEffect, optimized2) {\n      const isInPendingSuspense = !!suspense.pendingBranch;\n      if (isInPendingSuspense) {\n        suspense.deps++;\n      }\n      const hydratedEl = instance.vnode.el;\n      instance.asyncDep.catch(err => {\n        handleError(err, instance, 0);\n      }).then(asyncSetupResult => {\n        if (instance.isUnmounted || suspense.isUnmounted || suspense.pendingId !== instance.suspenseId) {\n          return;\n        }\n        instance.asyncResolved = true;\n        const {\n          vnode: vnode2\n        } = instance;\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          pushWarningContext(vnode2);\n        }\n        handleSetupResult(instance, asyncSetupResult, false);\n        if (hydratedEl) {\n          vnode2.el = hydratedEl;\n        }\n        const placeholder = !hydratedEl && instance.subTree.el;\n        setupRenderEffect(instance, vnode2,\n        // component may have been moved before resolve.\n        // if this is not a hydration, instance.subTree will be the comment\n        // placeholder.\n        parentNode(hydratedEl || instance.subTree.el),\n        // anchor will not be used if this is hydration, so only need to\n        // consider the comment placeholder case.\n        hydratedEl ? null : next(instance.subTree), suspense, namespace, optimized2);\n        if (placeholder) {\n          remove(placeholder);\n        }\n        updateHOCHostEl(instance, vnode2.el);\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          popWarningContext();\n        }\n        if (isInPendingSuspense && --suspense.deps === 0) {\n          suspense.resolve();\n        }\n      });\n    },\n    unmount(parentSuspense2, doRemove) {\n      suspense.isUnmounted = true;\n      if (suspense.activeBranch) {\n        unmount(suspense.activeBranch, parentComponent, parentSuspense2, doRemove);\n      }\n      if (suspense.pendingBranch) {\n        unmount(suspense.pendingBranch, parentComponent, parentSuspense2, doRemove);\n      }\n    }\n  };\n  return suspense;\n}\nfunction hydrateSuspense(node, vnode, parentComponent, parentSuspense, namespace, slotScopeIds, optimized, rendererInternals, hydrateNode) {\n  const suspense = vnode.suspense = createSuspenseBoundary(vnode, parentSuspense, parentComponent, node.parentNode,\n  // eslint-disable-next-line no-restricted-globals\n  document.createElement(\"div\"), null, namespace, slotScopeIds, optimized, rendererInternals, true);\n  const result = hydrateNode(node, suspense.pendingBranch = vnode.ssContent, parentComponent, suspense, slotScopeIds, optimized);\n  if (suspense.deps === 0) {\n    suspense.resolve(false, true);\n  }\n  return result;\n}\nfunction normalizeSuspenseChildren(vnode) {\n  const {\n    shapeFlag,\n    children\n  } = vnode;\n  const isSlotChildren = shapeFlag & 32;\n  vnode.ssContent = normalizeSuspenseSlot(isSlotChildren ? children.default : children);\n  vnode.ssFallback = isSlotChildren ? normalizeSuspenseSlot(children.fallback) : createVNode(Comment);\n}\nfunction normalizeSuspenseSlot(s) {\n  let block;\n  if (isFunction(s)) {\n    const trackBlock = isBlockTreeEnabled && s._c;\n    if (trackBlock) {\n      s._d = false;\n      openBlock();\n    }\n    s = s();\n    if (trackBlock) {\n      s._d = true;\n      block = currentBlock;\n      closeBlock();\n    }\n  }\n  if (isArray(s)) {\n    const singleChild = filterSingleRoot(s);\n    if (!!(process.env.NODE_ENV !== \"production\") && !singleChild && s.filter(child => child !== NULL_DYNAMIC_COMPONENT).length > 0) {\n      warn$1(`<Suspense> slots expect a single root node.`);\n    }\n    s = singleChild;\n  }\n  s = normalizeVNode(s);\n  if (block && !s.dynamicChildren) {\n    s.dynamicChildren = block.filter(c => c !== s);\n  }\n  return s;\n}\nfunction queueEffectWithSuspense(fn, suspense) {\n  if (suspense && suspense.pendingBranch) {\n    if (isArray(fn)) {\n      suspense.effects.push(...fn);\n    } else {\n      suspense.effects.push(fn);\n    }\n  } else {\n    queuePostFlushCb(fn);\n  }\n}\nfunction setActiveBranch(suspense, branch) {\n  suspense.activeBranch = branch;\n  const {\n    vnode,\n    parentComponent\n  } = suspense;\n  let el = branch.el;\n  while (!el && branch.component) {\n    branch = branch.component.subTree;\n    el = branch.el;\n  }\n  vnode.el = el;\n  if (parentComponent && parentComponent.subTree === vnode) {\n    parentComponent.vnode.el = el;\n    updateHOCHostEl(parentComponent, el);\n  }\n}\nfunction isVNodeSuspensible(vnode) {\n  const suspensible = vnode.props && vnode.props.suspensible;\n  return suspensible != null && suspensible !== false;\n}\nconst Fragment = Symbol.for(\"v-fgt\");\nconst Text = Symbol.for(\"v-txt\");\nconst Comment = Symbol.for(\"v-cmt\");\nconst Static = Symbol.for(\"v-stc\");\nconst blockStack = [];\nlet currentBlock = null;\nfunction openBlock(disableTracking = false) {\n  blockStack.push(currentBlock = disableTracking ? null : []);\n}\nfunction closeBlock() {\n  blockStack.pop();\n  currentBlock = blockStack[blockStack.length - 1] || null;\n}\nlet isBlockTreeEnabled = 1;\nfunction setBlockTracking(value, inVOnce = false) {\n  isBlockTreeEnabled += value;\n  if (value < 0 && currentBlock && inVOnce) {\n    currentBlock.hasOnce = true;\n  }\n}\nfunction setupBlock(vnode) {\n  vnode.dynamicChildren = isBlockTreeEnabled > 0 ? currentBlock || EMPTY_ARR : null;\n  closeBlock();\n  if (isBlockTreeEnabled > 0 && currentBlock) {\n    currentBlock.push(vnode);\n  }\n  return vnode;\n}\nfunction createElementBlock(type, props, children, patchFlag, dynamicProps, shapeFlag) {\n  return setupBlock(createBaseVNode(type, props, children, patchFlag, dynamicProps, shapeFlag, true));\n}\nfunction createBlock(type, props, children, patchFlag, dynamicProps) {\n  return setupBlock(createVNode(type, props, children, patchFlag, dynamicProps, true));\n}\nfunction isVNode(value) {\n  return value ? value.__v_isVNode === true : false;\n}\nfunction isSameVNodeType(n1, n2) {\n  if (!!(process.env.NODE_ENV !== \"production\") && n2.shapeFlag & 6 && n1.component) {\n    const dirtyInstances = hmrDirtyComponents.get(n2.type);\n    if (dirtyInstances && dirtyInstances.has(n1.component)) {\n      n1.shapeFlag &= ~256;\n      n2.shapeFlag &= ~512;\n      return false;\n    }\n  }\n  return n1.type === n2.type && n1.key === n2.key;\n}\nlet vnodeArgsTransformer;\nfunction transformVNodeArgs(transformer) {\n  vnodeArgsTransformer = transformer;\n}\nconst createVNodeWithArgsTransform = (...args) => {\n  return _createVNode(...(vnodeArgsTransformer ? vnodeArgsTransformer(args, currentRenderingInstance) : args));\n};\nconst normalizeKey = ({\n  key\n}) => key != null ? key : null;\nconst normalizeRef = ({\n  ref,\n  ref_key,\n  ref_for\n}) => {\n  if (typeof ref === \"number\") {\n    ref = \"\" + ref;\n  }\n  return ref != null ? isString(ref) || isRef(ref) || isFunction(ref) ? {\n    i: currentRenderingInstance,\n    r: ref,\n    k: ref_key,\n    f: !!ref_for\n  } : ref : null;\n};\nfunction createBaseVNode(type, props = null, children = null, patchFlag = 0, dynamicProps = null, shapeFlag = type === Fragment ? 0 : 1, isBlockNode = false, needFullChildrenNormalization = false) {\n  const vnode = {\n    __v_isVNode: true,\n    __v_skip: true,\n    type,\n    props,\n    key: props && normalizeKey(props),\n    ref: props && normalizeRef(props),\n    scopeId: currentScopeId,\n    slotScopeIds: null,\n    children,\n    component: null,\n    suspense: null,\n    ssContent: null,\n    ssFallback: null,\n    dirs: null,\n    transition: null,\n    el: null,\n    anchor: null,\n    target: null,\n    targetStart: null,\n    targetAnchor: null,\n    staticCount: 0,\n    shapeFlag,\n    patchFlag,\n    dynamicProps,\n    dynamicChildren: null,\n    appContext: null,\n    ctx: currentRenderingInstance\n  };\n  if (needFullChildrenNormalization) {\n    normalizeChildren(vnode, children);\n    if (shapeFlag & 128) {\n      type.normalize(vnode);\n    }\n  } else if (children) {\n    vnode.shapeFlag |= isString(children) ? 8 : 16;\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") && vnode.key !== vnode.key) {\n    warn$1(`VNode created with invalid key (NaN). VNode type:`, vnode.type);\n  }\n  if (isBlockTreeEnabled > 0 &&\n  // avoid a block node from tracking itself\n  !isBlockNode &&\n  // has current parent block\n  currentBlock && (\n  // presence of a patch flag indicates this node needs patching on updates.\n  // component nodes also should always be patched, because even if the\n  // component doesn't need to update, it needs to persist the instance on to\n  // the next vnode so that it can be properly unmounted later.\n  vnode.patchFlag > 0 || shapeFlag & 6) &&\n  // the EVENTS flag is only for hydration and if it is the only flag, the\n  // vnode should not be considered dynamic due to handler caching.\n  vnode.patchFlag !== 32) {\n    currentBlock.push(vnode);\n  }\n  return vnode;\n}\nconst createVNode = !!(process.env.NODE_ENV !== \"production\") ? createVNodeWithArgsTransform : _createVNode;\nfunction _createVNode(type, props = null, children = null, patchFlag = 0, dynamicProps = null, isBlockNode = false) {\n  if (!type || type === NULL_DYNAMIC_COMPONENT) {\n    if (!!(process.env.NODE_ENV !== \"production\") && !type) {\n      warn$1(`Invalid vnode type when creating vnode: ${type}.`);\n    }\n    type = Comment;\n  }\n  if (isVNode(type)) {\n    const cloned = cloneVNode(type, props, true\n    /* mergeRef: true */);\n    if (children) {\n      normalizeChildren(cloned, children);\n    }\n    if (isBlockTreeEnabled > 0 && !isBlockNode && currentBlock) {\n      if (cloned.shapeFlag & 6) {\n        currentBlock[currentBlock.indexOf(type)] = cloned;\n      } else {\n        currentBlock.push(cloned);\n      }\n    }\n    cloned.patchFlag = -2;\n    return cloned;\n  }\n  if (isClassComponent(type)) {\n    type = type.__vccOpts;\n  }\n  if (props) {\n    props = guardReactiveProps(props);\n    let {\n      class: klass,\n      style\n    } = props;\n    if (klass && !isString(klass)) {\n      props.class = normalizeClass(klass);\n    }\n    if (isObject(style)) {\n      if (isProxy(style) && !isArray(style)) {\n        style = extend({}, style);\n      }\n      props.style = normalizeStyle(style);\n    }\n  }\n  const shapeFlag = isString(type) ? 1 : isSuspense(type) ? 128 : isTeleport(type) ? 64 : isObject(type) ? 4 : isFunction(type) ? 2 : 0;\n  if (!!(process.env.NODE_ENV !== \"production\") && shapeFlag & 4 && isProxy(type)) {\n    type = toRaw(type);\n    warn$1(`Vue received a Component that was made a reactive object. This can lead to unnecessary performance overhead and should be avoided by marking the component with \\`markRaw\\` or using \\`shallowRef\\` instead of \\`ref\\`.`, `\nComponent that was made reactive: `, type);\n  }\n  return createBaseVNode(type, props, children, patchFlag, dynamicProps, shapeFlag, isBlockNode, true);\n}\nfunction guardReactiveProps(props) {\n  if (!props) return null;\n  return isProxy(props) || isInternalObject(props) ? extend({}, props) : props;\n}\nfunction cloneVNode(vnode, extraProps, mergeRef = false, cloneTransition = false) {\n  const {\n    props,\n    ref,\n    patchFlag,\n    children,\n    transition\n  } = vnode;\n  const mergedProps = extraProps ? mergeProps(props || {}, extraProps) : props;\n  const cloned = {\n    __v_isVNode: true,\n    __v_skip: true,\n    type: vnode.type,\n    props: mergedProps,\n    key: mergedProps && normalizeKey(mergedProps),\n    ref: extraProps && extraProps.ref ?\n    // #2078 in the case of <component :is=\"vnode\" ref=\"extra\"/>\n    // if the vnode itself already has a ref, cloneVNode will need to merge\n    // the refs so the single vnode can be set on multiple refs\n    mergeRef && ref ? isArray(ref) ? ref.concat(normalizeRef(extraProps)) : [ref, normalizeRef(extraProps)] : normalizeRef(extraProps) : ref,\n    scopeId: vnode.scopeId,\n    slotScopeIds: vnode.slotScopeIds,\n    children: !!(process.env.NODE_ENV !== \"production\") && patchFlag === -1 && isArray(children) ? children.map(deepCloneVNode) : children,\n    target: vnode.target,\n    targetStart: vnode.targetStart,\n    targetAnchor: vnode.targetAnchor,\n    staticCount: vnode.staticCount,\n    shapeFlag: vnode.shapeFlag,\n    // if the vnode is cloned with extra props, we can no longer assume its\n    // existing patch flag to be reliable and need to add the FULL_PROPS flag.\n    // note: preserve flag for fragments since they use the flag for children\n    // fast paths only.\n    patchFlag: extraProps && vnode.type !== Fragment ? patchFlag === -1 ? 16 : patchFlag | 16 : patchFlag,\n    dynamicProps: vnode.dynamicProps,\n    dynamicChildren: vnode.dynamicChildren,\n    appContext: vnode.appContext,\n    dirs: vnode.dirs,\n    transition,\n    // These should technically only be non-null on mounted VNodes. However,\n    // they *should* be copied for kept-alive vnodes. So we just always copy\n    // them since them being non-null during a mount doesn't affect the logic as\n    // they will simply be overwritten.\n    component: vnode.component,\n    suspense: vnode.suspense,\n    ssContent: vnode.ssContent && cloneVNode(vnode.ssContent),\n    ssFallback: vnode.ssFallback && cloneVNode(vnode.ssFallback),\n    el: vnode.el,\n    anchor: vnode.anchor,\n    ctx: vnode.ctx,\n    ce: vnode.ce\n  };\n  if (transition && cloneTransition) {\n    setTransitionHooks(cloned, transition.clone(cloned));\n  }\n  return cloned;\n}\nfunction deepCloneVNode(vnode) {\n  const cloned = cloneVNode(vnode);\n  if (isArray(vnode.children)) {\n    cloned.children = vnode.children.map(deepCloneVNode);\n  }\n  return cloned;\n}\nfunction createTextVNode(text = \" \", flag = 0) {\n  return createVNode(Text, null, text, flag);\n}\nfunction createStaticVNode(content, numberOfNodes) {\n  const vnode = createVNode(Static, null, content);\n  vnode.staticCount = numberOfNodes;\n  return vnode;\n}\nfunction createCommentVNode(text = \"\", asBlock = false) {\n  return asBlock ? (openBlock(), createBlock(Comment, null, text)) : createVNode(Comment, null, text);\n}\nfunction normalizeVNode(child) {\n  if (child == null || typeof child === \"boolean\") {\n    return createVNode(Comment);\n  } else if (isArray(child)) {\n    return createVNode(Fragment, null,\n    // #3666, avoid reference pollution when reusing vnode\n    child.slice());\n  } else if (isVNode(child)) {\n    return cloneIfMounted(child);\n  } else {\n    return createVNode(Text, null, String(child));\n  }\n}\nfunction cloneIfMounted(child) {\n  return child.el === null && child.patchFlag !== -1 || child.memo ? child : cloneVNode(child);\n}\nfunction normalizeChildren(vnode, children) {\n  let type = 0;\n  const {\n    shapeFlag\n  } = vnode;\n  if (children == null) {\n    children = null;\n  } else if (isArray(children)) {\n    type = 16;\n  } else if (typeof children === \"object\") {\n    if (shapeFlag & (1 | 64)) {\n      const slot = children.default;\n      if (slot) {\n        slot._c && (slot._d = false);\n        normalizeChildren(vnode, slot());\n        slot._c && (slot._d = true);\n      }\n      return;\n    } else {\n      type = 32;\n      const slotFlag = children._;\n      if (!slotFlag && !isInternalObject(children)) {\n        children._ctx = currentRenderingInstance;\n      } else if (slotFlag === 3 && currentRenderingInstance) {\n        if (currentRenderingInstance.slots._ === 1) {\n          children._ = 1;\n        } else {\n          children._ = 2;\n          vnode.patchFlag |= 1024;\n        }\n      }\n    }\n  } else if (isFunction(children)) {\n    children = {\n      default: children,\n      _ctx: currentRenderingInstance\n    };\n    type = 32;\n  } else {\n    children = String(children);\n    if (shapeFlag & 64) {\n      type = 16;\n      children = [createTextVNode(children)];\n    } else {\n      type = 8;\n    }\n  }\n  vnode.children = children;\n  vnode.shapeFlag |= type;\n}\nfunction mergeProps(...args) {\n  const ret = {};\n  for (let i = 0; i < args.length; i++) {\n    const toMerge = args[i];\n    for (const key in toMerge) {\n      if (key === \"class\") {\n        if (ret.class !== toMerge.class) {\n          ret.class = normalizeClass([ret.class, toMerge.class]);\n        }\n      } else if (key === \"style\") {\n        ret.style = normalizeStyle([ret.style, toMerge.style]);\n      } else if (isOn(key)) {\n        const existing = ret[key];\n        const incoming = toMerge[key];\n        if (incoming && existing !== incoming && !(isArray(existing) && existing.includes(incoming))) {\n          ret[key] = existing ? [].concat(existing, incoming) : incoming;\n        }\n      } else if (key !== \"\") {\n        ret[key] = toMerge[key];\n      }\n    }\n  }\n  return ret;\n}\nfunction invokeVNodeHook(hook, instance, vnode, prevVNode = null) {\n  callWithAsyncErrorHandling(hook, instance, 7, [vnode, prevVNode]);\n}\nconst emptyAppContext = createAppContext();\nlet uid = 0;\nfunction createComponentInstance(vnode, parent, suspense) {\n  const type = vnode.type;\n  const appContext = (parent ? parent.appContext : vnode.appContext) || emptyAppContext;\n  const instance = {\n    uid: uid++,\n    vnode,\n    type,\n    parent,\n    appContext,\n    root: null,\n    // to be immediately set\n    next: null,\n    subTree: null,\n    // will be set synchronously right after creation\n    effect: null,\n    update: null,\n    // will be set synchronously right after creation\n    job: null,\n    scope: new EffectScope(true\n    /* detached */),\n    render: null,\n    proxy: null,\n    exposed: null,\n    exposeProxy: null,\n    withProxy: null,\n    provides: parent ? parent.provides : Object.create(appContext.provides),\n    ids: parent ? parent.ids : [\"\", 0, 0],\n    accessCache: null,\n    renderCache: [],\n    // local resolved assets\n    components: null,\n    directives: null,\n    // resolved props and emits options\n    propsOptions: normalizePropsOptions(type, appContext),\n    emitsOptions: normalizeEmitsOptions(type, appContext),\n    // emit\n    emit: null,\n    // to be set immediately\n    emitted: null,\n    // props default value\n    propsDefaults: EMPTY_OBJ,\n    // inheritAttrs\n    inheritAttrs: type.inheritAttrs,\n    // state\n    ctx: EMPTY_OBJ,\n    data: EMPTY_OBJ,\n    props: EMPTY_OBJ,\n    attrs: EMPTY_OBJ,\n    slots: EMPTY_OBJ,\n    refs: EMPTY_OBJ,\n    setupState: EMPTY_OBJ,\n    setupContext: null,\n    // suspense related\n    suspense,\n    suspenseId: suspense ? suspense.pendingId : 0,\n    asyncDep: null,\n    asyncResolved: false,\n    // lifecycle hooks\n    // not using enums here because it results in computed properties\n    isMounted: false,\n    isUnmounted: false,\n    isDeactivated: false,\n    bc: null,\n    c: null,\n    bm: null,\n    m: null,\n    bu: null,\n    u: null,\n    um: null,\n    bum: null,\n    da: null,\n    a: null,\n    rtg: null,\n    rtc: null,\n    ec: null,\n    sp: null\n  };\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    instance.ctx = createDevRenderContext(instance);\n  } else {\n    instance.ctx = {\n      _: instance\n    };\n  }\n  instance.root = parent ? parent.root : instance;\n  instance.emit = emit.bind(null, instance);\n  if (vnode.ce) {\n    vnode.ce(instance);\n  }\n  return instance;\n}\nlet currentInstance = null;\nconst getCurrentInstance = () => currentInstance || currentRenderingInstance;\nlet internalSetCurrentInstance;\nlet setInSSRSetupState;\n{\n  const g = getGlobalThis();\n  const registerGlobalSetter = (key, setter) => {\n    let setters;\n    if (!(setters = g[key])) setters = g[key] = [];\n    setters.push(setter);\n    return v => {\n      if (setters.length > 1) setters.forEach(set => set(v));else setters[0](v);\n    };\n  };\n  internalSetCurrentInstance = registerGlobalSetter(`__VUE_INSTANCE_SETTERS__`, v => currentInstance = v);\n  setInSSRSetupState = registerGlobalSetter(`__VUE_SSR_SETTERS__`, v => isInSSRComponentSetup = v);\n}\nconst setCurrentInstance = instance => {\n  const prev = currentInstance;\n  internalSetCurrentInstance(instance);\n  instance.scope.on();\n  return () => {\n    instance.scope.off();\n    internalSetCurrentInstance(prev);\n  };\n};\nconst unsetCurrentInstance = () => {\n  currentInstance && currentInstance.scope.off();\n  internalSetCurrentInstance(null);\n};\nconst isBuiltInTag = /* @__PURE__ */makeMap(\"slot,component\");\nfunction validateComponentName(name, {\n  isNativeTag\n}) {\n  if (isBuiltInTag(name) || isNativeTag(name)) {\n    warn$1(\"Do not use built-in or reserved HTML elements as component id: \" + name);\n  }\n}\nfunction isStatefulComponent(instance) {\n  return instance.vnode.shapeFlag & 4;\n}\nlet isInSSRComponentSetup = false;\nfunction setupComponent(instance, isSSR = false, optimized = false) {\n  isSSR && setInSSRSetupState(isSSR);\n  const {\n    props,\n    children\n  } = instance.vnode;\n  const isStateful = isStatefulComponent(instance);\n  initProps(instance, props, isStateful, isSSR);\n  initSlots(instance, children, optimized);\n  const setupResult = isStateful ? setupStatefulComponent(instance, isSSR) : void 0;\n  isSSR && setInSSRSetupState(false);\n  return setupResult;\n}\nfunction setupStatefulComponent(instance, isSSR) {\n  var _a;\n  const Component = instance.type;\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    if (Component.name) {\n      validateComponentName(Component.name, instance.appContext.config);\n    }\n    if (Component.components) {\n      const names = Object.keys(Component.components);\n      for (let i = 0; i < names.length; i++) {\n        validateComponentName(names[i], instance.appContext.config);\n      }\n    }\n    if (Component.directives) {\n      const names = Object.keys(Component.directives);\n      for (let i = 0; i < names.length; i++) {\n        validateDirectiveName(names[i]);\n      }\n    }\n    if (Component.compilerOptions && isRuntimeOnly()) {\n      warn$1(`\"compilerOptions\" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.`);\n    }\n  }\n  instance.accessCache = /* @__PURE__ */Object.create(null);\n  instance.proxy = new Proxy(instance.ctx, PublicInstanceProxyHandlers);\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    exposePropsOnRenderContext(instance);\n  }\n  const {\n    setup\n  } = Component;\n  if (setup) {\n    pauseTracking();\n    const setupContext = instance.setupContext = setup.length > 1 ? createSetupContext(instance) : null;\n    const reset = setCurrentInstance(instance);\n    const setupResult = callWithErrorHandling(setup, instance, 0, [!!(process.env.NODE_ENV !== \"production\") ? shallowReadonly(instance.props) : instance.props, setupContext]);\n    const isAsyncSetup = isPromise(setupResult);\n    resetTracking();\n    reset();\n    if ((isAsyncSetup || instance.sp) && !isAsyncWrapper(instance)) {\n      markAsyncBoundary(instance);\n    }\n    if (isAsyncSetup) {\n      setupResult.then(unsetCurrentInstance, unsetCurrentInstance);\n      if (isSSR) {\n        return setupResult.then(resolvedResult => {\n          handleSetupResult(instance, resolvedResult, isSSR);\n        }).catch(e => {\n          handleError(e, instance, 0);\n        });\n      } else {\n        instance.asyncDep = setupResult;\n        if (!!(process.env.NODE_ENV !== \"production\") && !instance.suspense) {\n          const name = (_a = Component.name) != null ? _a : \"Anonymous\";\n          warn$1(`Component <${name}>: setup function returned a promise, but no <Suspense> boundary was found in the parent component tree. A component with async setup() must be nested in a <Suspense> in order to be rendered.`);\n        }\n      }\n    } else {\n      handleSetupResult(instance, setupResult, isSSR);\n    }\n  } else {\n    finishComponentSetup(instance, isSSR);\n  }\n}\nfunction handleSetupResult(instance, setupResult, isSSR) {\n  if (isFunction(setupResult)) {\n    if (instance.type.__ssrInlineRender) {\n      instance.ssrRender = setupResult;\n    } else {\n      instance.render = setupResult;\n    }\n  } else if (isObject(setupResult)) {\n    if (!!(process.env.NODE_ENV !== \"production\") && isVNode(setupResult)) {\n      warn$1(`setup() should not return VNodes directly - return a render function instead.`);\n    }\n    if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n      instance.devtoolsRawSetupState = setupResult;\n    }\n    instance.setupState = proxyRefs(setupResult);\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      exposeSetupStateOnRenderContext(instance);\n    }\n  } else if (!!(process.env.NODE_ENV !== \"production\") && setupResult !== void 0) {\n    warn$1(`setup() should return an object. Received: ${setupResult === null ? \"null\" : typeof setupResult}`);\n  }\n  finishComponentSetup(instance, isSSR);\n}\nlet compile;\nlet installWithProxy;\nfunction registerRuntimeCompiler(_compile) {\n  compile = _compile;\n  installWithProxy = i => {\n    if (i.render._rc) {\n      i.withProxy = new Proxy(i.ctx, RuntimeCompiledPublicInstanceProxyHandlers);\n    }\n  };\n}\nconst isRuntimeOnly = () => !compile;\nfunction finishComponentSetup(instance, isSSR, skipOptions) {\n  const Component = instance.type;\n  if (!instance.render) {\n    if (!isSSR && compile && !Component.render) {\n      const template = Component.template || __VUE_OPTIONS_API__ && resolveMergedOptions(instance).template;\n      if (template) {\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          startMeasure(instance, `compile`);\n        }\n        const {\n          isCustomElement,\n          compilerOptions\n        } = instance.appContext.config;\n        const {\n          delimiters,\n          compilerOptions: componentCompilerOptions\n        } = Component;\n        const finalCompilerOptions = extend(extend({\n          isCustomElement,\n          delimiters\n        }, compilerOptions), componentCompilerOptions);\n        Component.render = compile(template, finalCompilerOptions);\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          endMeasure(instance, `compile`);\n        }\n      }\n    }\n    instance.render = Component.render || NOOP;\n    if (installWithProxy) {\n      installWithProxy(instance);\n    }\n  }\n  if (__VUE_OPTIONS_API__ && true) {\n    const reset = setCurrentInstance(instance);\n    pauseTracking();\n    try {\n      applyOptions(instance);\n    } finally {\n      resetTracking();\n      reset();\n    }\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") && !Component.render && instance.render === NOOP && !isSSR) {\n    if (!compile && Component.template) {\n      warn$1(`Component provided template option but runtime compilation is not supported in this build of Vue.` + ` Configure your bundler to alias \"vue\" to \"vue/dist/vue.esm-bundler.js\".`);\n    } else {\n      warn$1(`Component is missing template or render function: `, Component);\n    }\n  }\n}\nconst attrsProxyHandlers = !!(process.env.NODE_ENV !== \"production\") ? {\n  get(target, key) {\n    markAttrsAccessed();\n    track(target, \"get\", \"\");\n    return target[key];\n  },\n  set() {\n    warn$1(`setupContext.attrs is readonly.`);\n    return false;\n  },\n  deleteProperty() {\n    warn$1(`setupContext.attrs is readonly.`);\n    return false;\n  }\n} : {\n  get(target, key) {\n    track(target, \"get\", \"\");\n    return target[key];\n  }\n};\nfunction getSlotsProxy(instance) {\n  return new Proxy(instance.slots, {\n    get(target, key) {\n      track(instance, \"get\", \"$slots\");\n      return target[key];\n    }\n  });\n}\nfunction createSetupContext(instance) {\n  const expose = exposed => {\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      if (instance.exposed) {\n        warn$1(`expose() should be called only once per setup().`);\n      }\n      if (exposed != null) {\n        let exposedType = typeof exposed;\n        if (exposedType === \"object\") {\n          if (isArray(exposed)) {\n            exposedType = \"array\";\n          } else if (isRef(exposed)) {\n            exposedType = \"ref\";\n          }\n        }\n        if (exposedType !== \"object\") {\n          warn$1(`expose() should be passed a plain object, received ${exposedType}.`);\n        }\n      }\n    }\n    instance.exposed = exposed || {};\n  };\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    let attrsProxy;\n    let slotsProxy;\n    return Object.freeze({\n      get attrs() {\n        return attrsProxy || (attrsProxy = new Proxy(instance.attrs, attrsProxyHandlers));\n      },\n      get slots() {\n        return slotsProxy || (slotsProxy = getSlotsProxy(instance));\n      },\n      get emit() {\n        return (event, ...args) => instance.emit(event, ...args);\n      },\n      expose\n    });\n  } else {\n    return {\n      attrs: new Proxy(instance.attrs, attrsProxyHandlers),\n      slots: instance.slots,\n      emit: instance.emit,\n      expose\n    };\n  }\n}\nfunction getComponentPublicInstance(instance) {\n  if (instance.exposed) {\n    return instance.exposeProxy || (instance.exposeProxy = new Proxy(proxyRefs(markRaw(instance.exposed)), {\n      get(target, key) {\n        if (key in target) {\n          return target[key];\n        } else if (key in publicPropertiesMap) {\n          return publicPropertiesMap[key](instance);\n        }\n      },\n      has(target, key) {\n        return key in target || key in publicPropertiesMap;\n      }\n    }));\n  } else {\n    return instance.proxy;\n  }\n}\nconst classifyRE = /(?:^|[-_])(\\w)/g;\nconst classify = str => str.replace(classifyRE, c => c.toUpperCase()).replace(/[-_]/g, \"\");\nfunction getComponentName(Component, includeInferred = true) {\n  return isFunction(Component) ? Component.displayName || Component.name : Component.name || includeInferred && Component.__name;\n}\nfunction formatComponentName(instance, Component, isRoot = false) {\n  let name = getComponentName(Component);\n  if (!name && Component.__file) {\n    const match = Component.__file.match(/([^/\\\\]+)\\.\\w+$/);\n    if (match) {\n      name = match[1];\n    }\n  }\n  if (!name && instance && instance.parent) {\n    const inferFromRegistry = registry => {\n      for (const key in registry) {\n        if (registry[key] === Component) {\n          return key;\n        }\n      }\n    };\n    name = inferFromRegistry(instance.components || instance.parent.type.components) || inferFromRegistry(instance.appContext.components);\n  }\n  return name ? classify(name) : isRoot ? `App` : `Anonymous`;\n}\nfunction isClassComponent(value) {\n  return isFunction(value) && \"__vccOpts\" in value;\n}\nconst computed = (getterOrOptions, debugOptions) => {\n  const c = computed$1(getterOrOptions, debugOptions, isInSSRComponentSetup);\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    const i = getCurrentInstance();\n    if (i && i.appContext.config.warnRecursiveComputed) {\n      c._warnRecursive = true;\n    }\n  }\n  return c;\n};\nfunction h(type, propsOrChildren, children) {\n  const l = arguments.length;\n  if (l === 2) {\n    if (isObject(propsOrChildren) && !isArray(propsOrChildren)) {\n      if (isVNode(propsOrChildren)) {\n        return createVNode(type, null, [propsOrChildren]);\n      }\n      return createVNode(type, propsOrChildren);\n    } else {\n      return createVNode(type, null, propsOrChildren);\n    }\n  } else {\n    if (l > 3) {\n      children = Array.prototype.slice.call(arguments, 2);\n    } else if (l === 3 && isVNode(children)) {\n      children = [children];\n    }\n    return createVNode(type, propsOrChildren, children);\n  }\n}\nfunction initCustomFormatter() {\n  if (!!!(process.env.NODE_ENV !== \"production\") || typeof window === \"undefined\") {\n    return;\n  }\n  const vueStyle = {\n    style: \"color:#3ba776\"\n  };\n  const numberStyle = {\n    style: \"color:#1677ff\"\n  };\n  const stringStyle = {\n    style: \"color:#f5222d\"\n  };\n  const keywordStyle = {\n    style: \"color:#eb2f96\"\n  };\n  const formatter = {\n    __vue_custom_formatter: true,\n    header(obj) {\n      if (!isObject(obj)) {\n        return null;\n      }\n      if (obj.__isVue) {\n        return [\"div\", vueStyle, `VueInstance`];\n      } else if (isRef(obj)) {\n        return [\"div\", {}, [\"span\", vueStyle, genRefFlag(obj)], \"<\",\n        // avoid debugger accessing value affecting behavior\n        formatValue(\"_value\" in obj ? obj._value : obj), `>`];\n      } else if (isReactive(obj)) {\n        return [\"div\", {}, [\"span\", vueStyle, isShallow(obj) ? \"ShallowReactive\" : \"Reactive\"], \"<\", formatValue(obj), `>${isReadonly(obj) ? ` (readonly)` : ``}`];\n      } else if (isReadonly(obj)) {\n        return [\"div\", {}, [\"span\", vueStyle, isShallow(obj) ? \"ShallowReadonly\" : \"Readonly\"], \"<\", formatValue(obj), \">\"];\n      }\n      return null;\n    },\n    hasBody(obj) {\n      return obj && obj.__isVue;\n    },\n    body(obj) {\n      if (obj && obj.__isVue) {\n        return [\"div\", {}, ...formatInstance(obj.$)];\n      }\n    }\n  };\n  function formatInstance(instance) {\n    const blocks = [];\n    if (instance.type.props && instance.props) {\n      blocks.push(createInstanceBlock(\"props\", toRaw(instance.props)));\n    }\n    if (instance.setupState !== EMPTY_OBJ) {\n      blocks.push(createInstanceBlock(\"setup\", instance.setupState));\n    }\n    if (instance.data !== EMPTY_OBJ) {\n      blocks.push(createInstanceBlock(\"data\", toRaw(instance.data)));\n    }\n    const computed = extractKeys(instance, \"computed\");\n    if (computed) {\n      blocks.push(createInstanceBlock(\"computed\", computed));\n    }\n    const injected = extractKeys(instance, \"inject\");\n    if (injected) {\n      blocks.push(createInstanceBlock(\"injected\", injected));\n    }\n    blocks.push([\"div\", {}, [\"span\", {\n      style: keywordStyle.style + \";opacity:0.66\"\n    }, \"$ (internal): \"], [\"object\", {\n      object: instance\n    }]]);\n    return blocks;\n  }\n  function createInstanceBlock(type, target) {\n    target = extend({}, target);\n    if (!Object.keys(target).length) {\n      return [\"span\", {}];\n    }\n    return [\"div\", {\n      style: \"line-height:1.25em;margin-bottom:0.6em\"\n    }, [\"div\", {\n      style: \"color:#476582\"\n    }, type], [\"div\", {\n      style: \"padding-left:1.25em\"\n    }, ...Object.keys(target).map(key => {\n      return [\"div\", {}, [\"span\", keywordStyle, key + \": \"], formatValue(target[key], false)];\n    })]];\n  }\n  function formatValue(v, asRaw = true) {\n    if (typeof v === \"number\") {\n      return [\"span\", numberStyle, v];\n    } else if (typeof v === \"string\") {\n      return [\"span\", stringStyle, JSON.stringify(v)];\n    } else if (typeof v === \"boolean\") {\n      return [\"span\", keywordStyle, v];\n    } else if (isObject(v)) {\n      return [\"object\", {\n        object: asRaw ? toRaw(v) : v\n      }];\n    } else {\n      return [\"span\", stringStyle, String(v)];\n    }\n  }\n  function extractKeys(instance, type) {\n    const Comp = instance.type;\n    if (isFunction(Comp)) {\n      return;\n    }\n    const extracted = {};\n    for (const key in instance.ctx) {\n      if (isKeyOfType(Comp, key, type)) {\n        extracted[key] = instance.ctx[key];\n      }\n    }\n    return extracted;\n  }\n  function isKeyOfType(Comp, key, type) {\n    const opts = Comp[type];\n    if (isArray(opts) && opts.includes(key) || isObject(opts) && key in opts) {\n      return true;\n    }\n    if (Comp.extends && isKeyOfType(Comp.extends, key, type)) {\n      return true;\n    }\n    if (Comp.mixins && Comp.mixins.some(m => isKeyOfType(m, key, type))) {\n      return true;\n    }\n  }\n  function genRefFlag(v) {\n    if (isShallow(v)) {\n      return `ShallowRef`;\n    }\n    if (v.effect) {\n      return `ComputedRef`;\n    }\n    return `Ref`;\n  }\n  if (window.devtoolsFormatters) {\n    window.devtoolsFormatters.push(formatter);\n  } else {\n    window.devtoolsFormatters = [formatter];\n  }\n}\nfunction withMemo(memo, render, cache, index) {\n  const cached = cache[index];\n  if (cached && isMemoSame(cached, memo)) {\n    return cached;\n  }\n  const ret = render();\n  ret.memo = memo.slice();\n  ret.cacheIndex = index;\n  return cache[index] = ret;\n}\nfunction isMemoSame(cached, memo) {\n  const prev = cached.memo;\n  if (prev.length != memo.length) {\n    return false;\n  }\n  for (let i = 0; i < prev.length; i++) {\n    if (hasChanged(prev[i], memo[i])) {\n      return false;\n    }\n  }\n  if (isBlockTreeEnabled > 0 && currentBlock) {\n    currentBlock.push(cached);\n  }\n  return true;\n}\nconst version = \"3.5.13\";\nconst warn = !!(process.env.NODE_ENV !== \"production\") ? warn$1 : NOOP;\nconst ErrorTypeStrings = ErrorTypeStrings$1;\nconst devtools = !!(process.env.NODE_ENV !== \"production\") || true ? devtools$1 : void 0;\nconst setDevtoolsHook = !!(process.env.NODE_ENV !== \"production\") || true ? setDevtoolsHook$1 : NOOP;\nconst _ssrUtils = {\n  createComponentInstance,\n  setupComponent,\n  renderComponentRoot,\n  setCurrentRenderingInstance,\n  isVNode: isVNode,\n  normalizeVNode,\n  getComponentPublicInstance,\n  ensureValidVNode,\n  pushWarningContext,\n  popWarningContext\n};\nconst ssrUtils = _ssrUtils;\nconst resolveFilter = null;\nconst compatUtils = null;\nconst DeprecationTypes = null;\nexport { BaseTransition, BaseTransitionPropsValidators, Comment, DeprecationTypes, ErrorCodes, ErrorTypeStrings, Fragment, KeepAlive, Static, Suspense, Teleport, Text, assertNumber, callWithAsyncErrorHandling, callWithErrorHandling, cloneVNode, compatUtils, computed, createBlock, createCommentVNode, createElementBlock, createBaseVNode as createElementVNode, createHydrationRenderer, createPropsRestProxy, createRenderer, createSlots, createStaticVNode, createTextVNode, createVNode, defineAsyncComponent, defineComponent, defineEmits, defineExpose, defineModel, defineOptions, defineProps, defineSlots, devtools, getCurrentInstance, getTransitionRawChildren, guardReactiveProps, h, handleError, hasInjectionContext, hydrateOnIdle, hydrateOnInteraction, hydrateOnMediaQuery, hydrateOnVisible, initCustomFormatter, inject, isMemoSame, isRuntimeOnly, isVNode, mergeDefaults, mergeModels, mergeProps, nextTick, onActivated, onBeforeMount, onBeforeUnmount, onBeforeUpdate, onDeactivated, onErrorCaptured, onMounted, onRenderTracked, onRenderTriggered, onServerPrefetch, onUnmounted, onUpdated, openBlock, popScopeId, provide, pushScopeId, queuePostFlushCb, registerRuntimeCompiler, renderList, renderSlot, resolveComponent, resolveDirective, resolveDynamicComponent, resolveFilter, resolveTransitionHooks, setBlockTracking, setDevtoolsHook, setTransitionHooks, ssrContextKey, ssrUtils, toHandlers, transformVNodeArgs, useAttrs, useId, useModel, useSSRContext, useSlots, useTemplateRef, useTransitionState, version, warn, watch, watchEffect, watchPostEffect, watchSyncEffect, withAsyncContext, withCtx, withDefaults, withDirectives, withMemo, withScopeId };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}