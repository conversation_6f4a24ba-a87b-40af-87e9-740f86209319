{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nconst imageProps = buildProps({\n  hideOnClickModal: Boolean,\n  src: {\n    type: String,\n    default: \"\"\n  },\n  fit: {\n    type: String,\n    values: [\"\", \"contain\", \"cover\", \"fill\", \"none\", \"scale-down\"],\n    default: \"\"\n  },\n  loading: {\n    type: String,\n    values: [\"eager\", \"lazy\"]\n  },\n  lazy: Bo<PERSON>an,\n  scrollContainer: {\n    type: definePropType([String, Object])\n  },\n  previewSrcList: {\n    type: definePropType(Array),\n    default: () => mutable([])\n  },\n  previewTeleported: Boolean,\n  zIndex: {\n    type: Number\n  },\n  initialIndex: {\n    type: Number,\n    default: 0\n  },\n  infinite: {\n    type: <PERSON>olean,\n    default: true\n  },\n  closeOnPressEscape: {\n    type: Boolean,\n    default: true\n  },\n  zoomRate: {\n    type: Number,\n    default: 1.2\n  },\n  minScale: {\n    type: Number,\n    default: 0.2\n  },\n  maxScale: {\n    type: Number,\n    default: 7\n  },\n  showProgress: {\n    type: Boolean,\n    default: false\n  },\n  crossorigin: {\n    type: definePropType(String)\n  }\n});\nconst imageEmits = {\n  load: evt => evt instanceof Event,\n  error: evt => evt instanceof Event,\n  switch: val => isNumber(val),\n  close: () => true,\n  show: () => true\n};\nexport { imageEmits, imageProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}