{"ast": null, "code": "import { defineComponent, ref, computed, watch, onMounted, onBeforeUnmount, provide, renderSlot, unref } from 'vue';\nimport { useTimeoutFn } from '@vueuse/core';\nimport { TOOLTIP_V2_OPEN, tooltipV2RootKey } from './constants.mjs';\nimport { tooltipV2RootProps } from './root.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { isPropAbsent, isNumber } from '../../../utils/types.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTooltipV2Root\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: tooltipV2RootProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const _open = ref(props.defaultOpen);\n    const triggerRef = ref(null);\n    const open = computed({\n      get: () => isPropAbsent(props.open) ? _open.value : props.open,\n      set: open2 => {\n        var _a;\n        _open.value = open2;\n        (_a = props[\"onUpdate:open\"]) == null ? void 0 : _a.call(props, open2);\n      }\n    });\n    const isOpenDelayed = computed(() => isNumber(props.delayDuration) && props.delayDuration > 0);\n    const {\n      start: onDelayedOpen,\n      stop: clearTimer\n    } = useTimeoutFn(() => {\n      open.value = true;\n    }, computed(() => props.delayDuration), {\n      immediate: false\n    });\n    const ns = useNamespace(\"tooltip-v2\");\n    const contentId = useId();\n    const onNormalOpen = () => {\n      clearTimer();\n      open.value = true;\n    };\n    const onDelayOpen = () => {\n      unref(isOpenDelayed) ? onDelayedOpen() : onNormalOpen();\n    };\n    const onOpen = onNormalOpen;\n    const onClose = () => {\n      clearTimer();\n      open.value = false;\n    };\n    const onChange = open2 => {\n      var _a;\n      if (open2) {\n        document.dispatchEvent(new CustomEvent(TOOLTIP_V2_OPEN));\n        onOpen();\n      }\n      (_a = props.onOpenChange) == null ? void 0 : _a.call(props, open2);\n    };\n    watch(open, onChange);\n    onMounted(() => {\n      document.addEventListener(TOOLTIP_V2_OPEN, onClose);\n    });\n    onBeforeUnmount(() => {\n      clearTimer();\n      document.removeEventListener(TOOLTIP_V2_OPEN, onClose);\n    });\n    provide(tooltipV2RootKey, {\n      contentId,\n      triggerRef,\n      ns,\n      onClose,\n      onDelayOpen,\n      onOpen\n    });\n    expose({\n      onOpen,\n      onClose\n    });\n    return (_ctx, _cache) => {\n      return renderSlot(_ctx.$slots, \"default\", {\n        open: unref(open)\n      });\n    };\n  }\n});\nvar TooltipV2Root = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"root.vue\"]]);\nexport { TooltipV2Root as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}