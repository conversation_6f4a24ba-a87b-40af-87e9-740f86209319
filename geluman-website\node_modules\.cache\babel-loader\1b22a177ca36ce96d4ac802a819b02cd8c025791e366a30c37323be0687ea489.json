{"ast": null, "code": "import { pick } from 'lodash-unified';\nimport { buildProps } from '../../utils/vue/props/runtime.mjs';\nconst ariaProps = buildProps({\n  ariaLabel: String,\n  ariaOrientation: {\n    type: String,\n    values: [\"horizontal\", \"vertical\", \"undefined\"]\n  },\n  ariaControls: String\n});\nconst useAriaProps = arias => {\n  return pick(ariaProps, arias);\n};\nexport { ariaProps, useAriaProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}