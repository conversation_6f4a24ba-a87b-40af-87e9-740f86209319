{"ast": null, "code": "import { ref, onMounted, watch } from 'vue';\nimport { isNumber, isUndefined } from '../../utils/types.mjs';\nimport { isObject } from '@vue/shared';\nconst useThrottleRender = (loading, throttle = 0) => {\n  if (throttle === 0) return loading;\n  const initVal = isObject(throttle) && Boolean(throttle.initVal);\n  const throttled = ref(initVal);\n  let timeoutHandle = null;\n  const dispatchThrottling = timer => {\n    if (isUndefined(timer)) {\n      throttled.value = loading.value;\n      return;\n    }\n    if (timeoutHandle) {\n      clearTimeout(timeoutHandle);\n    }\n    timeoutHandle = setTimeout(() => {\n      throttled.value = loading.value;\n    }, timer);\n  };\n  const dispatcher = type => {\n    if (type === \"leading\") {\n      if (isNumber(throttle)) {\n        dispatchThrottling(throttle);\n      } else {\n        dispatchThrottling(throttle.leading);\n      }\n    } else {\n      if (isObject(throttle)) {\n        dispatchThrottling(throttle.trailing);\n      } else {\n        throttled.value = false;\n      }\n    }\n  };\n  onMounted(() => dispatcher(\"leading\"));\n  watch(() => loading.value, val => {\n    dispatcher(val ? \"leading\" : \"trailing\");\n  });\n  return throttled;\n};\nexport { useThrottleRender };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}