{"ast": null, "code": "import { LEFT_CHECK_CHANGE_EVENT, RIGHT_CHECK_CHANGE_EVENT } from '../transfer.mjs';\nconst useCheckedChange = (checkedState, emit) => {\n  const onSourceCheckedChange = (val, movedKeys) => {\n    checkedState.leftChecked = val;\n    if (!movedKeys) return;\n    emit(LEFT_CHECK_CHANGE_EVENT, val, movedKeys);\n  };\n  const onTargetCheckedChange = (val, movedKeys) => {\n    checkedState.rightChecked = val;\n    if (!movedKeys) return;\n    emit(RIGHT_CHECK_CHANGE_EVENT, val, movedKeys);\n  };\n  return {\n    onSourceCheckedChange,\n    onTargetCheckedChange\n  };\n};\nexport { useCheckedChange };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}