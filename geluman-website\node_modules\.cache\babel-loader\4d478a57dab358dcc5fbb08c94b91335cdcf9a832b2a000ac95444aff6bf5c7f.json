{"ast": null, "code": "import { defineComponent, markRaw, ref, effectScope, shallowRef, computed, watch, nextTick, onMounted, openBlock, createBlock, unref, withCtx, createVNode, Transition, createElementVNode, normalizeClass, normalizeStyle, withModifiers, createCommentVNode, createElementBlock, Fragment, renderSlot, createTextVNode, toDisplayString, resolveDynamicComponent, renderList, withDirectives, vShow } from 'vue';\nimport { useEventListener } from '@vueuse/core';\nimport { throttle } from 'lodash-unified';\nimport ElFocusTrap from '../../focus-trap/src/focus-trap.mjs';\nimport { ElTeleport } from '../../teleport/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { FullScreen, ScaleToOriginal, Close, ArrowLeft, ArrowRight, ZoomOut, ZoomIn, RefreshLeft, RefreshRight } from '@element-plus/icons-vue';\nimport { imageViewerProps, imageViewerEmits } from './image-viewer.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useZIndex } from '../../../hooks/use-z-index/index.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { keysOf } from '../../../utils/objects.mjs';\nconst __default__ = defineComponent({\n  name: \"ElImageViewer\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: imageViewerProps,\n  emits: imageViewerEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    var _a;\n    const props = __props;\n    const modes = {\n      CONTAIN: {\n        name: \"contain\",\n        icon: markRaw(FullScreen)\n      },\n      ORIGINAL: {\n        name: \"original\",\n        icon: markRaw(ScaleToOriginal)\n      }\n    };\n    let stopWheelListener;\n    let prevOverflow = \"\";\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"image-viewer\");\n    const {\n      nextZIndex\n    } = useZIndex();\n    const wrapper = ref();\n    const imgRefs = ref([]);\n    const scopeEventListener = effectScope();\n    const loading = ref(true);\n    const activeIndex = ref(props.initialIndex);\n    const mode = shallowRef(modes.CONTAIN);\n    const transform = ref({\n      scale: 1,\n      deg: 0,\n      offsetX: 0,\n      offsetY: 0,\n      enableTransition: false\n    });\n    const zIndex = ref((_a = props.zIndex) != null ? _a : nextZIndex());\n    const isSingle = computed(() => {\n      const {\n        urlList\n      } = props;\n      return urlList.length <= 1;\n    });\n    const isFirst = computed(() => activeIndex.value === 0);\n    const isLast = computed(() => activeIndex.value === props.urlList.length - 1);\n    const currentImg = computed(() => props.urlList[activeIndex.value]);\n    const arrowPrevKls = computed(() => [ns.e(\"btn\"), ns.e(\"prev\"), ns.is(\"disabled\", !props.infinite && isFirst.value)]);\n    const arrowNextKls = computed(() => [ns.e(\"btn\"), ns.e(\"next\"), ns.is(\"disabled\", !props.infinite && isLast.value)]);\n    const imgStyle = computed(() => {\n      const {\n        scale,\n        deg,\n        offsetX,\n        offsetY,\n        enableTransition\n      } = transform.value;\n      let translateX = offsetX / scale;\n      let translateY = offsetY / scale;\n      const radian = deg * Math.PI / 180;\n      const cosRadian = Math.cos(radian);\n      const sinRadian = Math.sin(radian);\n      translateX = translateX * cosRadian + translateY * sinRadian;\n      translateY = translateY * cosRadian - offsetX / scale * sinRadian;\n      const style = {\n        transform: `scale(${scale}) rotate(${deg}deg) translate(${translateX}px, ${translateY}px)`,\n        transition: enableTransition ? \"transform .3s\" : \"\"\n      };\n      if (mode.value.name === modes.CONTAIN.name) {\n        style.maxWidth = style.maxHeight = \"100%\";\n      }\n      return style;\n    });\n    const progress = computed(() => `${activeIndex.value + 1} / ${props.urlList.length}`);\n    function hide() {\n      unregisterEventListener();\n      stopWheelListener == null ? void 0 : stopWheelListener();\n      document.body.style.overflow = prevOverflow;\n      emit(\"close\");\n    }\n    function registerEventListener() {\n      const keydownHandler = throttle(e => {\n        switch (e.code) {\n          case EVENT_CODE.esc:\n            props.closeOnPressEscape && hide();\n            break;\n          case EVENT_CODE.space:\n            toggleMode();\n            break;\n          case EVENT_CODE.left:\n            prev();\n            break;\n          case EVENT_CODE.up:\n            handleActions(\"zoomIn\");\n            break;\n          case EVENT_CODE.right:\n            next();\n            break;\n          case EVENT_CODE.down:\n            handleActions(\"zoomOut\");\n            break;\n        }\n      });\n      const mousewheelHandler = throttle(e => {\n        const delta = e.deltaY || e.deltaX;\n        handleActions(delta < 0 ? \"zoomIn\" : \"zoomOut\", {\n          zoomRate: props.zoomRate,\n          enableTransition: false\n        });\n      });\n      scopeEventListener.run(() => {\n        useEventListener(document, \"keydown\", keydownHandler);\n        useEventListener(document, \"wheel\", mousewheelHandler);\n      });\n    }\n    function unregisterEventListener() {\n      scopeEventListener.stop();\n    }\n    function handleImgLoad() {\n      loading.value = false;\n    }\n    function handleImgError(e) {\n      loading.value = false;\n      e.target.alt = t(\"el.image.error\");\n    }\n    function handleMouseDown(e) {\n      if (loading.value || e.button !== 0 || !wrapper.value) return;\n      transform.value.enableTransition = false;\n      const {\n        offsetX,\n        offsetY\n      } = transform.value;\n      const startX = e.pageX;\n      const startY = e.pageY;\n      const dragHandler = throttle(ev => {\n        transform.value = {\n          ...transform.value,\n          offsetX: offsetX + ev.pageX - startX,\n          offsetY: offsetY + ev.pageY - startY\n        };\n      });\n      const removeMousemove = useEventListener(document, \"mousemove\", dragHandler);\n      useEventListener(document, \"mouseup\", () => {\n        removeMousemove();\n      });\n      e.preventDefault();\n    }\n    function reset() {\n      transform.value = {\n        scale: 1,\n        deg: 0,\n        offsetX: 0,\n        offsetY: 0,\n        enableTransition: false\n      };\n    }\n    function toggleMode() {\n      if (loading.value) return;\n      const modeNames = keysOf(modes);\n      const modeValues = Object.values(modes);\n      const currentMode = mode.value.name;\n      const index = modeValues.findIndex(i => i.name === currentMode);\n      const nextIndex = (index + 1) % modeNames.length;\n      mode.value = modes[modeNames[nextIndex]];\n      reset();\n    }\n    function setActiveItem(index) {\n      const len = props.urlList.length;\n      activeIndex.value = (index + len) % len;\n    }\n    function prev() {\n      if (isFirst.value && !props.infinite) return;\n      setActiveItem(activeIndex.value - 1);\n    }\n    function next() {\n      if (isLast.value && !props.infinite) return;\n      setActiveItem(activeIndex.value + 1);\n    }\n    function handleActions(action, options = {}) {\n      if (loading.value) return;\n      const {\n        minScale,\n        maxScale\n      } = props;\n      const {\n        zoomRate,\n        rotateDeg,\n        enableTransition\n      } = {\n        zoomRate: props.zoomRate,\n        rotateDeg: 90,\n        enableTransition: true,\n        ...options\n      };\n      switch (action) {\n        case \"zoomOut\":\n          if (transform.value.scale > minScale) {\n            transform.value.scale = Number.parseFloat((transform.value.scale / zoomRate).toFixed(3));\n          }\n          break;\n        case \"zoomIn\":\n          if (transform.value.scale < maxScale) {\n            transform.value.scale = Number.parseFloat((transform.value.scale * zoomRate).toFixed(3));\n          }\n          break;\n        case \"clockwise\":\n          transform.value.deg += rotateDeg;\n          emit(\"rotate\", transform.value.deg);\n          break;\n        case \"anticlockwise\":\n          transform.value.deg -= rotateDeg;\n          emit(\"rotate\", transform.value.deg);\n          break;\n      }\n      transform.value.enableTransition = enableTransition;\n    }\n    function onFocusoutPrevented(event) {\n      var _a2;\n      if (((_a2 = event.detail) == null ? void 0 : _a2.focusReason) === \"pointer\") {\n        event.preventDefault();\n      }\n    }\n    function onCloseRequested() {\n      if (props.closeOnPressEscape) {\n        hide();\n      }\n    }\n    function wheelHandler(e) {\n      if (!e.ctrlKey) return;\n      if (e.deltaY < 0) {\n        e.preventDefault();\n        return false;\n      } else if (e.deltaY > 0) {\n        e.preventDefault();\n        return false;\n      }\n    }\n    watch(currentImg, () => {\n      nextTick(() => {\n        const $img = imgRefs.value[0];\n        if (!($img == null ? void 0 : $img.complete)) {\n          loading.value = true;\n        }\n      });\n    });\n    watch(activeIndex, val => {\n      reset();\n      emit(\"switch\", val);\n    });\n    onMounted(() => {\n      registerEventListener();\n      stopWheelListener = useEventListener(\"wheel\", wheelHandler, {\n        passive: false\n      });\n      prevOverflow = document.body.style.overflow;\n      document.body.style.overflow = \"hidden\";\n    });\n    expose({\n      setActiveItem\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElTeleport), {\n        to: \"body\",\n        disabled: !_ctx.teleported\n      }, {\n        default: withCtx(() => [createVNode(Transition, {\n          name: \"viewer-fade\",\n          appear: \"\"\n        }, {\n          default: withCtx(() => [createElementVNode(\"div\", {\n            ref_key: \"wrapper\",\n            ref: wrapper,\n            tabindex: -1,\n            class: normalizeClass(unref(ns).e(\"wrapper\")),\n            style: normalizeStyle({\n              zIndex: zIndex.value\n            })\n          }, [createVNode(unref(ElFocusTrap), {\n            loop: \"\",\n            trapped: \"\",\n            \"focus-trap-el\": wrapper.value,\n            \"focus-start-el\": \"container\",\n            onFocusoutPrevented,\n            onReleaseRequested: onCloseRequested\n          }, {\n            default: withCtx(() => [createElementVNode(\"div\", {\n              class: normalizeClass(unref(ns).e(\"mask\")),\n              onClick: withModifiers($event => _ctx.hideOnClickModal && hide(), [\"self\"])\n            }, null, 10, [\"onClick\"]), createCommentVNode(\" CLOSE \"), createElementVNode(\"span\", {\n              class: normalizeClass([unref(ns).e(\"btn\"), unref(ns).e(\"close\")]),\n              onClick: hide\n            }, [createVNode(unref(ElIcon), null, {\n              default: withCtx(() => [createVNode(unref(Close))]),\n              _: 1\n            })], 2), createCommentVNode(\" ARROW \"), !unref(isSingle) ? (openBlock(), createElementBlock(Fragment, {\n              key: 0\n            }, [createElementVNode(\"span\", {\n              class: normalizeClass(unref(arrowPrevKls)),\n              onClick: prev\n            }, [createVNode(unref(ElIcon), null, {\n              default: withCtx(() => [createVNode(unref(ArrowLeft))]),\n              _: 1\n            })], 2), createElementVNode(\"span\", {\n              class: normalizeClass(unref(arrowNextKls)),\n              onClick: next\n            }, [createVNode(unref(ElIcon), null, {\n              default: withCtx(() => [createVNode(unref(ArrowRight))]),\n              _: 1\n            })], 2)], 64)) : createCommentVNode(\"v-if\", true), _ctx.$slots.progress || _ctx.showProgress ? (openBlock(), createElementBlock(\"div\", {\n              key: 1,\n              class: normalizeClass([unref(ns).e(\"btn\"), unref(ns).e(\"progress\")])\n            }, [renderSlot(_ctx.$slots, \"progress\", {\n              activeIndex: activeIndex.value,\n              total: _ctx.urlList.length\n            }, () => [createTextVNode(toDisplayString(unref(progress)), 1)])], 2)) : createCommentVNode(\"v-if\", true), createCommentVNode(\" ACTIONS \"), createElementVNode(\"div\", {\n              class: normalizeClass([unref(ns).e(\"btn\"), unref(ns).e(\"actions\")])\n            }, [createElementVNode(\"div\", {\n              class: normalizeClass(unref(ns).e(\"actions__inner\"))\n            }, [renderSlot(_ctx.$slots, \"toolbar\", {\n              actions: handleActions,\n              prev,\n              next,\n              reset: toggleMode,\n              activeIndex: activeIndex.value,\n              setActiveItem\n            }, () => [createVNode(unref(ElIcon), {\n              onClick: $event => handleActions(\"zoomOut\")\n            }, {\n              default: withCtx(() => [createVNode(unref(ZoomOut))]),\n              _: 1\n            }, 8, [\"onClick\"]), createVNode(unref(ElIcon), {\n              onClick: $event => handleActions(\"zoomIn\")\n            }, {\n              default: withCtx(() => [createVNode(unref(ZoomIn))]),\n              _: 1\n            }, 8, [\"onClick\"]), createElementVNode(\"i\", {\n              class: normalizeClass(unref(ns).e(\"actions__divider\"))\n            }, null, 2), createVNode(unref(ElIcon), {\n              onClick: toggleMode\n            }, {\n              default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(mode).icon)))]),\n              _: 1\n            }), createElementVNode(\"i\", {\n              class: normalizeClass(unref(ns).e(\"actions__divider\"))\n            }, null, 2), createVNode(unref(ElIcon), {\n              onClick: $event => handleActions(\"anticlockwise\")\n            }, {\n              default: withCtx(() => [createVNode(unref(RefreshLeft))]),\n              _: 1\n            }, 8, [\"onClick\"]), createVNode(unref(ElIcon), {\n              onClick: $event => handleActions(\"clockwise\")\n            }, {\n              default: withCtx(() => [createVNode(unref(RefreshRight))]),\n              _: 1\n            }, 8, [\"onClick\"])])], 2)], 2), createCommentVNode(\" CANVAS \"), createElementVNode(\"div\", {\n              class: normalizeClass(unref(ns).e(\"canvas\"))\n            }, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.urlList, (url, i) => {\n              return withDirectives((openBlock(), createElementBlock(\"img\", {\n                ref_for: true,\n                ref: el => imgRefs.value[i] = el,\n                key: url,\n                src: url,\n                style: normalizeStyle(unref(imgStyle)),\n                class: normalizeClass(unref(ns).e(\"img\")),\n                crossorigin: _ctx.crossorigin,\n                onLoad: handleImgLoad,\n                onError: handleImgError,\n                onMousedown: handleMouseDown\n              }, null, 46, [\"src\", \"crossorigin\"])), [[vShow, i === activeIndex.value]]);\n            }), 128))], 2), renderSlot(_ctx.$slots, \"default\")]),\n            _: 3\n          }, 8, [\"focus-trap-el\"])], 6)]),\n          _: 3\n        })]),\n        _: 3\n      }, 8, [\"disabled\"]);\n    };\n  }\n});\nvar ImageViewer = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"image-viewer.vue\"]]);\nexport { ImageViewer as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}