{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, watch, provide, computed } from 'vue';\nimport { collapseContextKey } from './constants.mjs';\nimport { castArray } from 'lodash-unified';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst useCollapse = (props, emit) => {\n  const activeNames = ref(castArray(props.modelValue));\n  const setActiveNames = _activeNames => {\n    activeNames.value = _activeNames;\n    const value = props.accordion ? activeNames.value[0] : activeNames.value;\n    emit(UPDATE_MODEL_EVENT, value);\n    emit(CHANGE_EVENT, value);\n  };\n  const handleItemClick = name => {\n    if (props.accordion) {\n      setActiveNames([activeNames.value[0] === name ? \"\" : name]);\n    } else {\n      const _activeNames = [...activeNames.value];\n      const index = _activeNames.indexOf(name);\n      if (index > -1) {\n        _activeNames.splice(index, 1);\n      } else {\n        _activeNames.push(name);\n      }\n      setActiveNames(_activeNames);\n    }\n  };\n  watch(() => props.modelValue, () => activeNames.value = castArray(props.modelValue), {\n    deep: true\n  });\n  provide(collapseContextKey, {\n    activeNames,\n    handleItemClick\n  });\n  return {\n    activeNames,\n    setActiveNames\n  };\n};\nconst useCollapseDOM = () => {\n  const ns = useNamespace(\"collapse\");\n  const rootKls = computed(() => ns.b());\n  return {\n    rootKls\n  };\n};\nexport { useCollapse, useCollapseDOM };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}