{"ast": null, "code": "import { defineComponent, computed, provide, openBlock, createBlock, resolveDynamicComponent, normalizeClass, unref, normalizeStyle, withCtx, renderSlot } from 'vue';\nimport { rowContextKey } from './constants.mjs';\nimport { rowProps } from './row.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElRow\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: rowProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"row\");\n    const gutter = computed(() => props.gutter);\n    provide(rowContextKey, {\n      gutter\n    });\n    const style = computed(() => {\n      const styles = {};\n      if (!props.gutter) {\n        return styles;\n      }\n      styles.marginRight = styles.marginLeft = `-${props.gutter / 2}px`;\n      return styles;\n    });\n    const rowKls = computed(() => [ns.b(), ns.is(`justify-${props.justify}`, props.justify !== \"start\"), ns.is(`align-${props.align}`, !!props.align)]);\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(resolveDynamicComponent(_ctx.tag), {\n        class: normalizeClass(unref(rowKls)),\n        style: normalizeStyle(unref(style))\n      }, {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 8, [\"class\", \"style\"]);\n    };\n  }\n});\nvar Row = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"row.vue\"]]);\nexport { Row as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}