{"ast": null, "code": "import { defineComponent, inject, computed, openBlock, createElement<PERSON><PERSON>, normalizeClass, unref, withModifiers, createElementVNode, normalizeStyle, createBlock, withCtx, resolveDynamicComponent, createCommentVNode, createVNode } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { CaretRight } from '@element-plus/icons-vue';\nimport { ElCheckbox } from '../../checkbox/index.mjs';\nimport ElNodeContent from './tree-node-content.mjs';\nimport { treeNodeProps, treeNodeEmits, ROOT_TREE_INJECTION_KEY, NODE_CONTEXTMENU } from './virtual-tree.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isFunction, isString } from '@vue/shared';\nconst __default__ = defineComponent({\n  name: \"ElTreeNode\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: treeNodeProps,\n  emits: treeNodeEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const tree = inject(ROOT_TREE_INJECTION_KEY);\n    const ns = useNamespace(\"tree\");\n    const indent = computed(() => {\n      var _a;\n      return (_a = tree == null ? void 0 : tree.props.indent) != null ? _a : 16;\n    });\n    const icon = computed(() => {\n      var _a;\n      return (_a = tree == null ? void 0 : tree.props.icon) != null ? _a : CaretRight;\n    });\n    const getNodeClass = node => {\n      const nodeClassFunc = tree == null ? void 0 : tree.props.props.class;\n      if (!nodeClassFunc) return {};\n      let className;\n      if (isFunction(nodeClassFunc)) {\n        const {\n          data\n        } = node;\n        className = nodeClassFunc(data, node);\n      } else {\n        className = nodeClassFunc;\n      }\n      return isString(className) ? {\n        [className]: true\n      } : className;\n    };\n    const handleClick = e => {\n      emit(\"click\", props.node, e);\n    };\n    const handleDrop = e => {\n      emit(\"drop\", props.node, e);\n    };\n    const handleExpandIconClick = () => {\n      emit(\"toggle\", props.node);\n    };\n    const handleCheckChange = value => {\n      emit(\"check\", props.node, value);\n    };\n    const handleContextMenu = event => {\n      var _a, _b, _c, _d;\n      if ((_c = (_b = (_a = tree == null ? void 0 : tree.instance) == null ? void 0 : _a.vnode) == null ? void 0 : _b.props) == null ? void 0 : _c[\"onNodeContextmenu\"]) {\n        event.stopPropagation();\n        event.preventDefault();\n      }\n      tree == null ? void 0 : tree.ctx.emit(NODE_CONTEXTMENU, event, (_d = props.node) == null ? void 0 : _d.data, props.node);\n    };\n    return (_ctx, _cache) => {\n      var _a, _b, _c;\n      return openBlock(), createElementBlock(\"div\", {\n        ref: \"node$\",\n        class: normalizeClass([unref(ns).b(\"node\"), unref(ns).is(\"expanded\", _ctx.expanded), unref(ns).is(\"current\", _ctx.current), unref(ns).is(\"focusable\", !_ctx.disabled), unref(ns).is(\"checked\", !_ctx.disabled && _ctx.checked), getNodeClass(_ctx.node)]),\n        role: \"treeitem\",\n        tabindex: \"-1\",\n        \"aria-expanded\": _ctx.expanded,\n        \"aria-disabled\": _ctx.disabled,\n        \"aria-checked\": _ctx.checked,\n        \"data-key\": (_a = _ctx.node) == null ? void 0 : _a.key,\n        onClick: withModifiers(handleClick, [\"stop\"]),\n        onContextmenu: handleContextMenu,\n        onDragover: withModifiers(() => {}, [\"prevent\"]),\n        onDragenter: withModifiers(() => {}, [\"prevent\"]),\n        onDrop: withModifiers(handleDrop, [\"stop\"])\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).be(\"node\", \"content\")),\n        style: normalizeStyle({\n          paddingLeft: `${(_ctx.node.level - 1) * unref(indent)}px`,\n          height: _ctx.itemSize + \"px\"\n        })\n      }, [unref(icon) ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0,\n        class: normalizeClass([unref(ns).is(\"leaf\", !!((_b = _ctx.node) == null ? void 0 : _b.isLeaf)), unref(ns).is(\"hidden\", _ctx.hiddenExpandIcon), {\n          expanded: !((_c = _ctx.node) == null ? void 0 : _c.isLeaf) && _ctx.expanded\n        }, unref(ns).be(\"node\", \"expand-icon\")]),\n        onClick: withModifiers(handleExpandIconClick, [\"stop\"])\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(icon))))]),\n        _: 1\n      }, 8, [\"class\", \"onClick\"])) : createCommentVNode(\"v-if\", true), _ctx.showCheckbox ? (openBlock(), createBlock(unref(ElCheckbox), {\n        key: 1,\n        \"model-value\": _ctx.checked,\n        indeterminate: _ctx.indeterminate,\n        disabled: _ctx.disabled,\n        onChange: handleCheckChange,\n        onClick: withModifiers(() => {}, [\"stop\"])\n      }, null, 8, [\"model-value\", \"indeterminate\", \"disabled\", \"onClick\"])) : createCommentVNode(\"v-if\", true), createVNode(unref(ElNodeContent), {\n        node: _ctx.node\n      }, null, 8, [\"node\"])], 6)], 42, [\"aria-expanded\", \"aria-disabled\", \"aria-checked\", \"data-key\", \"onClick\", \"onDragover\", \"onDragenter\", \"onDrop\"]);\n    };\n  }\n});\nvar ElTreeNode = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tree-node.vue\"]]);\nexport { ElTreeNode as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}