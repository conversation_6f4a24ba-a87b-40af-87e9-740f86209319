{"ast": null, "code": "import { ArrowRight } from '@element-plus/icons-vue';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nconst collapseItemProps = buildProps({\n  title: {\n    type: String,\n    default: \"\"\n  },\n  name: {\n    type: definePropType([String, Number]),\n    default: void 0\n  },\n  icon: {\n    type: iconPropType,\n    default: ArrowRight\n  },\n  disabled: Boolean\n});\nexport { collapseItemProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}