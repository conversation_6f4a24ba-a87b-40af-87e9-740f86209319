{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { ref, isRef, nextTick } from 'vue';\nimport { isNull } from 'lodash-unified';\nimport { parseHeight } from './util.mjs';\nimport { hasOwn, isString } from '@vue/shared';\nimport { isClient } from '@vueuse/core';\nimport { isNumber } from '../../../utils/types.mjs';\nclass TableLayout {\n  constructor(options) {\n    this.observers = [];\n    this.table = null;\n    this.store = null;\n    this.columns = [];\n    this.fit = true;\n    this.showHeader = true;\n    this.height = ref(null);\n    this.scrollX = ref(false);\n    this.scrollY = ref(false);\n    this.bodyWidth = ref(null);\n    this.fixedWidth = ref(null);\n    this.rightFixedWidth = ref(null);\n    this.gutterWidth = 0;\n    for (const name in options) {\n      if (hasOwn(options, name)) {\n        if (isRef(this[name])) {\n          this[name].value = options[name];\n        } else {\n          this[name] = options[name];\n        }\n      }\n    }\n    if (!this.table) {\n      throw new Error(\"Table is required for Table Layout\");\n    }\n    if (!this.store) {\n      throw new Error(\"Store is required for Table Layout\");\n    }\n  }\n  updateScrollY() {\n    const height = this.height.value;\n    if (isNull(height)) return false;\n    const scrollBarRef = this.table.refs.scrollBarRef;\n    if (this.table.vnode.el && (scrollBarRef == null ? void 0 : scrollBarRef.wrapRef)) {\n      let scrollY = true;\n      const prevScrollY = this.scrollY.value;\n      scrollY = scrollBarRef.wrapRef.scrollHeight > scrollBarRef.wrapRef.clientHeight;\n      this.scrollY.value = scrollY;\n      return prevScrollY !== scrollY;\n    }\n    return false;\n  }\n  setHeight(value, prop = \"height\") {\n    if (!isClient) return;\n    const el = this.table.vnode.el;\n    value = parseHeight(value);\n    this.height.value = Number(value);\n    if (!el && (value || value === 0)) return nextTick(() => this.setHeight(value, prop));\n    if (isNumber(value)) {\n      el.style[prop] = `${value}px`;\n      this.updateElsHeight();\n    } else if (isString(value)) {\n      el.style[prop] = value;\n      this.updateElsHeight();\n    }\n  }\n  setMaxHeight(value) {\n    this.setHeight(value, \"max-height\");\n  }\n  getFlattenColumns() {\n    const flattenColumns = [];\n    const columns = this.table.store.states.columns.value;\n    columns.forEach(column => {\n      if (column.isColumnGroup) {\n        flattenColumns.push.apply(flattenColumns, column.columns);\n      } else {\n        flattenColumns.push(column);\n      }\n    });\n    return flattenColumns;\n  }\n  updateElsHeight() {\n    this.updateScrollY();\n    this.notifyObservers(\"scrollable\");\n  }\n  headerDisplayNone(elm) {\n    if (!elm) return true;\n    let headerChild = elm;\n    while (headerChild.tagName !== \"DIV\") {\n      if (getComputedStyle(headerChild).display === \"none\") {\n        return true;\n      }\n      headerChild = headerChild.parentElement;\n    }\n    return false;\n  }\n  updateColumnsWidth() {\n    if (!isClient) return;\n    const fit = this.fit;\n    const bodyWidth = this.table.vnode.el.clientWidth;\n    let bodyMinWidth = 0;\n    const flattenColumns = this.getFlattenColumns();\n    const flexColumns = flattenColumns.filter(column => !isNumber(column.width));\n    flattenColumns.forEach(column => {\n      if (isNumber(column.width) && column.realWidth) column.realWidth = null;\n    });\n    if (flexColumns.length > 0 && fit) {\n      flattenColumns.forEach(column => {\n        bodyMinWidth += Number(column.width || column.minWidth || 80);\n      });\n      if (bodyMinWidth <= bodyWidth) {\n        this.scrollX.value = false;\n        const totalFlexWidth = bodyWidth - bodyMinWidth;\n        if (flexColumns.length === 1) {\n          flexColumns[0].realWidth = Number(flexColumns[0].minWidth || 80) + totalFlexWidth;\n        } else {\n          const allColumnsWidth = flexColumns.reduce((prev, column) => prev + Number(column.minWidth || 80), 0);\n          const flexWidthPerPixel = totalFlexWidth / allColumnsWidth;\n          let noneFirstWidth = 0;\n          flexColumns.forEach((column, index) => {\n            if (index === 0) return;\n            const flexWidth = Math.floor(Number(column.minWidth || 80) * flexWidthPerPixel);\n            noneFirstWidth += flexWidth;\n            column.realWidth = Number(column.minWidth || 80) + flexWidth;\n          });\n          flexColumns[0].realWidth = Number(flexColumns[0].minWidth || 80) + totalFlexWidth - noneFirstWidth;\n        }\n      } else {\n        this.scrollX.value = true;\n        flexColumns.forEach(column => {\n          column.realWidth = Number(column.minWidth);\n        });\n      }\n      this.bodyWidth.value = Math.max(bodyMinWidth, bodyWidth);\n      this.table.state.resizeState.value.width = this.bodyWidth.value;\n    } else {\n      flattenColumns.forEach(column => {\n        if (!column.width && !column.minWidth) {\n          column.realWidth = 80;\n        } else {\n          column.realWidth = Number(column.width || column.minWidth);\n        }\n        bodyMinWidth += column.realWidth;\n      });\n      this.scrollX.value = bodyMinWidth > bodyWidth;\n      this.bodyWidth.value = bodyMinWidth;\n    }\n    const fixedColumns = this.store.states.fixedColumns.value;\n    if (fixedColumns.length > 0) {\n      let fixedWidth = 0;\n      fixedColumns.forEach(column => {\n        fixedWidth += Number(column.realWidth || column.width);\n      });\n      this.fixedWidth.value = fixedWidth;\n    }\n    const rightFixedColumns = this.store.states.rightFixedColumns.value;\n    if (rightFixedColumns.length > 0) {\n      let rightFixedWidth = 0;\n      rightFixedColumns.forEach(column => {\n        rightFixedWidth += Number(column.realWidth || column.width);\n      });\n      this.rightFixedWidth.value = rightFixedWidth;\n    }\n    this.notifyObservers(\"columns\");\n  }\n  addObserver(observer) {\n    this.observers.push(observer);\n  }\n  removeObserver(observer) {\n    const index = this.observers.indexOf(observer);\n    if (index !== -1) {\n      this.observers.splice(index, 1);\n    }\n  }\n  notifyObservers(event) {\n    const observers = this.observers;\n    observers.forEach(observer => {\n      var _a, _b;\n      switch (event) {\n        case \"columns\":\n          (_a = observer.state) == null ? void 0 : _a.onColumnsChange(this);\n          break;\n        case \"scrollable\":\n          (_b = observer.state) == null ? void 0 : _b.onScrollableChange(this);\n          break;\n        default:\n          throw new Error(`Table Layout don't have event ${event}.`);\n      }\n    });\n  }\n}\nexport { TableLayout as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}