{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { computed, unref } from 'vue';\nimport { sum, enforceUnit } from '../utils.mjs';\nimport { isNumber } from '../../../../utils/types.mjs';\nimport { addUnit } from '../../../../utils/dom/style.mjs';\nconst useStyles = (props, {\n  columnsTotalWidth,\n  rowsHeight,\n  fixedColumnsOnLeft,\n  fixedColumnsOnRight\n}) => {\n  const bodyWidth = computed(() => {\n    const {\n      fixed,\n      width,\n      vScrollbarSize\n    } = props;\n    const ret = width - vScrollbarSize;\n    return fixed ? Math.max(Math.round(unref(columnsTotalWidth)), ret) : ret;\n  });\n  const headerWidth = computed(() => unref(bodyWidth) + props.vScrollbarSize);\n  const mainTableHeight = computed(() => {\n    const {\n      height = 0,\n      maxHeight = 0,\n      footerHeight: footerHeight2,\n      hScrollbarSize\n    } = props;\n    if (maxHeight > 0) {\n      const _fixedRowsHeight = unref(fixedRowsHeight);\n      const _rowsHeight = unref(rowsHeight);\n      const _headerHeight = unref(headerHeight);\n      const total = _headerHeight + _fixedRowsHeight + _rowsHeight + hScrollbarSize;\n      return Math.min(total, maxHeight - footerHeight2);\n    }\n    return height - footerHeight2;\n  });\n  const fixedTableHeight = computed(() => {\n    const {\n      maxHeight\n    } = props;\n    const tableHeight = unref(mainTableHeight);\n    if (isNumber(maxHeight) && maxHeight > 0) return tableHeight;\n    const totalHeight = unref(rowsHeight) + unref(headerHeight) + unref(fixedRowsHeight);\n    return Math.min(tableHeight, totalHeight);\n  });\n  const mapColumn = column => column.width;\n  const leftTableWidth = computed(() => sum(unref(fixedColumnsOnLeft).map(mapColumn)));\n  const rightTableWidth = computed(() => sum(unref(fixedColumnsOnRight).map(mapColumn)));\n  const headerHeight = computed(() => sum(props.headerHeight));\n  const fixedRowsHeight = computed(() => {\n    var _a;\n    return (((_a = props.fixedData) == null ? void 0 : _a.length) || 0) * props.rowHeight;\n  });\n  const windowHeight = computed(() => {\n    return unref(mainTableHeight) - unref(headerHeight) - unref(fixedRowsHeight);\n  });\n  const rootStyle = computed(() => {\n    const {\n      style = {},\n      height,\n      width\n    } = props;\n    return enforceUnit({\n      ...style,\n      height,\n      width\n    });\n  });\n  const footerHeight = computed(() => enforceUnit({\n    height: props.footerHeight\n  }));\n  const emptyStyle = computed(() => ({\n    top: addUnit(unref(headerHeight)),\n    bottom: addUnit(props.footerHeight),\n    width: addUnit(props.width)\n  }));\n  return {\n    bodyWidth,\n    fixedTableHeight,\n    mainTableHeight,\n    leftTableWidth,\n    rightTableWidth,\n    headerWidth,\n    windowHeight,\n    footerHeight,\n    emptyStyle,\n    rootStyle,\n    headerHeight\n  };\n};\nexport { useStyles };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}