{"ast": null, "code": "const composeEventHandlers = (theirs<PERSON><PERSON><PERSON>, oursHand<PERSON>, {\n  checkForDefaultPrevented = true\n} = {}) => {\n  const handleEvent = event => {\n    const shouldPrevent = theirsHandler == null ? void 0 : theirsHandler(event);\n    if (checkForDefaultPrevented === false || !shouldPrevent) {\n      return oursHandler == null ? void 0 : oursHandler(event);\n    }\n  };\n  return handleEvent;\n};\nconst whenMouse = handler => {\n  return e => e.pointerType === \"mouse\" ? handler(e) : void 0;\n};\nexport { composeEventHandlers, whenMouse };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}