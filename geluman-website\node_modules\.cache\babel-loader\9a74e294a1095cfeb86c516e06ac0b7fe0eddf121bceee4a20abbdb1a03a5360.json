{"ast": null, "code": "import { defineComponent, computed, ref, reactive, unref, watch, onBeforeUnmount, h, withModifiers } from 'vue';\nimport { HORIZONTAL, ScrollbarDirKey, SCROLLBAR_MIN_SIZE } from '../defaults.mjs';\nimport { virtualizedScrollbarProps } from '../props.mjs';\nimport { renderThumbStyle } from '../utils.mjs';\nimport { BAR_MAP } from '../../../scrollbar/src/util.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { cAF, rAF } from '../../../../utils/raf.mjs';\nconst ScrollBar = defineComponent({\n  name: \"ElVirtualScrollBar\",\n  props: virtualizedScrollbarProps,\n  emits: [\"scroll\", \"start-move\", \"stop-move\"],\n  setup(props, {\n    emit\n  }) {\n    const GAP = computed(() => props.startGap + props.endGap);\n    const nsVirtualScrollbar = useNamespace(\"virtual-scrollbar\");\n    const nsScrollbar = useNamespace(\"scrollbar\");\n    const trackRef = ref();\n    const thumbRef = ref();\n    let frameHandle = null;\n    let onselectstartStore = null;\n    const state = reactive({\n      isDragging: false,\n      traveled: 0\n    });\n    const bar = computed(() => BAR_MAP[props.layout]);\n    const trackSize = computed(() => props.clientSize - unref(GAP));\n    const trackStyle = computed(() => ({\n      position: \"absolute\",\n      width: `${HORIZONTAL === props.layout ? trackSize.value : props.scrollbarSize}px`,\n      height: `${HORIZONTAL === props.layout ? props.scrollbarSize : trackSize.value}px`,\n      [ScrollbarDirKey[props.layout]]: \"2px\",\n      right: \"2px\",\n      bottom: \"2px\",\n      borderRadius: \"4px\"\n    }));\n    const thumbSize = computed(() => {\n      const ratio = props.ratio;\n      const clientSize = props.clientSize;\n      if (ratio >= 100) {\n        return Number.POSITIVE_INFINITY;\n      }\n      if (ratio >= 50) {\n        return ratio * clientSize / 100;\n      }\n      const SCROLLBAR_MAX_SIZE = clientSize / 3;\n      return Math.floor(Math.min(Math.max(ratio * clientSize, SCROLLBAR_MIN_SIZE), SCROLLBAR_MAX_SIZE));\n    });\n    const thumbStyle = computed(() => {\n      if (!Number.isFinite(thumbSize.value)) {\n        return {\n          display: \"none\"\n        };\n      }\n      const thumb = `${thumbSize.value}px`;\n      const style = renderThumbStyle({\n        bar: bar.value,\n        size: thumb,\n        move: state.traveled\n      }, props.layout);\n      return style;\n    });\n    const totalSteps = computed(() => Math.floor(props.clientSize - thumbSize.value - unref(GAP)));\n    const attachEvents = () => {\n      window.addEventListener(\"mousemove\", onMouseMove);\n      window.addEventListener(\"mouseup\", onMouseUp);\n      const thumbEl = unref(thumbRef);\n      if (!thumbEl) return;\n      onselectstartStore = document.onselectstart;\n      document.onselectstart = () => false;\n      thumbEl.addEventListener(\"touchmove\", onMouseMove, {\n        passive: true\n      });\n      thumbEl.addEventListener(\"touchend\", onMouseUp);\n    };\n    const detachEvents = () => {\n      window.removeEventListener(\"mousemove\", onMouseMove);\n      window.removeEventListener(\"mouseup\", onMouseUp);\n      document.onselectstart = onselectstartStore;\n      onselectstartStore = null;\n      const thumbEl = unref(thumbRef);\n      if (!thumbEl) return;\n      thumbEl.removeEventListener(\"touchmove\", onMouseMove);\n      thumbEl.removeEventListener(\"touchend\", onMouseUp);\n    };\n    const onThumbMouseDown = e => {\n      e.stopImmediatePropagation();\n      if (e.ctrlKey || [1, 2].includes(e.button)) {\n        return;\n      }\n      state.isDragging = true;\n      state[bar.value.axis] = e.currentTarget[bar.value.offset] - (e[bar.value.client] - e.currentTarget.getBoundingClientRect()[bar.value.direction]);\n      emit(\"start-move\");\n      attachEvents();\n    };\n    const onMouseUp = () => {\n      state.isDragging = false;\n      state[bar.value.axis] = 0;\n      emit(\"stop-move\");\n      detachEvents();\n    };\n    const onMouseMove = e => {\n      const {\n        isDragging\n      } = state;\n      if (!isDragging) return;\n      if (!thumbRef.value || !trackRef.value) return;\n      const prevPage = state[bar.value.axis];\n      if (!prevPage) return;\n      cAF(frameHandle);\n      const offset = (trackRef.value.getBoundingClientRect()[bar.value.direction] - e[bar.value.client]) * -1;\n      const thumbClickPosition = thumbRef.value[bar.value.offset] - prevPage;\n      const distance = offset - thumbClickPosition;\n      frameHandle = rAF(() => {\n        state.traveled = Math.max(props.startGap, Math.min(distance, totalSteps.value));\n        emit(\"scroll\", distance, totalSteps.value);\n      });\n    };\n    const clickTrackHandler = e => {\n      const offset = Math.abs(e.target.getBoundingClientRect()[bar.value.direction] - e[bar.value.client]);\n      const thumbHalf = thumbRef.value[bar.value.offset] / 2;\n      const distance = offset - thumbHalf;\n      state.traveled = Math.max(0, Math.min(distance, totalSteps.value));\n      emit(\"scroll\", distance, totalSteps.value);\n    };\n    watch(() => props.scrollFrom, v => {\n      if (state.isDragging) return;\n      state.traveled = Math.ceil(v * totalSteps.value);\n    });\n    onBeforeUnmount(() => {\n      detachEvents();\n    });\n    return () => {\n      return h(\"div\", {\n        role: \"presentation\",\n        ref: trackRef,\n        class: [nsVirtualScrollbar.b(), props.class, (props.alwaysOn || state.isDragging) && \"always-on\"],\n        style: trackStyle.value,\n        onMousedown: withModifiers(clickTrackHandler, [\"stop\", \"prevent\"]),\n        onTouchstartPrevent: onThumbMouseDown\n      }, h(\"div\", {\n        ref: thumbRef,\n        class: nsScrollbar.e(\"thumb\"),\n        style: thumbStyle.value,\n        onMousedown: onThumbMouseDown\n      }, []));\n    };\n  }\n});\nexport { ScrollBar as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}