{"ast": null, "code": "import { defineComponent, ref, computed, onMounted, watch, onBeforeUnmount, openBlock, createBlock, unref, createSlots, renderList, withCtx, renderSlot } from 'vue';\nimport { ElStatistic } from '../../statistic/index.mjs';\nimport { countdownProps, countdownEmits } from './countdown.mjs';\nimport { formatTime, getTime } from './utils.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { rAF, cAF } from '../../../utils/raf.mjs';\nimport { CHANGE_EVENT } from '../../../constants/event.mjs';\nconst __default__ = defineComponent({\n  name: \"ElCountdown\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: countdownProps,\n  emits: countdownEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    let timer;\n    const rawValue = ref(0);\n    const displayValue = computed(() => formatTime(rawValue.value, props.format));\n    const formatter = val => formatTime(val, props.format);\n    const stopTimer = () => {\n      if (timer) {\n        cAF(timer);\n        timer = void 0;\n      }\n    };\n    const startTimer = () => {\n      const timestamp = getTime(props.value);\n      const frameFunc = () => {\n        let diff = timestamp - Date.now();\n        emit(CHANGE_EVENT, diff);\n        if (diff <= 0) {\n          diff = 0;\n          stopTimer();\n          emit(\"finish\");\n        } else {\n          timer = rAF(frameFunc);\n        }\n        rawValue.value = diff;\n      };\n      timer = rAF(frameFunc);\n    };\n    onMounted(() => {\n      rawValue.value = getTime(props.value) - Date.now();\n      watch(() => [props.value, props.format], () => {\n        stopTimer();\n        startTimer();\n      }, {\n        immediate: true\n      });\n    });\n    onBeforeUnmount(() => {\n      stopTimer();\n    });\n    expose({\n      displayValue\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElStatistic), {\n        value: rawValue.value,\n        title: _ctx.title,\n        prefix: _ctx.prefix,\n        suffix: _ctx.suffix,\n        \"value-style\": _ctx.valueStyle,\n        formatter\n      }, createSlots({\n        _: 2\n      }, [renderList(_ctx.$slots, (_, name) => {\n        return {\n          name,\n          fn: withCtx(() => [renderSlot(_ctx.$slots, name)])\n        };\n      })]), 1032, [\"value\", \"title\", \"prefix\", \"suffix\", \"value-style\"]);\n    };\n  }\n});\nvar Countdown = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"countdown.vue\"]]);\nexport { Countdown as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}