{"ast": null, "code": "import { ref, onMounted, onUnmounted } from \"vue\";\nimport NavHeader from \"@/components/layout/NavHeader.vue\";\nimport SiteFooter from \"@/components/layout/SiteFooter.vue\";\nimport LoadingScreen from \"@/components/common/LoadingScreen.vue\";\nexport default {\n  __name: 'App',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const isScrolled = ref(false);\n    const handleScroll = () => {\n      isScrolled.value = window.scrollY > 50;\n    };\n    onMounted(() => {\n      window.addEventListener(\"scroll\", handleScroll);\n    });\n    onUnmounted(() => {\n      window.removeEventListener(\"scroll\", handleScroll);\n    });\n    const __returned__ = {\n      isScrolled,\n      handleScroll,\n      ref,\n      onMounted,\n      onUnmounted,\n      NavHeader,\n      SiteFooter,\n      LoadingScreen\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "onUnmounted", "Nav<PERSON><PERSON><PERSON>", "SiteFooter", "LoadingScreen", "isScrolled", "handleScroll", "value", "window", "scrollY", "addEventListener", "removeEventListener"], "sources": ["D:/codes/glmwebsite/geluman-website/src/App.vue"], "sourcesContent": ["<template>\r\n  <el-container class=\"layout-container\">\r\n    <el-header class=\"header\" :class=\"{ 'header-scrolled': isScrolled }\">\r\n      <nav-header />\r\n    </el-header>\r\n\r\n    <el-main>\r\n      <router-view v-slot=\"{ Component }\">\r\n        <transition name=\"fade\" mode=\"out-in\">\r\n          <component :is=\"Component\" />\r\n        </transition>\r\n      </router-view>\r\n    </el-main>\r\n\r\n    <el-footer>\r\n      <site-footer />\r\n    </el-footer>\r\n  </el-container>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, onUnmounted } from \"vue\";\r\nimport NavHeader from \"@/components/layout/NavHeader.vue\";\r\nimport SiteFooter from \"@/components/layout/SiteFooter.vue\";\r\nimport LoadingScreen from \"@/components/common/LoadingScreen.vue\";\r\n\r\nconst isScrolled = ref(false);\r\n\r\nconst handleScroll = () => {\r\n  isScrolled.value = window.scrollY > 50;\r\n};\r\n\r\nonMounted(() => {\r\n  window.addEventListener(\"scroll\", handleScroll);\r\n});\r\n\r\nonUnmounted(() => {\r\n  window.removeEventListener(\"scroll\", handleScroll);\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n:root {\r\n  --primary-color: #f6c17a;\r\n  --primary-light: #ffd699;\r\n  --primary-dark: #e5a75b;\r\n  --accent-color: #ff9f67;\r\n  --text-primary: #2c3e50;\r\n  --text-secondary: #5f6c7b;\r\n  --bg-primary: #ffffff;\r\n  --bg-secondary: #faf7f5;\r\n  --header-height: 64px;\r\n  --transition-base: all 0.3s ease;\r\n}\r\n\r\n.layout-container {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.header {\r\n  position: fixed;\r\n  width: 100%;\r\n  z-index: 1000;\r\n  transition: var(--transition-base);\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(8px);\r\n  padding: 0;\r\n  height: var(--header-height);\r\n}\r\n\r\n.header-scrolled {\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.el-main {\r\n  padding: 0;\r\n  flex: 1;\r\n}\r\n\r\n.el-footer {\r\n  padding: 0;\r\n  margin-top: auto;\r\n}\r\n\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n  opacity: 0;\r\n}\r\n</style>\r\n"], "mappings": "AAqBA,SAASA,GAAG,EAAEC,SAAS,EAAEC,WAAW,QAAQ,KAAK;AACjD,OAAOC,SAAS,MAAM,mCAAmC;AACzD,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,aAAa,MAAM,uCAAuC;;;;;;;IAEjE,MAAMC,UAAU,GAAGN,GAAG,CAAC,KAAK,CAAC;IAE7B,MAAMO,YAAY,GAAGA,CAAA,KAAM;MACzBD,UAAU,CAACE,KAAK,GAAGC,MAAM,CAACC,OAAO,GAAG,EAAE;IACxC,CAAC;IAEDT,SAAS,CAAC,MAAM;MACdQ,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IACjD,CAAC,CAAC;IAEFL,WAAW,CAAC,MAAM;MAChBO,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEL,YAAY,CAAC;IACpD,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}