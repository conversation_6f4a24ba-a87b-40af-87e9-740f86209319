{"ast": null, "code": "import { defineComponent, useSlots, inject, computed, openBlock, createElementBlock, normalizeClass, unref, withDirectives, isRef, withModifiers, vModelCheckbox, normalizeStyle, renderSlot, createTextVNode, toDisplayString, createCommentVNode } from 'vue';\nimport { checkboxGroupContextKey } from './constants.mjs';\nimport { checkboxProps, checkboxEmits } from './checkbox.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useCheckbox } from './composables/use-checkbox.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElCheckboxButton\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: checkboxProps,\n  emits: checkboxEmits,\n  setup(__props) {\n    const props = __props;\n    const slots = useSlots();\n    const {\n      isFocused,\n      isChecked,\n      isDisabled,\n      checkboxButtonSize,\n      model,\n      actualValue,\n      handleChange\n    } = useCheckbox(props, slots);\n    const checkboxGroup = inject(checkboxGroupContextKey, void 0);\n    const ns = useNamespace(\"checkbox\");\n    const activeStyle = computed(() => {\n      var _a, _b, _c, _d;\n      const fillValue = (_b = (_a = checkboxGroup == null ? void 0 : checkboxGroup.fill) == null ? void 0 : _a.value) != null ? _b : \"\";\n      return {\n        backgroundColor: fillValue,\n        borderColor: fillValue,\n        color: (_d = (_c = checkboxGroup == null ? void 0 : checkboxGroup.textColor) == null ? void 0 : _c.value) != null ? _d : \"\",\n        boxShadow: fillValue ? `-1px 0 0 0 ${fillValue}` : void 0\n      };\n    });\n    const labelKls = computed(() => {\n      return [ns.b(\"button\"), ns.bm(\"button\", checkboxButtonSize.value), ns.is(\"disabled\", isDisabled.value), ns.is(\"checked\", isChecked.value), ns.is(\"focus\", isFocused.value)];\n    });\n    return (_ctx, _cache) => {\n      var _a, _b, _c, _d;\n      return openBlock(), createElementBlock(\"label\", {\n        class: normalizeClass(unref(labelKls))\n      }, [_ctx.trueValue || _ctx.falseValue || _ctx.trueLabel || _ctx.falseLabel ? withDirectives((openBlock(), createElementBlock(\"input\", {\n        key: 0,\n        \"onUpdate:modelValue\": $event => isRef(model) ? model.value = $event : null,\n        class: normalizeClass(unref(ns).be(\"button\", \"original\")),\n        type: \"checkbox\",\n        name: _ctx.name,\n        tabindex: _ctx.tabindex,\n        disabled: unref(isDisabled),\n        \"true-value\": (_b = (_a = _ctx.trueValue) != null ? _a : _ctx.trueLabel) != null ? _b : true,\n        \"false-value\": (_d = (_c = _ctx.falseValue) != null ? _c : _ctx.falseLabel) != null ? _d : false,\n        onChange: unref(handleChange),\n        onFocus: $event => isFocused.value = true,\n        onBlur: $event => isFocused.value = false,\n        onClick: withModifiers(() => {}, [\"stop\"])\n      }, null, 42, [\"onUpdate:modelValue\", \"name\", \"tabindex\", \"disabled\", \"true-value\", \"false-value\", \"onChange\", \"onFocus\", \"onBlur\", \"onClick\"])), [[vModelCheckbox, unref(model)]]) : withDirectives((openBlock(), createElementBlock(\"input\", {\n        key: 1,\n        \"onUpdate:modelValue\": $event => isRef(model) ? model.value = $event : null,\n        class: normalizeClass(unref(ns).be(\"button\", \"original\")),\n        type: \"checkbox\",\n        name: _ctx.name,\n        tabindex: _ctx.tabindex,\n        disabled: unref(isDisabled),\n        value: unref(actualValue),\n        onChange: unref(handleChange),\n        onFocus: $event => isFocused.value = true,\n        onBlur: $event => isFocused.value = false,\n        onClick: withModifiers(() => {}, [\"stop\"])\n      }, null, 42, [\"onUpdate:modelValue\", \"name\", \"tabindex\", \"disabled\", \"value\", \"onChange\", \"onFocus\", \"onBlur\", \"onClick\"])), [[vModelCheckbox, unref(model)]]), _ctx.$slots.default || _ctx.label ? (openBlock(), createElementBlock(\"span\", {\n        key: 2,\n        class: normalizeClass(unref(ns).be(\"button\", \"inner\")),\n        style: normalizeStyle(unref(isChecked) ? unref(activeStyle) : void 0)\n      }, [renderSlot(_ctx.$slots, \"default\", {}, () => [createTextVNode(toDisplayString(_ctx.label), 1)])], 6)) : createCommentVNode(\"v-if\", true)], 2);\n    };\n  }\n});\nvar CheckboxButton = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"checkbox-button.vue\"]]);\nexport { CheckboxButton as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}