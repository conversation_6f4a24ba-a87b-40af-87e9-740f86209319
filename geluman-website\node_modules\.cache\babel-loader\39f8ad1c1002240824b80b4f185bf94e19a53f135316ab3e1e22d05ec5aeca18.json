{"ast": null, "code": "const DEFAULT_DYNAMIC_LIST_ITEM_SIZE = 50;\nconst ITEM_RENDER_EVT = \"itemRendered\";\nconst SCROLL_EVT = \"scroll\";\nconst FORWARD = \"forward\";\nconst BACKWARD = \"backward\";\nconst AUTO_ALIGNMENT = \"auto\";\nconst SMART_ALIGNMENT = \"smart\";\nconst START_ALIGNMENT = \"start\";\nconst CENTERED_ALIGNMENT = \"center\";\nconst END_ALIGNMENT = \"end\";\nconst HORIZONTAL = \"horizontal\";\nconst VERTICAL = \"vertical\";\nconst LTR = \"ltr\";\nconst RTL = \"rtl\";\nconst RTL_OFFSET_NAG = \"negative\";\nconst RTL_OFFSET_POS_ASC = \"positive-ascending\";\nconst RTL_OFFSET_POS_DESC = \"positive-descending\";\nconst ScrollbarSizeKey = {\n  [HORIZONTAL]: \"height\",\n  [VERTICAL]: \"width\"\n};\nconst ScrollbarDirKey = {\n  [HORIZONTAL]: \"left\",\n  [VERTICAL]: \"top\"\n};\nconst SCROLLBAR_MIN_SIZE = 20;\nexport { AUTO_ALIGNMENT, BACKWARD, CENTERED_ALIGNMENT, DEFAULT_DYNAMIC_LIST_ITEM_SIZE, END_ALIGNMENT, FORWARD, HORIZONTAL, ITEM_RENDER_EVT, LTR, RTL, RTL_OFFSET_NAG, RTL_OFFSET_POS_ASC, RTL_OFFSET_POS_DESC, SCROLLBAR_MIN_SIZE, SCROLL_EVT, SMART_ALIGNMENT, START_ALIGNMENT, ScrollbarDirKey, ScrollbarSizeKey, VERTICAL };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}