{"ast": null, "code": "import { unref as _unref, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveDirective as _resolveDirective, openBlock as _openBlock, createElementBlock as _createElementBlock, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"product-page\"\n};\nconst _hoisted_2 = {\n  class: \"hero\"\n};\nconst _hoisted_3 = {\n  class: \"hero-content\"\n};\nconst _hoisted_4 = [\"src\"];\nconst _hoisted_5 = {\n  class: \"download-options\"\n};\nconst _hoisted_6 = {\n  class: \"version-option\"\n};\nconst _hoisted_7 = {\n  href: \"https://gengxin.geluman.cn/eyeshield/GLM-Eyeshield-1.3.3.zip\",\n  class: \"download-btn\",\n  target: \"_blank\"\n};\nconst _hoisted_8 = {\n  class: \"demo\"\n};\nconst _hoisted_9 = {\n  class: \"container\"\n};\nconst _hoisted_10 = {\n  class: \"demo-gallery\"\n};\nconst _hoisted_11 = {\n  class: \"demo-item\"\n};\nconst _hoisted_12 = {\n  class: \"demo-image-container\"\n};\nconst _hoisted_13 = [\"src\"];\nconst _hoisted_14 = {\n  class: \"demo-item\"\n};\nconst _hoisted_15 = {\n  class: \"demo-image-container\"\n};\nconst _hoisted_16 = [\"src\"];\nconst _hoisted_17 = {\n  class: \"features\"\n};\nconst _hoisted_18 = {\n  class: \"container\"\n};\nconst _hoisted_19 = {\n  class: \"features-grid\"\n};\nconst _hoisted_20 = {\n  class: \"feature-icon\"\n};\nconst _hoisted_21 = {\n  class: \"usage\"\n};\nconst _hoisted_22 = {\n  class: \"container\"\n};\nconst _hoisted_23 = {\n  class: \"usage-steps\"\n};\nconst _hoisted_24 = {\n  class: \"step-number\"\n};\nimport { eyeshield } from \"@/assets\";\nimport huyanSetting from \"@/assets/img/huyan-setting.png\";\nimport huyanDemo from \"@/assets/img/huyan.png\";\nconst __default__ = {\n  name: \"EyeshieldPage\"\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props) {\n    const features = [{\n      icon: \"🎨\",\n      title: \"多种护眼模式\",\n      description: \"提供豆沙绿、杏灰色、淡黄色、浅粉色等多种护眼配色\"\n    }, {\n      icon: \"⚡\",\n      title: \"一键切换\",\n      description: \"便捷的开关控制，随时切换护眼模式\"\n    }, {\n      icon: \"🔄\",\n      title: \"智能适配\",\n      description: \"自动识别网页结构，智能调整背景色\"\n    }, {\n      icon: \"🎚️\",\n      title: \"全局控制\",\n      description: \"支持设置全局应用或指定网站应用\"\n    }];\n    const steps = [{\n      number: \"1\",\n      title: \"安装插件\",\n      description: \"从下方下载按钮下载并解压，打开Chrome或者Edge浏览器，进入（浏览器扩展管理）chrome://extensions/或者edge://extensions/，将解压后的文件夹拖入页面中，完成安装\"\n    }, {\n      number: \"2\",\n      title: \"选择模式\",\n      description: \"点击插件图标，选择合适的护眼模式\"\n    }, {\n      number: \"3\",\n      title: \"设置范围\",\n      description: \"选择是全局应用还是仅应用于特定网站\"\n    }, {\n      number: \"4\",\n      title: \"开始使用\",\n      description: \"打开网页即可享受舒适的护眼体验\"\n    }];\n    return (_ctx, _cache) => {\n      const _directive_ripple = _resolveDirective(\"ripple\");\n      return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"img\", {\n        src: _unref(eyeshield),\n        alt: \"护眼助手\",\n        class: \"plugin-logo\"\n      }, null, 8, _hoisted_4), _cache[2] || (_cache[2] = _createElementVNode(\"h1\", null, \"网页护眼助手\", -1)), _cache[3] || (_cache[3] = _createElementVNode(\"p\", {\n        class: \"subtitle\"\n      }, \"智能护眼，让阅读更舒适\", -1)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_withDirectives((_openBlock(), _createElementBlock(\"a\", _hoisted_7, _cache[0] || (_cache[0] = [_createTextVNode(\" 下载插件 \")]))), [[_directive_ripple]]), _cache[1] || (_cache[1] = _createElementVNode(\"p\", {\n        class: \"version-desc\"\n      }, \"标准版本，适合大多数用户使用\", -1))])])])]), _createElementVNode(\"section\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_cache[6] || (_cache[6] = _createElementVNode(\"h2\", {\n        class: \"section-title\"\n      }, \"产品演示\", -1)), _cache[7] || (_cache[7] = _createElementVNode(\"p\", {\n        class: \"section-subtitle\"\n      }, \"便捷的护眼工具，多种模式选择\", -1)), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"img\", {\n        src: _unref(huyanSetting),\n        alt: \"网页护眼助手 - 设置界面\",\n        class: \"demo-image\"\n      }, null, 8, _hoisted_13)]), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n        class: \"image-caption\"\n      }, \"护眼设置 - 多种模式可选\", -1))]), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"img\", {\n        src: _unref(huyanDemo),\n        alt: \"网页护眼助手 - 效果展示\",\n        class: \"demo-image\"\n      }, null, 8, _hoisted_16)]), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n        class: \"image-caption\"\n      }, \"护眼效果 - 舒适自然的阅读体验\", -1))])])])]), _createElementVNode(\"section\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_cache[8] || (_cache[8] = _createElementVNode(\"h2\", null, \"核心功能\", -1)), _createElementVNode(\"div\", _hoisted_19, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(features, feature => {\n        return _createElementVNode(\"div\", {\n          class: \"feature-card\",\n          key: feature.title\n        }, [_createElementVNode(\"div\", _hoisted_20, _toDisplayString(feature.icon), 1), _createElementVNode(\"h3\", null, _toDisplayString(feature.title), 1), _createElementVNode(\"p\", null, _toDisplayString(feature.description), 1)]);\n      }), 64))])])]), _createElementVNode(\"section\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_cache[9] || (_cache[9] = _createElementVNode(\"h2\", null, \"使用说明\", -1)), _createElementVNode(\"div\", _hoisted_23, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(steps, step => {\n        return _createElementVNode(\"div\", {\n          class: \"step\",\n          key: step.title\n        }, [_createElementVNode(\"div\", _hoisted_24, _toDisplayString(step.number), 1), _createElementVNode(\"h3\", null, _toDisplayString(step.title), 1), _createElementVNode(\"p\", null, _toDisplayString(step.description), 1)]);\n      }), 64))])])])]);\n    };\n  }\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}