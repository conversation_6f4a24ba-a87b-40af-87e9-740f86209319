{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/**\n * Converts `iterator` to an array.\n *\n * @private\n * @param {Object} iterator The iterator to convert.\n * @returns {Array} Returns the converted array.\n */\nfunction iteratorToArray(iterator) {\n  var data,\n    result = [];\n  while (!(data = iterator.next()).done) {\n    result.push(data.value);\n  }\n  return result;\n}\nexport default iteratorToArray;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}