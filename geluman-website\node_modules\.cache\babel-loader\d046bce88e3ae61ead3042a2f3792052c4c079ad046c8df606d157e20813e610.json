{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\n/**\n * Converts `map` to its key-value pairs.\n *\n * @private\n * @param {Object} map The map to convert.\n * @returns {Array} Returns the key-value pairs.\n */\nfunction mapToArray(map) {\n  var index = -1,\n    result = Array(map.size);\n  map.forEach(function (value, key) {\n    result[++index] = [key, value];\n  });\n  return result;\n}\nexport default mapToArray;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}