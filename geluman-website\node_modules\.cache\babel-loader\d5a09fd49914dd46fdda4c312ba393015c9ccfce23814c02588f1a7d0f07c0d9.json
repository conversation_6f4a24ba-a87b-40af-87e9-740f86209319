{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { version } from './version.mjs';\nimport { INSTALLED_KEY } from './constants/key.mjs';\nimport { provideGlobalConfig } from './components/config-provider/src/hooks/use-global-config.mjs';\nconst makeInstaller = (components = []) => {\n  const install = (app, options) => {\n    if (app[INSTALLED_KEY]) return;\n    app[INSTALLED_KEY] = true;\n    components.forEach(c => app.use(c));\n    if (options) provideGlobalConfig(options, app, true);\n  };\n  return {\n    version,\n    install\n  };\n};\nexport { makeInstaller };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}