<template>
  <div class="product-page">
    <section class="hero">
      <div class="hero-content">
        <img :src="eyeshield" alt="护眼助手" class="plugin-logo" />
        <h1>网页护眼助手</h1>
        <p class="subtitle">智能护眼，让阅读更舒适</p>
        <div class="download-options">
          <div class="version-option">
            <a
              href="https://gengxin.geluman.cn/eyeshield/GLM-Eyeshield-1.3.3.zip"
              class="download-btn"
              target="_blank"
              v-ripple
            >
              下载插件
            </a>
            <p class="version-desc">标准版本，适合大多数用户使用</p>
          </div>
        </div>
      </div>
    </section>

    <section class="demo">
      <div class="container">
        <h2 class="section-title">产品演示</h2>
        <p class="section-subtitle">便捷的护眼工具，多种模式选择</p>
        <div class="demo-gallery">
          <div class="demo-item">
            <div class="demo-image-container">
              <img
                :src="huyanSetting"
                alt="网页护眼助手 - 设置界面"
                class="demo-image"
              />
            </div>
            <div class="image-caption">护眼设置 - 多种模式可选</div>
          </div>
          <div class="demo-item">
            <div class="demo-image-container">
              <img
                :src="huyanDemo"
                alt="网页护眼助手 - 效果展示"
                class="demo-image"
              />
            </div>
            <div class="image-caption">护眼效果 - 舒适自然的阅读体验</div>
          </div>
        </div>
      </div>
    </section>

    <section class="features">
      <div class="container">
        <h2>核心功能</h2>
        <div class="features-grid">
          <div
            class="feature-card"
            v-for="feature in features"
            :key="feature.title"
          >
            <div class="feature-icon">{{ feature.icon }}</div>
            <h3>{{ feature.title }}</h3>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <section class="usage">
      <div class="container">
        <h2>使用说明</h2>
        <div class="usage-steps">
          <div class="step" v-for="step in steps" :key="step.title">
            <div class="step-number">{{ step.number }}</div>
            <h3>{{ step.title }}</h3>
            <p>{{ step.description }}</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { eyeshield } from "@/assets";
import huyanSetting from "@/assets/img/huyan-setting.png";
import huyanDemo from "@/assets/img/huyan.png";

const features = [
  {
    icon: "🎨",
    title: "多种护眼模式",
    description: "提供豆沙绿、杏灰色、淡黄色、浅粉色等多种护眼配色",
  },
  {
    icon: "⚡",
    title: "一键切换",
    description: "便捷的开关控制，随时切换护眼模式",
  },
  {
    icon: "🔄",
    title: "智能适配",
    description: "自动识别网页结构，智能调整背景色",
  },
  {
    icon: "🎚️",
    title: "全局控制",
    description: "支持设置全局应用或指定网站应用",
  },
];

const steps = [
  {
    number: "1",
    title: "安装插件",
    description:
      "从下方下载按钮下载并解压，打开Chrome或者Edge浏览器，进入（浏览器扩展管理）chrome://extensions/或者edge://extensions/，将解压后的文件夹拖入页面中，完成安装",
  },
  {
    number: "2",
    title: "选择模式",
    description: "点击插件图标，选择合适的护眼模式",
  },
  {
    number: "3",
    title: "设置范围",
    description: "选择是全局应用还是仅应用于特定网站",
  },
  {
    number: "4",
    title: "开始使用",
    description: "打开网页即可享受舒适的护眼体验",
  },
];
</script>

<style lang="scss" scoped>
.hero {
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-dark) 100%
  );
  padding: 6rem 0;
  text-align: center;
  color: white;

  .plugin-logo {
    width: 120px;
    height: auto;
    margin-bottom: 2rem;
  }

  h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
  }

  .download-options {
    display: flex;
    justify-content: center;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
    }

    .version-option {
      flex: 1;
      max-width: 400px;
    }

    .download-btn {
      display: inline-block;
      padding: 1rem 2rem;
      background: white;
      color: var(--primary-color);
      text-decoration: none;
      border-radius: 30px;
      font-weight: 500;
      transition: var(--transition-base);
      margin-bottom: 1rem;
      width: 100%;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      }
    }

    .version-desc {
      font-size: 0.9rem;
      opacity: 0.8;
      line-height: 1.5;
    }
  }
}

.demo {
  padding: 6rem 0;
  background: white;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
  }

  .section-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
  }

  .section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }

  .demo-gallery {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3rem;

    @media (max-width: 992px) {
      grid-template-columns: 1fr;
      gap: 4rem;
    }
  }

  .demo-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .demo-image-container {
      width: 100%;
      height: 350px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      border-radius: 12px;
      overflow: hidden;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f8f9fa;

      &:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
      }
    }

    .demo-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      display: block;
      border-radius: 12px;
    }

    .image-caption {
      margin-top: 1.5rem;
      font-size: 1.1rem;
      color: var(--text-secondary);
    }
  }
}

.features {
  padding: 6rem 0;
  background: var(--bg-secondary);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;

    @media (max-width: 992px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
  transition: var(--transition-base);

  &:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
  }

  .feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
  }

  p {
    color: var(--text-secondary);
    line-height: 1.6;
  }
}

// 使用说明部分的样式与 Highlight.vue 相同
.usage {
  padding: 6rem 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
  }

  .usage-steps {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;

    @media (max-width: 992px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .step {
    text-align: center;
    padding: 2rem;

    .step-number {
      width: 40px;
      height: 40px;
      background: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1rem;
      font-weight: 500;
    }

    h3 {
      font-size: 1.3rem;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    p {
      color: var(--text-secondary);
      line-height: 1.6;
    }
  }
}
</style>

<script>
export default {
  name: "EyeshieldPage",
};
</script>
