"use strict";(self["webpackChunkgeluman_website"]=self["webpackChunkgeluman_website"]||[]).push([[818],{4434:function(e,t,n){n.d(t,{q:function(){return Q}});n(1484),n(6961),n(4126),n(4615);var d=n(8450),o=n(8018),r=n(3255),a=n(577),s=n(5469);n(9370),n(2807),n(4929);const i="$treeNodeId",l=function(e,t){t&&!t[i]&&Object.defineProperty(t,i,{value:e.id,enumerable:!1,configurable:!1,writable:!1})},c=(e,t)=>null==t?void 0:t[e||i],h=(e,t,n)=>{const d=e.value.currentNode;n();const o=e.value.currentNode;d!==o&&t("current-change",o?o.data:null,o)};var u=n(3870);const p=e=>{let t=!0,n=!0,d=!0;for(let o=0,r=e.length;o<r;o++){const r=e[o];(!0!==r.checked||r.indeterminate)&&(t=!1,r.disabled||(d=!1)),(!1!==r.checked||r.indeterminate)&&(n=!1)}return{all:t,none:n,allWithoutDisable:d,half:!t&&!n}},f=function(e){if(0===e.childNodes.length||e.loading)return;const{all:t,none:n,half:d}=p(e.childNodes);t?(e.checked=!0,e.indeterminate=!1):d?(e.checked=!1,e.indeterminate=!0):n&&(e.checked=!1,e.indeterminate=!1);const o=e.parent;o&&0!==o.level&&(e.store.checkStrictly||f(o))},g=function(e,t){const n=e.store.props,d=e.data||{},o=n[t];if((0,r.Tn)(o))return o(d,e);if((0,r.Kg)(o))return d[o];if((0,u.b0)(o)){const e=d[t];return(0,u.b0)(e)?"":e}};let y=0;class k{constructor(e){this.id=y++,this.text=null,this.checked=!1,this.indeterminate=!1,this.data=null,this.expanded=!1,this.parent=null,this.visible=!0,this.isCurrent=!1,this.canFocus=!1;for(const t in e)(0,r.$3)(e,t)&&(this[t]=e[t]);this.level=0,this.loaded=!1,this.childNodes=[],this.loading=!1,this.parent&&(this.level=this.parent.level+1)}initialize(){const e=this.store;if(!e)throw new Error("[Node]store is required!");e.registerNode(this);const t=e.props;if(t&&"undefined"!==typeof t.isLeaf){const e=g(this,"isLeaf");(0,u.Lm)(e)&&(this.isLeafByUser=e)}if(!0!==e.lazy&&this.data?(this.setData(this.data),e.defaultExpandAll&&(this.expanded=!0,this.canFocus=!0)):this.level>0&&e.lazy&&e.defaultExpandAll&&!this.isLeafByUser&&this.expand(),(0,r.cy)(this.data)||l(this,this.data),!this.data)return;const n=e.defaultExpandedKeys,d=e.key;d&&n&&n.includes(this.key)&&this.expand(null,e.autoExpandParent),d&&void 0!==e.currentNodeKey&&this.key===e.currentNodeKey&&(e.currentNode=this,e.currentNode.isCurrent=!0),e.lazy&&e._initDefaultCheckedNode(this),this.updateLeafState(),!this.parent||1!==this.level&&!0!==this.parent.expanded||(this.canFocus=!0)}setData(e){let t;(0,r.cy)(e)||l(this,e),this.data=e,this.childNodes=[],t=0===this.level&&(0,r.cy)(this.data)?this.data:g(this,"children")||[];for(let n=0,d=t.length;n<d;n++)this.insertChild({data:t[n]})}get label(){return g(this,"label")}get key(){const e=this.store.key;return this.data?this.data[e]:null}get disabled(){return g(this,"disabled")}get nextSibling(){const e=this.parent;if(e){const t=e.childNodes.indexOf(this);if(t>-1)return e.childNodes[t+1]}return null}get previousSibling(){const e=this.parent;if(e){const t=e.childNodes.indexOf(this);if(t>-1)return t>0?e.childNodes[t-1]:null}return null}contains(e,t=!0){return(this.childNodes||[]).some((n=>n===e||t&&n.contains(e)))}remove(){const e=this.parent;e&&e.removeChild(this)}insertChild(e,t,n){if(!e)throw new Error("InsertChild error: child is required.");if(!(e instanceof k)){if(!n){const n=this.getChildren(!0);n.includes(e.data)||((0,u.b0)(t)||t<0?n.push(e.data):n.splice(t,0,e.data))}Object.assign(e,{parent:this,store:this.store}),e=(0,o.Kh)(new k(e)),e instanceof k&&e.initialize()}e.level=this.level+1,(0,u.b0)(t)||t<0?this.childNodes.push(e):this.childNodes.splice(t,0,e),this.updateLeafState()}insertBefore(e,t){let n;t&&(n=this.childNodes.indexOf(t)),this.insertChild(e,n)}insertAfter(e,t){let n;t&&(n=this.childNodes.indexOf(t),-1!==n&&(n+=1)),this.insertChild(e,n)}removeChild(e){const t=this.getChildren()||[],n=t.indexOf(e.data);n>-1&&t.splice(n,1);const d=this.childNodes.indexOf(e);d>-1&&(this.store&&this.store.deregisterNode(e),e.parent=null,this.childNodes.splice(d,1)),this.updateLeafState()}removeChildByData(e){let t=null;for(let n=0;n<this.childNodes.length;n++)if(this.childNodes[n].data===e){t=this.childNodes[n];break}t&&this.removeChild(t)}expand(e,t){const n=()=>{if(t){let e=this.parent;while(e.level>0)e.expanded=!0,e=e.parent}this.expanded=!0,e&&e(),this.childNodes.forEach((e=>{e.canFocus=!0}))};this.shouldLoadData()?this.loadData((e=>{(0,r.cy)(e)&&(this.checked?this.setChecked(!0,!0):this.store.checkStrictly||f(this),n())})):n()}doCreateChildren(e,t={}){e.forEach((e=>{this.insertChild(Object.assign({data:e},t),void 0,!0)}))}collapse(){this.expanded=!1,this.childNodes.forEach((e=>{e.canFocus=!1}))}shouldLoadData(){return!0===this.store.lazy&&this.store.load&&!this.loaded}updateLeafState(){if(!0===this.store.lazy&&!0!==this.loaded&&"undefined"!==typeof this.isLeafByUser)return void(this.isLeaf=this.isLeafByUser);const e=this.childNodes;!this.store.lazy||!0===this.store.lazy&&!0===this.loaded?this.isLeaf=!e||0===e.length:this.isLeaf=!1}setChecked(e,t,n,d){if(this.indeterminate="half"===e,this.checked=!0===e,this.store.checkStrictly)return;if(!this.shouldLoadData()||this.store.checkDescendants){const{all:n,allWithoutDisable:o}=p(this.childNodes);this.isLeaf||n||!o||(this.checked=!1,e=!1);const r=()=>{if(t){const n=this.childNodes;for(let a=0,s=n.length;a<s;a++){const o=n[a];d=d||!1!==e;const r=o.disabled?o.checked:d;o.setChecked(r,t,!0,d)}const{half:o,all:r}=p(n);r||(this.checked=r,this.indeterminate=o)}};if(this.shouldLoadData())return void this.loadData((()=>{r(),f(this)}),{checked:!1!==e});r()}const o=this.parent;o&&0!==o.level&&(n||f(o))}getChildren(e=!1){if(0===this.level)return this.data;const t=this.data;if(!t)return null;const n=this.store.props;let d="children";return n&&(d=n.children||"children"),(0,u.b0)(t[d])&&(t[d]=null),e&&!t[d]&&(t[d]=[]),t[d]}updateChildren(){const e=this.getChildren()||[],t=this.childNodes.map((e=>e.data)),n={},d=[];e.forEach(((e,o)=>{const r=e[i],a=!!r&&t.findIndex((e=>e[i]===r))>=0;a?n[r]={index:o,data:e}:d.push({index:o,data:e})})),this.store.lazy||t.forEach((e=>{n[e[i]]||this.removeChildByData(e)})),d.forEach((({index:e,data:t})=>{this.insertChild({data:t},e)})),this.updateLeafState()}loadData(e,t={}){if(!0!==this.store.lazy||!this.store.load||this.loaded||this.loading&&!Object.keys(t).length)e&&e.call(this);else{this.loading=!0;const n=n=>{this.childNodes=[],this.doCreateChildren(n,t),this.loaded=!0,this.loading=!1,this.updateLeafState(),e&&e.call(this,n)},d=()=>{this.loading=!1};this.store.load(this,n,d)}}eachNode(e){const t=[this];while(t.length){const n=t.shift();t.unshift(...n.childNodes),e(n)}}reInitChecked(){this.store.checkStrictly||f(this)}}class C{constructor(e){this.currentNode=null,this.currentNodeKey=null;for(const t in e)(0,r.$3)(e,t)&&(this[t]=e[t]);this.nodesMap={}}initialize(){if(this.root=new k({data:this.data,store:this}),this.root.initialize(),this.lazy&&this.load){const e=this.load;e(this.root,(e=>{this.root.doCreateChildren(e),this._initDefaultCheckedNodes()}))}else this._initDefaultCheckedNodes()}filter(e){const t=this.filterNodeMethod,n=this.lazy,o=async function(r){const a=r.root?r.root.childNodes:r.childNodes;for(const[n,s]of a.entries())s.visible=t.call(s,e,s.data,s),n%80===0&&n>0&&await(0,d.dY)(),await o(s);if(!r.visible&&a.length){let e=!0;e=!a.some((e=>e.visible)),r.root?r.root.visible=!1===e:r.visible=!1===e}e&&r.visible&&!r.isLeaf&&(n&&!r.loaded||r.expand())};o(this)}setData(e){const t=e!==this.root.data;t?(this.nodesMap={},this.root.setData(e),this._initDefaultCheckedNodes(),this.setCurrentNodeKey(this.currentNodeKey)):this.root.updateChildren()}getNode(e){if(e instanceof k)return e;const t=(0,r.Gv)(e)?c(this.key,e):e;return this.nodesMap[t]||null}insertBefore(e,t){const n=this.getNode(t);n.parent.insertBefore({data:e},n)}insertAfter(e,t){const n=this.getNode(t);n.parent.insertAfter({data:e},n)}remove(e){const t=this.getNode(e);t&&t.parent&&(t===this.currentNode&&(this.currentNode=null),t.parent.removeChild(t))}append(e,t){const n=(0,u.Xj)(t)?this.root:this.getNode(t);n&&n.insertChild({data:e})}_initDefaultCheckedNodes(){const e=this.defaultCheckedKeys||[],t=this.nodesMap;e.forEach((e=>{const n=t[e];n&&n.setChecked(!0,!this.checkStrictly)}))}_initDefaultCheckedNode(e){const t=this.defaultCheckedKeys||[];t.includes(e.key)&&e.setChecked(!0,!this.checkStrictly)}setDefaultCheckedKey(e){e!==this.defaultCheckedKeys&&(this.defaultCheckedKeys=e,this._initDefaultCheckedNodes())}registerNode(e){const t=this.key;if(e&&e.data)if(t){const t=e.key;void 0!==t&&(this.nodesMap[e.key]=e)}else this.nodesMap[e.id]=e}deregisterNode(e){const t=this.key;t&&e&&e.data&&(e.childNodes.forEach((e=>{this.deregisterNode(e)})),delete this.nodesMap[e.key])}getCheckedNodes(e=!1,t=!1){const n=[],d=function(o){const r=o.root?o.root.childNodes:o.childNodes;r.forEach((o=>{(o.checked||t&&o.indeterminate)&&(!e||e&&o.isLeaf)&&n.push(o.data),d(o)}))};return d(this),n}getCheckedKeys(e=!1){return this.getCheckedNodes(e).map((e=>(e||{})[this.key]))}getHalfCheckedNodes(){const e=[],t=function(n){const d=n.root?n.root.childNodes:n.childNodes;d.forEach((n=>{n.indeterminate&&e.push(n.data),t(n)}))};return t(this),e}getHalfCheckedKeys(){return this.getHalfCheckedNodes().map((e=>(e||{})[this.key]))}_getAllNodes(){const e=[],t=this.nodesMap;for(const n in t)(0,r.$3)(t,n)&&e.push(t[n]);return e}updateChildren(e,t){const n=this.nodesMap[e];if(!n)return;const d=n.childNodes;for(let o=d.length-1;o>=0;o--){const e=d[o];this.remove(e.data)}for(let o=0,r=t.length;o<r;o++){const e=t[o];this.append(e,n.data)}}_setCheckedKeys(e,t=!1,n){const d=this._getAllNodes().sort(((e,t)=>e.level-t.level)),o=Object.create(null),r=Object.keys(n);d.forEach((e=>e.setChecked(!1,!1)));const a=t=>{t.childNodes.forEach((t=>{var n;o[t.data[e]]=!0,(null==(n=t.childNodes)?void 0:n.length)&&a(t)}))};for(let s=0,i=d.length;s<i;s++){const n=d[s],i=n.data[e].toString(),l=r.includes(i);if(l){if(n.childNodes.length&&a(n),n.isLeaf||this.checkStrictly)n.setChecked(!0,!1);else if(n.setChecked(!0,!0),t){n.setChecked(!1,!1);const e=function(t){const n=t.childNodes;n.forEach((t=>{t.isLeaf||t.setChecked(!1,!1),e(t)}))};e(n)}}else n.checked&&!o[i]&&n.setChecked(!1,!1)}}setCheckedNodes(e,t=!1){const n=this.key,d={};e.forEach((e=>{d[(e||{})[n]]=!0})),this._setCheckedKeys(n,t,d)}setCheckedKeys(e,t=!1){this.defaultCheckedKeys=e;const n=this.key,d={};e.forEach((e=>{d[e]=!0})),this._setCheckedKeys(n,t,d)}setDefaultExpandedKeys(e){e=e||[],this.defaultExpandedKeys=e,e.forEach((e=>{const t=this.getNode(e);t&&t.expand(null,this.autoExpandParent)}))}setChecked(e,t,n){const d=this.getNode(e);d&&d.setChecked(!!t,n)}getCurrentNode(){return this.currentNode}setCurrentNode(e){const t=this.currentNode;t&&(t.isCurrent=!1),this.currentNode=e,this.currentNode.isCurrent=!0}setUserCurrentNode(e,t=!0){const n=e[this.key],d=this.nodesMap[n];this.setCurrentNode(d),t&&this.currentNode.level>1&&this.currentNode.parent.expand(null,!0)}setCurrentNodeKey(e,t=!0){if(this.currentNodeKey=e,(0,u.Xj)(e))return this.currentNode&&(this.currentNode.isCurrent=!1),void(this.currentNode=null);const n=this.getNode(e);n&&(this.setCurrentNode(n),t&&this.currentNode.level>1&&this.currentNode.parent.expand(null,!0))}}var v=n(1802),N=n(9671),x=n(5591),b=n(5194),K=n(6673),m=n(7040),D=n(3600);const E=(0,d.pM)({name:"ElTreeNodeContent",props:{node:{type:Object,required:!0},renderContent:Function},setup(e){const t=(0,D.DU)("tree"),n=(0,d.WQ)("NodeInstance"),o=(0,d.WQ)("RootTree");return()=>{const r=e.node,{data:a,store:s}=r;return e.renderContent?e.renderContent(d.h,{_self:n,node:r,data:a,store:s}):(0,d.RG)(o.ctx.slots,"default",{node:r,data:a},(()=>[(0,d.h)(K.$g,{tag:"span",truncated:!0,class:t.be("node","label")},(()=>[r.label]))]))}}});var w=(0,m.A)(E,[["__file","tree-node-content.vue"]]);function S(e){const t=(0,d.WQ)("TreeNodeMap",null),n={treeNodeExpand:t=>{e.node!==t&&e.node.collapse()},children:[]};return t&&t.children.push(n),(0,d.Gt)("TreeNodeMap",n),{broadcastExpanded:t=>{if(e.accordion)for(const e of n.children)e.treeNodeExpand(t)}}}var B=n(424);const $=Symbol("dragEvents");function A({props:e,ctx:t,el$:n,dropIndicator$:a,store:s}){const i=(0,D.DU)("tree"),l=(0,o.KR)({showDropIndicator:!1,draggingNode:null,dropNode:null,allowDrop:!0,dropType:null}),c=({event:n,treeNode:d})=>{if((0,r.Tn)(e.allowDrag)&&!e.allowDrag(d.node))return n.preventDefault(),!1;n.dataTransfer.effectAllowed="move";try{n.dataTransfer.setData("text/plain","")}catch(o){}l.value.draggingNode=d,t.emit("node-drag-start",d.node,n)},h=({event:d,treeNode:o})=>{const s=o,c=l.value.dropNode;c&&c.node.id!==s.node.id&&(0,B.vy)(c.$el,i.is("drop-inner"));const h=l.value.draggingNode;if(!h||!s)return;let u=!0,p=!0,f=!0,g=!0;(0,r.Tn)(e.allowDrop)&&(u=e.allowDrop(h.node,s.node,"prev"),g=p=e.allowDrop(h.node,s.node,"inner"),f=e.allowDrop(h.node,s.node,"next")),d.dataTransfer.dropEffect=p||u||f?"move":"none",(u||p||f)&&(null==c?void 0:c.node.id)!==s.node.id&&(c&&t.emit("node-drag-leave",h.node,c.node,d),t.emit("node-drag-enter",h.node,s.node,d)),l.value.dropNode=u||p||f?s:null,s.node.nextSibling===h.node&&(f=!1),s.node.previousSibling===h.node&&(u=!1),s.node.contains(h.node,!1)&&(p=!1),(h.node===s.node||h.node.contains(s.node))&&(u=!1,p=!1,f=!1);const y=s.$el.querySelector(`.${i.be("node","content")}`).getBoundingClientRect(),k=n.value.getBoundingClientRect();let C;const v=u?p?.25:f?.45:1:-1,N=f?p?.75:u?.55:0:1;let x=-9999;const b=d.clientY-y.top;C=b<y.height*v?"before":b>y.height*N?"after":p?"inner":"none";const K=s.$el.querySelector(`.${i.be("node","expand-icon")}`).getBoundingClientRect(),m=a.value;"before"===C?x=K.top-k.top:"after"===C&&(x=K.bottom-k.top),m.style.top=`${x}px`,m.style.left=K.right-k.left+"px","inner"===C?(0,B.iQ)(s.$el,i.is("drop-inner")):(0,B.vy)(s.$el,i.is("drop-inner")),l.value.showDropIndicator="before"===C||"after"===C,l.value.allowDrop=l.value.showDropIndicator||g,l.value.dropType=C,t.emit("node-drag-over",h.node,s.node,d)},u=e=>{const{draggingNode:n,dropType:d,dropNode:o}=l.value;if(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect="move"),n&&o){const r={data:n.node.data};"none"!==d&&n.node.remove(),"before"===d?o.node.parent.insertBefore(r,o.node):"after"===d?o.node.parent.insertAfter(r,o.node):"inner"===d&&o.node.insertChild(r),"none"!==d&&(s.value.registerNode(r),s.value.key&&n.node.eachNode((e=>{var t;null==(t=s.value.nodesMap[e.data[s.value.key]])||t.setChecked(e.checked,!s.value.checkStrictly)}))),(0,B.vy)(o.$el,i.is("drop-inner")),t.emit("node-drag-end",n.node,o.node,d,e),"none"!==d&&t.emit("node-drop",n.node,o.node,d,e)}n&&!o&&t.emit("node-drag-end",n.node,null,d,e),l.value.showDropIndicator=!1,l.value.draggingNode=null,l.value.dropNode=null,l.value.allowDrop=!0};return(0,d.Gt)($,{treeNodeDragStart:c,treeNodeDragOver:h,treeNodeDragEnd:u}),{dragState:l}}var L=n(3860);const T=(0,d.pM)({name:"ElTreeNode",components:{ElCollapseTransition:v.o,ElCheckbox:N.dI,NodeContent:w,ElIcon:x.tk,Loading:b.Loading},props:{node:{type:k,default:()=>({})},props:{type:Object,default:()=>({})},accordion:Boolean,renderContent:Function,renderAfterExpand:Boolean,showCheckbox:{type:Boolean,default:!1}},emits:["node-expand"],setup(e,t){const n=(0,D.DU)("tree"),{broadcastExpanded:a}=S(e),s=(0,d.WQ)("RootTree"),i=(0,o.KR)(!1),l=(0,o.KR)(!1),u=(0,o.KR)(),p=(0,o.KR)(),f=(0,o.KR)(),g=(0,d.WQ)($),y=(0,d.nI)();(0,d.Gt)("NodeInstance",y),s||(0,L.U)("Tree","Can not find node's tree."),e.node.expanded&&(i.value=!0,l.value=!0);const k=s.props.props["children"]||"children";(0,d.wB)((()=>{var t;const n=null==(t=e.node.data)?void 0:t[k];return n&&[...n]}),(()=>{e.node.updateChildren()})),(0,d.wB)((()=>e.node.indeterminate),(t=>{N(e.node.checked,t)})),(0,d.wB)((()=>e.node.checked),(t=>{N(t,e.node.indeterminate)})),(0,d.wB)((()=>e.node.childNodes.length),(()=>e.node.reInitChecked())),(0,d.wB)((()=>e.node.expanded),(e=>{(0,d.dY)((()=>i.value=e)),e&&(l.value=!0)}));const C=e=>c(s.props.nodeKey,e.data),v=t=>{const n=e.props.class;if(!n)return{};let d;if((0,r.Tn)(n)){const{data:e}=t;d=n(e,t)}else d=n;return(0,r.Kg)(d)?{[d]:!0}:d},N=(t,n)=>{u.value===t&&p.value===n||s.ctx.emit("check-change",e.node.data,t,n),u.value=t,p.value=n},x=t=>{h(s.store,s.ctx.emit,(()=>{var t;const n=null==(t=null==s?void 0:s.props)?void 0:t.nodeKey;if(n){const t=C(e.node);s.store.value.setCurrentNodeKey(t)}else s.store.value.setCurrentNode(e.node)})),s.currentNode.value=e.node,s.props.expandOnClickNode&&m(),(s.props.checkOnClickNode||e.node.isLeaf&&s.props.checkOnClickLeaf&&e.showCheckbox)&&!e.node.disabled&&E(!e.node.checked),s.ctx.emit("node-click",e.node.data,e.node,y,t)},K=t=>{var n;(null==(n=s.instance.vnode.props)?void 0:n["onNodeContextmenu"])&&(t.stopPropagation(),t.preventDefault()),s.ctx.emit("node-contextmenu",t,e.node.data,e.node,y)},m=()=>{e.node.isLeaf||(i.value?(s.ctx.emit("node-collapse",e.node.data,e.node,y),e.node.collapse()):e.node.expand((()=>{t.emit("node-expand",e.node.data,e.node,y)})))},E=t=>{e.node.setChecked(t,!(null==s?void 0:s.props.checkStrictly)),(0,d.dY)((()=>{const t=s.store.value;s.ctx.emit("check",e.node.data,{checkedNodes:t.getCheckedNodes(),checkedKeys:t.getCheckedKeys(),halfCheckedNodes:t.getHalfCheckedNodes(),halfCheckedKeys:t.getHalfCheckedKeys()})}))},w=(e,t,n)=>{a(t),s.ctx.emit("node-expand",e,t,n)},B=t=>{s.props.draggable&&g.treeNodeDragStart({event:t,treeNode:e})},A=t=>{t.preventDefault(),s.props.draggable&&g.treeNodeDragOver({event:t,treeNode:{$el:f.value,node:e.node}})},T=e=>{e.preventDefault()},R=e=>{s.props.draggable&&g.treeNodeDragEnd(e)};return{ns:n,node$:f,tree:s,expanded:i,childNodeRendered:l,oldChecked:u,oldIndeterminate:p,getNodeKey:C,getNodeClass:v,handleSelectChange:N,handleClick:x,handleContextMenu:K,handleExpandIconClick:m,handleCheckChange:E,handleChildNodeExpand:w,handleDragStart:B,handleDragOver:A,handleDrop:T,handleDragEnd:R,CaretRight:b.CaretRight}}});function R(e,t,n,o,s,i){const l=(0,d.g2)("el-icon"),c=(0,d.g2)("el-checkbox"),h=(0,d.g2)("loading"),u=(0,d.g2)("node-content"),p=(0,d.g2)("el-tree-node"),f=(0,d.g2)("el-collapse-transition");return(0,d.bo)(((0,d.uX)(),(0,d.CE)("div",{ref:"node$",class:(0,r.C4)([e.ns.b("node"),e.ns.is("expanded",e.expanded),e.ns.is("current",e.node.isCurrent),e.ns.is("hidden",!e.node.visible),e.ns.is("focusable",!e.node.disabled),e.ns.is("checked",!e.node.disabled&&e.node.checked),e.getNodeClass(e.node)]),role:"treeitem",tabindex:"-1","aria-expanded":e.expanded,"aria-disabled":e.node.disabled,"aria-checked":e.node.checked,draggable:e.tree.props.draggable,"data-key":e.getNodeKey(e.node),onClick:(0,a.D$)(e.handleClick,["stop"]),onContextmenu:e.handleContextMenu,onDragstart:(0,a.D$)(e.handleDragStart,["stop"]),onDragover:(0,a.D$)(e.handleDragOver,["stop"]),onDragend:(0,a.D$)(e.handleDragEnd,["stop"]),onDrop:(0,a.D$)(e.handleDrop,["stop"])},[(0,d.Lk)("div",{class:(0,r.C4)(e.ns.be("node","content")),style:(0,r.Tr)({paddingLeft:(e.node.level-1)*e.tree.props.indent+"px"})},[e.tree.props.icon||e.CaretRight?((0,d.uX)(),(0,d.Wv)(l,{key:0,class:(0,r.C4)([e.ns.be("node","expand-icon"),e.ns.is("leaf",e.node.isLeaf),{expanded:!e.node.isLeaf&&e.expanded}]),onClick:(0,a.D$)(e.handleExpandIconClick,["stop"])},{default:(0,d.k6)((()=>[((0,d.uX)(),(0,d.Wv)((0,d.$y)(e.tree.props.icon||e.CaretRight)))])),_:1},8,["class","onClick"])):(0,d.Q3)("v-if",!0),e.showCheckbox?((0,d.uX)(),(0,d.Wv)(c,{key:1,"model-value":e.node.checked,indeterminate:e.node.indeterminate,disabled:!!e.node.disabled,onClick:(0,a.D$)((()=>{}),["stop"]),onChange:e.handleCheckChange},null,8,["model-value","indeterminate","disabled","onClick","onChange"])):(0,d.Q3)("v-if",!0),e.node.loading?((0,d.uX)(),(0,d.Wv)(l,{key:2,class:(0,r.C4)([e.ns.be("node","loading-icon"),e.ns.is("loading")])},{default:(0,d.k6)((()=>[(0,d.bF)(h)])),_:1},8,["class"])):(0,d.Q3)("v-if",!0),(0,d.bF)(u,{node:e.node,"render-content":e.renderContent},null,8,["node","render-content"])],6),(0,d.bF)(f,null,{default:(0,d.k6)((()=>[!e.renderAfterExpand||e.childNodeRendered?(0,d.bo)(((0,d.uX)(),(0,d.CE)("div",{key:0,class:(0,r.C4)(e.ns.be("node","children")),role:"group","aria-expanded":e.expanded,onClick:(0,a.D$)((()=>{}),["stop"])},[((0,d.uX)(!0),(0,d.CE)(d.FK,null,(0,d.pI)(e.node.childNodes,(t=>((0,d.uX)(),(0,d.Wv)(p,{key:e.getNodeKey(t),"render-content":e.renderContent,"render-after-expand":e.renderAfterExpand,"show-checkbox":e.showCheckbox,node:t,accordion:e.accordion,props:e.props,onNodeExpand:e.handleChildNodeExpand},null,8,["render-content","render-after-expand","show-checkbox","node","accordion","props","onNodeExpand"])))),128))],10,["aria-expanded","onClick"])),[[a.aG,e.expanded]]):(0,d.Q3)("v-if",!0)])),_:1})],42,["aria-expanded","aria-disabled","aria-checked","draggable","data-key","onClick","onContextmenu","onDragstart","onDragover","onDragend","onDrop"])),[[a.aG,e.node.visible]])}var O=(0,m.A)(T,[["render",R],["__file","tree-node.vue"]]),I=n(4319),M=n(5996);function _({el$:e},t){const n=(0,D.DU)("tree");(0,d.sV)((()=>{r()})),(0,d.$u)((()=>{const t=Array.from(e.value.querySelectorAll("input[type=checkbox]"));t.forEach((e=>{e.setAttribute("tabindex","-1")}))}));const o=d=>{const o=d.target;if(!o.className.includes(n.b("node")))return;const r=d.code,a=Array.from(e.value.querySelectorAll(`.${n.is("focusable")}[role=treeitem]`)),s=a.indexOf(o);let i;if([M.R.up,M.R.down].includes(r)){if(d.preventDefault(),r===M.R.up){i=-1===s?0:0!==s?s-1:a.length-1;const e=i;while(1){if(t.value.getNode(a[i].dataset.key).canFocus)break;if(i--,i===e){i=-1;break}i<0&&(i=a.length-1)}}else{i=-1===s?0:s<a.length-1?s+1:0;const e=i;while(1){if(t.value.getNode(a[i].dataset.key).canFocus)break;if(i++,i===e){i=-1;break}i>=a.length&&(i=0)}}-1!==i&&a[i].focus()}[M.R.left,M.R.right].includes(r)&&(d.preventDefault(),o.click());const l=o.querySelector('[type="checkbox"]');[M.R.enter,M.R.numpadEnter,M.R.space].includes(r)&&l&&(d.preventDefault(),l.click())};(0,I.MLh)(e,"keydown",o);const r=()=>{var t;const d=Array.from(e.value.querySelectorAll(`.${n.is("focusable")}[role=treeitem]`)),o=Array.from(e.value.querySelectorAll("input[type=checkbox]"));o.forEach((e=>{e.setAttribute("tabindex","-1")}));const r=e.value.querySelectorAll(`.${n.is("checked")}[role=treeitem]`);r.length?r[0].setAttribute("tabindex","0"):null==(t=d[0])||t.setAttribute("tabindex","0")}}var q=n(2571),F=n(9085),z=n(171);const W=(0,d.pM)({name:"ElTree",components:{ElTreeNode:O},props:{data:{type:Array,default:()=>[]},emptyText:{type:String},renderAfterExpand:{type:Boolean,default:!0},nodeKey:String,checkStrictly:Boolean,defaultExpandAll:Boolean,expandOnClickNode:{type:Boolean,default:!0},checkOnClickNode:Boolean,checkOnClickLeaf:{type:Boolean,default:!0},checkDescendants:{type:Boolean,default:!1},autoExpandParent:{type:Boolean,default:!0},defaultCheckedKeys:Array,defaultExpandedKeys:Array,currentNodeKey:[String,Number],renderContent:Function,showCheckbox:{type:Boolean,default:!1},draggable:{type:Boolean,default:!1},allowDrag:Function,allowDrop:Function,props:{type:Object,default:()=>({children:"children",label:"label",disabled:"disabled"})},lazy:{type:Boolean,default:!1},highlightCurrent:Boolean,load:Function,filterNodeMethod:Function,accordion:Boolean,indent:{type:Number,default:18},icon:{type:q.Ze}},emits:["check-change","current-change","node-click","node-contextmenu","node-collapse","node-expand","check","node-drag-start","node-drag-end","node-drop","node-drag-leave","node-drag-enter","node-drag-over"],setup(e,t){const{t:n}=(0,F.Ym)(),r=(0,D.DU)("tree"),a=(0,d.WQ)(s.u,null),i=(0,o.KR)(new C({key:e.nodeKey,data:e.data,lazy:e.lazy,props:e.props,load:e.load,currentNodeKey:e.currentNodeKey,checkStrictly:e.checkStrictly,checkDescendants:e.checkDescendants,defaultCheckedKeys:e.defaultCheckedKeys,defaultExpandedKeys:e.defaultExpandedKeys,autoExpandParent:e.autoExpandParent,defaultExpandAll:e.defaultExpandAll,filterNodeMethod:e.filterNodeMethod}));i.value.initialize();const l=(0,o.KR)(i.value.root),u=(0,o.KR)(null),p=(0,o.KR)(null),f=(0,o.KR)(null),{broadcastExpanded:g}=S(e),{dragState:y}=A({props:e,ctx:t,el$:p,dropIndicator$:f,store:i});_({el$:p},i);const k=(0,d.EW)((()=>{const{childNodes:e}=l.value,t=!!a&&0!==a.hasFilteredOptions;return(!e||0===e.length||e.every((({visible:e})=>!e)))&&!t}));(0,d.wB)((()=>e.currentNodeKey),(e=>{i.value.setCurrentNodeKey(e)})),(0,d.wB)((()=>e.defaultCheckedKeys),(e=>{i.value.setDefaultCheckedKey(e)})),(0,d.wB)((()=>e.defaultExpandedKeys),(e=>{i.value.setDefaultExpandedKeys(e)})),(0,d.wB)((()=>e.data),(e=>{i.value.setData(e)}),{deep:!0}),(0,d.wB)((()=>e.checkStrictly),(e=>{i.value.checkStrictly=e}));const v=t=>{if(!e.filterNodeMethod)throw new Error("[Tree] filterNodeMethod is required when filter");i.value.filter(t)},N=t=>c(e.nodeKey,t.data),x=t=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in getNodePath");const n=i.value.getNode(t);if(!n)return[];const d=[n.data];let o=n.parent;while(o&&o!==l.value)d.push(o.data),o=o.parent;return d.reverse()},b=(e,t)=>i.value.getCheckedNodes(e,t),K=e=>i.value.getCheckedKeys(e),m=()=>{const e=i.value.getCurrentNode();return e?e.data:null},E=()=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in getCurrentKey");const t=m();return t?t[e.nodeKey]:null},w=(t,n)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCheckedNodes");i.value.setCheckedNodes(t,n)},B=(t,n)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCheckedKeys");i.value.setCheckedKeys(t,n)},$=(e,t,n)=>{i.value.setChecked(e,t,n)},L=()=>i.value.getHalfCheckedNodes(),T=()=>i.value.getHalfCheckedKeys(),R=(n,d=!0)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCurrentNode");h(i,t.emit,(()=>{g(n),i.value.setUserCurrentNode(n,d)}))},O=(n,d=!0)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCurrentKey");h(i,t.emit,(()=>{g(),i.value.setCurrentNodeKey(n,d)}))},I=e=>i.value.getNode(e),M=e=>{i.value.remove(e)},q=(e,t)=>{i.value.append(e,t)},W=(e,t)=>{i.value.insertBefore(e,t)},X=(e,t)=>{i.value.insertAfter(e,t)},U=(e,n,d)=>{g(n),t.emit("node-expand",e,n,d)},G=(t,n)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in updateKeyChild");i.value.updateChildren(t,n)};return(0,d.Gt)("RootTree",{ctx:t,props:e,store:i,root:l,currentNode:u,instance:(0,d.nI)()}),(0,d.Gt)(z.w,void 0),{ns:r,store:i,root:l,currentNode:u,dragState:y,el$:p,dropIndicator$:f,isEmpty:k,filter:v,getNodeKey:N,getNodePath:x,getCheckedNodes:b,getCheckedKeys:K,getCurrentNode:m,getCurrentKey:E,setCheckedNodes:w,setCheckedKeys:B,setChecked:$,getHalfCheckedNodes:L,getHalfCheckedKeys:T,setCurrentNode:R,setCurrentKey:O,t:n,getNode:I,remove:M,append:q,insertBefore:W,insertAfter:X,handleNodeExpand:U,updateKeyChildren:G}}});function X(e,t,n,o,s,i){const l=(0,d.g2)("el-tree-node");return(0,d.uX)(),(0,d.CE)("div",{ref:"el$",class:(0,r.C4)([e.ns.b(),e.ns.is("dragging",!!e.dragState.draggingNode),e.ns.is("drop-not-allow",!e.dragState.allowDrop),e.ns.is("drop-inner","inner"===e.dragState.dropType),{[e.ns.m("highlight-current")]:e.highlightCurrent}]),role:"tree"},[((0,d.uX)(!0),(0,d.CE)(d.FK,null,(0,d.pI)(e.root.childNodes,(t=>((0,d.uX)(),(0,d.Wv)(l,{key:e.getNodeKey(t),node:t,props:e.props,accordion:e.accordion,"render-after-expand":e.renderAfterExpand,"show-checkbox":e.showCheckbox,"render-content":e.renderContent,onNodeExpand:e.handleNodeExpand},null,8,["node","props","accordion","render-after-expand","show-checkbox","render-content","onNodeExpand"])))),128)),e.isEmpty?((0,d.uX)(),(0,d.CE)("div",{key:0,class:(0,r.C4)(e.ns.e("empty-block"))},[(0,d.RG)(e.$slots,"empty",{},(()=>{var t;return[(0,d.Lk)("span",{class:(0,r.C4)(e.ns.e("empty-text"))},(0,r.v_)(null!=(t=e.emptyText)?t:e.t("el.tree.emptyText")),3)]}))],2)):(0,d.Q3)("v-if",!0),(0,d.bo)((0,d.Lk)("div",{ref:"dropIndicator$",class:(0,r.C4)(e.ns.e("drop-indicator"))},null,2),[[a.aG,e.dragState.showDropIndicator]])],2)}var U=(0,m.A)(W,[["render",X],["__file","tree.vue"]]),G=n(8677);const Q=(0,G.GU)(U)}}]);