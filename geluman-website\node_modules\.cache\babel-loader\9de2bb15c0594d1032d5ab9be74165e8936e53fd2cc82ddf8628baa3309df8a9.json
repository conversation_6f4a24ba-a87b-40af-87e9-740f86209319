{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { reactive, ref, computed, watch, watchEffect, nextTick, onMounted } from 'vue';\nimport { castArray, isEqual, get, debounce, findLastIndex } from 'lodash-unified';\nimport { isIOS, isClient, useResizeObserver } from '@vueuse/core';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useComposition } from '../../../hooks/use-composition/index.mjs';\nimport { useFocusController } from '../../../hooks/use-focus-controller/index.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { useFormItem, useFormItemInputId } from '../../form/src/hooks/use-form-item.mjs';\nimport { useEmptyValues } from '../../../hooks/use-empty-values/index.mjs';\nimport { isArray, isFunction, isPlainObject, isObject } from '@vue/shared';\nimport { ValidateComponentsMap } from '../../../utils/vue/icon.mjs';\nimport { useFormSize } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { isUndefined, isNumber } from '../../../utils/types.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { scrollIntoView } from '../../../utils/dom/scroll.mjs';\nconst useSelect = (props, emit) => {\n  const {\n    t\n  } = useLocale();\n  const contentId = useId();\n  const nsSelect = useNamespace(\"select\");\n  const nsInput = useNamespace(\"input\");\n  const states = reactive({\n    inputValue: \"\",\n    options: /* @__PURE__ */new Map(),\n    cachedOptions: /* @__PURE__ */new Map(),\n    optionValues: [],\n    selected: [],\n    selectionWidth: 0,\n    collapseItemWidth: 0,\n    selectedLabel: \"\",\n    hoveringIndex: -1,\n    previousQuery: null,\n    inputHovering: false,\n    menuVisibleOnFocus: false,\n    isBeforeHide: false\n  });\n  const selectRef = ref();\n  const selectionRef = ref();\n  const tooltipRef = ref();\n  const tagTooltipRef = ref();\n  const inputRef = ref();\n  const prefixRef = ref();\n  const suffixRef = ref();\n  const menuRef = ref();\n  const tagMenuRef = ref();\n  const collapseItemRef = ref();\n  const scrollbarRef = ref();\n  const {\n    isComposing,\n    handleCompositionStart,\n    handleCompositionUpdate,\n    handleCompositionEnd\n  } = useComposition({\n    afterComposition: e => onInput(e)\n  });\n  const {\n    wrapperRef,\n    isFocused,\n    handleBlur\n  } = useFocusController(inputRef, {\n    beforeFocus() {\n      return selectDisabled.value;\n    },\n    afterFocus() {\n      if (props.automaticDropdown && !expanded.value) {\n        expanded.value = true;\n        states.menuVisibleOnFocus = true;\n      }\n    },\n    beforeBlur(event) {\n      var _a, _b;\n      return ((_a = tooltipRef.value) == null ? void 0 : _a.isFocusInsideContent(event)) || ((_b = tagTooltipRef.value) == null ? void 0 : _b.isFocusInsideContent(event));\n    },\n    afterBlur() {\n      var _a;\n      expanded.value = false;\n      states.menuVisibleOnFocus = false;\n      if (props.validateEvent) {\n        (_a = formItem == null ? void 0 : formItem.validate) == null ? void 0 : _a.call(formItem, \"blur\").catch(err => debugWarn(err));\n      }\n    }\n  });\n  const expanded = ref(false);\n  const hoverOption = ref();\n  const {\n    form,\n    formItem\n  } = useFormItem();\n  const {\n    inputId\n  } = useFormItemInputId(props, {\n    formItemContext: formItem\n  });\n  const {\n    valueOnClear,\n    isEmptyValue\n  } = useEmptyValues(props);\n  const selectDisabled = computed(() => props.disabled || (form == null ? void 0 : form.disabled));\n  const hasModelValue = computed(() => {\n    return isArray(props.modelValue) ? props.modelValue.length > 0 : !isEmptyValue(props.modelValue);\n  });\n  const needStatusIcon = computed(() => {\n    var _a;\n    return (_a = form == null ? void 0 : form.statusIcon) != null ? _a : false;\n  });\n  const showClose = computed(() => {\n    return props.clearable && !selectDisabled.value && states.inputHovering && hasModelValue.value;\n  });\n  const iconComponent = computed(() => props.remote && props.filterable && !props.remoteShowSuffix ? \"\" : props.suffixIcon);\n  const iconReverse = computed(() => nsSelect.is(\"reverse\", !!(iconComponent.value && expanded.value)));\n  const validateState = computed(() => (formItem == null ? void 0 : formItem.validateState) || \"\");\n  const validateIcon = computed(() => validateState.value && ValidateComponentsMap[validateState.value]);\n  const debounce$1 = computed(() => props.remote ? 300 : 0);\n  const isRemoteSearchEmpty = computed(() => props.remote && !states.inputValue && states.options.size === 0);\n  const emptyText = computed(() => {\n    if (props.loading) {\n      return props.loadingText || t(\"el.select.loading\");\n    } else {\n      if (props.filterable && states.inputValue && states.options.size > 0 && filteredOptionsCount.value === 0) {\n        return props.noMatchText || t(\"el.select.noMatch\");\n      }\n      if (states.options.size === 0) {\n        return props.noDataText || t(\"el.select.noData\");\n      }\n    }\n    return null;\n  });\n  const filteredOptionsCount = computed(() => optionsArray.value.filter(option => option.visible).length);\n  const optionsArray = computed(() => {\n    const list = Array.from(states.options.values());\n    const newList = [];\n    states.optionValues.forEach(item => {\n      const index = list.findIndex(i => i.value === item);\n      if (index > -1) {\n        newList.push(list[index]);\n      }\n    });\n    return newList.length >= list.length ? newList : list;\n  });\n  const cachedOptionsArray = computed(() => Array.from(states.cachedOptions.values()));\n  const showNewOption = computed(() => {\n    const hasExistingOption = optionsArray.value.filter(option => {\n      return !option.created;\n    }).some(option => {\n      return option.currentLabel === states.inputValue;\n    });\n    return props.filterable && props.allowCreate && states.inputValue !== \"\" && !hasExistingOption;\n  });\n  const updateOptions = () => {\n    if (props.filterable && isFunction(props.filterMethod)) return;\n    if (props.filterable && props.remote && isFunction(props.remoteMethod)) return;\n    optionsArray.value.forEach(option => {\n      var _a;\n      (_a = option.updateOption) == null ? void 0 : _a.call(option, states.inputValue);\n    });\n  };\n  const selectSize = useFormSize();\n  const collapseTagSize = computed(() => [\"small\"].includes(selectSize.value) ? \"small\" : \"default\");\n  const dropdownMenuVisible = computed({\n    get() {\n      return expanded.value && !isRemoteSearchEmpty.value;\n    },\n    set(val) {\n      expanded.value = val;\n    }\n  });\n  const shouldShowPlaceholder = computed(() => {\n    if (props.multiple && !isUndefined(props.modelValue)) {\n      return castArray(props.modelValue).length === 0 && !states.inputValue;\n    }\n    const value = isArray(props.modelValue) ? props.modelValue[0] : props.modelValue;\n    return props.filterable || isUndefined(value) ? !states.inputValue : true;\n  });\n  const currentPlaceholder = computed(() => {\n    var _a;\n    const _placeholder = (_a = props.placeholder) != null ? _a : t(\"el.select.placeholder\");\n    return props.multiple || !hasModelValue.value ? _placeholder : states.selectedLabel;\n  });\n  const mouseEnterEventName = computed(() => isIOS ? null : \"mouseenter\");\n  watch(() => props.modelValue, (val, oldVal) => {\n    if (props.multiple) {\n      if (props.filterable && !props.reserveKeyword) {\n        states.inputValue = \"\";\n        handleQueryChange(\"\");\n      }\n    }\n    setSelected();\n    if (!isEqual(val, oldVal) && props.validateEvent) {\n      formItem == null ? void 0 : formItem.validate(\"change\").catch(err => debugWarn(err));\n    }\n  }, {\n    flush: \"post\",\n    deep: true\n  });\n  watch(() => expanded.value, val => {\n    if (val) {\n      handleQueryChange(states.inputValue);\n    } else {\n      states.inputValue = \"\";\n      states.previousQuery = null;\n      states.isBeforeHide = true;\n    }\n    emit(\"visible-change\", val);\n  });\n  watch(() => states.options.entries(), () => {\n    if (!isClient) return;\n    setSelected();\n    if (props.defaultFirstOption && (props.filterable || props.remote) && filteredOptionsCount.value) {\n      checkDefaultFirstOption();\n    }\n  }, {\n    flush: \"post\"\n  });\n  watch([() => states.hoveringIndex, optionsArray], ([val]) => {\n    if (isNumber(val) && val > -1) {\n      hoverOption.value = optionsArray.value[val] || {};\n    } else {\n      hoverOption.value = {};\n    }\n    optionsArray.value.forEach(option => {\n      option.hover = hoverOption.value === option;\n    });\n  });\n  watchEffect(() => {\n    if (states.isBeforeHide) return;\n    updateOptions();\n  });\n  const handleQueryChange = val => {\n    if (states.previousQuery === val || isComposing.value) {\n      return;\n    }\n    states.previousQuery = val;\n    if (props.filterable && isFunction(props.filterMethod)) {\n      props.filterMethod(val);\n    } else if (props.filterable && props.remote && isFunction(props.remoteMethod)) {\n      props.remoteMethod(val);\n    }\n    if (props.defaultFirstOption && (props.filterable || props.remote) && filteredOptionsCount.value) {\n      nextTick(checkDefaultFirstOption);\n    } else {\n      nextTick(updateHoveringIndex);\n    }\n  };\n  const checkDefaultFirstOption = () => {\n    const optionsInDropdown = optionsArray.value.filter(n => n.visible && !n.disabled && !n.states.groupDisabled);\n    const userCreatedOption = optionsInDropdown.find(n => n.created);\n    const firstOriginOption = optionsInDropdown[0];\n    const valueList = optionsArray.value.map(item => item.value);\n    states.hoveringIndex = getValueIndex(valueList, userCreatedOption || firstOriginOption);\n  };\n  const setSelected = () => {\n    if (!props.multiple) {\n      const value = isArray(props.modelValue) ? props.modelValue[0] : props.modelValue;\n      const option = getOption(value);\n      states.selectedLabel = option.currentLabel;\n      states.selected = [option];\n      return;\n    } else {\n      states.selectedLabel = \"\";\n    }\n    const result = [];\n    if (!isUndefined(props.modelValue)) {\n      castArray(props.modelValue).forEach(value => {\n        result.push(getOption(value));\n      });\n    }\n    states.selected = result;\n  };\n  const getOption = value => {\n    let option;\n    const isObjectValue = isPlainObject(value);\n    for (let i = states.cachedOptions.size - 1; i >= 0; i--) {\n      const cachedOption = cachedOptionsArray.value[i];\n      const isEqualValue = isObjectValue ? get(cachedOption.value, props.valueKey) === get(value, props.valueKey) : cachedOption.value === value;\n      if (isEqualValue) {\n        option = {\n          value,\n          currentLabel: cachedOption.currentLabel,\n          get isDisabled() {\n            return cachedOption.isDisabled;\n          }\n        };\n        break;\n      }\n    }\n    if (option) return option;\n    const label = isObjectValue ? value.label : value != null ? value : \"\";\n    const newOption = {\n      value,\n      currentLabel: label\n    };\n    return newOption;\n  };\n  const updateHoveringIndex = () => {\n    states.hoveringIndex = optionsArray.value.findIndex(item => states.selected.some(selected => getValueKey(selected) === getValueKey(item)));\n  };\n  const resetSelectionWidth = () => {\n    states.selectionWidth = selectionRef.value.getBoundingClientRect().width;\n  };\n  const resetCollapseItemWidth = () => {\n    states.collapseItemWidth = collapseItemRef.value.getBoundingClientRect().width;\n  };\n  const updateTooltip = () => {\n    var _a, _b;\n    (_b = (_a = tooltipRef.value) == null ? void 0 : _a.updatePopper) == null ? void 0 : _b.call(_a);\n  };\n  const updateTagTooltip = () => {\n    var _a, _b;\n    (_b = (_a = tagTooltipRef.value) == null ? void 0 : _a.updatePopper) == null ? void 0 : _b.call(_a);\n  };\n  const onInputChange = () => {\n    if (states.inputValue.length > 0 && !expanded.value) {\n      expanded.value = true;\n    }\n    handleQueryChange(states.inputValue);\n  };\n  const onInput = event => {\n    states.inputValue = event.target.value;\n    if (props.remote) {\n      debouncedOnInputChange();\n    } else {\n      return onInputChange();\n    }\n  };\n  const debouncedOnInputChange = debounce(() => {\n    onInputChange();\n  }, debounce$1.value);\n  const emitChange = val => {\n    if (!isEqual(props.modelValue, val)) {\n      emit(CHANGE_EVENT, val);\n    }\n  };\n  const getLastNotDisabledIndex = value => findLastIndex(value, it => {\n    const option = states.cachedOptions.get(it);\n    return option && !option.disabled && !option.states.groupDisabled;\n  });\n  const deletePrevTag = e => {\n    if (!props.multiple) return;\n    if (e.code === EVENT_CODE.delete) return;\n    if (e.target.value.length <= 0) {\n      const value = castArray(props.modelValue).slice();\n      const lastNotDisabledIndex = getLastNotDisabledIndex(value);\n      if (lastNotDisabledIndex < 0) return;\n      const removeTagValue = value[lastNotDisabledIndex];\n      value.splice(lastNotDisabledIndex, 1);\n      emit(UPDATE_MODEL_EVENT, value);\n      emitChange(value);\n      emit(\"remove-tag\", removeTagValue);\n    }\n  };\n  const deleteTag = (event, tag) => {\n    const index = states.selected.indexOf(tag);\n    if (index > -1 && !selectDisabled.value) {\n      const value = castArray(props.modelValue).slice();\n      value.splice(index, 1);\n      emit(UPDATE_MODEL_EVENT, value);\n      emitChange(value);\n      emit(\"remove-tag\", tag.value);\n    }\n    event.stopPropagation();\n    focus();\n  };\n  const deleteSelected = event => {\n    event.stopPropagation();\n    const value = props.multiple ? [] : valueOnClear.value;\n    if (props.multiple) {\n      for (const item of states.selected) {\n        if (item.isDisabled) value.push(item.value);\n      }\n    }\n    emit(UPDATE_MODEL_EVENT, value);\n    emitChange(value);\n    states.hoveringIndex = -1;\n    expanded.value = false;\n    emit(\"clear\");\n    focus();\n  };\n  const handleOptionSelect = option => {\n    var _a;\n    if (props.multiple) {\n      const value = castArray((_a = props.modelValue) != null ? _a : []).slice();\n      const optionIndex = getValueIndex(value, option);\n      if (optionIndex > -1) {\n        value.splice(optionIndex, 1);\n      } else if (props.multipleLimit <= 0 || value.length < props.multipleLimit) {\n        value.push(option.value);\n      }\n      emit(UPDATE_MODEL_EVENT, value);\n      emitChange(value);\n      if (option.created) {\n        handleQueryChange(\"\");\n      }\n      if (props.filterable && !props.reserveKeyword) {\n        states.inputValue = \"\";\n      }\n    } else {\n      emit(UPDATE_MODEL_EVENT, option.value);\n      emitChange(option.value);\n      expanded.value = false;\n    }\n    focus();\n    if (expanded.value) return;\n    nextTick(() => {\n      scrollToOption(option);\n    });\n  };\n  const getValueIndex = (arr, option) => {\n    if (isUndefined(option)) return -1;\n    if (!isObject(option.value)) return arr.indexOf(option.value);\n    return arr.findIndex(item => {\n      return isEqual(get(item, props.valueKey), getValueKey(option));\n    });\n  };\n  const scrollToOption = option => {\n    var _a, _b, _c, _d, _e;\n    const targetOption = isArray(option) ? option[0] : option;\n    let target = null;\n    if (targetOption == null ? void 0 : targetOption.value) {\n      const options = optionsArray.value.filter(item => item.value === targetOption.value);\n      if (options.length > 0) {\n        target = options[0].$el;\n      }\n    }\n    if (tooltipRef.value && target) {\n      const menu = (_d = (_c = (_b = (_a = tooltipRef.value) == null ? void 0 : _a.popperRef) == null ? void 0 : _b.contentRef) == null ? void 0 : _c.querySelector) == null ? void 0 : _d.call(_c, `.${nsSelect.be(\"dropdown\", \"wrap\")}`);\n      if (menu) {\n        scrollIntoView(menu, target);\n      }\n    }\n    (_e = scrollbarRef.value) == null ? void 0 : _e.handleScroll();\n  };\n  const onOptionCreate = vm => {\n    states.options.set(vm.value, vm);\n    states.cachedOptions.set(vm.value, vm);\n  };\n  const onOptionDestroy = (key, vm) => {\n    if (states.options.get(key) === vm) {\n      states.options.delete(key);\n    }\n  };\n  const popperRef = computed(() => {\n    var _a, _b;\n    return (_b = (_a = tooltipRef.value) == null ? void 0 : _a.popperRef) == null ? void 0 : _b.contentRef;\n  });\n  const handleMenuEnter = () => {\n    states.isBeforeHide = false;\n    nextTick(() => {\n      var _a;\n      (_a = scrollbarRef.value) == null ? void 0 : _a.update();\n      scrollToOption(states.selected);\n    });\n  };\n  const focus = () => {\n    var _a;\n    (_a = inputRef.value) == null ? void 0 : _a.focus();\n  };\n  const blur = () => {\n    var _a;\n    if (expanded.value) {\n      expanded.value = false;\n      nextTick(() => {\n        var _a2;\n        return (_a2 = inputRef.value) == null ? void 0 : _a2.blur();\n      });\n      return;\n    }\n    (_a = inputRef.value) == null ? void 0 : _a.blur();\n  };\n  const handleClearClick = event => {\n    deleteSelected(event);\n  };\n  const handleClickOutside = event => {\n    expanded.value = false;\n    if (isFocused.value) {\n      const _event = new FocusEvent(\"focus\", event);\n      nextTick(() => handleBlur(_event));\n    }\n  };\n  const handleEsc = () => {\n    if (states.inputValue.length > 0) {\n      states.inputValue = \"\";\n    } else {\n      expanded.value = false;\n    }\n  };\n  const toggleMenu = () => {\n    if (selectDisabled.value) return;\n    if (isIOS) states.inputHovering = true;\n    if (states.menuVisibleOnFocus) {\n      states.menuVisibleOnFocus = false;\n    } else {\n      expanded.value = !expanded.value;\n    }\n  };\n  const selectOption = () => {\n    if (!expanded.value) {\n      toggleMenu();\n    } else {\n      const option = optionsArray.value[states.hoveringIndex];\n      if (option && !option.isDisabled) {\n        handleOptionSelect(option);\n      }\n    }\n  };\n  const getValueKey = item => {\n    return isObject(item.value) ? get(item.value, props.valueKey) : item.value;\n  };\n  const optionsAllDisabled = computed(() => optionsArray.value.filter(option => option.visible).every(option => option.isDisabled));\n  const showTagList = computed(() => {\n    if (!props.multiple) {\n      return [];\n    }\n    return props.collapseTags ? states.selected.slice(0, props.maxCollapseTags) : states.selected;\n  });\n  const collapseTagList = computed(() => {\n    if (!props.multiple) {\n      return [];\n    }\n    return props.collapseTags ? states.selected.slice(props.maxCollapseTags) : [];\n  });\n  const navigateOptions = direction => {\n    if (!expanded.value) {\n      expanded.value = true;\n      return;\n    }\n    if (states.options.size === 0 || filteredOptionsCount.value === 0 || isComposing.value) return;\n    if (!optionsAllDisabled.value) {\n      if (direction === \"next\") {\n        states.hoveringIndex++;\n        if (states.hoveringIndex === states.options.size) {\n          states.hoveringIndex = 0;\n        }\n      } else if (direction === \"prev\") {\n        states.hoveringIndex--;\n        if (states.hoveringIndex < 0) {\n          states.hoveringIndex = states.options.size - 1;\n        }\n      }\n      const option = optionsArray.value[states.hoveringIndex];\n      if (option.isDisabled || !option.visible) {\n        navigateOptions(direction);\n      }\n      nextTick(() => scrollToOption(hoverOption.value));\n    }\n  };\n  const getGapWidth = () => {\n    if (!selectionRef.value) return 0;\n    const style = window.getComputedStyle(selectionRef.value);\n    return Number.parseFloat(style.gap || \"6px\");\n  };\n  const tagStyle = computed(() => {\n    const gapWidth = getGapWidth();\n    const maxWidth = collapseItemRef.value && props.maxCollapseTags === 1 ? states.selectionWidth - states.collapseItemWidth - gapWidth : states.selectionWidth;\n    return {\n      maxWidth: `${maxWidth}px`\n    };\n  });\n  const collapseTagStyle = computed(() => {\n    return {\n      maxWidth: `${states.selectionWidth}px`\n    };\n  });\n  const popupScroll = data => {\n    emit(\"popup-scroll\", data);\n  };\n  useResizeObserver(selectionRef, resetSelectionWidth);\n  useResizeObserver(menuRef, updateTooltip);\n  useResizeObserver(wrapperRef, updateTooltip);\n  useResizeObserver(tagMenuRef, updateTagTooltip);\n  useResizeObserver(collapseItemRef, resetCollapseItemWidth);\n  onMounted(() => {\n    setSelected();\n  });\n  return {\n    inputId,\n    contentId,\n    nsSelect,\n    nsInput,\n    states,\n    isFocused,\n    expanded,\n    optionsArray,\n    hoverOption,\n    selectSize,\n    filteredOptionsCount,\n    updateTooltip,\n    updateTagTooltip,\n    debouncedOnInputChange,\n    onInput,\n    deletePrevTag,\n    deleteTag,\n    deleteSelected,\n    handleOptionSelect,\n    scrollToOption,\n    hasModelValue,\n    shouldShowPlaceholder,\n    currentPlaceholder,\n    mouseEnterEventName,\n    needStatusIcon,\n    showClose,\n    iconComponent,\n    iconReverse,\n    validateState,\n    validateIcon,\n    showNewOption,\n    updateOptions,\n    collapseTagSize,\n    setSelected,\n    selectDisabled,\n    emptyText,\n    handleCompositionStart,\n    handleCompositionUpdate,\n    handleCompositionEnd,\n    onOptionCreate,\n    onOptionDestroy,\n    handleMenuEnter,\n    focus,\n    blur,\n    handleClearClick,\n    handleClickOutside,\n    handleEsc,\n    toggleMenu,\n    selectOption,\n    getValueKey,\n    navigateOptions,\n    dropdownMenuVisible,\n    showTagList,\n    collapseTagList,\n    popupScroll,\n    tagStyle,\n    collapseTagStyle,\n    popperRef,\n    inputRef,\n    tooltipRef,\n    tagTooltipRef,\n    prefixRef,\n    suffixRef,\n    selectRef,\n    wrapperRef,\n    selectionRef,\n    scrollbarRef,\n    menuRef,\n    tagMenuRef,\n    collapseItemRef\n  };\n};\nexport { useSelect };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}