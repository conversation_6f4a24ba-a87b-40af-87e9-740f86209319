{"ast": null, "code": "import { isClient } from '@vueuse/core';\nimport { isString } from '@vue/shared';\nconst getElement = target => {\n  if (!isClient || target === \"\") return null;\n  if (isString(target)) {\n    try {\n      return document.querySelector(target);\n    } catch (e) {\n      return null;\n    }\n  }\n  return target;\n};\nexport { getElement };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}