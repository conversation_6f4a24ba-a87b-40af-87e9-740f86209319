{"ast": null, "code": "import { getCurrentInstance, computed } from 'vue';\nconst useProp = name => {\n  const vm = getCurrentInstance();\n  return computed(() => {\n    var _a, _b;\n    return (_b = (_a = vm == null ? void 0 : vm.proxy) == null ? void 0 : _a.$props) == null ? void 0 : _b[name];\n  });\n};\nexport { useProp };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}