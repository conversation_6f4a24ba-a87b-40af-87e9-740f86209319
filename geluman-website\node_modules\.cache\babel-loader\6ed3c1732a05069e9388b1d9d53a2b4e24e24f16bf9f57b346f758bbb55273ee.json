{"ast": null, "code": "import { useTooltipTriggerProps } from '../../tooltip/src/trigger.mjs';\nimport { roleTypes } from '../../popper/src/popper.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { useTooltipContentProps } from '../../tooltip/src/content.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nimport { createCollectionWithScope } from '../../collection/src/collection2.mjs';\nconst dropdownProps = buildProps({\n  trigger: useTooltipTriggerProps.trigger,\n  triggerKeys: {\n    type: definePropType(Array),\n    default: () => [EVENT_CODE.enter, EVENT_CODE.numpadEnter, EVENT_CODE.space, EVENT_CODE.down]\n  },\n  effect: {\n    ...useTooltipContentProps.effect,\n    default: \"light\"\n  },\n  type: {\n    type: definePropType(String)\n  },\n  placement: {\n    type: definePropType(String),\n    default: \"bottom\"\n  },\n  popperOptions: {\n    type: definePropType(Object),\n    default: () => ({})\n  },\n  id: String,\n  size: {\n    type: String,\n    default: \"\"\n  },\n  splitButton: Boolean,\n  hideOnClick: {\n    type: Boolean,\n    default: true\n  },\n  loop: {\n    type: Boolean,\n    default: true\n  },\n  showTimeout: {\n    type: Number,\n    default: 150\n  },\n  hideTimeout: {\n    type: Number,\n    default: 150\n  },\n  tabindex: {\n    type: definePropType([Number, String]),\n    default: 0\n  },\n  maxHeight: {\n    type: definePropType([Number, String]),\n    default: \"\"\n  },\n  popperClass: {\n    type: String,\n    default: \"\"\n  },\n  disabled: Boolean,\n  role: {\n    type: String,\n    values: roleTypes,\n    default: \"menu\"\n  },\n  buttonProps: {\n    type: definePropType(Object)\n  },\n  teleported: useTooltipContentProps.teleported,\n  persistent: {\n    type: Boolean,\n    default: true\n  }\n});\nconst dropdownItemProps = buildProps({\n  command: {\n    type: [Object, String, Number],\n    default: () => ({})\n  },\n  disabled: Boolean,\n  divided: Boolean,\n  textValue: String,\n  icon: {\n    type: iconPropType\n  }\n});\nconst dropdownMenuProps = buildProps({\n  onKeydown: {\n    type: definePropType(Function)\n  }\n});\nconst FIRST_KEYS = [EVENT_CODE.down, EVENT_CODE.pageDown, EVENT_CODE.home];\nconst LAST_KEYS = [EVENT_CODE.up, EVENT_CODE.pageUp, EVENT_CODE.end];\nconst FIRST_LAST_KEYS = [...FIRST_KEYS, ...LAST_KEYS];\nconst {\n  ElCollection,\n  ElCollectionItem,\n  COLLECTION_INJECTION_KEY,\n  COLLECTION_ITEM_INJECTION_KEY\n} = createCollectionWithScope(\"Dropdown\");\nexport { COLLECTION_INJECTION_KEY as DROPDOWN_COLLECTION_INJECTION_KEY, COLLECTION_ITEM_INJECTION_KEY as DROPDOWN_COLLECTION_ITEM_INJECTION_KEY, ElCollection, ElCollectionItem, FIRST_KEYS, FIRST_LAST_KEYS, LAST_KEYS, dropdownItemProps, dropdownMenuProps, dropdownProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}