{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { computed, unref, shallowRef, ref, watch, onBeforeUnmount } from 'vue';\nimport { createPopper } from '@popperjs/core';\nimport { fromPairs } from 'lodash-unified';\nconst usePopper = (referenceElementRef, popperElementRef, opts = {}) => {\n  const stateUpdater = {\n    name: \"updateState\",\n    enabled: true,\n    phase: \"write\",\n    fn: ({\n      state\n    }) => {\n      const derivedState = deriveState(state);\n      Object.assign(states.value, derivedState);\n    },\n    requires: [\"computeStyles\"]\n  };\n  const options = computed(() => {\n    const {\n      onFirstUpdate,\n      placement,\n      strategy,\n      modifiers\n    } = unref(opts);\n    return {\n      onFirstUpdate,\n      placement: placement || \"bottom\",\n      strategy: strategy || \"absolute\",\n      modifiers: [...(modifiers || []), stateUpdater, {\n        name: \"applyStyles\",\n        enabled: false\n      }]\n    };\n  });\n  const instanceRef = shallowRef();\n  const states = ref({\n    styles: {\n      popper: {\n        position: unref(options).strategy,\n        left: \"0\",\n        top: \"0\"\n      },\n      arrow: {\n        position: \"absolute\"\n      }\n    },\n    attributes: {}\n  });\n  const destroy = () => {\n    if (!instanceRef.value) return;\n    instanceRef.value.destroy();\n    instanceRef.value = void 0;\n  };\n  watch(options, newOptions => {\n    const instance = unref(instanceRef);\n    if (instance) {\n      instance.setOptions(newOptions);\n    }\n  }, {\n    deep: true\n  });\n  watch([referenceElementRef, popperElementRef], ([referenceElement, popperElement]) => {\n    destroy();\n    if (!referenceElement || !popperElement) return;\n    instanceRef.value = createPopper(referenceElement, popperElement, unref(options));\n  });\n  onBeforeUnmount(() => {\n    destroy();\n  });\n  return {\n    state: computed(() => {\n      var _a;\n      return {\n        ...(((_a = unref(instanceRef)) == null ? void 0 : _a.state) || {})\n      };\n    }),\n    styles: computed(() => unref(states).styles),\n    attributes: computed(() => unref(states).attributes),\n    update: () => {\n      var _a;\n      return (_a = unref(instanceRef)) == null ? void 0 : _a.update();\n    },\n    forceUpdate: () => {\n      var _a;\n      return (_a = unref(instanceRef)) == null ? void 0 : _a.forceUpdate();\n    },\n    instanceRef: computed(() => unref(instanceRef))\n  };\n};\nfunction deriveState(state) {\n  const elements = Object.keys(state.elements);\n  const styles = fromPairs(elements.map(element => [element, state.styles[element] || {}]));\n  const attributes = fromPairs(elements.map(element => [element, state.attributes[element]]));\n  return {\n    styles,\n    attributes\n  };\n}\nexport { usePopper };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}