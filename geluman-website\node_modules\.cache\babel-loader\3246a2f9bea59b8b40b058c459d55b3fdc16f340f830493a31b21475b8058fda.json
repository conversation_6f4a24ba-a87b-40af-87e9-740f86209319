{"ast": null, "code": "import baseConformsTo from './_baseConformsTo.js';\nimport keys from './keys.js';\n\n/**\n * Checks if `object` conforms to `source` by invoking the predicate\n * properties of `source` with the corresponding property values of `object`.\n *\n * **Note:** This method is equivalent to `_.conforms` when `source` is\n * partially applied.\n *\n * @static\n * @memberOf _\n * @since 4.14.0\n * @category Lang\n * @param {Object} object The object to inspect.\n * @param {Object} source The object of property predicates to conform to.\n * @returns {boolean} Returns `true` if `object` conforms, else `false`.\n * @example\n *\n * var object = { 'a': 1, 'b': 2 };\n *\n * _.conformsTo(object, { 'b': function(n) { return n > 1; } });\n * // => true\n *\n * _.conformsTo(object, { 'b': function(n) { return n > 2; } });\n * // => false\n */\nfunction conformsTo(object, source) {\n  return source == null || baseConformsTo(object, source, keys(source));\n}\nexport default conformsTo;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}