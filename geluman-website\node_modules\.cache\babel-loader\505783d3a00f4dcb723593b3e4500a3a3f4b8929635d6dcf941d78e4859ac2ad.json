{"ast": null, "code": "import { useAttrs, useSlots, computed } from 'vue';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nfunction useInputTagDom({\n  props,\n  isFocused,\n  hovering,\n  disabled,\n  inputValue,\n  size,\n  validateState,\n  validateIcon,\n  needStatusIcon\n}) {\n  const attrs = useAttrs();\n  const slots = useSlots();\n  const ns = useNamespace(\"input-tag\");\n  const nsInput = useNamespace(\"input\");\n  const containerKls = computed(() => [ns.b(), ns.is(\"focused\", isFocused.value), ns.is(\"hovering\", hovering.value), ns.is(\"disabled\", disabled.value), ns.m(size.value), ns.e(\"wrapper\"), attrs.class]);\n  const containerStyle = computed(() => [attrs.style]);\n  const innerKls = computed(() => {\n    var _a, _b;\n    return [ns.e(\"inner\"), ns.is(\"draggable\", props.draggable), ns.is(\"left-space\", !((_a = props.modelValue) == null ? void 0 : _a.length) && !slots.prefix), ns.is(\"right-space\", !((_b = props.modelValue) == null ? void 0 : _b.length) && !showSuffix.value)];\n  });\n  const showClear = computed(() => {\n    var _a;\n    return props.clearable && !disabled.value && !props.readonly && (((_a = props.modelValue) == null ? void 0 : _a.length) || inputValue.value) && (isFocused.value || hovering.value);\n  });\n  const showSuffix = computed(() => {\n    return slots.suffix || showClear.value || validateState.value && validateIcon.value && needStatusIcon.value;\n  });\n  return {\n    ns,\n    nsInput,\n    containerKls,\n    containerStyle,\n    innerKls,\n    showClear,\n    showSuffix\n  };\n}\nexport { useInputTagDom };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}