{"ast": null, "code": "import { defineComponent, inject, computed, toRef, openBlock, createElementBlock, mergeProps, unref, createElementVNode, normalizeClass, normalizeStyle, createCommentVNode } from 'vue';\nimport { maskProps } from './mask.mjs';\nimport { tourKey } from './helper.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLockscreen } from '../../../hooks/use-lockscreen/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTourMask\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: maskProps,\n  setup(__props) {\n    const props = __props;\n    const {\n      ns\n    } = inject(tourKey);\n    const radius = computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.pos) == null ? void 0 : _a.radius) != null ? _b : 2;\n    });\n    const roundInfo = computed(() => {\n      const v = radius.value;\n      const baseInfo = `a${v},${v} 0 0 1`;\n      return {\n        topRight: `${baseInfo} ${v},${v}`,\n        bottomRight: `${baseInfo} ${-v},${v}`,\n        bottomLeft: `${baseInfo} ${-v},${-v}`,\n        topLeft: `${baseInfo} ${v},${-v}`\n      };\n    });\n    const path = computed(() => {\n      const width = window.innerWidth;\n      const height = window.innerHeight;\n      const info = roundInfo.value;\n      const _path = `M${width},0 L0,0 L0,${height} L${width},${height} L${width},0 Z`;\n      const _radius = radius.value;\n      return props.pos ? `${_path} M${props.pos.left + _radius},${props.pos.top} h${props.pos.width - _radius * 2} ${info.topRight} v${props.pos.height - _radius * 2} ${info.bottomRight} h${-props.pos.width + _radius * 2} ${info.bottomLeft} v${-props.pos.height + _radius * 2} ${info.topLeft} z` : _path;\n    });\n    const pathStyle = computed(() => {\n      return {\n        fill: props.fill,\n        pointerEvents: \"auto\",\n        cursor: \"auto\"\n      };\n    });\n    useLockscreen(toRef(props, \"visible\"), {\n      ns\n    });\n    return (_ctx, _cache) => {\n      return _ctx.visible ? (openBlock(), createElementBlock(\"div\", mergeProps({\n        key: 0,\n        class: unref(ns).e(\"mask\"),\n        style: {\n          position: \"fixed\",\n          left: 0,\n          right: 0,\n          top: 0,\n          bottom: 0,\n          zIndex: _ctx.zIndex,\n          pointerEvents: _ctx.pos && _ctx.targetAreaClickable ? \"none\" : \"auto\"\n        }\n      }, _ctx.$attrs), [(openBlock(), createElementBlock(\"svg\", {\n        style: {\n          width: \"100%\",\n          height: \"100%\"\n        }\n      }, [createElementVNode(\"path\", {\n        class: normalizeClass(unref(ns).e(\"hollow\")),\n        style: normalizeStyle(unref(pathStyle)),\n        d: unref(path)\n      }, null, 14, [\"d\"])]))], 16)) : createCommentVNode(\"v-if\", true);\n    };\n  }\n});\nvar ElTourMask = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"mask.vue\"]]);\nexport { ElTourMask as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}