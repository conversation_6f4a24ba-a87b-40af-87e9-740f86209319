{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { shallowRef, watchEffect, readonly, unref, ref, isVue3, version, watch, customRef, getCurrentScope, onScopeDispose, effectScope, provide, inject, isRef, computed, reactive, toRefs as toRefs$1, toRef, isVue2, set as set$1, getCurrentInstance, onBeforeMount, nextTick, onBeforeUnmount, onMounted, onUnmounted, isReactive } from 'vue-demi';\nvar __defProp$9 = Object.defineProperty;\nvar __defProps$6 = Object.defineProperties;\nvar __getOwnPropDescs$6 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$b = Object.getOwnPropertySymbols;\nvar __hasOwnProp$b = Object.prototype.hasOwnProperty;\nvar __propIsEnum$b = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$9 = (obj, key, value) => key in obj ? __defProp$9(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues$9 = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp$b.call(b, prop)) __defNormalProp$9(a, prop, b[prop]);\n  if (__getOwnPropSymbols$b) for (var prop of __getOwnPropSymbols$b(b)) {\n    if (__propIsEnum$b.call(b, prop)) __defNormalProp$9(a, prop, b[prop]);\n  }\n  return a;\n};\nvar __spreadProps$6 = (a, b) => __defProps$6(a, __getOwnPropDescs$6(b));\nfunction computedEager(fn, options) {\n  var _a;\n  const result = shallowRef();\n  watchEffect(() => {\n    result.value = fn();\n  }, __spreadProps$6(__spreadValues$9({}, options), {\n    flush: (_a = options == null ? void 0 : options.flush) != null ? _a : \"sync\"\n  }));\n  return readonly(result);\n}\nvar _a;\nconst isClient = typeof window !== \"undefined\";\nconst isDef = val => typeof val !== \"undefined\";\nconst assert = (condition, ...infos) => {\n  if (!condition) console.warn(...infos);\n};\nconst toString = Object.prototype.toString;\nconst isBoolean = val => typeof val === \"boolean\";\nconst isFunction = val => typeof val === \"function\";\nconst isNumber = val => typeof val === \"number\";\nconst isString = val => typeof val === \"string\";\nconst isObject = val => toString.call(val) === \"[object Object]\";\nconst isWindow = val => typeof window !== \"undefined\" && toString.call(val) === \"[object Window]\";\nconst now = () => Date.now();\nconst timestamp = () => +Date.now();\nconst clamp = (n, min, max) => Math.min(max, Math.max(min, n));\nconst noop = () => {};\nconst rand = (min, max) => {\n  min = Math.ceil(min);\n  max = Math.floor(max);\n  return Math.floor(Math.random() * (max - min + 1)) + min;\n};\nconst isIOS = isClient && ((_a = window == null ? void 0 : window.navigator) == null ? void 0 : _a.userAgent) && /iP(ad|hone|od)/.test(window.navigator.userAgent);\nconst hasOwn = (val, key) => Object.prototype.hasOwnProperty.call(val, key);\nfunction resolveUnref(r) {\n  return typeof r === \"function\" ? r() : unref(r);\n}\nfunction createFilterWrapper(filter, fn) {\n  function wrapper(...args) {\n    return new Promise((resolve, reject) => {\n      Promise.resolve(filter(() => fn.apply(this, args), {\n        fn,\n        thisArg: this,\n        args\n      })).then(resolve).catch(reject);\n    });\n  }\n  return wrapper;\n}\nconst bypassFilter = invoke => {\n  return invoke();\n};\nfunction debounceFilter(ms, options = {}) {\n  let timer;\n  let maxTimer;\n  let lastRejector = noop;\n  const _clearTimeout = timer2 => {\n    clearTimeout(timer2);\n    lastRejector();\n    lastRejector = noop;\n  };\n  const filter = invoke => {\n    const duration = resolveUnref(ms);\n    const maxDuration = resolveUnref(options.maxWait);\n    if (timer) _clearTimeout(timer);\n    if (duration <= 0 || maxDuration !== void 0 && maxDuration <= 0) {\n      if (maxTimer) {\n        _clearTimeout(maxTimer);\n        maxTimer = null;\n      }\n      return Promise.resolve(invoke());\n    }\n    return new Promise((resolve, reject) => {\n      lastRejector = options.rejectOnCancel ? reject : resolve;\n      if (maxDuration && !maxTimer) {\n        maxTimer = setTimeout(() => {\n          if (timer) _clearTimeout(timer);\n          maxTimer = null;\n          resolve(invoke());\n        }, maxDuration);\n      }\n      timer = setTimeout(() => {\n        if (maxTimer) _clearTimeout(maxTimer);\n        maxTimer = null;\n        resolve(invoke());\n      }, duration);\n    });\n  };\n  return filter;\n}\nfunction throttleFilter(ms, trailing = true, leading = true, rejectOnCancel = false) {\n  let lastExec = 0;\n  let timer;\n  let isLeading = true;\n  let lastRejector = noop;\n  let lastValue;\n  const clear = () => {\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n      lastRejector();\n      lastRejector = noop;\n    }\n  };\n  const filter = _invoke => {\n    const duration = resolveUnref(ms);\n    const elapsed = Date.now() - lastExec;\n    const invoke = () => {\n      return lastValue = _invoke();\n    };\n    clear();\n    if (duration <= 0) {\n      lastExec = Date.now();\n      return invoke();\n    }\n    if (elapsed > duration && (leading || !isLeading)) {\n      lastExec = Date.now();\n      invoke();\n    } else if (trailing) {\n      lastValue = new Promise((resolve, reject) => {\n        lastRejector = rejectOnCancel ? reject : resolve;\n        timer = setTimeout(() => {\n          lastExec = Date.now();\n          isLeading = true;\n          resolve(invoke());\n          clear();\n        }, Math.max(0, duration - elapsed));\n      });\n    }\n    if (!leading && !timer) timer = setTimeout(() => isLeading = true, duration);\n    isLeading = false;\n    return lastValue;\n  };\n  return filter;\n}\nfunction pausableFilter(extendFilter = bypassFilter) {\n  const isActive = ref(true);\n  function pause() {\n    isActive.value = false;\n  }\n  function resume() {\n    isActive.value = true;\n  }\n  const eventFilter = (...args) => {\n    if (isActive.value) extendFilter(...args);\n  };\n  return {\n    isActive: readonly(isActive),\n    pause,\n    resume,\n    eventFilter\n  };\n}\nfunction __onlyVue3(name = \"this function\") {\n  if (isVue3) return;\n  throw new Error(`[VueUse] ${name} is only works on Vue 3.`);\n}\nfunction __onlyVue27Plus(name = \"this function\") {\n  if (isVue3 || version.startsWith(\"2.7.\")) return;\n  throw new Error(`[VueUse] ${name} is only works on Vue 2.7 or above.`);\n}\nconst directiveHooks = {\n  mounted: isVue3 ? \"mounted\" : \"inserted\",\n  updated: isVue3 ? \"updated\" : \"componentUpdated\",\n  unmounted: isVue3 ? \"unmounted\" : \"unbind\"\n};\nfunction promiseTimeout(ms, throwOnTimeout = false, reason = \"Timeout\") {\n  return new Promise((resolve, reject) => {\n    if (throwOnTimeout) setTimeout(() => reject(reason), ms);else setTimeout(resolve, ms);\n  });\n}\nfunction identity(arg) {\n  return arg;\n}\nfunction createSingletonPromise(fn) {\n  let _promise;\n  function wrapper() {\n    if (!_promise) _promise = fn();\n    return _promise;\n  }\n  wrapper.reset = async () => {\n    const _prev = _promise;\n    _promise = void 0;\n    if (_prev) await _prev;\n  };\n  return wrapper;\n}\nfunction invoke(fn) {\n  return fn();\n}\nfunction containsProp(obj, ...props) {\n  return props.some(k => k in obj);\n}\nfunction increaseWithUnit(target, delta) {\n  var _a;\n  if (typeof target === \"number\") return target + delta;\n  const value = ((_a = target.match(/^-?[0-9]+\\.?[0-9]*/)) == null ? void 0 : _a[0]) || \"\";\n  const unit = target.slice(value.length);\n  const result = parseFloat(value) + delta;\n  if (Number.isNaN(result)) return target;\n  return result + unit;\n}\nfunction objectPick(obj, keys, omitUndefined = false) {\n  return keys.reduce((n, k) => {\n    if (k in obj) {\n      if (!omitUndefined || obj[k] !== void 0) n[k] = obj[k];\n    }\n    return n;\n  }, {});\n}\nfunction computedWithControl(source, fn) {\n  let v = void 0;\n  let track;\n  let trigger;\n  const dirty = ref(true);\n  const update = () => {\n    dirty.value = true;\n    trigger();\n  };\n  watch(source, update, {\n    flush: \"sync\"\n  });\n  const get = isFunction(fn) ? fn : fn.get;\n  const set = isFunction(fn) ? void 0 : fn.set;\n  const result = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        if (dirty.value) {\n          v = get();\n          dirty.value = false;\n        }\n        track();\n        return v;\n      },\n      set(v2) {\n        set == null ? void 0 : set(v2);\n      }\n    };\n  });\n  if (Object.isExtensible(result)) result.trigger = update;\n  return result;\n}\nfunction tryOnScopeDispose(fn) {\n  if (getCurrentScope()) {\n    onScopeDispose(fn);\n    return true;\n  }\n  return false;\n}\nfunction createEventHook() {\n  const fns = [];\n  const off = fn => {\n    const index = fns.indexOf(fn);\n    if (index !== -1) fns.splice(index, 1);\n  };\n  const on = fn => {\n    fns.push(fn);\n    const offFn = () => off(fn);\n    tryOnScopeDispose(offFn);\n    return {\n      off: offFn\n    };\n  };\n  const trigger = param => {\n    fns.forEach(fn => fn(param));\n  };\n  return {\n    on,\n    off,\n    trigger\n  };\n}\nfunction createGlobalState(stateFactory) {\n  let initialized = false;\n  let state;\n  const scope = effectScope(true);\n  return () => {\n    if (!initialized) {\n      state = scope.run(stateFactory);\n      initialized = true;\n    }\n    return state;\n  };\n}\nfunction createInjectionState(composable) {\n  const key = Symbol(\"InjectionState\");\n  const useProvidingState = (...args) => {\n    const state = composable(...args);\n    provide(key, state);\n    return state;\n  };\n  const useInjectedState = () => inject(key);\n  return [useProvidingState, useInjectedState];\n}\nfunction createSharedComposable(composable) {\n  let subscribers = 0;\n  let state;\n  let scope;\n  const dispose = () => {\n    subscribers -= 1;\n    if (scope && subscribers <= 0) {\n      scope.stop();\n      state = void 0;\n      scope = void 0;\n    }\n  };\n  return (...args) => {\n    subscribers += 1;\n    if (!state) {\n      scope = effectScope(true);\n      state = scope.run(() => composable(...args));\n    }\n    tryOnScopeDispose(dispose);\n    return state;\n  };\n}\nfunction extendRef(ref, extend, {\n  enumerable = false,\n  unwrap = true\n} = {}) {\n  __onlyVue27Plus();\n  for (const [key, value] of Object.entries(extend)) {\n    if (key === \"value\") continue;\n    if (isRef(value) && unwrap) {\n      Object.defineProperty(ref, key, {\n        get() {\n          return value.value;\n        },\n        set(v) {\n          value.value = v;\n        },\n        enumerable\n      });\n    } else {\n      Object.defineProperty(ref, key, {\n        value,\n        enumerable\n      });\n    }\n  }\n  return ref;\n}\nfunction get(obj, key) {\n  if (key == null) return unref(obj);\n  return unref(obj)[key];\n}\nfunction isDefined(v) {\n  return unref(v) != null;\n}\nvar __defProp$8 = Object.defineProperty;\nvar __getOwnPropSymbols$a = Object.getOwnPropertySymbols;\nvar __hasOwnProp$a = Object.prototype.hasOwnProperty;\nvar __propIsEnum$a = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$8 = (obj, key, value) => key in obj ? __defProp$8(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues$8 = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp$a.call(b, prop)) __defNormalProp$8(a, prop, b[prop]);\n  if (__getOwnPropSymbols$a) for (var prop of __getOwnPropSymbols$a(b)) {\n    if (__propIsEnum$a.call(b, prop)) __defNormalProp$8(a, prop, b[prop]);\n  }\n  return a;\n};\nfunction makeDestructurable(obj, arr) {\n  if (typeof Symbol !== \"undefined\") {\n    const clone = __spreadValues$8({}, obj);\n    Object.defineProperty(clone, Symbol.iterator, {\n      enumerable: false,\n      value() {\n        let index = 0;\n        return {\n          next: () => ({\n            value: arr[index++],\n            done: index > arr.length\n          })\n        };\n      }\n    });\n    return clone;\n  } else {\n    return Object.assign([...arr], obj);\n  }\n}\nfunction reactify(fn, options) {\n  const unrefFn = (options == null ? void 0 : options.computedGetter) === false ? unref : resolveUnref;\n  return function (...args) {\n    return computed(() => fn.apply(this, args.map(i => unrefFn(i))));\n  };\n}\nfunction reactifyObject(obj, optionsOrKeys = {}) {\n  let keys = [];\n  let options;\n  if (Array.isArray(optionsOrKeys)) {\n    keys = optionsOrKeys;\n  } else {\n    options = optionsOrKeys;\n    const {\n      includeOwnProperties = true\n    } = optionsOrKeys;\n    keys.push(...Object.keys(obj));\n    if (includeOwnProperties) keys.push(...Object.getOwnPropertyNames(obj));\n  }\n  return Object.fromEntries(keys.map(key => {\n    const value = obj[key];\n    return [key, typeof value === \"function\" ? reactify(value.bind(obj), options) : value];\n  }));\n}\nfunction toReactive(objectRef) {\n  if (!isRef(objectRef)) return reactive(objectRef);\n  const proxy = new Proxy({}, {\n    get(_, p, receiver) {\n      return unref(Reflect.get(objectRef.value, p, receiver));\n    },\n    set(_, p, value) {\n      if (isRef(objectRef.value[p]) && !isRef(value)) objectRef.value[p].value = value;else objectRef.value[p] = value;\n      return true;\n    },\n    deleteProperty(_, p) {\n      return Reflect.deleteProperty(objectRef.value, p);\n    },\n    has(_, p) {\n      return Reflect.has(objectRef.value, p);\n    },\n    ownKeys() {\n      return Object.keys(objectRef.value);\n    },\n    getOwnPropertyDescriptor() {\n      return {\n        enumerable: true,\n        configurable: true\n      };\n    }\n  });\n  return reactive(proxy);\n}\nfunction reactiveComputed(fn) {\n  return toReactive(computed(fn));\n}\nfunction reactiveOmit(obj, ...keys) {\n  const flatKeys = keys.flat();\n  return reactiveComputed(() => Object.fromEntries(Object.entries(toRefs$1(obj)).filter(e => !flatKeys.includes(e[0]))));\n}\nfunction reactivePick(obj, ...keys) {\n  const flatKeys = keys.flat();\n  return reactive(Object.fromEntries(flatKeys.map(k => [k, toRef(obj, k)])));\n}\nfunction refAutoReset(defaultValue, afterMs = 1e4) {\n  return customRef((track, trigger) => {\n    let value = defaultValue;\n    let timer;\n    const resetAfter = () => setTimeout(() => {\n      value = defaultValue;\n      trigger();\n    }, resolveUnref(afterMs));\n    tryOnScopeDispose(() => {\n      clearTimeout(timer);\n    });\n    return {\n      get() {\n        track();\n        return value;\n      },\n      set(newValue) {\n        value = newValue;\n        trigger();\n        clearTimeout(timer);\n        timer = resetAfter();\n      }\n    };\n  });\n}\nfunction useDebounceFn(fn, ms = 200, options = {}) {\n  return createFilterWrapper(debounceFilter(ms, options), fn);\n}\nfunction refDebounced(value, ms = 200, options = {}) {\n  const debounced = ref(value.value);\n  const updater = useDebounceFn(() => {\n    debounced.value = value.value;\n  }, ms, options);\n  watch(value, () => updater());\n  return debounced;\n}\nfunction refDefault(source, defaultValue) {\n  return computed({\n    get() {\n      var _a;\n      return (_a = source.value) != null ? _a : defaultValue;\n    },\n    set(value) {\n      source.value = value;\n    }\n  });\n}\nfunction useThrottleFn(fn, ms = 200, trailing = false, leading = true, rejectOnCancel = false) {\n  return createFilterWrapper(throttleFilter(ms, trailing, leading, rejectOnCancel), fn);\n}\nfunction refThrottled(value, delay = 200, trailing = true, leading = true) {\n  if (delay <= 0) return value;\n  const throttled = ref(value.value);\n  const updater = useThrottleFn(() => {\n    throttled.value = value.value;\n  }, delay, trailing, leading);\n  watch(value, () => updater());\n  return throttled;\n}\nfunction refWithControl(initial, options = {}) {\n  let source = initial;\n  let track;\n  let trigger;\n  const ref = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        return get();\n      },\n      set(v) {\n        set(v);\n      }\n    };\n  });\n  function get(tracking = true) {\n    if (tracking) track();\n    return source;\n  }\n  function set(value, triggering = true) {\n    var _a, _b;\n    if (value === source) return;\n    const old = source;\n    if (((_a = options.onBeforeChange) == null ? void 0 : _a.call(options, value, old)) === false) return;\n    source = value;\n    (_b = options.onChanged) == null ? void 0 : _b.call(options, value, old);\n    if (triggering) trigger();\n  }\n  const untrackedGet = () => get(false);\n  const silentSet = v => set(v, false);\n  const peek = () => get(false);\n  const lay = v => set(v, false);\n  return extendRef(ref, {\n    get,\n    set,\n    untrackedGet,\n    silentSet,\n    peek,\n    lay\n  }, {\n    enumerable: true\n  });\n}\nconst controlledRef = refWithControl;\nfunction resolveRef(r) {\n  return typeof r === \"function\" ? computed(r) : ref(r);\n}\nfunction set(...args) {\n  if (args.length === 2) {\n    const [ref, value] = args;\n    ref.value = value;\n  }\n  if (args.length === 3) {\n    if (isVue2) {\n      set$1(...args);\n    } else {\n      const [target, key, value] = args;\n      target[key] = value;\n    }\n  }\n}\nfunction syncRef(left, right, options = {}) {\n  var _a, _b;\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true,\n    direction = \"both\",\n    transform = {}\n  } = options;\n  let watchLeft;\n  let watchRight;\n  const transformLTR = (_a = transform.ltr) != null ? _a : v => v;\n  const transformRTL = (_b = transform.rtl) != null ? _b : v => v;\n  if (direction === \"both\" || direction === \"ltr\") {\n    watchLeft = watch(left, newValue => right.value = transformLTR(newValue), {\n      flush,\n      deep,\n      immediate\n    });\n  }\n  if (direction === \"both\" || direction === \"rtl\") {\n    watchRight = watch(right, newValue => left.value = transformRTL(newValue), {\n      flush,\n      deep,\n      immediate\n    });\n  }\n  return () => {\n    watchLeft == null ? void 0 : watchLeft();\n    watchRight == null ? void 0 : watchRight();\n  };\n}\nfunction syncRefs(source, targets, options = {}) {\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true\n  } = options;\n  if (!Array.isArray(targets)) targets = [targets];\n  return watch(source, newValue => targets.forEach(target => target.value = newValue), {\n    flush,\n    deep,\n    immediate\n  });\n}\nvar __defProp$7 = Object.defineProperty;\nvar __defProps$5 = Object.defineProperties;\nvar __getOwnPropDescs$5 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$9 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$9 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$9 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$7 = (obj, key, value) => key in obj ? __defProp$7(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues$7 = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp$9.call(b, prop)) __defNormalProp$7(a, prop, b[prop]);\n  if (__getOwnPropSymbols$9) for (var prop of __getOwnPropSymbols$9(b)) {\n    if (__propIsEnum$9.call(b, prop)) __defNormalProp$7(a, prop, b[prop]);\n  }\n  return a;\n};\nvar __spreadProps$5 = (a, b) => __defProps$5(a, __getOwnPropDescs$5(b));\nfunction toRefs(objectRef) {\n  if (!isRef(objectRef)) return toRefs$1(objectRef);\n  const result = Array.isArray(objectRef.value) ? new Array(objectRef.value.length) : {};\n  for (const key in objectRef.value) {\n    result[key] = customRef(() => ({\n      get() {\n        return objectRef.value[key];\n      },\n      set(v) {\n        if (Array.isArray(objectRef.value)) {\n          const copy = [...objectRef.value];\n          copy[key] = v;\n          objectRef.value = copy;\n        } else {\n          const newObject = __spreadProps$5(__spreadValues$7({}, objectRef.value), {\n            [key]: v\n          });\n          Object.setPrototypeOf(newObject, objectRef.value);\n          objectRef.value = newObject;\n        }\n      }\n    }));\n  }\n  return result;\n}\nfunction tryOnBeforeMount(fn, sync = true) {\n  if (getCurrentInstance()) onBeforeMount(fn);else if (sync) fn();else nextTick(fn);\n}\nfunction tryOnBeforeUnmount(fn) {\n  if (getCurrentInstance()) onBeforeUnmount(fn);\n}\nfunction tryOnMounted(fn, sync = true) {\n  if (getCurrentInstance()) onMounted(fn);else if (sync) fn();else nextTick(fn);\n}\nfunction tryOnUnmounted(fn) {\n  if (getCurrentInstance()) onUnmounted(fn);\n}\nfunction createUntil(r, isNot = false) {\n  function toMatch(condition, {\n    flush = \"sync\",\n    deep = false,\n    timeout,\n    throwOnTimeout\n  } = {}) {\n    let stop = null;\n    const watcher = new Promise(resolve => {\n      stop = watch(r, v => {\n        if (condition(v) !== isNot) {\n          stop == null ? void 0 : stop();\n          resolve(v);\n        }\n      }, {\n        flush,\n        deep,\n        immediate: true\n      });\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(promiseTimeout(timeout, throwOnTimeout).then(() => resolveUnref(r)).finally(() => stop == null ? void 0 : stop()));\n    }\n    return Promise.race(promises);\n  }\n  function toBe(value, options) {\n    if (!isRef(value)) return toMatch(v => v === value, options);\n    const {\n      flush = \"sync\",\n      deep = false,\n      timeout,\n      throwOnTimeout\n    } = options != null ? options : {};\n    let stop = null;\n    const watcher = new Promise(resolve => {\n      stop = watch([r, value], ([v1, v2]) => {\n        if (isNot !== (v1 === v2)) {\n          stop == null ? void 0 : stop();\n          resolve(v1);\n        }\n      }, {\n        flush,\n        deep,\n        immediate: true\n      });\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(promiseTimeout(timeout, throwOnTimeout).then(() => resolveUnref(r)).finally(() => {\n        stop == null ? void 0 : stop();\n        return resolveUnref(r);\n      }));\n    }\n    return Promise.race(promises);\n  }\n  function toBeTruthy(options) {\n    return toMatch(v => Boolean(v), options);\n  }\n  function toBeNull(options) {\n    return toBe(null, options);\n  }\n  function toBeUndefined(options) {\n    return toBe(void 0, options);\n  }\n  function toBeNaN(options) {\n    return toMatch(Number.isNaN, options);\n  }\n  function toContains(value, options) {\n    return toMatch(v => {\n      const array = Array.from(v);\n      return array.includes(value) || array.includes(resolveUnref(value));\n    }, options);\n  }\n  function changed(options) {\n    return changedTimes(1, options);\n  }\n  function changedTimes(n = 1, options) {\n    let count = -1;\n    return toMatch(() => {\n      count += 1;\n      return count >= n;\n    }, options);\n  }\n  if (Array.isArray(resolveUnref(r))) {\n    const instance = {\n      toMatch,\n      toContains,\n      changed,\n      changedTimes,\n      get not() {\n        return createUntil(r, !isNot);\n      }\n    };\n    return instance;\n  } else {\n    const instance = {\n      toMatch,\n      toBe,\n      toBeTruthy,\n      toBeNull,\n      toBeNaN,\n      toBeUndefined,\n      changed,\n      changedTimes,\n      get not() {\n        return createUntil(r, !isNot);\n      }\n    };\n    return instance;\n  }\n}\nfunction until(r) {\n  return createUntil(r);\n}\nfunction useArrayEvery(list, fn) {\n  return computed(() => resolveUnref(list).every((element, index, array) => fn(resolveUnref(element), index, array)));\n}\nfunction useArrayFilter(list, fn) {\n  return computed(() => resolveUnref(list).map(i => resolveUnref(i)).filter(fn));\n}\nfunction useArrayFind(list, fn) {\n  return computed(() => resolveUnref(resolveUnref(list).find((element, index, array) => fn(resolveUnref(element), index, array))));\n}\nfunction useArrayFindIndex(list, fn) {\n  return computed(() => resolveUnref(list).findIndex((element, index, array) => fn(resolveUnref(element), index, array)));\n}\nfunction findLast(arr, cb) {\n  let index = arr.length;\n  while (index-- > 0) {\n    if (cb(arr[index], index, arr)) return arr[index];\n  }\n  return void 0;\n}\nfunction useArrayFindLast(list, fn) {\n  return computed(() => resolveUnref(!Array.prototype.findLast ? findLast(resolveUnref(list), (element, index, array) => fn(resolveUnref(element), index, array)) : resolveUnref(list).findLast((element, index, array) => fn(resolveUnref(element), index, array))));\n}\nfunction useArrayJoin(list, separator) {\n  return computed(() => resolveUnref(list).map(i => resolveUnref(i)).join(resolveUnref(separator)));\n}\nfunction useArrayMap(list, fn) {\n  return computed(() => resolveUnref(list).map(i => resolveUnref(i)).map(fn));\n}\nfunction useArrayReduce(list, reducer, ...args) {\n  const reduceCallback = (sum, value, index) => reducer(resolveUnref(sum), resolveUnref(value), index);\n  return computed(() => {\n    const resolved = resolveUnref(list);\n    return args.length ? resolved.reduce(reduceCallback, resolveUnref(args[0])) : resolved.reduce(reduceCallback);\n  });\n}\nfunction useArraySome(list, fn) {\n  return computed(() => resolveUnref(list).some((element, index, array) => fn(resolveUnref(element), index, array)));\n}\nfunction useArrayUnique(list) {\n  return computed(() => [...new Set(resolveUnref(list).map(element => resolveUnref(element)))]);\n}\nfunction useCounter(initialValue = 0, options = {}) {\n  const count = ref(initialValue);\n  const {\n    max = Infinity,\n    min = -Infinity\n  } = options;\n  const inc = (delta = 1) => count.value = Math.min(max, count.value + delta);\n  const dec = (delta = 1) => count.value = Math.max(min, count.value - delta);\n  const get = () => count.value;\n  const set = val => count.value = Math.max(min, Math.min(max, val));\n  const reset = (val = initialValue) => {\n    initialValue = val;\n    return set(val);\n  };\n  return {\n    count,\n    inc,\n    dec,\n    get,\n    set,\n    reset\n  };\n}\nconst REGEX_PARSE = /^(\\d{4})[-/]?(\\d{1,2})?[-/]?(\\d{0,2})[Tt\\s]*(\\d{1,2})?:?(\\d{1,2})?:?(\\d{1,2})?[.:]?(\\d+)?$/;\nconst REGEX_FORMAT = /\\[([^\\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a{1,2}|A{1,2}|m{1,2}|s{1,2}|Z{1,2}|SSS/g;\nconst defaultMeridiem = (hours, minutes, isLowercase, hasPeriod) => {\n  let m = hours < 12 ? \"AM\" : \"PM\";\n  if (hasPeriod) m = m.split(\"\").reduce((acc, curr) => acc += `${curr}.`, \"\");\n  return isLowercase ? m.toLowerCase() : m;\n};\nconst formatDate = (date, formatStr, options = {}) => {\n  var _a;\n  const years = date.getFullYear();\n  const month = date.getMonth();\n  const days = date.getDate();\n  const hours = date.getHours();\n  const minutes = date.getMinutes();\n  const seconds = date.getSeconds();\n  const milliseconds = date.getMilliseconds();\n  const day = date.getDay();\n  const meridiem = (_a = options.customMeridiem) != null ? _a : defaultMeridiem;\n  const matches = {\n    YY: () => String(years).slice(-2),\n    YYYY: () => years,\n    M: () => month + 1,\n    MM: () => `${month + 1}`.padStart(2, \"0\"),\n    MMM: () => date.toLocaleDateString(options.locales, {\n      month: \"short\"\n    }),\n    MMMM: () => date.toLocaleDateString(options.locales, {\n      month: \"long\"\n    }),\n    D: () => String(days),\n    DD: () => `${days}`.padStart(2, \"0\"),\n    H: () => String(hours),\n    HH: () => `${hours}`.padStart(2, \"0\"),\n    h: () => `${hours % 12 || 12}`.padStart(1, \"0\"),\n    hh: () => `${hours % 12 || 12}`.padStart(2, \"0\"),\n    m: () => String(minutes),\n    mm: () => `${minutes}`.padStart(2, \"0\"),\n    s: () => String(seconds),\n    ss: () => `${seconds}`.padStart(2, \"0\"),\n    SSS: () => `${milliseconds}`.padStart(3, \"0\"),\n    d: () => day,\n    dd: () => date.toLocaleDateString(options.locales, {\n      weekday: \"narrow\"\n    }),\n    ddd: () => date.toLocaleDateString(options.locales, {\n      weekday: \"short\"\n    }),\n    dddd: () => date.toLocaleDateString(options.locales, {\n      weekday: \"long\"\n    }),\n    A: () => meridiem(hours, minutes),\n    AA: () => meridiem(hours, minutes, false, true),\n    a: () => meridiem(hours, minutes, true),\n    aa: () => meridiem(hours, minutes, true, true)\n  };\n  return formatStr.replace(REGEX_FORMAT, (match, $1) => $1 || matches[match]());\n};\nconst normalizeDate = date => {\n  if (date === null) return new Date(NaN);\n  if (date === void 0) return new Date();\n  if (date instanceof Date) return new Date(date);\n  if (typeof date === \"string\" && !/Z$/i.test(date)) {\n    const d = date.match(REGEX_PARSE);\n    if (d) {\n      const m = d[2] - 1 || 0;\n      const ms = (d[7] || \"0\").substring(0, 3);\n      return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);\n    }\n  }\n  return new Date(date);\n};\nfunction useDateFormat(date, formatStr = \"HH:mm:ss\", options = {}) {\n  return computed(() => formatDate(normalizeDate(resolveUnref(date)), resolveUnref(formatStr), options));\n}\nfunction useIntervalFn(cb, interval = 1e3, options = {}) {\n  const {\n    immediate = true,\n    immediateCallback = false\n  } = options;\n  let timer = null;\n  const isActive = ref(false);\n  function clean() {\n    if (timer) {\n      clearInterval(timer);\n      timer = null;\n    }\n  }\n  function pause() {\n    isActive.value = false;\n    clean();\n  }\n  function resume() {\n    const intervalValue = resolveUnref(interval);\n    if (intervalValue <= 0) return;\n    isActive.value = true;\n    if (immediateCallback) cb();\n    clean();\n    timer = setInterval(cb, intervalValue);\n  }\n  if (immediate && isClient) resume();\n  if (isRef(interval) || isFunction(interval)) {\n    const stopWatch = watch(interval, () => {\n      if (isActive.value && isClient) resume();\n    });\n    tryOnScopeDispose(stopWatch);\n  }\n  tryOnScopeDispose(pause);\n  return {\n    isActive,\n    pause,\n    resume\n  };\n}\nvar __defProp$6 = Object.defineProperty;\nvar __getOwnPropSymbols$8 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$8 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$8 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$6 = (obj, key, value) => key in obj ? __defProp$6(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues$6 = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp$8.call(b, prop)) __defNormalProp$6(a, prop, b[prop]);\n  if (__getOwnPropSymbols$8) for (var prop of __getOwnPropSymbols$8(b)) {\n    if (__propIsEnum$8.call(b, prop)) __defNormalProp$6(a, prop, b[prop]);\n  }\n  return a;\n};\nfunction useInterval(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    immediate = true,\n    callback\n  } = options;\n  const counter = ref(0);\n  const update = () => counter.value += 1;\n  const reset = () => {\n    counter.value = 0;\n  };\n  const controls = useIntervalFn(callback ? () => {\n    update();\n    callback(counter.value);\n  } : update, interval, {\n    immediate\n  });\n  if (exposeControls) {\n    return __spreadValues$6({\n      counter,\n      reset\n    }, controls);\n  } else {\n    return counter;\n  }\n}\nfunction useLastChanged(source, options = {}) {\n  var _a;\n  const ms = ref((_a = options.initialValue) != null ? _a : null);\n  watch(source, () => ms.value = timestamp(), options);\n  return ms;\n}\nfunction useTimeoutFn(cb, interval, options = {}) {\n  const {\n    immediate = true\n  } = options;\n  const isPending = ref(false);\n  let timer = null;\n  function clear() {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  }\n  function stop() {\n    isPending.value = false;\n    clear();\n  }\n  function start(...args) {\n    clear();\n    isPending.value = true;\n    timer = setTimeout(() => {\n      isPending.value = false;\n      timer = null;\n      cb(...args);\n    }, resolveUnref(interval));\n  }\n  if (immediate) {\n    isPending.value = true;\n    if (isClient) start();\n  }\n  tryOnScopeDispose(stop);\n  return {\n    isPending: readonly(isPending),\n    start,\n    stop\n  };\n}\nvar __defProp$5 = Object.defineProperty;\nvar __getOwnPropSymbols$7 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$7 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$7 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$5 = (obj, key, value) => key in obj ? __defProp$5(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues$5 = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp$7.call(b, prop)) __defNormalProp$5(a, prop, b[prop]);\n  if (__getOwnPropSymbols$7) for (var prop of __getOwnPropSymbols$7(b)) {\n    if (__propIsEnum$7.call(b, prop)) __defNormalProp$5(a, prop, b[prop]);\n  }\n  return a;\n};\nfunction useTimeout(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    callback\n  } = options;\n  const controls = useTimeoutFn(callback != null ? callback : noop, interval, options);\n  const ready = computed(() => !controls.isPending.value);\n  if (exposeControls) {\n    return __spreadValues$5({\n      ready\n    }, controls);\n  } else {\n    return ready;\n  }\n}\nfunction useToNumber(value, options = {}) {\n  const {\n    method = \"parseFloat\",\n    radix,\n    nanToZero\n  } = options;\n  return computed(() => {\n    let resolved = resolveUnref(value);\n    if (typeof resolved === \"string\") resolved = Number[method](resolved, radix);\n    if (nanToZero && isNaN(resolved)) resolved = 0;\n    return resolved;\n  });\n}\nfunction useToString(value) {\n  return computed(() => `${resolveUnref(value)}`);\n}\nfunction useToggle(initialValue = false, options = {}) {\n  const {\n    truthyValue = true,\n    falsyValue = false\n  } = options;\n  const valueIsRef = isRef(initialValue);\n  const _value = ref(initialValue);\n  function toggle(value) {\n    if (arguments.length) {\n      _value.value = value;\n      return _value.value;\n    } else {\n      const truthy = resolveUnref(truthyValue);\n      _value.value = _value.value === truthy ? resolveUnref(falsyValue) : truthy;\n      return _value.value;\n    }\n  }\n  if (valueIsRef) return toggle;else return [_value, toggle];\n}\nfunction watchArray(source, cb, options) {\n  let oldList = (options == null ? void 0 : options.immediate) ? [] : [...(source instanceof Function ? source() : Array.isArray(source) ? source : unref(source))];\n  return watch(source, (newList, _, onCleanup) => {\n    const oldListRemains = new Array(oldList.length);\n    const added = [];\n    for (const obj of newList) {\n      let found = false;\n      for (let i = 0; i < oldList.length; i++) {\n        if (!oldListRemains[i] && obj === oldList[i]) {\n          oldListRemains[i] = true;\n          found = true;\n          break;\n        }\n      }\n      if (!found) added.push(obj);\n    }\n    const removed = oldList.filter((_2, i) => !oldListRemains[i]);\n    cb(newList, oldList, added, removed, onCleanup);\n    oldList = [...newList];\n  }, options);\n}\nvar __getOwnPropSymbols$6 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$6 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$6 = Object.prototype.propertyIsEnumerable;\nvar __objRest$5 = (source, exclude) => {\n  var target = {};\n  for (var prop in source) if (__hasOwnProp$6.call(source, prop) && exclude.indexOf(prop) < 0) target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$6) for (var prop of __getOwnPropSymbols$6(source)) {\n    if (exclude.indexOf(prop) < 0 && __propIsEnum$6.call(source, prop)) target[prop] = source[prop];\n  }\n  return target;\n};\nfunction watchWithFilter(source, cb, options = {}) {\n  const _a = options,\n    {\n      eventFilter = bypassFilter\n    } = _a,\n    watchOptions = __objRest$5(_a, [\"eventFilter\"]);\n  return watch(source, createFilterWrapper(eventFilter, cb), watchOptions);\n}\nvar __getOwnPropSymbols$5 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$5 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$5 = Object.prototype.propertyIsEnumerable;\nvar __objRest$4 = (source, exclude) => {\n  var target = {};\n  for (var prop in source) if (__hasOwnProp$5.call(source, prop) && exclude.indexOf(prop) < 0) target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$5) for (var prop of __getOwnPropSymbols$5(source)) {\n    if (exclude.indexOf(prop) < 0 && __propIsEnum$5.call(source, prop)) target[prop] = source[prop];\n  }\n  return target;\n};\nfunction watchAtMost(source, cb, options) {\n  const _a = options,\n    {\n      count\n    } = _a,\n    watchOptions = __objRest$4(_a, [\"count\"]);\n  const current = ref(0);\n  const stop = watchWithFilter(source, (...args) => {\n    current.value += 1;\n    if (current.value >= resolveUnref(count)) nextTick(() => stop());\n    cb(...args);\n  }, watchOptions);\n  return {\n    count: current,\n    stop\n  };\n}\nvar __defProp$4 = Object.defineProperty;\nvar __defProps$4 = Object.defineProperties;\nvar __getOwnPropDescs$4 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$4 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$4 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$4 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$4 = (obj, key, value) => key in obj ? __defProp$4(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues$4 = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp$4.call(b, prop)) __defNormalProp$4(a, prop, b[prop]);\n  if (__getOwnPropSymbols$4) for (var prop of __getOwnPropSymbols$4(b)) {\n    if (__propIsEnum$4.call(b, prop)) __defNormalProp$4(a, prop, b[prop]);\n  }\n  return a;\n};\nvar __spreadProps$4 = (a, b) => __defProps$4(a, __getOwnPropDescs$4(b));\nvar __objRest$3 = (source, exclude) => {\n  var target = {};\n  for (var prop in source) if (__hasOwnProp$4.call(source, prop) && exclude.indexOf(prop) < 0) target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$4) for (var prop of __getOwnPropSymbols$4(source)) {\n    if (exclude.indexOf(prop) < 0 && __propIsEnum$4.call(source, prop)) target[prop] = source[prop];\n  }\n  return target;\n};\nfunction watchDebounced(source, cb, options = {}) {\n  const _a = options,\n    {\n      debounce = 0,\n      maxWait = void 0\n    } = _a,\n    watchOptions = __objRest$3(_a, [\"debounce\", \"maxWait\"]);\n  return watchWithFilter(source, cb, __spreadProps$4(__spreadValues$4({}, watchOptions), {\n    eventFilter: debounceFilter(debounce, {\n      maxWait\n    })\n  }));\n}\nvar __defProp$3 = Object.defineProperty;\nvar __defProps$3 = Object.defineProperties;\nvar __getOwnPropDescs$3 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$3 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$3 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$3 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$3 = (obj, key, value) => key in obj ? __defProp$3(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues$3 = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp$3.call(b, prop)) __defNormalProp$3(a, prop, b[prop]);\n  if (__getOwnPropSymbols$3) for (var prop of __getOwnPropSymbols$3(b)) {\n    if (__propIsEnum$3.call(b, prop)) __defNormalProp$3(a, prop, b[prop]);\n  }\n  return a;\n};\nvar __spreadProps$3 = (a, b) => __defProps$3(a, __getOwnPropDescs$3(b));\nvar __objRest$2 = (source, exclude) => {\n  var target = {};\n  for (var prop in source) if (__hasOwnProp$3.call(source, prop) && exclude.indexOf(prop) < 0) target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$3) for (var prop of __getOwnPropSymbols$3(source)) {\n    if (exclude.indexOf(prop) < 0 && __propIsEnum$3.call(source, prop)) target[prop] = source[prop];\n  }\n  return target;\n};\nfunction watchIgnorable(source, cb, options = {}) {\n  const _a = options,\n    {\n      eventFilter = bypassFilter\n    } = _a,\n    watchOptions = __objRest$2(_a, [\"eventFilter\"]);\n  const filteredCb = createFilterWrapper(eventFilter, cb);\n  let ignoreUpdates;\n  let ignorePrevAsyncUpdates;\n  let stop;\n  if (watchOptions.flush === \"sync\") {\n    const ignore = ref(false);\n    ignorePrevAsyncUpdates = () => {};\n    ignoreUpdates = updater => {\n      ignore.value = true;\n      updater();\n      ignore.value = false;\n    };\n    stop = watch(source, (...args) => {\n      if (!ignore.value) filteredCb(...args);\n    }, watchOptions);\n  } else {\n    const disposables = [];\n    const ignoreCounter = ref(0);\n    const syncCounter = ref(0);\n    ignorePrevAsyncUpdates = () => {\n      ignoreCounter.value = syncCounter.value;\n    };\n    disposables.push(watch(source, () => {\n      syncCounter.value++;\n    }, __spreadProps$3(__spreadValues$3({}, watchOptions), {\n      flush: \"sync\"\n    })));\n    ignoreUpdates = updater => {\n      const syncCounterPrev = syncCounter.value;\n      updater();\n      ignoreCounter.value += syncCounter.value - syncCounterPrev;\n    };\n    disposables.push(watch(source, (...args) => {\n      const ignore = ignoreCounter.value > 0 && ignoreCounter.value === syncCounter.value;\n      ignoreCounter.value = 0;\n      syncCounter.value = 0;\n      if (ignore) return;\n      filteredCb(...args);\n    }, watchOptions));\n    stop = () => {\n      disposables.forEach(fn => fn());\n    };\n  }\n  return {\n    stop,\n    ignoreUpdates,\n    ignorePrevAsyncUpdates\n  };\n}\nfunction watchOnce(source, cb, options) {\n  const stop = watch(source, (...args) => {\n    nextTick(() => stop());\n    return cb(...args);\n  }, options);\n}\nvar __defProp$2 = Object.defineProperty;\nvar __defProps$2 = Object.defineProperties;\nvar __getOwnPropDescs$2 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$2 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$2 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$2 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$2 = (obj, key, value) => key in obj ? __defProp$2(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues$2 = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp$2.call(b, prop)) __defNormalProp$2(a, prop, b[prop]);\n  if (__getOwnPropSymbols$2) for (var prop of __getOwnPropSymbols$2(b)) {\n    if (__propIsEnum$2.call(b, prop)) __defNormalProp$2(a, prop, b[prop]);\n  }\n  return a;\n};\nvar __spreadProps$2 = (a, b) => __defProps$2(a, __getOwnPropDescs$2(b));\nvar __objRest$1 = (source, exclude) => {\n  var target = {};\n  for (var prop in source) if (__hasOwnProp$2.call(source, prop) && exclude.indexOf(prop) < 0) target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$2) for (var prop of __getOwnPropSymbols$2(source)) {\n    if (exclude.indexOf(prop) < 0 && __propIsEnum$2.call(source, prop)) target[prop] = source[prop];\n  }\n  return target;\n};\nfunction watchPausable(source, cb, options = {}) {\n  const _a = options,\n    {\n      eventFilter: filter\n    } = _a,\n    watchOptions = __objRest$1(_a, [\"eventFilter\"]);\n  const {\n    eventFilter,\n    pause,\n    resume,\n    isActive\n  } = pausableFilter(filter);\n  const stop = watchWithFilter(source, cb, __spreadProps$2(__spreadValues$2({}, watchOptions), {\n    eventFilter\n  }));\n  return {\n    stop,\n    pause,\n    resume,\n    isActive\n  };\n}\nvar __defProp$1 = Object.defineProperty;\nvar __defProps$1 = Object.defineProperties;\nvar __getOwnPropDescs$1 = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$1 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$1 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$1 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues$1 = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp$1.call(b, prop)) __defNormalProp$1(a, prop, b[prop]);\n  if (__getOwnPropSymbols$1) for (var prop of __getOwnPropSymbols$1(b)) {\n    if (__propIsEnum$1.call(b, prop)) __defNormalProp$1(a, prop, b[prop]);\n  }\n  return a;\n};\nvar __spreadProps$1 = (a, b) => __defProps$1(a, __getOwnPropDescs$1(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source) if (__hasOwnProp$1.call(source, prop) && exclude.indexOf(prop) < 0) target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$1) for (var prop of __getOwnPropSymbols$1(source)) {\n    if (exclude.indexOf(prop) < 0 && __propIsEnum$1.call(source, prop)) target[prop] = source[prop];\n  }\n  return target;\n};\nfunction watchThrottled(source, cb, options = {}) {\n  const _a = options,\n    {\n      throttle = 0,\n      trailing = true,\n      leading = true\n    } = _a,\n    watchOptions = __objRest(_a, [\"throttle\", \"trailing\", \"leading\"]);\n  return watchWithFilter(source, cb, __spreadProps$1(__spreadValues$1({}, watchOptions), {\n    eventFilter: throttleFilter(throttle, trailing, leading)\n  }));\n}\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols) for (var prop of __getOwnPropSymbols(b)) {\n    if (__propIsEnum.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nfunction watchTriggerable(source, cb, options = {}) {\n  let cleanupFn;\n  function onEffect() {\n    if (!cleanupFn) return;\n    const fn = cleanupFn;\n    cleanupFn = void 0;\n    fn();\n  }\n  function onCleanup(callback) {\n    cleanupFn = callback;\n  }\n  const _cb = (value, oldValue) => {\n    onEffect();\n    return cb(value, oldValue, onCleanup);\n  };\n  const res = watchIgnorable(source, _cb, options);\n  const {\n    ignoreUpdates\n  } = res;\n  const trigger = () => {\n    let res2;\n    ignoreUpdates(() => {\n      res2 = _cb(getWatchSources(source), getOldValue(source));\n    });\n    return res2;\n  };\n  return __spreadProps(__spreadValues({}, res), {\n    trigger\n  });\n}\nfunction getWatchSources(sources) {\n  if (isReactive(sources)) return sources;\n  if (Array.isArray(sources)) return sources.map(item => getOneWatchSource(item));\n  return getOneWatchSource(sources);\n}\nfunction getOneWatchSource(source) {\n  return typeof source === \"function\" ? source() : unref(source);\n}\nfunction getOldValue(source) {\n  return Array.isArray(source) ? source.map(() => void 0) : void 0;\n}\nfunction whenever(source, cb, options) {\n  return watch(source, (v, ov, onInvalidate) => {\n    if (v) cb(v, ov, onInvalidate);\n  }, options);\n}\nexport { __onlyVue27Plus, __onlyVue3, assert, refAutoReset as autoResetRef, bypassFilter, clamp, computedEager, computedWithControl, containsProp, computedWithControl as controlledComputed, controlledRef, createEventHook, createFilterWrapper, createGlobalState, createInjectionState, reactify as createReactiveFn, createSharedComposable, createSingletonPromise, debounceFilter, refDebounced as debouncedRef, watchDebounced as debouncedWatch, directiveHooks, computedEager as eagerComputed, extendRef, formatDate, get, hasOwn, identity, watchIgnorable as ignorableWatch, increaseWithUnit, invoke, isBoolean, isClient, isDef, isDefined, isFunction, isIOS, isNumber, isObject, isString, isWindow, makeDestructurable, noop, normalizeDate, now, objectPick, pausableFilter, watchPausable as pausableWatch, promiseTimeout, rand, reactify, reactifyObject, reactiveComputed, reactiveOmit, reactivePick, refAutoReset, refDebounced, refDefault, refThrottled, refWithControl, resolveRef, resolveUnref, set, syncRef, syncRefs, throttleFilter, refThrottled as throttledRef, watchThrottled as throttledWatch, timestamp, toReactive, toRefs, tryOnBeforeMount, tryOnBeforeUnmount, tryOnMounted, tryOnScopeDispose, tryOnUnmounted, until, useArrayEvery, useArrayFilter, useArrayFind, useArrayFindIndex, useArrayFindLast, useArrayJoin, useArrayMap, useArrayReduce, useArraySome, useArrayUnique, useCounter, useDateFormat, refDebounced as useDebounce, useDebounceFn, useInterval, useIntervalFn, useLastChanged, refThrottled as useThrottle, useThrottleFn, useTimeout, useTimeoutFn, useToNumber, useToString, useToggle, watchArray, watchAtMost, watchDebounced, watchIgnorable, watchOnce, watchPausable, watchThrottled, watchTriggerable, watchWithFilter, whenever };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}