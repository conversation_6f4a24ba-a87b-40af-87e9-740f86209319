{"ast": null, "code": "import { unref as _unref, createElementVNode as _createElementVNode, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"product-page\"\n};\nconst _hoisted_2 = {\n  class: \"hero\"\n};\nconst _hoisted_3 = {\n  class: \"hero-content\"\n};\nconst _hoisted_4 = [\"src\"];\nconst _hoisted_5 = {\n  class: \"cta-buttons\"\n};\nconst _hoisted_6 = {\n  href: \"https://gengxin.geluman.cn/downloads/GLM-Highlight-1.0.0.zip\",\n  class: \"download-btn\",\n  target: \"_blank\"\n};\nconst _hoisted_7 = {\n  class: \"demo\"\n};\nconst _hoisted_8 = {\n  class: \"container\"\n};\nconst _hoisted_9 = {\n  class: \"demo-gallery\"\n};\nconst _hoisted_10 = {\n  class: \"demo-item\"\n};\nconst _hoisted_11 = {\n  class: \"demo-image-container\"\n};\nconst _hoisted_12 = [\"src\"];\nconst _hoisted_13 = {\n  class: \"demo-item\"\n};\nconst _hoisted_14 = {\n  class: \"demo-image-container\"\n};\nconst _hoisted_15 = [\"src\"];\nconst _hoisted_16 = {\n  class: \"demo-item\"\n};\nconst _hoisted_17 = {\n  class: \"demo-image-container\"\n};\nconst _hoisted_18 = [\"src\"];\nconst _hoisted_19 = {\n  class: \"demo-item\"\n};\nconst _hoisted_20 = {\n  class: \"demo-image-container\"\n};\nconst _hoisted_21 = [\"src\"];\nconst _hoisted_22 = {\n  class: \"features\"\n};\nconst _hoisted_23 = {\n  class: \"container\"\n};\nconst _hoisted_24 = {\n  class: \"features-grid\"\n};\nconst _hoisted_25 = {\n  class: \"feature-icon\"\n};\nconst _hoisted_26 = {\n  class: \"usage\"\n};\nconst _hoisted_27 = {\n  class: \"container\"\n};\nconst _hoisted_28 = {\n  class: \"usage-steps\"\n};\nconst _hoisted_29 = {\n  class: \"step-number\"\n};\nimport { highlight } from \"@/assets\";\nimport { Download } from \"@element-plus/icons-vue\";\nimport highlight2 from \"@/assets/img/highlight-2.png\";\nimport highlightSetting from \"@/assets/img/highlight-setting.png\";\nimport highlightShow from \"@/assets/img/highlight-show.png\";\nimport highlightShow1 from \"@/assets/img/highlight-show1.png\";\nconst __default__ = {\n  name: \"HighlightPage\"\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props) {\n    const features = [{\n      icon: \"📁\",\n      title: \"多分类高亮\",\n      description: \"支持创建多个高亮分类，每个分类可独立设置颜色和名称，灵活管理不同场景的高亮需求\"\n    }, {\n      icon: \"🎨\",\n      title: \"颜色自定义\",\n      description: \"提供20种精心挑选的预设颜色，可为每个分类设置不同的高亮颜色，让重点内容更显眼\"\n    }, {\n      icon: \"🔍\",\n      title: \"关键词管理\",\n      description: \"支持添加、编辑、删除关键词，每个分类可包含多个关键词，并支持关键词搜索和去重\"\n    }, {\n      icon: \"🔄\",\n      title: \"配置同步\",\n      description: \"支持配置导入导出，可生成分享码与他人分享配置，轻松备份和迁移您的设置\"\n    }, {\n      icon: \"⚡\",\n      title: \"实时高亮\",\n      description: \"输入关键词后立即在页面上高亮显示匹配文本，即时预览效果，快速调整\"\n    }, {\n      icon: \"🎚️\",\n      title: \"灵活控制\",\n      description: \"总开关控制所有高亮显示/隐藏，每个分类独立开关，满足不同场景需求\"\n    }];\n    const steps = [{\n      number: \"1\",\n      title: \"安装插件\",\n      description: \"从下方下载按钮下载并解压，打开Chrome或者Edge浏览器，进入chrome://extensions/或者edge://extensions/，将解压后的文件夹拖入页面中，完成安装\"\n    }, {\n      number: \"2\",\n      title: \"添加关键词\",\n      description: \"在插件设置中添加需要高亮的关键词\"\n    }, {\n      number: \"3\",\n      title: \"选择颜色\",\n      description: \"为关键词设置合适的高亮颜色\"\n    }, {\n      number: \"4\",\n      title: \"开始使用\",\n      description: \"浏览网页时自动高亮显示关键词\"\n    }];\n    return (_ctx, _cache) => {\n      const _component_el_icon = _resolveComponent(\"el-icon\");\n      return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"img\", {\n        src: _unref(highlight),\n        alt: \"高亮插件\",\n        class: \"plugin-logo\"\n      }, null, 8, _hoisted_4), _cache[1] || (_cache[1] = _createElementVNode(\"h1\", null, \"网页文本高亮插件\", -1)), _cache[2] || (_cache[2] = _createElementVNode(\"p\", {\n        class: \"subtitle\"\n      }, \"让重要信息一目了然，提升阅读效率\", -1)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"a\", _hoisted_6, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_unref(Download))]),\n        _: 1\n      }), _cache[0] || (_cache[0] = _createTextVNode(\" 下载插件 \"))])])])]), _createElementVNode(\"section\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_cache[7] || (_cache[7] = _createElementVNode(\"h2\", {\n        class: \"section-title\"\n      }, \"产品演示\", -1)), _cache[8] || (_cache[8] = _createElementVNode(\"p\", {\n        class: \"section-subtitle\"\n      }, \"简单易用的设置界面，强大的高亮功能\", -1)), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"img\", {\n        src: _unref(highlightShow1),\n        alt: \"网页文本高亮插件 - 主界面\",\n        class: \"demo-image\"\n      }, null, 8, _hoisted_12)]), _cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n        class: \"image-caption\"\n      }, \"主界面 - 轻松创建多个高亮分类\", -1))]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"img\", {\n        src: _unref(highlight2),\n        alt: \"网页文本高亮插件 - 效果展示2\",\n        class: \"demo-image\"\n      }, null, 8, _hoisted_15)]), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n        class: \"image-caption\"\n      }, \"多彩高亮 - 不同类别信息区分明显\", -1))]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"img\", {\n        src: _unref(highlightSetting),\n        alt: \"网页文本高亮插件 - 设置界面\",\n        class: \"demo-image\"\n      }, null, 8, _hoisted_18)]), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n        class: \"image-caption\"\n      }, \" 个性化设置 - 自定义高亮模式，启用黑白名单控制网页高亮 \", -1))]), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"img\", {\n        src: _unref(highlightShow),\n        alt: \"网页文本高亮插件 - 效果展示\",\n        class: \"demo-image\"\n      }, null, 8, _hoisted_21)]), _cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n        class: \"image-caption\"\n      }, \"高亮效果 - 重要信息一目了然\", -1))])])])]), _createElementVNode(\"section\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_cache[9] || (_cache[9] = _createElementVNode(\"h2\", null, \"核心功能\", -1)), _createElementVNode(\"div\", _hoisted_24, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(features, (feature, index) => {\n        return _createElementVNode(\"div\", {\n          key: feature.title,\n          class: \"feature-card\"\n        }, [_createElementVNode(\"div\", _hoisted_25, _toDisplayString(feature.icon), 1), _createElementVNode(\"h3\", null, _toDisplayString(feature.title), 1), _createElementVNode(\"p\", null, _toDisplayString(feature.description), 1)]);\n      }), 64))])])]), _createElementVNode(\"section\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_cache[10] || (_cache[10] = _createElementVNode(\"h2\", null, \"使用说明\", -1)), _createElementVNode(\"div\", _hoisted_28, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(steps, step => {\n        return _createElementVNode(\"div\", {\n          class: \"step\",\n          key: step.title\n        }, [_createElementVNode(\"div\", _hoisted_29, _toDisplayString(step.number), 1), _createElementVNode(\"h3\", null, _toDisplayString(step.title), 1), _createElementVNode(\"p\", null, _toDisplayString(step.description), 1)]);\n      }), 64))])])])]);\n    };\n  }\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}