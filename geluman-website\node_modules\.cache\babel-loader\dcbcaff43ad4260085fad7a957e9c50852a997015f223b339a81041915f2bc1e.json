{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { warn } from 'vue';\nimport { fromPairs } from 'lodash-unified';\nimport { isObject, hasOwn } from '@vue/shared';\nconst epPropKey = \"__epPropKey\";\nconst definePropType = val => val;\nconst isEpProp = val => isObject(val) && !!val[epPropKey];\nconst buildProp = (prop, key) => {\n  if (!isObject(prop) || isEpProp(prop)) return prop;\n  const {\n    values,\n    required,\n    default: defaultValue,\n    type,\n    validator\n  } = prop;\n  const _validator = values || validator ? val => {\n    let valid = false;\n    let allowedValues = [];\n    if (values) {\n      allowedValues = Array.from(values);\n      if (hasOwn(prop, \"default\")) {\n        allowedValues.push(defaultValue);\n      }\n      valid || (valid = allowedValues.includes(val));\n    }\n    if (validator) valid || (valid = validator(val));\n    if (!valid && allowedValues.length > 0) {\n      const allowValuesText = [...new Set(allowedValues)].map(value => JSON.stringify(value)).join(\", \");\n      warn(`Invalid prop: validation failed${key ? ` for prop \"${key}\"` : \"\"}. Expected one of [${allowValuesText}], got value ${JSON.stringify(val)}.`);\n    }\n    return valid;\n  } : void 0;\n  const epProp = {\n    type,\n    required: !!required,\n    validator: _validator,\n    [epPropKey]: true\n  };\n  if (hasOwn(prop, \"default\")) epProp.default = defaultValue;\n  return epProp;\n};\nconst buildProps = props => fromPairs(Object.entries(props).map(([key, option]) => [key, buildProp(option, key)]));\nexport { buildProp, buildProps, definePropType, epPropKey, isEpProp };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}