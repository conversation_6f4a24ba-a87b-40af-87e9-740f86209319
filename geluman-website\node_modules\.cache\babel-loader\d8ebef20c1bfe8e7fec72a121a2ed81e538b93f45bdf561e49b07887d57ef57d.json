{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { isString } from '@vue/shared';\nconst mentionDropdownProps = buildProps({\n  options: {\n    type: definePropType(Array),\n    default: () => []\n  },\n  loading: Boolean,\n  disabled: Boolean,\n  contentId: String,\n  ariaLabel: String\n});\nconst mentionDropdownEmits = {\n  select: option => isString(option.value)\n};\nexport { mentionDropdownEmits, mentionDropdownProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}