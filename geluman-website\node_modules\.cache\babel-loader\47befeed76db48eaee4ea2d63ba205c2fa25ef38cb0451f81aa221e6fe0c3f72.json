{"ast": null, "code": "const FOCUS_AFTER_TRAPPED = \"focus-trap.focus-after-trapped\";\nconst FOCUS_AFTER_RELEASED = \"focus-trap.focus-after-released\";\nconst FOCUSOUT_PREVENTED = \"focus-trap.focusout-prevented\";\nconst FOCUS_AFTER_TRAPPED_OPTS = {\n  cancelable: true,\n  bubbles: false\n};\nconst FOCUSOUT_PREVENTED_OPTS = {\n  cancelable: true,\n  bubbles: false\n};\nconst ON_TRAP_FOCUS_EVT = \"focusAfterTrapped\";\nconst ON_RELEASE_FOCUS_EVT = \"focusAfterReleased\";\nconst FOCUS_TRAP_INJECTION_KEY = Symbol(\"elFocusTrap\");\nexport { FOCUSOUT_PREVENTED, FOCUSOUT_PREVENTED_OPTS, FOCUS_AFTER_RELEASED, FOCUS_AFTER_TRAPPED, FOCUS_AFTER_TRAPPED_OPTS, FOCUS_TRAP_INJECTION_KEY, ON_RELEASE_FOCUS_EVT, ON_TRAP_FOCUS_EVT };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}