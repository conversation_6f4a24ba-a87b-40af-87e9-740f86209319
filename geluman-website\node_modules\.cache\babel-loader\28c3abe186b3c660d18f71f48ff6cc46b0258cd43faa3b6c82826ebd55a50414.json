{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, getCurrentInstance, ref, computed, unref, onMounted, onUpdated, onActivated, resolveDynamicComponent, h, Fragment, nextTick } from 'vue';\nimport { useEventListener, isClient } from '@vueuse/core';\nimport { useCache } from '../hooks/use-cache.mjs';\nimport useWheel from '../hooks/use-wheel.mjs';\nimport ScrollBar from '../components/scrollbar.mjs';\nimport { isHorizontal, getRTLOffsetType, getScrollDir } from '../utils.mjs';\nimport { virtualizedListProps } from '../props.mjs';\nimport { ITEM_RENDER_EVT, SCROLL_EVT, HORIZONTAL, RTL, RTL_OFFSET_POS_ASC, RTL_OFFSET_NAG, BACKWARD, FORWARD, AUTO_ALIGNMENT, RTL_OFFSET_POS_DESC } from '../defaults.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isNumber } from '../../../../utils/types.mjs';\nimport { isString, hasOwn } from '@vue/shared';\nconst createList = ({\n  name,\n  getOffset,\n  getItemSize,\n  getItemOffset,\n  getEstimatedTotalSize,\n  getStartIndexForOffset,\n  getStopIndexForStartIndex,\n  initCache,\n  clearCache,\n  validateProps\n}) => {\n  return defineComponent({\n    name: name != null ? name : \"ElVirtualList\",\n    props: virtualizedListProps,\n    emits: [ITEM_RENDER_EVT, SCROLL_EVT],\n    setup(props, {\n      emit,\n      expose\n    }) {\n      validateProps(props);\n      const instance = getCurrentInstance();\n      const ns = useNamespace(\"vl\");\n      const dynamicSizeCache = ref(initCache(props, instance));\n      const getItemStyleCache = useCache();\n      const windowRef = ref();\n      const innerRef = ref();\n      const scrollbarRef = ref();\n      const states = ref({\n        isScrolling: false,\n        scrollDir: \"forward\",\n        scrollOffset: isNumber(props.initScrollOffset) ? props.initScrollOffset : 0,\n        updateRequested: false,\n        isScrollbarDragging: false,\n        scrollbarAlwaysOn: props.scrollbarAlwaysOn\n      });\n      const itemsToRender = computed(() => {\n        const {\n          total,\n          cache\n        } = props;\n        const {\n          isScrolling,\n          scrollDir,\n          scrollOffset\n        } = unref(states);\n        if (total === 0) {\n          return [0, 0, 0, 0];\n        }\n        const startIndex = getStartIndexForOffset(props, scrollOffset, unref(dynamicSizeCache));\n        const stopIndex = getStopIndexForStartIndex(props, startIndex, scrollOffset, unref(dynamicSizeCache));\n        const cacheBackward = !isScrolling || scrollDir === BACKWARD ? Math.max(1, cache) : 1;\n        const cacheForward = !isScrolling || scrollDir === FORWARD ? Math.max(1, cache) : 1;\n        return [Math.max(0, startIndex - cacheBackward), Math.max(0, Math.min(total - 1, stopIndex + cacheForward)), startIndex, stopIndex];\n      });\n      const estimatedTotalSize = computed(() => getEstimatedTotalSize(props, unref(dynamicSizeCache)));\n      const _isHorizontal = computed(() => isHorizontal(props.layout));\n      const windowStyle = computed(() => [{\n        position: \"relative\",\n        [`overflow-${_isHorizontal.value ? \"x\" : \"y\"}`]: \"scroll\",\n        WebkitOverflowScrolling: \"touch\",\n        willChange: \"transform\"\n      }, {\n        direction: props.direction,\n        height: isNumber(props.height) ? `${props.height}px` : props.height,\n        width: isNumber(props.width) ? `${props.width}px` : props.width\n      }, props.style]);\n      const innerStyle = computed(() => {\n        const size = unref(estimatedTotalSize);\n        const horizontal = unref(_isHorizontal);\n        return {\n          height: horizontal ? \"100%\" : `${size}px`,\n          pointerEvents: unref(states).isScrolling ? \"none\" : void 0,\n          width: horizontal ? `${size}px` : \"100%\"\n        };\n      });\n      const clientSize = computed(() => _isHorizontal.value ? props.width : props.height);\n      const {\n        onWheel\n      } = useWheel({\n        atStartEdge: computed(() => states.value.scrollOffset <= 0),\n        atEndEdge: computed(() => states.value.scrollOffset >= estimatedTotalSize.value),\n        layout: computed(() => props.layout)\n      }, offset => {\n        var _a, _b;\n        (_b = (_a = scrollbarRef.value).onMouseUp) == null ? void 0 : _b.call(_a);\n        scrollTo(Math.min(states.value.scrollOffset + offset, estimatedTotalSize.value - clientSize.value));\n      });\n      useEventListener(windowRef, \"wheel\", onWheel, {\n        passive: false\n      });\n      const emitEvents = () => {\n        const {\n          total\n        } = props;\n        if (total > 0) {\n          const [cacheStart, cacheEnd, visibleStart, visibleEnd] = unref(itemsToRender);\n          emit(ITEM_RENDER_EVT, cacheStart, cacheEnd, visibleStart, visibleEnd);\n        }\n        const {\n          scrollDir,\n          scrollOffset,\n          updateRequested\n        } = unref(states);\n        emit(SCROLL_EVT, scrollDir, scrollOffset, updateRequested);\n      };\n      const scrollVertically = e => {\n        const {\n          clientHeight,\n          scrollHeight,\n          scrollTop\n        } = e.currentTarget;\n        const _states = unref(states);\n        if (_states.scrollOffset === scrollTop) {\n          return;\n        }\n        const scrollOffset = Math.max(0, Math.min(scrollTop, scrollHeight - clientHeight));\n        states.value = {\n          ..._states,\n          isScrolling: true,\n          scrollDir: getScrollDir(_states.scrollOffset, scrollOffset),\n          scrollOffset,\n          updateRequested: false\n        };\n        nextTick(resetIsScrolling);\n      };\n      const scrollHorizontally = e => {\n        const {\n          clientWidth,\n          scrollLeft,\n          scrollWidth\n        } = e.currentTarget;\n        const _states = unref(states);\n        if (_states.scrollOffset === scrollLeft) {\n          return;\n        }\n        const {\n          direction\n        } = props;\n        let scrollOffset = scrollLeft;\n        if (direction === RTL) {\n          switch (getRTLOffsetType()) {\n            case RTL_OFFSET_NAG:\n              {\n                scrollOffset = -scrollLeft;\n                break;\n              }\n            case RTL_OFFSET_POS_DESC:\n              {\n                scrollOffset = scrollWidth - clientWidth - scrollLeft;\n                break;\n              }\n          }\n        }\n        scrollOffset = Math.max(0, Math.min(scrollOffset, scrollWidth - clientWidth));\n        states.value = {\n          ..._states,\n          isScrolling: true,\n          scrollDir: getScrollDir(_states.scrollOffset, scrollOffset),\n          scrollOffset,\n          updateRequested: false\n        };\n        nextTick(resetIsScrolling);\n      };\n      const onScroll = e => {\n        unref(_isHorizontal) ? scrollHorizontally(e) : scrollVertically(e);\n        emitEvents();\n      };\n      const onScrollbarScroll = (distanceToGo, totalSteps) => {\n        const offset = (estimatedTotalSize.value - clientSize.value) / totalSteps * distanceToGo;\n        scrollTo(Math.min(estimatedTotalSize.value - clientSize.value, offset));\n      };\n      const scrollTo = offset => {\n        offset = Math.max(offset, 0);\n        if (offset === unref(states).scrollOffset) {\n          return;\n        }\n        states.value = {\n          ...unref(states),\n          scrollOffset: offset,\n          scrollDir: getScrollDir(unref(states).scrollOffset, offset),\n          updateRequested: true\n        };\n        nextTick(resetIsScrolling);\n      };\n      const scrollToItem = (idx, alignment = AUTO_ALIGNMENT) => {\n        const {\n          scrollOffset\n        } = unref(states);\n        idx = Math.max(0, Math.min(idx, props.total - 1));\n        scrollTo(getOffset(props, idx, alignment, scrollOffset, unref(dynamicSizeCache)));\n      };\n      const getItemStyle = idx => {\n        const {\n          direction,\n          itemSize,\n          layout\n        } = props;\n        const itemStyleCache = getItemStyleCache.value(clearCache && itemSize, clearCache && layout, clearCache && direction);\n        let style;\n        if (hasOwn(itemStyleCache, String(idx))) {\n          style = itemStyleCache[idx];\n        } else {\n          const offset = getItemOffset(props, idx, unref(dynamicSizeCache));\n          const size = getItemSize(props, idx, unref(dynamicSizeCache));\n          const horizontal = unref(_isHorizontal);\n          const isRtl = direction === RTL;\n          const offsetHorizontal = horizontal ? offset : 0;\n          itemStyleCache[idx] = style = {\n            position: \"absolute\",\n            left: isRtl ? void 0 : `${offsetHorizontal}px`,\n            right: isRtl ? `${offsetHorizontal}px` : void 0,\n            top: !horizontal ? `${offset}px` : 0,\n            height: !horizontal ? `${size}px` : \"100%\",\n            width: horizontal ? `${size}px` : \"100%\"\n          };\n        }\n        return style;\n      };\n      const resetIsScrolling = () => {\n        states.value.isScrolling = false;\n        nextTick(() => {\n          getItemStyleCache.value(-1, null, null);\n        });\n      };\n      const resetScrollTop = () => {\n        const window = windowRef.value;\n        if (window) {\n          window.scrollTop = 0;\n        }\n      };\n      onMounted(() => {\n        if (!isClient) return;\n        const {\n          initScrollOffset\n        } = props;\n        const windowElement = unref(windowRef);\n        if (isNumber(initScrollOffset) && windowElement) {\n          if (unref(_isHorizontal)) {\n            windowElement.scrollLeft = initScrollOffset;\n          } else {\n            windowElement.scrollTop = initScrollOffset;\n          }\n        }\n        emitEvents();\n      });\n      onUpdated(() => {\n        const {\n          direction,\n          layout\n        } = props;\n        const {\n          scrollOffset,\n          updateRequested\n        } = unref(states);\n        const windowElement = unref(windowRef);\n        if (updateRequested && windowElement) {\n          if (layout === HORIZONTAL) {\n            if (direction === RTL) {\n              switch (getRTLOffsetType()) {\n                case RTL_OFFSET_NAG:\n                  {\n                    windowElement.scrollLeft = -scrollOffset;\n                    break;\n                  }\n                case RTL_OFFSET_POS_ASC:\n                  {\n                    windowElement.scrollLeft = scrollOffset;\n                    break;\n                  }\n                default:\n                  {\n                    const {\n                      clientWidth,\n                      scrollWidth\n                    } = windowElement;\n                    windowElement.scrollLeft = scrollWidth - clientWidth - scrollOffset;\n                    break;\n                  }\n              }\n            } else {\n              windowElement.scrollLeft = scrollOffset;\n            }\n          } else {\n            windowElement.scrollTop = scrollOffset;\n          }\n        }\n      });\n      onActivated(() => {\n        unref(windowRef).scrollTop = unref(states).scrollOffset;\n      });\n      const api = {\n        ns,\n        clientSize,\n        estimatedTotalSize,\n        windowStyle,\n        windowRef,\n        innerRef,\n        innerStyle,\n        itemsToRender,\n        scrollbarRef,\n        states,\n        getItemStyle,\n        onScroll,\n        onScrollbarScroll,\n        onWheel,\n        scrollTo,\n        scrollToItem,\n        resetScrollTop\n      };\n      expose({\n        windowRef,\n        innerRef,\n        getItemStyleCache,\n        scrollTo,\n        scrollToItem,\n        resetScrollTop,\n        states\n      });\n      return api;\n    },\n    render(ctx) {\n      var _a;\n      const {\n        $slots,\n        className,\n        clientSize,\n        containerElement,\n        data,\n        getItemStyle,\n        innerElement,\n        itemsToRender,\n        innerStyle,\n        layout,\n        total,\n        onScroll,\n        onScrollbarScroll,\n        states,\n        useIsScrolling,\n        windowStyle,\n        ns\n      } = ctx;\n      const [start, end] = itemsToRender;\n      const Container = resolveDynamicComponent(containerElement);\n      const Inner = resolveDynamicComponent(innerElement);\n      const children = [];\n      if (total > 0) {\n        for (let i = start; i <= end; i++) {\n          children.push(h(Fragment, {\n            key: i\n          }, (_a = $slots.default) == null ? void 0 : _a.call($slots, {\n            data,\n            index: i,\n            isScrolling: useIsScrolling ? states.isScrolling : void 0,\n            style: getItemStyle(i)\n          })));\n        }\n      }\n      const InnerNode = [h(Inner, {\n        style: innerStyle,\n        ref: \"innerRef\"\n      }, !isString(Inner) ? {\n        default: () => children\n      } : children)];\n      const scrollbar = h(ScrollBar, {\n        ref: \"scrollbarRef\",\n        clientSize,\n        layout,\n        onScroll: onScrollbarScroll,\n        ratio: clientSize * 100 / this.estimatedTotalSize,\n        scrollFrom: states.scrollOffset / (this.estimatedTotalSize - clientSize),\n        total\n      });\n      const listContainer = h(Container, {\n        class: [ns.e(\"window\"), className],\n        style: windowStyle,\n        onScroll,\n        ref: \"windowRef\",\n        key: 0\n      }, !isString(Container) ? {\n        default: () => [InnerNode]\n      } : [InnerNode]);\n      return h(\"div\", {\n        key: 0,\n        class: [ns.e(\"wrapper\"), states.scrollbarAlwaysOn ? \"always-on\" : \"\"]\n      }, [listContainer, scrollbar]);\n    }\n  });\n};\nexport { createList as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}