{"ast": null, "code": "import createFind from './_createFind.js';\nimport findLastIndex from './findLastIndex.js';\n\n/**\n * This method is like `_.find` except that it iterates over elements of\n * `collection` from right to left.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Collection\n * @param {Array|Object} collection The collection to inspect.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @param {number} [fromIndex=collection.length-1] The index to search from.\n * @returns {*} Returns the matched element, else `undefined`.\n * @example\n *\n * _.findLast([1, 2, 3, 4], function(n) {\n *   return n % 2 == 1;\n * });\n * // => 3\n */\nvar findLast = createFind(findLastIndex);\nexport default findLast;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}