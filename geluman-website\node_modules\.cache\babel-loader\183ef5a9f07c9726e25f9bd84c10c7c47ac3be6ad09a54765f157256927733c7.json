{"ast": null, "code": "import Descriptions from './src/description2.mjs';\nimport DescriptionItem from './src/description-item.mjs';\nexport { descriptionItemProps } from './src/description-item.mjs';\nexport { descriptionProps } from './src/description.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElDescriptions = withInstall(Descriptions, {\n  DescriptionsItem: DescriptionItem\n});\nconst ElDescriptionsItem = withNoopInstall(DescriptionItem);\nexport { ElDescriptions, ElDescriptionsItem, ElDescriptions as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}