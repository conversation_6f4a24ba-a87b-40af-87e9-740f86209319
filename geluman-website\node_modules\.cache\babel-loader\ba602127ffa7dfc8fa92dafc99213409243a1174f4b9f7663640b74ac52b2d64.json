{"ast": null, "code": "export function getDevtoolsGlobalHook() {\n  return getTarget().__VUE_DEVTOOLS_GLOBAL_HOOK__;\n}\nexport function getTarget() {\n  // @ts-expect-error navigator and windows are not available in all environments\n  return typeof navigator !== 'undefined' && typeof window !== 'undefined' ? window : typeof globalThis !== 'undefined' ? globalThis : {};\n}\nexport const isProxyAvailable = typeof Proxy === 'function';", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}