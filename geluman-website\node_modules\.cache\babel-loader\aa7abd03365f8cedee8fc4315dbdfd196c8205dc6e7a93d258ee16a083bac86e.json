{"ast": null, "code": "import { inject, computed } from 'vue';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nfunction useMapState() {\n  const table = inject(TABLE_INJECTION_KEY);\n  const store = table == null ? void 0 : table.store;\n  const leftFixedLeafCount = computed(() => {\n    return store.states.fixedLeafColumnsLength.value;\n  });\n  const rightFixedLeafCount = computed(() => {\n    return store.states.rightFixedColumns.value.length;\n  });\n  const columnsCount = computed(() => {\n    return store.states.columns.value.length;\n  });\n  const leftFixedCount = computed(() => {\n    return store.states.fixedColumns.value.length;\n  });\n  const rightFixedCount = computed(() => {\n    return store.states.rightFixedColumns.value.length;\n  });\n  return {\n    leftFixedLeafCount,\n    rightFixedLeafCount,\n    columnsCount,\n    leftFixedCount,\n    rightFixedCount,\n    columns: store.states.columns\n  };\n}\nexport { useMapState as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}