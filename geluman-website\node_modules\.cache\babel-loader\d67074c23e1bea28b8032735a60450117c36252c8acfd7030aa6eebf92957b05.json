{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nconst dialogContentProps = buildProps({\n  center: Boolean,\n  alignCenter: Boolean,\n  closeIcon: {\n    type: iconPropType\n  },\n  draggable: Boolean,\n  overflow: Boolean,\n  fullscreen: Boolean,\n  headerClass: String,\n  bodyClass: String,\n  footerClass: String,\n  showClose: {\n    type: Boolean,\n    default: true\n  },\n  title: {\n    type: String,\n    default: \"\"\n  },\n  ariaLevel: {\n    type: String,\n    default: \"2\"\n  }\n});\nconst dialogContentEmits = {\n  close: () => true\n};\nexport { dialogContentEmits, dialogContentProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}