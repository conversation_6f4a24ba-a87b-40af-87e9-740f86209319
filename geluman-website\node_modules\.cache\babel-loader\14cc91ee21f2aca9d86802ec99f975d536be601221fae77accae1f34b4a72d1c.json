{"ast": null, "code": "import Popover from './src/popover2.mjs';\nimport PopoverDirective, { VPopover } from './src/directive.mjs';\nexport { popoverEmits, popoverProps } from './src/popover.mjs';\nimport { withInstallDirective, withInstall } from '../../utils/vue/install.mjs';\nconst ElPopoverDirective = withInstallDirective(PopoverDirective, VPopover);\nconst ElPopover = withInstall(Popover, {\n  directive: ElPopoverDirective\n});\nexport { ElPopover, ElPopoverDirective, ElPopover as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}