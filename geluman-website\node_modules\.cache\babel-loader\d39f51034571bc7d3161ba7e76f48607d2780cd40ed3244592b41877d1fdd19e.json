{"ast": null, "code": "import { onMounted, watchEffect, onBeforeUnmount } from 'vue';\nimport { addUnit } from '../../utils/dom/style.mjs';\nconst useDraggable = (targetRef, dragRef, draggable, overflow) => {\n  const transform = {\n    offsetX: 0,\n    offsetY: 0\n  };\n  const adjustPosition = (moveX, moveY) => {\n    if (targetRef.value) {\n      const {\n        offsetX,\n        offsetY\n      } = transform;\n      const targetRect = targetRef.value.getBoundingClientRect();\n      const targetLeft = targetRect.left;\n      const targetTop = targetRect.top;\n      const targetWidth = targetRect.width;\n      const targetHeight = targetRect.height;\n      const clientWidth = document.documentElement.clientWidth;\n      const clientHeight = document.documentElement.clientHeight;\n      const minLeft = -targetLeft + offsetX;\n      const minTop = -targetTop + offsetY;\n      const maxLeft = clientWidth - targetLeft - targetWidth + offsetX;\n      const maxTop = clientHeight - targetTop - targetHeight + offsetY;\n      if (!(overflow == null ? void 0 : overflow.value)) {\n        moveX = Math.min(Math.max(moveX, minLeft), maxLeft);\n        moveY = Math.min(Math.max(moveY, minTop), maxTop);\n      }\n      transform.offsetX = moveX;\n      transform.offsetY = moveY;\n      targetRef.value.style.transform = `translate(${addUnit(moveX)}, ${addUnit(moveY)})`;\n    }\n  };\n  const onMousedown = e => {\n    const downX = e.clientX;\n    const downY = e.clientY;\n    const {\n      offsetX,\n      offsetY\n    } = transform;\n    const onMousemove = e2 => {\n      const moveX = offsetX + e2.clientX - downX;\n      const moveY = offsetY + e2.clientY - downY;\n      adjustPosition(moveX, moveY);\n    };\n    const onMouseup = () => {\n      document.removeEventListener(\"mousemove\", onMousemove);\n      document.removeEventListener(\"mouseup\", onMouseup);\n    };\n    document.addEventListener(\"mousemove\", onMousemove);\n    document.addEventListener(\"mouseup\", onMouseup);\n  };\n  const onDraggable = () => {\n    if (dragRef.value && targetRef.value) {\n      dragRef.value.addEventListener(\"mousedown\", onMousedown);\n      window.addEventListener(\"resize\", updatePosition);\n    }\n  };\n  const offDraggable = () => {\n    if (dragRef.value && targetRef.value) {\n      dragRef.value.removeEventListener(\"mousedown\", onMousedown);\n      window.removeEventListener(\"resize\", updatePosition);\n    }\n  };\n  const resetPosition = () => {\n    transform.offsetX = 0;\n    transform.offsetY = 0;\n    if (targetRef.value) {\n      targetRef.value.style.transform = \"\";\n    }\n  };\n  const updatePosition = () => {\n    const {\n      offsetX,\n      offsetY\n    } = transform;\n    adjustPosition(offsetX, offsetY);\n  };\n  onMounted(() => {\n    watchEffect(() => {\n      if (draggable.value) {\n        onDraggable();\n      } else {\n        offDraggable();\n      }\n    });\n  });\n  onBeforeUnmount(() => {\n    offDraggable();\n  });\n  return {\n    resetPosition,\n    updatePosition\n  };\n};\nexport { useDraggable };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}