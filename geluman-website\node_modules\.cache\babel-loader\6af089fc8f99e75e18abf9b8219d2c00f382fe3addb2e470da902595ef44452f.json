{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nconst tooltipV2Strategies = [\"absolute\", \"fixed\"];\nconst tooltipV2Placements = [\"top-start\", \"top-end\", \"top\", \"bottom-start\", \"bottom-end\", \"bottom\", \"left-start\", \"left-end\", \"left\", \"right-start\", \"right-end\", \"right\"];\nconst tooltipV2ContentProps = buildProps({\n  arrowPadding: {\n    type: definePropType(Number),\n    default: 5\n  },\n  effect: {\n    type: definePropType(String),\n    default: \"light\"\n  },\n  contentClass: String,\n  placement: {\n    type: definePropType(String),\n    values: tooltipV2Placements,\n    default: \"bottom\"\n  },\n  reference: {\n    type: definePropType(Object),\n    default: null\n  },\n  offset: {\n    type: Number,\n    default: 8\n  },\n  strategy: {\n    type: definePropType(String),\n    values: tooltipV2Strategies,\n    default: \"absolute\"\n  },\n  showArrow: Boolean,\n  ...useAriaProps([\"ariaLabel\"])\n});\nexport { tooltipV2ContentProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}