{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, computed, shallowRef, ref, onMounted, watch, onBeforeUnmount, openBlock, createElementBlock, normalizeStyle, renderSlot } from 'vue';\nimport { useMutationObserver } from '@vueuse/core';\nimport { watermarkProps } from './watermark.mjs';\nimport { reRendering, getStyleStr, getPixelRatio } from './utils.mjs';\nimport useClips, { FontGap } from './useClips.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { isArray } from '@vue/shared';\nimport { isUndefined } from '../../../utils/types.mjs';\nconst __default__ = defineComponent({\n  name: \"ElWatermark\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: watermarkProps,\n  setup(__props) {\n    const props = __props;\n    const style = {\n      position: \"relative\"\n    };\n    const color = computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.font) == null ? void 0 : _a.color) != null ? _b : \"rgba(0,0,0,.15)\";\n    });\n    const fontSize = computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.font) == null ? void 0 : _a.fontSize) != null ? _b : 16;\n    });\n    const fontWeight = computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.font) == null ? void 0 : _a.fontWeight) != null ? _b : \"normal\";\n    });\n    const fontStyle = computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.font) == null ? void 0 : _a.fontStyle) != null ? _b : \"normal\";\n    });\n    const fontFamily = computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.font) == null ? void 0 : _a.fontFamily) != null ? _b : \"sans-serif\";\n    });\n    const textAlign = computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.font) == null ? void 0 : _a.textAlign) != null ? _b : \"center\";\n    });\n    const textBaseline = computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.font) == null ? void 0 : _a.textBaseline) != null ? _b : \"hanging\";\n    });\n    const gapX = computed(() => props.gap[0]);\n    const gapY = computed(() => props.gap[1]);\n    const gapXCenter = computed(() => gapX.value / 2);\n    const gapYCenter = computed(() => gapY.value / 2);\n    const offsetLeft = computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.offset) == null ? void 0 : _a[0]) != null ? _b : gapXCenter.value;\n    });\n    const offsetTop = computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.offset) == null ? void 0 : _a[1]) != null ? _b : gapYCenter.value;\n    });\n    const getMarkStyle = () => {\n      const markStyle = {\n        zIndex: props.zIndex,\n        position: \"absolute\",\n        left: 0,\n        top: 0,\n        width: \"100%\",\n        height: \"100%\",\n        pointerEvents: \"none\",\n        backgroundRepeat: \"repeat\"\n      };\n      let positionLeft = offsetLeft.value - gapXCenter.value;\n      let positionTop = offsetTop.value - gapYCenter.value;\n      if (positionLeft > 0) {\n        markStyle.left = `${positionLeft}px`;\n        markStyle.width = `calc(100% - ${positionLeft}px)`;\n        positionLeft = 0;\n      }\n      if (positionTop > 0) {\n        markStyle.top = `${positionTop}px`;\n        markStyle.height = `calc(100% - ${positionTop}px)`;\n        positionTop = 0;\n      }\n      markStyle.backgroundPosition = `${positionLeft}px ${positionTop}px`;\n      return markStyle;\n    };\n    const containerRef = shallowRef(null);\n    const watermarkRef = shallowRef();\n    const stopObservation = ref(false);\n    const destroyWatermark = () => {\n      if (watermarkRef.value) {\n        watermarkRef.value.remove();\n        watermarkRef.value = void 0;\n      }\n    };\n    const appendWatermark = (base64Url, markWidth) => {\n      var _a;\n      if (containerRef.value && watermarkRef.value) {\n        stopObservation.value = true;\n        watermarkRef.value.setAttribute(\"style\", getStyleStr({\n          ...getMarkStyle(),\n          backgroundImage: `url('${base64Url}')`,\n          backgroundSize: `${Math.floor(markWidth)}px`\n        }));\n        (_a = containerRef.value) == null ? void 0 : _a.append(watermarkRef.value);\n        setTimeout(() => {\n          stopObservation.value = false;\n        });\n      }\n    };\n    const getMarkSize = ctx => {\n      let defaultWidth = 120;\n      let defaultHeight = 64;\n      const {\n        image,\n        content,\n        width,\n        height,\n        rotate\n      } = props;\n      if (!image && ctx.measureText) {\n        ctx.font = `${Number(fontSize.value)}px ${fontFamily.value}`;\n        const contents = isArray(content) ? content : [content];\n        let maxWidth = 0;\n        let maxHeight = 0;\n        contents.forEach(item => {\n          const {\n            width: width2,\n            fontBoundingBoxAscent,\n            fontBoundingBoxDescent,\n            actualBoundingBoxAscent,\n            actualBoundingBoxDescent\n          } = ctx.measureText(item);\n          const height2 = isUndefined(fontBoundingBoxAscent) ? actualBoundingBoxAscent + actualBoundingBoxDescent : fontBoundingBoxAscent + fontBoundingBoxDescent;\n          if (width2 > maxWidth) maxWidth = Math.ceil(width2);\n          if (height2 > maxHeight) maxHeight = Math.ceil(height2);\n        });\n        defaultWidth = maxWidth;\n        defaultHeight = maxHeight * contents.length + (contents.length - 1) * FontGap;\n        const angle = Math.PI / 180 * Number(rotate);\n        const space = Math.ceil(Math.abs(Math.sin(angle) * defaultHeight) / 2);\n        defaultWidth += space;\n      }\n      return [width != null ? width : defaultWidth, height != null ? height : defaultHeight];\n    };\n    const getClips = useClips();\n    const renderWatermark = () => {\n      const canvas = document.createElement(\"canvas\");\n      const ctx = canvas.getContext(\"2d\");\n      const image = props.image;\n      const content = props.content;\n      const rotate = props.rotate;\n      if (ctx) {\n        if (!watermarkRef.value) {\n          watermarkRef.value = document.createElement(\"div\");\n        }\n        const ratio = getPixelRatio();\n        const [markWidth, markHeight] = getMarkSize(ctx);\n        const drawCanvas = drawContent => {\n          const [textClips, clipWidth] = getClips(drawContent || \"\", rotate, ratio, markWidth, markHeight, {\n            color: color.value,\n            fontSize: fontSize.value,\n            fontStyle: fontStyle.value,\n            fontWeight: fontWeight.value,\n            fontFamily: fontFamily.value,\n            textAlign: textAlign.value,\n            textBaseline: textBaseline.value\n          }, gapX.value, gapY.value);\n          appendWatermark(textClips, clipWidth);\n        };\n        if (image) {\n          const img = new Image();\n          img.onload = () => {\n            drawCanvas(img);\n          };\n          img.onerror = () => {\n            drawCanvas(content);\n          };\n          img.crossOrigin = \"anonymous\";\n          img.referrerPolicy = \"no-referrer\";\n          img.src = image;\n        } else {\n          drawCanvas(content);\n        }\n      }\n    };\n    onMounted(() => {\n      renderWatermark();\n    });\n    watch(() => props, () => {\n      renderWatermark();\n    }, {\n      deep: true,\n      flush: \"post\"\n    });\n    onBeforeUnmount(() => {\n      destroyWatermark();\n    });\n    const onMutate = mutations => {\n      if (stopObservation.value) {\n        return;\n      }\n      mutations.forEach(mutation => {\n        if (reRendering(mutation, watermarkRef.value)) {\n          destroyWatermark();\n          renderWatermark();\n        }\n      });\n    };\n    useMutationObserver(containerRef, onMutate, {\n      attributes: true,\n      subtree: true,\n      childList: true\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"containerRef\",\n        ref: containerRef,\n        style: normalizeStyle([style])\n      }, [renderSlot(_ctx.$slots, \"default\")], 4);\n    };\n  }\n});\nvar Watermark = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"watermark.vue\"]]);\nexport { Watermark as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}