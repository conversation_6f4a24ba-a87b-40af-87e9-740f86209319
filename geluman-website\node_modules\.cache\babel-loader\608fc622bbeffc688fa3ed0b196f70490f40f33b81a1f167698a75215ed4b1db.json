{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nconst stepsProps = buildProps({\n  space: {\n    type: [Number, String],\n    default: \"\"\n  },\n  active: {\n    type: Number,\n    default: 0\n  },\n  direction: {\n    type: String,\n    default: \"horizontal\",\n    values: [\"horizontal\", \"vertical\"]\n  },\n  alignCenter: {\n    type: Boolean\n  },\n  simple: {\n    type: Boolean\n  },\n  finishStatus: {\n    type: String,\n    values: [\"wait\", \"process\", \"finish\", \"error\", \"success\"],\n    default: \"finish\"\n  },\n  processStatus: {\n    type: String,\n    values: [\"wait\", \"process\", \"finish\", \"error\", \"success\"],\n    default: \"process\"\n  }\n});\nconst stepsEmits = {\n  [CHANGE_EVENT]: (newVal, oldVal) => [newVal, oldVal].every(isNumber)\n};\nexport { stepsEmits, stepsProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}