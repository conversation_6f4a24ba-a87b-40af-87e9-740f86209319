{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, inject, ref, watch, watchEffect, openBlock, createElementBlock, normalizeClass, createElementVNode, Fragment, renderList, normalizeStyle } from 'vue';\nimport { colorPickerContextKey } from '../color-picker.mjs';\nimport Color from '../utils/color.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nconst _sfc_main = defineComponent({\n  props: {\n    colors: {\n      type: Array,\n      required: true\n    },\n    color: {\n      type: Object,\n      required: true\n    },\n    enableAlpha: {\n      type: Boolean,\n      required: true\n    }\n  },\n  setup(props) {\n    const ns = useNamespace(\"color-predefine\");\n    const {\n      currentColor\n    } = inject(colorPickerContextKey);\n    const rgbaColors = ref(parseColors(props.colors, props.color));\n    watch(() => currentColor.value, val => {\n      const color = new Color();\n      color.fromString(val);\n      rgbaColors.value.forEach(item => {\n        item.selected = color.compare(item);\n      });\n    });\n    watchEffect(() => {\n      rgbaColors.value = parseColors(props.colors, props.color);\n    });\n    function handleSelect(index) {\n      props.color.fromString(props.colors[index]);\n    }\n    function parseColors(colors, color) {\n      return colors.map(value => {\n        const c = new Color();\n        c.enableAlpha = props.enableAlpha;\n        c.format = \"rgba\";\n        c.fromString(value);\n        c.selected = c.value === color.value;\n        return c;\n      });\n    }\n    return {\n      rgbaColors,\n      handleSelect,\n      ns\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"div\", {\n    class: normalizeClass(_ctx.ns.b())\n  }, [createElementVNode(\"div\", {\n    class: normalizeClass(_ctx.ns.e(\"colors\"))\n  }, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.rgbaColors, (item, index) => {\n    return openBlock(), createElementBlock(\"div\", {\n      key: _ctx.colors[index],\n      class: normalizeClass([_ctx.ns.e(\"color-selector\"), _ctx.ns.is(\"alpha\", item._alpha < 100), {\n        selected: item.selected\n      }]),\n      onClick: $event => _ctx.handleSelect(index)\n    }, [createElementVNode(\"div\", {\n      style: normalizeStyle({\n        backgroundColor: item.value\n      })\n    }, null, 4)], 10, [\"onClick\"]);\n  }), 128))], 2)], 2);\n}\nvar Predefine = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"predefine.vue\"]]);\nexport { Predefine as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}