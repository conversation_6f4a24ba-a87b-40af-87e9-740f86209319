{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst watermarkProps = buildProps({\n  zIndex: {\n    type: Number,\n    default: 9\n  },\n  rotate: {\n    type: Number,\n    default: -22\n  },\n  width: Number,\n  height: Number,\n  image: String,\n  content: {\n    type: definePropType([String, Array]),\n    default: \"Element Plus\"\n  },\n  font: {\n    type: definePropType(Object)\n  },\n  gap: {\n    type: definePropType(Array),\n    default: () => [100, 100]\n  },\n  offset: {\n    type: definePropType(Array)\n  }\n});\nexport { watermarkProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}