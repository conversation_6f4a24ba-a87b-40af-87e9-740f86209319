{"ast": null, "code": "import baseSlice from './_baseSlice.js';\n\n/**\n * Gets all but the last element of `array`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to query.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * _.initial([1, 2, 3]);\n * // => [1, 2]\n */\nfunction initial(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseSlice(array, 0, -1) : [];\n}\nexport default initial;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}