{"ast": null, "code": "import createMathOperation from './_createMathOperation.js';\n\n/**\n * Adds two numbers.\n *\n * @static\n * @memberOf _\n * @since 3.4.0\n * @category Math\n * @param {number} augend The first number in an addition.\n * @param {number} addend The second number in an addition.\n * @returns {number} Returns the total.\n * @example\n *\n * _.add(6, 4);\n * // => 10\n */\nvar add = createMathOperation(function (augend, addend) {\n  return augend + addend;\n}, 0);\nexport default add;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}