{"ast": null, "code": "import Tabs from './src/tabs.mjs';\nexport { tabsEmits, tabsProps } from './src/tabs.mjs';\nimport TabPane from './src/tab-pane2.mjs';\nexport { tabBarProps } from './src/tab-bar.mjs';\nexport { tabNavEmits, tabNavProps } from './src/tab-nav.mjs';\nexport { tabPaneProps } from './src/tab-pane.mjs';\nexport { tabsRootContextKey } from './src/constants.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElTabs = withInstall(Tabs, {\n  TabPane\n});\nconst ElTabPane = withNoopInstall(TabPane);\nexport { ElTabPane, ElTabs, ElTabs as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}