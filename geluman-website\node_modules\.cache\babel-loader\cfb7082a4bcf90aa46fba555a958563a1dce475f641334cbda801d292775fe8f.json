{"ast": null, "code": "import { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../../utils/vue/icon.mjs';\nconst paginationNextProps = buildProps({\n  disabled: Boolean,\n  currentPage: {\n    type: Number,\n    default: 1\n  },\n  pageCount: {\n    type: Number,\n    default: 50\n  },\n  nextText: {\n    type: String\n  },\n  nextIcon: {\n    type: iconPropType\n  }\n});\nexport { paginationNextProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}