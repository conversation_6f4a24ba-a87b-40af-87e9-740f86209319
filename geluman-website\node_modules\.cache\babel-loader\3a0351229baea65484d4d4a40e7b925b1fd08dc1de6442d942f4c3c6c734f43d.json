{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nconst carouselProps = buildProps({\n  initialIndex: {\n    type: Number,\n    default: 0\n  },\n  height: {\n    type: String,\n    default: \"\"\n  },\n  trigger: {\n    type: String,\n    values: [\"hover\", \"click\"],\n    default: \"hover\"\n  },\n  autoplay: {\n    type: Boolean,\n    default: true\n  },\n  interval: {\n    type: Number,\n    default: 3e3\n  },\n  indicatorPosition: {\n    type: String,\n    values: [\"\", \"none\", \"outside\"],\n    default: \"\"\n  },\n  arrow: {\n    type: String,\n    values: [\"always\", \"hover\", \"never\"],\n    default: \"hover\"\n  },\n  type: {\n    type: String,\n    values: [\"\", \"card\"],\n    default: \"\"\n  },\n  cardScale: {\n    type: Number,\n    default: 0.83\n  },\n  loop: {\n    type: Boolean,\n    default: true\n  },\n  direction: {\n    type: String,\n    values: [\"horizontal\", \"vertical\"],\n    default: \"horizontal\"\n  },\n  pauseOnHover: {\n    type: Boolean,\n    default: true\n  },\n  motionBlur: Boolean\n});\nconst carouselEmits = {\n  change: (current, prev) => [current, prev].every(isNumber)\n};\nexport { carouselEmits, carouselProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}