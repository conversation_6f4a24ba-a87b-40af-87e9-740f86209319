{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { ref, onMounted, watch, onBeforeUnmount, computed, unref, watchEffect } from 'vue';\nimport { offset, flip, shift, detectOverflow, arrow, computePosition, autoUpdate } from '@floating-ui/dom';\nimport { isArray, isString, isFunction } from '@vue/shared';\nimport { isClient } from '@vueuse/core';\nimport { keysOf } from '../../../utils/objects.mjs';\nconst useTarget = (target, open, gap, mergedMask, scrollIntoViewOptions) => {\n  const posInfo = ref(null);\n  const getTargetEl = () => {\n    let targetEl;\n    if (isString(target.value)) {\n      targetEl = document.querySelector(target.value);\n    } else if (isFunction(target.value)) {\n      targetEl = target.value();\n    } else {\n      targetEl = target.value;\n    }\n    return targetEl;\n  };\n  const updatePosInfo = () => {\n    const targetEl = getTargetEl();\n    if (!targetEl || !open.value) {\n      posInfo.value = null;\n      return;\n    }\n    if (!isInViewPort(targetEl)) {\n      targetEl.scrollIntoView(scrollIntoViewOptions.value);\n    }\n    const {\n      left,\n      top,\n      width,\n      height\n    } = targetEl.getBoundingClientRect();\n    posInfo.value = {\n      left,\n      top,\n      width,\n      height,\n      radius: 0\n    };\n  };\n  onMounted(() => {\n    watch([open, target], () => {\n      updatePosInfo();\n    }, {\n      immediate: true\n    });\n    window.addEventListener(\"resize\", updatePosInfo);\n  });\n  onBeforeUnmount(() => {\n    window.removeEventListener(\"resize\", updatePosInfo);\n  });\n  const getGapOffset = index => {\n    var _a;\n    return (_a = isArray(gap.value.offset) ? gap.value.offset[index] : gap.value.offset) != null ? _a : 6;\n  };\n  const mergedPosInfo = computed(() => {\n    var _a;\n    if (!posInfo.value) return posInfo.value;\n    const gapOffsetX = getGapOffset(0);\n    const gapOffsetY = getGapOffset(1);\n    const gapRadius = ((_a = gap.value) == null ? void 0 : _a.radius) || 2;\n    return {\n      left: posInfo.value.left - gapOffsetX,\n      top: posInfo.value.top - gapOffsetY,\n      width: posInfo.value.width + gapOffsetX * 2,\n      height: posInfo.value.height + gapOffsetY * 2,\n      radius: gapRadius\n    };\n  });\n  const triggerTarget = computed(() => {\n    const targetEl = getTargetEl();\n    if (!mergedMask.value || !targetEl || !window.DOMRect) {\n      return targetEl || void 0;\n    }\n    return {\n      getBoundingClientRect() {\n        var _a, _b, _c, _d;\n        return window.DOMRect.fromRect({\n          width: ((_a = mergedPosInfo.value) == null ? void 0 : _a.width) || 0,\n          height: ((_b = mergedPosInfo.value) == null ? void 0 : _b.height) || 0,\n          x: ((_c = mergedPosInfo.value) == null ? void 0 : _c.left) || 0,\n          y: ((_d = mergedPosInfo.value) == null ? void 0 : _d.top) || 0\n        });\n      }\n    };\n  });\n  return {\n    mergedPosInfo,\n    triggerTarget\n  };\n};\nconst tourKey = Symbol(\"ElTour\");\nfunction isInViewPort(element) {\n  const viewWidth = window.innerWidth || document.documentElement.clientWidth;\n  const viewHeight = window.innerHeight || document.documentElement.clientHeight;\n  const {\n    top,\n    right,\n    bottom,\n    left\n  } = element.getBoundingClientRect();\n  return top >= 0 && left >= 0 && right <= viewWidth && bottom <= viewHeight;\n}\nconst useFloating = (referenceRef, contentRef, arrowRef, placement, strategy, offset$1, zIndex, showArrow) => {\n  const x = ref();\n  const y = ref();\n  const middlewareData = ref({});\n  const states = {\n    x,\n    y,\n    placement,\n    strategy,\n    middlewareData\n  };\n  const middleware = computed(() => {\n    const _middleware = [offset(unref(offset$1)), flip(), shift(), overflowMiddleware()];\n    if (unref(showArrow) && unref(arrowRef)) {\n      _middleware.push(arrow({\n        element: unref(arrowRef)\n      }));\n    }\n    return _middleware;\n  });\n  const update = async () => {\n    if (!isClient) return;\n    const referenceEl = unref(referenceRef);\n    const contentEl = unref(contentRef);\n    if (!referenceEl || !contentEl) return;\n    const data = await computePosition(referenceEl, contentEl, {\n      placement: unref(placement),\n      strategy: unref(strategy),\n      middleware: unref(middleware)\n    });\n    keysOf(states).forEach(key => {\n      states[key].value = data[key];\n    });\n  };\n  const contentStyle = computed(() => {\n    if (!unref(referenceRef)) {\n      return {\n        position: \"fixed\",\n        top: \"50%\",\n        left: \"50%\",\n        transform: \"translate3d(-50%, -50%, 0)\",\n        maxWidth: \"100vw\",\n        zIndex: unref(zIndex)\n      };\n    }\n    const {\n      overflow\n    } = unref(middlewareData);\n    return {\n      position: unref(strategy),\n      zIndex: unref(zIndex),\n      top: unref(y) != null ? `${unref(y)}px` : \"\",\n      left: unref(x) != null ? `${unref(x)}px` : \"\",\n      maxWidth: (overflow == null ? void 0 : overflow.maxWidth) ? `${overflow == null ? void 0 : overflow.maxWidth}px` : \"\"\n    };\n  });\n  const arrowStyle = computed(() => {\n    if (!unref(showArrow)) return {};\n    const {\n      arrow: arrow2\n    } = unref(middlewareData);\n    return {\n      left: (arrow2 == null ? void 0 : arrow2.x) != null ? `${arrow2 == null ? void 0 : arrow2.x}px` : \"\",\n      top: (arrow2 == null ? void 0 : arrow2.y) != null ? `${arrow2 == null ? void 0 : arrow2.y}px` : \"\"\n    };\n  });\n  let cleanup;\n  onMounted(() => {\n    const referenceEl = unref(referenceRef);\n    const contentEl = unref(contentRef);\n    if (referenceEl && contentEl) {\n      cleanup = autoUpdate(referenceEl, contentEl, update);\n    }\n    watchEffect(() => {\n      update();\n    });\n  });\n  onBeforeUnmount(() => {\n    cleanup && cleanup();\n  });\n  return {\n    update,\n    contentStyle,\n    arrowStyle\n  };\n};\nconst overflowMiddleware = () => {\n  return {\n    name: \"overflow\",\n    async fn(state) {\n      const overflow = await detectOverflow(state);\n      let overWidth = 0;\n      if (overflow.left > 0) overWidth = overflow.left;\n      if (overflow.right > 0) overWidth = overflow.right;\n      const floatingWidth = state.rects.floating.width;\n      return {\n        data: {\n          maxWidth: floatingWidth - overWidth\n        }\n      };\n    }\n  };\n};\nexport { tourKey, useFloating, useTarget };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}