{"ast": null, "code": "import { timePanelSharedProps } from './shared.mjs';\nimport { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nconst panelTimePickerProps = buildProps({\n  ...timePanelSharedProps,\n  datetimeRole: String,\n  parsedValue: {\n    type: definePropType(Object)\n  }\n});\nexport { panelTimePickerProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}