{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { isVNode, defineComponent, renderSlot, createVNode, createTextVNode } from 'vue';\nimport SpaceItem from './item.mjs';\nimport { useSpace } from './use-space.mjs';\nimport { PatchFlags, isFragment, isValidElementNode } from '../../../utils/vue/vnode.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nimport { isString, isArray } from '@vue/shared';\nimport { componentSizes } from '../../../constants/size.mjs';\nconst spaceProps = buildProps({\n  direction: {\n    type: String,\n    values: [\"horizontal\", \"vertical\"],\n    default: \"horizontal\"\n  },\n  class: {\n    type: definePropType([String, Object, Array]),\n    default: \"\"\n  },\n  style: {\n    type: definePropType([String, Array, Object]),\n    default: \"\"\n  },\n  alignment: {\n    type: definePropType(String),\n    default: \"center\"\n  },\n  prefixCls: {\n    type: String\n  },\n  spacer: {\n    type: definePropType([Object, String, Number, Array]),\n    default: null,\n    validator: val => isVNode(val) || isNumber(val) || isString(val)\n  },\n  wrap: Boolean,\n  fill: Boolean,\n  fillRatio: {\n    type: Number,\n    default: 100\n  },\n  size: {\n    type: [String, Array, Number],\n    values: componentSizes,\n    validator: val => {\n      return isNumber(val) || isArray(val) && val.length === 2 && val.every(isNumber);\n    }\n  }\n});\nconst Space = defineComponent({\n  name: \"ElSpace\",\n  props: spaceProps,\n  setup(props, {\n    slots\n  }) {\n    const {\n      classes,\n      containerStyle,\n      itemStyle\n    } = useSpace(props);\n    function extractChildren(children, parentKey = \"\", extractedChildren = []) {\n      const {\n        prefixCls\n      } = props;\n      children.forEach((child, loopKey) => {\n        if (isFragment(child)) {\n          if (isArray(child.children)) {\n            child.children.forEach((nested, key) => {\n              if (isFragment(nested) && isArray(nested.children)) {\n                extractChildren(nested.children, `${parentKey + key}-`, extractedChildren);\n              } else {\n                extractedChildren.push(createVNode(SpaceItem, {\n                  style: itemStyle.value,\n                  prefixCls,\n                  key: `nested-${parentKey + key}`\n                }, {\n                  default: () => [nested]\n                }, PatchFlags.PROPS | PatchFlags.STYLE, [\"style\", \"prefixCls\"]));\n              }\n            });\n          }\n        } else if (isValidElementNode(child)) {\n          extractedChildren.push(createVNode(SpaceItem, {\n            style: itemStyle.value,\n            prefixCls,\n            key: `LoopKey${parentKey + loopKey}`\n          }, {\n            default: () => [child]\n          }, PatchFlags.PROPS | PatchFlags.STYLE, [\"style\", \"prefixCls\"]));\n        }\n      });\n      return extractedChildren;\n    }\n    return () => {\n      var _a;\n      const {\n        spacer,\n        direction\n      } = props;\n      const children = renderSlot(slots, \"default\", {\n        key: 0\n      }, () => []);\n      if (((_a = children.children) != null ? _a : []).length === 0) return null;\n      if (isArray(children.children)) {\n        let extractedChildren = extractChildren(children.children);\n        if (spacer) {\n          const len = extractedChildren.length - 1;\n          extractedChildren = extractedChildren.reduce((acc, child, idx) => {\n            const children2 = [...acc, child];\n            if (idx !== len) {\n              children2.push(createVNode(\"span\", {\n                style: [itemStyle.value, direction === \"vertical\" ? \"width: 100%\" : null],\n                key: idx\n              }, [isVNode(spacer) ? spacer : createTextVNode(spacer, PatchFlags.TEXT)], PatchFlags.STYLE));\n            }\n            return children2;\n          }, []);\n        }\n        return createVNode(\"div\", {\n          class: classes.value,\n          style: containerStyle.value\n        }, extractedChildren, PatchFlags.STYLE | PatchFlags.CLASS);\n      }\n      return children.children;\n    };\n  }\n});\nexport { Space as default, spaceProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}