{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { usePropsAlias } from './use-props-alias.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../../constants/event.mjs';\nconst useMove = (props, checkedState, emit) => {\n  const propsAlias = usePropsAlias(props);\n  const _emit = (value, direction, movedKeys) => {\n    emit(UPDATE_MODEL_EVENT, value);\n    emit(CHANGE_EVENT, value, direction, movedKeys);\n  };\n  const addToLeft = () => {\n    const currentValue = props.modelValue.slice();\n    checkedState.rightChecked.forEach(item => {\n      const index = currentValue.indexOf(item);\n      if (index > -1) {\n        currentValue.splice(index, 1);\n      }\n    });\n    _emit(currentValue, \"left\", checkedState.rightChecked);\n  };\n  const addToRight = () => {\n    let currentValue = props.modelValue.slice();\n    const itemsToBeMoved = props.data.filter(item => {\n      const itemKey = item[propsAlias.value.key];\n      return checkedState.leftChecked.includes(itemKey) && !props.modelValue.includes(itemKey);\n    }).map(item => item[propsAlias.value.key]);\n    currentValue = props.targetOrder === \"unshift\" ? itemsToBeMoved.concat(currentValue) : currentValue.concat(itemsToBeMoved);\n    if (props.targetOrder === \"original\") {\n      currentValue = props.data.filter(item => currentValue.includes(item[propsAlias.value.key])).map(item => item[propsAlias.value.key]);\n    }\n    _emit(currentValue, \"right\", checkedState.leftChecked);\n  };\n  return {\n    addToLeft,\n    addToRight\n  };\n};\nexport { useMove };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}