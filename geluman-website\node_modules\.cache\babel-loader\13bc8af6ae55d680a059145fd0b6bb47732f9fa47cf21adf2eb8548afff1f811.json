{"ast": null, "code": "import { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { isString } from '@vue/shared';\nimport { isNumber, isBoolean } from '../../../utils/types.mjs';\nconst checkboxProps = {\n  modelValue: {\n    type: [Number, String, Boolean],\n    default: void 0\n  },\n  label: {\n    type: [String, Boolean, Number, Object],\n    default: void 0\n  },\n  value: {\n    type: [String, Boolean, Number, Object],\n    default: void 0\n  },\n  indeterminate: Boolean,\n  disabled: Boolean,\n  checked: Boolean,\n  name: {\n    type: String,\n    default: void 0\n  },\n  trueValue: {\n    type: [String, Number],\n    default: void 0\n  },\n  falseValue: {\n    type: [String, Number],\n    default: void 0\n  },\n  trueLabel: {\n    type: [String, Number],\n    default: void 0\n  },\n  falseLabel: {\n    type: [String, Number],\n    default: void 0\n  },\n  id: {\n    type: String,\n    default: void 0\n  },\n  border: Boolean,\n  size: useSizeProp,\n  tabindex: [String, Number],\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  ...useAriaProps([\"ariaControls\"])\n};\nconst checkboxEmits = {\n  [UPDATE_MODEL_EVENT]: val => isString(val) || isNumber(val) || isBoolean(val),\n  change: val => isString(val) || isNumber(val) || isBoolean(val)\n};\nexport { checkboxEmits, checkboxProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}