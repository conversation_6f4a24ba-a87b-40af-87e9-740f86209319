{"ast": null, "code": "require(\"core-js/modules/es.iterator.constructor.js\");\nrequire(\"core-js/modules/es.iterator.map.js\");\n!function (n, e) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = e() : \"function\" == typeof define && define.amd ? define(e) : (n = \"undefined\" != typeof globalThis ? globalThis : n || self).dayjs_plugin_localeData = e();\n}(this, function () {\n  \"use strict\";\n\n  return function (n, e, t) {\n    var r = e.prototype,\n      o = function (n) {\n        return n && (n.indexOf ? n : n.s);\n      },\n      u = function (n, e, t, r, u) {\n        var i = n.name ? n : n.$locale(),\n          a = o(i[e]),\n          s = o(i[t]),\n          f = a || s.map(function (n) {\n            return n.slice(0, r);\n          });\n        if (!u) return f;\n        var d = i.weekStart;\n        return f.map(function (n, e) {\n          return f[(e + (d || 0)) % 7];\n        });\n      },\n      i = function () {\n        return t.Ls[t.locale()];\n      },\n      a = function (n, e) {\n        return n.formats[e] || function (n) {\n          return n.replace(/(\\[[^\\]]+])|(MMMM|MM|DD|dddd)/g, function (n, e, t) {\n            return e || t.slice(1);\n          });\n        }(n.formats[e.toUpperCase()]);\n      },\n      s = function () {\n        var n = this;\n        return {\n          months: function (e) {\n            return e ? e.format(\"MMMM\") : u(n, \"months\");\n          },\n          monthsShort: function (e) {\n            return e ? e.format(\"MMM\") : u(n, \"monthsShort\", \"months\", 3);\n          },\n          firstDayOfWeek: function () {\n            return n.$locale().weekStart || 0;\n          },\n          weekdays: function (e) {\n            return e ? e.format(\"dddd\") : u(n, \"weekdays\");\n          },\n          weekdaysMin: function (e) {\n            return e ? e.format(\"dd\") : u(n, \"weekdaysMin\", \"weekdays\", 2);\n          },\n          weekdaysShort: function (e) {\n            return e ? e.format(\"ddd\") : u(n, \"weekdaysShort\", \"weekdays\", 3);\n          },\n          longDateFormat: function (e) {\n            return a(n.$locale(), e);\n          },\n          meridiem: this.$locale().meridiem,\n          ordinal: this.$locale().ordinal\n        };\n      };\n    r.localeData = function () {\n      return s.bind(this)();\n    }, t.localeData = function () {\n      var n = i();\n      return {\n        firstDayOfWeek: function () {\n          return n.weekStart || 0;\n        },\n        weekdays: function () {\n          return t.weekdays();\n        },\n        weekdaysShort: function () {\n          return t.weekdaysShort();\n        },\n        weekdaysMin: function () {\n          return t.weekdaysMin();\n        },\n        months: function () {\n          return t.months();\n        },\n        monthsShort: function () {\n          return t.monthsShort();\n        },\n        longDateFormat: function (e) {\n          return a(n, e);\n        },\n        meridiem: n.meridiem,\n        ordinal: n.ordinal\n      };\n    }, t.months = function () {\n      return u(i(), \"months\");\n    }, t.monthsShort = function () {\n      return u(i(), \"monthsShort\", \"months\", 3);\n    }, t.weekdays = function (n) {\n      return u(i(), \"weekdays\", null, null, n);\n    }, t.weekdaysShort = function (n) {\n      return u(i(), \"weekdaysShort\", \"weekdays\", 3, n);\n    }, t.weekdaysMin = function (n) {\n      return u(i(), \"weekdaysMin\", \"weekdays\", 2, n);\n    };\n  };\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}