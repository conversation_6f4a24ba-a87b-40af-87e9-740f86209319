{"ast": null, "code": "import { defineComponent, ref, toRef, computed, provide, readonly, unref, watch, onDeactivated, openBlock, createBlock, withCtx, createVNode, renderSlot, createCommentVNode, createElementBlock, toDisplayString } from 'vue';\nimport { ElPopper } from '../../popper/index.mjs';\nimport { TOOLTIP_INJECTION_KEY } from './constants.mjs';\nimport { useTooltipProps, tooltipEmits, useTooltipModelToggle } from './tooltip.mjs';\nimport ElTooltipTrigger from './trigger2.mjs';\nimport ElTooltipContent from './content2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { usePopperContainer } from '../../../hooks/use-popper-container/index.mjs';\nimport { useDelayedToggle } from '../../../hooks/use-delayed-toggle/index.mjs';\nimport ElP<PERSON><PERSON>Arrow from '../../popper/src/arrow2.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { isBoolean } from '../../../utils/types.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTooltip\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: useTooltipProps,\n  emits: tooltipEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    usePopperContainer();\n    const ns = useNamespace(\"tooltip\");\n    const id = useId();\n    const popperRef = ref();\n    const contentRef = ref();\n    const updatePopper = () => {\n      var _a;\n      const popperComponent = unref(popperRef);\n      if (popperComponent) {\n        (_a = popperComponent.popperInstanceRef) == null ? void 0 : _a.update();\n      }\n    };\n    const open = ref(false);\n    const toggleReason = ref();\n    const {\n      show,\n      hide,\n      hasUpdateHandler\n    } = useTooltipModelToggle({\n      indicator: open,\n      toggleReason\n    });\n    const {\n      onOpen,\n      onClose\n    } = useDelayedToggle({\n      showAfter: toRef(props, \"showAfter\"),\n      hideAfter: toRef(props, \"hideAfter\"),\n      autoClose: toRef(props, \"autoClose\"),\n      open: show,\n      close: hide\n    });\n    const controlled = computed(() => isBoolean(props.visible) && !hasUpdateHandler.value);\n    const kls = computed(() => {\n      return [ns.b(), props.popperClass];\n    });\n    provide(TOOLTIP_INJECTION_KEY, {\n      controlled,\n      id,\n      open: readonly(open),\n      trigger: toRef(props, \"trigger\"),\n      onOpen: event => {\n        onOpen(event);\n      },\n      onClose: event => {\n        onClose(event);\n      },\n      onToggle: event => {\n        if (unref(open)) {\n          onClose(event);\n        } else {\n          onOpen(event);\n        }\n      },\n      onShow: () => {\n        emit(\"show\", toggleReason.value);\n      },\n      onHide: () => {\n        emit(\"hide\", toggleReason.value);\n      },\n      onBeforeShow: () => {\n        emit(\"before-show\", toggleReason.value);\n      },\n      onBeforeHide: () => {\n        emit(\"before-hide\", toggleReason.value);\n      },\n      updatePopper\n    });\n    watch(() => props.disabled, disabled => {\n      if (disabled && open.value) {\n        open.value = false;\n      }\n    });\n    const isFocusInsideContent = event => {\n      var _a;\n      return (_a = contentRef.value) == null ? void 0 : _a.isFocusInsideContent(event);\n    };\n    onDeactivated(() => open.value && hide());\n    expose({\n      popperRef,\n      contentRef,\n      isFocusInsideContent,\n      updatePopper,\n      onOpen,\n      onClose,\n      hide\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElPopper), {\n        ref_key: \"popperRef\",\n        ref: popperRef,\n        role: _ctx.role\n      }, {\n        default: withCtx(() => [createVNode(ElTooltipTrigger, {\n          disabled: _ctx.disabled,\n          trigger: _ctx.trigger,\n          \"trigger-keys\": _ctx.triggerKeys,\n          \"virtual-ref\": _ctx.virtualRef,\n          \"virtual-triggering\": _ctx.virtualTriggering\n        }, {\n          default: withCtx(() => [_ctx.$slots.default ? renderSlot(_ctx.$slots, \"default\", {\n            key: 0\n          }) : createCommentVNode(\"v-if\", true)]),\n          _: 3\n        }, 8, [\"disabled\", \"trigger\", \"trigger-keys\", \"virtual-ref\", \"virtual-triggering\"]), createVNode(ElTooltipContent, {\n          ref_key: \"contentRef\",\n          ref: contentRef,\n          \"aria-label\": _ctx.ariaLabel,\n          \"boundaries-padding\": _ctx.boundariesPadding,\n          content: _ctx.content,\n          disabled: _ctx.disabled,\n          effect: _ctx.effect,\n          enterable: _ctx.enterable,\n          \"fallback-placements\": _ctx.fallbackPlacements,\n          \"hide-after\": _ctx.hideAfter,\n          \"gpu-acceleration\": _ctx.gpuAcceleration,\n          offset: _ctx.offset,\n          persistent: _ctx.persistent,\n          \"popper-class\": unref(kls),\n          \"popper-style\": _ctx.popperStyle,\n          placement: _ctx.placement,\n          \"popper-options\": _ctx.popperOptions,\n          pure: _ctx.pure,\n          \"raw-content\": _ctx.rawContent,\n          \"reference-el\": _ctx.referenceEl,\n          \"trigger-target-el\": _ctx.triggerTargetEl,\n          \"show-after\": _ctx.showAfter,\n          strategy: _ctx.strategy,\n          teleported: _ctx.teleported,\n          transition: _ctx.transition,\n          \"virtual-triggering\": _ctx.virtualTriggering,\n          \"z-index\": _ctx.zIndex,\n          \"append-to\": _ctx.appendTo\n        }, {\n          default: withCtx(() => [renderSlot(_ctx.$slots, \"content\", {}, () => [_ctx.rawContent ? (openBlock(), createElementBlock(\"span\", {\n            key: 0,\n            innerHTML: _ctx.content\n          }, null, 8, [\"innerHTML\"])) : (openBlock(), createElementBlock(\"span\", {\n            key: 1\n          }, toDisplayString(_ctx.content), 1))]), _ctx.showArrow ? (openBlock(), createBlock(unref(ElPopperArrow), {\n            key: 0,\n            \"arrow-offset\": _ctx.arrowOffset\n          }, null, 8, [\"arrow-offset\"])) : createCommentVNode(\"v-if\", true)]),\n          _: 3\n        }, 8, [\"aria-label\", \"boundaries-padding\", \"content\", \"disabled\", \"effect\", \"enterable\", \"fallback-placements\", \"hide-after\", \"gpu-acceleration\", \"offset\", \"persistent\", \"popper-class\", \"popper-style\", \"placement\", \"popper-options\", \"pure\", \"raw-content\", \"reference-el\", \"trigger-target-el\", \"show-after\", \"strategy\", \"teleported\", \"transition\", \"virtual-triggering\", \"z-index\", \"append-to\"])]),\n        _: 3\n      }, 8, [\"role\"]);\n    };\n  }\n});\nvar Tooltip = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tooltip.vue\"]]);\nexport { Tooltip as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}