{"ast": null, "code": "import lodash from './wrapperLodash.js';\n\n/**\n * Creates a `lodash` wrapper instance that wraps `value` with explicit method\n * chain sequences enabled. The result of such sequences must be unwrapped\n * with `_#value`.\n *\n * @static\n * @memberOf _\n * @since 1.3.0\n * @category Seq\n * @param {*} value The value to wrap.\n * @returns {Object} Returns the new `lodash` wrapper instance.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'age': 36 },\n *   { 'user': 'fred',    'age': 40 },\n *   { 'user': 'pebbles', 'age': 1 }\n * ];\n *\n * var youngest = _\n *   .chain(users)\n *   .sortBy('age')\n *   .map(function(o) {\n *     return o.user + ' is ' + o.age;\n *   })\n *   .head()\n *   .value();\n * // => 'pebbles is 1'\n */\nfunction chain(value) {\n  var result = lodash(value);\n  result.__chain__ = true;\n  return result;\n}\nexport default chain;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}