{"ast": null, "code": "import { defineComponent, inject, ref, onMounted, onBeforeUnmount, onUpdated, watch, computed, createVNode, Fragment, nextTick } from 'vue';\nimport { useResizeObserver } from '@vueuse/core';\nimport { formContextKey, formItemContextKey } from './constants.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst COMPONENT_NAME = \"ElLabelWrap\";\nvar FormLabelWrap = defineComponent({\n  name: COMPONENT_NAME,\n  props: {\n    isAutoWidth: Boolean,\n    updateAll: Boolean\n  },\n  setup(props, {\n    slots\n  }) {\n    const formContext = inject(formContextKey, void 0);\n    const formItemContext = inject(formItemContextKey);\n    if (!formItemContext) throwError(COMPONENT_NAME, \"usage: <el-form-item><label-wrap /></el-form-item>\");\n    const ns = useNamespace(\"form\");\n    const el = ref();\n    const computedWidth = ref(0);\n    const getLabelWidth = () => {\n      var _a;\n      if ((_a = el.value) == null ? void 0 : _a.firstElementChild) {\n        const width = window.getComputedStyle(el.value.firstElementChild).width;\n        return Math.ceil(Number.parseFloat(width));\n      } else {\n        return 0;\n      }\n    };\n    const updateLabelWidth = (action = \"update\") => {\n      nextTick(() => {\n        if (slots.default && props.isAutoWidth) {\n          if (action === \"update\") {\n            computedWidth.value = getLabelWidth();\n          } else if (action === \"remove\") {\n            formContext == null ? void 0 : formContext.deregisterLabelWidth(computedWidth.value);\n          }\n        }\n      });\n    };\n    const updateLabelWidthFn = () => updateLabelWidth(\"update\");\n    onMounted(() => {\n      updateLabelWidthFn();\n    });\n    onBeforeUnmount(() => {\n      updateLabelWidth(\"remove\");\n    });\n    onUpdated(() => updateLabelWidthFn());\n    watch(computedWidth, (val, oldVal) => {\n      if (props.updateAll) {\n        formContext == null ? void 0 : formContext.registerLabelWidth(val, oldVal);\n      }\n    });\n    useResizeObserver(computed(() => {\n      var _a, _b;\n      return (_b = (_a = el.value) == null ? void 0 : _a.firstElementChild) != null ? _b : null;\n    }), updateLabelWidthFn);\n    return () => {\n      var _a, _b;\n      if (!slots) return null;\n      const {\n        isAutoWidth\n      } = props;\n      if (isAutoWidth) {\n        const autoLabelWidth = formContext == null ? void 0 : formContext.autoLabelWidth;\n        const hasLabel = formItemContext == null ? void 0 : formItemContext.hasLabel;\n        const style = {};\n        if (hasLabel && autoLabelWidth && autoLabelWidth !== \"auto\") {\n          const marginWidth = Math.max(0, Number.parseInt(autoLabelWidth, 10) - computedWidth.value);\n          const labelPosition = formItemContext.labelPosition || formContext.labelPosition;\n          const marginPosition = labelPosition === \"left\" ? \"marginRight\" : \"marginLeft\";\n          if (marginWidth) {\n            style[marginPosition] = `${marginWidth}px`;\n          }\n        }\n        return createVNode(\"div\", {\n          \"ref\": el,\n          \"class\": [ns.be(\"item\", \"label-wrap\")],\n          \"style\": style\n        }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n      } else {\n        return createVNode(Fragment, {\n          \"ref\": el\n        }, [(_b = slots.default) == null ? void 0 : _b.call(slots)]);\n      }\n    };\n  }\n});\nexport { FormLabelWrap as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}