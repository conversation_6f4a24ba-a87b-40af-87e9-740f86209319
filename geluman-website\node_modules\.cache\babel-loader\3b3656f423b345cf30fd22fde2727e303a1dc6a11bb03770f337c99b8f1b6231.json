{"ast": null, "code": "import Breadcrumb from './src/breadcrumb2.mjs';\nimport BreadcrumbItem from './src/breadcrumb-item2.mjs';\nexport { breadcrumbProps } from './src/breadcrumb.mjs';\nexport { breadcrumbItemProps } from './src/breadcrumb-item.mjs';\nexport { breadcrumbKey } from './src/constants.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElBreadcrumb = withInstall(Breadcrumb, {\n  BreadcrumbItem\n});\nconst ElBreadcrumbItem = withNoopInstall(BreadcrumbItem);\nexport { ElBreadcrumb, ElBreadcrumbItem, ElBreadcrumb as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}