{"ast": null, "code": "import { unref as _unref, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"nav-container\"\n};\nconst _hoisted_2 = [\"src\"];\nconst _hoisted_3 = {\n  class: \"nav-menu\"\n};\nimport { useI18n } from \"vue-i18n\";\nimport { Grid, InfoFilled } from \"@element-plus/icons-vue\";\nimport { logo } from \"@/assets\";\nexport default {\n  __name: 'NavHeader',\n  setup(__props) {\n    const {\n      t\n    } = useI18n();\n    return (_ctx, _cache) => {\n      const _component_router_link = _resolveComponent(\"router-link\");\n      const _component_el_icon = _resolveComponent(\"el-icon\");\n      return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_router_link, {\n        to: \"/\",\n        class: \"logo-link\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"img\", {\n          src: _unref(logo),\n          alt: \"格鲁曼\",\n          class: \"logo\"\n        }, null, 8, _hoisted_2)]),\n        _: 1\n      }), _createElementVNode(\"nav\", _hoisted_3, [_createVNode(_component_router_link, {\n        to: \"/products\",\n        class: \"nav-item\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_unref(Grid))]),\n          _: 1\n        }), _createTextVNode(\" \" + _toDisplayString(_unref(t)(\"nav.products\")) + \" \", 1), _cache[0] || (_cache[0] = _createElementVNode(\"span\", {\n          class: \"nav-background\"\n        }, null, -1))]),\n        _: 1\n      }), _createVNode(_component_router_link, {\n        to: \"/about\",\n        class: \"nav-item\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_unref(InfoFilled))]),\n          _: 1\n        }), _createTextVNode(\" \" + _toDisplayString(_unref(t)(\"nav.about\")) + \" \", 1), _cache[1] || (_cache[1] = _createElementVNode(\"span\", {\n          class: \"nav-background\"\n        }, null, -1))]),\n        _: 1\n      })])]);\n    };\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}