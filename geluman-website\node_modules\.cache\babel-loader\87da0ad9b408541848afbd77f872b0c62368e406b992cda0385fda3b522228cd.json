{"ast": null, "code": "import { ref, provide, inject, onMounted, unref, onBeforeUnmount } from 'vue';\nimport Collection from './collection.mjs';\nimport CollectionItem from './collection-item.mjs';\nconst COLLECTION_ITEM_SIGN = `data-el-collection-item`;\nconst createCollectionWithScope = name => {\n  const COLLECTION_NAME = `El${name}Collection`;\n  const COLLECTION_ITEM_NAME = `${COLLECTION_NAME}Item`;\n  const COLLECTION_INJECTION_KEY = Symbol(COLLECTION_NAME);\n  const COLLECTION_ITEM_INJECTION_KEY = Symbol(COLLECTION_ITEM_NAME);\n  const ElCollection = {\n    ...Collection,\n    name: COLLECTION_NAME,\n    setup() {\n      const collectionRef = ref();\n      const itemMap = /* @__PURE__ */new Map();\n      const getItems = () => {\n        const collectionEl = unref(collectionRef);\n        if (!collectionEl) return [];\n        const orderedNodes = Array.from(collectionEl.querySelectorAll(`[${COLLECTION_ITEM_SIGN}]`));\n        const items = [...itemMap.values()];\n        return items.sort((a, b) => orderedNodes.indexOf(a.ref) - orderedNodes.indexOf(b.ref));\n      };\n      provide(COLLECTION_INJECTION_KEY, {\n        itemMap,\n        getItems,\n        collectionRef\n      });\n    }\n  };\n  const ElCollectionItem = {\n    ...CollectionItem,\n    name: COLLECTION_ITEM_NAME,\n    setup(_, {\n      attrs\n    }) {\n      const collectionItemRef = ref();\n      const collectionInjection = inject(COLLECTION_INJECTION_KEY, void 0);\n      provide(COLLECTION_ITEM_INJECTION_KEY, {\n        collectionItemRef\n      });\n      onMounted(() => {\n        const collectionItemEl = unref(collectionItemRef);\n        if (collectionItemEl) {\n          collectionInjection.itemMap.set(collectionItemEl, {\n            ref: collectionItemEl,\n            ...attrs\n          });\n        }\n      });\n      onBeforeUnmount(() => {\n        const collectionItemEl = unref(collectionItemRef);\n        collectionInjection.itemMap.delete(collectionItemEl);\n      });\n    }\n  };\n  return {\n    COLLECTION_INJECTION_KEY,\n    COLLECTION_ITEM_INJECTION_KEY,\n    ElCollection,\n    ElCollectionItem\n  };\n};\nexport { COLLECTION_ITEM_SIGN, createCollectionWithScope };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}