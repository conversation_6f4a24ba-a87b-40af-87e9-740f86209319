{"ast": null, "code": "import Tour from './src/tour2.mjs';\nimport TourStep from './src/step2.mjs';\nexport { tourEmits, tourProps } from './src/tour.mjs';\nexport { tourStepEmits, tourStepProps } from './src/step.mjs';\nexport { tourContentEmits, tourContentProps, tourPlacements, tourStrategies } from './src/content2.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElTour = withInstall(Tour, {\n  TourStep\n});\nconst ElTourStep = withNoopInstall(TourStep);\nexport { ElTour, ElTourStep, ElTour as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}