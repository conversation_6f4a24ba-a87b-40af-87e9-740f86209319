{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst skeletonItemProps = buildProps({\n  variant: {\n    type: String,\n    values: [\"circle\", \"rect\", \"h1\", \"h3\", \"text\", \"caption\", \"p\", \"image\", \"button\"],\n    default: \"text\"\n  }\n});\nexport { skeletonItemProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}