{"ast": null, "code": "import { panelRangeSharedProps } from './shared.mjs';\nimport { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nconst panelYearRangeProps = buildProps({\n  ...panelRangeSharedProps\n});\nconst panelYearRangeEmits = [\"pick\", \"set-picker-option\", \"calendar-change\"];\nexport { panelYearRangeEmits, panelYearRangeProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}