{"ast": null, "code": "import { Loading } from '@element-plus/icons-vue';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nconst buttonTypes = [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"];\nconst buttonNativeTypes = [\"button\", \"submit\", \"reset\"];\nconst buttonProps = buildProps({\n  size: useSizeProp,\n  disabled: Boolean,\n  type: {\n    type: String,\n    values: buttonTypes,\n    default: \"\"\n  },\n  icon: {\n    type: iconPropType\n  },\n  nativeType: {\n    type: String,\n    values: buttonNativeTypes,\n    default: \"button\"\n  },\n  loading: Boolean,\n  loadingIcon: {\n    type: iconPropType,\n    default: () => Loading\n  },\n  plain: Boolean,\n  text: <PERSON><PERSON><PERSON>,\n  link: <PERSON><PERSON><PERSON>,\n  bg: <PERSON><PERSON><PERSON>,\n  autofocus: Boolean,\n  round: Boolean,\n  circle: <PERSON>olean,\n  color: String,\n  dark: Boolean,\n  autoInsertSpace: {\n    type: Boolean,\n    default: void 0\n  },\n  tag: {\n    type: definePropType([String, Object]),\n    default: \"button\"\n  }\n});\nconst buttonEmits = {\n  click: evt => evt instanceof MouseEvent\n};\nexport { buttonEmits, buttonNativeTypes, buttonProps, buttonTypes };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}