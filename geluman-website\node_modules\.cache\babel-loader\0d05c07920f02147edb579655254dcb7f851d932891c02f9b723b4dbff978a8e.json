{"ast": null, "code": "import { defineComponent, reactive, computed, toRefs, provide, openBlock, createElementBlock, unref, normalizeClass, createElementVNode, normalizeStyle, createVNode, createBlock, createCommentVNode, Fragment, renderList, withModifiers } from 'vue';\nimport { useEventListener } from '@vueuse/core';\nimport { ElInputNumber } from '../../input-number/index.mjs';\nimport { sliderContextKey } from './constants.mjs';\nimport { sliderProps, sliderEmits } from './slider.mjs';\nimport SliderButton from './button2.mjs';\nimport SliderMarker from './marker.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useSlide } from './composables/use-slide.mjs';\nimport { useStops } from './composables/use-stops.mjs';\nimport { useMarks } from './composables/use-marks.mjs';\nimport { useWatch } from './composables/use-watch.mjs';\nimport { useLifecycle } from './composables/use-lifecycle.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useFormItemInputId } from '../../form/src/hooks/use-form-item.mjs';\nimport { useFormSize } from '../../form/src/hooks/use-form-common-props.mjs';\nconst __default__ = defineComponent({\n  name: \"ElSlider\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: sliderProps,\n  emits: sliderEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"slider\");\n    const {\n      t\n    } = useLocale();\n    const initData = reactive({\n      firstValue: 0,\n      secondValue: 0,\n      oldValue: 0,\n      dragging: false,\n      sliderSize: 1\n    });\n    const {\n      elFormItem,\n      slider,\n      firstButton,\n      secondButton,\n      sliderDisabled,\n      minValue,\n      maxValue,\n      runwayStyle,\n      barStyle,\n      resetSize,\n      emitChange,\n      onSliderWrapperPrevent,\n      onSliderClick,\n      onSliderDown,\n      onSliderMarkerDown,\n      setFirstValue,\n      setSecondValue\n    } = useSlide(props, initData, emit);\n    const {\n      stops,\n      getStopStyle\n    } = useStops(props, initData, minValue, maxValue);\n    const {\n      inputId,\n      isLabeledByFormItem\n    } = useFormItemInputId(props, {\n      formItemContext: elFormItem\n    });\n    const sliderWrapperSize = useFormSize();\n    const sliderInputSize = computed(() => props.inputSize || sliderWrapperSize.value);\n    const groupLabel = computed(() => {\n      return props.ariaLabel || t(\"el.slider.defaultLabel\", {\n        min: props.min,\n        max: props.max\n      });\n    });\n    const firstButtonLabel = computed(() => {\n      if (props.range) {\n        return props.rangeStartLabel || t(\"el.slider.defaultRangeStartLabel\");\n      } else {\n        return groupLabel.value;\n      }\n    });\n    const firstValueText = computed(() => {\n      return props.formatValueText ? props.formatValueText(firstValue.value) : `${firstValue.value}`;\n    });\n    const secondButtonLabel = computed(() => {\n      return props.rangeEndLabel || t(\"el.slider.defaultRangeEndLabel\");\n    });\n    const secondValueText = computed(() => {\n      return props.formatValueText ? props.formatValueText(secondValue.value) : `${secondValue.value}`;\n    });\n    const sliderKls = computed(() => [ns.b(), ns.m(sliderWrapperSize.value), ns.is(\"vertical\", props.vertical), {\n      [ns.m(\"with-input\")]: props.showInput\n    }]);\n    const markList = useMarks(props);\n    useWatch(props, initData, minValue, maxValue, emit, elFormItem);\n    const precision = computed(() => {\n      const precisions = [props.min, props.max, props.step].map(item => {\n        const decimal = `${item}`.split(\".\")[1];\n        return decimal ? decimal.length : 0;\n      });\n      return Math.max.apply(null, precisions);\n    });\n    const {\n      sliderWrapper\n    } = useLifecycle(props, initData, resetSize);\n    const {\n      firstValue,\n      secondValue,\n      sliderSize\n    } = toRefs(initData);\n    const updateDragging = val => {\n      initData.dragging = val;\n    };\n    useEventListener(sliderWrapper, \"touchstart\", onSliderWrapperPrevent, {\n      passive: false\n    });\n    useEventListener(sliderWrapper, \"touchmove\", onSliderWrapperPrevent, {\n      passive: false\n    });\n    provide(sliderContextKey, {\n      ...toRefs(props),\n      sliderSize,\n      disabled: sliderDisabled,\n      precision,\n      emitChange,\n      resetSize,\n      updateDragging\n    });\n    expose({\n      onSliderClick\n    });\n    return (_ctx, _cache) => {\n      var _a, _b;\n      return openBlock(), createElementBlock(\"div\", {\n        id: _ctx.range ? unref(inputId) : void 0,\n        ref_key: \"sliderWrapper\",\n        ref: sliderWrapper,\n        class: normalizeClass(unref(sliderKls)),\n        role: _ctx.range ? \"group\" : void 0,\n        \"aria-label\": _ctx.range && !unref(isLabeledByFormItem) ? unref(groupLabel) : void 0,\n        \"aria-labelledby\": _ctx.range && unref(isLabeledByFormItem) ? (_a = unref(elFormItem)) == null ? void 0 : _a.labelId : void 0\n      }, [createElementVNode(\"div\", {\n        ref_key: \"slider\",\n        ref: slider,\n        class: normalizeClass([unref(ns).e(\"runway\"), {\n          \"show-input\": _ctx.showInput && !_ctx.range\n        }, unref(ns).is(\"disabled\", unref(sliderDisabled))]),\n        style: normalizeStyle(unref(runwayStyle)),\n        onMousedown: unref(onSliderDown),\n        onTouchstartPassive: unref(onSliderDown)\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"bar\")),\n        style: normalizeStyle(unref(barStyle))\n      }, null, 6), createVNode(SliderButton, {\n        id: !_ctx.range ? unref(inputId) : void 0,\n        ref_key: \"firstButton\",\n        ref: firstButton,\n        \"model-value\": unref(firstValue),\n        vertical: _ctx.vertical,\n        \"tooltip-class\": _ctx.tooltipClass,\n        placement: _ctx.placement,\n        role: \"slider\",\n        \"aria-label\": _ctx.range || !unref(isLabeledByFormItem) ? unref(firstButtonLabel) : void 0,\n        \"aria-labelledby\": !_ctx.range && unref(isLabeledByFormItem) ? (_b = unref(elFormItem)) == null ? void 0 : _b.labelId : void 0,\n        \"aria-valuemin\": _ctx.min,\n        \"aria-valuemax\": _ctx.range ? unref(secondValue) : _ctx.max,\n        \"aria-valuenow\": unref(firstValue),\n        \"aria-valuetext\": unref(firstValueText),\n        \"aria-orientation\": _ctx.vertical ? \"vertical\" : \"horizontal\",\n        \"aria-disabled\": unref(sliderDisabled),\n        \"onUpdate:modelValue\": unref(setFirstValue)\n      }, null, 8, [\"id\", \"model-value\", \"vertical\", \"tooltip-class\", \"placement\", \"aria-label\", \"aria-labelledby\", \"aria-valuemin\", \"aria-valuemax\", \"aria-valuenow\", \"aria-valuetext\", \"aria-orientation\", \"aria-disabled\", \"onUpdate:modelValue\"]), _ctx.range ? (openBlock(), createBlock(SliderButton, {\n        key: 0,\n        ref_key: \"secondButton\",\n        ref: secondButton,\n        \"model-value\": unref(secondValue),\n        vertical: _ctx.vertical,\n        \"tooltip-class\": _ctx.tooltipClass,\n        placement: _ctx.placement,\n        role: \"slider\",\n        \"aria-label\": unref(secondButtonLabel),\n        \"aria-valuemin\": unref(firstValue),\n        \"aria-valuemax\": _ctx.max,\n        \"aria-valuenow\": unref(secondValue),\n        \"aria-valuetext\": unref(secondValueText),\n        \"aria-orientation\": _ctx.vertical ? \"vertical\" : \"horizontal\",\n        \"aria-disabled\": unref(sliderDisabled),\n        \"onUpdate:modelValue\": unref(setSecondValue)\n      }, null, 8, [\"model-value\", \"vertical\", \"tooltip-class\", \"placement\", \"aria-label\", \"aria-valuemin\", \"aria-valuemax\", \"aria-valuenow\", \"aria-valuetext\", \"aria-orientation\", \"aria-disabled\", \"onUpdate:modelValue\"])) : createCommentVNode(\"v-if\", true), _ctx.showStops ? (openBlock(), createElementBlock(\"div\", {\n        key: 1\n      }, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(stops), (item, key) => {\n        return openBlock(), createElementBlock(\"div\", {\n          key,\n          class: normalizeClass(unref(ns).e(\"stop\")),\n          style: normalizeStyle(unref(getStopStyle)(item))\n        }, null, 6);\n      }), 128))])) : createCommentVNode(\"v-if\", true), unref(markList).length > 0 ? (openBlock(), createElementBlock(Fragment, {\n        key: 2\n      }, [createElementVNode(\"div\", null, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(markList), (item, key) => {\n        return openBlock(), createElementBlock(\"div\", {\n          key,\n          style: normalizeStyle(unref(getStopStyle)(item.position)),\n          class: normalizeClass([unref(ns).e(\"stop\"), unref(ns).e(\"marks-stop\")])\n        }, null, 6);\n      }), 128))]), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"marks\"))\n      }, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(markList), (item, key) => {\n        return openBlock(), createBlock(unref(SliderMarker), {\n          key,\n          mark: item.mark,\n          style: normalizeStyle(unref(getStopStyle)(item.position)),\n          onMousedown: withModifiers($event => unref(onSliderMarkerDown)(item.position), [\"stop\"])\n        }, null, 8, [\"mark\", \"style\", \"onMousedown\"]);\n      }), 128))], 2)], 64)) : createCommentVNode(\"v-if\", true)], 46, [\"onMousedown\", \"onTouchstartPassive\"]), _ctx.showInput && !_ctx.range ? (openBlock(), createBlock(unref(ElInputNumber), {\n        key: 0,\n        ref: \"input\",\n        \"model-value\": unref(firstValue),\n        class: normalizeClass(unref(ns).e(\"input\")),\n        step: _ctx.step,\n        disabled: unref(sliderDisabled),\n        controls: _ctx.showInputControls,\n        min: _ctx.min,\n        max: _ctx.max,\n        precision: unref(precision),\n        debounce: _ctx.debounce,\n        size: unref(sliderInputSize),\n        \"onUpdate:modelValue\": unref(setFirstValue),\n        onChange: unref(emitChange)\n      }, null, 8, [\"model-value\", \"class\", \"step\", \"disabled\", \"controls\", \"min\", \"max\", \"precision\", \"debounce\", \"size\", \"onUpdate:modelValue\", \"onChange\"])) : createCommentVNode(\"v-if\", true)], 10, [\"id\", \"role\", \"aria-label\", \"aria-labelledby\"]);\n    };\n  }\n});\nvar Slider = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"slider.vue\"]]);\nexport { Slider as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}