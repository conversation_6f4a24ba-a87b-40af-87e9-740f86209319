{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { computed, watch } from 'vue';\nimport { CHECKED_CHANGE_EVENT } from '../transfer-panel.mjs';\nimport { usePropsAlias } from './use-props-alias.mjs';\nimport { isFunction } from '@vue/shared';\nconst useCheck = (props, panelState, emit) => {\n  const propsAlias = usePropsAlias(props);\n  const filteredData = computed(() => {\n    return props.data.filter(item => {\n      if (isFunction(props.filterMethod)) {\n        return props.filterMethod(panelState.query, item);\n      } else {\n        const label = String(item[propsAlias.value.label] || item[propsAlias.value.key]);\n        return label.toLowerCase().includes(panelState.query.toLowerCase());\n      }\n    });\n  });\n  const checkableData = computed(() => filteredData.value.filter(item => !item[propsAlias.value.disabled]));\n  const checkedSummary = computed(() => {\n    const checkedLength = panelState.checked.length;\n    const dataLength = props.data.length;\n    const {\n      noChecked,\n      hasChecked\n    } = props.format;\n    if (noChecked && hasChecked) {\n      return checkedLength > 0 ? hasChecked.replace(/\\${checked}/g, checkedLength.toString()).replace(/\\${total}/g, dataLength.toString()) : noChecked.replace(/\\${total}/g, dataLength.toString());\n    } else {\n      return `${checkedLength}/${dataLength}`;\n    }\n  });\n  const isIndeterminate = computed(() => {\n    const checkedLength = panelState.checked.length;\n    return checkedLength > 0 && checkedLength < checkableData.value.length;\n  });\n  const updateAllChecked = () => {\n    const checkableDataKeys = checkableData.value.map(item => item[propsAlias.value.key]);\n    panelState.allChecked = checkableDataKeys.length > 0 && checkableDataKeys.every(item => panelState.checked.includes(item));\n  };\n  const handleAllCheckedChange = value => {\n    panelState.checked = value ? checkableData.value.map(item => item[propsAlias.value.key]) : [];\n  };\n  watch(() => panelState.checked, (val, oldVal) => {\n    updateAllChecked();\n    if (panelState.checkChangeByUser) {\n      const movedKeys = val.concat(oldVal).filter(v => !val.includes(v) || !oldVal.includes(v));\n      emit(CHECKED_CHANGE_EVENT, val, movedKeys);\n    } else {\n      emit(CHECKED_CHANGE_EVENT, val);\n      panelState.checkChangeByUser = true;\n    }\n  });\n  watch(checkableData, () => {\n    updateAllChecked();\n  });\n  watch(() => props.data, () => {\n    const checked = [];\n    const filteredDataKeys = filteredData.value.map(item => item[propsAlias.value.key]);\n    panelState.checked.forEach(item => {\n      if (filteredDataKeys.includes(item)) {\n        checked.push(item);\n      }\n    });\n    panelState.checkChangeByUser = false;\n    panelState.checked = checked;\n  });\n  watch(() => props.defaultChecked, (val, oldVal) => {\n    if (oldVal && val.length === oldVal.length && val.every(item => oldVal.includes(item))) return;\n    const checked = [];\n    const checkableDataKeys = checkableData.value.map(item => item[propsAlias.value.key]);\n    val.forEach(item => {\n      if (checkableDataKeys.includes(item)) {\n        checked.push(item);\n      }\n    });\n    panelState.checkChangeByUser = false;\n    panelState.checked = checked;\n  }, {\n    immediate: true\n  });\n  return {\n    filteredData,\n    checkableData,\n    checkedSummary,\n    isIndeterminate,\n    updateAllChecked,\n    handleAllCheckedChange\n  };\n};\nexport { useCheck };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}