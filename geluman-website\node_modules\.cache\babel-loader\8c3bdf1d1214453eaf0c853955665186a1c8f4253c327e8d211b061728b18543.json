{"ast": null, "code": "import createMathOperation from './_createMathOperation.js';\n\n/**\n * Divide two numbers.\n *\n * @static\n * @memberOf _\n * @since 4.7.0\n * @category Math\n * @param {number} dividend The first number in a division.\n * @param {number} divisor The second number in a division.\n * @returns {number} Returns the quotient.\n * @example\n *\n * _.divide(6, 4);\n * // => 1.5\n */\nvar divide = createMathOperation(function (dividend, divisor) {\n  return dividend / divisor;\n}, 1);\nexport default divide;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}