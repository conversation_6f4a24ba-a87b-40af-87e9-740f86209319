{"ast": null, "code": "import baseIsMatch from './_baseIsMatch.js';\nimport getMatchData from './_getMatchData.js';\n\n/**\n * This method is like `_.isMatch` except that it accepts `customizer` which\n * is invoked to compare values. If `customizer` returns `undefined`, comparisons\n * are handled by the method instead. The `customizer` is invoked with five\n * arguments: (objValue, srcValue, index|key, object, source).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {Object} object The object to inspect.\n * @param {Object} source The object of property values to match.\n * @param {Function} [customizer] The function to customize comparisons.\n * @returns {boolean} Returns `true` if `object` is a match, else `false`.\n * @example\n *\n * function isGreeting(value) {\n *   return /^h(?:i|ello)$/.test(value);\n * }\n *\n * function customizer(objValue, srcValue) {\n *   if (isGreeting(objValue) && isGreeting(srcValue)) {\n *     return true;\n *   }\n * }\n *\n * var object = { 'greeting': 'hello' };\n * var source = { 'greeting': 'hi' };\n *\n * _.isMatchWith(object, source, customizer);\n * // => true\n */\nfunction isMatchWith(object, source, customizer) {\n  customizer = typeof customizer == 'function' ? customizer : undefined;\n  return baseIsMatch(object, source, getMatchData(source), customizer);\n}\nexport default isMatchWith;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}