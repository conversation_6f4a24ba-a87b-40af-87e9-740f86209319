{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst badgeProps = buildProps({\n  value: {\n    type: [String, Number],\n    default: \"\"\n  },\n  max: {\n    type: Number,\n    default: 99\n  },\n  isDot: Boolean,\n  hidden: Boolean,\n  type: {\n    type: String,\n    values: [\"primary\", \"success\", \"warning\", \"info\", \"danger\"],\n    default: \"danger\"\n  },\n  showZero: {\n    type: Boolean,\n    default: true\n  },\n  color: String,\n  badgeStyle: {\n    type: definePropType([String, Object, Array])\n  },\n  offset: {\n    type: definePropType(Array),\n    default: [0, 0]\n  },\n  badgeClass: {\n    type: String\n  }\n});\nexport { badgeProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}