{"ast": null, "code": "import { unref } from 'vue';\nimport { useTimeout } from '../use-timeout/index.mjs';\nimport { buildProps } from '../../utils/vue/props/runtime.mjs';\nimport { isNumber } from '../../utils/types.mjs';\nconst useDelayedToggleProps = buildProps({\n  showAfter: {\n    type: Number,\n    default: 0\n  },\n  hideAfter: {\n    type: Number,\n    default: 200\n  },\n  autoClose: {\n    type: Number,\n    default: 0\n  }\n});\nconst useDelayedToggle = ({\n  showAfter,\n  hideAfter,\n  autoClose,\n  open,\n  close\n}) => {\n  const {\n    registerTimeout\n  } = useTimeout();\n  const {\n    registerTimeout: registerTimeoutForAutoClose,\n    cancelTimeout: cancelTimeoutForAutoClose\n  } = useTimeout();\n  const onOpen = event => {\n    registerTimeout(() => {\n      open(event);\n      const _autoClose = unref(autoClose);\n      if (isNumber(_autoClose) && _autoClose > 0) {\n        registerTimeoutForAutoClose(() => {\n          close(event);\n        }, _autoClose);\n      }\n    }, unref(showAfter));\n  };\n  const onClose = event => {\n    cancelTimeoutForAutoClose();\n    registerTimeout(() => {\n      close(event);\n    }, unref(hideAfter));\n  };\n  return {\n    onOpen,\n    onClose\n  };\n};\nexport { useDelayedToggle, useDelayedToggleProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}