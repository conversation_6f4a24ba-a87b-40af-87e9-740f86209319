{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, inject, watch, onBeforeUnmount, openBlock, createBlock, unref, withCtx, renderSlot, createElementBlock, mergeProps } from 'vue';\nimport { tooltipV2RootKey } from './constants.mjs';\nimport ForwardRef from './forward-ref.mjs';\nimport { tooltipV2TriggerProps } from './trigger.mjs';\nimport { tooltipV2CommonProps } from './common.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { composeEventHandlers } from '../../../utils/dom/event.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTooltipV2Trigger\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: {\n    ...tooltipV2CommonProps,\n    ...tooltipV2TriggerProps\n  },\n  setup(__props) {\n    const props = __props;\n    const {\n      onClose,\n      onOpen,\n      onDelayOpen,\n      triggerRef,\n      contentId\n    } = inject(tooltipV2RootKey);\n    let isMousedown = false;\n    const setTriggerRef = el => {\n      triggerRef.value = el;\n    };\n    const onMouseup = () => {\n      isMousedown = false;\n    };\n    const onMouseenter = composeEventHandlers(props.onMouseEnter, onDelayOpen);\n    const onMouseleave = composeEventHandlers(props.onMouseLeave, onClose);\n    const onMousedown = composeEventHandlers(props.onMouseDown, () => {\n      onClose();\n      isMousedown = true;\n      document.addEventListener(\"mouseup\", onMouseup, {\n        once: true\n      });\n    });\n    const onFocus = composeEventHandlers(props.onFocus, () => {\n      if (!isMousedown) onOpen();\n    });\n    const onBlur = composeEventHandlers(props.onBlur, onClose);\n    const onClick = composeEventHandlers(props.onClick, e => {\n      if (e.detail === 0) onClose();\n    });\n    const events = {\n      blur: onBlur,\n      click: onClick,\n      focus: onFocus,\n      mousedown: onMousedown,\n      mouseenter: onMouseenter,\n      mouseleave: onMouseleave\n    };\n    const setEvents = (el, events2, type) => {\n      if (el) {\n        Object.entries(events2).forEach(([name, handler]) => {\n          el[type](name, handler);\n        });\n      }\n    };\n    watch(triggerRef, (triggerEl, previousTriggerEl) => {\n      setEvents(triggerEl, events, \"addEventListener\");\n      setEvents(previousTriggerEl, events, \"removeEventListener\");\n      if (triggerEl) {\n        triggerEl.setAttribute(\"aria-describedby\", contentId.value);\n      }\n    });\n    onBeforeUnmount(() => {\n      setEvents(triggerRef.value, events, \"removeEventListener\");\n      document.removeEventListener(\"mouseup\", onMouseup);\n    });\n    return (_ctx, _cache) => {\n      return _ctx.nowrap ? (openBlock(), createBlock(unref(ForwardRef), {\n        key: 0,\n        \"set-ref\": setTriggerRef,\n        \"only-child\": \"\"\n      }, {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      })) : (openBlock(), createElementBlock(\"button\", mergeProps({\n        key: 1,\n        ref_key: \"triggerRef\",\n        ref: triggerRef\n      }, _ctx.$attrs), [renderSlot(_ctx.$slots, \"default\")], 16));\n    };\n  }\n});\nvar TooltipV2Trigger = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"trigger.vue\"]]);\nexport { TooltipV2Trigger as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}