{"ast": null, "code": "import { getCurrentInstance, inject, ref, computed, unref } from 'vue';\nconst defaultNamespace = \"el\";\nconst statePrefix = \"is-\";\nconst _bem = (namespace, block, blockSuffix, element, modifier) => {\n  let cls = `${namespace}-${block}`;\n  if (blockSuffix) {\n    cls += `-${blockSuffix}`;\n  }\n  if (element) {\n    cls += `__${element}`;\n  }\n  if (modifier) {\n    cls += `--${modifier}`;\n  }\n  return cls;\n};\nconst namespaceContextKey = Symbol(\"namespaceContextKey\");\nconst useGetDerivedNamespace = namespaceOverrides => {\n  const derivedNamespace = namespaceOverrides || (getCurrentInstance() ? inject(namespaceContextKey, ref(defaultNamespace)) : ref(defaultNamespace));\n  const namespace = computed(() => {\n    return unref(derivedNamespace) || defaultNamespace;\n  });\n  return namespace;\n};\nconst useNamespace = (block, namespaceOverrides) => {\n  const namespace = useGetDerivedNamespace(namespaceOverrides);\n  const b = (blockSuffix = \"\") => _bem(namespace.value, block, blockSuffix, \"\", \"\");\n  const e = element => element ? _bem(namespace.value, block, \"\", element, \"\") : \"\";\n  const m = modifier => modifier ? _bem(namespace.value, block, \"\", \"\", modifier) : \"\";\n  const be = (blockSuffix, element) => blockSuffix && element ? _bem(namespace.value, block, blockSuffix, element, \"\") : \"\";\n  const em = (element, modifier) => element && modifier ? _bem(namespace.value, block, \"\", element, modifier) : \"\";\n  const bm = (blockSuffix, modifier) => blockSuffix && modifier ? _bem(namespace.value, block, blockSuffix, \"\", modifier) : \"\";\n  const bem = (blockSuffix, element, modifier) => blockSuffix && element && modifier ? _bem(namespace.value, block, blockSuffix, element, modifier) : \"\";\n  const is = (name, ...args) => {\n    const state = args.length >= 1 ? args[0] : true;\n    return name && state ? `${statePrefix}${name}` : \"\";\n  };\n  const cssVar = object => {\n    const styles = {};\n    for (const key in object) {\n      if (object[key]) {\n        styles[`--${namespace.value}-${key}`] = object[key];\n      }\n    }\n    return styles;\n  };\n  const cssVarBlock = object => {\n    const styles = {};\n    for (const key in object) {\n      if (object[key]) {\n        styles[`--${namespace.value}-${block}-${key}`] = object[key];\n      }\n    }\n    return styles;\n  };\n  const cssVarName = name => `--${namespace.value}-${name}`;\n  const cssVarBlockName = name => `--${namespace.value}-${block}-${name}`;\n  return {\n    namespace,\n    b,\n    e,\n    m,\n    be,\n    em,\n    bm,\n    bem,\n    is,\n    cssVar,\n    cssVarName,\n    cssVarBlock,\n    cssVarBlockName\n  };\n};\nexport { defaultNamespace, namespaceContextKey, useGetDerivedNamespace, useNamespace };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}