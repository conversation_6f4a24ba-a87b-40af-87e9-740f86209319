{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { reactive } from 'vue';\nimport { markNodeData, NODE_KEY } from './util.mjs';\nimport { hasOwn, isArray, isFunction, isString } from '@vue/shared';\nimport { isBoolean, isUndefined } from '../../../../utils/types.mjs';\nconst getChildState = node => {\n  let all = true;\n  let none = true;\n  let allWithoutDisable = true;\n  for (let i = 0, j = node.length; i < j; i++) {\n    const n = node[i];\n    if (n.checked !== true || n.indeterminate) {\n      all = false;\n      if (!n.disabled) {\n        allWithoutDisable = false;\n      }\n    }\n    if (n.checked !== false || n.indeterminate) {\n      none = false;\n    }\n  }\n  return {\n    all,\n    none,\n    allWithoutDisable,\n    half: !all && !none\n  };\n};\nconst reInitChecked = function (node) {\n  if (node.childNodes.length === 0 || node.loading) return;\n  const {\n    all,\n    none,\n    half\n  } = getChildState(node.childNodes);\n  if (all) {\n    node.checked = true;\n    node.indeterminate = false;\n  } else if (half) {\n    node.checked = false;\n    node.indeterminate = true;\n  } else if (none) {\n    node.checked = false;\n    node.indeterminate = false;\n  }\n  const parent = node.parent;\n  if (!parent || parent.level === 0) return;\n  if (!node.store.checkStrictly) {\n    reInitChecked(parent);\n  }\n};\nconst getPropertyFromData = function (node, prop) {\n  const props = node.store.props;\n  const data = node.data || {};\n  const config = props[prop];\n  if (isFunction(config)) {\n    return config(data, node);\n  } else if (isString(config)) {\n    return data[config];\n  } else if (isUndefined(config)) {\n    const dataProp = data[prop];\n    return isUndefined(dataProp) ? \"\" : dataProp;\n  }\n};\nlet nodeIdSeed = 0;\nclass Node {\n  constructor(options) {\n    this.id = nodeIdSeed++;\n    this.text = null;\n    this.checked = false;\n    this.indeterminate = false;\n    this.data = null;\n    this.expanded = false;\n    this.parent = null;\n    this.visible = true;\n    this.isCurrent = false;\n    this.canFocus = false;\n    for (const name in options) {\n      if (hasOwn(options, name)) {\n        this[name] = options[name];\n      }\n    }\n    this.level = 0;\n    this.loaded = false;\n    this.childNodes = [];\n    this.loading = false;\n    if (this.parent) {\n      this.level = this.parent.level + 1;\n    }\n  }\n  initialize() {\n    const store = this.store;\n    if (!store) {\n      throw new Error(\"[Node]store is required!\");\n    }\n    store.registerNode(this);\n    const props = store.props;\n    if (props && typeof props.isLeaf !== \"undefined\") {\n      const isLeaf = getPropertyFromData(this, \"isLeaf\");\n      if (isBoolean(isLeaf)) {\n        this.isLeafByUser = isLeaf;\n      }\n    }\n    if (store.lazy !== true && this.data) {\n      this.setData(this.data);\n      if (store.defaultExpandAll) {\n        this.expanded = true;\n        this.canFocus = true;\n      }\n    } else if (this.level > 0 && store.lazy && store.defaultExpandAll && !this.isLeafByUser) {\n      this.expand();\n    }\n    if (!isArray(this.data)) {\n      markNodeData(this, this.data);\n    }\n    if (!this.data) return;\n    const defaultExpandedKeys = store.defaultExpandedKeys;\n    const key = store.key;\n    if (key && defaultExpandedKeys && defaultExpandedKeys.includes(this.key)) {\n      this.expand(null, store.autoExpandParent);\n    }\n    if (key && store.currentNodeKey !== void 0 && this.key === store.currentNodeKey) {\n      store.currentNode = this;\n      store.currentNode.isCurrent = true;\n    }\n    if (store.lazy) {\n      store._initDefaultCheckedNode(this);\n    }\n    this.updateLeafState();\n    if (this.parent && (this.level === 1 || this.parent.expanded === true)) this.canFocus = true;\n  }\n  setData(data) {\n    if (!isArray(data)) {\n      markNodeData(this, data);\n    }\n    this.data = data;\n    this.childNodes = [];\n    let children;\n    if (this.level === 0 && isArray(this.data)) {\n      children = this.data;\n    } else {\n      children = getPropertyFromData(this, \"children\") || [];\n    }\n    for (let i = 0, j = children.length; i < j; i++) {\n      this.insertChild({\n        data: children[i]\n      });\n    }\n  }\n  get label() {\n    return getPropertyFromData(this, \"label\");\n  }\n  get key() {\n    const nodeKey = this.store.key;\n    if (this.data) return this.data[nodeKey];\n    return null;\n  }\n  get disabled() {\n    return getPropertyFromData(this, \"disabled\");\n  }\n  get nextSibling() {\n    const parent = this.parent;\n    if (parent) {\n      const index = parent.childNodes.indexOf(this);\n      if (index > -1) {\n        return parent.childNodes[index + 1];\n      }\n    }\n    return null;\n  }\n  get previousSibling() {\n    const parent = this.parent;\n    if (parent) {\n      const index = parent.childNodes.indexOf(this);\n      if (index > -1) {\n        return index > 0 ? parent.childNodes[index - 1] : null;\n      }\n    }\n    return null;\n  }\n  contains(target, deep = true) {\n    return (this.childNodes || []).some(child => child === target || deep && child.contains(target));\n  }\n  remove() {\n    const parent = this.parent;\n    if (parent) {\n      parent.removeChild(this);\n    }\n  }\n  insertChild(child, index, batch) {\n    if (!child) throw new Error(\"InsertChild error: child is required.\");\n    if (!(child instanceof Node)) {\n      if (!batch) {\n        const children = this.getChildren(true);\n        if (!children.includes(child.data)) {\n          if (isUndefined(index) || index < 0) {\n            children.push(child.data);\n          } else {\n            children.splice(index, 0, child.data);\n          }\n        }\n      }\n      Object.assign(child, {\n        parent: this,\n        store: this.store\n      });\n      child = reactive(new Node(child));\n      if (child instanceof Node) {\n        child.initialize();\n      }\n    }\n    child.level = this.level + 1;\n    if (isUndefined(index) || index < 0) {\n      this.childNodes.push(child);\n    } else {\n      this.childNodes.splice(index, 0, child);\n    }\n    this.updateLeafState();\n  }\n  insertBefore(child, ref) {\n    let index;\n    if (ref) {\n      index = this.childNodes.indexOf(ref);\n    }\n    this.insertChild(child, index);\n  }\n  insertAfter(child, ref) {\n    let index;\n    if (ref) {\n      index = this.childNodes.indexOf(ref);\n      if (index !== -1) index += 1;\n    }\n    this.insertChild(child, index);\n  }\n  removeChild(child) {\n    const children = this.getChildren() || [];\n    const dataIndex = children.indexOf(child.data);\n    if (dataIndex > -1) {\n      children.splice(dataIndex, 1);\n    }\n    const index = this.childNodes.indexOf(child);\n    if (index > -1) {\n      this.store && this.store.deregisterNode(child);\n      child.parent = null;\n      this.childNodes.splice(index, 1);\n    }\n    this.updateLeafState();\n  }\n  removeChildByData(data) {\n    let targetNode = null;\n    for (let i = 0; i < this.childNodes.length; i++) {\n      if (this.childNodes[i].data === data) {\n        targetNode = this.childNodes[i];\n        break;\n      }\n    }\n    if (targetNode) {\n      this.removeChild(targetNode);\n    }\n  }\n  expand(callback, expandParent) {\n    const done = () => {\n      if (expandParent) {\n        let parent = this.parent;\n        while (parent.level > 0) {\n          parent.expanded = true;\n          parent = parent.parent;\n        }\n      }\n      this.expanded = true;\n      if (callback) callback();\n      this.childNodes.forEach(item => {\n        item.canFocus = true;\n      });\n    };\n    if (this.shouldLoadData()) {\n      this.loadData(data => {\n        if (isArray(data)) {\n          if (this.checked) {\n            this.setChecked(true, true);\n          } else if (!this.store.checkStrictly) {\n            reInitChecked(this);\n          }\n          done();\n        }\n      });\n    } else {\n      done();\n    }\n  }\n  doCreateChildren(array, defaultProps = {}) {\n    array.forEach(item => {\n      this.insertChild(Object.assign({\n        data: item\n      }, defaultProps), void 0, true);\n    });\n  }\n  collapse() {\n    this.expanded = false;\n    this.childNodes.forEach(item => {\n      item.canFocus = false;\n    });\n  }\n  shouldLoadData() {\n    return this.store.lazy === true && this.store.load && !this.loaded;\n  }\n  updateLeafState() {\n    if (this.store.lazy === true && this.loaded !== true && typeof this.isLeafByUser !== \"undefined\") {\n      this.isLeaf = this.isLeafByUser;\n      return;\n    }\n    const childNodes = this.childNodes;\n    if (!this.store.lazy || this.store.lazy === true && this.loaded === true) {\n      this.isLeaf = !childNodes || childNodes.length === 0;\n      return;\n    }\n    this.isLeaf = false;\n  }\n  setChecked(value, deep, recursion, passValue) {\n    this.indeterminate = value === \"half\";\n    this.checked = value === true;\n    if (this.store.checkStrictly) return;\n    if (!(this.shouldLoadData() && !this.store.checkDescendants)) {\n      const {\n        all,\n        allWithoutDisable\n      } = getChildState(this.childNodes);\n      if (!this.isLeaf && !all && allWithoutDisable) {\n        this.checked = false;\n        value = false;\n      }\n      const handleDescendants = () => {\n        if (deep) {\n          const childNodes = this.childNodes;\n          for (let i = 0, j = childNodes.length; i < j; i++) {\n            const child = childNodes[i];\n            passValue = passValue || value !== false;\n            const isCheck = child.disabled ? child.checked : passValue;\n            child.setChecked(isCheck, deep, true, passValue);\n          }\n          const {\n            half,\n            all: all2\n          } = getChildState(childNodes);\n          if (!all2) {\n            this.checked = all2;\n            this.indeterminate = half;\n          }\n        }\n      };\n      if (this.shouldLoadData()) {\n        this.loadData(() => {\n          handleDescendants();\n          reInitChecked(this);\n        }, {\n          checked: value !== false\n        });\n        return;\n      } else {\n        handleDescendants();\n      }\n    }\n    const parent = this.parent;\n    if (!parent || parent.level === 0) return;\n    if (!recursion) {\n      reInitChecked(parent);\n    }\n  }\n  getChildren(forceInit = false) {\n    if (this.level === 0) return this.data;\n    const data = this.data;\n    if (!data) return null;\n    const props = this.store.props;\n    let children = \"children\";\n    if (props) {\n      children = props.children || \"children\";\n    }\n    if (isUndefined(data[children])) {\n      data[children] = null;\n    }\n    if (forceInit && !data[children]) {\n      data[children] = [];\n    }\n    return data[children];\n  }\n  updateChildren() {\n    const newData = this.getChildren() || [];\n    const oldData = this.childNodes.map(node => node.data);\n    const newDataMap = {};\n    const newNodes = [];\n    newData.forEach((item, index) => {\n      const key = item[NODE_KEY];\n      const isNodeExists = !!key && oldData.findIndex(data => data[NODE_KEY] === key) >= 0;\n      if (isNodeExists) {\n        newDataMap[key] = {\n          index,\n          data: item\n        };\n      } else {\n        newNodes.push({\n          index,\n          data: item\n        });\n      }\n    });\n    if (!this.store.lazy) {\n      oldData.forEach(item => {\n        if (!newDataMap[item[NODE_KEY]]) this.removeChildByData(item);\n      });\n    }\n    newNodes.forEach(({\n      index,\n      data\n    }) => {\n      this.insertChild({\n        data\n      }, index);\n    });\n    this.updateLeafState();\n  }\n  loadData(callback, defaultProps = {}) {\n    if (this.store.lazy === true && this.store.load && !this.loaded && (!this.loading || Object.keys(defaultProps).length)) {\n      this.loading = true;\n      const resolve = children => {\n        this.childNodes = [];\n        this.doCreateChildren(children, defaultProps);\n        this.loaded = true;\n        this.loading = false;\n        this.updateLeafState();\n        if (callback) {\n          callback.call(this, children);\n        }\n      };\n      const reject = () => {\n        this.loading = false;\n      };\n      this.store.load(this, resolve, reject);\n    } else {\n      if (callback) {\n        callback.call(this);\n      }\n    }\n  }\n  eachNode(callback) {\n    const arr = [this];\n    while (arr.length) {\n      const node = arr.shift();\n      arr.unshift(...node.childNodes);\n      callback(node);\n    }\n  }\n  reInitChecked() {\n    if (this.store.checkStrictly) return;\n    reInitChecked(this);\n  }\n}\nexport { Node as default, getChildState };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}