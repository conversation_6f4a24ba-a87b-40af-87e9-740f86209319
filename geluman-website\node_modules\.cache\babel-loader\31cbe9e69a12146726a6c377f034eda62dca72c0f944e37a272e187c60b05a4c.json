{"ast": null, "code": "import { useTooltipContentProps } from './content.mjs';\nimport { useTooltipTriggerProps } from './trigger.mjs';\nimport { popperProps } from '../../popper/src/popper.mjs';\nimport { popperArrowProps } from '../../popper/src/arrow.mjs';\nimport { createModelToggleComposable } from '../../../hooks/use-model-toggle/index.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst {\n  useModelToggleProps: useTooltipModelToggleProps,\n  useModelToggleEmits: useTooltipModelToggleEmits,\n  useModelToggle: useTooltipModelToggle\n} = createModelToggleComposable(\"visible\");\nconst useTooltipProps = buildProps({\n  ...popperProps,\n  ...useTooltipModelToggleProps,\n  ...useTooltipContentProps,\n  ...useTooltipTriggerProps,\n  ...popperArrowProps,\n  showArrow: {\n    type: Boolean,\n    default: true\n  }\n});\nconst tooltipEmits = [...useTooltipModelToggleEmits, \"before-show\", \"before-hide\", \"show\", \"hide\", \"open\", \"close\"];\nexport { tooltipEmits, useTooltipModelToggle, useTooltipModelToggleEmits, useTooltipModelToggleProps, useTooltipProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}