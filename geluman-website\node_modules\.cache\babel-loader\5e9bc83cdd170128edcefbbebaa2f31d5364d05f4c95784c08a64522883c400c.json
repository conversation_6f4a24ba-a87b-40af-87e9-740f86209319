{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { componentSizes } from '../../../constants/size.mjs';\nconst textProps = buildProps({\n  type: {\n    type: String,\n    values: [\"primary\", \"success\", \"info\", \"warning\", \"danger\", \"\"],\n    default: \"\"\n  },\n  size: {\n    type: String,\n    values: componentSizes,\n    default: \"\"\n  },\n  truncated: Boolean,\n  lineClamp: {\n    type: [String, Number]\n  },\n  tag: {\n    type: String,\n    default: \"span\"\n  }\n});\nexport { textProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}