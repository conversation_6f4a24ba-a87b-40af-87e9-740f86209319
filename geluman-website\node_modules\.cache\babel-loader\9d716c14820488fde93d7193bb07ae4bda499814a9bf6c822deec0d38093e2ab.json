{"ast": null, "code": "import { datePickerSharedProps, selectionModeWithDefault } from './shared.mjs';\nimport { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nconst basicYearTableProps = buildProps({\n  ...datePickerSharedProps,\n  selectionMode: selectionModeWithDefault(\"year\")\n});\nexport { basicYearTableProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}