{"ast": null, "code": "import { StarFilled, Star } from '@element-plus/icons-vue';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nconst rateProps = buildProps({\n  modelValue: {\n    type: Number,\n    default: 0\n  },\n  id: {\n    type: String,\n    default: void 0\n  },\n  lowThreshold: {\n    type: Number,\n    default: 2\n  },\n  highThreshold: {\n    type: Number,\n    default: 4\n  },\n  max: {\n    type: Number,\n    default: 5\n  },\n  colors: {\n    type: definePropType([Array, Object]),\n    default: () => mutable([\"\", \"\", \"\"])\n  },\n  voidColor: {\n    type: String,\n    default: \"\"\n  },\n  disabledVoidColor: {\n    type: String,\n    default: \"\"\n  },\n  icons: {\n    type: definePropType([Array, Object]),\n    default: () => [StarFilled, StarFilled, StarFilled]\n  },\n  voidIcon: {\n    type: iconPropType,\n    default: () => Star\n  },\n  disabledVoidIcon: {\n    type: iconPropType,\n    default: () => StarFilled\n  },\n  disabled: Boolean,\n  allowHalf: Boolean,\n  showText: Boolean,\n  showScore: Boolean,\n  textColor: {\n    type: String,\n    default: \"\"\n  },\n  texts: {\n    type: definePropType(Array),\n    default: () => mutable([\"Extremely bad\", \"Disappointed\", \"Fair\", \"Satisfied\", \"Surprise\"])\n  },\n  scoreTemplate: {\n    type: String,\n    default: \"{value}\"\n  },\n  size: useSizeProp,\n  clearable: Boolean,\n  ...useAriaProps([\"ariaLabel\"])\n});\nconst rateEmits = {\n  [CHANGE_EVENT]: value => isNumber(value),\n  [UPDATE_MODEL_EVENT]: value => isNumber(value)\n};\nexport { rateEmits, rateProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}