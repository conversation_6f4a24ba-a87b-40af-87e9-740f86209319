{"ast": null, "code": "import { createCollectionWithScope } from '../../collection/src/collection2.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst rovingFocusGroupProps = buildProps({\n  style: {\n    type: definePropType([String, Array, Object])\n  },\n  currentTabId: {\n    type: definePropType(String)\n  },\n  defaultCurrentTabId: String,\n  loop: <PERSON>olean,\n  dir: {\n    type: String,\n    values: [\"ltr\", \"rtl\"],\n    default: \"ltr\"\n  },\n  orientation: {\n    type: definePropType(String)\n  },\n  onBlur: Function,\n  onFocus: Function,\n  onMousedown: Function\n});\nconst {\n  ElCollection,\n  ElCollectionItem,\n  COLLECTION_INJECTION_KEY,\n  COLLECTION_ITEM_INJECTION_KEY\n} = createCollectionWithScope(\"RovingFocusGroup\");\nexport { ElCollection, ElCollectionItem, COLLECTION_INJECTION_KEY as ROVING_FOCUS_COLLECTION_INJECTION_KEY, COLLECTION_ITEM_INJECTION_KEY as ROVING_FOCUS_ITEM_COLLECTION_INJECTION_KEY, rovingFocusGroupProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}