<template>
  <div class="loading-screen" v-if="loading">
    <div class="loading-content">
      <div class="logo-container">
        <img
          src="@/assets/img/glmlogo.png"
          alt="格鲁曼"
          class="logo animate-pulse"
        />
      </div>
      <div class="loading-spinner">
        <svg class="circular" viewBox="25 25 50 50">
          <circle
            class="path"
            cx="50"
            cy="50"
            r="20"
            fill="none"
            stroke-width="4"
            stroke-miterlimit="10"
          />
        </svg>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";

// 使用sessionStorage来记录是否是当前会话首次访问
const hasVisited = sessionStorage.getItem("hasVisitedSite");
const loading = ref(!hasVisited);

onMounted(() => {
  if (!hasVisited) {
    // 如果是当前会话首次访问，设置加载状态并设置sessionStorage标记
    sessionStorage.setItem("hasVisitedSite", "true");
    setTimeout(() => {
      loading.value = false;
    }, 1500);
  }
});
</script>

<style lang="scss" scoped>
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: white;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s;

  .loading-content {
    text-align: center;
  }

  .logo-container {
    margin-bottom: 2rem;

    .logo {
      width: 200px;
      height: auto;
    }
  }
}

.loading-spinner {
  position: relative;
  width: 40px;
  height: 40px;
  margin: 0 auto;

  .circular {
    animation: rotate 2s linear infinite;
    transform-origin: center center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
  }

  .path {
    stroke: var(--primary-color);
    stroke-dasharray: 89, 200;
    stroke-dashoffset: 0;
    animation: dash 1.5s ease-in-out infinite;
  }
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -124px;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
