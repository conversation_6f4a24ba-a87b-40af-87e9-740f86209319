{"ast": null, "code": "import { inject, ref, computed, unref } from 'vue';\nimport { collapseContextKey } from './constants.mjs';\nimport { useIdInjection } from '../../../hooks/use-id/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst useCollapseItem = props => {\n  const collapse = inject(collapseContextKey);\n  const {\n    namespace\n  } = useNamespace(\"collapse\");\n  const focusing = ref(false);\n  const isClick = ref(false);\n  const idInjection = useIdInjection();\n  const id = computed(() => idInjection.current++);\n  const name = computed(() => {\n    var _a;\n    return (_a = props.name) != null ? _a : `${namespace.value}-id-${idInjection.prefix}-${unref(id)}`;\n  });\n  const isActive = computed(() => collapse == null ? void 0 : collapse.activeNames.value.includes(unref(name)));\n  const handleFocus = () => {\n    setTimeout(() => {\n      if (!isClick.value) {\n        focusing.value = true;\n      } else {\n        isClick.value = false;\n      }\n    }, 50);\n  };\n  const handleHeaderClick = () => {\n    if (props.disabled) return;\n    collapse == null ? void 0 : collapse.handleItemClick(unref(name));\n    focusing.value = false;\n    isClick.value = true;\n  };\n  const handleEnterClick = () => {\n    collapse == null ? void 0 : collapse.handleItemClick(unref(name));\n  };\n  return {\n    focusing,\n    id,\n    isActive,\n    handleFocus,\n    handleHeaderClick,\n    handleEnterClick\n  };\n};\nconst useCollapseItemDOM = (props, {\n  focusing,\n  isActive,\n  id\n}) => {\n  const ns = useNamespace(\"collapse\");\n  const rootKls = computed(() => [ns.b(\"item\"), ns.is(\"active\", unref(isActive)), ns.is(\"disabled\", props.disabled)]);\n  const headKls = computed(() => [ns.be(\"item\", \"header\"), ns.is(\"active\", unref(isActive)), {\n    focusing: unref(focusing) && !props.disabled\n  }]);\n  const arrowKls = computed(() => [ns.be(\"item\", \"arrow\"), ns.is(\"active\", unref(isActive))]);\n  const itemWrapperKls = computed(() => ns.be(\"item\", \"wrap\"));\n  const itemContentKls = computed(() => ns.be(\"item\", \"content\"));\n  const scopedContentId = computed(() => ns.b(`content-${unref(id)}`));\n  const scopedHeadId = computed(() => ns.b(`head-${unref(id)}`));\n  return {\n    arrowKls,\n    headKls,\n    rootKls,\n    itemWrapperKls,\n    itemContentKls,\n    scopedContentId,\n    scopedHeadId\n  };\n};\nexport { useCollapseItem, useCollapseItemDOM };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}