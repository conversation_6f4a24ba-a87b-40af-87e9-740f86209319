{"ast": null, "code": "import { createVNode, mergeProps, isVNode } from 'vue';\nimport { tryCall } from '../utils.mjs';\nimport Row$1 from '../components/row.mjs';\nfunction _isSlot(s) {\n  return typeof s === \"function\" || Object.prototype.toString.call(s) === \"[object Object]\" && !isVNode(s);\n}\nconst RowRenderer = (props, {\n  slots\n}) => {\n  const {\n    columns,\n    columnsStyles,\n    depthMap,\n    expandColumnKey,\n    expandedRowKeys,\n    estimatedRowHeight,\n    hasFixedColumns,\n    rowData,\n    rowIndex,\n    style,\n    isScrolling,\n    rowProps,\n    rowClass,\n    rowKey,\n    rowEventHandlers,\n    ns,\n    onRowHovered,\n    onRowExpanded\n  } = props;\n  const rowKls = tryCall(rowClass, {\n    columns,\n    rowData,\n    rowIndex\n  }, \"\");\n  const additionalProps = tryCall(rowProps, {\n    columns,\n    rowData,\n    rowIndex\n  });\n  const _rowKey = rowData[rowKey];\n  const depth = depthMap[_rowKey] || 0;\n  const canExpand = Boolean(expandColumnKey);\n  const isFixedRow = rowIndex < 0;\n  const kls = [ns.e(\"row\"), rowKls, {\n    [ns.e(`row-depth-${depth}`)]: canExpand && rowIndex >= 0,\n    [ns.is(\"expanded\")]: canExpand && expandedRowKeys.includes(_rowKey),\n    [ns.is(\"fixed\")]: !depth && isFixedRow,\n    [ns.is(\"customized\")]: Boolean(slots.row)\n  }];\n  const onRowHover = hasFixedColumns ? onRowHovered : void 0;\n  const _rowProps = {\n    ...additionalProps,\n    columns,\n    columnsStyles,\n    class: kls,\n    depth,\n    expandColumnKey,\n    estimatedRowHeight: isFixedRow ? void 0 : estimatedRowHeight,\n    isScrolling,\n    rowIndex,\n    rowData,\n    rowKey: _rowKey,\n    rowEventHandlers,\n    style\n  };\n  const handlerMosueEnter = e => {\n    onRowHover == null ? void 0 : onRowHover({\n      hovered: true,\n      rowKey: _rowKey,\n      event: e,\n      rowData,\n      rowIndex\n    });\n  };\n  const handlerMouseLeave = e => {\n    onRowHover == null ? void 0 : onRowHover({\n      hovered: false,\n      rowKey: _rowKey,\n      event: e,\n      rowData,\n      rowIndex\n    });\n  };\n  return createVNode(Row$1, mergeProps(_rowProps, {\n    \"onRowExpand\": onRowExpanded,\n    \"onMouseenter\": handlerMosueEnter,\n    \"onMouseleave\": handlerMouseLeave,\n    \"rowkey\": _rowKey\n  }), _isSlot(slots) ? slots : {\n    default: () => [slots]\n  });\n};\nvar Row = RowRenderer;\nexport { Row as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}