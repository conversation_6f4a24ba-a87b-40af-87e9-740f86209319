{"ast": null, "code": "import { defineComponent, computed, provide, reactive, toRef, ref, createVNode, mergeProps } from 'vue';\nimport dayjs from 'dayjs';\nimport customParseFormat from 'dayjs/plugin/customParseFormat.js';\nimport advancedFormat from 'dayjs/plugin/advancedFormat.js';\nimport localeData from 'dayjs/plugin/localeData.js';\nimport weekOfYear from 'dayjs/plugin/weekOfYear.js';\nimport weekYear from 'dayjs/plugin/weekYear.js';\nimport dayOfYear from 'dayjs/plugin/dayOfYear.js';\nimport isSameOrAfter from 'dayjs/plugin/isSameOrAfter.js';\nimport isSameOrBefore from 'dayjs/plugin/isSameOrBefore.js';\nimport '../../time-picker/index.mjs';\nimport { ROOT_PICKER_INJECTION_KEY } from './constants.mjs';\nimport { datePickerProps } from './props/date-picker.mjs';\nimport { getPanel } from './panel-utils.mjs';\nimport { DEFAULT_FORMATS_DATEPICKER, DEFAULT_FORMATS_DATE } from '../../time-picker/src/constants.mjs';\nimport CommonPicker from '../../time-picker/src/common/picker.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\ndayjs.extend(localeData);\ndayjs.extend(advancedFormat);\ndayjs.extend(customParseFormat);\ndayjs.extend(weekOfYear);\ndayjs.extend(weekYear);\ndayjs.extend(dayOfYear);\ndayjs.extend(isSameOrAfter);\ndayjs.extend(isSameOrBefore);\nvar DatePicker = defineComponent({\n  name: \"ElDatePicker\",\n  install: null,\n  props: datePickerProps,\n  emits: [UPDATE_MODEL_EVENT],\n  setup(props, {\n    expose,\n    emit,\n    slots\n  }) {\n    const ns = useNamespace(\"picker-panel\");\n    const isDefaultFormat = computed(() => {\n      return !props.format;\n    });\n    provide(\"ElIsDefaultFormat\", isDefaultFormat);\n    provide(\"ElPopperOptions\", reactive(toRef(props, \"popperOptions\")));\n    provide(ROOT_PICKER_INJECTION_KEY, {\n      slots,\n      pickerNs: ns\n    });\n    const commonPicker = ref();\n    const refProps = {\n      focus: () => {\n        var _a;\n        (_a = commonPicker.value) == null ? void 0 : _a.focus();\n      },\n      blur: () => {\n        var _a;\n        (_a = commonPicker.value) == null ? void 0 : _a.blur();\n      },\n      handleOpen: () => {\n        var _a;\n        (_a = commonPicker.value) == null ? void 0 : _a.handleOpen();\n      },\n      handleClose: () => {\n        var _a;\n        (_a = commonPicker.value) == null ? void 0 : _a.handleClose();\n      }\n    };\n    expose(refProps);\n    const onModelValueUpdated = val => {\n      emit(UPDATE_MODEL_EVENT, val);\n    };\n    return () => {\n      var _a;\n      const format = (_a = props.format) != null ? _a : DEFAULT_FORMATS_DATEPICKER[props.type] || DEFAULT_FORMATS_DATE;\n      const Component = getPanel(props.type);\n      return createVNode(CommonPicker, mergeProps(props, {\n        \"format\": format,\n        \"type\": props.type,\n        \"ref\": commonPicker,\n        \"onUpdate:modelValue\": onModelValueUpdated\n      }), {\n        default: scopedProps => createVNode(Component, scopedProps, {\n          \"prev-month\": slots[\"prev-month\"],\n          \"next-month\": slots[\"next-month\"],\n          \"prev-year\": slots[\"prev-year\"],\n          \"next-year\": slots[\"next-year\"]\n        }),\n        \"range-separator\": slots[\"range-separator\"]\n      });\n    };\n  }\n});\nexport { DatePicker as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}