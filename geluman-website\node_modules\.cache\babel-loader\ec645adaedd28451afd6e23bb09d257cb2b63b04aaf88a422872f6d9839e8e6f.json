{"ast": null, "code": "import { computed } from 'vue';\nconst useYearRangeHeader = ({\n  unlinkPanels,\n  leftDate,\n  rightDate\n}) => {\n  const leftPrevYear = () => {\n    leftDate.value = leftDate.value.subtract(10, \"year\");\n    if (!unlinkPanels.value) {\n      rightDate.value = rightDate.value.subtract(10, \"year\");\n    }\n  };\n  const rightNextYear = () => {\n    if (!unlinkPanels.value) {\n      leftDate.value = leftDate.value.add(10, \"year\");\n    }\n    rightDate.value = rightDate.value.add(10, \"year\");\n  };\n  const leftNextYear = () => {\n    leftDate.value = leftDate.value.add(10, \"year\");\n  };\n  const rightPrevYear = () => {\n    rightDate.value = rightDate.value.subtract(10, \"year\");\n  };\n  const leftLabel = computed(() => {\n    const leftStartDate = Math.floor(leftDate.value.year() / 10) * 10;\n    return `${leftStartDate}-${leftStartDate + 9}`;\n  });\n  const rightLabel = computed(() => {\n    const rightStartDate = Math.floor(rightDate.value.year() / 10) * 10;\n    return `${rightStartDate}-${rightStartDate + 9}`;\n  });\n  const leftYear = computed(() => {\n    const leftEndDate = Math.floor(leftDate.value.year() / 10) * 10 + 9;\n    return leftEndDate;\n  });\n  const rightYear = computed(() => {\n    const rightStartDate = Math.floor(rightDate.value.year() / 10) * 10;\n    return rightStartDate;\n  });\n  return {\n    leftPrevYear,\n    rightNextYear,\n    leftNextYear,\n    rightPrevYear,\n    leftLabel,\n    rightLabel,\n    leftYear,\n    rightYear\n  };\n};\nexport { useYearRangeHeader };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}