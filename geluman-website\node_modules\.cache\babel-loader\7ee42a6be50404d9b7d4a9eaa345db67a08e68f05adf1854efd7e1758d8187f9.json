{"ast": null, "code": "import Button from './src/button2.mjs';\nimport ButtonGroup from './src/button-group2.mjs';\nexport { buttonEmits, buttonNativeTypes, buttonProps, buttonTypes } from './src/button.mjs';\nexport { buttonGroupContextKey } from './src/constants.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElButton = withInstall(Button, {\n  ButtonGroup\n});\nconst ElButtonGroup = withNoopInstall(ButtonGroup);\nexport { ElButton, ElButtonGroup, ElButton as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}