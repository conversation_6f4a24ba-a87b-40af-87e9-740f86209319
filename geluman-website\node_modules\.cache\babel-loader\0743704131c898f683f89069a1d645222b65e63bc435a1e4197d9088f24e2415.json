{"ast": null, "code": "import { defineComponent, ref, inject, computed, onBeforeUnmount, unref, watch, openBlock, createBlock, withCtx, createVNode, Transition, withDirectives, mergeProps, renderSlot, vShow, createCommentVNode } from 'vue';\nimport { computedEager, onClickOutside } from '@vueuse/core';\nimport '../../popper/index.mjs';\nimport { ElTeleport } from '../../teleport/index.mjs';\nimport { TOOLTIP_INJECTION_KEY } from './constants.mjs';\nimport { useTooltipContentProps } from './content.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { usePopperContainerId } from '../../../hooks/use-popper-container/index.mjs';\nimport ElPopperContent from '../../popper/src/content2.mjs';\nimport { tryFocus } from '../../focus-trap/src/utils.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { composeEventHandlers } from '../../../utils/dom/event.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTooltipContent\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: useTooltipContentProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const {\n      selector\n    } = usePopperContainerId();\n    const ns = useNamespace(\"tooltip\");\n    const contentRef = ref();\n    const popperContentRef = computedEager(() => {\n      var _a;\n      return (_a = contentRef.value) == null ? void 0 : _a.popperContentRef;\n    });\n    let stopHandle;\n    const {\n      controlled,\n      id,\n      open,\n      trigger,\n      onClose,\n      onOpen,\n      onShow,\n      onHide,\n      onBeforeShow,\n      onBeforeHide\n    } = inject(TOOLTIP_INJECTION_KEY, void 0);\n    const transitionClass = computed(() => {\n      return props.transition || `${ns.namespace.value}-fade-in-linear`;\n    });\n    const persistentRef = computed(() => {\n      if (process.env.NODE_ENV === \"test\") {\n        return true;\n      }\n      return props.persistent;\n    });\n    onBeforeUnmount(() => {\n      stopHandle == null ? void 0 : stopHandle();\n    });\n    const shouldRender = computed(() => {\n      return unref(persistentRef) ? true : unref(open);\n    });\n    const shouldShow = computed(() => {\n      return props.disabled ? false : unref(open);\n    });\n    const appendTo = computed(() => {\n      return props.appendTo || selector.value;\n    });\n    const contentStyle = computed(() => {\n      var _a;\n      return (_a = props.style) != null ? _a : {};\n    });\n    const ariaHidden = ref(true);\n    const onTransitionLeave = () => {\n      onHide();\n      isFocusInsideContent() && tryFocus(document.body);\n      ariaHidden.value = true;\n    };\n    const stopWhenControlled = () => {\n      if (unref(controlled)) return true;\n    };\n    const onContentEnter = composeEventHandlers(stopWhenControlled, () => {\n      if (props.enterable && unref(trigger) === \"hover\") {\n        onOpen();\n      }\n    });\n    const onContentLeave = composeEventHandlers(stopWhenControlled, () => {\n      if (unref(trigger) === \"hover\") {\n        onClose();\n      }\n    });\n    const onBeforeEnter = () => {\n      var _a, _b;\n      (_b = (_a = contentRef.value) == null ? void 0 : _a.updatePopper) == null ? void 0 : _b.call(_a);\n      onBeforeShow == null ? void 0 : onBeforeShow();\n    };\n    const onBeforeLeave = () => {\n      onBeforeHide == null ? void 0 : onBeforeHide();\n    };\n    const onAfterShow = () => {\n      onShow();\n      stopHandle = onClickOutside(popperContentRef, () => {\n        if (unref(controlled)) return;\n        const $trigger = unref(trigger);\n        if ($trigger !== \"hover\") {\n          onClose();\n        }\n      });\n    };\n    const onBlur = () => {\n      if (!props.virtualTriggering) {\n        onClose();\n      }\n    };\n    const isFocusInsideContent = event => {\n      var _a;\n      const popperContent = (_a = contentRef.value) == null ? void 0 : _a.popperContentRef;\n      const activeElement = (event == null ? void 0 : event.relatedTarget) || document.activeElement;\n      return popperContent == null ? void 0 : popperContent.contains(activeElement);\n    };\n    watch(() => unref(open), val => {\n      if (!val) {\n        stopHandle == null ? void 0 : stopHandle();\n      } else {\n        ariaHidden.value = false;\n      }\n    }, {\n      flush: \"post\"\n    });\n    watch(() => props.content, () => {\n      var _a, _b;\n      (_b = (_a = contentRef.value) == null ? void 0 : _a.updatePopper) == null ? void 0 : _b.call(_a);\n    });\n    expose({\n      contentRef,\n      isFocusInsideContent\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElTeleport), {\n        disabled: !_ctx.teleported,\n        to: unref(appendTo)\n      }, {\n        default: withCtx(() => [createVNode(Transition, {\n          name: unref(transitionClass),\n          onAfterLeave: onTransitionLeave,\n          onBeforeEnter,\n          onAfterEnter: onAfterShow,\n          onBeforeLeave\n        }, {\n          default: withCtx(() => [unref(shouldRender) ? withDirectives((openBlock(), createBlock(unref(ElPopperContent), mergeProps({\n            key: 0,\n            id: unref(id),\n            ref_key: \"contentRef\",\n            ref: contentRef\n          }, _ctx.$attrs, {\n            \"aria-label\": _ctx.ariaLabel,\n            \"aria-hidden\": ariaHidden.value,\n            \"boundaries-padding\": _ctx.boundariesPadding,\n            \"fallback-placements\": _ctx.fallbackPlacements,\n            \"gpu-acceleration\": _ctx.gpuAcceleration,\n            offset: _ctx.offset,\n            placement: _ctx.placement,\n            \"popper-options\": _ctx.popperOptions,\n            strategy: _ctx.strategy,\n            effect: _ctx.effect,\n            enterable: _ctx.enterable,\n            pure: _ctx.pure,\n            \"popper-class\": _ctx.popperClass,\n            \"popper-style\": [_ctx.popperStyle, unref(contentStyle)],\n            \"reference-el\": _ctx.referenceEl,\n            \"trigger-target-el\": _ctx.triggerTargetEl,\n            visible: unref(shouldShow),\n            \"z-index\": _ctx.zIndex,\n            onMouseenter: unref(onContentEnter),\n            onMouseleave: unref(onContentLeave),\n            onBlur,\n            onClose: unref(onClose)\n          }), {\n            default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n            _: 3\n          }, 16, [\"id\", \"aria-label\", \"aria-hidden\", \"boundaries-padding\", \"fallback-placements\", \"gpu-acceleration\", \"offset\", \"placement\", \"popper-options\", \"strategy\", \"effect\", \"enterable\", \"pure\", \"popper-class\", \"popper-style\", \"reference-el\", \"trigger-target-el\", \"visible\", \"z-index\", \"onMouseenter\", \"onMouseleave\", \"onClose\"])), [[vShow, unref(shouldShow)]]) : createCommentVNode(\"v-if\", true)]),\n          _: 3\n        }, 8, [\"name\"])]),\n        _: 3\n      }, 8, [\"disabled\", \"to\"]);\n    };\n  }\n});\nvar ElTooltipContent = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"content.vue\"]]);\nexport { ElTooltipContent as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}