{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { ref, toRef, getCurrentInstance, shallowRef, computed, unref, watch } from 'vue';\nimport { useColumns } from './composables/use-columns.mjs';\nimport { useScrollbar } from './composables/use-scrollbar.mjs';\nimport { useRow } from './composables/use-row.mjs';\nimport { useData } from './composables/use-data.mjs';\nimport { useStyles } from './composables/use-styles.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nimport { isArray } from '@vue/shared';\nfunction useTable(props) {\n  const mainTableRef = ref();\n  const leftTableRef = ref();\n  const rightTableRef = ref();\n  const {\n    columns,\n    columnsStyles,\n    columnsTotalWidth,\n    fixedColumnsOnLeft,\n    fixedColumnsOnRight,\n    hasFixedColumns,\n    mainColumns,\n    onColumnSorted\n  } = useColumns(props, toRef(props, \"columns\"), toRef(props, \"fixed\"));\n  const {\n    scrollTo,\n    scrollToLeft,\n    scrollToTop,\n    scrollToRow,\n    onScroll,\n    onVerticalScroll,\n    scrollPos\n  } = useScrollbar(props, {\n    mainTableRef,\n    leftTableRef,\n    rightTableRef,\n    onMaybeEndReached\n  });\n  const ns = useNamespace(\"table-v2\");\n  const instance = getCurrentInstance();\n  const isScrolling = shallowRef(false);\n  const {\n    expandedRowKeys,\n    lastRenderedRowIndex,\n    isDynamic,\n    isResetting,\n    rowHeights,\n    resetAfterIndex,\n    onRowExpanded,\n    onRowHeightChange,\n    onRowHovered,\n    onRowsRendered\n  } = useRow(props, {\n    mainTableRef,\n    leftTableRef,\n    rightTableRef,\n    tableInstance: instance,\n    ns,\n    isScrolling\n  });\n  const {\n    data,\n    depthMap\n  } = useData(props, {\n    expandedRowKeys,\n    lastRenderedRowIndex,\n    resetAfterIndex\n  });\n  const rowsHeight = computed(() => {\n    const {\n      estimatedRowHeight,\n      rowHeight\n    } = props;\n    const _data = unref(data);\n    if (isNumber(estimatedRowHeight)) {\n      return Object.values(unref(rowHeights)).reduce((acc, curr) => acc + curr, 0);\n    }\n    return _data.length * rowHeight;\n  });\n  const {\n    bodyWidth,\n    fixedTableHeight,\n    mainTableHeight,\n    leftTableWidth,\n    rightTableWidth,\n    headerWidth,\n    windowHeight,\n    footerHeight,\n    emptyStyle,\n    rootStyle,\n    headerHeight\n  } = useStyles(props, {\n    columnsTotalWidth,\n    fixedColumnsOnLeft,\n    fixedColumnsOnRight,\n    rowsHeight\n  });\n  const containerRef = ref();\n  const showEmpty = computed(() => {\n    const noData = unref(data).length === 0;\n    return isArray(props.fixedData) ? props.fixedData.length === 0 && noData : noData;\n  });\n  function getRowHeight(rowIndex) {\n    const {\n      estimatedRowHeight,\n      rowHeight,\n      rowKey\n    } = props;\n    if (!estimatedRowHeight) return rowHeight;\n    return unref(rowHeights)[unref(data)[rowIndex][rowKey]] || estimatedRowHeight;\n  }\n  const isEndReached = ref(false);\n  function onMaybeEndReached() {\n    const {\n      onEndReached\n    } = props;\n    if (!onEndReached) return;\n    const {\n      scrollTop\n    } = unref(scrollPos);\n    const _totalHeight = unref(rowsHeight);\n    const clientHeight = unref(windowHeight);\n    const remainDistance = _totalHeight - (scrollTop + clientHeight) + props.hScrollbarSize;\n    if (!isEndReached.value && unref(lastRenderedRowIndex) >= 0 && _totalHeight <= scrollTop + unref(mainTableHeight) - unref(headerHeight)) {\n      isEndReached.value = true;\n      onEndReached(remainDistance);\n    } else {\n      isEndReached.value = false;\n    }\n  }\n  watch(() => unref(rowsHeight), () => isEndReached.value = false);\n  watch(() => props.expandedRowKeys, val => expandedRowKeys.value = val, {\n    deep: true\n  });\n  return {\n    columns,\n    containerRef,\n    mainTableRef,\n    leftTableRef,\n    rightTableRef,\n    isDynamic,\n    isResetting,\n    isScrolling,\n    hasFixedColumns,\n    columnsStyles,\n    columnsTotalWidth,\n    data,\n    expandedRowKeys,\n    depthMap,\n    fixedColumnsOnLeft,\n    fixedColumnsOnRight,\n    mainColumns,\n    bodyWidth,\n    emptyStyle,\n    rootStyle,\n    headerWidth,\n    footerHeight,\n    mainTableHeight,\n    fixedTableHeight,\n    leftTableWidth,\n    rightTableWidth,\n    showEmpty,\n    getRowHeight,\n    onColumnSorted,\n    onRowHovered,\n    onRowExpanded,\n    onRowsRendered,\n    onRowHeightChange,\n    scrollTo,\n    scrollToLeft,\n    scrollToTop,\n    scrollToRow,\n    onScroll,\n    onVerticalScroll\n  };\n}\nexport { useTable };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}