{"ast": null, "code": "import toArray from './toArray.js';\n\n/**\n * Gets the next value on a wrapped object following the\n * [iterator protocol](https://mdn.io/iteration_protocols#iterator).\n *\n * @name next\n * @memberOf _\n * @since 4.0.0\n * @category Seq\n * @returns {Object} Returns the next iterator value.\n * @example\n *\n * var wrapped = _([1, 2]);\n *\n * wrapped.next();\n * // => { 'done': false, 'value': 1 }\n *\n * wrapped.next();\n * // => { 'done': false, 'value': 2 }\n *\n * wrapped.next();\n * // => { 'done': true, 'value': undefined }\n */\nfunction wrapperNext() {\n  if (this.__values__ === undefined) {\n    this.__values__ = toArray(this.value());\n  }\n  var done = this.__index__ >= this.__values__.length,\n    value = done ? undefined : this.__values__[this.__index__++];\n  return {\n    'done': done,\n    'value': value\n  };\n}\nexport default wrapperNext;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}