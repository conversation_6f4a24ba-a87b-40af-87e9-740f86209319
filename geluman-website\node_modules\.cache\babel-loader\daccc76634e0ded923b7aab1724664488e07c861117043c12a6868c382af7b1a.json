{"ast": null, "code": "const attachEvents = (el, binding) => {\n  const popperComponent = binding.arg || binding.value;\n  const popover = popperComponent == null ? void 0 : popperComponent.popperRef;\n  if (popover) {\n    popover.triggerRef = el;\n  }\n};\nvar PopoverDirective = {\n  mounted(el, binding) {\n    attachEvents(el, binding);\n  },\n  updated(el, binding) {\n    attachEvents(el, binding);\n  }\n};\nconst VPopover = \"popover\";\nexport { VPopover, PopoverDirective as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}