{"ast": null, "code": "import { defineComponent, getCurrentInstance, ref, computed, watch, resolveComponent, resolveDirective, openBlock, createBlock, withCtx, createElementBlock, createElementVNode, normalizeClass, createVNode, Fragment, renderList, createTextVNode, toDisplayString, withDirectives, renderSlot } from 'vue';\nimport { ElCheckbox } from '../../checkbox/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { ArrowDown, ArrowUp } from '@element-plus/icons-vue';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { ElScrollbar } from '../../scrollbar/index.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport ClickOutside from '../../../directives/click-outside/index.mjs';\nimport { useTooltipContentProps } from '../../tooltip/src/content.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isPropAbsent } from '../../../utils/types.mjs';\nconst {\n  CheckboxGroup: ElCheckboxGroup\n} = ElCheckbox;\nconst _sfc_main = defineComponent({\n  name: \"ElTableFilterPanel\",\n  components: {\n    ElCheckbox,\n    ElCheckboxGroup,\n    ElScrollbar,\n    ElTooltip,\n    ElIcon,\n    ArrowDown,\n    ArrowUp\n  },\n  directives: {\n    ClickOutside\n  },\n  props: {\n    placement: {\n      type: String,\n      default: \"bottom-start\"\n    },\n    store: {\n      type: Object\n    },\n    column: {\n      type: Object\n    },\n    upDataColumn: {\n      type: Function\n    },\n    appendTo: useTooltipContentProps.appendTo\n  },\n  setup(props) {\n    const instance = getCurrentInstance();\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"table-filter\");\n    const parent = instance == null ? void 0 : instance.parent;\n    if (!parent.filterPanels.value[props.column.id]) {\n      parent.filterPanels.value[props.column.id] = instance;\n    }\n    const tooltipVisible = ref(false);\n    const tooltip = ref(null);\n    const filters = computed(() => {\n      return props.column && props.column.filters;\n    });\n    const filterClassName = computed(() => {\n      if (props.column.filterClassName) {\n        return `${ns.b()} ${props.column.filterClassName}`;\n      }\n      return ns.b();\n    });\n    const filterValue = computed({\n      get: () => {\n        var _a;\n        return (((_a = props.column) == null ? void 0 : _a.filteredValue) || [])[0];\n      },\n      set: value => {\n        if (filteredValue.value) {\n          if (!isPropAbsent(value)) {\n            filteredValue.value.splice(0, 1, value);\n          } else {\n            filteredValue.value.splice(0, 1);\n          }\n        }\n      }\n    });\n    const filteredValue = computed({\n      get() {\n        if (props.column) {\n          return props.column.filteredValue || [];\n        }\n        return [];\n      },\n      set(value) {\n        if (props.column) {\n          props.upDataColumn(\"filteredValue\", value);\n        }\n      }\n    });\n    const multiple = computed(() => {\n      if (props.column) {\n        return props.column.filterMultiple;\n      }\n      return true;\n    });\n    const isActive = filter => {\n      return filter.value === filterValue.value;\n    };\n    const hidden = () => {\n      tooltipVisible.value = false;\n    };\n    const showFilterPanel = e => {\n      e.stopPropagation();\n      tooltipVisible.value = !tooltipVisible.value;\n    };\n    const hideFilterPanel = () => {\n      tooltipVisible.value = false;\n    };\n    const handleConfirm = () => {\n      confirmFilter(filteredValue.value);\n      hidden();\n    };\n    const handleReset = () => {\n      filteredValue.value = [];\n      confirmFilter(filteredValue.value);\n      hidden();\n    };\n    const handleSelect = _filterValue => {\n      filterValue.value = _filterValue;\n      if (!isPropAbsent(_filterValue)) {\n        confirmFilter(filteredValue.value);\n      } else {\n        confirmFilter([]);\n      }\n      hidden();\n    };\n    const confirmFilter = filteredValue2 => {\n      props.store.commit(\"filterChange\", {\n        column: props.column,\n        values: filteredValue2\n      });\n      props.store.updateAllSelected();\n    };\n    watch(tooltipVisible, value => {\n      if (props.column) {\n        props.upDataColumn(\"filterOpened\", value);\n      }\n    }, {\n      immediate: true\n    });\n    const popperPaneRef = computed(() => {\n      var _a, _b;\n      return (_b = (_a = tooltip.value) == null ? void 0 : _a.popperRef) == null ? void 0 : _b.contentRef;\n    });\n    return {\n      tooltipVisible,\n      multiple,\n      filterClassName,\n      filteredValue,\n      filterValue,\n      filters,\n      handleConfirm,\n      handleReset,\n      handleSelect,\n      isPropAbsent,\n      isActive,\n      t,\n      ns,\n      showFilterPanel,\n      hideFilterPanel,\n      popperPaneRef,\n      tooltip\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_checkbox = resolveComponent(\"el-checkbox\");\n  const _component_el_checkbox_group = resolveComponent(\"el-checkbox-group\");\n  const _component_el_scrollbar = resolveComponent(\"el-scrollbar\");\n  const _component_arrow_up = resolveComponent(\"arrow-up\");\n  const _component_arrow_down = resolveComponent(\"arrow-down\");\n  const _component_el_icon = resolveComponent(\"el-icon\");\n  const _component_el_tooltip = resolveComponent(\"el-tooltip\");\n  const _directive_click_outside = resolveDirective(\"click-outside\");\n  return openBlock(), createBlock(_component_el_tooltip, {\n    ref: \"tooltip\",\n    visible: _ctx.tooltipVisible,\n    offset: 0,\n    placement: _ctx.placement,\n    \"show-arrow\": false,\n    \"stop-popper-mouse-event\": false,\n    teleported: \"\",\n    effect: \"light\",\n    pure: \"\",\n    \"popper-class\": _ctx.filterClassName,\n    persistent: \"\",\n    \"append-to\": _ctx.appendTo\n  }, {\n    content: withCtx(() => [_ctx.multiple ? (openBlock(), createElementBlock(\"div\", {\n      key: 0\n    }, [createElementVNode(\"div\", {\n      class: normalizeClass(_ctx.ns.e(\"content\"))\n    }, [createVNode(_component_el_scrollbar, {\n      \"wrap-class\": _ctx.ns.e(\"wrap\")\n    }, {\n      default: withCtx(() => [createVNode(_component_el_checkbox_group, {\n        modelValue: _ctx.filteredValue,\n        \"onUpdate:modelValue\": $event => _ctx.filteredValue = $event,\n        class: normalizeClass(_ctx.ns.e(\"checkbox-group\"))\n      }, {\n        default: withCtx(() => [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.filters, filter => {\n          return openBlock(), createBlock(_component_el_checkbox, {\n            key: filter.value,\n            value: filter.value\n          }, {\n            default: withCtx(() => [createTextVNode(toDisplayString(filter.text), 1)]),\n            _: 2\n          }, 1032, [\"value\"]);\n        }), 128))]),\n        _: 1\n      }, 8, [\"modelValue\", \"onUpdate:modelValue\", \"class\"])]),\n      _: 1\n    }, 8, [\"wrap-class\"])], 2), createElementVNode(\"div\", {\n      class: normalizeClass(_ctx.ns.e(\"bottom\"))\n    }, [createElementVNode(\"button\", {\n      class: normalizeClass({\n        [_ctx.ns.is(\"disabled\")]: _ctx.filteredValue.length === 0\n      }),\n      disabled: _ctx.filteredValue.length === 0,\n      type: \"button\",\n      onClick: _ctx.handleConfirm\n    }, toDisplayString(_ctx.t(\"el.table.confirmFilter\")), 11, [\"disabled\", \"onClick\"]), createElementVNode(\"button\", {\n      type: \"button\",\n      onClick: _ctx.handleReset\n    }, toDisplayString(_ctx.t(\"el.table.resetFilter\")), 9, [\"onClick\"])], 2)])) : (openBlock(), createElementBlock(\"ul\", {\n      key: 1,\n      class: normalizeClass(_ctx.ns.e(\"list\"))\n    }, [createElementVNode(\"li\", {\n      class: normalizeClass([_ctx.ns.e(\"list-item\"), {\n        [_ctx.ns.is(\"active\")]: _ctx.isPropAbsent(_ctx.filterValue)\n      }]),\n      onClick: $event => _ctx.handleSelect(null)\n    }, toDisplayString(_ctx.t(\"el.table.clearFilter\")), 11, [\"onClick\"]), (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.filters, filter => {\n      return openBlock(), createElementBlock(\"li\", {\n        key: filter.value,\n        class: normalizeClass([_ctx.ns.e(\"list-item\"), _ctx.ns.is(\"active\", _ctx.isActive(filter))]),\n        label: filter.value,\n        onClick: $event => _ctx.handleSelect(filter.value)\n      }, toDisplayString(filter.text), 11, [\"label\", \"onClick\"]);\n    }), 128))], 2))]),\n    default: withCtx(() => [withDirectives((openBlock(), createElementBlock(\"span\", {\n      class: normalizeClass([`${_ctx.ns.namespace.value}-table__column-filter-trigger`, `${_ctx.ns.namespace.value}-none-outline`]),\n      onClick: _ctx.showFilterPanel\n    }, [createVNode(_component_el_icon, null, {\n      default: withCtx(() => [renderSlot(_ctx.$slots, \"filter-icon\", {}, () => [_ctx.column.filterOpened ? (openBlock(), createBlock(_component_arrow_up, {\n        key: 0\n      })) : (openBlock(), createBlock(_component_arrow_down, {\n        key: 1\n      }))])]),\n      _: 3\n    })], 10, [\"onClick\"])), [[_directive_click_outside, _ctx.hideFilterPanel, _ctx.popperPaneRef]])]),\n    _: 3\n  }, 8, [\"visible\", \"placement\", \"popper-class\", \"append-to\"]);\n}\nvar FilterPanel = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"filter-panel.vue\"]]);\nexport { FilterPanel as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}