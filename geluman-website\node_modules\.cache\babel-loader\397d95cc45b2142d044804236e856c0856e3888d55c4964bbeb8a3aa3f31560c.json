{"ast": null, "code": "import { defineComponent, ref, inject, computed, watch, nextTick, onMounted, onBeforeUnmount, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, renderSlot, createTextVNode, toDisplayString, createCommentVNode } from 'vue';\nimport { anchorLinkProps } from './anchor-link.mjs';\nimport { anchorKey } from './constants.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst __default__ = defineComponent({\n  name: \"ElAnchorLink\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: anchorLinkProps,\n  setup(__props) {\n    const props = __props;\n    const linkRef = ref(null);\n    const {\n      ns,\n      direction,\n      currentAnchor,\n      addLink,\n      removeLink,\n      handleClick: contextHandleClick\n    } = inject(anchorKey);\n    const cls = computed(() => [ns.e(\"link\"), ns.is(\"active\", currentAnchor.value === props.href)]);\n    const handleClick = e => {\n      contextHandleClick(e, props.href);\n    };\n    watch(() => props.href, (val, oldVal) => {\n      nextTick(() => {\n        if (oldVal) removeLink(oldVal);\n        if (val) {\n          addLink({\n            href: val,\n            el: linkRef.value\n          });\n        }\n      });\n    });\n    onMounted(() => {\n      const {\n        href\n      } = props;\n      if (href) {\n        addLink({\n          href,\n          el: linkRef.value\n        });\n      }\n    });\n    onBeforeUnmount(() => {\n      const {\n        href\n      } = props;\n      if (href) {\n        removeLink(href);\n      }\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(ns).e(\"item\"))\n      }, [createElementVNode(\"a\", {\n        ref_key: \"linkRef\",\n        ref: linkRef,\n        class: normalizeClass(unref(cls)),\n        href: _ctx.href,\n        onClick: handleClick\n      }, [renderSlot(_ctx.$slots, \"default\", {}, () => [createTextVNode(toDisplayString(_ctx.title), 1)])], 10, [\"href\"]), _ctx.$slots[\"sub-link\"] && unref(direction) === \"vertical\" ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"list\"))\n      }, [renderSlot(_ctx.$slots, \"sub-link\")], 2)) : createCommentVNode(\"v-if\", true)], 2);\n    };\n  }\n});\nvar AnchorLink = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"anchor-link.vue\"]]);\nexport { AnchorLink as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}