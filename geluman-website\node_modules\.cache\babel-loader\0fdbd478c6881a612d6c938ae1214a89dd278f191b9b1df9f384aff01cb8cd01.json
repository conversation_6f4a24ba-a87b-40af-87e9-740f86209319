{"ast": null, "code": "import baseRest from './_baseRest.js';\nimport isIterateeCall from './_isIterateeCall.js';\n\n/**\n * Creates a function like `_.assign`.\n *\n * @private\n * @param {Function} assigner The function to assign values.\n * @returns {Function} Returns the new assigner function.\n */\nfunction createAssigner(assigner) {\n  return baseRest(function (object, sources) {\n    var index = -1,\n      length = sources.length,\n      customizer = length > 1 ? sources[length - 1] : undefined,\n      guard = length > 2 ? sources[2] : undefined;\n    customizer = assigner.length > 3 && typeof customizer == 'function' ? (length--, customizer) : undefined;\n    if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n      customizer = length < 3 ? undefined : customizer;\n      length = 1;\n    }\n    object = Object(object);\n    while (++index < length) {\n      var source = sources[index];\n      if (source) {\n        assigner(object, source, index, customizer);\n      }\n    }\n    return object;\n  });\n}\nexport default createAssigner;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}