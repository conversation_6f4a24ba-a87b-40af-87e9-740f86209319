{"ast": null, "code": "import { defineComponent, useSlots, computed, openBlock, createBlock, resolveDynamicComponent, unref, normalizeClass, withCtx, createElementVNode, withDirectives, createElementBlock, isRef, withModifiers, vModelCheckbox, renderSlot, Fragment, createTextVNode, toDisplayString, createCommentVNode } from 'vue';\nimport { checkboxProps, checkboxEmits } from './checkbox.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useCheckbox } from './composables/use-checkbox.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElCheckbox\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: checkboxProps,\n  emits: checkboxEmits,\n  setup(__props) {\n    const props = __props;\n    const slots = useSlots();\n    const {\n      inputId,\n      isLabeledByFormItem,\n      isChecked,\n      isDisabled,\n      isFocused,\n      checkboxSize,\n      hasOwnLabel,\n      model,\n      actualValue,\n      handleChange,\n      onClickRoot\n    } = useCheckbox(props, slots);\n    const ns = useNamespace(\"checkbox\");\n    const compKls = computed(() => {\n      return [ns.b(), ns.m(checkboxSize.value), ns.is(\"disabled\", isDisabled.value), ns.is(\"bordered\", props.border), ns.is(\"checked\", isChecked.value)];\n    });\n    const spanKls = computed(() => {\n      return [ns.e(\"input\"), ns.is(\"disabled\", isDisabled.value), ns.is(\"checked\", isChecked.value), ns.is(\"indeterminate\", props.indeterminate), ns.is(\"focus\", isFocused.value)];\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(resolveDynamicComponent(!unref(hasOwnLabel) && unref(isLabeledByFormItem) ? \"span\" : \"label\"), {\n        class: normalizeClass(unref(compKls)),\n        \"aria-controls\": _ctx.indeterminate ? _ctx.ariaControls : null,\n        onClick: unref(onClickRoot)\n      }, {\n        default: withCtx(() => {\n          var _a, _b, _c, _d;\n          return [createElementVNode(\"span\", {\n            class: normalizeClass(unref(spanKls))\n          }, [_ctx.trueValue || _ctx.falseValue || _ctx.trueLabel || _ctx.falseLabel ? withDirectives((openBlock(), createElementBlock(\"input\", {\n            key: 0,\n            id: unref(inputId),\n            \"onUpdate:modelValue\": $event => isRef(model) ? model.value = $event : null,\n            class: normalizeClass(unref(ns).e(\"original\")),\n            type: \"checkbox\",\n            indeterminate: _ctx.indeterminate,\n            name: _ctx.name,\n            tabindex: _ctx.tabindex,\n            disabled: unref(isDisabled),\n            \"true-value\": (_b = (_a = _ctx.trueValue) != null ? _a : _ctx.trueLabel) != null ? _b : true,\n            \"false-value\": (_d = (_c = _ctx.falseValue) != null ? _c : _ctx.falseLabel) != null ? _d : false,\n            onChange: unref(handleChange),\n            onFocus: $event => isFocused.value = true,\n            onBlur: $event => isFocused.value = false,\n            onClick: withModifiers(() => {}, [\"stop\"])\n          }, null, 42, [\"id\", \"onUpdate:modelValue\", \"indeterminate\", \"name\", \"tabindex\", \"disabled\", \"true-value\", \"false-value\", \"onChange\", \"onFocus\", \"onBlur\", \"onClick\"])), [[vModelCheckbox, unref(model)]]) : withDirectives((openBlock(), createElementBlock(\"input\", {\n            key: 1,\n            id: unref(inputId),\n            \"onUpdate:modelValue\": $event => isRef(model) ? model.value = $event : null,\n            class: normalizeClass(unref(ns).e(\"original\")),\n            type: \"checkbox\",\n            indeterminate: _ctx.indeterminate,\n            disabled: unref(isDisabled),\n            value: unref(actualValue),\n            name: _ctx.name,\n            tabindex: _ctx.tabindex,\n            onChange: unref(handleChange),\n            onFocus: $event => isFocused.value = true,\n            onBlur: $event => isFocused.value = false,\n            onClick: withModifiers(() => {}, [\"stop\"])\n          }, null, 42, [\"id\", \"onUpdate:modelValue\", \"indeterminate\", \"disabled\", \"value\", \"name\", \"tabindex\", \"onChange\", \"onFocus\", \"onBlur\", \"onClick\"])), [[vModelCheckbox, unref(model)]]), createElementVNode(\"span\", {\n            class: normalizeClass(unref(ns).e(\"inner\"))\n          }, null, 2)], 2), unref(hasOwnLabel) ? (openBlock(), createElementBlock(\"span\", {\n            key: 0,\n            class: normalizeClass(unref(ns).e(\"label\"))\n          }, [renderSlot(_ctx.$slots, \"default\"), !_ctx.$slots.default ? (openBlock(), createElementBlock(Fragment, {\n            key: 0\n          }, [createTextVNode(toDisplayString(_ctx.label), 1)], 64)) : createCommentVNode(\"v-if\", true)], 2)) : createCommentVNode(\"v-if\", true)];\n        }),\n        _: 3\n      }, 8, [\"class\", \"aria-controls\", \"onClick\"]);\n    };\n  }\n});\nvar Checkbox = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"checkbox.vue\"]]);\nexport { Checkbox as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}