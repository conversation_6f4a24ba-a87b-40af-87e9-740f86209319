{"ast": null, "code": "import { defineComponent, inject, ref, getCurrentInstance, provide, watch, nextTick, resolveComponent, withDirectives, openBlock, createElementBlock, normalizeClass, withModifiers, createElementVNode, normalizeStyle, createBlock, withCtx, resolveDynamicComponent, createCommentVNode, createVNode, Fragment, renderList, vShow } from 'vue';\nimport { ElCollapseTransition } from '../../collapse-transition/index.mjs';\nimport { ElCheckbox } from '../../checkbox/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { Loading, CaretRight } from '@element-plus/icons-vue';\nimport NodeContent from './tree-node-content.mjs';\nimport { getNodeKey, handleCurrentChange } from './model/util.mjs';\nimport { useNodeExpandEventBroadcast } from './model/useNodeExpandEventBroadcast.mjs';\nimport { dragEventsKey } from './model/useDragNode.mjs';\nimport Node from './model/node.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { isFunction, isString } from '@vue/shared';\nconst _sfc_main = defineComponent({\n  name: \"ElTreeNode\",\n  components: {\n    ElCollapseTransition,\n    ElCheckbox,\n    NodeContent,\n    ElIcon,\n    Loading\n  },\n  props: {\n    node: {\n      type: Node,\n      default: () => ({})\n    },\n    props: {\n      type: Object,\n      default: () => ({})\n    },\n    accordion: Boolean,\n    renderContent: Function,\n    renderAfterExpand: Boolean,\n    showCheckbox: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: [\"node-expand\"],\n  setup(props, ctx) {\n    const ns = useNamespace(\"tree\");\n    const {\n      broadcastExpanded\n    } = useNodeExpandEventBroadcast(props);\n    const tree = inject(\"RootTree\");\n    const expanded = ref(false);\n    const childNodeRendered = ref(false);\n    const oldChecked = ref();\n    const oldIndeterminate = ref();\n    const node$ = ref();\n    const dragEvents = inject(dragEventsKey);\n    const instance = getCurrentInstance();\n    provide(\"NodeInstance\", instance);\n    if (!tree) {\n      debugWarn(\"Tree\", \"Can not find node's tree.\");\n    }\n    if (props.node.expanded) {\n      expanded.value = true;\n      childNodeRendered.value = true;\n    }\n    const childrenKey = tree.props.props[\"children\"] || \"children\";\n    watch(() => {\n      var _a;\n      const children = (_a = props.node.data) == null ? void 0 : _a[childrenKey];\n      return children && [...children];\n    }, () => {\n      props.node.updateChildren();\n    });\n    watch(() => props.node.indeterminate, val => {\n      handleSelectChange(props.node.checked, val);\n    });\n    watch(() => props.node.checked, val => {\n      handleSelectChange(val, props.node.indeterminate);\n    });\n    watch(() => props.node.childNodes.length, () => props.node.reInitChecked());\n    watch(() => props.node.expanded, val => {\n      nextTick(() => expanded.value = val);\n      if (val) {\n        childNodeRendered.value = true;\n      }\n    });\n    const getNodeKey$1 = node => {\n      return getNodeKey(tree.props.nodeKey, node.data);\n    };\n    const getNodeClass = node => {\n      const nodeClassFunc = props.props.class;\n      if (!nodeClassFunc) {\n        return {};\n      }\n      let className;\n      if (isFunction(nodeClassFunc)) {\n        const {\n          data\n        } = node;\n        className = nodeClassFunc(data, node);\n      } else {\n        className = nodeClassFunc;\n      }\n      if (isString(className)) {\n        return {\n          [className]: true\n        };\n      } else {\n        return className;\n      }\n    };\n    const handleSelectChange = (checked, indeterminate) => {\n      if (oldChecked.value !== checked || oldIndeterminate.value !== indeterminate) {\n        tree.ctx.emit(\"check-change\", props.node.data, checked, indeterminate);\n      }\n      oldChecked.value = checked;\n      oldIndeterminate.value = indeterminate;\n    };\n    const handleClick = e => {\n      handleCurrentChange(tree.store, tree.ctx.emit, () => {\n        var _a;\n        const nodeKeyProp = (_a = tree == null ? void 0 : tree.props) == null ? void 0 : _a.nodeKey;\n        if (nodeKeyProp) {\n          const curNodeKey = getNodeKey$1(props.node);\n          tree.store.value.setCurrentNodeKey(curNodeKey);\n        } else {\n          tree.store.value.setCurrentNode(props.node);\n        }\n      });\n      tree.currentNode.value = props.node;\n      if (tree.props.expandOnClickNode) {\n        handleExpandIconClick();\n      }\n      if ((tree.props.checkOnClickNode || props.node.isLeaf && tree.props.checkOnClickLeaf && props.showCheckbox) && !props.node.disabled) {\n        handleCheckChange(!props.node.checked);\n      }\n      tree.ctx.emit(\"node-click\", props.node.data, props.node, instance, e);\n    };\n    const handleContextMenu = event => {\n      var _a;\n      if ((_a = tree.instance.vnode.props) == null ? void 0 : _a[\"onNodeContextmenu\"]) {\n        event.stopPropagation();\n        event.preventDefault();\n      }\n      tree.ctx.emit(\"node-contextmenu\", event, props.node.data, props.node, instance);\n    };\n    const handleExpandIconClick = () => {\n      if (props.node.isLeaf) return;\n      if (expanded.value) {\n        tree.ctx.emit(\"node-collapse\", props.node.data, props.node, instance);\n        props.node.collapse();\n      } else {\n        props.node.expand(() => {\n          ctx.emit(\"node-expand\", props.node.data, props.node, instance);\n        });\n      }\n    };\n    const handleCheckChange = value => {\n      props.node.setChecked(value, !(tree == null ? void 0 : tree.props.checkStrictly));\n      nextTick(() => {\n        const store = tree.store.value;\n        tree.ctx.emit(\"check\", props.node.data, {\n          checkedNodes: store.getCheckedNodes(),\n          checkedKeys: store.getCheckedKeys(),\n          halfCheckedNodes: store.getHalfCheckedNodes(),\n          halfCheckedKeys: store.getHalfCheckedKeys()\n        });\n      });\n    };\n    const handleChildNodeExpand = (nodeData, node, instance2) => {\n      broadcastExpanded(node);\n      tree.ctx.emit(\"node-expand\", nodeData, node, instance2);\n    };\n    const handleDragStart = event => {\n      if (!tree.props.draggable) return;\n      dragEvents.treeNodeDragStart({\n        event,\n        treeNode: props\n      });\n    };\n    const handleDragOver = event => {\n      event.preventDefault();\n      if (!tree.props.draggable) return;\n      dragEvents.treeNodeDragOver({\n        event,\n        treeNode: {\n          $el: node$.value,\n          node: props.node\n        }\n      });\n    };\n    const handleDrop = event => {\n      event.preventDefault();\n    };\n    const handleDragEnd = event => {\n      if (!tree.props.draggable) return;\n      dragEvents.treeNodeDragEnd(event);\n    };\n    return {\n      ns,\n      node$,\n      tree,\n      expanded,\n      childNodeRendered,\n      oldChecked,\n      oldIndeterminate,\n      getNodeKey: getNodeKey$1,\n      getNodeClass,\n      handleSelectChange,\n      handleClick,\n      handleContextMenu,\n      handleExpandIconClick,\n      handleCheckChange,\n      handleChildNodeExpand,\n      handleDragStart,\n      handleDragOver,\n      handleDrop,\n      handleDragEnd,\n      CaretRight\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = resolveComponent(\"el-icon\");\n  const _component_el_checkbox = resolveComponent(\"el-checkbox\");\n  const _component_loading = resolveComponent(\"loading\");\n  const _component_node_content = resolveComponent(\"node-content\");\n  const _component_el_tree_node = resolveComponent(\"el-tree-node\");\n  const _component_el_collapse_transition = resolveComponent(\"el-collapse-transition\");\n  return withDirectives((openBlock(), createElementBlock(\"div\", {\n    ref: \"node$\",\n    class: normalizeClass([_ctx.ns.b(\"node\"), _ctx.ns.is(\"expanded\", _ctx.expanded), _ctx.ns.is(\"current\", _ctx.node.isCurrent), _ctx.ns.is(\"hidden\", !_ctx.node.visible), _ctx.ns.is(\"focusable\", !_ctx.node.disabled), _ctx.ns.is(\"checked\", !_ctx.node.disabled && _ctx.node.checked), _ctx.getNodeClass(_ctx.node)]),\n    role: \"treeitem\",\n    tabindex: \"-1\",\n    \"aria-expanded\": _ctx.expanded,\n    \"aria-disabled\": _ctx.node.disabled,\n    \"aria-checked\": _ctx.node.checked,\n    draggable: _ctx.tree.props.draggable,\n    \"data-key\": _ctx.getNodeKey(_ctx.node),\n    onClick: withModifiers(_ctx.handleClick, [\"stop\"]),\n    onContextmenu: _ctx.handleContextMenu,\n    onDragstart: withModifiers(_ctx.handleDragStart, [\"stop\"]),\n    onDragover: withModifiers(_ctx.handleDragOver, [\"stop\"]),\n    onDragend: withModifiers(_ctx.handleDragEnd, [\"stop\"]),\n    onDrop: withModifiers(_ctx.handleDrop, [\"stop\"])\n  }, [createElementVNode(\"div\", {\n    class: normalizeClass(_ctx.ns.be(\"node\", \"content\")),\n    style: normalizeStyle({\n      paddingLeft: (_ctx.node.level - 1) * _ctx.tree.props.indent + \"px\"\n    })\n  }, [_ctx.tree.props.icon || _ctx.CaretRight ? (openBlock(), createBlock(_component_el_icon, {\n    key: 0,\n    class: normalizeClass([_ctx.ns.be(\"node\", \"expand-icon\"), _ctx.ns.is(\"leaf\", _ctx.node.isLeaf), {\n      expanded: !_ctx.node.isLeaf && _ctx.expanded\n    }]),\n    onClick: withModifiers(_ctx.handleExpandIconClick, [\"stop\"])\n  }, {\n    default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.tree.props.icon || _ctx.CaretRight)))]),\n    _: 1\n  }, 8, [\"class\", \"onClick\"])) : createCommentVNode(\"v-if\", true), _ctx.showCheckbox ? (openBlock(), createBlock(_component_el_checkbox, {\n    key: 1,\n    \"model-value\": _ctx.node.checked,\n    indeterminate: _ctx.node.indeterminate,\n    disabled: !!_ctx.node.disabled,\n    onClick: withModifiers(() => {}, [\"stop\"]),\n    onChange: _ctx.handleCheckChange\n  }, null, 8, [\"model-value\", \"indeterminate\", \"disabled\", \"onClick\", \"onChange\"])) : createCommentVNode(\"v-if\", true), _ctx.node.loading ? (openBlock(), createBlock(_component_el_icon, {\n    key: 2,\n    class: normalizeClass([_ctx.ns.be(\"node\", \"loading-icon\"), _ctx.ns.is(\"loading\")])\n  }, {\n    default: withCtx(() => [createVNode(_component_loading)]),\n    _: 1\n  }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true), createVNode(_component_node_content, {\n    node: _ctx.node,\n    \"render-content\": _ctx.renderContent\n  }, null, 8, [\"node\", \"render-content\"])], 6), createVNode(_component_el_collapse_transition, null, {\n    default: withCtx(() => [!_ctx.renderAfterExpand || _ctx.childNodeRendered ? withDirectives((openBlock(), createElementBlock(\"div\", {\n      key: 0,\n      class: normalizeClass(_ctx.ns.be(\"node\", \"children\")),\n      role: \"group\",\n      \"aria-expanded\": _ctx.expanded,\n      onClick: withModifiers(() => {}, [\"stop\"])\n    }, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.node.childNodes, child => {\n      return openBlock(), createBlock(_component_el_tree_node, {\n        key: _ctx.getNodeKey(child),\n        \"render-content\": _ctx.renderContent,\n        \"render-after-expand\": _ctx.renderAfterExpand,\n        \"show-checkbox\": _ctx.showCheckbox,\n        node: child,\n        accordion: _ctx.accordion,\n        props: _ctx.props,\n        onNodeExpand: _ctx.handleChildNodeExpand\n      }, null, 8, [\"render-content\", \"render-after-expand\", \"show-checkbox\", \"node\", \"accordion\", \"props\", \"onNodeExpand\"]);\n    }), 128))], 10, [\"aria-expanded\", \"onClick\"])), [[vShow, _ctx.expanded]]) : createCommentVNode(\"v-if\", true)]),\n    _: 1\n  })], 42, [\"aria-expanded\", \"aria-disabled\", \"aria-checked\", \"draggable\", \"data-key\", \"onClick\", \"onContextmenu\", \"onDragstart\", \"onDragover\", \"onDragend\", \"onDrop\"])), [[vShow, _ctx.node.visible]]);\n}\nvar ElTreeNode = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"tree-node.vue\"]]);\nexport { ElTreeNode as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}