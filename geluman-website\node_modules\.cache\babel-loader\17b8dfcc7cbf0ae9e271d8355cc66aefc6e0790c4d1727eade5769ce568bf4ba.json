{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nconst MAP_KEY_TO_FOCUS_INTENT = {\n  ArrowLeft: \"prev\",\n  ArrowUp: \"prev\",\n  ArrowRight: \"next\",\n  ArrowDown: \"next\",\n  PageUp: \"first\",\n  Home: \"first\",\n  PageDown: \"last\",\n  End: \"last\"\n};\nconst getDirectionAwareKey = (key, dir) => {\n  if (dir !== \"rtl\") return key;\n  switch (key) {\n    case EVENT_CODE.right:\n      return EVENT_CODE.left;\n    case EVENT_CODE.left:\n      return EVENT_CODE.right;\n    default:\n      return key;\n  }\n};\nconst getFocusIntent = (event, orientation, dir) => {\n  const key = getDirectionAwareKey(event.code, dir);\n  if (orientation === \"vertical\" && [EVENT_CODE.left, EVENT_CODE.right].includes(key)) return void 0;\n  if (orientation === \"horizontal\" && [EVENT_CODE.up, EVENT_CODE.down].includes(key)) return void 0;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n};\nconst reorderArray = (array, atIdx) => {\n  return array.map((_, idx) => array[(idx + atIdx) % array.length]);\n};\nconst focusFirst = elements => {\n  const {\n    activeElement: prevActive\n  } = document;\n  for (const element of elements) {\n    if (element === prevActive) return;\n    element.focus();\n    if (prevActive !== document.activeElement) return;\n  }\n};\nexport { focusFirst, getFocusIntent, reorderArray };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}