{"ast": null, "code": "import eq from './eq.js';\n\n/**\n * The base implementation of `_.sortedUniq` and `_.sortedUniqBy` without\n * support for iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @returns {Array} Returns the new duplicate free array.\n */\nfunction baseSortedUniq(array, iteratee) {\n  var index = -1,\n    length = array.length,\n    resIndex = 0,\n    result = [];\n  while (++index < length) {\n    var value = array[index],\n      computed = iteratee ? iteratee(value) : value;\n    if (!index || !eq(computed, seen)) {\n      var seen = computed;\n      result[resIndex++] = value === 0 ? 0 : value;\n    }\n  }\n  return result;\n}\nexport default baseSortedUniq;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}