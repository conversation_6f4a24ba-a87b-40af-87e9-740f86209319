{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { inject, computed, getCurrentInstance, toRaw, watch } from 'vue';\nimport { castArray, get, isEqual } from 'lodash-unified';\nimport { selectKey, selectGroupKey } from './token.mjs';\nimport { COMPONENT_NAME } from './option.mjs';\nimport { escapeStringRegexp } from '../../../utils/strings.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nimport { isObject } from '@vue/shared';\nfunction useOption(props, states) {\n  const select = inject(selectKey);\n  if (!select) {\n    throwError(COMPONENT_NAME, \"usage: <el-select><el-option /></el-select/>\");\n  }\n  const selectGroup = inject(selectGroupKey, {\n    disabled: false\n  });\n  const itemSelected = computed(() => {\n    return contains(castArray(select.props.modelValue), props.value);\n  });\n  const limitReached = computed(() => {\n    var _a;\n    if (select.props.multiple) {\n      const modelValue = castArray((_a = select.props.modelValue) != null ? _a : []);\n      return !itemSelected.value && modelValue.length >= select.props.multipleLimit && select.props.multipleLimit > 0;\n    } else {\n      return false;\n    }\n  });\n  const currentLabel = computed(() => {\n    return props.label || (isObject(props.value) ? \"\" : props.value);\n  });\n  const currentValue = computed(() => {\n    return props.value || props.label || \"\";\n  });\n  const isDisabled = computed(() => {\n    return props.disabled || states.groupDisabled || limitReached.value;\n  });\n  const instance = getCurrentInstance();\n  const contains = (arr = [], target) => {\n    if (!isObject(props.value)) {\n      return arr && arr.includes(target);\n    } else {\n      const valueKey = select.props.valueKey;\n      return arr && arr.some(item => {\n        return toRaw(get(item, valueKey)) === get(target, valueKey);\n      });\n    }\n  };\n  const hoverItem = () => {\n    if (!props.disabled && !selectGroup.disabled) {\n      select.states.hoveringIndex = select.optionsArray.indexOf(instance.proxy);\n    }\n  };\n  const updateOption = query => {\n    const regexp = new RegExp(escapeStringRegexp(query), \"i\");\n    states.visible = regexp.test(String(currentLabel.value)) || props.created;\n  };\n  watch(() => currentLabel.value, () => {\n    if (!props.created && !select.props.remote) select.setSelected();\n  });\n  watch(() => props.value, (val, oldVal) => {\n    const {\n      remote,\n      valueKey\n    } = select.props;\n    const shouldUpdate = remote ? val !== oldVal : !isEqual(val, oldVal);\n    if (shouldUpdate) {\n      select.onOptionDestroy(oldVal, instance.proxy);\n      select.onOptionCreate(instance.proxy);\n    }\n    if (!props.created && !remote) {\n      if (valueKey && isObject(val) && isObject(oldVal) && val[valueKey] === oldVal[valueKey]) {\n        return;\n      }\n      select.setSelected();\n    }\n  });\n  watch(() => selectGroup.disabled, () => {\n    states.groupDisabled = selectGroup.disabled;\n  }, {\n    immediate: true\n  });\n  return {\n    select,\n    currentLabel,\n    currentValue,\n    itemSelected,\n    isDisabled,\n    hoverItem,\n    updateOption\n  };\n}\nexport { useOption };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}