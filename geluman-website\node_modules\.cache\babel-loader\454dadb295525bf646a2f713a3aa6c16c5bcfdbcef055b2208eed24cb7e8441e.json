{"ast": null, "code": "import { getCurrentInstance, inject, ref, unref, watch } from 'vue';\nimport { isValidRange, getDefaultValue } from '../utils.mjs';\nimport { ROOT_PICKER_INJECTION_KEY } from '../constants.mjs';\nimport { useShortcut } from './use-shortcut.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { isArray } from '@vue/shared';\nconst useRangePicker = (props, {\n  defaultValue,\n  leftDate,\n  rightDate,\n  unit,\n  onParsedValueChanged\n}) => {\n  const {\n    emit\n  } = getCurrentInstance();\n  const {\n    pickerNs\n  } = inject(ROOT_PICKER_INJECTION_KEY);\n  const drpNs = useNamespace(\"date-range-picker\");\n  const {\n    t,\n    lang\n  } = useLocale();\n  const handleShortcutClick = useShortcut(lang);\n  const minDate = ref();\n  const maxDate = ref();\n  const rangeState = ref({\n    endDate: null,\n    selecting: false\n  });\n  const handleChangeRange = val => {\n    rangeState.value = val;\n  };\n  const handleRangeConfirm = (visible = false) => {\n    const _minDate = unref(minDate);\n    const _maxDate = unref(maxDate);\n    if (isValidRange([_minDate, _maxDate])) {\n      emit(\"pick\", [_minDate, _maxDate], visible);\n    }\n  };\n  const onSelect = selecting => {\n    rangeState.value.selecting = selecting;\n    if (!selecting) {\n      rangeState.value.endDate = null;\n    }\n  };\n  const onReset = parsedValue => {\n    if (isArray(parsedValue) && parsedValue.length === 2) {\n      const [start, end] = parsedValue;\n      minDate.value = start;\n      leftDate.value = start;\n      maxDate.value = end;\n      onParsedValueChanged(unref(minDate), unref(maxDate));\n    } else {\n      restoreDefault();\n    }\n  };\n  const restoreDefault = () => {\n    const [start, end] = getDefaultValue(unref(defaultValue), {\n      lang: unref(lang),\n      unit,\n      unlinkPanels: props.unlinkPanels\n    });\n    minDate.value = void 0;\n    maxDate.value = void 0;\n    leftDate.value = start;\n    rightDate.value = end;\n  };\n  watch(defaultValue, val => {\n    if (val) {\n      restoreDefault();\n    }\n  }, {\n    immediate: true\n  });\n  watch(() => props.parsedValue, onReset, {\n    immediate: true\n  });\n  return {\n    minDate,\n    maxDate,\n    rangeState,\n    lang,\n    ppNs: pickerNs,\n    drpNs,\n    handleChangeRange,\n    handleRangeConfirm,\n    handleShortcutClick,\n    onSelect,\n    onReset,\n    t\n  };\n};\nexport { useRangePicker };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}