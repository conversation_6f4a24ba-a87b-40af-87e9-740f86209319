{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { defineComponent, ref, onMounted, computed, provide, reactive, toRefs, watch, openBlock, createElementBlock, unref, normalizeClass, renderSlot, nextTick } from 'vue';\nimport { radioGroupProps, radioGroupEmits } from './radio-group.mjs';\nimport { radioGroupKey } from './constants.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { useFormItem, useFormItemInputId } from '../../form/src/hooks/use-form-item.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nconst __default__ = defineComponent({\n  name: \"ElRadioGroup\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: radioGroupProps,\n  emits: radioGroupEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"radio\");\n    const radioId = useId();\n    const radioGroupRef = ref();\n    const {\n      formItem\n    } = useFormItem();\n    const {\n      inputId: groupId,\n      isLabeledByFormItem\n    } = useFormItemInputId(props, {\n      formItemContext: formItem\n    });\n    const changeEvent = value => {\n      emit(UPDATE_MODEL_EVENT, value);\n      nextTick(() => emit(CHANGE_EVENT, value));\n    };\n    onMounted(() => {\n      const radios = radioGroupRef.value.querySelectorAll(\"[type=radio]\");\n      const firstLabel = radios[0];\n      if (!Array.from(radios).some(radio => radio.checked) && firstLabel) {\n        firstLabel.tabIndex = 0;\n      }\n    });\n    const name = computed(() => {\n      return props.name || radioId.value;\n    });\n    provide(radioGroupKey, reactive({\n      ...toRefs(props),\n      changeEvent,\n      name\n    }));\n    watch(() => props.modelValue, () => {\n      if (props.validateEvent) {\n        formItem == null ? void 0 : formItem.validate(\"change\").catch(err => debugWarn(err));\n      }\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        id: unref(groupId),\n        ref_key: \"radioGroupRef\",\n        ref: radioGroupRef,\n        class: normalizeClass(unref(ns).b(\"group\")),\n        role: \"radiogroup\",\n        \"aria-label\": !unref(isLabeledByFormItem) ? _ctx.ariaLabel || \"radio-group\" : void 0,\n        \"aria-labelledby\": unref(isLabeledByFormItem) ? unref(formItem).labelId : void 0\n      }, [renderSlot(_ctx.$slots, \"default\")], 10, [\"id\", \"aria-label\", \"aria-labelledby\"]);\n    };\n  }\n});\nvar RadioGroup = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"radio-group.vue\"]]);\nexport { RadioGroup as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}