{"ast": null, "code": "import { defineComponent, openBlock, createBlock, Transition, mergeProps, unref, toHandlers, withCtx, renderSlot } from 'vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElCollapseTransition\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  setup(__props) {\n    const ns = useNamespace(\"collapse-transition\");\n    const reset = el => {\n      el.style.maxHeight = \"\";\n      el.style.overflow = el.dataset.oldOverflow;\n      el.style.paddingTop = el.dataset.oldPaddingTop;\n      el.style.paddingBottom = el.dataset.oldPaddingBottom;\n    };\n    const on = {\n      beforeEnter(el) {\n        if (!el.dataset) el.dataset = {};\n        el.dataset.oldPaddingTop = el.style.paddingTop;\n        el.dataset.oldPaddingBottom = el.style.paddingBottom;\n        if (el.style.height) el.dataset.elExistsHeight = el.style.height;\n        el.style.maxHeight = 0;\n        el.style.paddingTop = 0;\n        el.style.paddingBottom = 0;\n      },\n      enter(el) {\n        requestAnimationFrame(() => {\n          el.dataset.oldOverflow = el.style.overflow;\n          if (el.dataset.elExistsHeight) {\n            el.style.maxHeight = el.dataset.elExistsHeight;\n          } else if (el.scrollHeight !== 0) {\n            el.style.maxHeight = `${el.scrollHeight}px`;\n          } else {\n            el.style.maxHeight = 0;\n          }\n          el.style.paddingTop = el.dataset.oldPaddingTop;\n          el.style.paddingBottom = el.dataset.oldPaddingBottom;\n          el.style.overflow = \"hidden\";\n        });\n      },\n      afterEnter(el) {\n        el.style.maxHeight = \"\";\n        el.style.overflow = el.dataset.oldOverflow;\n      },\n      enterCancelled(el) {\n        reset(el);\n      },\n      beforeLeave(el) {\n        if (!el.dataset) el.dataset = {};\n        el.dataset.oldPaddingTop = el.style.paddingTop;\n        el.dataset.oldPaddingBottom = el.style.paddingBottom;\n        el.dataset.oldOverflow = el.style.overflow;\n        el.style.maxHeight = `${el.scrollHeight}px`;\n        el.style.overflow = \"hidden\";\n      },\n      leave(el) {\n        if (el.scrollHeight !== 0) {\n          el.style.maxHeight = 0;\n          el.style.paddingTop = 0;\n          el.style.paddingBottom = 0;\n        }\n      },\n      afterLeave(el) {\n        reset(el);\n      },\n      leaveCancelled(el) {\n        reset(el);\n      }\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(Transition, mergeProps({\n        name: unref(ns).b()\n      }, toHandlers(on)), {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 16, [\"name\"]);\n    };\n  }\n});\nvar CollapseTransition = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"collapse-transition.vue\"]]);\nexport { CollapseTransition as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}