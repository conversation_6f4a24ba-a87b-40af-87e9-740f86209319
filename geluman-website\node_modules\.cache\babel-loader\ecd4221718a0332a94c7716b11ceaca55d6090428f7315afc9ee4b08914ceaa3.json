{"ast": null, "code": "import apply from './_apply.js';\nimport arrayMap from './_arrayMap.js';\nimport baseFlatten from './_baseFlatten.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseRest from './_baseRest.js';\nimport baseUnary from './_baseUnary.js';\nimport castRest from './_castRest.js';\nimport isArray from './isArray.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMin = Math.min;\n\n/**\n * Creates a function that invokes `func` with its arguments transformed.\n *\n * @static\n * @since 4.0.0\n * @memberOf _\n * @category Function\n * @param {Function} func The function to wrap.\n * @param {...(Function|Function[])} [transforms=[_.identity]]\n *  The argument transforms.\n * @returns {Function} Returns the new function.\n * @example\n *\n * function doubled(n) {\n *   return n * 2;\n * }\n *\n * function square(n) {\n *   return n * n;\n * }\n *\n * var func = _.overArgs(function(x, y) {\n *   return [x, y];\n * }, [square, doubled]);\n *\n * func(9, 3);\n * // => [81, 6]\n *\n * func(10, 5);\n * // => [100, 10]\n */\nvar overArgs = castRest(function (func, transforms) {\n  transforms = transforms.length == 1 && isArray(transforms[0]) ? arrayMap(transforms[0], baseUnary(baseIteratee)) : arrayMap(baseFlatten(transforms, 1), baseUnary(baseIteratee));\n  var funcsLength = transforms.length;\n  return baseRest(function (args) {\n    var index = -1,\n      length = nativeMin(args.length, funcsLength);\n    while (++index < length) {\n      args[index] = transforms[index].call(this, args[index]);\n    }\n    return apply(func, this, args);\n  });\n});\nexport default overArgs;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}