{"ast": null, "code": "import baseFlatten from './_baseFlatten.js';\nimport map from './map.js';\nimport toInteger from './toInteger.js';\n\n/**\n * This method is like `_.flatMap` except that it recursively flattens the\n * mapped results up to `depth` times.\n *\n * @static\n * @memberOf _\n * @since 4.7.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @param {number} [depth=1] The maximum recursion depth.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * function duplicate(n) {\n *   return [[[n, n]]];\n * }\n *\n * _.flatMapDepth([1, 2], duplicate, 2);\n * // => [[1, 1], [2, 2]]\n */\nfunction flatMapDepth(collection, iteratee, depth) {\n  depth = depth === undefined ? 1 : toInteger(depth);\n  return baseFlatten(map(collection, iteratee), depth);\n}\nexport default flatMapDepth;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}