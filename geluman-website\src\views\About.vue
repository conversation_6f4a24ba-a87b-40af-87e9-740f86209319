<template>
  <div class="about-page">
    <section class="about-hero">
      <div class="about-content">
        <h1 class="title">关于格鲁曼</h1>
        <p class="subtitle">用创新驱动技术，以匠心创造价值</p>
      </div>

      <!-- 添加粒子网格背景 -->
      <div class="hero-background">
        <!-- 科技感粒子 -->
        <div class="tech-particles">
          <span
            v-for="n in 30"
            :key="n"
            class="particle"
            :style="{
              '--delay': `${Math.random() * 5}s`,
              '--size': `${Math.random() * 3 + 1}px`,
              '--x': `${Math.random() * 100}%`,
              '--y': `${Math.random() * 100}%`,
            }"
          ></span>
        </div>
        <!-- 网格背景 -->
        <div class="grid-background"></div>
      </div>
    </section>

    <section class="company-intro">
      <div class="container">
        <div class="intro-grid">
          <div class="intro-text">
            <h2>我们的故事</h2>
            <p>
              成立于2024年，格鲁曼专注于开发浏览器插件和休闲小游戏，致力于为用户提供卓越的在线体验。"匠心创造价值"是我们的核心理念。我们相信，每一个细节都至关重要，只有通过精细打磨和不断优化，才能创造出真正有价值的产品。
            </p>
          </div>
          <div class="intro-decoration">
            <div class="tech-circles">
              <span class="circle circle-1"></span>
              <span class="circle circle-2"></span>
              <span class="circle circle-3"></span>
            </div>
            <div class="code-lines">
              <span class="line"></span>
              <span class="line"></span>
              <span class="line"></span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="values">
      <div class="container">
        <h2 class="section-title">我们的价值观</h2>
        <div class="values-grid">
          <el-card
            v-for="(value, index) in values"
            :key="value.title"
            class="value-card"
          >
            <div class="value-icon">
              <el-icon :size="32"><component :is="value.icon" /></el-icon>
            </div>
            <h3>{{ value.title }}</h3>
            <p>{{ value.description }}</p>
          </el-card>
        </div>
      </div>
    </section>

    <section class="contact">
      <div class="container">
        <h2 class="section-title">联系我们</h2>
        <div class="contact-content">
          <div class="contact-info">
            <div class="info-item">
              <el-icon><Message /></el-icon>
              <span><EMAIL></span>
            </div>
            <div class="info-item">
              <el-icon><Location /></el-icon>
              <span>中国·成都</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: "AboutPage",
};
</script>

<script setup>
import {
  Promotion,
  Medal,
  Cpu,
  User,
  Message,
  Location,
} from "@element-plus/icons-vue";

const values = [
  {
    icon: Promotion,
    title: "创新驱动",
    description: "持续创新，推动技术边界，为用户带来更好的产品体验",
  },
  {
    icon: Medal,
    title: "匠心品质",
    description: "专注细节，精益求精，打造极致用户体验",
  },
  {
    icon: Cpu,
    title: "技术领先",
    description: "拥抱新技术，保持技术敏锐度，引领行业发展",
  },
  {
    icon: User,
    title: "用户至上",
    description: "以用户需求为中心，持续优化产品，提供贴心服务",
  },
];
</script>

<style lang="scss" scoped>
.about-page {
  padding-top: var(--header-height);
}

.about-hero {
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-dark) 100%
  );
  padding: 6rem 0;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;

  .about-content {
    position: relative;
    z-index: 10;
  }

  .title {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.company-intro {
  padding: 6rem 0;
  background: var(--bg-secondary);

  .intro-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 2rem;
    }
  }

  .intro-text {
    h2 {
      font-size: 2rem;
      margin-bottom: 1.5rem;
      color: var(--text-primary);
    }

    p {
      color: var(--text-secondary);
      line-height: 1.8;
    }
  }

  .intro-decoration {
    position: relative;
    height: 300px;

    .tech-circles {
      position: absolute;
      width: 100%;
      height: 100%;

      .circle {
        position: absolute;
        border-radius: 50%;
        background: var(--primary-color);
        opacity: 0.1;
        animation: float 6s infinite ease-in-out;

        &.circle-1 {
          width: 100px;
          height: 100px;
          top: 20%;
          left: 20%;
          animation-delay: 0s;
        }

        &.circle-2 {
          width: 60px;
          height: 60px;
          top: 50%;
          right: 30%;
          animation-delay: -2s;
        }

        &.circle-3 {
          width: 40px;
          height: 40px;
          bottom: 20%;
          left: 40%;
          animation-delay: -4s;
        }
      }
    }

    .code-lines {
      position: absolute;
      width: 100%;
      height: 100%;

      .line {
        position: absolute;
        height: 2px;
        background: linear-gradient(
          90deg,
          var(--primary-color) 0%,
          transparent 100%
        );
        opacity: 0.2;
        animation: slidein 3s infinite ease-in-out;

        &:nth-child(1) {
          width: 60%;
          top: 30%;
          left: -10%;
          animation-delay: 0s;
        }

        &:nth-child(2) {
          width: 40%;
          top: 50%;
          left: -10%;
          animation-delay: -1s;
        }

        &:nth-child(3) {
          width: 50%;
          top: 70%;
          left: -10%;
          animation-delay: -2s;
        }
      }
    }
  }
}

.values {
  padding: 6rem 0;

  .section-title {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 3rem;
  }

  .values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
  }

  .value-card {
    text-align: center;
    padding: 2rem;
    transition: var(--transition-base);

    &:hover {
      transform: translateY(-5px);
    }

    .value-icon {
      margin-bottom: 1.5rem;

      .el-icon {
        font-size: 2.5rem;
        color: var(--primary-color);
      }
    }

    h3 {
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    p {
      color: var(--text-secondary);
    }
  }
}

.contact {
  padding: 6rem 0;
  background: var(--bg-secondary);

  .section-title {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 3rem;
  }

  .contact-content {
    max-width: 600px;
    margin: 0 auto;
  }

  .contact-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;

    .info-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: var(--text-secondary);

      .el-icon {
        color: var(--primary-color);
      }
    }
  }
}

@media (max-width: 768px) {
  .about-hero {
    padding: 4rem 1rem;

    .title {
      font-size: 2.5rem;
    }
  }

  .company-intro,
  .values,
  .contact {
    padding: 4rem 1rem;
  }
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.tech-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 2;

  .particle {
    position: absolute;
    width: var(--size);
    height: var(--size);
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    left: var(--x);
    top: var(--y);
    animation: pulse 2s infinite ease-in-out;
    animation-delay: var(--delay);
  }
}

.grid-background {
  position: absolute;
  width: 800%;
  height: 800%;
  background: linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 50px 50px;
  transform: rotate(45deg);
  top: -350%;
  left: -350%;
  animation: grid-move 50s linear infinite;
  pointer-events: none;
}

.value-card {
  &:hover {
    .value-icon {
      animation: iconPop 0.5s ease;
    }
  }
}

@keyframes particleMove {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
}

@keyframes iconPop {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.2;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.8;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-20px) scale(1.1);
  }
}

@keyframes slidein {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    transform: translateX(0);
    opacity: 0.2;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes grid-move {
  0% {
    transform: translate3d(-5%, -5%, 0) rotate(45deg);
  }
  100% {
    transform: translate3d(-15%, -15%, 0) rotate(45deg);
  }
}
</style>
