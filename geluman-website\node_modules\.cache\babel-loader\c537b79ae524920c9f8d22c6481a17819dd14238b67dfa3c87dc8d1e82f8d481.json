{"ast": null, "code": "import { defineComponent, getCurrentInstance, ref, computed, unref, inject, resolveComponent, openBlock, createBlock, withCtx, createVNode, mergeProps, renderSlot } from 'vue';\nimport ElRovingFocusItem from '../../roving-focus-group/src/roving-focus-item.mjs';\nimport ElDropdownItemImpl from './dropdown-item-impl.mjs';\nimport { useDropdown } from './useDropdown.mjs';\nimport { ElCollectionItem, dropdownItemProps } from './dropdown.mjs';\nimport { DROPDOWN_INJECTION_KEY } from './tokens.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { composeEventHandlers, whenMouse } from '../../../utils/dom/event.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElDropdownItem\",\n  components: {\n    ElDropdownCollectionItem: ElCollectionItem,\n    ElRovingFocusItem,\n    ElDropdownItemImpl\n  },\n  inheritAttrs: false,\n  props: dropdownItemProps,\n  emits: [\"pointermove\", \"pointerleave\", \"click\"],\n  setup(props, {\n    emit,\n    attrs\n  }) {\n    const {\n      elDropdown\n    } = useDropdown();\n    const _instance = getCurrentInstance();\n    const itemRef = ref(null);\n    const textContent = computed(() => {\n      var _a, _b;\n      return (_b = (_a = unref(itemRef)) == null ? void 0 : _a.textContent) != null ? _b : \"\";\n    });\n    const {\n      onItemEnter,\n      onItemLeave\n    } = inject(DROPDOWN_INJECTION_KEY, void 0);\n    const handlePointerMove = composeEventHandlers(e => {\n      emit(\"pointermove\", e);\n      return e.defaultPrevented;\n    }, whenMouse(e => {\n      if (props.disabled) {\n        onItemLeave(e);\n        return;\n      }\n      const target = e.currentTarget;\n      if (target === document.activeElement || target.contains(document.activeElement)) {\n        return;\n      }\n      onItemEnter(e);\n      if (!e.defaultPrevented) {\n        target == null ? void 0 : target.focus();\n      }\n    }));\n    const handlePointerLeave = composeEventHandlers(e => {\n      emit(\"pointerleave\", e);\n      return e.defaultPrevented;\n    }, whenMouse(onItemLeave));\n    const handleClick = composeEventHandlers(e => {\n      if (props.disabled) {\n        return;\n      }\n      emit(\"click\", e);\n      return e.type !== \"keydown\" && e.defaultPrevented;\n    }, e => {\n      var _a, _b, _c;\n      if (props.disabled) {\n        e.stopImmediatePropagation();\n        return;\n      }\n      if ((_a = elDropdown == null ? void 0 : elDropdown.hideOnClick) == null ? void 0 : _a.value) {\n        (_b = elDropdown.handleClick) == null ? void 0 : _b.call(elDropdown);\n      }\n      (_c = elDropdown.commandHandler) == null ? void 0 : _c.call(elDropdown, props.command, _instance, e);\n    });\n    const propsAndAttrs = computed(() => ({\n      ...props,\n      ...attrs\n    }));\n    return {\n      handleClick,\n      handlePointerMove,\n      handlePointerLeave,\n      textContent,\n      propsAndAttrs\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _a;\n  const _component_el_dropdown_item_impl = resolveComponent(\"el-dropdown-item-impl\");\n  const _component_el_roving_focus_item = resolveComponent(\"el-roving-focus-item\");\n  const _component_el_dropdown_collection_item = resolveComponent(\"el-dropdown-collection-item\");\n  return openBlock(), createBlock(_component_el_dropdown_collection_item, {\n    disabled: _ctx.disabled,\n    \"text-value\": (_a = _ctx.textValue) != null ? _a : _ctx.textContent\n  }, {\n    default: withCtx(() => [createVNode(_component_el_roving_focus_item, {\n      focusable: !_ctx.disabled\n    }, {\n      default: withCtx(() => [createVNode(_component_el_dropdown_item_impl, mergeProps(_ctx.propsAndAttrs, {\n        onPointerleave: _ctx.handlePointerLeave,\n        onPointermove: _ctx.handlePointerMove,\n        onClickimpl: _ctx.handleClick\n      }), {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 16, [\"onPointerleave\", \"onPointermove\", \"onClickimpl\"])]),\n      _: 3\n    }, 8, [\"focusable\"])]),\n    _: 3\n  }, 8, [\"disabled\", \"text-value\"]);\n}\nvar DropdownItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"dropdown-item.vue\"]]);\nexport { DropdownItem as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}