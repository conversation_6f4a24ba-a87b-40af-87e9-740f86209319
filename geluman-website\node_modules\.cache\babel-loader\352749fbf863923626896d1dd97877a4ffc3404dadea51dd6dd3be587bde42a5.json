{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { isString, isArray } from '@vue/shared';\nconst menuItemProps = buildProps({\n  index: {\n    type: definePropType([String, null]),\n    default: null\n  },\n  route: {\n    type: definePropType([String, Object])\n  },\n  disabled: Boolean\n});\nconst menuItemEmits = {\n  click: item => isString(item.index) && isArray(item.indexPath)\n};\nexport { menuItemEmits, menuItemProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}