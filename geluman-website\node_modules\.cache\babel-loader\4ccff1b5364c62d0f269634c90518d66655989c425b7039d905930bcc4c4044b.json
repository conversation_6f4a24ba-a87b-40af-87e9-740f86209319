{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, shallowRef, openBlock, createElementBlock, normalizeClass, unref, with<PERSON><PERSON><PERSON>, withModifiers, createBlock, withCtx, renderSlot, createElementVNode } from 'vue';\nimport { cloneDeep, isEqual } from 'lodash-unified';\nimport UploadDragger from './upload-dragger2.mjs';\nimport { uploadContentProps } from './upload-content.mjs';\nimport { genFileId } from './upload.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { entriesOf } from '../../../utils/objects.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { isPlainObject, isFunction } from '@vue/shared';\nconst __default__ = defineComponent({\n  name: \"ElUploadContent\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: uploadContentProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"upload\");\n    const disabled = useFormDisabled();\n    const requests = shallowRef({});\n    const inputRef = shallowRef();\n    const uploadFiles = files => {\n      if (files.length === 0) return;\n      const {\n        autoUpload,\n        limit,\n        fileList,\n        multiple,\n        onStart,\n        onExceed\n      } = props;\n      if (limit && fileList.length + files.length > limit) {\n        onExceed(files, fileList);\n        return;\n      }\n      if (!multiple) {\n        files = files.slice(0, 1);\n      }\n      for (const file of files) {\n        const rawFile = file;\n        rawFile.uid = genFileId();\n        onStart(rawFile);\n        if (autoUpload) upload(rawFile);\n      }\n    };\n    const upload = async rawFile => {\n      inputRef.value.value = \"\";\n      if (!props.beforeUpload) {\n        return doUpload(rawFile);\n      }\n      let hookResult;\n      let beforeData = {};\n      try {\n        const originData = props.data;\n        const beforeUploadPromise = props.beforeUpload(rawFile);\n        beforeData = isPlainObject(props.data) ? cloneDeep(props.data) : props.data;\n        hookResult = await beforeUploadPromise;\n        if (isPlainObject(props.data) && isEqual(originData, beforeData)) {\n          beforeData = cloneDeep(props.data);\n        }\n      } catch (e) {\n        hookResult = false;\n      }\n      if (hookResult === false) {\n        props.onRemove(rawFile);\n        return;\n      }\n      let file = rawFile;\n      if (hookResult instanceof Blob) {\n        if (hookResult instanceof File) {\n          file = hookResult;\n        } else {\n          file = new File([hookResult], rawFile.name, {\n            type: rawFile.type\n          });\n        }\n      }\n      doUpload(Object.assign(file, {\n        uid: rawFile.uid\n      }), beforeData);\n    };\n    const resolveData = async (data, rawFile) => {\n      if (isFunction(data)) {\n        return data(rawFile);\n      }\n      return data;\n    };\n    const doUpload = async (rawFile, beforeData) => {\n      const {\n        headers,\n        data,\n        method,\n        withCredentials,\n        name: filename,\n        action,\n        onProgress,\n        onSuccess,\n        onError,\n        httpRequest\n      } = props;\n      try {\n        beforeData = await resolveData(beforeData != null ? beforeData : data, rawFile);\n      } catch (e) {\n        props.onRemove(rawFile);\n        return;\n      }\n      const {\n        uid\n      } = rawFile;\n      const options = {\n        headers: headers || {},\n        withCredentials,\n        file: rawFile,\n        data: beforeData,\n        method,\n        filename,\n        action,\n        onProgress: evt => {\n          onProgress(evt, rawFile);\n        },\n        onSuccess: res => {\n          onSuccess(res, rawFile);\n          delete requests.value[uid];\n        },\n        onError: err => {\n          onError(err, rawFile);\n          delete requests.value[uid];\n        }\n      };\n      const request = httpRequest(options);\n      requests.value[uid] = request;\n      if (request instanceof Promise) {\n        request.then(options.onSuccess, options.onError);\n      }\n    };\n    const handleChange = e => {\n      const files = e.target.files;\n      if (!files) return;\n      uploadFiles(Array.from(files));\n    };\n    const handleClick = () => {\n      if (!disabled.value) {\n        inputRef.value.value = \"\";\n        inputRef.value.click();\n      }\n    };\n    const handleKeydown = () => {\n      handleClick();\n    };\n    const abort = file => {\n      const _reqs = entriesOf(requests.value).filter(file ? ([uid]) => String(file.uid) === uid : () => true);\n      _reqs.forEach(([uid, req]) => {\n        if (req instanceof XMLHttpRequest) req.abort();\n        delete requests.value[uid];\n      });\n    };\n    expose({\n      abort,\n      upload\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ns).b(), unref(ns).m(_ctx.listType), unref(ns).is(\"drag\", _ctx.drag), unref(ns).is(\"disabled\", unref(disabled))]),\n        tabindex: unref(disabled) ? \"-1\" : \"0\",\n        onClick: handleClick,\n        onKeydown: withKeys(withModifiers(handleKeydown, [\"self\"]), [\"enter\", \"space\"])\n      }, [_ctx.drag ? (openBlock(), createBlock(UploadDragger, {\n        key: 0,\n        disabled: unref(disabled),\n        onFile: uploadFiles\n      }, {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 8, [\"disabled\"])) : renderSlot(_ctx.$slots, \"default\", {\n        key: 1\n      }), createElementVNode(\"input\", {\n        ref_key: \"inputRef\",\n        ref: inputRef,\n        class: normalizeClass(unref(ns).e(\"input\")),\n        name: _ctx.name,\n        disabled: unref(disabled),\n        multiple: _ctx.multiple,\n        accept: _ctx.accept,\n        type: \"file\",\n        onChange: handleChange,\n        onClick: withModifiers(() => {}, [\"stop\"])\n      }, null, 42, [\"name\", \"disabled\", \"multiple\", \"accept\", \"onClick\"])], 42, [\"tabindex\", \"onKeydown\"]);\n    };\n  }\n});\nvar UploadContent = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"upload-content.vue\"]]);\nexport { UploadContent as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}