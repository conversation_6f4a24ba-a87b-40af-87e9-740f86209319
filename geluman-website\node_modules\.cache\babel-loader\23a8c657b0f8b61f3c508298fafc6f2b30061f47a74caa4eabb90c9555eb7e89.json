{"ast": null, "code": "import { defineComponent, computed, h, renderSlot } from 'vue';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst spaceItemProps = buildProps({\n  prefixCls: {\n    type: String\n  }\n});\nconst SpaceItem = defineComponent({\n  name: \"ElSpaceItem\",\n  props: spaceItemProps,\n  setup(props, {\n    slots\n  }) {\n    const ns = useNamespace(\"space\");\n    const classes = computed(() => `${props.prefixCls || ns.b()}__item`);\n    return () => h(\"div\", {\n      class: classes.value\n    }, renderSlot(slots, \"default\"));\n  }\n});\nexport { SpaceItem as default, spaceItemProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}