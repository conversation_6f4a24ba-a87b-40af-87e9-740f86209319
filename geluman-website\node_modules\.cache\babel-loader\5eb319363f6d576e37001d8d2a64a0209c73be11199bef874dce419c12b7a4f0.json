{"ast": null, "code": "import { createElementVNode as _createElementVNode, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"not-found\"\n};\nconst _hoisted_2 = {\n  class: \"content\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[1] || (_cache[1] = _createElementVNode(\"h1\", {\n    class: \"title\"\n  }, \"404\", -1 /* HOISTED */)), _cache[2] || (_cache[2] = _createElementVNode(\"p\", {\n    class: \"subtitle\"\n  }, \"抱歉，您访问的页面不存在\", -1 /* HOISTED */)), _createVNode(_component_router_link, {\n    to: \"/\",\n    class: \"back-home\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode($setup[\"HomeFilled\"])]),\n      _: 1 /* STABLE */\n    }), _cache[0] || (_cache[0] = _createTextVNode(\" 返回首页 \"))]),\n    _: 1 /* STABLE */\n  })]), _cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n    class: \"background-animation\"\n  }, null, -1 /* HOISTED */))]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_router_link", "to", "default", "_withCtx", "_component_el_icon", "$setup", "_", "_createTextVNode"], "sources": ["D:\\codes\\glmwebsite\\geluman-website\\src\\views\\NotFound.vue"], "sourcesContent": ["<template>\r\n  <div class=\"not-found\">\r\n    <div class=\"content\">\r\n      <h1 class=\"title\">404</h1>\r\n      <p class=\"subtitle\">抱歉，您访问的页面不存在</p>\r\n      <router-link to=\"/\" class=\"back-home\">\r\n        <el-icon><HomeFilled /></el-icon>\r\n        返回首页\r\n      </router-link>\r\n    </div>\r\n    <div class=\"background-animation\"></div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { HomeFilled } from \"@element-plus/icons-vue\";\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.not-found {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: var(--bg-secondary);\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  .content {\r\n    text-align: center;\r\n    z-index: 1;\r\n  }\r\n\r\n  .title {\r\n    font-size: 8rem;\r\n    color: var(--primary-color);\r\n    margin-bottom: 1rem;\r\n    font-weight: 700;\r\n    text-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 1.5rem;\r\n    color: var(--text-secondary);\r\n    margin-bottom: 2rem;\r\n  }\r\n\r\n  .back-home {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    gap: 0.5rem;\r\n    padding: 0.8rem 1.5rem;\r\n    background: var(--primary-color);\r\n    color: white;\r\n    text-decoration: none;\r\n    border-radius: 30px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\r\n    }\r\n  }\r\n\r\n  .background-animation {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(\r\n        45deg,\r\n        transparent 45%,\r\n        rgba(99, 102, 241, 0.03) 50%,\r\n        transparent 55%\r\n      ),\r\n      linear-gradient(\r\n        -45deg,\r\n        transparent 45%,\r\n        rgba(99, 102, 241, 0.03) 50%,\r\n        transparent 55%\r\n      );\r\n    background-size: 60px 60px;\r\n    animation: moveBackground 20s linear infinite;\r\n  }\r\n}\r\n\r\n@keyframes moveBackground {\r\n  0% {\r\n    background-position: 0 0;\r\n  }\r\n  100% {\r\n    background-position: 60px 60px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .not-found {\r\n    .title {\r\n      font-size: 6rem;\r\n    }\r\n\r\n    .subtitle {\r\n      font-size: 1.2rem;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAS;;;;uBADtBC,mBAAA,CAUM,OAVNC,UAUM,GATJC,mBAAA,CAOM,OAPNC,UAOM,G,0BANJD,mBAAA,CAA0B;IAAtBH,KAAK,EAAC;EAAO,GAAC,KAAG,sB,0BACrBG,mBAAA,CAAoC;IAAjCH,KAAK,EAAC;EAAU,GAAC,cAAY,sBAChCK,YAAA,CAGcC,sBAAA;IAHDC,EAAE,EAAC,GAAG;IAACP,KAAK,EAAC;;IALhCQ,OAAA,EAAAC,QAAA,CAMQ,MAAiC,CAAjCJ,YAAA,CAAiCK,kBAAA;MANzCF,OAAA,EAAAC,QAAA,CAMiB,MAAc,CAAdJ,YAAA,CAAcM,MAAA,gB;MAN/BC,CAAA;kCAAAC,gBAAA,CAMyC,QAEnC,G;IARND,CAAA;kCAUIT,mBAAA,CAAwC;IAAnCH,KAAK,EAAC;EAAsB,4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}