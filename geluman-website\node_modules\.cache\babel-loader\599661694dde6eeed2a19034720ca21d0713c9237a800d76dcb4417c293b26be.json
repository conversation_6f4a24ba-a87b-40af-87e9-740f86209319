{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { toRefs, computed, nextTick, watch } from 'vue';\nimport { pick, isEqual, isNil } from 'lodash-unified';\nimport { ElTree } from '../../tree/index.mjs';\nimport component from './tree-select-option.mjs';\nimport { treeEach, toValidArray, treeFind, isValidValue, isValidArray } from './utils.mjs';\nimport { escapeStringRegexp } from '../../../utils/strings.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { isFunction } from '@vue/shared';\nimport { isEmpty } from '../../../utils/types.mjs';\nconst useTree = (props, {\n  attrs,\n  slots,\n  emit\n}, {\n  select,\n  tree,\n  key\n}) => {\n  watch(() => props.modelValue, () => {\n    if (props.showCheckbox) {\n      nextTick(() => {\n        const treeInstance = tree.value;\n        if (treeInstance && !isEqual(treeInstance.getCheckedKeys(), toValidArray(props.modelValue))) {\n          treeInstance.setCheckedKeys(toValidArray(props.modelValue));\n        }\n      });\n    }\n  }, {\n    immediate: true,\n    deep: true\n  });\n  const propsMap = computed(() => ({\n    value: key.value,\n    label: \"label\",\n    children: \"children\",\n    disabled: \"disabled\",\n    isLeaf: \"isLeaf\",\n    ...props.props\n  }));\n  const getNodeValByProp = (prop, data) => {\n    var _a;\n    const propVal = propsMap.value[prop];\n    if (isFunction(propVal)) {\n      return propVal(data, (_a = tree.value) == null ? void 0 : _a.getNode(getNodeValByProp(\"value\", data)));\n    } else {\n      return data[propVal];\n    }\n  };\n  const defaultExpandedParentKeys = toValidArray(props.modelValue).map(value => {\n    return treeFind(props.data || [], data => getNodeValByProp(\"value\", data) === value, data => getNodeValByProp(\"children\", data), (data, index, array, parent) => parent && getNodeValByProp(\"value\", parent));\n  }).filter(item => isValidValue(item));\n  const cacheOptions = computed(() => {\n    if (!props.renderAfterExpand && !props.lazy) return [];\n    const options = [];\n    treeEach(props.data.concat(props.cacheData), node => {\n      const value = getNodeValByProp(\"value\", node);\n      options.push({\n        value,\n        currentLabel: getNodeValByProp(\"label\", node),\n        isDisabled: getNodeValByProp(\"disabled\", node)\n      });\n    }, data => getNodeValByProp(\"children\", data));\n    return options;\n  });\n  const getChildCheckedKeys = () => {\n    var _a;\n    return (_a = tree.value) == null ? void 0 : _a.getCheckedKeys().filter(checkedKey => {\n      var _a2;\n      const node = (_a2 = tree.value) == null ? void 0 : _a2.getNode(checkedKey);\n      return !isNil(node) && isEmpty(node.childNodes);\n    });\n  };\n  return {\n    ...pick(toRefs(props), Object.keys(ElTree.props)),\n    ...attrs,\n    nodeKey: key,\n    expandOnClickNode: computed(() => {\n      return !props.checkStrictly && props.expandOnClickNode;\n    }),\n    defaultExpandedKeys: computed(() => {\n      return props.defaultExpandedKeys ? props.defaultExpandedKeys.concat(defaultExpandedParentKeys) : defaultExpandedParentKeys;\n    }),\n    renderContent: (h, {\n      node,\n      data,\n      store\n    }) => {\n      return h(component, {\n        value: getNodeValByProp(\"value\", data),\n        label: getNodeValByProp(\"label\", data),\n        disabled: getNodeValByProp(\"disabled\", data),\n        visible: node.visible\n      }, props.renderContent ? () => props.renderContent(h, {\n        node,\n        data,\n        store\n      }) : slots.default ? () => slots.default({\n        node,\n        data,\n        store\n      }) : void 0);\n    },\n    filterNodeMethod: (value, data, node) => {\n      if (props.filterNodeMethod) return props.filterNodeMethod(value, data, node);\n      if (!value) return true;\n      const regexp = new RegExp(escapeStringRegexp(value), \"i\");\n      return regexp.test(getNodeValByProp(\"label\", data) || \"\");\n    },\n    onNodeClick: (data, node, e) => {\n      var _a, _b, _c, _d;\n      (_a = attrs.onNodeClick) == null ? void 0 : _a.call(attrs, data, node, e);\n      if (props.showCheckbox && props.checkOnClickNode) return;\n      if (!props.showCheckbox && (props.checkStrictly || node.isLeaf)) {\n        if (!getNodeValByProp(\"disabled\", data)) {\n          const option = (_b = select.value) == null ? void 0 : _b.states.options.get(getNodeValByProp(\"value\", data));\n          (_c = select.value) == null ? void 0 : _c.handleOptionSelect(option);\n        }\n      } else if (props.expandOnClickNode) {\n        e.proxy.handleExpandIconClick();\n      }\n      (_d = select.value) == null ? void 0 : _d.focus();\n    },\n    onCheck: (data, params) => {\n      var _a;\n      if (!props.showCheckbox) return;\n      const dataValue = getNodeValByProp(\"value\", data);\n      const dataMap = {};\n      treeEach([tree.value.store.root], node => dataMap[node.key] = node, node => node.childNodes);\n      const uncachedCheckedKeys = params.checkedKeys;\n      const cachedKeys = props.multiple ? toValidArray(props.modelValue).filter(item => !(item in dataMap) && !uncachedCheckedKeys.includes(item)) : [];\n      const checkedKeys = cachedKeys.concat(uncachedCheckedKeys);\n      if (props.checkStrictly) {\n        emit(UPDATE_MODEL_EVENT, props.multiple ? checkedKeys : checkedKeys.includes(dataValue) ? dataValue : void 0);\n      } else {\n        if (props.multiple) {\n          const childKeys = getChildCheckedKeys();\n          emit(UPDATE_MODEL_EVENT, cachedKeys.concat(childKeys));\n        } else {\n          const firstLeaf = treeFind([data], data2 => !isValidArray(getNodeValByProp(\"children\", data2)) && !getNodeValByProp(\"disabled\", data2), data2 => getNodeValByProp(\"children\", data2));\n          const firstLeafKey = firstLeaf ? getNodeValByProp(\"value\", firstLeaf) : void 0;\n          const hasCheckedChild = isValidValue(props.modelValue) && !!treeFind([data], data2 => getNodeValByProp(\"value\", data2) === props.modelValue, data2 => getNodeValByProp(\"children\", data2));\n          emit(UPDATE_MODEL_EVENT, firstLeafKey === props.modelValue || hasCheckedChild ? void 0 : firstLeafKey);\n        }\n      }\n      nextTick(() => {\n        var _a2;\n        const checkedKeys2 = toValidArray(props.modelValue);\n        tree.value.setCheckedKeys(checkedKeys2);\n        (_a2 = attrs.onCheck) == null ? void 0 : _a2.call(attrs, data, {\n          checkedKeys: tree.value.getCheckedKeys(),\n          checkedNodes: tree.value.getCheckedNodes(),\n          halfCheckedKeys: tree.value.getHalfCheckedKeys(),\n          halfCheckedNodes: tree.value.getHalfCheckedNodes()\n        });\n      });\n      (_a = select.value) == null ? void 0 : _a.focus();\n    },\n    onNodeExpand: (data, node, e) => {\n      var _a;\n      (_a = attrs.onNodeExpand) == null ? void 0 : _a.call(attrs, data, node, e);\n      nextTick(() => {\n        if (!props.checkStrictly && props.lazy && props.multiple && node.checked) {\n          const dataMap = {};\n          const uncachedCheckedKeys = tree.value.getCheckedKeys();\n          treeEach([tree.value.store.root], node2 => dataMap[node2.key] = node2, node2 => node2.childNodes);\n          const cachedKeys = toValidArray(props.modelValue).filter(item => !(item in dataMap) && !uncachedCheckedKeys.includes(item));\n          const childKeys = getChildCheckedKeys();\n          emit(UPDATE_MODEL_EVENT, cachedKeys.concat(childKeys));\n        }\n      });\n    },\n    cacheOptions\n  };\n};\nexport { useTree };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}