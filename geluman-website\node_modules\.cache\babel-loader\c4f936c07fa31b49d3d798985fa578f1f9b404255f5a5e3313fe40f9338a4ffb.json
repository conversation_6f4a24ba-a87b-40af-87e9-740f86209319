{"ast": null, "code": "import { defineComponent, inject, computed, openBlock, createElementBlock, unref, normalizeClass, normalizeStyle, createElementVNode, renderSlot, toDisplayString, createVNode, withCtx, createBlock, resolveDynamicComponent, createCommentVNode } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { dialogInjectionKey } from './constants.mjs';\nimport { dialogContentProps, dialogContentEmits } from './dialog-content.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { FOCUS_TRAP_INJECTION_KEY } from '../../focus-trap/src/tokens.mjs';\nimport { useDraggable } from '../../../hooks/use-draggable/index.mjs';\nimport { CloseComponents } from '../../../utils/vue/icon.mjs';\nimport { composeRefs } from '../../../utils/vue/refs.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElDialogContent\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: dialogContentProps,\n  emits: dialogContentEmits,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const {\n      Close\n    } = CloseComponents;\n    const {\n      dialogRef,\n      headerRef,\n      bodyId,\n      ns,\n      style\n    } = inject(dialogInjectionKey);\n    const {\n      focusTrapRef\n    } = inject(FOCUS_TRAP_INJECTION_KEY);\n    const dialogKls = computed(() => [ns.b(), ns.is(\"fullscreen\", props.fullscreen), ns.is(\"draggable\", props.draggable), ns.is(\"align-center\", props.alignCenter), {\n      [ns.m(\"center\")]: props.center\n    }]);\n    const composedDialogRef = composeRefs(focusTrapRef, dialogRef);\n    const draggable = computed(() => props.draggable);\n    const overflow = computed(() => props.overflow);\n    const {\n      resetPosition,\n      updatePosition\n    } = useDraggable(dialogRef, headerRef, draggable, overflow);\n    expose({\n      resetPosition,\n      updatePosition\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref: unref(composedDialogRef),\n        class: normalizeClass(unref(dialogKls)),\n        style: normalizeStyle(unref(style)),\n        tabindex: \"-1\"\n      }, [createElementVNode(\"header\", {\n        ref_key: \"headerRef\",\n        ref: headerRef,\n        class: normalizeClass([unref(ns).e(\"header\"), _ctx.headerClass, {\n          \"show-close\": _ctx.showClose\n        }])\n      }, [renderSlot(_ctx.$slots, \"header\", {}, () => [createElementVNode(\"span\", {\n        role: \"heading\",\n        \"aria-level\": _ctx.ariaLevel,\n        class: normalizeClass(unref(ns).e(\"title\"))\n      }, toDisplayString(_ctx.title), 11, [\"aria-level\"])]), _ctx.showClose ? (openBlock(), createElementBlock(\"button\", {\n        key: 0,\n        \"aria-label\": unref(t)(\"el.dialog.close\"),\n        class: normalizeClass(unref(ns).e(\"headerbtn\")),\n        type: \"button\",\n        onClick: $event => _ctx.$emit(\"close\")\n      }, [createVNode(unref(ElIcon), {\n        class: normalizeClass(unref(ns).e(\"close\"))\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.closeIcon || unref(Close))))]),\n        _: 1\n      }, 8, [\"class\"])], 10, [\"aria-label\", \"onClick\"])) : createCommentVNode(\"v-if\", true)], 2), createElementVNode(\"div\", {\n        id: unref(bodyId),\n        class: normalizeClass([unref(ns).e(\"body\"), _ctx.bodyClass])\n      }, [renderSlot(_ctx.$slots, \"default\")], 10, [\"id\"]), _ctx.$slots.footer ? (openBlock(), createElementBlock(\"footer\", {\n        key: 0,\n        class: normalizeClass([unref(ns).e(\"footer\"), _ctx.footerClass])\n      }, [renderSlot(_ctx.$slots, \"footer\")], 2)) : createCommentVNode(\"v-if\", true)], 6);\n    };\n  }\n});\nvar ElDialogContent = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"dialog-content.vue\"]]);\nexport { ElDialogContent as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}