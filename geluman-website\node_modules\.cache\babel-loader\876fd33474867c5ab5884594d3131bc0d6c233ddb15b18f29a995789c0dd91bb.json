{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { rangeArr } from '../../time-picker/src/utils.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { isObject } from '@vue/shared';\nconst getPrevMonthLastDays = (date, count) => {\n  const lastDay = date.subtract(1, \"month\").endOf(\"month\").date();\n  return rangeArr(count).map((_, index) => lastDay - (count - index - 1));\n};\nconst getMonthDays = date => {\n  const days = date.daysInMonth();\n  return rangeArr(days).map((_, index) => index + 1);\n};\nconst toNestedArr = days => rangeArr(days.length / 7).map(index => {\n  const start = index * 7;\n  return days.slice(start, start + 7);\n});\nconst dateTableProps = buildProps({\n  selectedDay: {\n    type: definePropType(Object)\n  },\n  range: {\n    type: definePropType(Array)\n  },\n  date: {\n    type: definePropType(Object),\n    required: true\n  },\n  hideHeader: {\n    type: Boolean\n  }\n});\nconst dateTableEmits = {\n  pick: value => isObject(value)\n};\nexport { dateTableEmits, dateTableProps, getMonthDays, getPrevMonthLastDays, toNestedArr };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}