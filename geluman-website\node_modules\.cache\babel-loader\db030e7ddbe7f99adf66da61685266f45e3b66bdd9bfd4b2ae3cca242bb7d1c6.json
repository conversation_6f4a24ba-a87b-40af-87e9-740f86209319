{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define(t) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_advancedFormat = t();\n}(this, function () {\n  \"use strict\";\n\n  return function (e, t) {\n    var r = t.prototype,\n      n = r.format;\n    r.format = function (e) {\n      var t = this,\n        r = this.$locale();\n      if (!this.isValid()) return n.bind(this)(e);\n      var s = this.$utils(),\n        a = (e || \"YYYY-MM-DDTHH:mm:ssZ\").replace(/\\[([^\\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g, function (e) {\n          switch (e) {\n            case \"Q\":\n              return Math.ceil((t.$M + 1) / 3);\n            case \"Do\":\n              return r.ordinal(t.$D);\n            case \"gggg\":\n              return t.weekYear();\n            case \"GGGG\":\n              return t.isoWeekYear();\n            case \"wo\":\n              return r.ordinal(t.week(), \"W\");\n            case \"w\":\n            case \"ww\":\n              return s.s(t.week(), \"w\" === e ? 1 : 2, \"0\");\n            case \"W\":\n            case \"WW\":\n              return s.s(t.isoWeek(), \"W\" === e ? 1 : 2, \"0\");\n            case \"k\":\n            case \"kk\":\n              return s.s(String(0 === t.$H ? 24 : t.$H), \"k\" === e ? 1 : 2, \"0\");\n            case \"X\":\n              return Math.floor(t.$d.getTime() / 1e3);\n            case \"x\":\n              return t.$d.getTime();\n            case \"z\":\n              return \"[\" + t.offsetName() + \"]\";\n            case \"zzz\":\n              return \"[\" + t.offsetName(\"long\") + \"]\";\n            default:\n              return e;\n          }\n        });\n      return n.bind(this)(a);\n    };\n  };\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}