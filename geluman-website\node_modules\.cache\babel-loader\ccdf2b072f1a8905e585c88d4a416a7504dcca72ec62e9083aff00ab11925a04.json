{"ast": null, "code": "import baseRepeat from './_baseRepeat.js';\nimport baseToString from './_baseToString.js';\nimport castSlice from './_castSlice.js';\nimport hasUnicode from './_hasUnicode.js';\nimport stringSize from './_stringSize.js';\nimport stringToArray from './_stringToArray.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil;\n\n/**\n * Creates the padding for `string` based on `length`. The `chars` string\n * is truncated if the number of characters exceeds `length`.\n *\n * @private\n * @param {number} length The padding length.\n * @param {string} [chars=' '] The string used as padding.\n * @returns {string} Returns the padding for `string`.\n */\nfunction createPadding(length, chars) {\n  chars = chars === undefined ? ' ' : baseToString(chars);\n  var charsLength = chars.length;\n  if (charsLength < 2) {\n    return charsLength ? baseRepeat(chars, length) : chars;\n  }\n  var result = baseRepeat(chars, nativeCeil(length / stringSize(chars)));\n  return hasUnicode(chars) ? castSlice(stringToArray(result), 0, length).join('') : result.slice(0, length);\n}\nexport default createPadding;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}