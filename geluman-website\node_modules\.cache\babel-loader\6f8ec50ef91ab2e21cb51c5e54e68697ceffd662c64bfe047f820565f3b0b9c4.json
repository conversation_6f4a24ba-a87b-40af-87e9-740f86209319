{"ast": null, "code": "import message from './src/method.mjs';\nexport { messageDefaults, messageEmits, messageProps, messageTypes } from './src/message.mjs';\nimport { withInstallFunction } from '../../utils/vue/install.mjs';\nconst ElMessage = withInstallFunction(message, \"$message\");\nexport { ElMessage, ElMessage as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}