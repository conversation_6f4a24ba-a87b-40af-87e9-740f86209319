{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { defineComponent, inject, ref, computed, watch, unref, createVNode, mergeProps, toRaw } from 'vue';\nimport { get } from 'lodash-unified';\nimport GroupItem from './group-item.mjs';\nimport OptionItem from './option-item.mjs';\nimport { useProps } from './useProps.mjs';\nimport { selectV2InjectionKey } from './token.mjs';\nimport FixedSizeList from '../../virtual-list/src/components/fixed-size-list.mjs';\nimport DynamicSizeList from '../../virtual-list/src/components/dynamic-size-list.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isUndefined } from '../../../utils/types.mjs';\nimport { isIOS } from '@vueuse/core';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { isObject } from '@vue/shared';\nconst props = {\n  loading: Boolean,\n  data: {\n    type: Array,\n    required: true\n  },\n  hoveringIndex: Number,\n  width: Number\n};\nvar ElSelectMenu = defineComponent({\n  name: \"ElSelectDropdown\",\n  props,\n  setup(props2, {\n    slots,\n    expose\n  }) {\n    const select = inject(selectV2InjectionKey);\n    const ns = useNamespace(\"select\");\n    const {\n      getLabel,\n      getValue,\n      getDisabled\n    } = useProps(select.props);\n    const cachedHeights = ref([]);\n    const listRef = ref();\n    const size = computed(() => props2.data.length);\n    watch(() => size.value, () => {\n      var _a, _b;\n      (_b = (_a = select.tooltipRef.value).updatePopper) == null ? void 0 : _b.call(_a);\n    });\n    const isSized = computed(() => isUndefined(select.props.estimatedOptionHeight));\n    const listProps = computed(() => {\n      if (isSized.value) {\n        return {\n          itemSize: select.props.itemHeight\n        };\n      }\n      return {\n        estimatedSize: select.props.estimatedOptionHeight,\n        itemSize: idx => cachedHeights.value[idx]\n      };\n    });\n    const contains = (arr = [], target) => {\n      const {\n        props: {\n          valueKey\n        }\n      } = select;\n      if (!isObject(target)) {\n        return arr.includes(target);\n      }\n      return arr && arr.some(item => {\n        return toRaw(get(item, valueKey)) === get(target, valueKey);\n      });\n    };\n    const isEqual = (selected, target) => {\n      if (!isObject(target)) {\n        return selected === target;\n      } else {\n        const {\n          valueKey\n        } = select.props;\n        return get(selected, valueKey) === get(target, valueKey);\n      }\n    };\n    const isItemSelected = (modelValue, target) => {\n      if (select.props.multiple) {\n        return contains(modelValue, getValue(target));\n      }\n      return isEqual(modelValue, getValue(target));\n    };\n    const isItemDisabled = (modelValue, selected) => {\n      const {\n        disabled,\n        multiple,\n        multipleLimit\n      } = select.props;\n      return disabled || !selected && (multiple ? multipleLimit > 0 && modelValue.length >= multipleLimit : false);\n    };\n    const isItemHovering = target => props2.hoveringIndex === target;\n    const scrollToItem = index => {\n      const list = listRef.value;\n      if (list) {\n        list.scrollToItem(index);\n      }\n    };\n    const resetScrollTop = () => {\n      const list = listRef.value;\n      if (list) {\n        list.resetScrollTop();\n      }\n    };\n    const exposed = {\n      listRef,\n      isSized,\n      isItemDisabled,\n      isItemHovering,\n      isItemSelected,\n      scrollToItem,\n      resetScrollTop\n    };\n    expose(exposed);\n    const Item = itemProps => {\n      const {\n        index,\n        data,\n        style\n      } = itemProps;\n      const sized = unref(isSized);\n      const {\n        itemSize,\n        estimatedSize\n      } = unref(listProps);\n      const {\n        modelValue\n      } = select.props;\n      const {\n        onSelect,\n        onHover\n      } = select;\n      const item = data[index];\n      if (item.type === \"Group\") {\n        return createVNode(GroupItem, {\n          \"item\": item,\n          \"style\": style,\n          \"height\": sized ? itemSize : estimatedSize\n        }, null);\n      }\n      const isSelected = isItemSelected(modelValue, item);\n      const isDisabled = isItemDisabled(modelValue, isSelected);\n      const isHovering = isItemHovering(index);\n      return createVNode(OptionItem, mergeProps(itemProps, {\n        \"selected\": isSelected,\n        \"disabled\": getDisabled(item) || isDisabled,\n        \"created\": !!item.created,\n        \"hovering\": isHovering,\n        \"item\": item,\n        \"onSelect\": onSelect,\n        \"onHover\": onHover\n      }), {\n        default: props3 => {\n          var _a;\n          return ((_a = slots.default) == null ? void 0 : _a.call(slots, props3)) || createVNode(\"span\", null, [getLabel(item)]);\n        }\n      });\n    };\n    const {\n      onKeyboardNavigate,\n      onKeyboardSelect\n    } = select;\n    const onForward = () => {\n      onKeyboardNavigate(\"forward\");\n    };\n    const onBackward = () => {\n      onKeyboardNavigate(\"backward\");\n    };\n    const onKeydown = e => {\n      const {\n        code\n      } = e;\n      const {\n        tab,\n        esc,\n        down,\n        up,\n        enter,\n        numpadEnter\n      } = EVENT_CODE;\n      if ([esc, down, up, enter, numpadEnter].includes(code)) {\n        e.preventDefault();\n        e.stopPropagation();\n      }\n      switch (code) {\n        case tab:\n        case esc:\n          break;\n        case down:\n          onForward();\n          break;\n        case up:\n          onBackward();\n          break;\n        case enter:\n        case numpadEnter:\n          onKeyboardSelect();\n          break;\n      }\n    };\n    return () => {\n      var _a, _b, _c, _d;\n      const {\n        data,\n        width\n      } = props2;\n      const {\n        height,\n        multiple,\n        scrollbarAlwaysOn\n      } = select.props;\n      const isScrollbarAlwaysOn = computed(() => {\n        return isIOS ? true : scrollbarAlwaysOn;\n      });\n      const List = unref(isSized) ? FixedSizeList : DynamicSizeList;\n      return createVNode(\"div\", {\n        \"class\": [ns.b(\"dropdown\"), ns.is(\"multiple\", multiple)],\n        \"style\": {\n          width: `${width}px`\n        }\n      }, [(_a = slots.header) == null ? void 0 : _a.call(slots), ((_b = slots.loading) == null ? void 0 : _b.call(slots)) || ((_c = slots.empty) == null ? void 0 : _c.call(slots)) || createVNode(List, mergeProps({\n        \"ref\": listRef\n      }, unref(listProps), {\n        \"className\": ns.be(\"dropdown\", \"list\"),\n        \"scrollbarAlwaysOn\": isScrollbarAlwaysOn.value,\n        \"data\": data,\n        \"height\": height,\n        \"width\": width,\n        \"total\": data.length,\n        \"onKeydown\": onKeydown\n      }), {\n        default: props3 => createVNode(Item, props3, null)\n      }), (_d = slots.footer) == null ? void 0 : _d.call(slots)]);\n    };\n  }\n});\nexport { ElSelectMenu as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}