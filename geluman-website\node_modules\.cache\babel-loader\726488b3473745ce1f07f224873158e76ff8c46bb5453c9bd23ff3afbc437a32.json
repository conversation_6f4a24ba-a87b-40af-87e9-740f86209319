{"ast": null, "code": "import { createApp } from \"vue\";\nimport ElementPlus from \"element-plus\";\nimport \"element-plus/dist/index.css\";\nimport * as ElementPlusIconsVue from \"@element-plus/icons-vue\";\nimport i18n from \"./i18n\";\nimport App from \"./App.vue\";\nimport router from \"./router\";\nimport \"./styles/element-variables.scss\";\nimport \"./styles/main.scss\";\nimport \"./styles/animations.scss\";\nconst app = createApp(App);\n\n// 注册所有图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component);\n}\n\n// 波纹效果指令\napp.directive(\"ripple\", {\n  mounted(el) {\n    el.addEventListener(\"click\", e => {\n      const ripple = document.createElement(\"span\");\n      ripple.classList.add(\"ripple\");\n      el.appendChild(ripple);\n      const rect = el.getBoundingClientRect();\n      const size = Math.max(rect.width, rect.height);\n      const x = e.clientX - rect.left - size / 2;\n      const y = e.clientY - rect.top - size / 2;\n      ripple.style.width = ripple.style.height = `${size}px`;\n      ripple.style.left = `${x}px`;\n      ripple.style.top = `${y}px`;\n      setTimeout(() => ripple.remove(), 1000);\n    });\n  }\n});\n\n// 初始化语言\nconst savedLanguage = localStorage.getItem(\"language\");\nif (savedLanguage) {\n  i18n.global.locale.value = savedLanguage;\n}\napp.use(ElementPlus).use(router).use(i18n).mount(\"#app\");", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}