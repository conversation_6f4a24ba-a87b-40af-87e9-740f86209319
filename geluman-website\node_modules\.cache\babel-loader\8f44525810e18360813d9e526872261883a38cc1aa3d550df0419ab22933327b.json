{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nconst colProps = buildProps({\n  tag: {\n    type: String,\n    default: \"div\"\n  },\n  span: {\n    type: Number,\n    default: 24\n  },\n  offset: {\n    type: Number,\n    default: 0\n  },\n  pull: {\n    type: Number,\n    default: 0\n  },\n  push: {\n    type: Number,\n    default: 0\n  },\n  xs: {\n    type: definePropType([Number, Object]),\n    default: () => mutable({})\n  },\n  sm: {\n    type: definePropType([Number, Object]),\n    default: () => mutable({})\n  },\n  md: {\n    type: definePropType([Number, Object]),\n    default: () => mutable({})\n  },\n  lg: {\n    type: definePropType([Number, Object]),\n    default: () => mutable({})\n  },\n  xl: {\n    type: definePropType([Number, Object]),\n    default: () => mutable({})\n  }\n});\nexport { colProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}