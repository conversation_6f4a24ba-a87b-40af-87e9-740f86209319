{"ast": null, "code": "import { createVNode as _createVNode, resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, withCtx as _withCtx, resolveDynamicComponent as _resolveDynamicComponent, openBlock as _openBlock, createBlock as _createBlock, Transition as _Transition } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_header = _resolveComponent(\"el-header\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  const _component_el_main = _resolveComponent(\"el-main\");\n  const _component_el_footer = _resolveComponent(\"el-footer\");\n  const _component_el_container = _resolveComponent(\"el-container\");\n  return _openBlock(), _createBlock(_component_el_container, {\n    class: \"layout-container\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_header, {\n      class: _normalizeClass([\"header\", {\n        'header-scrolled': $setup.isScrolled\n      }])\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"NavHeader\"])]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"class\"]), _createVNode(_component_el_main, null, {\n      default: _withCtx(() => [_createVNode(_component_router_view, null, {\n        default: _withCtx(({\n          Component\n        }) => [_createVNode(_Transition, {\n          name: \"fade\",\n          mode: \"out-in\"\n        }, {\n          default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(Component)))]),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_footer, null, {\n      default: _withCtx(() => [_createVNode($setup[\"SiteFooter\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["_createBlock", "_component_el_container", "class", "default", "_withCtx", "_createVNode", "_component_el_header", "_normalizeClass", "$setup", "isScrolled", "_", "_component_el_main", "_component_router_view", "Component", "_Transition", "name", "mode", "_resolveDynamicComponent", "_component_el_footer"], "sources": ["D:\\codes\\glmwebsite\\geluman-website\\src\\App.vue"], "sourcesContent": ["<template>\r\n  <el-container class=\"layout-container\">\r\n    <el-header class=\"header\" :class=\"{ 'header-scrolled': isScrolled }\">\r\n      <nav-header />\r\n    </el-header>\r\n\r\n    <el-main>\r\n      <router-view v-slot=\"{ Component }\">\r\n        <transition name=\"fade\" mode=\"out-in\">\r\n          <component :is=\"Component\" />\r\n        </transition>\r\n      </router-view>\r\n    </el-main>\r\n\r\n    <el-footer>\r\n      <site-footer />\r\n    </el-footer>\r\n  </el-container>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, onUnmounted } from \"vue\";\r\nimport NavHeader from \"@/components/layout/NavHeader.vue\";\r\nimport SiteFooter from \"@/components/layout/SiteFooter.vue\";\r\nimport LoadingScreen from \"@/components/common/LoadingScreen.vue\";\r\n\r\nconst isScrolled = ref(false);\r\n\r\nconst handleScroll = () => {\r\n  isScrolled.value = window.scrollY > 50;\r\n};\r\n\r\nonMounted(() => {\r\n  window.addEventListener(\"scroll\", handleScroll);\r\n});\r\n\r\nonUnmounted(() => {\r\n  window.removeEventListener(\"scroll\", handleScroll);\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n:root {\r\n  --primary-color: #f6c17a;\r\n  --primary-light: #ffd699;\r\n  --primary-dark: #e5a75b;\r\n  --accent-color: #ff9f67;\r\n  --text-primary: #2c3e50;\r\n  --text-secondary: #5f6c7b;\r\n  --bg-primary: #ffffff;\r\n  --bg-secondary: #faf7f5;\r\n  --header-height: 64px;\r\n  --transition-base: all 0.3s ease;\r\n}\r\n\r\n.layout-container {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.header {\r\n  position: fixed;\r\n  width: 100%;\r\n  z-index: 1000;\r\n  transition: var(--transition-base);\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(8px);\r\n  padding: 0;\r\n  height: var(--header-height);\r\n}\r\n\r\n.header-scrolled {\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.el-main {\r\n  padding: 0;\r\n  flex: 1;\r\n}\r\n\r\n.el-footer {\r\n  padding: 0;\r\n  margin-top: auto;\r\n}\r\n\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n  opacity: 0;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;uBACEA,YAAA,CAgBeC,uBAAA;IAhBDC,KAAK,EAAC;EAAkB;IADxCC,OAAA,EAAAC,QAAA,CAEI,MAEY,CAFZC,YAAA,CAEYC,oBAAA;MAFDJ,KAAK,EAFpBK,eAAA,EAEqB,QAAQ;QAAA,mBAA8BC,MAAA,CAAAC;MAAU;;MAFrEN,OAAA,EAAAC,QAAA,CAGM,MAAc,CAAdC,YAAA,CAAcG,MAAA,e;MAHpBE,CAAA;kCAMIL,YAAA,CAMUM,kBAAA;MAZdR,OAAA,EAAAC,QAAA,CAOM,MAIc,CAJdC,YAAA,CAIcO,sBAAA;QAXpBT,OAAA,EAAAC,QAAA,CAQQ,CAEa;UAHQS;QAAS,OAC9BR,YAAA,CAEaS,WAAA;UAFDC,IAAI,EAAC,MAAM;UAACC,IAAI,EAAC;;UARrCb,OAAA,EAAAC,QAAA,CASU,MAA6B,E,cAA7BJ,YAAA,CAA6BiB,wBATvC,CAS0BJ,SAAS,I;UATnCH,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QAcIL,YAAA,CAEYa,oBAAA;MAhBhBf,OAAA,EAAAC,QAAA,CAeM,MAAe,CAAfC,YAAA,CAAeG,MAAA,gB;MAfrBE,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}