{"ast": null, "code": "import createGrid from '../builders/build-grid.mjs';\nimport { AUTO_ALIGNMENT, CENTERED_ALIGNMENT, END_ALIGNMENT, START_ALIGNMENT, SMART_ALIGNMENT } from '../defaults.mjs';\nimport { isNumber } from '../../../../utils/types.mjs';\nimport { throwError } from '../../../../utils/error.mjs';\nconst SCOPE = \"ElFixedSizeGrid\";\nconst FixedSizeGrid = createGrid({\n  name: \"ElFixedSizeGrid\",\n  getColumnPosition: ({\n    columnWidth\n  }, index) => [columnWidth, index * columnWidth],\n  getRowPosition: ({\n    rowHeight\n  }, index) => [rowHeight, index * rowHeight],\n  getEstimatedTotalHeight: ({\n    totalRow,\n    rowHeight\n  }) => rowHeight * totalRow,\n  getEstimatedTotalWidth: ({\n    totalColumn,\n    columnWidth\n  }) => columnWidth * totalColumn,\n  getColumnOffset: ({\n    totalColumn,\n    columnWidth,\n    width\n  }, columnIndex, alignment, scrollLeft, _, scrollBarWidth) => {\n    width = Number(width);\n    const lastColumnOffset = Math.max(0, totalColumn * columnWidth - width);\n    const maxOffset = Math.min(lastColumnOffset, columnIndex * columnWidth);\n    const minOffset = Math.max(0, columnIndex * columnWidth - width + scrollBarWidth + columnWidth);\n    if (alignment === \"smart\") {\n      if (scrollLeft >= minOffset - width && scrollLeft <= maxOffset + width) {\n        alignment = AUTO_ALIGNMENT;\n      } else {\n        alignment = CENTERED_ALIGNMENT;\n      }\n    }\n    switch (alignment) {\n      case START_ALIGNMENT:\n        return maxOffset;\n      case END_ALIGNMENT:\n        return minOffset;\n      case CENTERED_ALIGNMENT:\n        {\n          const middleOffset = Math.round(minOffset + (maxOffset - minOffset) / 2);\n          if (middleOffset < Math.ceil(width / 2)) {\n            return 0;\n          } else if (middleOffset > lastColumnOffset + Math.floor(width / 2)) {\n            return lastColumnOffset;\n          } else {\n            return middleOffset;\n          }\n        }\n      case AUTO_ALIGNMENT:\n      default:\n        if (scrollLeft >= minOffset && scrollLeft <= maxOffset) {\n          return scrollLeft;\n        } else if (minOffset > maxOffset) {\n          return minOffset;\n        } else if (scrollLeft < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n  getRowOffset: ({\n    rowHeight,\n    height,\n    totalRow\n  }, rowIndex, align, scrollTop, _, scrollBarWidth) => {\n    height = Number(height);\n    const lastRowOffset = Math.max(0, totalRow * rowHeight - height);\n    const maxOffset = Math.min(lastRowOffset, rowIndex * rowHeight);\n    const minOffset = Math.max(0, rowIndex * rowHeight - height + scrollBarWidth + rowHeight);\n    if (align === SMART_ALIGNMENT) {\n      if (scrollTop >= minOffset - height && scrollTop <= maxOffset + height) {\n        align = AUTO_ALIGNMENT;\n      } else {\n        align = CENTERED_ALIGNMENT;\n      }\n    }\n    switch (align) {\n      case START_ALIGNMENT:\n        return maxOffset;\n      case END_ALIGNMENT:\n        return minOffset;\n      case CENTERED_ALIGNMENT:\n        {\n          const middleOffset = Math.round(minOffset + (maxOffset - minOffset) / 2);\n          if (middleOffset < Math.ceil(height / 2)) {\n            return 0;\n          } else if (middleOffset > lastRowOffset + Math.floor(height / 2)) {\n            return lastRowOffset;\n          } else {\n            return middleOffset;\n          }\n        }\n      case AUTO_ALIGNMENT:\n      default:\n        if (scrollTop >= minOffset && scrollTop <= maxOffset) {\n          return scrollTop;\n        } else if (minOffset > maxOffset) {\n          return minOffset;\n        } else if (scrollTop < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n  getColumnStartIndexForOffset: ({\n    columnWidth,\n    totalColumn\n  }, scrollLeft) => Math.max(0, Math.min(totalColumn - 1, Math.floor(scrollLeft / columnWidth))),\n  getColumnStopIndexForStartIndex: ({\n    columnWidth,\n    totalColumn,\n    width\n  }, startIndex, scrollLeft) => {\n    const left = startIndex * columnWidth;\n    const visibleColumnsCount = Math.ceil((width + scrollLeft - left) / columnWidth);\n    return Math.max(0, Math.min(totalColumn - 1, startIndex + visibleColumnsCount - 1));\n  },\n  getRowStartIndexForOffset: ({\n    rowHeight,\n    totalRow\n  }, scrollTop) => Math.max(0, Math.min(totalRow - 1, Math.floor(scrollTop / rowHeight))),\n  getRowStopIndexForStartIndex: ({\n    rowHeight,\n    totalRow,\n    height\n  }, startIndex, scrollTop) => {\n    const top = startIndex * rowHeight;\n    const numVisibleRows = Math.ceil((height + scrollTop - top) / rowHeight);\n    return Math.max(0, Math.min(totalRow - 1, startIndex + numVisibleRows - 1));\n  },\n  initCache: () => void 0,\n  clearCache: true,\n  validateProps: ({\n    columnWidth,\n    rowHeight\n  }) => {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!isNumber(columnWidth)) {\n        throwError(SCOPE, `\n          \"columnWidth\" must be passed as number,\n            instead ${typeof columnWidth} was given.\n        `);\n      }\n      if (!isNumber(rowHeight)) {\n        throwError(SCOPE, `\n          \"columnWidth\" must be passed as number,\n            instead ${typeof rowHeight} was given.\n        `);\n      }\n    }\n  }\n});\nexport { FixedSizeGrid as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}