{"ast": null, "code": "/**\n * Gets the value at `key`, unless `key` is \"__proto__\" or \"constructor\".\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction safeGet(object, key) {\n  if (key === 'constructor' && typeof object[key] === 'function') {\n    return;\n  }\n  if (key == '__proto__') {\n    return;\n  }\n  return object[key];\n}\nexport default safeGet;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}