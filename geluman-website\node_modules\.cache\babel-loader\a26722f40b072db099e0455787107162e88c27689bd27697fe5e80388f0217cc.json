{"ast": null, "code": "import { defineComponent, inject, watch, computed, openBlock, createElementBlock, Fragment, unref, normalizeClass, createVNode, withCtx, createBlock, resolveDynamicComponent, createCommentVNode, createElementVNode, renderSlot, toDisplayString, renderList, mergeProps, createTextVNode } from 'vue';\nimport { omit } from 'lodash-unified';\nimport { ElButton } from '../../button/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { tourStepProps, tourStepEmits } from './step.mjs';\nimport { tourKey } from './helper.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { CloseComponents } from '../../../utils/vue/icon.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTourStep\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: tourStepProps,\n  emits: tourStepEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const {\n      Close\n    } = CloseComponents;\n    const {\n      t\n    } = useLocale();\n    const {\n      currentStep,\n      current,\n      total,\n      showClose,\n      closeIcon,\n      mergedType,\n      ns,\n      slots: tourSlots,\n      updateModelValue,\n      onClose: tourOnClose,\n      onFinish: tourOnFinish,\n      onChange\n    } = inject(tourKey);\n    watch(props, val => {\n      currentStep.value = val;\n    }, {\n      immediate: true\n    });\n    const mergedShowClose = computed(() => {\n      var _a;\n      return (_a = props.showClose) != null ? _a : showClose.value;\n    });\n    const mergedCloseIcon = computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.closeIcon) != null ? _a : closeIcon.value) != null ? _b : Close;\n    });\n    const filterButtonProps = btnProps => {\n      if (!btnProps) return;\n      return omit(btnProps, [\"children\", \"onClick\"]);\n    };\n    const onPrev = () => {\n      var _a, _b;\n      current.value -= 1;\n      if ((_a = props.prevButtonProps) == null ? void 0 : _a.onClick) {\n        (_b = props.prevButtonProps) == null ? void 0 : _b.onClick();\n      }\n      onChange();\n    };\n    const onNext = () => {\n      var _a;\n      if (current.value >= total.value - 1) {\n        onFinish();\n      } else {\n        current.value += 1;\n      }\n      if ((_a = props.nextButtonProps) == null ? void 0 : _a.onClick) {\n        props.nextButtonProps.onClick();\n      }\n      onChange();\n    };\n    const onFinish = () => {\n      onClose();\n      tourOnFinish();\n    };\n    const onClose = () => {\n      updateModelValue(false);\n      tourOnClose();\n      emit(\"close\");\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(Fragment, null, [unref(mergedShowClose) ? (openBlock(), createElementBlock(\"button\", {\n        key: 0,\n        \"aria-label\": \"Close\",\n        class: normalizeClass(unref(ns).e(\"closebtn\")),\n        type: \"button\",\n        onClick: onClose\n      }, [createVNode(unref(ElIcon), {\n        class: normalizeClass(unref(ns).e(\"close\"))\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(mergedCloseIcon))))]),\n        _: 1\n      }, 8, [\"class\"])], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"header\", {\n        class: normalizeClass([unref(ns).e(\"header\"), {\n          \"show-close\": unref(showClose)\n        }])\n      }, [renderSlot(_ctx.$slots, \"header\", {}, () => [createElementVNode(\"span\", {\n        role: \"heading\",\n        class: normalizeClass(unref(ns).e(\"title\"))\n      }, toDisplayString(_ctx.title), 3)])], 2), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"body\"))\n      }, [renderSlot(_ctx.$slots, \"default\", {}, () => [createElementVNode(\"span\", null, toDisplayString(_ctx.description), 1)])], 2), createElementVNode(\"footer\", {\n        class: normalizeClass(unref(ns).e(\"footer\"))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).b(\"indicators\"))\n      }, [unref(tourSlots).indicators ? (openBlock(), createBlock(resolveDynamicComponent(unref(tourSlots).indicators), {\n        key: 0,\n        current: unref(current),\n        total: unref(total)\n      }, null, 8, [\"current\", \"total\"])) : (openBlock(true), createElementBlock(Fragment, {\n        key: 1\n      }, renderList(unref(total), (item, index) => {\n        return openBlock(), createElementBlock(\"span\", {\n          key: item,\n          class: normalizeClass([unref(ns).b(\"indicator\"), index === unref(current) ? \"is-active\" : \"\"])\n        }, null, 2);\n      }), 128))], 2), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).b(\"buttons\"))\n      }, [unref(current) > 0 ? (openBlock(), createBlock(unref(ElButton), mergeProps({\n        key: 0,\n        size: \"small\",\n        type: unref(mergedType)\n      }, filterButtonProps(_ctx.prevButtonProps), {\n        onClick: onPrev\n      }), {\n        default: withCtx(() => {\n          var _a, _b;\n          return [createTextVNode(toDisplayString((_b = (_a = _ctx.prevButtonProps) == null ? void 0 : _a.children) != null ? _b : unref(t)(\"el.tour.previous\")), 1)];\n        }),\n        _: 1\n      }, 16, [\"type\"])) : createCommentVNode(\"v-if\", true), unref(current) <= unref(total) - 1 ? (openBlock(), createBlock(unref(ElButton), mergeProps({\n        key: 1,\n        size: \"small\",\n        type: unref(mergedType) === \"primary\" ? \"default\" : \"primary\"\n      }, filterButtonProps(_ctx.nextButtonProps), {\n        onClick: onNext\n      }), {\n        default: withCtx(() => {\n          var _a, _b;\n          return [createTextVNode(toDisplayString((_b = (_a = _ctx.nextButtonProps) == null ? void 0 : _a.children) != null ? _b : unref(current) === unref(total) - 1 ? unref(t)(\"el.tour.finish\") : unref(t)(\"el.tour.next\")), 1)];\n        }),\n        _: 1\n      }, 16, [\"type\"])) : createCommentVNode(\"v-if\", true)], 2)], 2)], 64);\n    };\n  }\n});\nvar TourStep = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"step.vue\"]]);\nexport { TourStep as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}