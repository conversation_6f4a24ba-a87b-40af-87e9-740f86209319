{"ast": null, "code": "import Dropdown from './src/dropdown2.mjs';\nimport DropdownItem from './src/dropdown-item.mjs';\nimport DropdownMenu from './src/dropdown-menu.mjs';\nexport { DROPDOWN_COLLECTION_INJECTION_KEY, DROPDOWN_COLLECTION_ITEM_INJECTION_KEY, ElCollection, ElCollectionItem, FIRST_KEYS, FIRST_LAST_KEYS, LAST_KEYS, dropdownItemProps, dropdownMenuProps, dropdownProps } from './src/dropdown.mjs';\nexport { DROPDOWN_INJECTION_KEY } from './src/tokens.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElDropdown = withInstall(Dropdown, {\n  DropdownItem,\n  DropdownMenu\n});\nconst ElDropdownItem = withNoopInstall(DropdownItem);\nconst ElDropdownMenu = withNoopInstall(DropdownMenu);\nexport { ElDropdown, ElDropdownItem, ElDropdownMenu, ElDropdown as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}