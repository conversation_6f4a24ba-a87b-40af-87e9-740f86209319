{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { defineComponent, computed, unref, reactive, toRefs, getCurrentInstance, onBeforeUnmount, nextTick, withDirectives, openBlock, createElementBlock, normalizeClass, withModifiers, renderSlot, createElementVNode, toDisplayString, vShow } from 'vue';\nimport { useOption } from './useOption.mjs';\nimport { COMPONENT_NAME, optionProps } from './option.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nconst _sfc_main = defineComponent({\n  name: COMPONENT_NAME,\n  componentName: COMPONENT_NAME,\n  props: optionProps,\n  setup(props) {\n    const ns = useNamespace(\"select\");\n    const id = useId();\n    const containerKls = computed(() => [ns.be(\"dropdown\", \"item\"), ns.is(\"disabled\", unref(isDisabled)), ns.is(\"selected\", unref(itemSelected)), ns.is(\"hovering\", unref(hover))]);\n    const states = reactive({\n      index: -1,\n      groupDisabled: false,\n      visible: true,\n      hover: false\n    });\n    const {\n      currentLabel,\n      itemSelected,\n      isDisabled,\n      select,\n      hoverItem,\n      updateOption\n    } = useOption(props, states);\n    const {\n      visible,\n      hover\n    } = toRefs(states);\n    const vm = getCurrentInstance().proxy;\n    select.onOptionCreate(vm);\n    onBeforeUnmount(() => {\n      const key = vm.value;\n      const {\n        selected: selectedOptions\n      } = select.states;\n      const doesSelected = selectedOptions.some(item => {\n        return item.value === vm.value;\n      });\n      nextTick(() => {\n        if (select.states.cachedOptions.get(key) === vm && !doesSelected) {\n          select.states.cachedOptions.delete(key);\n        }\n      });\n      select.onOptionDestroy(key, vm);\n    });\n    function selectOptionClick() {\n      if (!isDisabled.value) {\n        select.handleOptionSelect(vm);\n      }\n    }\n    return {\n      ns,\n      id,\n      containerKls,\n      currentLabel,\n      itemSelected,\n      isDisabled,\n      select,\n      visible,\n      hover,\n      states,\n      hoverItem,\n      updateOption,\n      selectOptionClick\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache) {\n  return withDirectives((openBlock(), createElementBlock(\"li\", {\n    id: _ctx.id,\n    class: normalizeClass(_ctx.containerKls),\n    role: \"option\",\n    \"aria-disabled\": _ctx.isDisabled || void 0,\n    \"aria-selected\": _ctx.itemSelected,\n    onMousemove: _ctx.hoverItem,\n    onClick: withModifiers(_ctx.selectOptionClick, [\"stop\"])\n  }, [renderSlot(_ctx.$slots, \"default\", {}, () => [createElementVNode(\"span\", null, toDisplayString(_ctx.currentLabel), 1)])], 42, [\"id\", \"aria-disabled\", \"aria-selected\", \"onMousemove\", \"onClick\"])), [[vShow, _ctx.visible]]);\n}\nvar Option = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"option.vue\"]]);\nexport { Option as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}