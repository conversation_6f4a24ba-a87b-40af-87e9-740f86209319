{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { isArray, isDate } from '@vue/shared';\nimport { UPDATE_MODEL_EVENT, INPUT_EVENT } from '../../../constants/event.mjs';\nconst isValidRange = range => isArray(range) && range.length === 2 && range.every(item => isDate(item));\nconst calendarProps = buildProps({\n  modelValue: {\n    type: Date\n  },\n  range: {\n    type: definePropType(Array),\n    validator: isValidRange\n  }\n});\nconst calendarEmits = {\n  [UPDATE_MODEL_EVENT]: value => isDate(value),\n  [INPUT_EVENT]: value => isDate(value)\n};\nexport { calendarEmits, calendarProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}