{"ast": null, "code": "import Skeleton from './src/skeleton.mjs';\nimport SkeletonItem from './src/skeleton-item2.mjs';\nexport { skeletonProps } from './src/skeleton2.mjs';\nexport { skeletonItemProps } from './src/skeleton-item.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElSkeleton = withInstall(Skeleton, {\n  SkeletonItem\n});\nconst ElSkeletonItem = withNoopInstall(SkeletonItem);\nexport { ElSkeleton, ElSkeletonItem, ElSkeleton as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}