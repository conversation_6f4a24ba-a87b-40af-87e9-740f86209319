{"ast": null, "code": "import { computed, ref, watchEffect } from 'vue';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isArray } from '@vue/shared';\nimport { isNumber } from '../../../utils/types.mjs';\nconst SIZE_MAP = {\n  small: 8,\n  default: 12,\n  large: 16\n};\nfunction useSpace(props) {\n  const ns = useNamespace(\"space\");\n  const classes = computed(() => [ns.b(), ns.m(props.direction), props.class]);\n  const horizontalSize = ref(0);\n  const verticalSize = ref(0);\n  const containerStyle = computed(() => {\n    const wrapKls = props.wrap || props.fill ? {\n      flexWrap: \"wrap\"\n    } : {};\n    const alignment = {\n      alignItems: props.alignment\n    };\n    const gap = {\n      rowGap: `${verticalSize.value}px`,\n      columnGap: `${horizontalSize.value}px`\n    };\n    return [wrapKls, alignment, gap, props.style];\n  });\n  const itemStyle = computed(() => {\n    return props.fill ? {\n      flexGrow: 1,\n      minWidth: `${props.fillRatio}%`\n    } : {};\n  });\n  watchEffect(() => {\n    const {\n      size = \"small\",\n      wrap,\n      direction: dir,\n      fill\n    } = props;\n    if (isArray(size)) {\n      const [h = 0, v = 0] = size;\n      horizontalSize.value = h;\n      verticalSize.value = v;\n    } else {\n      let val;\n      if (isNumber(size)) {\n        val = size;\n      } else {\n        val = SIZE_MAP[size || \"small\"] || SIZE_MAP.small;\n      }\n      if ((wrap || fill) && dir === \"horizontal\") {\n        horizontalSize.value = verticalSize.value = val;\n      } else {\n        if (dir === \"horizontal\") {\n          horizontalSize.value = val;\n          verticalSize.value = 0;\n        } else {\n          verticalSize.value = val;\n          horizontalSize.value = 0;\n        }\n      }\n    }\n  });\n  return {\n    classes,\n    containerStyle,\n    itemStyle\n  };\n}\nexport { useSpace };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}