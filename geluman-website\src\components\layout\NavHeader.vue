<template>
  <div class="nav-container">
    <router-link to="/" class="logo-link">
      <img :src="logo" alt="格鲁曼" class="logo" />
    </router-link>

    <nav class="nav-menu">
      <router-link to="/products" class="nav-item">
        <el-icon><Grid /></el-icon>
        {{ t("nav.products") }}
        <span class="nav-background"></span>
      </router-link>
      <router-link to="/about" class="nav-item">
        <el-icon><InfoFilled /></el-icon>
        {{ t("nav.about") }}
        <span class="nav-background"></span>
      </router-link>
    </nav>
  </div>
</template>

<script setup>
import { useI18n } from "vue-i18n";
import { Grid, InfoFilled } from "@element-plus/icons-vue";
import { logo } from "@/assets";

const { t } = useI18n();
</script>

<style lang="scss" scoped>
.nav-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-link {
  display: flex;
  align-items: center;

  .logo {
    height: 28px;
    transition: var(--transition-base);

    &:hover {
      transform: scale(1.05);
    }
  }
}

.nav-menu {
  display: flex;
  gap: 2rem;

  .nav-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
    text-decoration: none;
    font-size: 1.1rem;
    padding: 0.6rem 1.2rem;
    position: relative;
    transition: color 0.3s ease;

    .el-icon {
      font-size: 1.1rem;
    }

    .nav-background {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 0;
      height: 100%;
      background: var(--bg-accent);
      border-radius: 20px;
      transition: width 0.3s ease;
      z-index: -1;
    }

    &:hover,
    &.router-link-active {
      color: var(--primary-color);

      .nav-background {
        width: 100%;
      }
    }
  }
}

@media (max-width: 768px) {
  .nav-container {
    padding: 0 1rem;
  }

  .nav-menu {
    gap: 1rem;

    .nav-item {
      padding: 0.6rem 1rem;
      font-size: 1rem;
    }
  }
}
</style>
