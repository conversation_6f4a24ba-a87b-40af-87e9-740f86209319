{"ast": null, "code": "import { inject, ref, computed, onMounted, watch, toRef, onUnmounted } from 'vue';\nimport { formContextKey, formItemContextKey } from '../constants.mjs';\nimport { useId } from '../../../../hooks/use-id/index.mjs';\nconst useFormItem = () => {\n  const form = inject(formContextKey, void 0);\n  const formItem = inject(formItemContextKey, void 0);\n  return {\n    form,\n    formItem\n  };\n};\nconst useFormItemInputId = (props, {\n  formItemContext,\n  disableIdGeneration,\n  disableIdManagement\n}) => {\n  if (!disableIdGeneration) {\n    disableIdGeneration = ref(false);\n  }\n  if (!disableIdManagement) {\n    disableIdManagement = ref(false);\n  }\n  const inputId = ref();\n  let idUnwatch = void 0;\n  const isLabeledByFormItem = computed(() => {\n    var _a;\n    return !!(!(props.label || props.ariaLabel) && formItemContext && formItemContext.inputIds && ((_a = formItemContext.inputIds) == null ? void 0 : _a.length) <= 1);\n  });\n  onMounted(() => {\n    idUnwatch = watch([toRef(props, \"id\"), disableIdGeneration], ([id, disableIdGeneration2]) => {\n      const newId = id != null ? id : !disableIdGeneration2 ? useId().value : void 0;\n      if (newId !== inputId.value) {\n        if (formItemContext == null ? void 0 : formItemContext.removeInputId) {\n          inputId.value && formItemContext.removeInputId(inputId.value);\n          if (!(disableIdManagement == null ? void 0 : disableIdManagement.value) && !disableIdGeneration2 && newId) {\n            formItemContext.addInputId(newId);\n          }\n        }\n        inputId.value = newId;\n      }\n    }, {\n      immediate: true\n    });\n  });\n  onUnmounted(() => {\n    idUnwatch && idUnwatch();\n    if (formItemContext == null ? void 0 : formItemContext.removeInputId) {\n      inputId.value && formItemContext.removeInputId(inputId.value);\n    }\n  });\n  return {\n    isLabeledByFormItem,\n    inputId\n  };\n};\nexport { useFormItem, useFormItemInputId };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}