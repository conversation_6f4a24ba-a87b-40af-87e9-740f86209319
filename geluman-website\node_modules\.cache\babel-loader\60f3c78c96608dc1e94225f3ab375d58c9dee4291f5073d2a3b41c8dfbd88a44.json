{"ast": null, "code": "import { transferProps, transferCheckedChangeFn } from './transfer.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst CHECKED_CHANGE_EVENT = \"checked-change\";\nconst transferPanelProps = buildProps({\n  data: transferProps.data,\n  optionRender: {\n    type: definePropType(Function)\n  },\n  placeholder: String,\n  title: String,\n  filterable: Boolean,\n  format: transferProps.format,\n  filterMethod: transferProps.filterMethod,\n  defaultChecked: transferProps.leftDefaultChecked,\n  props: transferProps.props\n});\nconst transferPanelEmits = {\n  [CHECKED_CHANGE_EVENT]: transferCheckedChangeFn\n};\nexport { CHECKED_CHANGE_EVENT, transferPanelEmits, transferPanelProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}