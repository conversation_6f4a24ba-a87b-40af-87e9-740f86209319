{"ast": null, "code": "import Table from './src/table.mjs';\nimport ElTableColumn$1 from './src/table-column/index.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElTable = withInstall(Table, {\n  TableColumn: ElTableColumn$1\n});\nconst ElTableColumn = withNoopInstall(ElTableColumn$1);\nexport { ElTable, ElTableColumn, ElTable as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}