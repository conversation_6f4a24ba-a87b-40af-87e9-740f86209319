{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport { defineComponent, getCurrentInstance, inject, ref, watch, nextTick, onBeforeUnmount, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle } from 'vue';\nimport { useResizeObserver } from '@vueuse/core';\nimport { tabsRootContextKey } from './constants.mjs';\nimport { tabBarProps } from './tab-bar.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { capitalize } from '../../../utils/strings.mjs';\nconst COMPONENT_NAME = \"ElTabBar\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: tabBarProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const instance = getCurrentInstance();\n    const rootTabs = inject(tabsRootContextKey);\n    if (!rootTabs) throwError(COMPONENT_NAME, \"<el-tabs><el-tab-bar /></el-tabs>\");\n    const ns = useNamespace(\"tabs\");\n    const barRef = ref();\n    const barStyle = ref();\n    const getBarStyle = () => {\n      let offset = 0;\n      let tabSize = 0;\n      const sizeName = [\"top\", \"bottom\"].includes(rootTabs.props.tabPosition) ? \"width\" : \"height\";\n      const sizeDir = sizeName === \"width\" ? \"x\" : \"y\";\n      const position = sizeDir === \"x\" ? \"left\" : \"top\";\n      props.tabs.every(tab => {\n        var _a, _b;\n        const $el = (_b = (_a = instance.parent) == null ? void 0 : _a.refs) == null ? void 0 : _b[`tab-${tab.uid}`];\n        if (!$el) return false;\n        if (!tab.active) {\n          return true;\n        }\n        offset = $el[`offset${capitalize(position)}`];\n        tabSize = $el[`client${capitalize(sizeName)}`];\n        const tabStyles = window.getComputedStyle($el);\n        if (sizeName === \"width\") {\n          tabSize -= Number.parseFloat(tabStyles.paddingLeft) + Number.parseFloat(tabStyles.paddingRight);\n          offset += Number.parseFloat(tabStyles.paddingLeft);\n        }\n        return false;\n      });\n      return {\n        [sizeName]: `${tabSize}px`,\n        transform: `translate${capitalize(sizeDir)}(${offset}px)`\n      };\n    };\n    const update = () => barStyle.value = getBarStyle();\n    const saveObserver = [];\n    const observerTabs = () => {\n      var _a;\n      saveObserver.forEach(observer => observer.stop());\n      saveObserver.length = 0;\n      const list = (_a = instance.parent) == null ? void 0 : _a.refs;\n      if (!list) return;\n      for (const key in list) {\n        if (key.startsWith(\"tab-\")) {\n          const _el = list[key];\n          if (_el) {\n            saveObserver.push(useResizeObserver(_el, update));\n          }\n        }\n      }\n    };\n    watch(() => props.tabs, async () => {\n      await nextTick();\n      update();\n      observerTabs();\n    }, {\n      immediate: true\n    });\n    const barObserever = useResizeObserver(barRef, () => update());\n    onBeforeUnmount(() => {\n      saveObserver.forEach(observer => observer.stop());\n      saveObserver.length = 0;\n      barObserever.stop();\n    });\n    expose({\n      ref: barRef,\n      update\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"barRef\",\n        ref: barRef,\n        class: normalizeClass([unref(ns).e(\"active-bar\"), unref(ns).is(unref(rootTabs).props.tabPosition)]),\n        style: normalizeStyle(barStyle.value)\n      }, null, 6);\n    };\n  }\n});\nvar TabBar = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tab-bar.vue\"]]);\nexport { TabBar as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}