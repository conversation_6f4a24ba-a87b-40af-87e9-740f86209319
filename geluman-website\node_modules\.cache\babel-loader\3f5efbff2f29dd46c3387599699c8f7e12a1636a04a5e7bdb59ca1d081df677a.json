{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, renderSlot, toDisplayString, createVNode, withCtx, createTextVNode, createCommentVNode, createSlots, normalizeProps, guardReactiveProps, Fragment, renderList, createBlock } from 'vue';\nimport { ElButtonGroup, ElButton } from '../../button/index.mjs';\nimport DateTable from './date-table2.mjs';\nimport { useCalendar } from './use-calendar.mjs';\nimport { calendarProps, calendarEmits } from './calendar.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nconst COMPONENT_NAME = \"ElCalendar\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: calendarProps,\n  emits: calendarEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"calendar\");\n    const {\n      calculateValidatedDateRange,\n      date,\n      pickDay,\n      realSelectedDay,\n      selectDate,\n      validatedRange\n    } = useCalendar(props, emit, COMPONENT_NAME);\n    const {\n      t\n    } = useLocale();\n    const i18nDate = computed(() => {\n      const pickedMonth = `el.datepicker.month${date.value.format(\"M\")}`;\n      return `${date.value.year()} ${t(\"el.datepicker.year\")} ${t(pickedMonth)}`;\n    });\n    expose({\n      selectedDay: realSelectedDay,\n      pickDay,\n      selectDate,\n      calculateValidatedDateRange\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(ns).b())\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"header\"))\n      }, [renderSlot(_ctx.$slots, \"header\", {\n        date: unref(i18nDate)\n      }, () => [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"title\"))\n      }, toDisplayString(unref(i18nDate)), 3), unref(validatedRange).length === 0 ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"button-group\"))\n      }, [createVNode(unref(ElButtonGroup), null, {\n        default: withCtx(() => [createVNode(unref(ElButton), {\n          size: \"small\",\n          onClick: $event => unref(selectDate)(\"prev-month\")\n        }, {\n          default: withCtx(() => [createTextVNode(toDisplayString(unref(t)(\"el.datepicker.prevMonth\")), 1)]),\n          _: 1\n        }, 8, [\"onClick\"]), createVNode(unref(ElButton), {\n          size: \"small\",\n          onClick: $event => unref(selectDate)(\"today\")\n        }, {\n          default: withCtx(() => [createTextVNode(toDisplayString(unref(t)(\"el.datepicker.today\")), 1)]),\n          _: 1\n        }, 8, [\"onClick\"]), createVNode(unref(ElButton), {\n          size: \"small\",\n          onClick: $event => unref(selectDate)(\"next-month\")\n        }, {\n          default: withCtx(() => [createTextVNode(toDisplayString(unref(t)(\"el.datepicker.nextMonth\")), 1)]),\n          _: 1\n        }, 8, [\"onClick\"])]),\n        _: 1\n      })], 2)) : createCommentVNode(\"v-if\", true)])], 2), unref(validatedRange).length === 0 ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"body\"))\n      }, [createVNode(DateTable, {\n        date: unref(date),\n        \"selected-day\": unref(realSelectedDay),\n        onPick: unref(pickDay)\n      }, createSlots({\n        _: 2\n      }, [_ctx.$slots[\"date-cell\"] ? {\n        name: \"date-cell\",\n        fn: withCtx(data => [renderSlot(_ctx.$slots, \"date-cell\", normalizeProps(guardReactiveProps(data)))])\n      } : void 0]), 1032, [\"date\", \"selected-day\", \"onPick\"])], 2)) : (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(unref(ns).e(\"body\"))\n      }, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(validatedRange), (range_, index) => {\n        return openBlock(), createBlock(DateTable, {\n          key: index,\n          date: range_[0],\n          \"selected-day\": unref(realSelectedDay),\n          range: range_,\n          \"hide-header\": index !== 0,\n          onPick: unref(pickDay)\n        }, createSlots({\n          _: 2\n        }, [_ctx.$slots[\"date-cell\"] ? {\n          name: \"date-cell\",\n          fn: withCtx(data => [renderSlot(_ctx.$slots, \"date-cell\", normalizeProps(guardReactiveProps(data)))])\n        } : void 0]), 1032, [\"date\", \"selected-day\", \"range\", \"hide-header\", \"onPick\"]);\n      }), 128))], 2))], 2);\n    };\n  }\n});\nvar Calendar = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"calendar.vue\"]]);\nexport { Calendar as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}