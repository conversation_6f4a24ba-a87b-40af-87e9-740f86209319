{"ast": null, "code": "import { computed, unref, ref } from 'vue';\nimport { useZIndex } from '../../../../hooks/use-z-index/index.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isNumber } from '../../../../utils/types.mjs';\nconst usePopperContentDOM = (props, {\n  attributes,\n  styles,\n  role\n}) => {\n  const {\n    nextZIndex\n  } = useZIndex();\n  const ns = useNamespace(\"popper\");\n  const contentAttrs = computed(() => unref(attributes).popper);\n  const contentZIndex = ref(isNumber(props.zIndex) ? props.zIndex : nextZIndex());\n  const contentClass = computed(() => [ns.b(), ns.is(\"pure\", props.pure), ns.is(props.effect), props.popperClass]);\n  const contentStyle = computed(() => {\n    return [{\n      zIndex: unref(contentZIndex)\n    }, unref(styles).popper, props.popperStyle || {}];\n  });\n  const ariaModal = computed(() => role.value === \"dialog\" ? \"false\" : void 0);\n  const arrowStyle = computed(() => unref(styles).arrow || {});\n  const updateZIndex = () => {\n    contentZIndex.value = isNumber(props.zIndex) ? props.zIndex : nextZIndex();\n  };\n  return {\n    ariaModal,\n    arrowStyle,\n    contentAttrs,\n    contentClass,\n    contentStyle,\n    contentZIndex,\n    updateZIndex\n  };\n};\nexport { usePopperContentDOM };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}