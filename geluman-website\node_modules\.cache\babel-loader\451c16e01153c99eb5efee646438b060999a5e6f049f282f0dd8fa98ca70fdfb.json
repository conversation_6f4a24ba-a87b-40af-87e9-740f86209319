{"ast": null, "code": "import baseFunctions from './_baseFunctions.js';\nimport keys from './keys.js';\n\n/**\n * Creates an array of function property names from own enumerable properties\n * of `object`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to inspect.\n * @returns {Array} Returns the function names.\n * @see _.functionsIn\n * @example\n *\n * function Foo() {\n *   this.a = _.constant('a');\n *   this.b = _.constant('b');\n * }\n *\n * Foo.prototype.c = _.constant('c');\n *\n * _.functions(new Foo);\n * // => ['a', 'b']\n */\nfunction functions(object) {\n  return object == null ? [] : baseFunctions(object, keys(object));\n}\nexport default functions;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}