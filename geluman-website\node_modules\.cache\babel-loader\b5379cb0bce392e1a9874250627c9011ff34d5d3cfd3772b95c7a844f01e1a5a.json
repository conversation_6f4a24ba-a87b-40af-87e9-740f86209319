{"ast": null, "code": "import { defineComponent, useSlots, ref, computed, openBlock, createBlock, Transition, unref, withCtx, withDirectives, createElementVNode, normalizeClass, renderSlot, resolveDynamicComponent, createCommentVNode, createElementBlock, createTextVNode, toDisplayString, Fragment, createVNode, vShow } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { alertProps, alertEmits } from './alert.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { TypeComponentsMap, TypeComponents } from '../../../utils/vue/icon.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElAlert\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: alertProps,\n  emits: alertEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const {\n      Close\n    } = TypeComponents;\n    const slots = useSlots();\n    const ns = useNamespace(\"alert\");\n    const visible = ref(true);\n    const iconComponent = computed(() => TypeComponentsMap[props.type]);\n    const hasDesc = computed(() => !!(props.description || slots.default));\n    const close = evt => {\n      visible.value = false;\n      emit(\"close\", evt);\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(Transition, {\n        name: unref(ns).b(\"fade\"),\n        persisted: \"\"\n      }, {\n        default: withCtx(() => [withDirectives(createElementVNode(\"div\", {\n          class: normalizeClass([unref(ns).b(), unref(ns).m(_ctx.type), unref(ns).is(\"center\", _ctx.center), unref(ns).is(_ctx.effect)]),\n          role: \"alert\"\n        }, [_ctx.showIcon && (_ctx.$slots.icon || unref(iconComponent)) ? (openBlock(), createBlock(unref(ElIcon), {\n          key: 0,\n          class: normalizeClass([unref(ns).e(\"icon\"), {\n            [unref(ns).is(\"big\")]: unref(hasDesc)\n          }])\n        }, {\n          default: withCtx(() => [renderSlot(_ctx.$slots, \"icon\", {}, () => [(openBlock(), createBlock(resolveDynamicComponent(unref(iconComponent))))])]),\n          _: 3\n        }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).e(\"content\"))\n        }, [_ctx.title || _ctx.$slots.title ? (openBlock(), createElementBlock(\"span\", {\n          key: 0,\n          class: normalizeClass([unref(ns).e(\"title\"), {\n            \"with-description\": unref(hasDesc)\n          }])\n        }, [renderSlot(_ctx.$slots, \"title\", {}, () => [createTextVNode(toDisplayString(_ctx.title), 1)])], 2)) : createCommentVNode(\"v-if\", true), unref(hasDesc) ? (openBlock(), createElementBlock(\"p\", {\n          key: 1,\n          class: normalizeClass(unref(ns).e(\"description\"))\n        }, [renderSlot(_ctx.$slots, \"default\", {}, () => [createTextVNode(toDisplayString(_ctx.description), 1)])], 2)) : createCommentVNode(\"v-if\", true), _ctx.closable ? (openBlock(), createElementBlock(Fragment, {\n          key: 2\n        }, [_ctx.closeText ? (openBlock(), createElementBlock(\"div\", {\n          key: 0,\n          class: normalizeClass([unref(ns).e(\"close-btn\"), unref(ns).is(\"customed\")]),\n          onClick: close\n        }, toDisplayString(_ctx.closeText), 3)) : (openBlock(), createBlock(unref(ElIcon), {\n          key: 1,\n          class: normalizeClass(unref(ns).e(\"close-btn\")),\n          onClick: close\n        }, {\n          default: withCtx(() => [createVNode(unref(Close))]),\n          _: 1\n        }, 8, [\"class\"]))], 64)) : createCommentVNode(\"v-if\", true)], 2)], 2), [[vShow, visible.value]])]),\n        _: 3\n      }, 8, [\"name\"]);\n    };\n  }\n});\nvar Alert = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"alert.vue\"]]);\nexport { Alert as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}