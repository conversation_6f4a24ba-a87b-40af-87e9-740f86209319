{"ast": null, "code": "import arrayMap from './_arrayMap.js';\nimport createOver from './_createOver.js';\n\n/**\n * Creates a function that invokes `iteratees` with the arguments it receives\n * and returns their results.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Util\n * @param {...(Function|Function[])} [iteratees=[_.identity]]\n *  The iteratees to invoke.\n * @returns {Function} Returns the new function.\n * @example\n *\n * var func = _.over([Math.max, Math.min]);\n *\n * func(1, 2, 3, 4);\n * // => [4, 1]\n */\nvar over = createOver(arrayMap);\nexport default over;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}