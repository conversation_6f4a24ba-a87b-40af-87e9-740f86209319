{"ast": null, "code": "import { shallowRef, ref, computed, watch } from 'vue';\nimport { useFormDisabled, useFormSize } from '../../../form/src/hooks/use-form-common-props.mjs';\nimport { isUndefined } from '../../../../utils/types.mjs';\nimport { INPUT_EVENT, UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../../constants/event.mjs';\nimport { EVENT_CODE } from '../../../../constants/aria.mjs';\nimport { useFocusController } from '../../../../hooks/use-focus-controller/index.mjs';\nimport { debugWarn } from '../../../../utils/error.mjs';\nimport { useComposition } from '../../../../hooks/use-composition/index.mjs';\nfunction useInputTag({\n  props,\n  emit,\n  formItem\n}) {\n  const disabled = useFormDisabled();\n  const size = useFormSize();\n  const inputRef = shallowRef();\n  const inputValue = ref();\n  const tagSize = computed(() => {\n    return [\"small\"].includes(size.value) ? \"small\" : \"default\";\n  });\n  const placeholder = computed(() => {\n    var _a;\n    return ((_a = props.modelValue) == null ? void 0 : _a.length) ? void 0 : props.placeholder;\n  });\n  const closable = computed(() => !(props.readonly || disabled.value));\n  const inputLimit = computed(() => {\n    var _a, _b;\n    return isUndefined(props.max) ? false : ((_b = (_a = props.modelValue) == null ? void 0 : _a.length) != null ? _b : 0) >= props.max;\n  });\n  const handleInput = event => {\n    var _a, _b;\n    if (inputLimit.value) {\n      inputValue.value = void 0;\n      return;\n    }\n    if (isComposing.value) return;\n    if (props.delimiter) {\n      const replacement = (_a = inputValue.value) == null ? void 0 : _a.replace(props.delimiter, \"\");\n      if ((replacement == null ? void 0 : replacement.length) !== ((_b = inputValue.value) == null ? void 0 : _b.length)) {\n        inputValue.value = replacement;\n        handleAddTag();\n      }\n    }\n    emit(INPUT_EVENT, event.target.value);\n  };\n  const handleKeydown = event => {\n    var _a;\n    if (isComposing.value) return;\n    switch (event.code) {\n      case props.trigger:\n        event.preventDefault();\n        event.stopPropagation();\n        handleAddTag();\n        break;\n      case EVENT_CODE.numpadEnter:\n        if (props.trigger === EVENT_CODE.enter) {\n          event.preventDefault();\n          event.stopPropagation();\n          handleAddTag();\n        }\n        break;\n      case EVENT_CODE.backspace:\n        if (!inputValue.value && ((_a = props.modelValue) == null ? void 0 : _a.length)) {\n          event.preventDefault();\n          event.stopPropagation();\n          handleRemoveTag(props.modelValue.length - 1);\n        }\n        break;\n    }\n  };\n  const handleAddTag = () => {\n    var _a, _b;\n    const value = (_a = inputValue.value) == null ? void 0 : _a.trim();\n    if (!value || inputLimit.value) return;\n    const list = [...((_b = props.modelValue) != null ? _b : []), value];\n    emit(UPDATE_MODEL_EVENT, list);\n    emit(CHANGE_EVENT, list);\n    emit(\"add-tag\", value);\n    inputValue.value = void 0;\n  };\n  const handleRemoveTag = index => {\n    var _a;\n    const value = ((_a = props.modelValue) != null ? _a : []).slice();\n    const [item] = value.splice(index, 1);\n    emit(UPDATE_MODEL_EVENT, value);\n    emit(CHANGE_EVENT, value);\n    emit(\"remove-tag\", item);\n  };\n  const handleClear = () => {\n    inputValue.value = void 0;\n    emit(UPDATE_MODEL_EVENT, void 0);\n    emit(CHANGE_EVENT, void 0);\n    emit(\"clear\");\n  };\n  const handleDragged = (draggingIndex, dropIndex, type) => {\n    var _a;\n    const value = ((_a = props.modelValue) != null ? _a : []).slice();\n    const [draggedItem] = value.splice(draggingIndex, 1);\n    const step = dropIndex > draggingIndex && type === \"before\" ? -1 : dropIndex < draggingIndex && type === \"after\" ? 1 : 0;\n    value.splice(dropIndex + step, 0, draggedItem);\n    emit(UPDATE_MODEL_EVENT, value);\n    emit(CHANGE_EVENT, value);\n  };\n  const focus = () => {\n    var _a;\n    (_a = inputRef.value) == null ? void 0 : _a.focus();\n  };\n  const blur = () => {\n    var _a;\n    (_a = inputRef.value) == null ? void 0 : _a.blur();\n  };\n  const {\n    wrapperRef,\n    isFocused\n  } = useFocusController(inputRef, {\n    beforeFocus() {\n      return disabled.value;\n    },\n    afterBlur() {\n      var _a;\n      if (props.saveOnBlur) {\n        handleAddTag();\n      } else {\n        inputValue.value = void 0;\n      }\n      if (props.validateEvent) {\n        (_a = formItem == null ? void 0 : formItem.validate) == null ? void 0 : _a.call(formItem, \"blur\").catch(err => debugWarn(err));\n      }\n    }\n  });\n  const {\n    isComposing,\n    handleCompositionStart,\n    handleCompositionUpdate,\n    handleCompositionEnd\n  } = useComposition({\n    afterComposition: handleInput\n  });\n  watch(() => props.modelValue, () => {\n    var _a;\n    if (props.validateEvent) {\n      (_a = formItem == null ? void 0 : formItem.validate) == null ? void 0 : _a.call(formItem, CHANGE_EVENT).catch(err => debugWarn(err));\n    }\n  });\n  return {\n    inputRef,\n    wrapperRef,\n    isFocused,\n    isComposing,\n    inputValue,\n    size,\n    tagSize,\n    placeholder,\n    closable,\n    disabled,\n    inputLimit,\n    handleDragged,\n    handleInput,\n    handleKeydown,\n    handleAddTag,\n    handleRemoveTag,\n    handleClear,\n    handleCompositionStart,\n    handleCompositionUpdate,\n    handleCompositionEnd,\n    focus,\n    blur\n  };\n}\nexport { useInputTag };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}