{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define(t) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_isSameOrAfter = t();\n}(this, function () {\n  \"use strict\";\n\n  return function (e, t) {\n    t.prototype.isSameOrAfter = function (e, t) {\n      return this.isSame(e, t) || this.isAfter(e, t);\n    };\n  };\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}