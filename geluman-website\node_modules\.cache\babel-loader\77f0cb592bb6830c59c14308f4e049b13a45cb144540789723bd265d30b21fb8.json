{"ast": null, "code": "import { defineComponent, inject, computed, resolveComponent, openBlock, createElementBlock, Fragment, normalizeClass, createCommentVNode, createElementVNode, mergeProps, withModifiers, createBlock, withCtx, resolveDynamicComponent, renderSlot } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { dropdownItemProps, DROPDOWN_COLLECTION_ITEM_INJECTION_KEY as COLLECTION_ITEM_INJECTION_KEY } from './dropdown.mjs';\nimport { DROPDOWN_INJECTION_KEY } from './tokens.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { ROVING_FOCUS_ITEM_COLLECTION_INJECTION_KEY as COLLECTION_ITEM_INJECTION_KEY$1 } from '../../roving-focus-group/src/roving-focus-group.mjs';\nimport { ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY } from '../../roving-focus-group/src/tokens.mjs';\nimport { COLLECTION_ITEM_SIGN } from '../../collection/src/collection2.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { composeRefs } from '../../../utils/vue/refs.mjs';\nimport { composeEventHandlers } from '../../../utils/dom/event.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nconst _sfc_main = defineComponent({\n  name: \"DropdownItemImpl\",\n  components: {\n    ElIcon\n  },\n  props: dropdownItemProps,\n  emits: [\"pointermove\", \"pointerleave\", \"click\", \"clickimpl\"],\n  setup(_, {\n    emit\n  }) {\n    const ns = useNamespace(\"dropdown\");\n    const {\n      role: menuRole\n    } = inject(DROPDOWN_INJECTION_KEY, void 0);\n    const {\n      collectionItemRef: dropdownCollectionItemRef\n    } = inject(COLLECTION_ITEM_INJECTION_KEY, void 0);\n    const {\n      collectionItemRef: rovingFocusCollectionItemRef\n    } = inject(COLLECTION_ITEM_INJECTION_KEY$1, void 0);\n    const {\n      rovingFocusGroupItemRef,\n      tabIndex,\n      handleFocus,\n      handleKeydown: handleItemKeydown,\n      handleMousedown\n    } = inject(ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY, void 0);\n    const itemRef = composeRefs(dropdownCollectionItemRef, rovingFocusCollectionItemRef, rovingFocusGroupItemRef);\n    const role = computed(() => {\n      if (menuRole.value === \"menu\") {\n        return \"menuitem\";\n      } else if (menuRole.value === \"navigation\") {\n        return \"link\";\n      }\n      return \"button\";\n    });\n    const handleKeydown = composeEventHandlers(e => {\n      if ([EVENT_CODE.enter, EVENT_CODE.numpadEnter, EVENT_CODE.space].includes(e.code)) {\n        e.preventDefault();\n        e.stopImmediatePropagation();\n        emit(\"clickimpl\", e);\n        return true;\n      }\n    }, handleItemKeydown);\n    return {\n      ns,\n      itemRef,\n      dataset: {\n        [COLLECTION_ITEM_SIGN]: \"\"\n      },\n      role,\n      tabIndex,\n      handleFocus,\n      handleKeydown,\n      handleMousedown\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = resolveComponent(\"el-icon\");\n  return openBlock(), createElementBlock(Fragment, null, [_ctx.divided ? (openBlock(), createElementBlock(\"li\", {\n    key: 0,\n    role: \"separator\",\n    class: normalizeClass(_ctx.ns.bem(\"menu\", \"item\", \"divided\"))\n  }, null, 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"li\", mergeProps({\n    ref: _ctx.itemRef\n  }, {\n    ..._ctx.dataset,\n    ..._ctx.$attrs\n  }, {\n    \"aria-disabled\": _ctx.disabled,\n    class: [_ctx.ns.be(\"menu\", \"item\"), _ctx.ns.is(\"disabled\", _ctx.disabled)],\n    tabindex: _ctx.tabIndex,\n    role: _ctx.role,\n    onClick: e => _ctx.$emit(\"clickimpl\", e),\n    onFocus: _ctx.handleFocus,\n    onKeydown: withModifiers(_ctx.handleKeydown, [\"self\"]),\n    onMousedown: _ctx.handleMousedown,\n    onPointermove: e => _ctx.$emit(\"pointermove\", e),\n    onPointerleave: e => _ctx.$emit(\"pointerleave\", e)\n  }), [_ctx.icon ? (openBlock(), createBlock(_component_el_icon, {\n    key: 0\n  }, {\n    default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.icon)))]),\n    _: 1\n  })) : createCommentVNode(\"v-if\", true), renderSlot(_ctx.$slots, \"default\")], 16, [\"aria-disabled\", \"tabindex\", \"role\", \"onClick\", \"onFocus\", \"onKeydown\", \"onMousedown\", \"onPointermove\", \"onPointerleave\"])], 64);\n}\nvar ElDropdownItemImpl = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"dropdown-item-impl.vue\"]]);\nexport { ElDropdownItemImpl as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}