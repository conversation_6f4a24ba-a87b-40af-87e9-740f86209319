{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, getCurrentInstance, ref, computed, unref, onMounted, nextTick, resolveDynamicComponent, h, Fragment } from 'vue';\nimport { useEventListener, isClient } from '@vueuse/core';\nimport ScrollBar from '../components/scrollbar.mjs';\nimport { useGridWheel } from '../hooks/use-grid-wheel.mjs';\nimport { useCache } from '../hooks/use-cache.mjs';\nimport { virtualizedGridProps } from '../props.mjs';\nimport { getScrollDir, getRTLOffsetType, isRTL } from '../utils.mjs';\nimport { ITEM_RENDER_EVT, SCROLL_EVT, FORWARD, BACKWARD, AUTO_ALIGNMENT, RTL, RTL_OFFSET_POS_ASC, RTL_OFFSET_NAG, RTL_OFFSET_POS_DESC } from '../defaults.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isNumber } from '../../../../utils/types.mjs';\nimport { getScrollBarWidth } from '../../../../utils/dom/scroll.mjs';\nimport { isString, hasOwn } from '@vue/shared';\nconst createGrid = ({\n  name,\n  clearCache,\n  getColumnPosition,\n  getColumnStartIndexForOffset,\n  getColumnStopIndexForStartIndex,\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n  getColumnOffset,\n  getRowOffset,\n  getRowPosition,\n  getRowStartIndexForOffset,\n  getRowStopIndexForStartIndex,\n  initCache,\n  injectToInstance,\n  validateProps\n}) => {\n  return defineComponent({\n    name: name != null ? name : \"ElVirtualList\",\n    props: virtualizedGridProps,\n    emits: [ITEM_RENDER_EVT, SCROLL_EVT],\n    setup(props, {\n      emit,\n      expose,\n      slots\n    }) {\n      const ns = useNamespace(\"vl\");\n      validateProps(props);\n      const instance = getCurrentInstance();\n      const cache = ref(initCache(props, instance));\n      injectToInstance == null ? void 0 : injectToInstance(instance, cache);\n      const windowRef = ref();\n      const hScrollbar = ref();\n      const vScrollbar = ref();\n      const innerRef = ref(null);\n      const states = ref({\n        isScrolling: false,\n        scrollLeft: isNumber(props.initScrollLeft) ? props.initScrollLeft : 0,\n        scrollTop: isNumber(props.initScrollTop) ? props.initScrollTop : 0,\n        updateRequested: false,\n        xAxisScrollDir: FORWARD,\n        yAxisScrollDir: FORWARD\n      });\n      const getItemStyleCache = useCache();\n      const parsedHeight = computed(() => Number.parseInt(`${props.height}`, 10));\n      const parsedWidth = computed(() => Number.parseInt(`${props.width}`, 10));\n      const columnsToRender = computed(() => {\n        const {\n          totalColumn,\n          totalRow,\n          columnCache\n        } = props;\n        const {\n          isScrolling,\n          xAxisScrollDir,\n          scrollLeft\n        } = unref(states);\n        if (totalColumn === 0 || totalRow === 0) {\n          return [0, 0, 0, 0];\n        }\n        const startIndex = getColumnStartIndexForOffset(props, scrollLeft, unref(cache));\n        const stopIndex = getColumnStopIndexForStartIndex(props, startIndex, scrollLeft, unref(cache));\n        const cacheBackward = !isScrolling || xAxisScrollDir === BACKWARD ? Math.max(1, columnCache) : 1;\n        const cacheForward = !isScrolling || xAxisScrollDir === FORWARD ? Math.max(1, columnCache) : 1;\n        return [Math.max(0, startIndex - cacheBackward), Math.max(0, Math.min(totalColumn - 1, stopIndex + cacheForward)), startIndex, stopIndex];\n      });\n      const rowsToRender = computed(() => {\n        const {\n          totalColumn,\n          totalRow,\n          rowCache\n        } = props;\n        const {\n          isScrolling,\n          yAxisScrollDir,\n          scrollTop\n        } = unref(states);\n        if (totalColumn === 0 || totalRow === 0) {\n          return [0, 0, 0, 0];\n        }\n        const startIndex = getRowStartIndexForOffset(props, scrollTop, unref(cache));\n        const stopIndex = getRowStopIndexForStartIndex(props, startIndex, scrollTop, unref(cache));\n        const cacheBackward = !isScrolling || yAxisScrollDir === BACKWARD ? Math.max(1, rowCache) : 1;\n        const cacheForward = !isScrolling || yAxisScrollDir === FORWARD ? Math.max(1, rowCache) : 1;\n        return [Math.max(0, startIndex - cacheBackward), Math.max(0, Math.min(totalRow - 1, stopIndex + cacheForward)), startIndex, stopIndex];\n      });\n      const estimatedTotalHeight = computed(() => getEstimatedTotalHeight(props, unref(cache)));\n      const estimatedTotalWidth = computed(() => getEstimatedTotalWidth(props, unref(cache)));\n      const windowStyle = computed(() => {\n        var _a;\n        return [{\n          position: \"relative\",\n          overflow: \"hidden\",\n          WebkitOverflowScrolling: \"touch\",\n          willChange: \"transform\"\n        }, {\n          direction: props.direction,\n          height: isNumber(props.height) ? `${props.height}px` : props.height,\n          width: isNumber(props.width) ? `${props.width}px` : props.width\n        }, (_a = props.style) != null ? _a : {}];\n      });\n      const innerStyle = computed(() => {\n        const width = `${unref(estimatedTotalWidth)}px`;\n        const height = `${unref(estimatedTotalHeight)}px`;\n        return {\n          height,\n          pointerEvents: unref(states).isScrolling ? \"none\" : void 0,\n          width\n        };\n      });\n      const emitEvents = () => {\n        const {\n          totalColumn,\n          totalRow\n        } = props;\n        if (totalColumn > 0 && totalRow > 0) {\n          const [columnCacheStart, columnCacheEnd, columnVisibleStart, columnVisibleEnd] = unref(columnsToRender);\n          const [rowCacheStart, rowCacheEnd, rowVisibleStart, rowVisibleEnd] = unref(rowsToRender);\n          emit(ITEM_RENDER_EVT, {\n            columnCacheStart,\n            columnCacheEnd,\n            rowCacheStart,\n            rowCacheEnd,\n            columnVisibleStart,\n            columnVisibleEnd,\n            rowVisibleStart,\n            rowVisibleEnd\n          });\n        }\n        const {\n          scrollLeft,\n          scrollTop,\n          updateRequested,\n          xAxisScrollDir,\n          yAxisScrollDir\n        } = unref(states);\n        emit(SCROLL_EVT, {\n          xAxisScrollDir,\n          scrollLeft,\n          yAxisScrollDir,\n          scrollTop,\n          updateRequested\n        });\n      };\n      const onScroll = e => {\n        const {\n          clientHeight,\n          clientWidth,\n          scrollHeight,\n          scrollLeft,\n          scrollTop,\n          scrollWidth\n        } = e.currentTarget;\n        const _states = unref(states);\n        if (_states.scrollTop === scrollTop && _states.scrollLeft === scrollLeft) {\n          return;\n        }\n        let _scrollLeft = scrollLeft;\n        if (isRTL(props.direction)) {\n          switch (getRTLOffsetType()) {\n            case RTL_OFFSET_NAG:\n              _scrollLeft = -scrollLeft;\n              break;\n            case RTL_OFFSET_POS_DESC:\n              _scrollLeft = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        }\n        states.value = {\n          ..._states,\n          isScrolling: true,\n          scrollLeft: _scrollLeft,\n          scrollTop: Math.max(0, Math.min(scrollTop, scrollHeight - clientHeight)),\n          updateRequested: true,\n          xAxisScrollDir: getScrollDir(_states.scrollLeft, _scrollLeft),\n          yAxisScrollDir: getScrollDir(_states.scrollTop, scrollTop)\n        };\n        nextTick(() => resetIsScrolling());\n        onUpdated();\n        emitEvents();\n      };\n      const onVerticalScroll = (distance, totalSteps) => {\n        const height = unref(parsedHeight);\n        const offset = (estimatedTotalHeight.value - height) / totalSteps * distance;\n        scrollTo({\n          scrollTop: Math.min(estimatedTotalHeight.value - height, offset)\n        });\n      };\n      const onHorizontalScroll = (distance, totalSteps) => {\n        const width = unref(parsedWidth);\n        const offset = (estimatedTotalWidth.value - width) / totalSteps * distance;\n        scrollTo({\n          scrollLeft: Math.min(estimatedTotalWidth.value - width, offset)\n        });\n      };\n      const {\n        onWheel\n      } = useGridWheel({\n        atXStartEdge: computed(() => states.value.scrollLeft <= 0),\n        atXEndEdge: computed(() => states.value.scrollLeft >= estimatedTotalWidth.value - unref(parsedWidth)),\n        atYStartEdge: computed(() => states.value.scrollTop <= 0),\n        atYEndEdge: computed(() => states.value.scrollTop >= estimatedTotalHeight.value - unref(parsedHeight))\n      }, (x, y) => {\n        var _a, _b, _c, _d;\n        (_b = (_a = hScrollbar.value) == null ? void 0 : _a.onMouseUp) == null ? void 0 : _b.call(_a);\n        (_d = (_c = vScrollbar.value) == null ? void 0 : _c.onMouseUp) == null ? void 0 : _d.call(_c);\n        const width = unref(parsedWidth);\n        const height = unref(parsedHeight);\n        scrollTo({\n          scrollLeft: Math.min(states.value.scrollLeft + x, estimatedTotalWidth.value - width),\n          scrollTop: Math.min(states.value.scrollTop + y, estimatedTotalHeight.value - height)\n        });\n      });\n      useEventListener(windowRef, \"wheel\", onWheel, {\n        passive: false\n      });\n      const scrollTo = ({\n        scrollLeft = states.value.scrollLeft,\n        scrollTop = states.value.scrollTop\n      }) => {\n        scrollLeft = Math.max(scrollLeft, 0);\n        scrollTop = Math.max(scrollTop, 0);\n        const _states = unref(states);\n        if (scrollTop === _states.scrollTop && scrollLeft === _states.scrollLeft) {\n          return;\n        }\n        states.value = {\n          ..._states,\n          xAxisScrollDir: getScrollDir(_states.scrollLeft, scrollLeft),\n          yAxisScrollDir: getScrollDir(_states.scrollTop, scrollTop),\n          scrollLeft,\n          scrollTop,\n          updateRequested: true\n        };\n        nextTick(() => resetIsScrolling());\n        onUpdated();\n        emitEvents();\n      };\n      const scrollToItem = (rowIndex = 0, columnIdx = 0, alignment = AUTO_ALIGNMENT) => {\n        const _states = unref(states);\n        columnIdx = Math.max(0, Math.min(columnIdx, props.totalColumn - 1));\n        rowIndex = Math.max(0, Math.min(rowIndex, props.totalRow - 1));\n        const scrollBarWidth = getScrollBarWidth(ns.namespace.value);\n        const _cache = unref(cache);\n        const estimatedHeight = getEstimatedTotalHeight(props, _cache);\n        const estimatedWidth = getEstimatedTotalWidth(props, _cache);\n        scrollTo({\n          scrollLeft: getColumnOffset(props, columnIdx, alignment, _states.scrollLeft, _cache, estimatedWidth > props.width ? scrollBarWidth : 0),\n          scrollTop: getRowOffset(props, rowIndex, alignment, _states.scrollTop, _cache, estimatedHeight > props.height ? scrollBarWidth : 0)\n        });\n      };\n      const getItemStyle = (rowIndex, columnIndex) => {\n        const {\n          columnWidth,\n          direction,\n          rowHeight\n        } = props;\n        const itemStyleCache = getItemStyleCache.value(clearCache && columnWidth, clearCache && rowHeight, clearCache && direction);\n        const key = `${rowIndex},${columnIndex}`;\n        if (hasOwn(itemStyleCache, key)) {\n          return itemStyleCache[key];\n        } else {\n          const [, left] = getColumnPosition(props, columnIndex, unref(cache));\n          const _cache = unref(cache);\n          const rtl = isRTL(direction);\n          const [height, top] = getRowPosition(props, rowIndex, _cache);\n          const [width] = getColumnPosition(props, columnIndex, _cache);\n          itemStyleCache[key] = {\n            position: \"absolute\",\n            left: rtl ? void 0 : `${left}px`,\n            right: rtl ? `${left}px` : void 0,\n            top: `${top}px`,\n            height: `${height}px`,\n            width: `${width}px`\n          };\n          return itemStyleCache[key];\n        }\n      };\n      const resetIsScrolling = () => {\n        states.value.isScrolling = false;\n        nextTick(() => {\n          getItemStyleCache.value(-1, null, null);\n        });\n      };\n      onMounted(() => {\n        if (!isClient) return;\n        const {\n          initScrollLeft,\n          initScrollTop\n        } = props;\n        const windowElement = unref(windowRef);\n        if (windowElement) {\n          if (isNumber(initScrollLeft)) {\n            windowElement.scrollLeft = initScrollLeft;\n          }\n          if (isNumber(initScrollTop)) {\n            windowElement.scrollTop = initScrollTop;\n          }\n        }\n        emitEvents();\n      });\n      const onUpdated = () => {\n        const {\n          direction\n        } = props;\n        const {\n          scrollLeft,\n          scrollTop,\n          updateRequested\n        } = unref(states);\n        const windowElement = unref(windowRef);\n        if (updateRequested && windowElement) {\n          if (direction === RTL) {\n            switch (getRTLOffsetType()) {\n              case RTL_OFFSET_NAG:\n                {\n                  windowElement.scrollLeft = -scrollLeft;\n                  break;\n                }\n              case RTL_OFFSET_POS_ASC:\n                {\n                  windowElement.scrollLeft = scrollLeft;\n                  break;\n                }\n              default:\n                {\n                  const {\n                    clientWidth,\n                    scrollWidth\n                  } = windowElement;\n                  windowElement.scrollLeft = scrollWidth - clientWidth - scrollLeft;\n                  break;\n                }\n            }\n          } else {\n            windowElement.scrollLeft = Math.max(0, scrollLeft);\n          }\n          windowElement.scrollTop = Math.max(0, scrollTop);\n        }\n      };\n      const {\n        resetAfterColumnIndex,\n        resetAfterRowIndex,\n        resetAfter\n      } = instance.proxy;\n      expose({\n        windowRef,\n        innerRef,\n        getItemStyleCache,\n        scrollTo,\n        scrollToItem,\n        states,\n        resetAfterColumnIndex,\n        resetAfterRowIndex,\n        resetAfter\n      });\n      const renderScrollbars = () => {\n        const {\n          scrollbarAlwaysOn,\n          scrollbarStartGap,\n          scrollbarEndGap,\n          totalColumn,\n          totalRow\n        } = props;\n        const width = unref(parsedWidth);\n        const height = unref(parsedHeight);\n        const estimatedWidth = unref(estimatedTotalWidth);\n        const estimatedHeight = unref(estimatedTotalHeight);\n        const {\n          scrollLeft,\n          scrollTop\n        } = unref(states);\n        const horizontalScrollbar = h(ScrollBar, {\n          ref: hScrollbar,\n          alwaysOn: scrollbarAlwaysOn,\n          startGap: scrollbarStartGap,\n          endGap: scrollbarEndGap,\n          class: ns.e(\"horizontal\"),\n          clientSize: width,\n          layout: \"horizontal\",\n          onScroll: onHorizontalScroll,\n          ratio: width * 100 / estimatedWidth,\n          scrollFrom: scrollLeft / (estimatedWidth - width),\n          total: totalRow,\n          visible: true\n        });\n        const verticalScrollbar = h(ScrollBar, {\n          ref: vScrollbar,\n          alwaysOn: scrollbarAlwaysOn,\n          startGap: scrollbarStartGap,\n          endGap: scrollbarEndGap,\n          class: ns.e(\"vertical\"),\n          clientSize: height,\n          layout: \"vertical\",\n          onScroll: onVerticalScroll,\n          ratio: height * 100 / estimatedHeight,\n          scrollFrom: scrollTop / (estimatedHeight - height),\n          total: totalColumn,\n          visible: true\n        });\n        return {\n          horizontalScrollbar,\n          verticalScrollbar\n        };\n      };\n      const renderItems = () => {\n        var _a;\n        const [columnStart, columnEnd] = unref(columnsToRender);\n        const [rowStart, rowEnd] = unref(rowsToRender);\n        const {\n          data,\n          totalColumn,\n          totalRow,\n          useIsScrolling,\n          itemKey\n        } = props;\n        const children = [];\n        if (totalRow > 0 && totalColumn > 0) {\n          for (let row = rowStart; row <= rowEnd; row++) {\n            for (let column = columnStart; column <= columnEnd; column++) {\n              const key = itemKey({\n                columnIndex: column,\n                data,\n                rowIndex: row\n              });\n              children.push(h(Fragment, {\n                key\n              }, (_a = slots.default) == null ? void 0 : _a.call(slots, {\n                columnIndex: column,\n                data,\n                isScrolling: useIsScrolling ? unref(states).isScrolling : void 0,\n                style: getItemStyle(row, column),\n                rowIndex: row\n              })));\n            }\n          }\n        }\n        return children;\n      };\n      const renderInner = () => {\n        const Inner = resolveDynamicComponent(props.innerElement);\n        const children = renderItems();\n        return [h(Inner, {\n          style: unref(innerStyle),\n          ref: innerRef\n        }, !isString(Inner) ? {\n          default: () => children\n        } : children)];\n      };\n      const renderWindow = () => {\n        const Container = resolveDynamicComponent(props.containerElement);\n        const {\n          horizontalScrollbar,\n          verticalScrollbar\n        } = renderScrollbars();\n        const Inner = renderInner();\n        return h(\"div\", {\n          key: 0,\n          class: ns.e(\"wrapper\"),\n          role: props.role\n        }, [h(Container, {\n          class: props.className,\n          style: unref(windowStyle),\n          onScroll,\n          ref: windowRef\n        }, !isString(Container) ? {\n          default: () => Inner\n        } : Inner), horizontalScrollbar, verticalScrollbar]);\n      };\n      return renderWindow;\n    }\n  });\n};\nexport { createGrid as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}