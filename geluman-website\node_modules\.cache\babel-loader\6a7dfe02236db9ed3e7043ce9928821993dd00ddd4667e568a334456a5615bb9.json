{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport arrayEach from './_arrayEach.js';\nimport arrayIncludes from './_arrayIncludes.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1,\n  WRAP_BIND_KEY_FLAG = 2,\n  WRAP_CURRY_FLAG = 8,\n  WRAP_CURRY_RIGHT_FLAG = 16,\n  WRAP_PARTIAL_FLAG = 32,\n  WRAP_PARTIAL_RIGHT_FLAG = 64,\n  WRAP_ARY_FLAG = 128,\n  WRAP_REARG_FLAG = 256,\n  WRAP_FLIP_FLAG = 512;\n\n/** Used to associate wrap methods with their bit flags. */\nvar wrapFlags = [['ary', WRAP_ARY_FLAG], ['bind', WRAP_BIND_FLAG], ['bindKey', WRAP_BIND_KEY_FLAG], ['curry', WRAP_CURRY_FLAG], ['curryRight', WRAP_CURRY_RIGHT_FLAG], ['flip', WRAP_<PERSON>IP_FLAG], ['partial', WRAP_PARTIAL_FLAG], ['partialRight', WRAP_PARTIAL_RIGHT_FLAG], ['rearg', WRAP_REARG_FLAG]];\n\n/**\n * Updates wrapper `details` based on `bitmask` flags.\n *\n * @private\n * @returns {Array} details The details to modify.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @returns {Array} Returns `details`.\n */\nfunction updateWrapDetails(details, bitmask) {\n  arrayEach(wrapFlags, function (pair) {\n    var value = '_.' + pair[0];\n    if (bitmask & pair[1] && !arrayIncludes(details, value)) {\n      details.push(value);\n    }\n  });\n  return details.sort();\n}\nexport default updateWrapDetails;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}