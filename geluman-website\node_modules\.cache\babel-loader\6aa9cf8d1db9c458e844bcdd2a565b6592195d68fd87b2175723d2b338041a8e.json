{"ast": null, "code": "import { computed } from 'vue';\nimport { TinyColor } from '@ctrl/tinycolor';\nimport { useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nfunction darken(color, amount = 20) {\n  return color.mix(\"#141414\", amount).toString();\n}\nfunction useButtonCustomStyle(props) {\n  const _disabled = useFormDisabled();\n  const ns = useNamespace(\"button\");\n  return computed(() => {\n    let styles = {};\n    let buttonColor = props.color;\n    if (buttonColor) {\n      const match = buttonColor.match(/var\\((.*?)\\)/);\n      if (match) {\n        buttonColor = window.getComputedStyle(window.document.documentElement).getPropertyValue(match[1]);\n      }\n      const color = new TinyColor(buttonColor);\n      const activeBgColor = props.dark ? color.tint(20).toString() : darken(color, 20);\n      if (props.plain) {\n        styles = ns.cssVarBlock({\n          \"bg-color\": props.dark ? darken(color, 90) : color.tint(90).toString(),\n          \"text-color\": buttonColor,\n          \"border-color\": props.dark ? darken(color, 50) : color.tint(50).toString(),\n          \"hover-text-color\": `var(${ns.cssVarName(\"color-white\")})`,\n          \"hover-bg-color\": buttonColor,\n          \"hover-border-color\": buttonColor,\n          \"active-bg-color\": activeBgColor,\n          \"active-text-color\": `var(${ns.cssVarName(\"color-white\")})`,\n          \"active-border-color\": activeBgColor\n        });\n        if (_disabled.value) {\n          styles[ns.cssVarBlockName(\"disabled-bg-color\")] = props.dark ? darken(color, 90) : color.tint(90).toString();\n          styles[ns.cssVarBlockName(\"disabled-text-color\")] = props.dark ? darken(color, 50) : color.tint(50).toString();\n          styles[ns.cssVarBlockName(\"disabled-border-color\")] = props.dark ? darken(color, 80) : color.tint(80).toString();\n        }\n      } else {\n        const hoverBgColor = props.dark ? darken(color, 30) : color.tint(30).toString();\n        const textColor = color.isDark() ? `var(${ns.cssVarName(\"color-white\")})` : `var(${ns.cssVarName(\"color-black\")})`;\n        styles = ns.cssVarBlock({\n          \"bg-color\": buttonColor,\n          \"text-color\": textColor,\n          \"border-color\": buttonColor,\n          \"hover-bg-color\": hoverBgColor,\n          \"hover-text-color\": textColor,\n          \"hover-border-color\": hoverBgColor,\n          \"active-bg-color\": activeBgColor,\n          \"active-border-color\": activeBgColor\n        });\n        if (_disabled.value) {\n          const disabledButtonColor = props.dark ? darken(color, 50) : color.tint(50).toString();\n          styles[ns.cssVarBlockName(\"disabled-bg-color\")] = disabledButtonColor;\n          styles[ns.cssVarBlockName(\"disabled-text-color\")] = props.dark ? \"rgba(255, 255, 255, 0.5)\" : `var(${ns.cssVarName(\"color-white\")})`;\n          styles[ns.cssVarBlockName(\"disabled-border-color\")] = disabledButtonColor;\n        }\n      }\n    }\n    return styles;\n  });\n}\nexport { darken, useButtonCustomStyle };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}