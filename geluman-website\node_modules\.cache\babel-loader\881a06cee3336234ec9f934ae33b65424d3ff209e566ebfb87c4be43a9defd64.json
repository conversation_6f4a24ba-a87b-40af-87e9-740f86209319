{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { isVNode, h } from 'vue';\nimport { isArray, isFunction } from '@vue/shared';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nconst sumReducer = (sum2, num) => sum2 + num;\nconst sum = listLike => {\n  return isArray(listLike) ? listLike.reduce(sumReducer, 0) : listLike;\n};\nconst tryCall = (fLike, params, defaultRet = {}) => {\n  return isFunction(fLike) ? fLike(params) : fLike != null ? fLike : defaultRet;\n};\nconst enforceUnit = style => {\n  [\"width\", \"maxWidth\", \"minWidth\", \"height\"].forEach(key => {\n    style[key] = addUnit(style[key]);\n  });\n  return style;\n};\nconst componentToSlot = ComponentLike => isVNode(ComponentLike) ? props => h(ComponentLike, props) : ComponentLike;\nexport { componentToSlot, enforceUnit, sum, tryCall };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}