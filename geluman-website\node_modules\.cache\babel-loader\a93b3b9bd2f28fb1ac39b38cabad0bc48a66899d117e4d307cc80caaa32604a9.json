{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { unref, ref, onMounted, watchEffect, isRef } from 'vue';\nimport { isClient, unrefElement } from '@vueuse/core';\nimport { isNil } from 'lodash-unified';\nimport { arrow, computePosition } from '@floating-ui/dom';\nimport { keysOf } from '../../utils/objects.mjs';\nimport { buildProps } from '../../utils/vue/props/runtime.mjs';\nconst useFloatingProps = buildProps({});\nconst unrefReference = elRef => {\n  if (!isClient) return;\n  if (!elRef) return elRef;\n  const unrefEl = unrefElement(elRef);\n  if (unrefEl) return unrefEl;\n  return isRef(elRef) ? unrefEl : elRef;\n};\nconst getPositionDataWithUnit = (record, key) => {\n  const value = record == null ? void 0 : record[key];\n  return isNil(value) ? \"\" : `${value}px`;\n};\nconst useFloating = ({\n  middleware,\n  placement,\n  strategy\n}) => {\n  const referenceRef = ref();\n  const contentRef = ref();\n  const x = ref();\n  const y = ref();\n  const middlewareData = ref({});\n  const states = {\n    x,\n    y,\n    placement,\n    strategy,\n    middlewareData\n  };\n  const update = async () => {\n    if (!isClient) return;\n    const referenceEl = unrefReference(referenceRef);\n    const contentEl = unrefElement(contentRef);\n    if (!referenceEl || !contentEl) return;\n    const data = await computePosition(referenceEl, contentEl, {\n      placement: unref(placement),\n      strategy: unref(strategy),\n      middleware: unref(middleware)\n    });\n    keysOf(states).forEach(key => {\n      states[key].value = data[key];\n    });\n  };\n  onMounted(() => {\n    watchEffect(() => {\n      update();\n    });\n  });\n  return {\n    ...states,\n    update,\n    referenceRef,\n    contentRef\n  };\n};\nconst arrowMiddleware = ({\n  arrowRef,\n  padding\n}) => {\n  return {\n    name: \"arrow\",\n    options: {\n      element: arrowRef,\n      padding\n    },\n    fn(args) {\n      const arrowEl = unref(arrowRef);\n      if (!arrowEl) return {};\n      return arrow({\n        element: arrowEl,\n        padding\n      }).fn(args);\n    }\n  };\n};\nexport { arrowMiddleware, getPositionDataWithUnit, useFloating, useFloatingProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}