{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { defineComponent, inject, h } from 'vue';\nimport useLayoutObserver from '../layout-observer.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport useStyle from './style-helper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nvar TableFooter = defineComponent({\n  name: \"ElTableFooter\",\n  props: {\n    fixed: {\n      type: String,\n      default: \"\"\n    },\n    store: {\n      required: true,\n      type: Object\n    },\n    summaryMethod: Function,\n    sumText: String,\n    border: Boolean,\n    defaultSort: {\n      type: Object,\n      default: () => {\n        return {\n          prop: \"\",\n          order: \"\"\n        };\n      }\n    }\n  },\n  setup(props) {\n    const parent = inject(TABLE_INJECTION_KEY);\n    const ns = useNamespace(\"table\");\n    const {\n      getCellClasses,\n      getCellStyles,\n      columns\n    } = useStyle(props);\n    const {\n      onScrollableChange,\n      onColumnsChange\n    } = useLayoutObserver(parent);\n    return {\n      ns,\n      onScrollableChange,\n      onColumnsChange,\n      getCellClasses,\n      getCellStyles,\n      columns\n    };\n  },\n  render() {\n    const {\n      columns,\n      getCellStyles,\n      getCellClasses,\n      summaryMethod,\n      sumText\n    } = this;\n    const data = this.store.states.data.value;\n    let sums = [];\n    if (summaryMethod) {\n      sums = summaryMethod({\n        columns,\n        data\n      });\n    } else {\n      columns.forEach((column, index) => {\n        if (index === 0) {\n          sums[index] = sumText;\n          return;\n        }\n        const values = data.map(item => Number(item[column.property]));\n        const precisions = [];\n        let notNumber = true;\n        values.forEach(value => {\n          if (!Number.isNaN(+value)) {\n            notNumber = false;\n            const decimal = `${value}`.split(\".\")[1];\n            precisions.push(decimal ? decimal.length : 0);\n          }\n        });\n        const precision = Math.max.apply(null, precisions);\n        if (!notNumber) {\n          sums[index] = values.reduce((prev, curr) => {\n            const value = Number(curr);\n            if (!Number.isNaN(+value)) {\n              return Number.parseFloat((prev + curr).toFixed(Math.min(precision, 20)));\n            } else {\n              return prev;\n            }\n          }, 0);\n        } else {\n          sums[index] = \"\";\n        }\n      });\n    }\n    return h(h(\"tfoot\", [h(\"tr\", {}, [...columns.map((column, cellIndex) => h(\"td\", {\n      key: cellIndex,\n      colspan: column.colSpan,\n      rowspan: column.rowSpan,\n      class: getCellClasses(columns, cellIndex),\n      style: getCellStyles(column, cellIndex)\n    }, [h(\"div\", {\n      class: [\"cell\", column.labelClassName]\n    }, [sums[cellIndex]])]))])]));\n  }\n});\nexport { TableFooter as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}