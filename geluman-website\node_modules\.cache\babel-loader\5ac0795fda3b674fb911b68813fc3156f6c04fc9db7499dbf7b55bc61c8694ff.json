{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\n/*!\n  * shared v9.14.2\n  * (c) 2024 ka<PERSON>ya kawa<PERSON>\n  * Released under the MIT License.\n  */\n/**\n * Original Utilities\n * written by ka<PERSON><PERSON> kawa<PERSON>\n */\nconst inBrowser = typeof window !== 'undefined';\nlet mark;\nlet measure;\nif (process.env.NODE_ENV !== 'production') {\n  const perf = inBrowser && window.performance;\n  if (perf && perf.mark && perf.measure && perf.clearMarks &&\n  // @ts-ignore browser compat\n  perf.clearMeasures) {\n    mark = tag => {\n      perf.mark(tag);\n    };\n    measure = (name, startTag, endTag) => {\n      perf.measure(name, startTag, endTag);\n      perf.clearMarks(startTag);\n      perf.clearMarks(endTag);\n    };\n  }\n}\nconst RE_ARGS = /\\{([0-9a-zA-Z]+)\\}/g;\n/* eslint-disable */\nfunction format(message, ...args) {\n  if (args.length === 1 && isObject(args[0])) {\n    args = args[0];\n  }\n  if (!args || !args.hasOwnProperty) {\n    args = {};\n  }\n  return message.replace(RE_ARGS, (match, identifier) => {\n    return args.hasOwnProperty(identifier) ? args[identifier] : '';\n  });\n}\nconst makeSymbol = (name, shareable = false) => !shareable ? Symbol(name) : Symbol.for(name);\nconst generateFormatCacheKey = (locale, key, source) => friendlyJSONstringify({\n  l: locale,\n  k: key,\n  s: source\n});\nconst friendlyJSONstringify = json => JSON.stringify(json).replace(/\\u2028/g, '\\\\u2028').replace(/\\u2029/g, '\\\\u2029').replace(/\\u0027/g, '\\\\u0027');\nconst isNumber = val => typeof val === 'number' && isFinite(val);\nconst isDate = val => toTypeString(val) === '[object Date]';\nconst isRegExp = val => toTypeString(val) === '[object RegExp]';\nconst isEmptyObject = val => isPlainObject(val) && Object.keys(val).length === 0;\nconst assign = Object.assign;\nconst _create = Object.create;\nconst create = (obj = null) => _create(obj);\nlet _globalThis;\nconst getGlobalThis = () => {\n  // prettier-ignore\n  return _globalThis || (_globalThis = typeof globalThis !== 'undefined' ? globalThis : typeof self !== 'undefined' ? self : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : create());\n};\nfunction escapeHtml(rawText) {\n  return rawText.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\"/g, '&quot;').replace(/'/g, '&apos;');\n}\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction hasOwn(obj, key) {\n  return hasOwnProperty.call(obj, key);\n}\n/* eslint-enable */\n/**\n * Useful Utilities By Evan you\n * Modified by kazuya kawaguchi\n * MIT License\n * https://github.com/vuejs/vue-next/blob/master/packages/shared/src/index.ts\n * https://github.com/vuejs/vue-next/blob/master/packages/shared/src/codeframe.ts\n */\nconst isArray = Array.isArray;\nconst isFunction = val => typeof val === 'function';\nconst isString = val => typeof val === 'string';\nconst isBoolean = val => typeof val === 'boolean';\nconst isSymbol = val => typeof val === 'symbol';\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst isObject = val => val !== null && typeof val === 'object';\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst isPromise = val => {\n  return isObject(val) && isFunction(val.then) && isFunction(val.catch);\n};\nconst objectToString = Object.prototype.toString;\nconst toTypeString = value => objectToString.call(value);\nconst isPlainObject = val => {\n  if (!isObject(val)) return false;\n  const proto = Object.getPrototypeOf(val);\n  return proto === null || proto.constructor === Object;\n};\n// for converting list and named values to displayed strings.\nconst toDisplayString = val => {\n  return val == null ? '' : isArray(val) || isPlainObject(val) && val.toString === objectToString ? JSON.stringify(val, null, 2) : String(val);\n};\nfunction join(items, separator = '') {\n  return items.reduce((str, item, index) => index === 0 ? str + item : str + separator + item, '');\n}\nconst RANGE = 2;\nfunction generateCodeFrame(source, start = 0, end = source.length) {\n  const lines = source.split(/\\r?\\n/);\n  let count = 0;\n  const res = [];\n  for (let i = 0; i < lines.length; i++) {\n    count += lines[i].length + 1;\n    if (count >= start) {\n      for (let j = i - RANGE; j <= i + RANGE || end > count; j++) {\n        if (j < 0 || j >= lines.length) continue;\n        const line = j + 1;\n        res.push(`${line}${' '.repeat(3 - String(line).length)}|  ${lines[j]}`);\n        const lineLength = lines[j].length;\n        if (j === i) {\n          // push underline\n          const pad = start - (count - lineLength) + 1;\n          const length = Math.max(1, end > count ? lineLength - pad : end - start);\n          res.push(`   |  ` + ' '.repeat(pad) + '^'.repeat(length));\n        } else if (j > i) {\n          if (end > count) {\n            const length = Math.max(Math.min(end - count, lineLength), 1);\n            res.push(`   |  ` + '^'.repeat(length));\n          }\n          count += lineLength + 1;\n        }\n      }\n      break;\n    }\n  }\n  return res.join('\\n');\n}\nfunction incrementer(code) {\n  let current = code;\n  return () => ++current;\n}\nfunction warn(msg, err) {\n  if (typeof console !== 'undefined') {\n    console.warn(`[intlify] ` + msg);\n    /* istanbul ignore if */\n    if (err) {\n      console.warn(err.stack);\n    }\n  }\n}\nconst hasWarned = {};\nfunction warnOnce(msg) {\n  if (!hasWarned[msg]) {\n    hasWarned[msg] = true;\n    warn(msg);\n  }\n}\n\n/**\n * Event emitter, forked from the below:\n * - original repository url: https://github.com/developit/mitt\n * - code url: https://github.com/developit/mitt/blob/master/src/index.ts\n * - author: Jason Miller (https://github.com/developit)\n * - license: MIT\n */\n/**\n * Create a event emitter\n *\n * @returns An event emitter\n */\nfunction createEmitter() {\n  const events = new Map();\n  const emitter = {\n    events,\n    on(event, handler) {\n      const handlers = events.get(event);\n      const added = handlers && handlers.push(handler);\n      if (!added) {\n        events.set(event, [handler]);\n      }\n    },\n    off(event, handler) {\n      const handlers = events.get(event);\n      if (handlers) {\n        handlers.splice(handlers.indexOf(handler) >>> 0, 1);\n      }\n    },\n    emit(event, payload) {\n      (events.get(event) || []).slice().map(handler => handler(payload));\n      (events.get('*') || []).slice().map(handler => handler(event, payload));\n    }\n  };\n  return emitter;\n}\nconst isNotObjectOrIsArray = val => !isObject(val) || isArray(val);\n// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\nfunction deepCopy(src, des) {\n  // src and des should both be objects, and none of them can be a array\n  if (isNotObjectOrIsArray(src) || isNotObjectOrIsArray(des)) {\n    throw new Error('Invalid value');\n  }\n  const stack = [{\n    src,\n    des\n  }];\n  while (stack.length) {\n    const {\n      src,\n      des\n    } = stack.pop();\n    // using `Object.keys` which skips prototype properties\n    Object.keys(src).forEach(key => {\n      if (key === '__proto__') {\n        return;\n      }\n      // if src[key] is an object/array, set des[key]\n      // to empty object/array to prevent setting by reference\n      if (isObject(src[key]) && !isObject(des[key])) {\n        des[key] = Array.isArray(src[key]) ? [] : create();\n      }\n      if (isNotObjectOrIsArray(des[key]) || isNotObjectOrIsArray(src[key])) {\n        // replace with src[key] when:\n        // src[key] or des[key] is not an object, or\n        // src[key] or des[key] is an array\n        des[key] = src[key];\n      } else {\n        // src[key] and des[key] are both objects, merge them\n        stack.push({\n          src: src[key],\n          des: des[key]\n        });\n      }\n    });\n  }\n}\nexport { assign, create, createEmitter, deepCopy, escapeHtml, format, friendlyJSONstringify, generateCodeFrame, generateFormatCacheKey, getGlobalThis, hasOwn, inBrowser, incrementer, isArray, isBoolean, isDate, isEmptyObject, isFunction, isNumber, isObject, isPlainObject, isPromise, isRegExp, isString, isSymbol, join, makeSymbol, mark, measure, objectToString, toDisplayString, toTypeString, warn, warnOnce };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}