{"ast": null, "code": "import { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../../utils/vue/icon.mjs';\nconst paginationPrevProps = buildProps({\n  disabled: Boolean,\n  currentPage: {\n    type: Number,\n    default: 1\n  },\n  prevText: {\n    type: String\n  },\n  prevIcon: {\n    type: iconPropType\n  }\n});\nconst paginationPrevEmits = {\n  click: evt => evt instanceof MouseEvent\n};\nexport { paginationPrevEmits, paginationPrevProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}