{"ast": null, "code": "import { computed } from 'vue';\nimport useMenuColor from './use-menu-color.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst useMenuCssVar = (props, level) => {\n  const ns = useNamespace(\"menu\");\n  return computed(() => ns.cssVarBlock({\n    \"text-color\": props.textColor || \"\",\n    \"hover-text-color\": props.textColor || \"\",\n    \"bg-color\": props.backgroundColor || \"\",\n    \"hover-bg-color\": useMenuColor(props).value || \"\",\n    \"active-color\": props.activeTextColor || \"\",\n    level: `${level}`\n  }));\n};\nexport { useMenuCssVar };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}