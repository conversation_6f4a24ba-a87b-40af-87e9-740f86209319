{"ast": null, "code": "import Form from './src/form2.mjs';\nimport FormItem from './src/form-item2.mjs';\nexport { formEmits, formMetaProps, formProps } from './src/form.mjs';\nexport { formItemProps, formItemValidateStates } from './src/form-item.mjs';\nexport { formContextKey, formItemContextKey } from './src/constants.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nexport { useDisabled, useFormDisabled, useFormSize, useSize } from './src/hooks/use-form-common-props.mjs';\nexport { useFormItem, useFormItemInputId } from './src/hooks/use-form-item.mjs';\nconst ElForm = withInstall(Form, {\n  FormItem\n});\nconst ElFormItem = withNoopInstall(FormItem);\nexport { ElForm, ElFormItem, ElForm as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}