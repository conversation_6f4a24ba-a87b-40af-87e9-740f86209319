{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, renderSlot, createTextVNode, toDisplayString, createCommentVNode, createElementVNode, normalizeStyle } from 'vue';\nimport { statisticProps } from './statistic.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isFunction } from '@vue/shared';\nimport { isNumber } from '../../../utils/types.mjs';\nconst __default__ = defineComponent({\n  name: \"ElStatistic\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: statisticProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"statistic\");\n    const displayValue = computed(() => {\n      const {\n        value,\n        formatter,\n        precision,\n        decimalSeparator,\n        groupSeparator\n      } = props;\n      if (isFunction(formatter)) return formatter(value);\n      if (!isNumber(value) || Number.isNaN(value)) return value;\n      let [integer, decimal = \"\"] = String(value).split(\".\");\n      decimal = decimal.padEnd(precision, \"0\").slice(0, precision > 0 ? precision : 0);\n      integer = integer.replace(/\\B(?=(\\d{3})+(?!\\d))/g, groupSeparator);\n      return [integer, decimal].join(decimal ? decimalSeparator : \"\");\n    });\n    expose({\n      displayValue\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(ns).b())\n      }, [_ctx.$slots.title || _ctx.title ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"head\"))\n      }, [renderSlot(_ctx.$slots, \"title\", {}, () => [createTextVNode(toDisplayString(_ctx.title), 1)])], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"content\"))\n      }, [_ctx.$slots.prefix || _ctx.prefix ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"prefix\"))\n      }, [renderSlot(_ctx.$slots, \"prefix\", {}, () => [createElementVNode(\"span\", null, toDisplayString(_ctx.prefix), 1)])], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"span\", {\n        class: normalizeClass(unref(ns).e(\"number\")),\n        style: normalizeStyle(_ctx.valueStyle)\n      }, toDisplayString(unref(displayValue)), 7), _ctx.$slots.suffix || _ctx.suffix ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(unref(ns).e(\"suffix\"))\n      }, [renderSlot(_ctx.$slots, \"suffix\", {}, () => [createElementVNode(\"span\", null, toDisplayString(_ctx.suffix), 1)])], 2)) : createCommentVNode(\"v-if\", true)], 2)], 2);\n    };\n  }\n});\nvar Statistic = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"statistic.vue\"]]);\nexport { Statistic as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}