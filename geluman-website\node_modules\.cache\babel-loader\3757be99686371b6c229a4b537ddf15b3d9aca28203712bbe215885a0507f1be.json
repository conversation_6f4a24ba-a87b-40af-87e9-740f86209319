{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst tourStrategies = [\"absolute\", \"fixed\"];\nconst tourPlacements = [\"top-start\", \"top-end\", \"top\", \"bottom-start\", \"bottom-end\", \"bottom\", \"left-start\", \"left-end\", \"left\", \"right-start\", \"right-end\", \"right\"];\nconst tourContentProps = buildProps({\n  placement: {\n    type: definePropType(String),\n    values: tourPlacements,\n    default: \"bottom\"\n  },\n  reference: {\n    type: definePropType(Object),\n    default: null\n  },\n  strategy: {\n    type: definePropType(String),\n    values: tourStrategies,\n    default: \"absolute\"\n  },\n  offset: {\n    type: Number,\n    default: 10\n  },\n  showArrow: Boolean,\n  zIndex: {\n    type: Number,\n    default: 2001\n  }\n});\nconst tourContentEmits = {\n  close: () => true\n};\nexport { tourContentEmits, tourContentProps, tourPlacements, tourStrategies };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}