{"ast": null, "code": "import { createVNode, renderSlot, mergeProps } from 'vue';\nimport { oppositeOrderMap, SortOrder, Alignment } from '../constants.mjs';\nimport { placeholderSign } from '../private.mjs';\nimport { enforceUnit, componentToSlot, tryCall } from '../utils.mjs';\nimport HeaderCell$1 from '../components/header-cell.mjs';\nimport SortIcon from '../components/sort-icon.mjs';\nconst HeaderCellRenderer = (props, {\n  slots\n}) => {\n  const {\n    column,\n    ns,\n    style,\n    onColumnSorted\n  } = props;\n  const cellStyle = enforceUnit(style);\n  if (column.placeholderSign === placeholderSign) {\n    return createVNode(\"div\", {\n      \"class\": ns.em(\"header-row-cell\", \"placeholder\"),\n      \"style\": cellStyle\n    }, null);\n  }\n  const {\n    headerCellRenderer,\n    headerClass,\n    sortable\n  } = column;\n  const cellProps = {\n    ...props,\n    class: ns.e(\"header-cell-text\")\n  };\n  const columnCellRenderer = componentToSlot(headerCellRenderer);\n  const Cell = columnCellRenderer ? columnCellRenderer(cellProps) : renderSlot(slots, \"default\", cellProps, () => [createVNode(HeaderCell$1, cellProps, null)]);\n  const {\n    sortBy,\n    sortState,\n    headerCellProps\n  } = props;\n  let sorting, sortOrder;\n  if (sortState) {\n    const order = sortState[column.key];\n    sorting = Boolean(oppositeOrderMap[order]);\n    sortOrder = sorting ? order : SortOrder.ASC;\n  } else {\n    sorting = column.key === sortBy.key;\n    sortOrder = sorting ? sortBy.order : SortOrder.ASC;\n  }\n  const cellKls = [ns.e(\"header-cell\"), tryCall(headerClass, props, \"\"), column.align === Alignment.CENTER && ns.is(\"align-center\"), column.align === Alignment.RIGHT && ns.is(\"align-right\"), sortable && ns.is(\"sortable\")];\n  const cellWrapperProps = {\n    ...tryCall(headerCellProps, props),\n    onClick: column.sortable ? onColumnSorted : void 0,\n    class: cellKls,\n    style: cellStyle,\n    [\"data-key\"]: column.key\n  };\n  return createVNode(\"div\", mergeProps(cellWrapperProps, {\n    \"role\": \"columnheader\"\n  }), [Cell, sortable && createVNode(SortIcon, {\n    \"class\": [ns.e(\"sort-icon\"), sorting && ns.is(\"sorting\")],\n    \"sortOrder\": sortOrder\n  }, null)]);\n};\nvar HeaderCell = HeaderCellRenderer;\nexport { HeaderCell as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}