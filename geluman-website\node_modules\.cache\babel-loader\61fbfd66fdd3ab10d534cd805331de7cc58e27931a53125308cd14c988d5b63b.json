{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, ref, inject, computed, provide, readonly, toRef, unref, watch, renderSlot } from 'vue';\nimport { useEventListener } from '@vueuse/core';\nimport { rovingFocusGroupProps, ROVING_FOCUS_COLLECTION_INJECTION_KEY as COLLECTION_INJECTION_KEY } from './roving-focus-group.mjs';\nimport { ROVING_FOCUS_GROUP_INJECTION_KEY } from './tokens.mjs';\nimport { focusFirst } from './utils.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { composeEventHandlers } from '../../../utils/dom/event.mjs';\nconst CURRENT_TAB_ID_CHANGE_EVT = \"currentTabIdChange\";\nconst ENTRY_FOCUS_EVT = \"rovingFocusGroup.entryFocus\";\nconst EVT_OPTS = {\n  bubbles: false,\n  cancelable: true\n};\nconst _sfc_main = defineComponent({\n  name: \"ElRovingFocusGroupImpl\",\n  inheritAttrs: false,\n  props: rovingFocusGroupProps,\n  emits: [CURRENT_TAB_ID_CHANGE_EVT, \"entryFocus\"],\n  setup(props, {\n    emit\n  }) {\n    var _a;\n    const currentTabbedId = ref((_a = props.currentTabId || props.defaultCurrentTabId) != null ? _a : null);\n    const isBackingOut = ref(false);\n    const isClickFocus = ref(false);\n    const rovingFocusGroupRef = ref();\n    const {\n      getItems\n    } = inject(COLLECTION_INJECTION_KEY, void 0);\n    const rovingFocusGroupRootStyle = computed(() => {\n      return [{\n        outline: \"none\"\n      }, props.style];\n    });\n    const onItemFocus = tabbedId => {\n      emit(CURRENT_TAB_ID_CHANGE_EVT, tabbedId);\n    };\n    const onItemShiftTab = () => {\n      isBackingOut.value = true;\n    };\n    const onMousedown = composeEventHandlers(e => {\n      var _a2;\n      (_a2 = props.onMousedown) == null ? void 0 : _a2.call(props, e);\n    }, () => {\n      isClickFocus.value = true;\n    });\n    const onFocus = composeEventHandlers(e => {\n      var _a2;\n      (_a2 = props.onFocus) == null ? void 0 : _a2.call(props, e);\n    }, e => {\n      const isKeyboardFocus = !unref(isClickFocus);\n      const {\n        target,\n        currentTarget\n      } = e;\n      if (target === currentTarget && isKeyboardFocus && !unref(isBackingOut)) {\n        const entryFocusEvt = new Event(ENTRY_FOCUS_EVT, EVT_OPTS);\n        currentTarget == null ? void 0 : currentTarget.dispatchEvent(entryFocusEvt);\n        if (!entryFocusEvt.defaultPrevented) {\n          const items = getItems().filter(item => item.focusable);\n          const activeItem = items.find(item => item.active);\n          const currentItem = items.find(item => item.id === unref(currentTabbedId));\n          const candidates = [activeItem, currentItem, ...items].filter(Boolean);\n          const candidateNodes = candidates.map(item => item.ref);\n          focusFirst(candidateNodes);\n        }\n      }\n      isClickFocus.value = false;\n    });\n    const onBlur = composeEventHandlers(e => {\n      var _a2;\n      (_a2 = props.onBlur) == null ? void 0 : _a2.call(props, e);\n    }, () => {\n      isBackingOut.value = false;\n    });\n    const handleEntryFocus = (...args) => {\n      emit(\"entryFocus\", ...args);\n    };\n    provide(ROVING_FOCUS_GROUP_INJECTION_KEY, {\n      currentTabbedId: readonly(currentTabbedId),\n      loop: toRef(props, \"loop\"),\n      tabIndex: computed(() => {\n        return unref(isBackingOut) ? -1 : 0;\n      }),\n      rovingFocusGroupRef,\n      rovingFocusGroupRootStyle,\n      orientation: toRef(props, \"orientation\"),\n      dir: toRef(props, \"dir\"),\n      onItemFocus,\n      onItemShiftTab,\n      onBlur,\n      onFocus,\n      onMousedown\n    });\n    watch(() => props.currentTabId, val => {\n      currentTabbedId.value = val != null ? val : null;\n    });\n    useEventListener(rovingFocusGroupRef, ENTRY_FOCUS_EVT, handleEntryFocus);\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return renderSlot(_ctx.$slots, \"default\");\n}\nvar ElRovingFocusGroupImpl = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"roving-focus-group-impl.vue\"]]);\nexport { ElRovingFocusGroupImpl as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}