{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { getCurrentInstance, toRefs, ref, computed, watch, unref } from 'vue';\nimport { getKeysMap, getRowIdentity, toggleRowStatus, getColumnById, getColumnByKey, orderBy } from '../util.mjs';\nimport useExpand from './expand.mjs';\nimport useCurrent from './current.mjs';\nimport useTree from './tree.mjs';\nimport { hasOwn, isArray, isString } from '@vue/shared';\nconst sortData = (data, states) => {\n  const sortingColumn = states.sortingColumn;\n  if (!sortingColumn || isString(sortingColumn.sortable)) {\n    return data;\n  }\n  return orderBy(data, states.sortProp, states.sortOrder, sortingColumn.sortMethod, sortingColumn.sortBy);\n};\nconst doFlattenColumns = columns => {\n  const result = [];\n  columns.forEach(column => {\n    if (column.children && column.children.length > 0) {\n      result.push.apply(result, doFlattenColumns(column.children));\n    } else {\n      result.push(column);\n    }\n  });\n  return result;\n};\nfunction useWatcher() {\n  var _a;\n  const instance = getCurrentInstance();\n  const {\n    size: tableSize\n  } = toRefs((_a = instance.proxy) == null ? void 0 : _a.$props);\n  const rowKey = ref(null);\n  const data = ref([]);\n  const _data = ref([]);\n  const isComplex = ref(false);\n  const _columns = ref([]);\n  const originColumns = ref([]);\n  const columns = ref([]);\n  const fixedColumns = ref([]);\n  const rightFixedColumns = ref([]);\n  const leafColumns = ref([]);\n  const fixedLeafColumns = ref([]);\n  const rightFixedLeafColumns = ref([]);\n  const updateOrderFns = [];\n  const leafColumnsLength = ref(0);\n  const fixedLeafColumnsLength = ref(0);\n  const rightFixedLeafColumnsLength = ref(0);\n  const isAllSelected = ref(false);\n  const selection = ref([]);\n  const reserveSelection = ref(false);\n  const selectOnIndeterminate = ref(false);\n  const selectable = ref(null);\n  const filters = ref({});\n  const filteredData = ref(null);\n  const sortingColumn = ref(null);\n  const sortProp = ref(null);\n  const sortOrder = ref(null);\n  const hoverRow = ref(null);\n  const selectedMap = computed(() => {\n    return rowKey.value ? getKeysMap(selection.value, rowKey.value) : void 0;\n  });\n  watch(data, () => {\n    var _a2;\n    if (instance.state) {\n      scheduleLayout(false);\n      const needUpdateFixed = instance.props.tableLayout === \"auto\";\n      if (needUpdateFixed) {\n        (_a2 = instance.refs.tableHeaderRef) == null ? void 0 : _a2.updateFixedColumnStyle();\n      }\n    }\n  }, {\n    deep: true\n  });\n  const assertRowKey = () => {\n    if (!rowKey.value) throw new Error(\"[ElTable] prop row-key is required\");\n  };\n  const updateChildFixed = column => {\n    var _a2;\n    (_a2 = column.children) == null ? void 0 : _a2.forEach(childColumn => {\n      childColumn.fixed = column.fixed;\n      updateChildFixed(childColumn);\n    });\n  };\n  const updateColumns = () => {\n    var _a2, _b;\n    _columns.value.forEach(column => {\n      updateChildFixed(column);\n    });\n    fixedColumns.value = _columns.value.filter(column => column.type !== \"selection\" && [true, \"left\"].includes(column.fixed));\n    let selectColFixLeft;\n    if (((_b = (_a2 = _columns.value) == null ? void 0 : _a2[0]) == null ? void 0 : _b.type) === \"selection\") {\n      const selectColumn = _columns.value[0];\n      selectColFixLeft = [true, \"left\"].includes(selectColumn.fixed) || fixedColumns.value.length && selectColumn.fixed !== \"right\";\n      if (selectColFixLeft) {\n        fixedColumns.value.unshift(selectColumn);\n      }\n    }\n    rightFixedColumns.value = _columns.value.filter(column => column.fixed === \"right\");\n    const notFixedColumns = _columns.value.filter(column => (selectColFixLeft ? column.type !== \"selection\" : true) && !column.fixed);\n    originColumns.value = [].concat(fixedColumns.value).concat(notFixedColumns).concat(rightFixedColumns.value);\n    const leafColumns2 = doFlattenColumns(notFixedColumns);\n    const fixedLeafColumns2 = doFlattenColumns(fixedColumns.value);\n    const rightFixedLeafColumns2 = doFlattenColumns(rightFixedColumns.value);\n    leafColumnsLength.value = leafColumns2.length;\n    fixedLeafColumnsLength.value = fixedLeafColumns2.length;\n    rightFixedLeafColumnsLength.value = rightFixedLeafColumns2.length;\n    columns.value = [].concat(fixedLeafColumns2).concat(leafColumns2).concat(rightFixedLeafColumns2);\n    isComplex.value = fixedColumns.value.length > 0 || rightFixedColumns.value.length > 0;\n  };\n  const scheduleLayout = (needUpdateColumns, immediate = false) => {\n    if (needUpdateColumns) {\n      updateColumns();\n    }\n    if (immediate) {\n      instance.state.doLayout();\n    } else {\n      instance.state.debouncedUpdateLayout();\n    }\n  };\n  const isSelected = row => {\n    if (selectedMap.value) {\n      return !!selectedMap.value[getRowIdentity(row, rowKey.value)];\n    } else {\n      return selection.value.includes(row);\n    }\n  };\n  const clearSelection = () => {\n    isAllSelected.value = false;\n    const oldSelection = selection.value;\n    selection.value = [];\n    if (oldSelection.length) {\n      instance.emit(\"selection-change\", []);\n    }\n  };\n  const cleanSelection = () => {\n    var _a2, _b;\n    let deleted;\n    if (rowKey.value) {\n      deleted = [];\n      const childrenKey = (_b = (_a2 = instance == null ? void 0 : instance.store) == null ? void 0 : _a2.states) == null ? void 0 : _b.childrenColumnName.value;\n      const dataMap = getKeysMap(data.value, rowKey.value, true, childrenKey);\n      for (const key in selectedMap.value) {\n        if (hasOwn(selectedMap.value, key) && !dataMap[key]) {\n          deleted.push(selectedMap.value[key].row);\n        }\n      }\n    } else {\n      deleted = selection.value.filter(item => !data.value.includes(item));\n    }\n    if (deleted.length) {\n      const newSelection = selection.value.filter(item => !deleted.includes(item));\n      selection.value = newSelection;\n      instance.emit(\"selection-change\", newSelection.slice());\n    }\n  };\n  const getSelectionRows = () => {\n    return (selection.value || []).slice();\n  };\n  const toggleRowSelection = (row, selected, emitChange = true, ignoreSelectable = false) => {\n    var _a2, _b, _c, _d;\n    const treeProps = {\n      children: (_b = (_a2 = instance == null ? void 0 : instance.store) == null ? void 0 : _a2.states) == null ? void 0 : _b.childrenColumnName.value,\n      checkStrictly: (_d = (_c = instance == null ? void 0 : instance.store) == null ? void 0 : _c.states) == null ? void 0 : _d.checkStrictly.value\n    };\n    const changed = toggleRowStatus(selection.value, row, selected, treeProps, ignoreSelectable ? void 0 : selectable.value, data.value.indexOf(row));\n    if (changed) {\n      const newSelection = (selection.value || []).slice();\n      if (emitChange) {\n        instance.emit(\"select\", newSelection, row);\n      }\n      instance.emit(\"selection-change\", newSelection);\n    }\n  };\n  const _toggleAllSelection = () => {\n    var _a2, _b;\n    const value = selectOnIndeterminate.value ? !isAllSelected.value : !(isAllSelected.value || selection.value.length);\n    isAllSelected.value = value;\n    let selectionChanged = false;\n    let childrenCount = 0;\n    const rowKey2 = (_b = (_a2 = instance == null ? void 0 : instance.store) == null ? void 0 : _a2.states) == null ? void 0 : _b.rowKey.value;\n    const {\n      childrenColumnName\n    } = instance.store.states;\n    const treeProps = {\n      children: childrenColumnName.value,\n      checkStrictly: false\n    };\n    data.value.forEach((row, index) => {\n      const rowIndex = index + childrenCount;\n      if (toggleRowStatus(selection.value, row, value, treeProps, selectable.value, rowIndex)) {\n        selectionChanged = true;\n      }\n      childrenCount += getChildrenCount(getRowIdentity(row, rowKey2));\n    });\n    if (selectionChanged) {\n      instance.emit(\"selection-change\", selection.value ? selection.value.slice() : []);\n    }\n    instance.emit(\"select-all\", (selection.value || []).slice());\n  };\n  const updateSelectionByRowKey = () => {\n    data.value.forEach(row => {\n      const rowId = getRowIdentity(row, rowKey.value);\n      const rowInfo = selectedMap.value[rowId];\n      if (rowInfo) {\n        selection.value[rowInfo.index] = row;\n      }\n    });\n  };\n  const updateAllSelected = () => {\n    var _a2;\n    if (((_a2 = data.value) == null ? void 0 : _a2.length) === 0) {\n      isAllSelected.value = false;\n      return;\n    }\n    const {\n      childrenColumnName\n    } = instance.store.states;\n    let rowIndex = 0;\n    let selectedCount = 0;\n    const checkSelectedStatus = data2 => {\n      var _a3;\n      for (const row of data2) {\n        const isRowSelectable = selectable.value && selectable.value.call(null, row, rowIndex);\n        if (!isSelected(row)) {\n          if (!selectable.value || isRowSelectable) {\n            return false;\n          }\n        } else {\n          selectedCount++;\n        }\n        rowIndex++;\n        if (((_a3 = row[childrenColumnName.value]) == null ? void 0 : _a3.length) && !checkSelectedStatus(row[childrenColumnName.value])) {\n          return false;\n        }\n      }\n      return true;\n    };\n    const isAllSelected_ = checkSelectedStatus(data.value || []);\n    isAllSelected.value = selectedCount === 0 ? false : isAllSelected_;\n  };\n  const getChildrenCount = rowKey2 => {\n    var _a2;\n    if (!instance || !instance.store) return 0;\n    const {\n      treeData\n    } = instance.store.states;\n    let count = 0;\n    const children = (_a2 = treeData.value[rowKey2]) == null ? void 0 : _a2.children;\n    if (children) {\n      count += children.length;\n      children.forEach(childKey => {\n        count += getChildrenCount(childKey);\n      });\n    }\n    return count;\n  };\n  const updateFilters = (columns2, values) => {\n    if (!isArray(columns2)) {\n      columns2 = [columns2];\n    }\n    const filters_ = {};\n    columns2.forEach(col => {\n      filters.value[col.id] = values;\n      filters_[col.columnKey || col.id] = values;\n    });\n    return filters_;\n  };\n  const updateSort = (column, prop, order) => {\n    if (sortingColumn.value && sortingColumn.value !== column) {\n      sortingColumn.value.order = null;\n    }\n    sortingColumn.value = column;\n    sortProp.value = prop;\n    sortOrder.value = order;\n  };\n  const execFilter = () => {\n    let sourceData = unref(_data);\n    Object.keys(filters.value).forEach(columnId => {\n      const values = filters.value[columnId];\n      if (!values || values.length === 0) return;\n      const column = getColumnById({\n        columns: columns.value\n      }, columnId);\n      if (column && column.filterMethod) {\n        sourceData = sourceData.filter(row => {\n          return values.some(value => column.filterMethod.call(null, value, row, column));\n        });\n      }\n    });\n    filteredData.value = sourceData;\n  };\n  const execSort = () => {\n    data.value = sortData(filteredData.value, {\n      sortingColumn: sortingColumn.value,\n      sortProp: sortProp.value,\n      sortOrder: sortOrder.value\n    });\n  };\n  const execQuery = (ignore = void 0) => {\n    if (!(ignore && ignore.filter)) {\n      execFilter();\n    }\n    execSort();\n  };\n  const clearFilter = columnKeys => {\n    const {\n      tableHeaderRef\n    } = instance.refs;\n    if (!tableHeaderRef) return;\n    const panels = Object.assign({}, tableHeaderRef.filterPanels);\n    const keys = Object.keys(panels);\n    if (!keys.length) return;\n    if (isString(columnKeys)) {\n      columnKeys = [columnKeys];\n    }\n    if (isArray(columnKeys)) {\n      const columns_ = columnKeys.map(key => getColumnByKey({\n        columns: columns.value\n      }, key));\n      keys.forEach(key => {\n        const column = columns_.find(col => col.id === key);\n        if (column) {\n          column.filteredValue = [];\n        }\n      });\n      instance.store.commit(\"filterChange\", {\n        column: columns_,\n        values: [],\n        silent: true,\n        multi: true\n      });\n    } else {\n      keys.forEach(key => {\n        const column = columns.value.find(col => col.id === key);\n        if (column) {\n          column.filteredValue = [];\n        }\n      });\n      filters.value = {};\n      instance.store.commit(\"filterChange\", {\n        column: {},\n        values: [],\n        silent: true\n      });\n    }\n  };\n  const clearSort = () => {\n    if (!sortingColumn.value) return;\n    updateSort(null, null, null);\n    instance.store.commit(\"changeSortCondition\", {\n      silent: true\n    });\n  };\n  const {\n    setExpandRowKeys,\n    toggleRowExpansion,\n    updateExpandRows,\n    states: expandStates,\n    isRowExpanded\n  } = useExpand({\n    data,\n    rowKey\n  });\n  const {\n    updateTreeExpandKeys,\n    toggleTreeExpansion,\n    updateTreeData,\n    updateKeyChildren,\n    loadOrToggle,\n    states: treeStates\n  } = useTree({\n    data,\n    rowKey\n  });\n  const {\n    updateCurrentRowData,\n    updateCurrentRow,\n    setCurrentRowKey,\n    states: currentData\n  } = useCurrent({\n    data,\n    rowKey\n  });\n  const setExpandRowKeysAdapter = val => {\n    setExpandRowKeys(val);\n    updateTreeExpandKeys(val);\n  };\n  const toggleRowExpansionAdapter = (row, expanded) => {\n    const hasExpandColumn = columns.value.some(({\n      type\n    }) => type === \"expand\");\n    if (hasExpandColumn) {\n      toggleRowExpansion(row, expanded);\n    } else {\n      toggleTreeExpansion(row, expanded);\n    }\n  };\n  return {\n    assertRowKey,\n    updateColumns,\n    scheduleLayout,\n    isSelected,\n    clearSelection,\n    cleanSelection,\n    getSelectionRows,\n    toggleRowSelection,\n    _toggleAllSelection,\n    toggleAllSelection: null,\n    updateSelectionByRowKey,\n    updateAllSelected,\n    updateFilters,\n    updateCurrentRow,\n    updateSort,\n    execFilter,\n    execSort,\n    execQuery,\n    clearFilter,\n    clearSort,\n    toggleRowExpansion,\n    setExpandRowKeysAdapter,\n    setCurrentRowKey,\n    toggleRowExpansionAdapter,\n    isRowExpanded,\n    updateExpandRows,\n    updateCurrentRowData,\n    loadOrToggle,\n    updateTreeData,\n    updateKeyChildren,\n    states: {\n      tableSize,\n      rowKey,\n      data,\n      _data,\n      isComplex,\n      _columns,\n      originColumns,\n      columns,\n      fixedColumns,\n      rightFixedColumns,\n      leafColumns,\n      fixedLeafColumns,\n      rightFixedLeafColumns,\n      updateOrderFns,\n      leafColumnsLength,\n      fixedLeafColumnsLength,\n      rightFixedLeafColumnsLength,\n      isAllSelected,\n      selection,\n      reserveSelection,\n      selectOnIndeterminate,\n      selectable,\n      filters,\n      filteredData,\n      sortingColumn,\n      sortProp,\n      sortOrder,\n      hoverRow,\n      ...expandStates,\n      ...treeStates,\n      ...currentData\n    }\n  };\n}\nexport { useWatcher as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}