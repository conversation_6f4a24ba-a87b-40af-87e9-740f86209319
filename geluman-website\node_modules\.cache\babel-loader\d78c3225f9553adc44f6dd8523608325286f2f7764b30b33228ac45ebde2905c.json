{"ast": null, "code": "import { defineComponent, computed, ref, unref, openBlock, createBlock, mergeProps, withCtx, createElementBlock, normalizeClass, toDisplayString, createCommentVNode, renderSlot, createTextVNode } from 'vue';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { popoverProps, popoverEmits } from './popover.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nconst updateEventKeyRaw = `onUpdate:visible`;\nconst __default__ = defineComponent({\n  name: \"ElPopover\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: popoverProps,\n  emits: popoverEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const onUpdateVisible = computed(() => {\n      return props[updateEventKeyRaw];\n    });\n    const ns = useNamespace(\"popover\");\n    const tooltipRef = ref();\n    const popperRef = computed(() => {\n      var _a;\n      return (_a = unref(tooltipRef)) == null ? void 0 : _a.popperRef;\n    });\n    const style = computed(() => {\n      return [{\n        width: addUnit(props.width)\n      }, props.popperStyle];\n    });\n    const kls = computed(() => {\n      return [ns.b(), props.popperClass, {\n        [ns.m(\"plain\")]: !!props.content\n      }];\n    });\n    const gpuAcceleration = computed(() => {\n      return props.transition === `${ns.namespace.value}-fade-in-linear`;\n    });\n    const hide = () => {\n      var _a;\n      (_a = tooltipRef.value) == null ? void 0 : _a.hide();\n    };\n    const beforeEnter = () => {\n      emit(\"before-enter\");\n    };\n    const beforeLeave = () => {\n      emit(\"before-leave\");\n    };\n    const afterEnter = () => {\n      emit(\"after-enter\");\n    };\n    const afterLeave = () => {\n      emit(\"update:visible\", false);\n      emit(\"after-leave\");\n    };\n    expose({\n      popperRef,\n      hide\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElTooltip), mergeProps({\n        ref_key: \"tooltipRef\",\n        ref: tooltipRef\n      }, _ctx.$attrs, {\n        trigger: _ctx.trigger,\n        \"trigger-keys\": _ctx.triggerKeys,\n        placement: _ctx.placement,\n        disabled: _ctx.disabled,\n        visible: _ctx.visible,\n        transition: _ctx.transition,\n        \"popper-options\": _ctx.popperOptions,\n        tabindex: _ctx.tabindex,\n        content: _ctx.content,\n        offset: _ctx.offset,\n        \"show-after\": _ctx.showAfter,\n        \"hide-after\": _ctx.hideAfter,\n        \"auto-close\": _ctx.autoClose,\n        \"show-arrow\": _ctx.showArrow,\n        \"aria-label\": _ctx.title,\n        effect: _ctx.effect,\n        enterable: _ctx.enterable,\n        \"popper-class\": unref(kls),\n        \"popper-style\": unref(style),\n        teleported: _ctx.teleported,\n        persistent: _ctx.persistent,\n        \"gpu-acceleration\": unref(gpuAcceleration),\n        \"onUpdate:visible\": unref(onUpdateVisible),\n        onBeforeShow: beforeEnter,\n        onBeforeHide: beforeLeave,\n        onShow: afterEnter,\n        onHide: afterLeave\n      }), {\n        content: withCtx(() => [_ctx.title ? (openBlock(), createElementBlock(\"div\", {\n          key: 0,\n          class: normalizeClass(unref(ns).e(\"title\")),\n          role: \"title\"\n        }, toDisplayString(_ctx.title), 3)) : createCommentVNode(\"v-if\", true), renderSlot(_ctx.$slots, \"default\", {}, () => [createTextVNode(toDisplayString(_ctx.content), 1)])]),\n        default: withCtx(() => [_ctx.$slots.reference ? renderSlot(_ctx.$slots, \"reference\", {\n          key: 0\n        }) : createCommentVNode(\"v-if\", true)]),\n        _: 3\n      }, 16, [\"trigger\", \"trigger-keys\", \"placement\", \"disabled\", \"visible\", \"transition\", \"popper-options\", \"tabindex\", \"content\", \"offset\", \"show-after\", \"hide-after\", \"auto-close\", \"show-arrow\", \"aria-label\", \"effect\", \"enterable\", \"popper-class\", \"popper-style\", \"teleported\", \"persistent\", \"gpu-acceleration\", \"onUpdate:visible\"]);\n    };\n  }\n});\nvar Popover = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"popover.vue\"]]);\nexport { Popover as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}