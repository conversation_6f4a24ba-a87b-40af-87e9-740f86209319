{"ast": null, "code": "import { defineComponent, createVNode, renderSlot, h } from 'vue';\nimport { useSameTarget } from '../../../hooks/use-same-target/index.mjs';\nimport { PatchFlags } from '../../../utils/vue/vnode.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst overlayProps = buildProps({\n  mask: {\n    type: Boolean,\n    default: true\n  },\n  customMaskEvent: Boolean,\n  overlayClass: {\n    type: definePropType([String, Array, Object])\n  },\n  zIndex: {\n    type: definePropType([String, Number])\n  }\n});\nconst overlayEmits = {\n  click: evt => evt instanceof MouseEvent\n};\nconst BLOCK = \"overlay\";\nvar Overlay = defineComponent({\n  name: \"ElOverlay\",\n  props: overlayProps,\n  emits: overlayEmits,\n  setup(props, {\n    slots,\n    emit\n  }) {\n    const ns = useNamespace(BLOCK);\n    const onMaskClick = e => {\n      emit(\"click\", e);\n    };\n    const {\n      onClick,\n      onMousedown,\n      onMouseup\n    } = useSameTarget(props.customMaskEvent ? void 0 : onMaskClick);\n    return () => {\n      return props.mask ? createVNode(\"div\", {\n        class: [ns.b(), props.overlayClass],\n        style: {\n          zIndex: props.zIndex\n        },\n        onClick,\n        onMousedown,\n        onMouseup\n      }, [renderSlot(slots, \"default\")], PatchFlags.STYLE | PatchFlags.CLASS | PatchFlags.PROPS, [\"onClick\", \"onMouseup\", \"onMousedown\"]) : h(\"div\", {\n        class: props.overlayClass,\n        style: {\n          zIndex: props.zIndex,\n          position: \"fixed\",\n          top: \"0px\",\n          right: \"0px\",\n          bottom: \"0px\",\n          left: \"0px\"\n        }\n      }, [renderSlot(slots, \"default\")]);\n    };\n  }\n});\nexport { Overlay as default, overlayEmits, overlayProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}