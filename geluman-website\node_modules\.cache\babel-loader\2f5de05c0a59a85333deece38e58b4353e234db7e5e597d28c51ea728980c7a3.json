{"ast": null, "code": "import { getCurrentInstance, shallowRef, computed, ref, onMounted, watch } from 'vue';\nimport { draggable } from '../utils/draggable.mjs';\nimport { getClientXY } from '../../../../utils/dom/position.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { EVENT_CODE } from '../../../../constants/aria.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { addUnit } from '../../../../utils/dom/style.mjs';\nconst useAlphaSlider = props => {\n  const instance = getCurrentInstance();\n  const {\n    t\n  } = useLocale();\n  const thumb = shallowRef();\n  const bar = shallowRef();\n  const alpha = computed(() => props.color.get(\"alpha\"));\n  const alphaLabel = computed(() => t(\"el.colorpicker.alphaLabel\"));\n  function handleClick(event) {\n    var _a;\n    const target = event.target;\n    if (target !== thumb.value) {\n      handleDrag(event);\n    }\n    (_a = thumb.value) == null ? void 0 : _a.focus();\n  }\n  function handleDrag(event) {\n    if (!bar.value || !thumb.value) return;\n    const el = instance.vnode.el;\n    const rect = el.getBoundingClientRect();\n    const {\n      clientX,\n      clientY\n    } = getClientXY(event);\n    if (!props.vertical) {\n      let left = clientX - rect.left;\n      left = Math.max(thumb.value.offsetWidth / 2, left);\n      left = Math.min(left, rect.width - thumb.value.offsetWidth / 2);\n      props.color.set(\"alpha\", Math.round((left - thumb.value.offsetWidth / 2) / (rect.width - thumb.value.offsetWidth) * 100));\n    } else {\n      let top = clientY - rect.top;\n      top = Math.max(thumb.value.offsetHeight / 2, top);\n      top = Math.min(top, rect.height - thumb.value.offsetHeight / 2);\n      props.color.set(\"alpha\", Math.round((top - thumb.value.offsetHeight / 2) / (rect.height - thumb.value.offsetHeight) * 100));\n    }\n  }\n  function handleKeydown(event) {\n    const {\n      code,\n      shiftKey\n    } = event;\n    const step = shiftKey ? 10 : 1;\n    switch (code) {\n      case EVENT_CODE.left:\n      case EVENT_CODE.down:\n        event.preventDefault();\n        event.stopPropagation();\n        incrementPosition(-step);\n        break;\n      case EVENT_CODE.right:\n      case EVENT_CODE.up:\n        event.preventDefault();\n        event.stopPropagation();\n        incrementPosition(step);\n        break;\n    }\n  }\n  function incrementPosition(step) {\n    let next = alpha.value + step;\n    next = next < 0 ? 0 : next > 100 ? 100 : next;\n    props.color.set(\"alpha\", next);\n  }\n  return {\n    thumb,\n    bar,\n    alpha,\n    alphaLabel,\n    handleDrag,\n    handleClick,\n    handleKeydown\n  };\n};\nconst useAlphaSliderDOM = (props, {\n  bar,\n  thumb,\n  handleDrag\n}) => {\n  const instance = getCurrentInstance();\n  const ns = useNamespace(\"color-alpha-slider\");\n  const thumbLeft = ref(0);\n  const thumbTop = ref(0);\n  const background = ref();\n  function getThumbLeft() {\n    if (!thumb.value) return 0;\n    if (props.vertical) return 0;\n    const el = instance.vnode.el;\n    const alpha = props.color.get(\"alpha\");\n    if (!el) return 0;\n    return Math.round(alpha * (el.offsetWidth - thumb.value.offsetWidth / 2) / 100);\n  }\n  function getThumbTop() {\n    if (!thumb.value) return 0;\n    const el = instance.vnode.el;\n    if (!props.vertical) return 0;\n    const alpha = props.color.get(\"alpha\");\n    if (!el) return 0;\n    return Math.round(alpha * (el.offsetHeight - thumb.value.offsetHeight / 2) / 100);\n  }\n  function getBackground() {\n    if (props.color && props.color.value) {\n      const {\n        r,\n        g,\n        b\n      } = props.color.toRgb();\n      return `linear-gradient(to right, rgba(${r}, ${g}, ${b}, 0) 0%, rgba(${r}, ${g}, ${b}, 1) 100%)`;\n    }\n    return \"\";\n  }\n  function update() {\n    thumbLeft.value = getThumbLeft();\n    thumbTop.value = getThumbTop();\n    background.value = getBackground();\n  }\n  onMounted(() => {\n    if (!bar.value || !thumb.value) return;\n    const dragConfig = {\n      drag: event => {\n        handleDrag(event);\n      },\n      end: event => {\n        handleDrag(event);\n      }\n    };\n    draggable(bar.value, dragConfig);\n    draggable(thumb.value, dragConfig);\n    update();\n  });\n  watch(() => props.color.get(\"alpha\"), () => update());\n  watch(() => props.color.value, () => update());\n  const rootKls = computed(() => [ns.b(), ns.is(\"vertical\", props.vertical)]);\n  const barKls = computed(() => ns.e(\"bar\"));\n  const thumbKls = computed(() => ns.e(\"thumb\"));\n  const barStyle = computed(() => ({\n    background: background.value\n  }));\n  const thumbStyle = computed(() => ({\n    left: addUnit(thumbLeft.value),\n    top: addUnit(thumbTop.value)\n  }));\n  return {\n    rootKls,\n    barKls,\n    barStyle,\n    thumbKls,\n    thumbStyle,\n    update\n  };\n};\nexport { useAlphaSlider, useAlphaSliderDOM };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}