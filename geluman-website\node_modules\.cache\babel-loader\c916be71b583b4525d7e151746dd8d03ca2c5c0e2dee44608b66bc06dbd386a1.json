{"ast": null, "code": "import ConfigProvider from './src/config-provider.mjs';\nexport { messageConfig } from './src/config-provider.mjs';\nexport { configProviderProps } from './src/config-provider-props.mjs';\nexport { configProviderContextKey } from './src/constants.mjs';\nexport { provideGlobalConfig, useGlobalComponentSettings, useGlobalConfig } from './src/hooks/use-global-config.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElConfigProvider = withInstall(ConfigProvider);\nexport { ElConfigProvider, ElConfigProvider as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}