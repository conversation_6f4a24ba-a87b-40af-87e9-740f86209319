{"ast": null, "code": "import { computed } from 'vue';\nimport { TinyColor } from '@ctrl/tinycolor';\nfunction useMenuColor(props) {\n  const menuBarColor = computed(() => {\n    const color = props.backgroundColor;\n    return color ? new TinyColor(color).shade(20).toString() : \"\";\n  });\n  return menuBarColor;\n}\nexport { useMenuColor as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}