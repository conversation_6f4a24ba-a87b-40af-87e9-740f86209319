{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport { placements } from '@popperjs/core';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { UPDATE_MODEL_EVENT, INPUT_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nimport { isArray } from '@vue/shared';\nconst sliderProps = buildProps({\n  modelValue: {\n    type: definePropType([Number, Array]),\n    default: 0\n  },\n  id: {\n    type: String,\n    default: void 0\n  },\n  min: {\n    type: Number,\n    default: 0\n  },\n  max: {\n    type: Number,\n    default: 100\n  },\n  step: {\n    type: Number,\n    default: 1\n  },\n  showInput: Boolean,\n  showInputControls: {\n    type: Boolean,\n    default: true\n  },\n  size: useSizeProp,\n  inputSize: useSizeProp,\n  showStops: Boolean,\n  showTooltip: {\n    type: Boolean,\n    default: true\n  },\n  formatTooltip: {\n    type: definePropType(Function),\n    default: void 0\n  },\n  disabled: Boolean,\n  range: Boolean,\n  vertical: Boolean,\n  height: String,\n  debounce: {\n    type: Number,\n    default: 300\n  },\n  rangeStartLabel: {\n    type: String,\n    default: void 0\n  },\n  rangeEndLabel: {\n    type: String,\n    default: void 0\n  },\n  formatValueText: {\n    type: definePropType(Function),\n    default: void 0\n  },\n  tooltipClass: {\n    type: String,\n    default: void 0\n  },\n  placement: {\n    type: String,\n    values: placements,\n    default: \"top\"\n  },\n  marks: {\n    type: definePropType(Object)\n  },\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  persistent: {\n    type: Boolean,\n    default: true\n  },\n  ...useAriaProps([\"ariaLabel\"])\n});\nconst isValidValue = value => isNumber(value) || isArray(value) && value.every(isNumber);\nconst sliderEmits = {\n  [UPDATE_MODEL_EVENT]: isValidValue,\n  [INPUT_EVENT]: isValidValue,\n  [CHANGE_EVENT]: isValidValue\n};\nexport { sliderEmits, sliderProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}