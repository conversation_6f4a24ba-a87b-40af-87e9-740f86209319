{"ast": null, "code": "import { isNil } from 'lodash-unified';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { CHANGE_EVENT, INPUT_EVENT, UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nconst inputNumberProps = buildProps({\n  id: {\n    type: String,\n    default: void 0\n  },\n  step: {\n    type: Number,\n    default: 1\n  },\n  stepStrictly: Boolean,\n  max: {\n    type: Number,\n    default: Number.POSITIVE_INFINITY\n  },\n  min: {\n    type: Number,\n    default: Number.NEGATIVE_INFINITY\n  },\n  modelValue: Number,\n  readonly: <PERSON><PERSON>an,\n  disabled: Boolean,\n  size: useSizeProp,\n  controls: {\n    type: <PERSON>olean,\n    default: true\n  },\n  controlsPosition: {\n    type: String,\n    default: \"\",\n    values: [\"\", \"right\"]\n  },\n  valueOnClear: {\n    type: [String, Number, null],\n    validator: val => val === null || isNumber(val) || [\"min\", \"max\"].includes(val),\n    default: null\n  },\n  name: String,\n  placeholder: String,\n  precision: {\n    type: Number,\n    validator: val => val >= 0 && val === Number.parseInt(`${val}`, 10)\n  },\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  ...useAriaProps([\"ariaLabel\"])\n});\nconst inputNumberEmits = {\n  [CHANGE_EVENT]: (cur, prev) => prev !== cur,\n  blur: e => e instanceof FocusEvent,\n  focus: e => e instanceof FocusEvent,\n  [INPUT_EVENT]: val => isNumber(val) || isNil(val),\n  [UPDATE_MODEL_EVENT]: val => isNumber(val) || isNil(val)\n};\nexport { inputNumberEmits, inputNumberProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}