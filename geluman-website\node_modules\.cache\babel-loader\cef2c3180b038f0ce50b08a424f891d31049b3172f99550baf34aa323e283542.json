{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, getCurrentInstance, computed, ref, watch, provide, h } from 'vue';\nimport { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';\nimport { elPaginationKey } from './constants.mjs';\nimport Prev from './components/prev2.mjs';\nimport Next from './components/next2.mjs';\nimport Sizes from './components/sizes2.mjs';\nimport Jumper from './components/jumper2.mjs';\nimport Total from './components/total2.mjs';\nimport Pager from './components/pager2.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nimport { useSizeProp, useGlobalSize } from '../../../hooks/use-size/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useDeprecated } from '../../../hooks/use-deprecated/index.mjs';\nimport { CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nconst isAbsent = v => typeof v !== \"number\";\nconst paginationProps = buildProps({\n  pageSize: Number,\n  defaultPageSize: Number,\n  total: Number,\n  pageCount: Number,\n  pagerCount: {\n    type: Number,\n    validator: value => {\n      return isNumber(value) && Math.trunc(value) === value && value > 4 && value < 22 && value % 2 === 1;\n    },\n    default: 7\n  },\n  currentPage: Number,\n  defaultCurrentPage: Number,\n  layout: {\n    type: String,\n    default: [\"prev\", \"pager\", \"next\", \"jumper\", \"->\", \"total\"].join(\", \")\n  },\n  pageSizes: {\n    type: definePropType(Array),\n    default: () => mutable([10, 20, 30, 40, 50, 100])\n  },\n  popperClass: {\n    type: String,\n    default: \"\"\n  },\n  prevText: {\n    type: String,\n    default: \"\"\n  },\n  prevIcon: {\n    type: iconPropType,\n    default: () => ArrowLeft\n  },\n  nextText: {\n    type: String,\n    default: \"\"\n  },\n  nextIcon: {\n    type: iconPropType,\n    default: () => ArrowRight\n  },\n  teleported: {\n    type: Boolean,\n    default: true\n  },\n  small: Boolean,\n  size: useSizeProp,\n  background: Boolean,\n  disabled: Boolean,\n  hideOnSinglePage: Boolean,\n  appendSizeTo: String\n});\nconst paginationEmits = {\n  \"update:current-page\": val => isNumber(val),\n  \"update:page-size\": val => isNumber(val),\n  \"size-change\": val => isNumber(val),\n  change: (currentPage, pageSize) => isNumber(currentPage) && isNumber(pageSize),\n  \"current-change\": val => isNumber(val),\n  \"prev-click\": val => isNumber(val),\n  \"next-click\": val => isNumber(val)\n};\nconst componentName = \"ElPagination\";\nvar Pagination = defineComponent({\n  name: componentName,\n  props: paginationProps,\n  emits: paginationEmits,\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"pagination\");\n    const vnodeProps = getCurrentInstance().vnode.props || {};\n    const _globalSize = useGlobalSize();\n    const _size = computed(() => {\n      var _a;\n      return props.small ? \"small\" : (_a = props.size) != null ? _a : _globalSize.value;\n    });\n    useDeprecated({\n      from: \"small\",\n      replacement: \"size\",\n      version: \"3.0.0\",\n      scope: \"el-pagination\",\n      ref: \"https://element-plus.org/zh-CN/component/pagination.html\"\n    }, computed(() => !!props.small));\n    const hasCurrentPageListener = \"onUpdate:currentPage\" in vnodeProps || \"onUpdate:current-page\" in vnodeProps || \"onCurrentChange\" in vnodeProps;\n    const hasPageSizeListener = \"onUpdate:pageSize\" in vnodeProps || \"onUpdate:page-size\" in vnodeProps || \"onSizeChange\" in vnodeProps;\n    const assertValidUsage = computed(() => {\n      if (isAbsent(props.total) && isAbsent(props.pageCount)) return false;\n      if (!isAbsent(props.currentPage) && !hasCurrentPageListener) return false;\n      if (props.layout.includes(\"sizes\")) {\n        if (!isAbsent(props.pageCount)) {\n          if (!hasPageSizeListener) return false;\n        } else if (!isAbsent(props.total)) {\n          if (!isAbsent(props.pageSize)) {\n            if (!hasPageSizeListener) {\n              return false;\n            }\n          }\n        }\n      }\n      return true;\n    });\n    const innerPageSize = ref(isAbsent(props.defaultPageSize) ? 10 : props.defaultPageSize);\n    const innerCurrentPage = ref(isAbsent(props.defaultCurrentPage) ? 1 : props.defaultCurrentPage);\n    const pageSizeBridge = computed({\n      get() {\n        return isAbsent(props.pageSize) ? innerPageSize.value : props.pageSize;\n      },\n      set(v) {\n        if (isAbsent(props.pageSize)) {\n          innerPageSize.value = v;\n        }\n        if (hasPageSizeListener) {\n          emit(\"update:page-size\", v);\n          emit(\"size-change\", v);\n        }\n      }\n    });\n    const pageCountBridge = computed(() => {\n      let pageCount = 0;\n      if (!isAbsent(props.pageCount)) {\n        pageCount = props.pageCount;\n      } else if (!isAbsent(props.total)) {\n        pageCount = Math.max(1, Math.ceil(props.total / pageSizeBridge.value));\n      }\n      return pageCount;\n    });\n    const currentPageBridge = computed({\n      get() {\n        return isAbsent(props.currentPage) ? innerCurrentPage.value : props.currentPage;\n      },\n      set(v) {\n        let newCurrentPage = v;\n        if (v < 1) {\n          newCurrentPage = 1;\n        } else if (v > pageCountBridge.value) {\n          newCurrentPage = pageCountBridge.value;\n        }\n        if (isAbsent(props.currentPage)) {\n          innerCurrentPage.value = newCurrentPage;\n        }\n        if (hasCurrentPageListener) {\n          emit(\"update:current-page\", newCurrentPage);\n          emit(\"current-change\", newCurrentPage);\n        }\n      }\n    });\n    watch(pageCountBridge, val => {\n      if (currentPageBridge.value > val) currentPageBridge.value = val;\n    });\n    watch([currentPageBridge, pageSizeBridge], value => {\n      emit(CHANGE_EVENT, ...value);\n    }, {\n      flush: \"post\"\n    });\n    function handleCurrentChange(val) {\n      currentPageBridge.value = val;\n    }\n    function handleSizeChange(val) {\n      pageSizeBridge.value = val;\n      const newPageCount = pageCountBridge.value;\n      if (currentPageBridge.value > newPageCount) {\n        currentPageBridge.value = newPageCount;\n      }\n    }\n    function prev() {\n      if (props.disabled) return;\n      currentPageBridge.value -= 1;\n      emit(\"prev-click\", currentPageBridge.value);\n    }\n    function next() {\n      if (props.disabled) return;\n      currentPageBridge.value += 1;\n      emit(\"next-click\", currentPageBridge.value);\n    }\n    function addClass(element, cls) {\n      if (element) {\n        if (!element.props) {\n          element.props = {};\n        }\n        element.props.class = [element.props.class, cls].join(\" \");\n      }\n    }\n    provide(elPaginationKey, {\n      pageCount: pageCountBridge,\n      disabled: computed(() => props.disabled),\n      currentPage: currentPageBridge,\n      changeEvent: handleCurrentChange,\n      handleSizeChange\n    });\n    return () => {\n      var _a, _b;\n      if (!assertValidUsage.value) {\n        debugWarn(componentName, t(\"el.pagination.deprecationWarning\"));\n        return null;\n      }\n      if (!props.layout) return null;\n      if (props.hideOnSinglePage && pageCountBridge.value <= 1) return null;\n      const rootChildren = [];\n      const rightWrapperChildren = [];\n      const rightWrapperRoot = h(\"div\", {\n        class: ns.e(\"rightwrapper\")\n      }, rightWrapperChildren);\n      const TEMPLATE_MAP = {\n        prev: h(Prev, {\n          disabled: props.disabled,\n          currentPage: currentPageBridge.value,\n          prevText: props.prevText,\n          prevIcon: props.prevIcon,\n          onClick: prev\n        }),\n        jumper: h(Jumper, {\n          size: _size.value\n        }),\n        pager: h(Pager, {\n          currentPage: currentPageBridge.value,\n          pageCount: pageCountBridge.value,\n          pagerCount: props.pagerCount,\n          onChange: handleCurrentChange,\n          disabled: props.disabled\n        }),\n        next: h(Next, {\n          disabled: props.disabled,\n          currentPage: currentPageBridge.value,\n          pageCount: pageCountBridge.value,\n          nextText: props.nextText,\n          nextIcon: props.nextIcon,\n          onClick: next\n        }),\n        sizes: h(Sizes, {\n          pageSize: pageSizeBridge.value,\n          pageSizes: props.pageSizes,\n          popperClass: props.popperClass,\n          disabled: props.disabled,\n          teleported: props.teleported,\n          size: _size.value,\n          appendSizeTo: props.appendSizeTo\n        }),\n        slot: (_b = (_a = slots == null ? void 0 : slots.default) == null ? void 0 : _a.call(slots)) != null ? _b : null,\n        total: h(Total, {\n          total: isAbsent(props.total) ? 0 : props.total\n        })\n      };\n      const components = props.layout.split(\",\").map(item => item.trim());\n      let haveRightWrapper = false;\n      components.forEach(c => {\n        if (c === \"->\") {\n          haveRightWrapper = true;\n          return;\n        }\n        if (!haveRightWrapper) {\n          rootChildren.push(TEMPLATE_MAP[c]);\n        } else {\n          rightWrapperChildren.push(TEMPLATE_MAP[c]);\n        }\n      });\n      addClass(rootChildren[0], ns.is(\"first\"));\n      addClass(rootChildren[rootChildren.length - 1], ns.is(\"last\"));\n      if (haveRightWrapper && rightWrapperChildren.length > 0) {\n        addClass(rightWrapperChildren[0], ns.is(\"first\"));\n        addClass(rightWrapperChildren[rightWrapperChildren.length - 1], ns.is(\"last\"));\n        rootChildren.push(rightWrapperRoot);\n      }\n      return h(\"div\", {\n        class: [ns.b(), ns.is(\"background\", props.background), ns.m(_size.value)]\n      }, rootChildren);\n    };\n  }\n});\nexport { Pagination as default, paginationEmits, paginationProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}