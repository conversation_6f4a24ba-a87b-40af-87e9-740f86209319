{"ast": null, "code": "import { columns, expandColumnKey, row<PERSON>ey } from './common.mjs';\nimport { virtualizedGridProps } from '../../virtual-list/src/props.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst tableV2RowProps = buildProps({\n  class: String,\n  columns,\n  columnsStyles: {\n    type: definePropType(Object),\n    required: true\n  },\n  depth: Number,\n  expandColumnKey,\n  estimatedRowHeight: {\n    ...virtualizedGridProps.estimatedRowHeight,\n    default: void 0\n  },\n  isScrolling: <PERSON>olean,\n  onRowExpand: {\n    type: definePropType(Function)\n  },\n  onRowHover: {\n    type: definePropType(Function)\n  },\n  onRowHeightChange: {\n    type: definePropType(Function)\n  },\n  rowData: {\n    type: definePropType(Object),\n    required: true\n  },\n  rowEventHandlers: {\n    type: definePropType(Object)\n  },\n  rowIndex: {\n    type: Number,\n    required: true\n  },\n  row<PERSON>ey,\n  style: {\n    type: definePropType(Object)\n  }\n});\nexport { tableV2RowProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}