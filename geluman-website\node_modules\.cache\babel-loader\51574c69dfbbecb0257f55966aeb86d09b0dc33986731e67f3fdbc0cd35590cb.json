{"ast": null, "code": "import { defineComponent, computed, unref, withDirectives, openBlock, createElementBlock, normalizeClass, normalizeStyle, vShow, createCommentVNode, renderSlot } from 'vue';\nimport { carouselItemProps } from './carousel-item.mjs';\nimport { useCarouselItem } from './use-carousel-item.mjs';\nimport { CAROUSEL_ITEM_NAME } from './constants.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: CAROUSEL_ITEM_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: carouselItemProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"carousel\");\n    const {\n      carouselItemRef,\n      active,\n      animating,\n      hover,\n      inStage,\n      isVertical,\n      translate,\n      isCardType,\n      scale,\n      ready,\n      handleItemClick\n    } = useCarouselItem(props);\n    const itemKls = computed(() => [ns.e(\"item\"), ns.is(\"active\", active.value), ns.is(\"in-stage\", inStage.value), ns.is(\"hover\", hover.value), ns.is(\"animating\", animating.value), {\n      [ns.em(\"item\", \"card\")]: isCardType.value,\n      [ns.em(\"item\", \"card-vertical\")]: isCardType.value && isVertical.value\n    }]);\n    const itemStyle = computed(() => {\n      const translateType = `translate${unref(isVertical) ? \"Y\" : \"X\"}`;\n      const _translate = `${translateType}(${unref(translate)}px)`;\n      const _scale = `scale(${unref(scale)})`;\n      const transform = [_translate, _scale].join(\" \");\n      return {\n        transform\n      };\n    });\n    return (_ctx, _cache) => {\n      return withDirectives((openBlock(), createElementBlock(\"div\", {\n        ref_key: \"carouselItemRef\",\n        ref: carouselItemRef,\n        class: normalizeClass(unref(itemKls)),\n        style: normalizeStyle(unref(itemStyle)),\n        onClick: unref(handleItemClick)\n      }, [unref(isCardType) ? withDirectives((openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"mask\"))\n      }, null, 2)), [[vShow, !unref(active)]]) : createCommentVNode(\"v-if\", true), renderSlot(_ctx.$slots, \"default\")], 14, [\"onClick\"])), [[vShow, unref(ready)]]);\n    };\n  }\n});\nvar CarouselItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"carousel-item.vue\"]]);\nexport { CarouselItem as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}