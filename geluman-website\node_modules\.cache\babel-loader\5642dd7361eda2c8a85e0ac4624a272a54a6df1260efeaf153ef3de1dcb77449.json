{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nvar defaultProps = {\n  type: {\n    type: String,\n    default: \"default\"\n  },\n  label: String,\n  className: String,\n  labelClassName: String,\n  property: String,\n  prop: String,\n  width: {\n    type: [String, Number],\n    default: \"\"\n  },\n  minWidth: {\n    type: [String, Number],\n    default: \"\"\n  },\n  renderHeader: Function,\n  sortable: {\n    type: [Boolean, String],\n    default: false\n  },\n  sortMethod: Function,\n  sortBy: [String, Function, Array],\n  resizable: {\n    type: Boolean,\n    default: true\n  },\n  columnKey: String,\n  align: String,\n  headerAlign: String,\n  showOverflowTooltip: {\n    type: [Boolean, Object],\n    default: void 0\n  },\n  tooltipFormatter: Function,\n  fixed: [Boolean, String],\n  formatter: Function,\n  selectable: Function,\n  reserveSelection: Boolean,\n  filterMethod: Function,\n  filteredValue: Array,\n  filters: Array,\n  filterPlacement: String,\n  filterMultiple: {\n    type: Boolean,\n    default: true\n  },\n  filterClassName: String,\n  index: [Number, Function],\n  sortOrders: {\n    type: Array,\n    default: () => {\n      return [\"ascending\", \"descending\", null];\n    },\n    validator: val => {\n      return val.every(order => [\"ascending\", \"descending\", null].includes(order));\n    }\n  }\n};\nexport { defaultProps as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}