{"ast": null, "code": "import arraySample from './_arraySample.js';\nimport baseSample from './_baseSample.js';\nimport isArray from './isArray.js';\n\n/**\n * Gets a random element from `collection`.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Collection\n * @param {Array|Object} collection The collection to sample.\n * @returns {*} Returns the random element.\n * @example\n *\n * _.sample([1, 2, 3, 4]);\n * // => 2\n */\nfunction sample(collection) {\n  var func = isArray(collection) ? arraySample : baseSample;\n  return func(collection);\n}\nexport default sample;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}