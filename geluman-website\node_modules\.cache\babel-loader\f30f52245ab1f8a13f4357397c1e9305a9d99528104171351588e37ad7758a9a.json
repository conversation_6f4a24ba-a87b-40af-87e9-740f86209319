{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { reactive, ref, computed, nextTick, watch, watchEffect, onMounted } from 'vue';\nimport { debounce, get, findLastIndex, isEqual } from 'lodash-unified';\nimport { useResizeObserver } from '@vueuse/core';\nimport { useAllowCreate } from './useAllowCreate.mjs';\nimport { useProps } from './useProps.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormItem, useFormItemInputId } from '../../form/src/hooks/use-form-item.mjs';\nimport { useEmptyValues } from '../../../hooks/use-empty-values/index.mjs';\nimport { useComposition } from '../../../hooks/use-composition/index.mjs';\nimport { useFocusController } from '../../../hooks/use-focus-controller/index.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { isArray, isFunction, isObject } from '@vue/shared';\nimport { ValidateComponentsMap } from '../../../utils/vue/icon.mjs';\nimport { escapeStringRegexp } from '../../../utils/strings.mjs';\nimport { useFormSize } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { isUndefined, isNumber } from '../../../utils/types.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nconst useSelect = (props, emit) => {\n  const {\n    t\n  } = useLocale();\n  const nsSelect = useNamespace(\"select\");\n  const nsInput = useNamespace(\"input\");\n  const {\n    form: elForm,\n    formItem: elFormItem\n  } = useFormItem();\n  const {\n    inputId\n  } = useFormItemInputId(props, {\n    formItemContext: elFormItem\n  });\n  const {\n    aliasProps,\n    getLabel,\n    getValue,\n    getDisabled,\n    getOptions\n  } = useProps(props);\n  const {\n    valueOnClear,\n    isEmptyValue\n  } = useEmptyValues(props);\n  const states = reactive({\n    inputValue: \"\",\n    cachedOptions: [],\n    createdOptions: [],\n    hoveringIndex: -1,\n    inputHovering: false,\n    selectionWidth: 0,\n    collapseItemWidth: 0,\n    previousQuery: null,\n    previousValue: void 0,\n    selectedLabel: \"\",\n    menuVisibleOnFocus: false,\n    isBeforeHide: false\n  });\n  const popperSize = ref(-1);\n  const selectRef = ref();\n  const selectionRef = ref();\n  const tooltipRef = ref();\n  const tagTooltipRef = ref();\n  const inputRef = ref();\n  const prefixRef = ref();\n  const suffixRef = ref();\n  const menuRef = ref();\n  const tagMenuRef = ref();\n  const collapseItemRef = ref();\n  const {\n    isComposing,\n    handleCompositionStart,\n    handleCompositionEnd,\n    handleCompositionUpdate\n  } = useComposition({\n    afterComposition: e => onInput(e)\n  });\n  const {\n    wrapperRef,\n    isFocused,\n    handleBlur\n  } = useFocusController(inputRef, {\n    beforeFocus() {\n      return selectDisabled.value;\n    },\n    afterFocus() {\n      if (props.automaticDropdown && !expanded.value) {\n        expanded.value = true;\n        states.menuVisibleOnFocus = true;\n      }\n    },\n    beforeBlur(event) {\n      var _a, _b;\n      return ((_a = tooltipRef.value) == null ? void 0 : _a.isFocusInsideContent(event)) || ((_b = tagTooltipRef.value) == null ? void 0 : _b.isFocusInsideContent(event));\n    },\n    afterBlur() {\n      var _a;\n      expanded.value = false;\n      states.menuVisibleOnFocus = false;\n      if (props.validateEvent) {\n        (_a = elFormItem == null ? void 0 : elFormItem.validate) == null ? void 0 : _a.call(elFormItem, \"blur\").catch(err => debugWarn(err));\n      }\n    }\n  });\n  const allOptions = computed(() => filterOptions(\"\"));\n  const hasOptions = computed(() => {\n    if (props.loading) return false;\n    return props.options.length > 0 || states.createdOptions.length > 0;\n  });\n  const filteredOptions = ref([]);\n  const expanded = ref(false);\n  const selectDisabled = computed(() => props.disabled || (elForm == null ? void 0 : elForm.disabled));\n  const needStatusIcon = computed(() => {\n    var _a;\n    return (_a = elForm == null ? void 0 : elForm.statusIcon) != null ? _a : false;\n  });\n  const popupHeight = computed(() => {\n    const totalHeight = filteredOptions.value.length * props.itemHeight;\n    return totalHeight > props.height ? props.height : totalHeight;\n  });\n  const hasModelValue = computed(() => {\n    return props.multiple ? isArray(props.modelValue) && props.modelValue.length > 0 : !isEmptyValue(props.modelValue);\n  });\n  const showClearBtn = computed(() => {\n    return props.clearable && !selectDisabled.value && states.inputHovering && hasModelValue.value;\n  });\n  const iconComponent = computed(() => props.remote && props.filterable ? \"\" : props.suffixIcon);\n  const iconReverse = computed(() => iconComponent.value && nsSelect.is(\"reverse\", expanded.value));\n  const validateState = computed(() => (elFormItem == null ? void 0 : elFormItem.validateState) || \"\");\n  const validateIcon = computed(() => {\n    if (!validateState.value) return;\n    return ValidateComponentsMap[validateState.value];\n  });\n  const debounce$1 = computed(() => props.remote ? 300 : 0);\n  const emptyText = computed(() => {\n    if (props.loading) {\n      return props.loadingText || t(\"el.select.loading\");\n    } else {\n      if (props.remote && !states.inputValue && !hasOptions.value) return false;\n      if (props.filterable && states.inputValue && hasOptions.value && filteredOptions.value.length === 0) {\n        return props.noMatchText || t(\"el.select.noMatch\");\n      }\n      if (!hasOptions.value) {\n        return props.noDataText || t(\"el.select.noData\");\n      }\n    }\n    return null;\n  });\n  const filterOptions = query => {\n    const regexp = new RegExp(escapeStringRegexp(query), \"i\");\n    const isFilterMethodValid = props.filterable && isFunction(props.filterMethod);\n    const isRemoteMethodValid = props.filterable && props.remote && isFunction(props.remoteMethod);\n    const isValidOption = o => {\n      if (isFilterMethodValid || isRemoteMethodValid) return true;\n      return query ? regexp.test(getLabel(o) || \"\") : true;\n    };\n    if (props.loading) {\n      return [];\n    }\n    return [...states.createdOptions, ...props.options].reduce((all, item) => {\n      const options = getOptions(item);\n      if (isArray(options)) {\n        const filtered = options.filter(isValidOption);\n        if (filtered.length > 0) {\n          all.push({\n            label: getLabel(item),\n            type: \"Group\"\n          }, ...filtered);\n        }\n      } else if (props.remote || isValidOption(item)) {\n        all.push(item);\n      }\n      return all;\n    }, []);\n  };\n  const updateOptions = () => {\n    filteredOptions.value = filterOptions(states.inputValue);\n  };\n  const allOptionsValueMap = computed(() => {\n    const valueMap = /* @__PURE__ */new Map();\n    allOptions.value.forEach((option, index) => {\n      valueMap.set(getValueKey(getValue(option)), {\n        option,\n        index\n      });\n    });\n    return valueMap;\n  });\n  const filteredOptionsValueMap = computed(() => {\n    const valueMap = /* @__PURE__ */new Map();\n    filteredOptions.value.forEach((option, index) => {\n      valueMap.set(getValueKey(getValue(option)), {\n        option,\n        index\n      });\n    });\n    return valueMap;\n  });\n  const optionsAllDisabled = computed(() => filteredOptions.value.every(option => getDisabled(option)));\n  const selectSize = useFormSize();\n  const collapseTagSize = computed(() => selectSize.value === \"small\" ? \"small\" : \"default\");\n  const calculatePopperSize = () => {\n    var _a;\n    if (isNumber(props.fitInputWidth)) {\n      popperSize.value = props.fitInputWidth;\n      return;\n    }\n    const width = ((_a = selectRef.value) == null ? void 0 : _a.offsetWidth) || 200;\n    if (!props.fitInputWidth && hasOptions.value) {\n      nextTick(() => {\n        popperSize.value = Math.max(width, calculateLabelMaxWidth());\n      });\n    } else {\n      popperSize.value = width;\n    }\n  };\n  const calculateLabelMaxWidth = () => {\n    var _a, _b;\n    const canvas = document.createElement(\"canvas\");\n    const ctx = canvas.getContext(\"2d\");\n    const selector = nsSelect.be(\"dropdown\", \"item\");\n    const dom = ((_b = (_a = menuRef.value) == null ? void 0 : _a.listRef) == null ? void 0 : _b.innerRef) || document;\n    const dropdownItemEl = dom.querySelector(`.${selector}`);\n    if (dropdownItemEl === null || ctx === null) return 0;\n    const style = getComputedStyle(dropdownItemEl);\n    const padding = Number.parseFloat(style.paddingLeft) + Number.parseFloat(style.paddingRight);\n    ctx.font = `bold ${style.font.replace(new RegExp(`\\\\b${style.fontWeight}\\\\b`), \"\")}`;\n    const maxWidth = filteredOptions.value.reduce((max, option) => {\n      const metrics = ctx.measureText(getLabel(option));\n      return Math.max(metrics.width, max);\n    }, 0);\n    return maxWidth + padding;\n  };\n  const getGapWidth = () => {\n    if (!selectionRef.value) return 0;\n    const style = window.getComputedStyle(selectionRef.value);\n    return Number.parseFloat(style.gap || \"6px\");\n  };\n  const tagStyle = computed(() => {\n    const gapWidth = getGapWidth();\n    const maxWidth = collapseItemRef.value && props.maxCollapseTags === 1 ? states.selectionWidth - states.collapseItemWidth - gapWidth : states.selectionWidth;\n    return {\n      maxWidth: `${maxWidth}px`\n    };\n  });\n  const collapseTagStyle = computed(() => {\n    return {\n      maxWidth: `${states.selectionWidth}px`\n    };\n  });\n  const shouldShowPlaceholder = computed(() => {\n    if (isArray(props.modelValue)) {\n      return props.modelValue.length === 0 && !states.inputValue;\n    }\n    return props.filterable ? !states.inputValue : true;\n  });\n  const currentPlaceholder = computed(() => {\n    var _a;\n    const _placeholder = (_a = props.placeholder) != null ? _a : t(\"el.select.placeholder\");\n    return props.multiple || !hasModelValue.value ? _placeholder : states.selectedLabel;\n  });\n  const popperRef = computed(() => {\n    var _a, _b;\n    return (_b = (_a = tooltipRef.value) == null ? void 0 : _a.popperRef) == null ? void 0 : _b.contentRef;\n  });\n  const indexRef = computed(() => {\n    if (props.multiple) {\n      const len = props.modelValue.length;\n      if (props.modelValue.length > 0 && filteredOptionsValueMap.value.has(props.modelValue[len - 1])) {\n        const {\n          index\n        } = filteredOptionsValueMap.value.get(props.modelValue[len - 1]);\n        return index;\n      }\n    } else {\n      if (!isEmptyValue(props.modelValue) && filteredOptionsValueMap.value.has(props.modelValue)) {\n        const {\n          index\n        } = filteredOptionsValueMap.value.get(props.modelValue);\n        return index;\n      }\n    }\n    return -1;\n  });\n  const dropdownMenuVisible = computed({\n    get() {\n      return expanded.value && emptyText.value !== false;\n    },\n    set(val) {\n      expanded.value = val;\n    }\n  });\n  const showTagList = computed(() => {\n    if (!props.multiple) {\n      return [];\n    }\n    return props.collapseTags ? states.cachedOptions.slice(0, props.maxCollapseTags) : states.cachedOptions;\n  });\n  const collapseTagList = computed(() => {\n    if (!props.multiple) {\n      return [];\n    }\n    return props.collapseTags ? states.cachedOptions.slice(props.maxCollapseTags) : [];\n  });\n  const {\n    createNewOption,\n    removeNewOption,\n    selectNewOption,\n    clearAllNewOption\n  } = useAllowCreate(props, states);\n  const toggleMenu = () => {\n    if (selectDisabled.value) return;\n    if (states.menuVisibleOnFocus) {\n      states.menuVisibleOnFocus = false;\n    } else {\n      expanded.value = !expanded.value;\n    }\n  };\n  const onInputChange = () => {\n    if (states.inputValue.length > 0 && !expanded.value) {\n      expanded.value = true;\n    }\n    createNewOption(states.inputValue);\n    handleQueryChange(states.inputValue);\n  };\n  const debouncedOnInputChange = debounce(onInputChange, debounce$1.value);\n  const handleQueryChange = val => {\n    if (states.previousQuery === val || isComposing.value) {\n      return;\n    }\n    states.previousQuery = val;\n    if (props.filterable && isFunction(props.filterMethod)) {\n      props.filterMethod(val);\n    } else if (props.filterable && props.remote && isFunction(props.remoteMethod)) {\n      props.remoteMethod(val);\n    }\n    if (props.defaultFirstOption && (props.filterable || props.remote) && filteredOptions.value.length) {\n      nextTick(checkDefaultFirstOption);\n    } else {\n      nextTick(updateHoveringIndex);\n    }\n  };\n  const checkDefaultFirstOption = () => {\n    const optionsInDropdown = filteredOptions.value.filter(n => !n.disabled && n.type !== \"Group\");\n    const userCreatedOption = optionsInDropdown.find(n => n.created);\n    const firstOriginOption = optionsInDropdown[0];\n    states.hoveringIndex = getValueIndex(filteredOptions.value, userCreatedOption || firstOriginOption);\n  };\n  const emitChange = val => {\n    if (!isEqual(props.modelValue, val)) {\n      emit(CHANGE_EVENT, val);\n    }\n  };\n  const update = val => {\n    emit(UPDATE_MODEL_EVENT, val);\n    emitChange(val);\n    states.previousValue = props.multiple ? String(val) : val;\n    nextTick(() => {\n      if (props.multiple && isArray(props.modelValue)) {\n        const selectedOptions = props.modelValue.map(value => getOption(value));\n        if (!isEqual(states.cachedOptions, selectedOptions)) {\n          states.cachedOptions = selectedOptions;\n        }\n      } else {\n        initStates(true);\n      }\n    });\n  };\n  const getValueIndex = (arr = [], value) => {\n    if (!isObject(value)) {\n      return arr.indexOf(value);\n    }\n    const valueKey = props.valueKey;\n    let index = -1;\n    arr.some((item, i) => {\n      if (get(item, valueKey) === get(value, valueKey)) {\n        index = i;\n        return true;\n      }\n      return false;\n    });\n    return index;\n  };\n  const getValueKey = item => {\n    return isObject(item) ? get(item, props.valueKey) : item;\n  };\n  const handleResize = () => {\n    calculatePopperSize();\n  };\n  const resetSelectionWidth = () => {\n    states.selectionWidth = selectionRef.value.getBoundingClientRect().width;\n  };\n  const resetCollapseItemWidth = () => {\n    states.collapseItemWidth = collapseItemRef.value.getBoundingClientRect().width;\n  };\n  const updateTooltip = () => {\n    var _a, _b;\n    (_b = (_a = tooltipRef.value) == null ? void 0 : _a.updatePopper) == null ? void 0 : _b.call(_a);\n  };\n  const updateTagTooltip = () => {\n    var _a, _b;\n    (_b = (_a = tagTooltipRef.value) == null ? void 0 : _a.updatePopper) == null ? void 0 : _b.call(_a);\n  };\n  const onSelect = option => {\n    if (props.multiple) {\n      let selectedOptions = props.modelValue.slice();\n      const index = getValueIndex(selectedOptions, getValue(option));\n      if (index > -1) {\n        selectedOptions = [...selectedOptions.slice(0, index), ...selectedOptions.slice(index + 1)];\n        states.cachedOptions.splice(index, 1);\n        removeNewOption(option);\n      } else if (props.multipleLimit <= 0 || selectedOptions.length < props.multipleLimit) {\n        selectedOptions = [...selectedOptions, getValue(option)];\n        states.cachedOptions.push(option);\n        selectNewOption(option);\n      }\n      update(selectedOptions);\n      if (option.created) {\n        handleQueryChange(\"\");\n      }\n      if (props.filterable && !props.reserveKeyword) {\n        states.inputValue = \"\";\n      }\n    } else {\n      states.selectedLabel = getLabel(option);\n      update(getValue(option));\n      expanded.value = false;\n      selectNewOption(option);\n      if (!option.created) {\n        clearAllNewOption();\n      }\n    }\n    focus();\n  };\n  const deleteTag = (event, option) => {\n    let selectedOptions = props.modelValue.slice();\n    const index = getValueIndex(selectedOptions, getValue(option));\n    if (index > -1 && !selectDisabled.value) {\n      selectedOptions = [...props.modelValue.slice(0, index), ...props.modelValue.slice(index + 1)];\n      states.cachedOptions.splice(index, 1);\n      update(selectedOptions);\n      emit(\"remove-tag\", getValue(option));\n      removeNewOption(option);\n    }\n    event.stopPropagation();\n    focus();\n  };\n  const focus = () => {\n    var _a;\n    (_a = inputRef.value) == null ? void 0 : _a.focus();\n  };\n  const blur = () => {\n    var _a;\n    if (expanded.value) {\n      expanded.value = false;\n      nextTick(() => {\n        var _a2;\n        return (_a2 = inputRef.value) == null ? void 0 : _a2.blur();\n      });\n      return;\n    }\n    (_a = inputRef.value) == null ? void 0 : _a.blur();\n  };\n  const handleEsc = () => {\n    if (states.inputValue.length > 0) {\n      states.inputValue = \"\";\n    } else {\n      expanded.value = false;\n    }\n  };\n  const getLastNotDisabledIndex = value => findLastIndex(value, it => !states.cachedOptions.some(option => getValue(option) === it && getDisabled(option)));\n  const handleDel = e => {\n    if (!props.multiple) return;\n    if (e.code === EVENT_CODE.delete) return;\n    if (states.inputValue.length === 0) {\n      e.preventDefault();\n      const selected = props.modelValue.slice();\n      const lastNotDisabledIndex = getLastNotDisabledIndex(selected);\n      if (lastNotDisabledIndex < 0) return;\n      const removeTagValue = selected[lastNotDisabledIndex];\n      selected.splice(lastNotDisabledIndex, 1);\n      const option = states.cachedOptions[lastNotDisabledIndex];\n      states.cachedOptions.splice(lastNotDisabledIndex, 1);\n      removeNewOption(option);\n      update(selected);\n      emit(\"remove-tag\", removeTagValue);\n    }\n  };\n  const handleClear = () => {\n    let emptyValue;\n    if (isArray(props.modelValue)) {\n      emptyValue = [];\n    } else {\n      emptyValue = valueOnClear.value;\n    }\n    states.selectedLabel = \"\";\n    expanded.value = false;\n    update(emptyValue);\n    emit(\"clear\");\n    clearAllNewOption();\n    focus();\n  };\n  const onKeyboardNavigate = (direction, hoveringIndex = void 0) => {\n    const options = filteredOptions.value;\n    if (![\"forward\", \"backward\"].includes(direction) || selectDisabled.value || options.length <= 0 || optionsAllDisabled.value || isComposing.value) {\n      return;\n    }\n    if (!expanded.value) {\n      return toggleMenu();\n    }\n    if (isUndefined(hoveringIndex)) {\n      hoveringIndex = states.hoveringIndex;\n    }\n    let newIndex = -1;\n    if (direction === \"forward\") {\n      newIndex = hoveringIndex + 1;\n      if (newIndex >= options.length) {\n        newIndex = 0;\n      }\n    } else if (direction === \"backward\") {\n      newIndex = hoveringIndex - 1;\n      if (newIndex < 0 || newIndex >= options.length) {\n        newIndex = options.length - 1;\n      }\n    }\n    const option = options[newIndex];\n    if (getDisabled(option) || option.type === \"Group\") {\n      return onKeyboardNavigate(direction, newIndex);\n    } else {\n      states.hoveringIndex = newIndex;\n      scrollToItem(newIndex);\n    }\n  };\n  const onKeyboardSelect = () => {\n    if (!expanded.value) {\n      return toggleMenu();\n    } else if (~states.hoveringIndex && filteredOptions.value[states.hoveringIndex]) {\n      onSelect(filteredOptions.value[states.hoveringIndex]);\n    }\n  };\n  const onHoverOption = idx => {\n    states.hoveringIndex = idx != null ? idx : -1;\n  };\n  const updateHoveringIndex = () => {\n    if (!props.multiple) {\n      states.hoveringIndex = filteredOptions.value.findIndex(item => {\n        return getValueKey(item) === getValueKey(props.modelValue);\n      });\n    } else {\n      states.hoveringIndex = filteredOptions.value.findIndex(item => props.modelValue.some(modelValue => getValueKey(modelValue) === getValueKey(item)));\n    }\n  };\n  const onInput = event => {\n    states.inputValue = event.target.value;\n    if (props.remote) {\n      debouncedOnInputChange();\n    } else {\n      return onInputChange();\n    }\n  };\n  const handleClickOutside = event => {\n    expanded.value = false;\n    if (isFocused.value) {\n      const _event = new FocusEvent(\"focus\", event);\n      handleBlur(_event);\n    }\n  };\n  const handleMenuEnter = () => {\n    states.isBeforeHide = false;\n    return nextTick(() => {\n      if (~indexRef.value) {\n        scrollToItem(states.hoveringIndex);\n      }\n    });\n  };\n  const scrollToItem = index => {\n    menuRef.value.scrollToItem(index);\n  };\n  const getOption = (value, cachedOptions) => {\n    const selectValue = getValueKey(value);\n    if (allOptionsValueMap.value.has(selectValue)) {\n      const {\n        option\n      } = allOptionsValueMap.value.get(selectValue);\n      return option;\n    }\n    if (cachedOptions && cachedOptions.length) {\n      const option = cachedOptions.find(option2 => getValueKey(getValue(option2)) === selectValue);\n      if (option) {\n        return option;\n      }\n    }\n    return {\n      [aliasProps.value.value]: value,\n      [aliasProps.value.label]: value\n    };\n  };\n  const initStates = (needUpdateSelectedLabel = false) => {\n    if (props.multiple) {\n      if (props.modelValue.length > 0) {\n        const cachedOptions = states.cachedOptions.slice();\n        states.cachedOptions.length = 0;\n        states.previousValue = props.modelValue.toString();\n        for (const value of props.modelValue) {\n          const option = getOption(value, cachedOptions);\n          states.cachedOptions.push(option);\n        }\n      } else {\n        states.cachedOptions = [];\n        states.previousValue = void 0;\n      }\n    } else {\n      if (hasModelValue.value) {\n        states.previousValue = props.modelValue;\n        const options = filteredOptions.value;\n        const selectedItemIndex = options.findIndex(option => getValueKey(getValue(option)) === getValueKey(props.modelValue));\n        if (~selectedItemIndex) {\n          states.selectedLabel = getLabel(options[selectedItemIndex]);\n        } else {\n          if (!states.selectedLabel || needUpdateSelectedLabel) {\n            states.selectedLabel = getValueKey(props.modelValue);\n          }\n        }\n      } else {\n        states.selectedLabel = \"\";\n        states.previousValue = void 0;\n      }\n    }\n    clearAllNewOption();\n    calculatePopperSize();\n  };\n  watch(() => props.fitInputWidth, () => {\n    calculatePopperSize();\n  });\n  watch(expanded, val => {\n    if (val) {\n      if (!props.persistent) {\n        calculatePopperSize();\n      }\n      handleQueryChange(\"\");\n    } else {\n      states.inputValue = \"\";\n      states.previousQuery = null;\n      states.isBeforeHide = true;\n      createNewOption(\"\");\n    }\n    emit(\"visible-change\", val);\n  });\n  watch(() => props.modelValue, (val, oldVal) => {\n    var _a;\n    const isValEmpty = !val || isArray(val) && val.length === 0;\n    if (isValEmpty || props.multiple && !isEqual(val.toString(), states.previousValue) || !props.multiple && getValueKey(val) !== getValueKey(states.previousValue)) {\n      initStates(true);\n    }\n    if (!isEqual(val, oldVal) && props.validateEvent) {\n      (_a = elFormItem == null ? void 0 : elFormItem.validate) == null ? void 0 : _a.call(elFormItem, \"change\").catch(err => debugWarn(err));\n    }\n  }, {\n    deep: true\n  });\n  watch(() => props.options, () => {\n    const input = inputRef.value;\n    if (!input || input && document.activeElement !== input) {\n      initStates();\n    }\n  }, {\n    deep: true,\n    flush: \"post\"\n  });\n  watch(() => filteredOptions.value, () => {\n    calculatePopperSize();\n    return menuRef.value && nextTick(menuRef.value.resetScrollTop);\n  });\n  watchEffect(() => {\n    if (states.isBeforeHide) return;\n    updateOptions();\n  });\n  watchEffect(() => {\n    const {\n      valueKey,\n      options\n    } = props;\n    const duplicateValue = /* @__PURE__ */new Map();\n    for (const item of options) {\n      const optionValue = getValue(item);\n      let v = optionValue;\n      if (isObject(v)) {\n        v = get(optionValue, valueKey);\n      }\n      if (duplicateValue.get(v)) {\n        debugWarn(\"ElSelectV2\", `The option values you provided seem to be duplicated, which may cause some problems, please check.`);\n        break;\n      } else {\n        duplicateValue.set(v, true);\n      }\n    }\n  });\n  onMounted(() => {\n    initStates();\n  });\n  useResizeObserver(selectRef, handleResize);\n  useResizeObserver(selectionRef, resetSelectionWidth);\n  useResizeObserver(menuRef, updateTooltip);\n  useResizeObserver(wrapperRef, updateTooltip);\n  useResizeObserver(tagMenuRef, updateTagTooltip);\n  useResizeObserver(collapseItemRef, resetCollapseItemWidth);\n  return {\n    inputId,\n    collapseTagSize,\n    currentPlaceholder,\n    expanded,\n    emptyText,\n    popupHeight,\n    debounce: debounce$1,\n    allOptions,\n    filteredOptions,\n    iconComponent,\n    iconReverse,\n    tagStyle,\n    collapseTagStyle,\n    popperSize,\n    dropdownMenuVisible,\n    hasModelValue,\n    shouldShowPlaceholder,\n    selectDisabled,\n    selectSize,\n    needStatusIcon,\n    showClearBtn,\n    states,\n    isFocused,\n    nsSelect,\n    nsInput,\n    inputRef,\n    menuRef,\n    tagMenuRef,\n    tooltipRef,\n    tagTooltipRef,\n    selectRef,\n    wrapperRef,\n    selectionRef,\n    prefixRef,\n    suffixRef,\n    collapseItemRef,\n    popperRef,\n    validateState,\n    validateIcon,\n    showTagList,\n    collapseTagList,\n    debouncedOnInputChange,\n    deleteTag,\n    getLabel,\n    getValue,\n    getDisabled,\n    getValueKey,\n    handleClear,\n    handleClickOutside,\n    handleDel,\n    handleEsc,\n    focus,\n    blur,\n    handleMenuEnter,\n    handleResize,\n    resetSelectionWidth,\n    updateTooltip,\n    updateTagTooltip,\n    updateOptions,\n    toggleMenu,\n    scrollTo: scrollToItem,\n    onInput,\n    onKeyboardNavigate,\n    onKeyboardSelect,\n    onSelect,\n    onHover: onHoverOption,\n    handleCompositionStart,\n    handleCompositionEnd,\n    handleCompositionUpdate\n  };\n};\nexport { useSelect as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}