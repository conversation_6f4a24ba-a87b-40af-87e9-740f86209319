{"ast": null, "code": "import { cAF, rAF } from '../../../../utils/raf.mjs';\nconst useGridWheel = ({\n  atXEndEdge,\n  atXStartEdge,\n  atYEndEdge,\n  atYStartEdge\n}, onWheelDelta) => {\n  let frameHandle = null;\n  let xOffset = 0;\n  let yOffset = 0;\n  const hasReachedEdge = (x, y) => {\n    const xEdgeReached = x <= 0 && atXStartEdge.value || x >= 0 && atXEndEdge.value;\n    const yEdgeReached = y <= 0 && atYStartEdge.value || y >= 0 && atYEndEdge.value;\n    return xEdgeReached && yEdgeReached;\n  };\n  const onWheel = e => {\n    cAF(frameHandle);\n    let x = e.deltaX;\n    let y = e.deltaY;\n    if (Math.abs(x) > Math.abs(y)) {\n      y = 0;\n    } else {\n      x = 0;\n    }\n    if (e.shiftKey && y !== 0) {\n      x = y;\n      y = 0;\n    }\n    if (hasReachedEdge(xOffset, yOffset) && hasReachedEdge(xOffset + x, yOffset + y)) return;\n    xOffset += x;\n    yOffset += y;\n    e.preventDefault();\n    frameHandle = rAF(() => {\n      onWheelDelta(xOffset, yOffset);\n      xOffset = 0;\n      yOffset = 0;\n    });\n  };\n  return {\n    hasReachedEdge,\n    onWheel\n  };\n};\nexport { useGridWheel };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}