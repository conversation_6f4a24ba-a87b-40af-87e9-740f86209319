{"ast": null, "code": "import { shallowRef, ref, onMounted } from 'vue';\nimport { useThrottleFn, useEventListener } from '@vueuse/core';\nimport { throwError } from '../../../utils/error.mjs';\nconst useBackTop = (props, emit, componentName) => {\n  const el = shallowRef();\n  const container = shallowRef();\n  const visible = ref(false);\n  const handleScroll = () => {\n    if (el.value) visible.value = el.value.scrollTop >= props.visibilityHeight;\n  };\n  const handleClick = event => {\n    var _a;\n    (_a = el.value) == null ? void 0 : _a.scrollTo({\n      top: 0,\n      behavior: \"smooth\"\n    });\n    emit(\"click\", event);\n  };\n  const handleScrollThrottled = useThrottleFn(handleScroll, 300, true);\n  useEventListener(container, \"scroll\", handleScrollThrottled);\n  onMounted(() => {\n    var _a;\n    container.value = document;\n    el.value = document.documentElement;\n    if (props.target) {\n      el.value = (_a = document.querySelector(props.target)) != null ? _a : void 0;\n      if (!el.value) {\n        throwError(componentName, `target does not exist: ${props.target}`);\n      }\n      container.value = el.value;\n    }\n    handleScroll();\n  });\n  return {\n    visible,\n    handleClick\n  };\n};\nexport { useBackTop };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}