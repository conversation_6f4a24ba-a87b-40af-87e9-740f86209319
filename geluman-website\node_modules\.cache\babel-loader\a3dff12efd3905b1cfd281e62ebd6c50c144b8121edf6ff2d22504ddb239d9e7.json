{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, inject, computed, openBlock, createElementBlock, normalizeClass, normalizeStyle, withModifiers, renderSlot, unref } from 'vue';\nimport { DROPDOWN_INJECTION_KEY } from './tokens.mjs';\nimport { dropdownMenuProps, DROPDOWN_COLLECTION_INJECTION_KEY as COLLECTION_INJECTION_KEY, FIRST_LAST_KEYS, LAST_KEYS } from './dropdown.mjs';\nimport { useDropdown } from './useDropdown.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { ROVING_FOCUS_GROUP_INJECTION_KEY } from '../../roving-focus-group/src/tokens.mjs';\nimport { ROVING_FOCUS_COLLECTION_INJECTION_KEY as COLLECTION_INJECTION_KEY$1 } from '../../roving-focus-group/src/roving-focus-group.mjs';\nimport { focusFirst } from '../../roving-focus-group/src/utils.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { FOCUS_TRAP_INJECTION_KEY } from '../../focus-trap/src/tokens.mjs';\nimport { composeRefs } from '../../../utils/vue/refs.mjs';\nimport { composeEventHandlers } from '../../../utils/dom/event.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElDropdownMenu\",\n  props: dropdownMenuProps,\n  setup(props) {\n    const ns = useNamespace(\"dropdown\");\n    const {\n      _elDropdownSize\n    } = useDropdown();\n    const size = _elDropdownSize.value;\n    const {\n      focusTrapRef,\n      onKeydown\n    } = inject(FOCUS_TRAP_INJECTION_KEY, void 0);\n    const {\n      contentRef,\n      role,\n      triggerId\n    } = inject(DROPDOWN_INJECTION_KEY, void 0);\n    const {\n      collectionRef: dropdownCollectionRef,\n      getItems\n    } = inject(COLLECTION_INJECTION_KEY, void 0);\n    const {\n      rovingFocusGroupRef,\n      rovingFocusGroupRootStyle,\n      tabIndex,\n      onBlur,\n      onFocus,\n      onMousedown\n    } = inject(ROVING_FOCUS_GROUP_INJECTION_KEY, void 0);\n    const {\n      collectionRef: rovingFocusGroupCollectionRef\n    } = inject(COLLECTION_INJECTION_KEY$1, void 0);\n    const dropdownKls = computed(() => {\n      return [ns.b(\"menu\"), ns.bm(\"menu\", size == null ? void 0 : size.value)];\n    });\n    const dropdownListWrapperRef = composeRefs(contentRef, dropdownCollectionRef, focusTrapRef, rovingFocusGroupRef, rovingFocusGroupCollectionRef);\n    const composedKeydown = composeEventHandlers(e => {\n      var _a;\n      (_a = props.onKeydown) == null ? void 0 : _a.call(props, e);\n    }, e => {\n      const {\n        currentTarget,\n        code,\n        target\n      } = e;\n      currentTarget.contains(target);\n      if (EVENT_CODE.tab === code) {\n        e.stopImmediatePropagation();\n      }\n      e.preventDefault();\n      if (target !== unref(contentRef) || !FIRST_LAST_KEYS.includes(code)) return;\n      const items = getItems().filter(item => !item.disabled);\n      const targets = items.map(item => item.ref);\n      if (LAST_KEYS.includes(code)) {\n        targets.reverse();\n      }\n      focusFirst(targets);\n    });\n    const handleKeydown = e => {\n      composedKeydown(e);\n      onKeydown(e);\n    };\n    return {\n      size,\n      rovingFocusGroupRootStyle,\n      tabIndex,\n      dropdownKls,\n      role,\n      triggerId,\n      dropdownListWrapperRef,\n      handleKeydown,\n      onBlur,\n      onFocus,\n      onMousedown\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"ul\", {\n    ref: _ctx.dropdownListWrapperRef,\n    class: normalizeClass(_ctx.dropdownKls),\n    style: normalizeStyle(_ctx.rovingFocusGroupRootStyle),\n    tabindex: -1,\n    role: _ctx.role,\n    \"aria-labelledby\": _ctx.triggerId,\n    onBlur: _ctx.onBlur,\n    onFocus: _ctx.onFocus,\n    onKeydown: withModifiers(_ctx.handleKeydown, [\"self\"]),\n    onMousedown: withModifiers(_ctx.onMousedown, [\"self\"])\n  }, [renderSlot(_ctx.$slots, \"default\")], 46, [\"role\", \"aria-labelledby\", \"onBlur\", \"onFocus\", \"onKeydown\", \"onMousedown\"]);\n}\nvar DropdownMenu = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"dropdown-menu.vue\"]]);\nexport { DropdownMenu as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}