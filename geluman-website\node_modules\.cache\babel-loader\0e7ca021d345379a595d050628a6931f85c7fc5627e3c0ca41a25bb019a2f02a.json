{"ast": null, "code": "import { ajaxUpload } from './ajax.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nimport { NOOP } from '@vue/shared';\nconst uploadListTypes = [\"text\", \"picture\", \"picture-card\"];\nlet fileId = 1;\nconst genFileId = () => Date.now() + fileId++;\nconst uploadBaseProps = buildProps({\n  action: {\n    type: String,\n    default: \"#\"\n  },\n  headers: {\n    type: definePropType(Object)\n  },\n  method: {\n    type: String,\n    default: \"post\"\n  },\n  data: {\n    type: definePropType([Object, Function, Promise]),\n    default: () => mutable({})\n  },\n  multiple: <PERSON><PERSON><PERSON>,\n  name: {\n    type: String,\n    default: \"file\"\n  },\n  drag: Boolean,\n  withCredentials: Boolean,\n  showFileList: {\n    type: <PERSON>olean,\n    default: true\n  },\n  accept: {\n    type: String,\n    default: \"\"\n  },\n  fileList: {\n    type: definePropType(Array),\n    default: () => mutable([])\n  },\n  autoUpload: {\n    type: Boolean,\n    default: true\n  },\n  listType: {\n    type: String,\n    values: uploadListTypes,\n    default: \"text\"\n  },\n  httpRequest: {\n    type: definePropType(Function),\n    default: ajaxUpload\n  },\n  disabled: Boolean,\n  limit: Number\n});\nconst uploadProps = buildProps({\n  ...uploadBaseProps,\n  beforeUpload: {\n    type: definePropType(Function),\n    default: NOOP\n  },\n  beforeRemove: {\n    type: definePropType(Function)\n  },\n  onRemove: {\n    type: definePropType(Function),\n    default: NOOP\n  },\n  onChange: {\n    type: definePropType(Function),\n    default: NOOP\n  },\n  onPreview: {\n    type: definePropType(Function),\n    default: NOOP\n  },\n  onSuccess: {\n    type: definePropType(Function),\n    default: NOOP\n  },\n  onProgress: {\n    type: definePropType(Function),\n    default: NOOP\n  },\n  onError: {\n    type: definePropType(Function),\n    default: NOOP\n  },\n  onExceed: {\n    type: definePropType(Function),\n    default: NOOP\n  },\n  crossorigin: {\n    type: definePropType(String)\n  }\n});\nexport { genFileId, uploadBaseProps, uploadListTypes, uploadProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}