{"ast": null, "code": "import baseAt from './_baseAt.js';\nimport flatRest from './_flatRest.js';\n\n/**\n * Creates an array of values corresponding to `paths` of `object`.\n *\n * @static\n * @memberOf _\n * @since 1.0.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {...(string|string[])} [paths] The property paths to pick.\n * @returns {Array} Returns the picked values.\n * @example\n *\n * var object = { 'a': [{ 'b': { 'c': 3 } }, 4] };\n *\n * _.at(object, ['a[0].b.c', 'a[1]']);\n * // => [3, 4]\n */\nvar at = flatRest(baseAt);\nexport default at;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}