{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent } from 'vue';\nimport { flattedChildren } from '../../../utils/vue/vnode.mjs';\nimport { isArray } from '@vue/shared';\nvar ElTourSteps = defineComponent({\n  name: \"ElTourSteps\",\n  props: {\n    current: {\n      type: Number,\n      default: 0\n    }\n  },\n  emits: [\"update-total\"],\n  setup(props, {\n    slots,\n    emit\n  }) {\n    let cacheTotal = 0;\n    return () => {\n      var _a, _b;\n      const children = (_a = slots.default) == null ? void 0 : _a.call(slots);\n      const result = [];\n      let total = 0;\n      function filterSteps(children2) {\n        if (!isArray(children2)) return;\n        children2.forEach(item => {\n          var _a2;\n          const name = (_a2 = (item == null ? void 0 : item.type) || {}) == null ? void 0 : _a2.name;\n          if (name === \"ElTourStep\") {\n            result.push(item);\n            total += 1;\n          }\n        });\n      }\n      if (children.length) {\n        filterSteps(flattedChildren((_b = children[0]) == null ? void 0 : _b.children));\n      }\n      if (cacheTotal !== total) {\n        cacheTotal = total;\n        emit(\"update-total\", total);\n      }\n      if (result.length) {\n        return result[props.current];\n      }\n      return null;\n    };\n  }\n});\nexport { ElTourSteps as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}