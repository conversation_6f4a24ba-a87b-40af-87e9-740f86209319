{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { ref, computed } from 'vue';\nimport dayjs from 'dayjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { INPUT_EVENT, UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { isArray, isDate } from '@vue/shared';\nimport { debugWarn } from '../../../utils/error.mjs';\nconst adjacentMonth = (start, end) => {\n  const firstMonthLastDay = start.endOf(\"month\");\n  const lastMonthFirstDay = end.startOf(\"month\");\n  const isSameWeek = firstMonthLastDay.isSame(lastMonthFirstDay, \"week\");\n  const lastMonthStartDay = isSameWeek ? lastMonthFirstDay.add(1, \"week\") : lastMonthFirstDay;\n  return [[start, firstMonthLastDay], [lastMonthStartDay.startOf(\"week\"), end]];\n};\nconst threeConsecutiveMonth = (start, end) => {\n  const firstMonthLastDay = start.endOf(\"month\");\n  const secondMonthFirstDay = start.add(1, \"month\").startOf(\"month\");\n  const secondMonthStartDay = firstMonthLastDay.isSame(secondMonthFirstDay, \"week\") ? secondMonthFirstDay.add(1, \"week\") : secondMonthFirstDay;\n  const secondMonthLastDay = secondMonthStartDay.endOf(\"month\");\n  const lastMonthFirstDay = end.startOf(\"month\");\n  const lastMonthStartDay = secondMonthLastDay.isSame(lastMonthFirstDay, \"week\") ? lastMonthFirstDay.add(1, \"week\") : lastMonthFirstDay;\n  return [[start, firstMonthLastDay], [secondMonthStartDay.startOf(\"week\"), secondMonthLastDay], [lastMonthStartDay.startOf(\"week\"), end]];\n};\nconst useCalendar = (props, emit, componentName) => {\n  const {\n    lang\n  } = useLocale();\n  const selectedDay = ref();\n  const now = dayjs().locale(lang.value);\n  const realSelectedDay = computed({\n    get() {\n      if (!props.modelValue) return selectedDay.value;\n      return date.value;\n    },\n    set(val) {\n      if (!val) return;\n      selectedDay.value = val;\n      const result = val.toDate();\n      emit(INPUT_EVENT, result);\n      emit(UPDATE_MODEL_EVENT, result);\n    }\n  });\n  const validatedRange = computed(() => {\n    if (!props.range || !isArray(props.range) || props.range.length !== 2 || props.range.some(item => !isDate(item))) return [];\n    const rangeArrDayjs = props.range.map(_ => dayjs(_).locale(lang.value));\n    const [startDayjs, endDayjs] = rangeArrDayjs;\n    if (startDayjs.isAfter(endDayjs)) {\n      debugWarn(componentName, \"end time should be greater than start time\");\n      return [];\n    }\n    if (startDayjs.isSame(endDayjs, \"month\")) {\n      return calculateValidatedDateRange(startDayjs, endDayjs);\n    } else {\n      if (startDayjs.add(1, \"month\").month() !== endDayjs.month()) {\n        debugWarn(componentName, \"start time and end time interval must not exceed two months\");\n        return [];\n      }\n      return calculateValidatedDateRange(startDayjs, endDayjs);\n    }\n  });\n  const date = computed(() => {\n    if (!props.modelValue) {\n      return realSelectedDay.value || (validatedRange.value.length ? validatedRange.value[0][0] : now);\n    } else {\n      return dayjs(props.modelValue).locale(lang.value);\n    }\n  });\n  const prevMonthDayjs = computed(() => date.value.subtract(1, \"month\").date(1));\n  const nextMonthDayjs = computed(() => date.value.add(1, \"month\").date(1));\n  const prevYearDayjs = computed(() => date.value.subtract(1, \"year\").date(1));\n  const nextYearDayjs = computed(() => date.value.add(1, \"year\").date(1));\n  const calculateValidatedDateRange = (startDayjs, endDayjs) => {\n    const firstDay = startDayjs.startOf(\"week\");\n    const lastDay = endDayjs.endOf(\"week\");\n    const firstMonth = firstDay.get(\"month\");\n    const lastMonth = lastDay.get(\"month\");\n    if (firstMonth === lastMonth) {\n      return [[firstDay, lastDay]];\n    } else if ((firstMonth + 1) % 12 === lastMonth) {\n      return adjacentMonth(firstDay, lastDay);\n    } else if (firstMonth + 2 === lastMonth || (firstMonth + 1) % 11 === lastMonth) {\n      return threeConsecutiveMonth(firstDay, lastDay);\n    } else {\n      debugWarn(componentName, \"start time and end time interval must not exceed two months\");\n      return [];\n    }\n  };\n  const pickDay = day => {\n    realSelectedDay.value = day;\n  };\n  const selectDate = type => {\n    const dateMap = {\n      \"prev-month\": prevMonthDayjs.value,\n      \"next-month\": nextMonthDayjs.value,\n      \"prev-year\": prevYearDayjs.value,\n      \"next-year\": nextYearDayjs.value,\n      today: now\n    };\n    const day = dateMap[type];\n    if (!day.isSame(date.value, \"day\")) {\n      pickDay(day);\n    }\n  };\n  return {\n    calculateValidatedDateRange,\n    date,\n    realSelectedDay,\n    pickDay,\n    selectDate,\n    validatedRange\n  };\n};\nexport { useCalendar };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}