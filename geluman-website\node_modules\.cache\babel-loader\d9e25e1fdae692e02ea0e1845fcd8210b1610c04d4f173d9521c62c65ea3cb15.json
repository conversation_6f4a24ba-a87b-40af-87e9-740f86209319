{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, inject, computed, ref, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, toDisplayString, createVNode, createCommentVNode } from 'vue';\nimport dayjs from 'dayjs';\nimport { union } from 'lodash-unified';\nimport { panelTimeRangeProps } from '../props/panel-time-range.mjs';\nimport { useTimePanel } from '../composables/use-time-panel.mjs';\nimport { useOldValue, buildAvailableTimeSlotGetter } from '../composables/use-time-picker.mjs';\nimport TimeSpinner from './basic-time-spinner.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isArray } from '@vue/shared';\nimport { EVENT_CODE } from '../../../../constants/aria.mjs';\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"panel-time-range\",\n  props: panelTimeRangeProps,\n  emits: [\"pick\", \"select-range\", \"set-picker-option\"],\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const makeSelectRange = (start, end) => {\n      const result = [];\n      for (let i = start; i <= end; i++) {\n        result.push(i);\n      }\n      return result;\n    };\n    const {\n      t,\n      lang\n    } = useLocale();\n    const nsTime = useNamespace(\"time\");\n    const nsPicker = useNamespace(\"picker\");\n    const pickerBase = inject(\"EP_PICKER_BASE\");\n    const {\n      arrowControl,\n      disabledHours,\n      disabledMinutes,\n      disabledSeconds,\n      defaultValue\n    } = pickerBase.props;\n    const startContainerKls = computed(() => [nsTime.be(\"range-picker\", \"body\"), nsTime.be(\"panel\", \"content\"), nsTime.is(\"arrow\", arrowControl), showSeconds.value ? \"has-seconds\" : \"\"]);\n    const endContainerKls = computed(() => [nsTime.be(\"range-picker\", \"body\"), nsTime.be(\"panel\", \"content\"), nsTime.is(\"arrow\", arrowControl), showSeconds.value ? \"has-seconds\" : \"\"]);\n    const startTime = computed(() => props.parsedValue[0]);\n    const endTime = computed(() => props.parsedValue[1]);\n    const oldValue = useOldValue(props);\n    const handleCancel = () => {\n      emit(\"pick\", oldValue.value, false);\n    };\n    const showSeconds = computed(() => {\n      return props.format.includes(\"ss\");\n    });\n    const amPmMode = computed(() => {\n      if (props.format.includes(\"A\")) return \"A\";\n      if (props.format.includes(\"a\")) return \"a\";\n      return \"\";\n    });\n    const handleConfirm = (visible = false) => {\n      emit(\"pick\", [startTime.value, endTime.value], visible);\n    };\n    const handleMinChange = date => {\n      handleChange(date.millisecond(0), endTime.value);\n    };\n    const handleMaxChange = date => {\n      handleChange(startTime.value, date.millisecond(0));\n    };\n    const isValidValue = _date => {\n      const parsedDate = _date.map(_ => dayjs(_).locale(lang.value));\n      const result = getRangeAvailableTime(parsedDate);\n      return parsedDate[0].isSame(result[0]) && parsedDate[1].isSame(result[1]);\n    };\n    const handleChange = (start, end) => {\n      if (!props.visible) {\n        return;\n      }\n      emit(\"pick\", [start, end], true);\n    };\n    const btnConfirmDisabled = computed(() => {\n      return startTime.value > endTime.value;\n    });\n    const selectionRange = ref([0, 2]);\n    const setMinSelectionRange = (start, end) => {\n      emit(\"select-range\", start, end, \"min\");\n      selectionRange.value = [start, end];\n    };\n    const offset = computed(() => showSeconds.value ? 11 : 8);\n    const setMaxSelectionRange = (start, end) => {\n      emit(\"select-range\", start, end, \"max\");\n      const _offset = unref(offset);\n      selectionRange.value = [start + _offset, end + _offset];\n    };\n    const changeSelectionRange = step => {\n      const list = showSeconds.value ? [0, 3, 6, 11, 14, 17] : [0, 3, 8, 11];\n      const mapping = [\"hours\", \"minutes\"].concat(showSeconds.value ? [\"seconds\"] : []);\n      const index = list.indexOf(selectionRange.value[0]);\n      const next = (index + step + list.length) % list.length;\n      const half = list.length / 2;\n      if (next < half) {\n        timePickerOptions[\"start_emitSelectRange\"](mapping[next]);\n      } else {\n        timePickerOptions[\"end_emitSelectRange\"](mapping[next - half]);\n      }\n    };\n    const handleKeydown = event => {\n      const code = event.code;\n      const {\n        left,\n        right,\n        up,\n        down\n      } = EVENT_CODE;\n      if ([left, right].includes(code)) {\n        const step = code === left ? -1 : 1;\n        changeSelectionRange(step);\n        event.preventDefault();\n        return;\n      }\n      if ([up, down].includes(code)) {\n        const step = code === up ? -1 : 1;\n        const role = selectionRange.value[0] < offset.value ? \"start\" : \"end\";\n        timePickerOptions[`${role}_scrollDown`](step);\n        event.preventDefault();\n        return;\n      }\n    };\n    const disabledHours_ = (role, compare) => {\n      const defaultDisable = disabledHours ? disabledHours(role) : [];\n      const isStart = role === \"start\";\n      const compareDate = compare || (isStart ? endTime.value : startTime.value);\n      const compareHour = compareDate.hour();\n      const nextDisable = isStart ? makeSelectRange(compareHour + 1, 23) : makeSelectRange(0, compareHour - 1);\n      return union(defaultDisable, nextDisable);\n    };\n    const disabledMinutes_ = (hour, role, compare) => {\n      const defaultDisable = disabledMinutes ? disabledMinutes(hour, role) : [];\n      const isStart = role === \"start\";\n      const compareDate = compare || (isStart ? endTime.value : startTime.value);\n      const compareHour = compareDate.hour();\n      if (hour !== compareHour) {\n        return defaultDisable;\n      }\n      const compareMinute = compareDate.minute();\n      const nextDisable = isStart ? makeSelectRange(compareMinute + 1, 59) : makeSelectRange(0, compareMinute - 1);\n      return union(defaultDisable, nextDisable);\n    };\n    const disabledSeconds_ = (hour, minute, role, compare) => {\n      const defaultDisable = disabledSeconds ? disabledSeconds(hour, minute, role) : [];\n      const isStart = role === \"start\";\n      const compareDate = compare || (isStart ? endTime.value : startTime.value);\n      const compareHour = compareDate.hour();\n      const compareMinute = compareDate.minute();\n      if (hour !== compareHour || minute !== compareMinute) {\n        return defaultDisable;\n      }\n      const compareSecond = compareDate.second();\n      const nextDisable = isStart ? makeSelectRange(compareSecond + 1, 59) : makeSelectRange(0, compareSecond - 1);\n      return union(defaultDisable, nextDisable);\n    };\n    const getRangeAvailableTime = ([start, end]) => {\n      return [getAvailableTime(start, \"start\", true, end), getAvailableTime(end, \"end\", false, start)];\n    };\n    const {\n      getAvailableHours,\n      getAvailableMinutes,\n      getAvailableSeconds\n    } = buildAvailableTimeSlotGetter(disabledHours_, disabledMinutes_, disabledSeconds_);\n    const {\n      timePickerOptions,\n      getAvailableTime,\n      onSetOption\n    } = useTimePanel({\n      getAvailableHours,\n      getAvailableMinutes,\n      getAvailableSeconds\n    });\n    const parseUserInput = days => {\n      if (!days) return null;\n      if (isArray(days)) {\n        return days.map(d => dayjs(d, props.format).locale(lang.value));\n      }\n      return dayjs(days, props.format).locale(lang.value);\n    };\n    const formatToString = days => {\n      if (!days) return null;\n      if (isArray(days)) {\n        return days.map(d => d.format(props.format));\n      }\n      return days.format(props.format);\n    };\n    const getDefaultValue = () => {\n      if (isArray(defaultValue)) {\n        return defaultValue.map(d => dayjs(d).locale(lang.value));\n      }\n      const defaultDay = dayjs(defaultValue).locale(lang.value);\n      return [defaultDay, defaultDay.add(60, \"m\")];\n    };\n    emit(\"set-picker-option\", [\"formatToString\", formatToString]);\n    emit(\"set-picker-option\", [\"parseUserInput\", parseUserInput]);\n    emit(\"set-picker-option\", [\"isValidValue\", isValidValue]);\n    emit(\"set-picker-option\", [\"handleKeydownInput\", handleKeydown]);\n    emit(\"set-picker-option\", [\"getDefaultValue\", getDefaultValue]);\n    emit(\"set-picker-option\", [\"getRangeAvailableTime\", getRangeAvailableTime]);\n    return (_ctx, _cache) => {\n      return _ctx.actualVisible ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass([unref(nsTime).b(\"range-picker\"), unref(nsPicker).b(\"panel\")])\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(nsTime).be(\"range-picker\", \"content\"))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(nsTime).be(\"range-picker\", \"cell\"))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(nsTime).be(\"range-picker\", \"header\"))\n      }, toDisplayString(unref(t)(\"el.datepicker.startTime\")), 3), createElementVNode(\"div\", {\n        class: normalizeClass(unref(startContainerKls))\n      }, [createVNode(TimeSpinner, {\n        ref: \"minSpinner\",\n        role: \"start\",\n        \"show-seconds\": unref(showSeconds),\n        \"am-pm-mode\": unref(amPmMode),\n        \"arrow-control\": unref(arrowControl),\n        \"spinner-date\": unref(startTime),\n        \"disabled-hours\": disabledHours_,\n        \"disabled-minutes\": disabledMinutes_,\n        \"disabled-seconds\": disabledSeconds_,\n        onChange: handleMinChange,\n        onSetOption: unref(onSetOption),\n        onSelectRange: setMinSelectionRange\n      }, null, 8, [\"show-seconds\", \"am-pm-mode\", \"arrow-control\", \"spinner-date\", \"onSetOption\"])], 2)], 2), createElementVNode(\"div\", {\n        class: normalizeClass(unref(nsTime).be(\"range-picker\", \"cell\"))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(nsTime).be(\"range-picker\", \"header\"))\n      }, toDisplayString(unref(t)(\"el.datepicker.endTime\")), 3), createElementVNode(\"div\", {\n        class: normalizeClass(unref(endContainerKls))\n      }, [createVNode(TimeSpinner, {\n        ref: \"maxSpinner\",\n        role: \"end\",\n        \"show-seconds\": unref(showSeconds),\n        \"am-pm-mode\": unref(amPmMode),\n        \"arrow-control\": unref(arrowControl),\n        \"spinner-date\": unref(endTime),\n        \"disabled-hours\": disabledHours_,\n        \"disabled-minutes\": disabledMinutes_,\n        \"disabled-seconds\": disabledSeconds_,\n        onChange: handleMaxChange,\n        onSetOption: unref(onSetOption),\n        onSelectRange: setMaxSelectionRange\n      }, null, 8, [\"show-seconds\", \"am-pm-mode\", \"arrow-control\", \"spinner-date\", \"onSetOption\"])], 2)], 2)], 2), createElementVNode(\"div\", {\n        class: normalizeClass(unref(nsTime).be(\"panel\", \"footer\"))\n      }, [createElementVNode(\"button\", {\n        type: \"button\",\n        class: normalizeClass([unref(nsTime).be(\"panel\", \"btn\"), \"cancel\"]),\n        onClick: $event => handleCancel()\n      }, toDisplayString(unref(t)(\"el.datepicker.cancel\")), 11, [\"onClick\"]), createElementVNode(\"button\", {\n        type: \"button\",\n        class: normalizeClass([unref(nsTime).be(\"panel\", \"btn\"), \"confirm\"]),\n        disabled: unref(btnConfirmDisabled),\n        onClick: $event => handleConfirm()\n      }, toDisplayString(unref(t)(\"el.datepicker.confirm\")), 11, [\"disabled\", \"onClick\"])], 2)], 2)) : createCommentVNode(\"v-if\", true);\n    };\n  }\n});\nvar TimeRangePanel = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"panel-time-range.vue\"]]);\nexport { TimeRangePanel as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}