{"ast": null, "code": "import createCompounder from './_createCompounder.js';\n\n/**\n * Converts `string` to\n * [kebab case](https://en.wikipedia.org/wiki/Letter_case#Special_case_styles).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the kebab cased string.\n * @example\n *\n * _.kebabCase('Foo Bar');\n * // => 'foo-bar'\n *\n * _.kebabCase('fooBar');\n * // => 'foo-bar'\n *\n * _.kebabCase('__FOO_BAR__');\n * // => 'foo-bar'\n */\nvar kebabCase = createCompounder(function (result, word, index) {\n  return result + (index ? '-' : '') + word.toLowerCase();\n});\nexport default kebabCase;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}