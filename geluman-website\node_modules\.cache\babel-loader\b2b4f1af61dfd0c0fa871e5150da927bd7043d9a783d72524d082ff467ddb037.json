{"ast": null, "code": "import { rowKey, columns, dataType, fixedDataType, expandKeys, classType, requiredNumber } from './common.mjs';\nimport { tableV2RowProps } from './row.mjs';\nimport { tableV2HeaderProps } from './header.mjs';\nimport { tableV2GridProps } from './grid.mjs';\nimport { virtualizedGridProps, virtualizedScrollbarProps } from '../../virtual-list/src/props.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst tableV2Props = buildProps({\n  cache: tableV2GridProps.cache,\n  estimatedRowHeight: tableV2RowProps.estimatedRowHeight,\n  rowKey,\n  headerClass: {\n    type: definePropType([String, Function])\n  },\n  headerProps: {\n    type: definePropType([Object, Function])\n  },\n  headerCellProps: {\n    type: definePropType([Object, Function])\n  },\n  headerHeight: tableV2HeaderProps.headerHeight,\n  footerHeight: {\n    type: Number,\n    default: 0\n  },\n  rowClass: {\n    type: definePropType([String, Function])\n  },\n  rowProps: {\n    type: definePropType([Object, Function])\n  },\n  rowHeight: {\n    type: Number,\n    default: 50\n  },\n  cellProps: {\n    type: definePropType([Object, Function])\n  },\n  columns,\n  data: dataType,\n  dataGetter: {\n    type: definePropType(Function)\n  },\n  fixedData: fixedDataType,\n  expandColumnKey: tableV2RowProps.expandColumnKey,\n  expandedRowKeys: expandKeys,\n  defaultExpandedRowKeys: expandKeys,\n  class: classType,\n  fixed: Boolean,\n  style: {\n    type: definePropType(Object)\n  },\n  width: requiredNumber,\n  height: requiredNumber,\n  maxHeight: Number,\n  useIsScrolling: Boolean,\n  indentSize: {\n    type: Number,\n    default: 12\n  },\n  iconSize: {\n    type: Number,\n    default: 12\n  },\n  hScrollbarSize: virtualizedGridProps.hScrollbarSize,\n  vScrollbarSize: virtualizedGridProps.vScrollbarSize,\n  scrollbarAlwaysOn: virtualizedScrollbarProps.alwaysOn,\n  sortBy: {\n    type: definePropType(Object),\n    default: () => ({})\n  },\n  sortState: {\n    type: definePropType(Object),\n    default: void 0\n  },\n  onColumnSort: {\n    type: definePropType(Function)\n  },\n  onExpandedRowsChange: {\n    type: definePropType(Function)\n  },\n  onEndReached: {\n    type: definePropType(Function)\n  },\n  onRowExpand: tableV2RowProps.onRowExpand,\n  onScroll: tableV2GridProps.onScroll,\n  onRowsRendered: tableV2GridProps.onRowsRendered,\n  rowEventHandlers: tableV2RowProps.rowEventHandlers\n});\nexport { tableV2Props };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}