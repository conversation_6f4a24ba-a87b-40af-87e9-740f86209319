{"ast": null, "code": "import baseRandom from './_baseRandom.js';\n\n/**\n * A specialized version of `_.sample` for arrays.\n *\n * @private\n * @param {Array} array The array to sample.\n * @returns {*} Returns the random element.\n */\nfunction arraySample(array) {\n  var length = array.length;\n  return length ? array[baseRandom(0, length - 1)] : undefined;\n}\nexport default arraySample;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}