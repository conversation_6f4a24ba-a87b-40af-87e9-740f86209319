{"ast": null, "code": "import { HomeFilled } from \"@element-plus/icons-vue\";\nexport default {\n  __name: 'NotFound',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const __returned__ = {\n      get HomeFilled() {\n        return HomeFilled;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["HomeFilled"], "sources": ["D:/codes/glmwebsite/geluman-website/src/views/NotFound.vue"], "sourcesContent": ["<template>\r\n  <div class=\"not-found\">\r\n    <div class=\"content\">\r\n      <h1 class=\"title\">404</h1>\r\n      <p class=\"subtitle\">抱歉，您访问的页面不存在</p>\r\n      <router-link to=\"/\" class=\"back-home\">\r\n        <el-icon><HomeFilled /></el-icon>\r\n        返回首页\r\n      </router-link>\r\n    </div>\r\n    <div class=\"background-animation\"></div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { HomeFilled } from \"@element-plus/icons-vue\";\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.not-found {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: var(--bg-secondary);\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  .content {\r\n    text-align: center;\r\n    z-index: 1;\r\n  }\r\n\r\n  .title {\r\n    font-size: 8rem;\r\n    color: var(--primary-color);\r\n    margin-bottom: 1rem;\r\n    font-weight: 700;\r\n    text-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 1.5rem;\r\n    color: var(--text-secondary);\r\n    margin-bottom: 2rem;\r\n  }\r\n\r\n  .back-home {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    gap: 0.5rem;\r\n    padding: 0.8rem 1.5rem;\r\n    background: var(--primary-color);\r\n    color: white;\r\n    text-decoration: none;\r\n    border-radius: 30px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\r\n    }\r\n  }\r\n\r\n  .background-animation {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(\r\n        45deg,\r\n        transparent 45%,\r\n        rgba(99, 102, 241, 0.03) 50%,\r\n        transparent 55%\r\n      ),\r\n      linear-gradient(\r\n        -45deg,\r\n        transparent 45%,\r\n        rgba(99, 102, 241, 0.03) 50%,\r\n        transparent 55%\r\n      );\r\n    background-size: 60px 60px;\r\n    animation: moveBackground 20s linear infinite;\r\n  }\r\n}\r\n\r\n@keyframes moveBackground {\r\n  0% {\r\n    background-position: 0 0;\r\n  }\r\n  100% {\r\n    background-position: 60px 60px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .not-found {\r\n    .title {\r\n      font-size: 6rem;\r\n    }\r\n\r\n    .subtitle {\r\n      font-size: 1.2rem;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAeA,SAASA,UAAU,QAAQ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}