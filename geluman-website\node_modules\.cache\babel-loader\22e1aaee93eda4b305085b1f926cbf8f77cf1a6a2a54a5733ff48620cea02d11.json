{"ast": null, "code": "import { useSizeProp } from '../../../../hooks/use-size/index.mjs';\nvar defaultProps = {\n  data: {\n    type: Array,\n    default: () => []\n  },\n  size: useSizeProp,\n  width: [String, Number],\n  height: [String, Number],\n  maxHeight: [String, Number],\n  fit: {\n    type: Boolean,\n    default: true\n  },\n  stripe: Boolean,\n  border: Boolean,\n  rowKey: [String, Function],\n  showHeader: {\n    type: Boolean,\n    default: true\n  },\n  showSummary: Boolean,\n  sumText: String,\n  summaryMethod: Function,\n  rowClassName: [String, Function],\n  rowStyle: [Object, Function],\n  cellClassName: [String, Function],\n  cellStyle: [Object, Function],\n  headerRowClassName: [String, Function],\n  headerRowStyle: [Object, Function],\n  headerCellClassName: [String, Function],\n  headerCellStyle: [Object, Function],\n  highlightCurrentRow: Boolean,\n  currentRowKey: [String, Number],\n  emptyText: String,\n  expandRowKeys: Array,\n  defaultExpandAll: Boolean,\n  defaultSort: Object,\n  tooltipEffect: String,\n  tooltipOptions: Object,\n  spanMethod: Function,\n  selectOnIndeterminate: {\n    type: Boolean,\n    default: true\n  },\n  indent: {\n    type: Number,\n    default: 16\n  },\n  treeProps: {\n    type: Object,\n    default: () => {\n      return {\n        hasChildren: \"hasChildren\",\n        children: \"children\",\n        checkStrictly: false\n      };\n    }\n  },\n  lazy: Boolean,\n  load: Function,\n  style: {\n    type: Object,\n    default: () => ({})\n  },\n  className: {\n    type: String,\n    default: \"\"\n  },\n  tableLayout: {\n    type: String,\n    default: \"fixed\"\n  },\n  scrollbarAlwaysOn: Boolean,\n  flexible: Boolean,\n  showOverflowTooltip: [Boolean, Object],\n  tooltipFormatter: Function,\n  appendFilterPanelTo: String,\n  scrollbarTabindex: {\n    type: [Number, String],\n    default: void 0\n  },\n  allowDragLastColumn: {\n    type: Boolean,\n    default: true\n  },\n  preserveExpandedContent: {\n    type: Boolean,\n    default: false\n  }\n};\nexport { defaultProps as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}