{"ast": null, "code": "import { ref } from 'vue';\nconst usePopperContentFocusTrap = (props, emit) => {\n  const trapped = ref(false);\n  const focusStartRef = ref();\n  const onFocusAfterTrapped = () => {\n    emit(\"focus\");\n  };\n  const onFocusAfterReleased = event => {\n    var _a;\n    if (((_a = event.detail) == null ? void 0 : _a.focusReason) !== \"pointer\") {\n      focusStartRef.value = \"first\";\n      emit(\"blur\");\n    }\n  };\n  const onFocusInTrap = event => {\n    if (props.visible && !trapped.value) {\n      if (event.target) {\n        focusStartRef.value = event.target;\n      }\n      trapped.value = true;\n    }\n  };\n  const onFocusoutPrevented = event => {\n    if (!props.trapping) {\n      if (event.detail.focusReason === \"pointer\") {\n        event.preventDefault();\n      }\n      trapped.value = false;\n    }\n  };\n  const onReleaseRequested = () => {\n    trapped.value = false;\n    emit(\"close\");\n  };\n  return {\n    focusStartRef,\n    trapped,\n    onFocusAfterReleased,\n    onFocusAfterTrapped,\n    onFocusInTrap,\n    onFocusoutPrevented,\n    onReleaseRequested\n  };\n};\nexport { usePopperContentFocusTrap };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}