import { createApp } from "vue";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import i18n from "./i18n";
import App from "./App.vue";
import router from "./router";
import "./styles/element-variables.scss";
import "./styles/main.scss";
import "./styles/animations.scss";

const app = createApp(App);

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

// 波纹效果指令
app.directive("ripple", {
  mounted(el) {
    el.addEventListener("click", (e) => {
      const ripple = document.createElement("span");
      ripple.classList.add("ripple");
      el.appendChild(ripple);

      const rect = el.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;

      ripple.style.width = ripple.style.height = `${size}px`;
      ripple.style.left = `${x}px`;
      ripple.style.top = `${y}px`;

      setTimeout(() => ripple.remove(), 1000);
    });
  },
});

// 初始化语言
const savedLanguage = localStorage.getItem("language");
if (savedLanguage) {
  i18n.global.locale.value = savedLanguage;
}

app.use(ElementPlus).use(router).use(i18n).mount("#app");
