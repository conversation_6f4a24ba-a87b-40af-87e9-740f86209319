{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, withDirectives, isRef, withModifiers, vModelRadio, renderSlot, createTextVNode, toDisplayString, nextTick } from 'vue';\nimport { radioProps, radioEmits } from './radio.mjs';\nimport { useRadio } from './use-radio.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { CHANGE_EVENT } from '../../../constants/event.mjs';\nconst __default__ = defineComponent({\n  name: \"ElRadio\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: radioProps,\n  emits: radioEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"radio\");\n    const {\n      radioRef,\n      radioGroup,\n      focus,\n      size,\n      disabled,\n      modelValue,\n      actualValue\n    } = useRadio(props, emit);\n    function handleChange() {\n      nextTick(() => emit(CHANGE_EVENT, modelValue.value));\n    }\n    return (_ctx, _cache) => {\n      var _a;\n      return openBlock(), createElementBlock(\"label\", {\n        class: normalizeClass([unref(ns).b(), unref(ns).is(\"disabled\", unref(disabled)), unref(ns).is(\"focus\", unref(focus)), unref(ns).is(\"bordered\", _ctx.border), unref(ns).is(\"checked\", unref(modelValue) === unref(actualValue)), unref(ns).m(unref(size))])\n      }, [createElementVNode(\"span\", {\n        class: normalizeClass([unref(ns).e(\"input\"), unref(ns).is(\"disabled\", unref(disabled)), unref(ns).is(\"checked\", unref(modelValue) === unref(actualValue))])\n      }, [withDirectives(createElementVNode(\"input\", {\n        ref_key: \"radioRef\",\n        ref: radioRef,\n        \"onUpdate:modelValue\": $event => isRef(modelValue) ? modelValue.value = $event : null,\n        class: normalizeClass(unref(ns).e(\"original\")),\n        value: unref(actualValue),\n        name: _ctx.name || ((_a = unref(radioGroup)) == null ? void 0 : _a.name),\n        disabled: unref(disabled),\n        checked: unref(modelValue) === unref(actualValue),\n        type: \"radio\",\n        onFocus: $event => focus.value = true,\n        onBlur: $event => focus.value = false,\n        onChange: handleChange,\n        onClick: withModifiers(() => {}, [\"stop\"])\n      }, null, 42, [\"onUpdate:modelValue\", \"value\", \"name\", \"disabled\", \"checked\", \"onFocus\", \"onBlur\", \"onClick\"]), [[vModelRadio, unref(modelValue)]]), createElementVNode(\"span\", {\n        class: normalizeClass(unref(ns).e(\"inner\"))\n      }, null, 2)], 2), createElementVNode(\"span\", {\n        class: normalizeClass(unref(ns).e(\"label\")),\n        onKeydown: withModifiers(() => {}, [\"stop\"])\n      }, [renderSlot(_ctx.$slots, \"default\", {}, () => [createTextVNode(toDisplayString(_ctx.label), 1)])], 42, [\"onKeydown\"])], 2);\n    };\n  }\n});\nvar Radio = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"radio.vue\"]]);\nexport { Radio as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}