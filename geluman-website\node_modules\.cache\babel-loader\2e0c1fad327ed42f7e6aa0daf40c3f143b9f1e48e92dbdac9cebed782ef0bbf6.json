{"ast": null, "code": "import arraySampleSize from './_arraySampleSize.js';\nimport baseSampleSize from './_baseSampleSize.js';\nimport isArray from './isArray.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Gets `n` random elements at unique keys from `collection` up to the\n * size of `collection`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Collection\n * @param {Array|Object} collection The collection to sample.\n * @param {number} [n=1] The number of elements to sample.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the random elements.\n * @example\n *\n * _.sampleSize([1, 2, 3], 2);\n * // => [3, 1]\n *\n * _.sampleSize([1, 2, 3], 4);\n * // => [2, 3, 1]\n */\nfunction sampleSize(collection, n, guard) {\n  if (guard ? isIterateeCall(collection, n, guard) : n === undefined) {\n    n = 1;\n  } else {\n    n = toInteger(n);\n  }\n  var func = isArray(collection) ? arraySampleSize : baseSampleSize;\n  return func(collection, n);\n}\nexport default sampleSize;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}