{"ast": null, "code": "import { provide } from 'vue';\nconst FORWARD_REF_INJECTION_KEY = Symbol(\"elForwardRef\");\nconst useForwardRef = forwardRef => {\n  const setForwardRef = el => {\n    forwardRef.value = el;\n  };\n  provide(FORWARD_REF_INJECTION_KEY, {\n    setForwardRef\n  });\n};\nconst useForwardRefDirective = setForwardRef => {\n  return {\n    mounted(el) {\n      setForwardRef(el);\n    },\n    updated(el) {\n      setForwardRef(el);\n    },\n    unmounted() {\n      setForwardRef(null);\n    }\n  };\n};\nexport { FORWARD_REF_INJECTION_KEY, useForwardRef, useForwardRefDirective };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}