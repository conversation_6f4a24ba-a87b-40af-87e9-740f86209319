{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, getCurrentInstance, inject, ref, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, renderSlot, createBlock, withCtx, resolveDynamicComponent, toDisplayString } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { breadcrumbKey } from './constants.mjs';\nimport { breadcrumbItemProps } from './breadcrumb-item.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElBreadcrumbItem\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: breadcrumbItemProps,\n  setup(__props) {\n    const props = __props;\n    const instance = getCurrentInstance();\n    const breadcrumbContext = inject(breadcrumbKey, void 0);\n    const ns = useNamespace(\"breadcrumb\");\n    const router = instance.appContext.config.globalProperties.$router;\n    const link = ref();\n    const onClick = () => {\n      if (!props.to || !router) return;\n      props.replace ? router.replace(props.to) : router.push(props.to);\n    };\n    return (_ctx, _cache) => {\n      var _a, _b;\n      return openBlock(), createElementBlock(\"span\", {\n        class: normalizeClass(unref(ns).e(\"item\"))\n      }, [createElementVNode(\"span\", {\n        ref_key: \"link\",\n        ref: link,\n        class: normalizeClass([unref(ns).e(\"inner\"), unref(ns).is(\"link\", !!_ctx.to)]),\n        role: \"link\",\n        onClick\n      }, [renderSlot(_ctx.$slots, \"default\")], 2), ((_a = unref(breadcrumbContext)) == null ? void 0 : _a.separatorIcon) ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"separator\"))\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(breadcrumbContext).separatorIcon)))]),\n        _: 1\n      }, 8, [\"class\"])) : (openBlock(), createElementBlock(\"span\", {\n        key: 1,\n        class: normalizeClass(unref(ns).e(\"separator\")),\n        role: \"presentation\"\n      }, toDisplayString((_b = unref(breadcrumbContext)) == null ? void 0 : _b.separator), 3))], 2);\n    };\n  }\n});\nvar BreadcrumbItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"breadcrumb-item.vue\"]]);\nexport { BreadcrumbItem as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}