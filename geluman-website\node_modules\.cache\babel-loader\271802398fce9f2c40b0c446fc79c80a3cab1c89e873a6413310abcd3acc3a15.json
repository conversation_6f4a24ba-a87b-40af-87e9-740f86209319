{"ast": null, "code": "import { createVNode, mergeProps, isVNode } from 'vue';\nimport Table from '../table-grid.mjs';\nfunction _isSlot(s) {\n  return typeof s === \"function\" || Object.prototype.toString.call(s) === \"[object Object]\" && !isVNode(s);\n}\nconst LeftTable = (props, {\n  slots\n}) => {\n  if (!props.columns.length) return;\n  const {\n    rightTableRef,\n    ...rest\n  } = props;\n  return createVNode(Table, mergeProps({\n    \"ref\": rightTableRef\n  }, rest), _isSlot(slots) ? slots : {\n    default: () => [slots]\n  });\n};\nvar RightTable = LeftTable;\nexport { RightTable as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}