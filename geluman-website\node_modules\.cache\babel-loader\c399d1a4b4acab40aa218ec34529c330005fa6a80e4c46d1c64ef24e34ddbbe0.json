{"ast": null, "code": "import { ref, nextTick } from 'vue';\nimport { isKorean } from '../../utils/i18n.mjs';\nfunction useComposition({\n  afterComposition,\n  emit\n}) {\n  const isComposing = ref(false);\n  const handleCompositionStart = event => {\n    emit == null ? void 0 : emit(\"compositionstart\", event);\n    isComposing.value = true;\n  };\n  const handleCompositionUpdate = event => {\n    var _a;\n    emit == null ? void 0 : emit(\"compositionupdate\", event);\n    const text = (_a = event.target) == null ? void 0 : _a.value;\n    const lastCharacter = text[text.length - 1] || \"\";\n    isComposing.value = !isKorean(lastCharacter);\n  };\n  const handleCompositionEnd = event => {\n    emit == null ? void 0 : emit(\"compositionend\", event);\n    if (isComposing.value) {\n      isComposing.value = false;\n      nextTick(() => afterComposition(event));\n    }\n  };\n  const handleComposition = event => {\n    event.type === \"compositionend\" ? handleCompositionEnd(event) : handleCompositionUpdate(event);\n  };\n  return {\n    isComposing,\n    handleComposition,\n    handleCompositionStart,\n    handleCompositionUpdate,\n    handleCompositionEnd\n  };\n}\nexport { useComposition };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}