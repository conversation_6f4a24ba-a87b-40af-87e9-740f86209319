{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { watch, nextTick, toRefs, computed } from 'vue';\nimport { pick } from 'lodash-unified';\nimport { ElSelect } from '../../select/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nconst useSelect = (props, {\n  attrs,\n  emit\n}, {\n  select,\n  tree,\n  key\n}) => {\n  const ns = useNamespace(\"tree-select\");\n  watch(() => props.data, () => {\n    if (props.filterable) {\n      nextTick(() => {\n        var _a, _b;\n        (_b = tree.value) == null ? void 0 : _b.filter((_a = select.value) == null ? void 0 : _a.states.inputValue);\n      });\n    }\n  }, {\n    flush: \"post\"\n  });\n  const result = {\n    ...pick(toRefs(props), Object.keys(ElSelect.props)),\n    ...attrs,\n    class: computed(() => attrs.class),\n    style: computed(() => attrs.style),\n    \"onUpdate:modelValue\": value => emit(UPDATE_MODEL_EVENT, value),\n    valueKey: key,\n    popperClass: computed(() => {\n      const classes = [ns.e(\"popper\")];\n      if (props.popperClass) classes.push(props.popperClass);\n      return classes.join(\" \");\n    }),\n    filterMethod: (keyword = \"\") => {\n      var _a;\n      if (props.filterMethod) {\n        props.filterMethod(keyword);\n      } else if (props.remoteMethod) {\n        props.remoteMethod(keyword);\n      } else {\n        (_a = tree.value) == null ? void 0 : _a.filter(keyword);\n      }\n    }\n  };\n  return result;\n};\nexport { useSelect };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}