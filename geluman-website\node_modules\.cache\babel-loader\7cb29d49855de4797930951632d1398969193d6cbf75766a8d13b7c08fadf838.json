{"ast": null, "code": "import { inject, ref, h } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport { getCell, getColumnByCell, createTablePopper, removePopper } from '../util.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport { hasClass, addClass, removeClass } from '../../../../utils/dom/style.mjs';\nfunction isGreaterThan(a, b, epsilon = 0.03) {\n  return a - b > epsilon;\n}\nfunction useEvents(props) {\n  const parent = inject(TABLE_INJECTION_KEY);\n  const tooltipContent = ref(\"\");\n  const tooltipTrigger = ref(h(\"div\"));\n  const handleEvent = (event, row, name) => {\n    var _a;\n    const table = parent;\n    const cell = getCell(event);\n    let column;\n    const namespace = (_a = table == null ? void 0 : table.vnode.el) == null ? void 0 : _a.dataset.prefix;\n    if (cell) {\n      column = getColumnByCell({\n        columns: props.store.states.columns.value\n      }, cell, namespace);\n      if (column) {\n        table == null ? void 0 : table.emit(`cell-${name}`, row, column, cell, event);\n      }\n    }\n    table == null ? void 0 : table.emit(`row-${name}`, row, column, event);\n  };\n  const handleDoubleClick = (event, row) => {\n    handleEvent(event, row, \"dblclick\");\n  };\n  const handleClick = (event, row) => {\n    props.store.commit(\"setCurrentRow\", row);\n    handleEvent(event, row, \"click\");\n  };\n  const handleContextMenu = (event, row) => {\n    handleEvent(event, row, \"contextmenu\");\n  };\n  const handleMouseEnter = debounce(index => {\n    props.store.commit(\"setHoverRow\", index);\n  }, 30);\n  const handleMouseLeave = debounce(() => {\n    props.store.commit(\"setHoverRow\", null);\n  }, 30);\n  const getPadding = el => {\n    const style = window.getComputedStyle(el, null);\n    const paddingLeft = Number.parseInt(style.paddingLeft, 10) || 0;\n    const paddingRight = Number.parseInt(style.paddingRight, 10) || 0;\n    const paddingTop = Number.parseInt(style.paddingTop, 10) || 0;\n    const paddingBottom = Number.parseInt(style.paddingBottom, 10) || 0;\n    return {\n      left: paddingLeft,\n      right: paddingRight,\n      top: paddingTop,\n      bottom: paddingBottom\n    };\n  };\n  const toggleRowClassByCell = (rowSpan, event, toggle) => {\n    let node = event.target.parentNode;\n    while (rowSpan > 1) {\n      node = node == null ? void 0 : node.nextSibling;\n      if (!node || node.nodeName !== \"TR\") break;\n      toggle(node, \"hover-row hover-fixed-row\");\n      rowSpan--;\n    }\n  };\n  const handleCellMouseEnter = (event, row, tooltipOptions) => {\n    var _a, _b, _c;\n    const table = parent;\n    const cell = getCell(event);\n    const namespace = (_a = table == null ? void 0 : table.vnode.el) == null ? void 0 : _a.dataset.prefix;\n    let column;\n    if (cell) {\n      column = getColumnByCell({\n        columns: props.store.states.columns.value\n      }, cell, namespace);\n      if (cell.rowSpan > 1) {\n        toggleRowClassByCell(cell.rowSpan, event, addClass);\n      }\n      const hoverState = table.hoverState = {\n        cell,\n        column,\n        row\n      };\n      table == null ? void 0 : table.emit(\"cell-mouse-enter\", hoverState.row, hoverState.column, hoverState.cell, event);\n    }\n    if (!tooltipOptions) {\n      return;\n    }\n    const cellChild = event.target.querySelector(\".cell\");\n    if (!(hasClass(cellChild, `${namespace}-tooltip`) && cellChild.childNodes.length)) {\n      return;\n    }\n    const range = document.createRange();\n    range.setStart(cellChild, 0);\n    range.setEnd(cellChild, cellChild.childNodes.length);\n    const {\n      width: rangeWidth,\n      height: rangeHeight\n    } = range.getBoundingClientRect();\n    const {\n      width: cellChildWidth,\n      height: cellChildHeight\n    } = cellChild.getBoundingClientRect();\n    const {\n      top,\n      left,\n      right,\n      bottom\n    } = getPadding(cellChild);\n    const horizontalPadding = left + right;\n    const verticalPadding = top + bottom;\n    if (isGreaterThan(rangeWidth + horizontalPadding, cellChildWidth) || isGreaterThan(rangeHeight + verticalPadding, cellChildHeight) || isGreaterThan(cellChild.scrollWidth, cellChildWidth)) {\n      createTablePopper(tooltipOptions, cell.innerText || cell.textContent, row, column, cell, table);\n    } else if (((_b = removePopper) == null ? void 0 : _b.trigger) === cell) {\n      (_c = removePopper) == null ? void 0 : _c();\n    }\n  };\n  const handleCellMouseLeave = event => {\n    const cell = getCell(event);\n    if (!cell) return;\n    if (cell.rowSpan > 1) {\n      toggleRowClassByCell(cell.rowSpan, event, removeClass);\n    }\n    const oldHoverState = parent == null ? void 0 : parent.hoverState;\n    parent == null ? void 0 : parent.emit(\"cell-mouse-leave\", oldHoverState == null ? void 0 : oldHoverState.row, oldHoverState == null ? void 0 : oldHoverState.column, oldHoverState == null ? void 0 : oldHoverState.cell, event);\n  };\n  return {\n    handleDoubleClick,\n    handleClick,\n    handleContextMenu,\n    handleMouseEnter,\n    handleMouseLeave,\n    handleCellMouseEnter,\n    handleCellMouseLeave,\n    tooltipContent,\n    tooltipTrigger\n  };\n}\nexport { useEvents as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}