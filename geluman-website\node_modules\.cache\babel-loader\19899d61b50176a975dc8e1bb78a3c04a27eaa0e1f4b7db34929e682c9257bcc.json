{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, createElementVNode, unref } from 'vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ImgEmpty\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  setup(__props) {\n    const ns = useNamespace(\"empty\");\n    const id = useId();\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"svg\", {\n        viewBox: \"0 0 79 86\",\n        version: \"1.1\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        \"xmlns:xlink\": \"http://www.w3.org/1999/xlink\"\n      }, [createElementVNode(\"defs\", null, [createElementVNode(\"linearGradient\", {\n        id: `linearGradient-1-${unref(id)}`,\n        x1: \"38.8503086%\",\n        y1: \"0%\",\n        x2: \"61.1496914%\",\n        y2: \"100%\"\n      }, [createElementVNode(\"stop\", {\n        \"stop-color\": `var(${unref(ns).cssVarBlockName(\"fill-color-1\")})`,\n        offset: \"0%\"\n      }, null, 8, [\"stop-color\"]), createElementVNode(\"stop\", {\n        \"stop-color\": `var(${unref(ns).cssVarBlockName(\"fill-color-4\")})`,\n        offset: \"100%\"\n      }, null, 8, [\"stop-color\"])], 8, [\"id\"]), createElementVNode(\"linearGradient\", {\n        id: `linearGradient-2-${unref(id)}`,\n        x1: \"0%\",\n        y1: \"9.5%\",\n        x2: \"100%\",\n        y2: \"90.5%\"\n      }, [createElementVNode(\"stop\", {\n        \"stop-color\": `var(${unref(ns).cssVarBlockName(\"fill-color-1\")})`,\n        offset: \"0%\"\n      }, null, 8, [\"stop-color\"]), createElementVNode(\"stop\", {\n        \"stop-color\": `var(${unref(ns).cssVarBlockName(\"fill-color-6\")})`,\n        offset: \"100%\"\n      }, null, 8, [\"stop-color\"])], 8, [\"id\"]), createElementVNode(\"rect\", {\n        id: `path-3-${unref(id)}`,\n        x: \"0\",\n        y: \"0\",\n        width: \"17\",\n        height: \"36\"\n      }, null, 8, [\"id\"])]), createElementVNode(\"g\", {\n        stroke: \"none\",\n        \"stroke-width\": \"1\",\n        fill: \"none\",\n        \"fill-rule\": \"evenodd\"\n      }, [createElementVNode(\"g\", {\n        transform: \"translate(-1268.000000, -535.000000)\"\n      }, [createElementVNode(\"g\", {\n        transform: \"translate(1268.000000, 535.000000)\"\n      }, [createElementVNode(\"path\", {\n        d: \"M39.5,86 C61.3152476,86 79,83.9106622 79,81.3333333 C79,78.7560045 57.3152476,78 35.5,78 C13.6847524,78 0,78.7560045 0,81.3333333 C0,83.9106622 17.6847524,86 39.5,86 Z\",\n        fill: `var(${unref(ns).cssVarBlockName(\"fill-color-3\")})`\n      }, null, 8, [\"fill\"]), createElementVNode(\"polygon\", {\n        fill: `var(${unref(ns).cssVarBlockName(\"fill-color-7\")})`,\n        transform: \"translate(27.500000, 51.500000) scale(1, -1) translate(-27.500000, -51.500000) \",\n        points: \"13 58 53 58 42 45 2 45\"\n      }, null, 8, [\"fill\"]), createElementVNode(\"g\", {\n        transform: \"translate(34.500000, 31.500000) scale(-1, 1) rotate(-25.000000) translate(-34.500000, -31.500000) translate(7.000000, 10.000000)\"\n      }, [createElementVNode(\"polygon\", {\n        fill: `var(${unref(ns).cssVarBlockName(\"fill-color-7\")})`,\n        transform: \"translate(11.500000, 5.000000) scale(1, -1) translate(-11.500000, -5.000000) \",\n        points: \"2.84078316e-14 3 18 3 23 7 5 7\"\n      }, null, 8, [\"fill\"]), createElementVNode(\"polygon\", {\n        fill: `var(${unref(ns).cssVarBlockName(\"fill-color-5\")})`,\n        points: \"-3.69149156e-15 7 38 7 38 43 -3.69149156e-15 43\"\n      }, null, 8, [\"fill\"]), createElementVNode(\"rect\", {\n        fill: `url(#linearGradient-1-${unref(id)})`,\n        transform: \"translate(46.500000, 25.000000) scale(-1, 1) translate(-46.500000, -25.000000) \",\n        x: \"38\",\n        y: \"7\",\n        width: \"17\",\n        height: \"36\"\n      }, null, 8, [\"fill\"]), createElementVNode(\"polygon\", {\n        fill: `var(${unref(ns).cssVarBlockName(\"fill-color-2\")})`,\n        transform: \"translate(39.500000, 3.500000) scale(-1, 1) translate(-39.500000, -3.500000) \",\n        points: \"24 7 41 7 55 -3.63806207e-12 38 -3.63806207e-12\"\n      }, null, 8, [\"fill\"])]), createElementVNode(\"rect\", {\n        fill: `url(#linearGradient-2-${unref(id)})`,\n        x: \"13\",\n        y: \"45\",\n        width: \"40\",\n        height: \"36\"\n      }, null, 8, [\"fill\"]), createElementVNode(\"g\", {\n        transform: \"translate(53.000000, 45.000000)\"\n      }, [createElementVNode(\"use\", {\n        fill: `var(${unref(ns).cssVarBlockName(\"fill-color-8\")})`,\n        transform: \"translate(8.500000, 18.000000) scale(-1, 1) translate(-8.500000, -18.000000) \",\n        \"xlink:href\": `#path-3-${unref(id)}`\n      }, null, 8, [\"fill\", \"xlink:href\"]), createElementVNode(\"polygon\", {\n        fill: `var(${unref(ns).cssVarBlockName(\"fill-color-9\")})`,\n        mask: `url(#mask-4-${unref(id)})`,\n        transform: \"translate(12.000000, 9.000000) scale(-1, 1) translate(-12.000000, -9.000000) \",\n        points: \"7 0 24 0 20 18 7 16.5\"\n      }, null, 8, [\"fill\", \"mask\"])]), createElementVNode(\"polygon\", {\n        fill: `var(${unref(ns).cssVarBlockName(\"fill-color-2\")})`,\n        transform: \"translate(66.000000, 51.500000) scale(-1, 1) translate(-66.000000, -51.500000) \",\n        points: \"62 45 79 45 70 58 53 58\"\n      }, null, 8, [\"fill\"])])])])]);\n    };\n  }\n});\nvar ImgEmpty = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"img-empty.vue\"]]);\nexport { ImgEmpty as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}