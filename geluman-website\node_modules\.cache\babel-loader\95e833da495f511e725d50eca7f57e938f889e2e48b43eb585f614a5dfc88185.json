{"ast": null, "code": "import { unref } from 'vue';\nimport { isArray } from '@vue/shared';\nconst isTriggerType = (trigger, type) => {\n  if (isArray(trigger)) {\n    return trigger.includes(type);\n  }\n  return trigger === type;\n};\nconst whenTrigger = (trigger, type, handler) => {\n  return e => {\n    isTriggerType(unref(trigger), type) && handler(e);\n  };\n};\nexport { isTriggerType, whenTrigger };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}