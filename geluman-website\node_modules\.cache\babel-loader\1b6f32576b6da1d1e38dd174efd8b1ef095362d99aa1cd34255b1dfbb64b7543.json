{"ast": null, "code": "import { placements } from '@popperjs/core';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nconst POSITIONING_STRATEGIES = [\"fixed\", \"absolute\"];\nconst popperCoreConfigProps = buildProps({\n  boundariesPadding: {\n    type: Number,\n    default: 0\n  },\n  fallbackPlacements: {\n    type: definePropType(Array),\n    default: void 0\n  },\n  gpuAcceleration: {\n    type: Boolean,\n    default: true\n  },\n  offset: {\n    type: Number,\n    default: 12\n  },\n  placement: {\n    type: String,\n    values: placements,\n    default: \"bottom\"\n  },\n  popperOptions: {\n    type: definePropType(Object),\n    default: () => ({})\n  },\n  strategy: {\n    type: String,\n    values: POSITIONING_STRATEGIES,\n    default: \"absolute\"\n  }\n});\nconst popperContentProps = buildProps({\n  ...popperCoreConfigProps,\n  id: String,\n  style: {\n    type: definePropType([String, Array, Object])\n  },\n  className: {\n    type: definePropType([String, Array, Object])\n  },\n  effect: {\n    type: definePropType(String),\n    default: \"dark\"\n  },\n  visible: Boolean,\n  enterable: {\n    type: Boolean,\n    default: true\n  },\n  pure: Boolean,\n  focusOnShow: {\n    type: Boolean,\n    default: false\n  },\n  trapping: {\n    type: Boolean,\n    default: false\n  },\n  popperClass: {\n    type: definePropType([String, Array, Object])\n  },\n  popperStyle: {\n    type: definePropType([String, Array, Object])\n  },\n  referenceEl: {\n    type: definePropType(Object)\n  },\n  triggerTargetEl: {\n    type: definePropType(Object)\n  },\n  stopPopperMouseEvent: {\n    type: Boolean,\n    default: true\n  },\n  virtualTriggering: Boolean,\n  zIndex: Number,\n  ...useAriaProps([\"ariaLabel\"])\n});\nconst popperContentEmits = {\n  mouseenter: evt => evt instanceof MouseEvent,\n  mouseleave: evt => evt instanceof MouseEvent,\n  focus: () => true,\n  blur: () => true,\n  close: () => true\n};\nconst usePopperCoreConfigProps = popperCoreConfigProps;\nconst usePopperContentProps = popperContentProps;\nconst usePopperContentEmits = popperContentEmits;\nexport { popperContentEmits, popperContentProps, popperCoreConfigProps, usePopperContentEmits, usePopperContentProps, usePopperCoreConfigProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}