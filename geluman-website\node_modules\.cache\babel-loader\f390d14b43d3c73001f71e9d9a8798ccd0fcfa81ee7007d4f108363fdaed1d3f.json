{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { getCurrentInstance, useSlots, ref, computed, unref, isVNode, watch, shallowRef, onMounted, onBeforeUnmount, provide } from 'vue';\nimport { throttle } from 'lodash-unified';\nimport { useResizeObserver } from '@vueuse/core';\nimport { CAROUSEL_ITEM_NAME, carouselContextKey } from './constants.mjs';\nimport { useOrderedChildren } from '../../../hooks/use-ordered-children/index.mjs';\nimport { isString } from '@vue/shared';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { flattedChildren } from '../../../utils/vue/vnode.mjs';\nimport { CHANGE_EVENT } from '../../../constants/event.mjs';\nconst THROTTLE_TIME = 300;\nconst useCarousel = (props, emit, componentName) => {\n  const {\n    children: items,\n    addChild: addItem,\n    removeChild: removeItem\n  } = useOrderedChildren(getCurrentInstance(), CAROUSEL_ITEM_NAME);\n  const slots = useSlots();\n  const activeIndex = ref(-1);\n  const timer = ref(null);\n  const hover = ref(false);\n  const root = ref();\n  const containerHeight = ref(0);\n  const isItemsTwoLength = ref(true);\n  const isFirstCall = ref(true);\n  const isTransitioning = ref(false);\n  const arrowDisplay = computed(() => props.arrow !== \"never\" && !unref(isVertical));\n  const hasLabel = computed(() => {\n    return items.value.some(item => item.props.label.toString().length > 0);\n  });\n  const isCardType = computed(() => props.type === \"card\");\n  const isVertical = computed(() => props.direction === \"vertical\");\n  const containerStyle = computed(() => {\n    if (props.height !== \"auto\") {\n      return {\n        height: props.height\n      };\n    }\n    return {\n      height: `${containerHeight.value}px`,\n      overflow: \"hidden\"\n    };\n  });\n  const throttledArrowClick = throttle(index => {\n    setActiveItem(index);\n  }, THROTTLE_TIME, {\n    trailing: true\n  });\n  const throttledIndicatorHover = throttle(index => {\n    handleIndicatorHover(index);\n  }, THROTTLE_TIME);\n  const isTwoLengthShow = index => {\n    if (!isItemsTwoLength.value) return true;\n    return activeIndex.value <= 1 ? index <= 1 : index > 1;\n  };\n  function pauseTimer() {\n    if (timer.value) {\n      clearInterval(timer.value);\n      timer.value = null;\n    }\n  }\n  function startTimer() {\n    if (props.interval <= 0 || !props.autoplay || timer.value) return;\n    timer.value = setInterval(() => playSlides(), props.interval);\n  }\n  const playSlides = () => {\n    if (!isFirstCall.value) {\n      isTransitioning.value = true;\n    }\n    isFirstCall.value = false;\n    if (activeIndex.value < items.value.length - 1) {\n      activeIndex.value = activeIndex.value + 1;\n    } else if (props.loop) {\n      activeIndex.value = 0;\n    } else {\n      isTransitioning.value = false;\n    }\n  };\n  function setActiveItem(index) {\n    if (!isFirstCall.value) {\n      isTransitioning.value = true;\n    }\n    isFirstCall.value = false;\n    if (isString(index)) {\n      const filteredItems = items.value.filter(item => item.props.name === index);\n      if (filteredItems.length > 0) {\n        index = items.value.indexOf(filteredItems[0]);\n      }\n    }\n    index = Number(index);\n    if (Number.isNaN(index) || index !== Math.floor(index)) {\n      debugWarn(componentName, \"index must be integer.\");\n      return;\n    }\n    const itemCount = items.value.length;\n    const oldIndex = activeIndex.value;\n    if (index < 0) {\n      activeIndex.value = props.loop ? itemCount - 1 : 0;\n    } else if (index >= itemCount) {\n      activeIndex.value = props.loop ? 0 : itemCount - 1;\n    } else {\n      activeIndex.value = index;\n    }\n    if (oldIndex === activeIndex.value) {\n      resetItemPosition(oldIndex);\n    }\n    resetTimer();\n  }\n  function resetItemPosition(oldIndex) {\n    items.value.forEach((item, index) => {\n      item.translateItem(index, activeIndex.value, oldIndex);\n    });\n  }\n  function itemInStage(item, index) {\n    var _a, _b, _c, _d;\n    const _items = unref(items);\n    const itemCount = _items.length;\n    if (itemCount === 0 || !item.states.inStage) return false;\n    const nextItemIndex = index + 1;\n    const prevItemIndex = index - 1;\n    const lastItemIndex = itemCount - 1;\n    const isLastItemActive = _items[lastItemIndex].states.active;\n    const isFirstItemActive = _items[0].states.active;\n    const isNextItemActive = (_b = (_a = _items[nextItemIndex]) == null ? void 0 : _a.states) == null ? void 0 : _b.active;\n    const isPrevItemActive = (_d = (_c = _items[prevItemIndex]) == null ? void 0 : _c.states) == null ? void 0 : _d.active;\n    if (index === lastItemIndex && isFirstItemActive || isNextItemActive) {\n      return \"left\";\n    } else if (index === 0 && isLastItemActive || isPrevItemActive) {\n      return \"right\";\n    }\n    return false;\n  }\n  function handleMouseEnter() {\n    hover.value = true;\n    if (props.pauseOnHover) {\n      pauseTimer();\n    }\n  }\n  function handleMouseLeave() {\n    hover.value = false;\n    startTimer();\n  }\n  function handleTransitionEnd() {\n    isTransitioning.value = false;\n  }\n  function handleButtonEnter(arrow) {\n    if (unref(isVertical)) return;\n    items.value.forEach((item, index) => {\n      if (arrow === itemInStage(item, index)) {\n        item.states.hover = true;\n      }\n    });\n  }\n  function handleButtonLeave() {\n    if (unref(isVertical)) return;\n    items.value.forEach(item => {\n      item.states.hover = false;\n    });\n  }\n  function handleIndicatorClick(index) {\n    if (index !== activeIndex.value) {\n      if (!isFirstCall.value) {\n        isTransitioning.value = true;\n      }\n    }\n    activeIndex.value = index;\n  }\n  function handleIndicatorHover(index) {\n    if (props.trigger === \"hover\" && index !== activeIndex.value) {\n      activeIndex.value = index;\n      if (!isFirstCall.value) {\n        isTransitioning.value = true;\n      }\n    }\n  }\n  function prev() {\n    setActiveItem(activeIndex.value - 1);\n  }\n  function next() {\n    setActiveItem(activeIndex.value + 1);\n  }\n  function resetTimer() {\n    pauseTimer();\n    if (!props.pauseOnHover) startTimer();\n  }\n  function setContainerHeight(height) {\n    if (props.height !== \"auto\") return;\n    containerHeight.value = height;\n  }\n  function PlaceholderItem() {\n    var _a;\n    const defaultSlots = (_a = slots.default) == null ? void 0 : _a.call(slots);\n    if (!defaultSlots) return null;\n    const flatSlots = flattedChildren(defaultSlots);\n    const normalizeSlots = flatSlots.filter(slot => {\n      return isVNode(slot) && slot.type.name === CAROUSEL_ITEM_NAME;\n    });\n    if ((normalizeSlots == null ? void 0 : normalizeSlots.length) === 2 && props.loop && !isCardType.value) {\n      isItemsTwoLength.value = true;\n      return normalizeSlots;\n    }\n    isItemsTwoLength.value = false;\n    return null;\n  }\n  watch(() => activeIndex.value, (current, prev2) => {\n    resetItemPosition(prev2);\n    if (isItemsTwoLength.value) {\n      current = current % 2;\n      prev2 = prev2 % 2;\n    }\n    if (prev2 > -1) {\n      emit(CHANGE_EVENT, current, prev2);\n    }\n  });\n  watch(() => props.autoplay, autoplay => {\n    autoplay ? startTimer() : pauseTimer();\n  });\n  watch(() => props.loop, () => {\n    setActiveItem(activeIndex.value);\n  });\n  watch(() => props.interval, () => {\n    resetTimer();\n  });\n  const resizeObserver = shallowRef();\n  onMounted(() => {\n    watch(() => items.value, () => {\n      if (items.value.length > 0) setActiveItem(props.initialIndex);\n    }, {\n      immediate: true\n    });\n    resizeObserver.value = useResizeObserver(root.value, () => {\n      resetItemPosition();\n    });\n    startTimer();\n  });\n  onBeforeUnmount(() => {\n    pauseTimer();\n    if (root.value && resizeObserver.value) resizeObserver.value.stop();\n  });\n  provide(carouselContextKey, {\n    root,\n    isCardType,\n    isVertical,\n    items,\n    loop: props.loop,\n    cardScale: props.cardScale,\n    addItem,\n    removeItem,\n    setActiveItem,\n    setContainerHeight\n  });\n  return {\n    root,\n    activeIndex,\n    arrowDisplay,\n    hasLabel,\n    hover,\n    isCardType,\n    isTransitioning,\n    items,\n    isVertical,\n    containerStyle,\n    isItemsTwoLength,\n    handleButtonEnter,\n    handleTransitionEnd,\n    handleButtonLeave,\n    handleIndicatorClick,\n    handleMouseEnter,\n    handleMouseLeave,\n    setActiveItem,\n    prev,\n    next,\n    PlaceholderItem,\n    isTwoLengthShow,\n    throttledArrowClick,\n    throttledIndicatorHover\n  };\n};\nexport { useCarousel };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}