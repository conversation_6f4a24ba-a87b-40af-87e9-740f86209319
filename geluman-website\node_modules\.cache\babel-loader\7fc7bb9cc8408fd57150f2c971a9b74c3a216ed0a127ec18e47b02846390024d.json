{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { componentSizes } from '../../../constants/size.mjs';\nconst tagProps = buildProps({\n  type: {\n    type: String,\n    values: [\"primary\", \"success\", \"info\", \"warning\", \"danger\"],\n    default: \"primary\"\n  },\n  closable: Boolean,\n  disableTransitions: <PERSON><PERSON>an,\n  hit: Boolean,\n  color: String,\n  size: {\n    type: String,\n    values: componentSizes\n  },\n  effect: {\n    type: String,\n    values: [\"dark\", \"light\", \"plain\"],\n    default: \"light\"\n  },\n  round: Boolean\n});\nconst tagEmits = {\n  close: evt => evt instanceof MouseEvent,\n  click: evt => evt instanceof MouseEvent\n};\nexport { tagEmits, tagProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}