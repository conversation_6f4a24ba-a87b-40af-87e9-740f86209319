{"ast": null, "code": "import Radio from './src/radio2.mjs';\nimport RadioButton from './src/radio-button2.mjs';\nimport RadioGroup from './src/radio-group2.mjs';\nexport { radioEmits, radioProps, radioPropsBase } from './src/radio.mjs';\nexport { radioGroupEmits, radioGroupProps } from './src/radio-group.mjs';\nexport { radioButtonProps } from './src/radio-button.mjs';\nexport { radioGroupKey } from './src/constants.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElRadio = withInstall(Radio, {\n  RadioButton,\n  RadioGroup\n});\nconst ElRadioGroup = withNoopInstall(RadioGroup);\nconst ElRadioButton = withNoopInstall(RadioButton);\nexport { ElRadio, ElRadioButton, ElRadioGroup, ElRadio as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}