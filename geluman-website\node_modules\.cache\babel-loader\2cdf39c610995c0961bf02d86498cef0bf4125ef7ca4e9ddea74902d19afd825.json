{"ast": null, "code": "import { defineComponent, inject, unref, openBlock, createElement<PERSON><PERSON>, Fragment, createElementVNode, renderList, createBlock, createVNode } from 'vue';\nimport ElDescriptionsCell from './descriptions-cell.mjs';\nimport { descriptionsKey } from './token.mjs';\nimport { descriptionsRowProps } from './descriptions-row.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst __default__ = defineComponent({\n  name: \"ElDescriptionsRow\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: descriptionsRowProps,\n  setup(__props) {\n    const descriptions = inject(descriptionsKey, {});\n    return (_ctx, _cache) => {\n      return unref(descriptions).direction === \"vertical\" ? (openBlock(), createElementBlock(Fragment, {\n        key: 0\n      }, [createElementVNode(\"tr\", null, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.row, (cell, _index) => {\n        return openBlock(), createBlock(unref(ElDescriptionsCell), {\n          key: `tr1-${_index}`,\n          cell,\n          tag: \"th\",\n          type: \"label\"\n        }, null, 8, [\"cell\"]);\n      }), 128))]), createElementVNode(\"tr\", null, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.row, (cell, _index) => {\n        return openBlock(), createBlock(unref(ElDescriptionsCell), {\n          key: `tr2-${_index}`,\n          cell,\n          tag: \"td\",\n          type: \"content\"\n        }, null, 8, [\"cell\"]);\n      }), 128))])], 64)) : (openBlock(), createElementBlock(\"tr\", {\n        key: 1\n      }, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.row, (cell, _index) => {\n        return openBlock(), createElementBlock(Fragment, {\n          key: `tr3-${_index}`\n        }, [unref(descriptions).border ? (openBlock(), createElementBlock(Fragment, {\n          key: 0\n        }, [createVNode(unref(ElDescriptionsCell), {\n          cell,\n          tag: \"td\",\n          type: \"label\"\n        }, null, 8, [\"cell\"]), createVNode(unref(ElDescriptionsCell), {\n          cell,\n          tag: \"td\",\n          type: \"content\"\n        }, null, 8, [\"cell\"])], 64)) : (openBlock(), createBlock(unref(ElDescriptionsCell), {\n          key: 1,\n          cell,\n          tag: \"td\",\n          type: \"both\"\n        }, null, 8, [\"cell\"]))], 64);\n      }), 128))]));\n    };\n  }\n});\nvar ElDescriptionsRow = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"descriptions-row.vue\"]]);\nexport { ElDescriptionsRow as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}