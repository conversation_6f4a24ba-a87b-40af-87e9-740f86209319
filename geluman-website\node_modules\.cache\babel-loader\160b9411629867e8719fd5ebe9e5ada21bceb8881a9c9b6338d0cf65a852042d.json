{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, renderSlot } from 'vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElAside\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: {\n    width: {\n      type: String,\n      default: null\n    }\n  },\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"aside\");\n    const style = computed(() => props.width ? ns.cssVarBlock({\n      width: props.width\n    }) : {});\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"aside\", {\n        class: normalizeClass(unref(ns).b()),\n        style: normalizeStyle(unref(style))\n      }, [renderSlot(_ctx.$slots, \"default\")], 6);\n    };\n  }\n});\nvar Aside = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"aside.vue\"]]);\nexport { Aside as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}