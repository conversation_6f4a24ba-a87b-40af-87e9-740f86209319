{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\n/*!\n  * vue-i18n v9.14.2\n  * (c) 2024 kazuya kawa<PERSON>\n  * Released under the MIT License.\n  */\nimport { getGlobalThis, incrementer, format, makeSymbol, isPlainObject, isArray, create, deepCopy, isString, hasOwn, isObject, warn, warnOnce, isBoolean, isRegExp, isFunction, inBrowser, assign, isNumber, createEmitter, isEmptyObject } from '@intlify/shared';\nimport { CoreWarnCodes, CoreErrorCodes, createCompileError, DEFAULT_LOCALE, updateFallbackLocale, setFallbackContext, createCoreContext, clearDateTimeFormat, clearNumberFormat, setAdditionalMeta, getFallbackContext, NOT_REOSLVED, isTranslateFallbackWarn, isTranslateMissingWarn, parseTranslateArgs, translate, MISSING_RESOLVE_VALUE, parseDateTimeArgs, datetime, parseNumberArgs, number, isMessageAST, isMessageFunction, fallbackWithLocaleChain, NUMBER_FORMAT_OPTIONS_KEYS, DATETIME_FORMAT_OPTIONS_KEYS, registerMessageCompiler, compile, compileToFunction, registerMessageResolver, resolveValue, registerLocaleFallbacker, setDevToolsHook } from '@intlify/core-base';\nimport { createVNode, Text, computed, watch, getCurrentInstance, ref, shallowRef, Fragment, defineComponent, h, effectScope, inject, onMounted, onUnmounted, onBeforeMount, isRef } from 'vue';\nimport { setupDevtoolsPlugin } from '@vue/devtools-api';\n\n/**\n * Vue I18n Version\n *\n * @remarks\n * Semver format. Same format as the package.json `version` field.\n *\n * @VueI18nGeneral\n */\nconst VERSION = '9.14.2';\n/**\n * This is only called in esm-bundler builds.\n * istanbul-ignore-next\n */\nfunction initFeatureFlags() {\n  if (typeof __VUE_I18N_FULL_INSTALL__ !== 'boolean') {\n    getGlobalThis().__VUE_I18N_FULL_INSTALL__ = true;\n  }\n  if (typeof __VUE_I18N_LEGACY_API__ !== 'boolean') {\n    getGlobalThis().__VUE_I18N_LEGACY_API__ = true;\n  }\n  if (typeof __INTLIFY_JIT_COMPILATION__ !== 'boolean') {\n    getGlobalThis().__INTLIFY_JIT_COMPILATION__ = false;\n  }\n  if (typeof __INTLIFY_DROP_MESSAGE_COMPILER__ !== 'boolean') {\n    getGlobalThis().__INTLIFY_DROP_MESSAGE_COMPILER__ = false;\n  }\n  if (typeof __INTLIFY_PROD_DEVTOOLS__ !== 'boolean') {\n    getGlobalThis().__INTLIFY_PROD_DEVTOOLS__ = false;\n  }\n}\nconst code$1 = CoreWarnCodes.__EXTEND_POINT__;\nconst inc$1 = incrementer(code$1);\nconst I18nWarnCodes = {\n  FALLBACK_TO_ROOT: code$1,\n  // 9\n  NOT_SUPPORTED_PRESERVE: inc$1(),\n  // 10\n  NOT_SUPPORTED_FORMATTER: inc$1(),\n  // 11\n  NOT_SUPPORTED_PRESERVE_DIRECTIVE: inc$1(),\n  // 12\n  NOT_SUPPORTED_GET_CHOICE_INDEX: inc$1(),\n  // 13\n  COMPONENT_NAME_LEGACY_COMPATIBLE: inc$1(),\n  // 14\n  NOT_FOUND_PARENT_SCOPE: inc$1(),\n  // 15\n  IGNORE_OBJ_FLATTEN: inc$1(),\n  // 16\n  NOTICE_DROP_ALLOW_COMPOSITION: inc$1(),\n  // 17\n  NOTICE_DROP_TRANSLATE_EXIST_COMPATIBLE_FLAG: inc$1() // 18\n};\nconst warnMessages = {\n  [I18nWarnCodes.FALLBACK_TO_ROOT]: `Fall back to {type} '{key}' with root locale.`,\n  [I18nWarnCodes.NOT_SUPPORTED_PRESERVE]: `Not supported 'preserve'.`,\n  [I18nWarnCodes.NOT_SUPPORTED_FORMATTER]: `Not supported 'formatter'.`,\n  [I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE]: `Not supported 'preserveDirectiveContent'.`,\n  [I18nWarnCodes.NOT_SUPPORTED_GET_CHOICE_INDEX]: `Not supported 'getChoiceIndex'.`,\n  [I18nWarnCodes.COMPONENT_NAME_LEGACY_COMPATIBLE]: `Component name legacy compatible: '{name}' -> 'i18n'`,\n  [I18nWarnCodes.NOT_FOUND_PARENT_SCOPE]: `Not found parent scope. use the global scope.`,\n  [I18nWarnCodes.IGNORE_OBJ_FLATTEN]: `Ignore object flatten: '{key}' key has an string value`,\n  [I18nWarnCodes.NOTICE_DROP_ALLOW_COMPOSITION]: `'allowComposition' option will be dropped in the next major version. For more information, please see 👉 https://tinyurl.com/2p97mcze`,\n  [I18nWarnCodes.NOTICE_DROP_TRANSLATE_EXIST_COMPATIBLE_FLAG]: `'translateExistCompatible' option will be dropped in the next major version.`\n};\nfunction getWarnMessage(code, ...args) {\n  return format(warnMessages[code], ...args);\n}\nconst code = CoreErrorCodes.__EXTEND_POINT__;\nconst inc = incrementer(code);\nconst I18nErrorCodes = {\n  // composer module errors\n  UNEXPECTED_RETURN_TYPE: code,\n  // 24\n  // legacy module errors\n  INVALID_ARGUMENT: inc(),\n  // 25\n  // i18n module errors\n  MUST_BE_CALL_SETUP_TOP: inc(),\n  // 26\n  NOT_INSTALLED: inc(),\n  // 27\n  NOT_AVAILABLE_IN_LEGACY_MODE: inc(),\n  // 28\n  // directive module errors\n  REQUIRED_VALUE: inc(),\n  // 29\n  INVALID_VALUE: inc(),\n  // 30\n  // vue-devtools errors\n  CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN: inc(),\n  // 31\n  NOT_INSTALLED_WITH_PROVIDE: inc(),\n  // 32\n  // unexpected error\n  UNEXPECTED_ERROR: inc(),\n  // 33\n  // not compatible legacy vue-i18n constructor\n  NOT_COMPATIBLE_LEGACY_VUE_I18N: inc(),\n  // 34\n  // bridge support vue 2.x only\n  BRIDGE_SUPPORT_VUE_2_ONLY: inc(),\n  // 35\n  // need to define `i18n` option in `allowComposition: true` and `useScope: 'local' at `useI18n``\n  MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION: inc(),\n  // 36\n  // Not available Compostion API in Legacy API mode. Please make sure that the legacy API mode is working properly\n  NOT_AVAILABLE_COMPOSITION_IN_LEGACY: inc(),\n  // 37\n  // for enhancement\n  __EXTEND_POINT__: inc() // 38\n};\nfunction createI18nError(code, ...args) {\n  return createCompileError(code, null, process.env.NODE_ENV !== 'production' ? {\n    messages: errorMessages,\n    args\n  } : undefined);\n}\nconst errorMessages = {\n  [I18nErrorCodes.UNEXPECTED_RETURN_TYPE]: 'Unexpected return type in composer',\n  [I18nErrorCodes.INVALID_ARGUMENT]: 'Invalid argument',\n  [I18nErrorCodes.MUST_BE_CALL_SETUP_TOP]: 'Must be called at the top of a `setup` function',\n  [I18nErrorCodes.NOT_INSTALLED]: 'Need to install with `app.use` function',\n  [I18nErrorCodes.UNEXPECTED_ERROR]: 'Unexpected error',\n  [I18nErrorCodes.NOT_AVAILABLE_IN_LEGACY_MODE]: 'Not available in legacy mode',\n  [I18nErrorCodes.REQUIRED_VALUE]: `Required in value: {0}`,\n  [I18nErrorCodes.INVALID_VALUE]: `Invalid value`,\n  [I18nErrorCodes.CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN]: `Cannot setup vue-devtools plugin`,\n  [I18nErrorCodes.NOT_INSTALLED_WITH_PROVIDE]: 'Need to install with `provide` function',\n  [I18nErrorCodes.NOT_COMPATIBLE_LEGACY_VUE_I18N]: 'Not compatible legacy VueI18n.',\n  [I18nErrorCodes.BRIDGE_SUPPORT_VUE_2_ONLY]: 'vue-i18n-bridge support Vue 2.x only',\n  [I18nErrorCodes.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION]: 'Must define ‘i18n’ option or custom block in Composition API with using local scope in Legacy API mode',\n  [I18nErrorCodes.NOT_AVAILABLE_COMPOSITION_IN_LEGACY]: 'Not available Compostion API in Legacy API mode. Please make sure that the legacy API mode is working properly'\n};\nconst TranslateVNodeSymbol = /* #__PURE__*/makeSymbol('__translateVNode');\nconst DatetimePartsSymbol = /* #__PURE__*/makeSymbol('__datetimeParts');\nconst NumberPartsSymbol = /* #__PURE__*/makeSymbol('__numberParts');\nconst EnableEmitter = /* #__PURE__*/makeSymbol('__enableEmitter');\nconst DisableEmitter = /* #__PURE__*/makeSymbol('__disableEmitter');\nconst SetPluralRulesSymbol = makeSymbol('__setPluralRules');\nmakeSymbol('__intlifyMeta');\nconst InejctWithOptionSymbol = /* #__PURE__*/makeSymbol('__injectWithOption');\nconst DisposeSymbol = /* #__PURE__*/makeSymbol('__dispose');\nconst __VUE_I18N_BRIDGE__ = '__VUE_I18N_BRIDGE__';\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Transform flat json in obj to normal json in obj\n */\nfunction handleFlatJson(obj) {\n  // check obj\n  if (!isObject(obj)) {\n    return obj;\n  }\n  for (const key in obj) {\n    // check key\n    if (!hasOwn(obj, key)) {\n      continue;\n    }\n    // handle for normal json\n    if (!key.includes('.')) {\n      // recursive process value if value is also a object\n      if (isObject(obj[key])) {\n        handleFlatJson(obj[key]);\n      }\n    }\n    // handle for flat json, transform to normal json\n    else {\n      // go to the last object\n      const subKeys = key.split('.');\n      const lastIndex = subKeys.length - 1;\n      let currentObj = obj;\n      let hasStringValue = false;\n      for (let i = 0; i < lastIndex; i++) {\n        if (!(subKeys[i] in currentObj)) {\n          currentObj[subKeys[i]] = create();\n        }\n        if (!isObject(currentObj[subKeys[i]])) {\n          process.env.NODE_ENV !== 'production' && warn(getWarnMessage(I18nWarnCodes.IGNORE_OBJ_FLATTEN, {\n            key: subKeys[i]\n          }));\n          hasStringValue = true;\n          break;\n        }\n        currentObj = currentObj[subKeys[i]];\n      }\n      // update last object value, delete old property\n      if (!hasStringValue) {\n        currentObj[subKeys[lastIndex]] = obj[key];\n        delete obj[key];\n      }\n      // recursive process value if value is also a object\n      if (isObject(currentObj[subKeys[lastIndex]])) {\n        handleFlatJson(currentObj[subKeys[lastIndex]]);\n      }\n    }\n  }\n  return obj;\n}\nfunction getLocaleMessages(locale, options) {\n  const {\n    messages,\n    __i18n,\n    messageResolver,\n    flatJson\n  } = options;\n  // prettier-ignore\n  const ret = isPlainObject(messages) ? messages : isArray(__i18n) ? create() : {\n    [locale]: create()\n  };\n  // merge locale messages of i18n custom block\n  if (isArray(__i18n)) {\n    __i18n.forEach(custom => {\n      if ('locale' in custom && 'resource' in custom) {\n        const {\n          locale,\n          resource\n        } = custom;\n        if (locale) {\n          ret[locale] = ret[locale] || create();\n          deepCopy(resource, ret[locale]);\n        } else {\n          deepCopy(resource, ret);\n        }\n      } else {\n        isString(custom) && deepCopy(JSON.parse(custom), ret);\n      }\n    });\n  }\n  // handle messages for flat json\n  if (messageResolver == null && flatJson) {\n    for (const key in ret) {\n      if (hasOwn(ret, key)) {\n        handleFlatJson(ret[key]);\n      }\n    }\n  }\n  return ret;\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getComponentOptions(instance) {\n  return instance.type;\n}\nfunction adjustI18nResources(gl, options, componentOptions // eslint-disable-line @typescript-eslint/no-explicit-any\n) {\n  let messages = isObject(options.messages) ? options.messages : create();\n  if ('__i18nGlobal' in componentOptions) {\n    messages = getLocaleMessages(gl.locale.value, {\n      messages,\n      __i18n: componentOptions.__i18nGlobal\n    });\n  }\n  // merge locale messages\n  const locales = Object.keys(messages);\n  if (locales.length) {\n    locales.forEach(locale => {\n      gl.mergeLocaleMessage(locale, messages[locale]);\n    });\n  }\n  {\n    // merge datetime formats\n    if (isObject(options.datetimeFormats)) {\n      const locales = Object.keys(options.datetimeFormats);\n      if (locales.length) {\n        locales.forEach(locale => {\n          gl.mergeDateTimeFormat(locale, options.datetimeFormats[locale]);\n        });\n      }\n    }\n    // merge number formats\n    if (isObject(options.numberFormats)) {\n      const locales = Object.keys(options.numberFormats);\n      if (locales.length) {\n        locales.forEach(locale => {\n          gl.mergeNumberFormat(locale, options.numberFormats[locale]);\n        });\n      }\n    }\n  }\n}\nfunction createTextNode(key) {\n  return createVNode(Text, null, key, 0);\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n// extend VNode interface\nconst DEVTOOLS_META = '__INTLIFY_META__';\nconst NOOP_RETURN_ARRAY = () => [];\nconst NOOP_RETURN_FALSE = () => false;\nlet composerID = 0;\nfunction defineCoreMissingHandler(missing) {\n  return (ctx, locale, key, type) => {\n    return missing(locale, key, getCurrentInstance() || undefined, type);\n  };\n}\n// for Intlify DevTools\n/* #__NO_SIDE_EFFECTS__ */\nconst getMetaInfo = () => {\n  const instance = getCurrentInstance();\n  let meta = null; // eslint-disable-line @typescript-eslint/no-explicit-any\n  return instance && (meta = getComponentOptions(instance)[DEVTOOLS_META]) ? {\n    [DEVTOOLS_META]: meta\n  } // eslint-disable-line @typescript-eslint/no-explicit-any\n  : null;\n};\n/**\n * Create composer interface factory\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nfunction createComposer(options = {}, VueI18nLegacy) {\n  const {\n    __root,\n    __injectWithOption\n  } = options;\n  const _isGlobal = __root === undefined;\n  const flatJson = options.flatJson;\n  const _ref = inBrowser ? ref : shallowRef;\n  const translateExistCompatible = !!options.translateExistCompatible;\n  if (process.env.NODE_ENV !== 'production') {\n    if (translateExistCompatible && !false) {\n      warnOnce(getWarnMessage(I18nWarnCodes.NOTICE_DROP_TRANSLATE_EXIST_COMPATIBLE_FLAG));\n    }\n  }\n  let _inheritLocale = isBoolean(options.inheritLocale) ? options.inheritLocale : true;\n  const _locale = _ref(\n  // prettier-ignore\n  __root && _inheritLocale ? __root.locale.value : isString(options.locale) ? options.locale : DEFAULT_LOCALE);\n  const _fallbackLocale = _ref(\n  // prettier-ignore\n  __root && _inheritLocale ? __root.fallbackLocale.value : isString(options.fallbackLocale) || isArray(options.fallbackLocale) || isPlainObject(options.fallbackLocale) || options.fallbackLocale === false ? options.fallbackLocale : _locale.value);\n  const _messages = _ref(getLocaleMessages(_locale.value, options));\n  // prettier-ignore\n  const _datetimeFormats = _ref(isPlainObject(options.datetimeFormats) ? options.datetimeFormats : {\n    [_locale.value]: {}\n  });\n  // prettier-ignore\n  const _numberFormats = _ref(isPlainObject(options.numberFormats) ? options.numberFormats : {\n    [_locale.value]: {}\n  });\n  // warning suppress options\n  // prettier-ignore\n  let _missingWarn = __root ? __root.missingWarn : isBoolean(options.missingWarn) || isRegExp(options.missingWarn) ? options.missingWarn : true;\n  // prettier-ignore\n  let _fallbackWarn = __root ? __root.fallbackWarn : isBoolean(options.fallbackWarn) || isRegExp(options.fallbackWarn) ? options.fallbackWarn : true;\n  // prettier-ignore\n  let _fallbackRoot = __root ? __root.fallbackRoot : isBoolean(options.fallbackRoot) ? options.fallbackRoot : true;\n  // configure fall back to root\n  let _fallbackFormat = !!options.fallbackFormat;\n  // runtime missing\n  let _missing = isFunction(options.missing) ? options.missing : null;\n  let _runtimeMissing = isFunction(options.missing) ? defineCoreMissingHandler(options.missing) : null;\n  // postTranslation handler\n  let _postTranslation = isFunction(options.postTranslation) ? options.postTranslation : null;\n  // prettier-ignore\n  let _warnHtmlMessage = __root ? __root.warnHtmlMessage : isBoolean(options.warnHtmlMessage) ? options.warnHtmlMessage : true;\n  let _escapeParameter = !!options.escapeParameter;\n  // custom linked modifiers\n  // prettier-ignore\n  const _modifiers = __root ? __root.modifiers : isPlainObject(options.modifiers) ? options.modifiers : {};\n  // pluralRules\n  let _pluralRules = options.pluralRules || __root && __root.pluralRules;\n  // runtime context\n  // eslint-disable-next-line prefer-const\n  let _context;\n  const getCoreContext = () => {\n    _isGlobal && setFallbackContext(null);\n    const ctxOptions = {\n      version: VERSION,\n      locale: _locale.value,\n      fallbackLocale: _fallbackLocale.value,\n      messages: _messages.value,\n      modifiers: _modifiers,\n      pluralRules: _pluralRules,\n      missing: _runtimeMissing === null ? undefined : _runtimeMissing,\n      missingWarn: _missingWarn,\n      fallbackWarn: _fallbackWarn,\n      fallbackFormat: _fallbackFormat,\n      unresolving: true,\n      postTranslation: _postTranslation === null ? undefined : _postTranslation,\n      warnHtmlMessage: _warnHtmlMessage,\n      escapeParameter: _escapeParameter,\n      messageResolver: options.messageResolver,\n      messageCompiler: options.messageCompiler,\n      __meta: {\n        framework: 'vue'\n      }\n    };\n    {\n      ctxOptions.datetimeFormats = _datetimeFormats.value;\n      ctxOptions.numberFormats = _numberFormats.value;\n      ctxOptions.__datetimeFormatters = isPlainObject(_context) ? _context.__datetimeFormatters : undefined;\n      ctxOptions.__numberFormatters = isPlainObject(_context) ? _context.__numberFormatters : undefined;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      ctxOptions.__v_emitter = isPlainObject(_context) ? _context.__v_emitter : undefined;\n    }\n    const ctx = createCoreContext(ctxOptions);\n    _isGlobal && setFallbackContext(ctx);\n    return ctx;\n  };\n  _context = getCoreContext();\n  updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\n  // track reactivity\n  function trackReactivityValues() {\n    return [_locale.value, _fallbackLocale.value, _messages.value, _datetimeFormats.value, _numberFormats.value];\n  }\n  // locale\n  const locale = computed({\n    get: () => _locale.value,\n    set: val => {\n      _locale.value = val;\n      _context.locale = _locale.value;\n    }\n  });\n  // fallbackLocale\n  const fallbackLocale = computed({\n    get: () => _fallbackLocale.value,\n    set: val => {\n      _fallbackLocale.value = val;\n      _context.fallbackLocale = _fallbackLocale.value;\n      updateFallbackLocale(_context, _locale.value, val);\n    }\n  });\n  // messages\n  const messages = computed(() => _messages.value);\n  // datetimeFormats\n  const datetimeFormats = /* #__PURE__*/computed(() => _datetimeFormats.value);\n  // numberFormats\n  const numberFormats = /* #__PURE__*/computed(() => _numberFormats.value);\n  // getPostTranslationHandler\n  function getPostTranslationHandler() {\n    return isFunction(_postTranslation) ? _postTranslation : null;\n  }\n  // setPostTranslationHandler\n  function setPostTranslationHandler(handler) {\n    _postTranslation = handler;\n    _context.postTranslation = handler;\n  }\n  // getMissingHandler\n  function getMissingHandler() {\n    return _missing;\n  }\n  // setMissingHandler\n  function setMissingHandler(handler) {\n    if (handler !== null) {\n      _runtimeMissing = defineCoreMissingHandler(handler);\n    }\n    _missing = handler;\n    _context.missing = _runtimeMissing;\n  }\n  function isResolvedTranslateMessage(type, arg // eslint-disable-line @typescript-eslint/no-explicit-any\n  ) {\n    return type !== 'translate' || !arg.resolvedMessage;\n  }\n  const wrapWithDeps = (fn, argumentParser, warnType, fallbackSuccess, fallbackFail, successCondition) => {\n    trackReactivityValues(); // track reactive dependency\n    // NOTE: experimental !!\n    let ret;\n    try {\n      if (process.env.NODE_ENV !== 'production' || __INTLIFY_PROD_DEVTOOLS__) {\n        setAdditionalMeta(getMetaInfo());\n      }\n      if (!_isGlobal) {\n        _context.fallbackContext = __root ? getFallbackContext() : undefined;\n      }\n      ret = fn(_context);\n    } finally {\n      if (process.env.NODE_ENV !== 'production' || __INTLIFY_PROD_DEVTOOLS__) {\n        setAdditionalMeta(null);\n      }\n      if (!_isGlobal) {\n        _context.fallbackContext = undefined;\n      }\n    }\n    if (warnType !== 'translate exists' &&\n    // for not `te` (e.g `t`)\n    isNumber(ret) && ret === NOT_REOSLVED || warnType === 'translate exists' && !ret // for `te`\n    ) {\n      const [key, arg2] = argumentParser();\n      if (process.env.NODE_ENV !== 'production' && __root && isString(key) && isResolvedTranslateMessage(warnType, arg2)) {\n        if (_fallbackRoot && (isTranslateFallbackWarn(_fallbackWarn, key) || isTranslateMissingWarn(_missingWarn, key))) {\n          warn(getWarnMessage(I18nWarnCodes.FALLBACK_TO_ROOT, {\n            key,\n            type: warnType\n          }));\n        }\n        // for vue-devtools timeline event\n        if (process.env.NODE_ENV !== 'production') {\n          const {\n            __v_emitter: emitter\n          } = _context;\n          if (emitter && _fallbackRoot) {\n            emitter.emit(\"fallback\" /* VueDevToolsTimelineEvents.FALBACK */, {\n              type: warnType,\n              key,\n              to: 'global',\n              groupId: `${warnType}:${key}`\n            });\n          }\n        }\n      }\n      return __root && _fallbackRoot ? fallbackSuccess(__root) : fallbackFail(key);\n    } else if (successCondition(ret)) {\n      return ret;\n    } else {\n      /* istanbul ignore next */\n      throw createI18nError(I18nErrorCodes.UNEXPECTED_RETURN_TYPE);\n    }\n  };\n  // t\n  function t(...args) {\n    return wrapWithDeps(context => Reflect.apply(translate, null, [context, ...args]), () => parseTranslateArgs(...args), 'translate', root => Reflect.apply(root.t, root, [...args]), key => key, val => isString(val));\n  }\n  // rt\n  function rt(...args) {\n    const [arg1, arg2, arg3] = args;\n    if (arg3 && !isObject(arg3)) {\n      throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);\n    }\n    return t(...[arg1, arg2, assign({\n      resolvedMessage: true\n    }, arg3 || {})]);\n  }\n  // d\n  function d(...args) {\n    return wrapWithDeps(context => Reflect.apply(datetime, null, [context, ...args]), () => parseDateTimeArgs(...args), 'datetime format', root => Reflect.apply(root.d, root, [...args]), () => MISSING_RESOLVE_VALUE, val => isString(val));\n  }\n  // n\n  function n(...args) {\n    return wrapWithDeps(context => Reflect.apply(number, null, [context, ...args]), () => parseNumberArgs(...args), 'number format', root => Reflect.apply(root.n, root, [...args]), () => MISSING_RESOLVE_VALUE, val => isString(val));\n  }\n  // for custom processor\n  function normalize(values) {\n    return values.map(val => isString(val) || isNumber(val) || isBoolean(val) ? createTextNode(String(val)) : val);\n  }\n  const interpolate = val => val;\n  const processor = {\n    normalize,\n    interpolate,\n    type: 'vnode'\n  };\n  // translateVNode, using for `i18n-t` component\n  function translateVNode(...args) {\n    return wrapWithDeps(context => {\n      let ret;\n      const _context = context;\n      try {\n        _context.processor = processor;\n        ret = Reflect.apply(translate, null, [_context, ...args]);\n      } finally {\n        _context.processor = null;\n      }\n      return ret;\n    }, () => parseTranslateArgs(...args), 'translate',\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    root => root[TranslateVNodeSymbol](...args), key => [createTextNode(key)], val => isArray(val));\n  }\n  // numberParts, using for `i18n-n` component\n  function numberParts(...args) {\n    return wrapWithDeps(context => Reflect.apply(number, null, [context, ...args]), () => parseNumberArgs(...args), 'number format',\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    root => root[NumberPartsSymbol](...args), NOOP_RETURN_ARRAY, val => isString(val) || isArray(val));\n  }\n  // datetimeParts, using for `i18n-d` component\n  function datetimeParts(...args) {\n    return wrapWithDeps(context => Reflect.apply(datetime, null, [context, ...args]), () => parseDateTimeArgs(...args), 'datetime format',\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    root => root[DatetimePartsSymbol](...args), NOOP_RETURN_ARRAY, val => isString(val) || isArray(val));\n  }\n  function setPluralRules(rules) {\n    _pluralRules = rules;\n    _context.pluralRules = _pluralRules;\n  }\n  // te\n  function te(key, locale) {\n    return wrapWithDeps(() => {\n      if (!key) {\n        return false;\n      }\n      const targetLocale = isString(locale) ? locale : _locale.value;\n      const message = getLocaleMessage(targetLocale);\n      const resolved = _context.messageResolver(message, key);\n      return !translateExistCompatible ? isMessageAST(resolved) || isMessageFunction(resolved) || isString(resolved) : resolved != null;\n    }, () => [key], 'translate exists', root => {\n      return Reflect.apply(root.te, root, [key, locale]);\n    }, NOOP_RETURN_FALSE, val => isBoolean(val));\n  }\n  function resolveMessages(key) {\n    let messages = null;\n    const locales = fallbackWithLocaleChain(_context, _fallbackLocale.value, _locale.value);\n    for (let i = 0; i < locales.length; i++) {\n      const targetLocaleMessages = _messages.value[locales[i]] || {};\n      const messageValue = _context.messageResolver(targetLocaleMessages, key);\n      if (messageValue != null) {\n        messages = messageValue;\n        break;\n      }\n    }\n    return messages;\n  }\n  // tm\n  function tm(key) {\n    const messages = resolveMessages(key);\n    // prettier-ignore\n    return messages != null ? messages : __root ? __root.tm(key) || {} : {};\n  }\n  // getLocaleMessage\n  function getLocaleMessage(locale) {\n    return _messages.value[locale] || {};\n  }\n  // setLocaleMessage\n  function setLocaleMessage(locale, message) {\n    if (flatJson) {\n      const _message = {\n        [locale]: message\n      };\n      for (const key in _message) {\n        if (hasOwn(_message, key)) {\n          handleFlatJson(_message[key]);\n        }\n      }\n      message = _message[locale];\n    }\n    _messages.value[locale] = message;\n    _context.messages = _messages.value;\n  }\n  // mergeLocaleMessage\n  function mergeLocaleMessage(locale, message) {\n    _messages.value[locale] = _messages.value[locale] || {};\n    const _message = {\n      [locale]: message\n    };\n    if (flatJson) {\n      for (const key in _message) {\n        if (hasOwn(_message, key)) {\n          handleFlatJson(_message[key]);\n        }\n      }\n    }\n    message = _message[locale];\n    deepCopy(message, _messages.value[locale]);\n    _context.messages = _messages.value;\n  }\n  // getDateTimeFormat\n  function getDateTimeFormat(locale) {\n    return _datetimeFormats.value[locale] || {};\n  }\n  // setDateTimeFormat\n  function setDateTimeFormat(locale, format) {\n    _datetimeFormats.value[locale] = format;\n    _context.datetimeFormats = _datetimeFormats.value;\n    clearDateTimeFormat(_context, locale, format);\n  }\n  // mergeDateTimeFormat\n  function mergeDateTimeFormat(locale, format) {\n    _datetimeFormats.value[locale] = assign(_datetimeFormats.value[locale] || {}, format);\n    _context.datetimeFormats = _datetimeFormats.value;\n    clearDateTimeFormat(_context, locale, format);\n  }\n  // getNumberFormat\n  function getNumberFormat(locale) {\n    return _numberFormats.value[locale] || {};\n  }\n  // setNumberFormat\n  function setNumberFormat(locale, format) {\n    _numberFormats.value[locale] = format;\n    _context.numberFormats = _numberFormats.value;\n    clearNumberFormat(_context, locale, format);\n  }\n  // mergeNumberFormat\n  function mergeNumberFormat(locale, format) {\n    _numberFormats.value[locale] = assign(_numberFormats.value[locale] || {}, format);\n    _context.numberFormats = _numberFormats.value;\n    clearNumberFormat(_context, locale, format);\n  }\n  // for debug\n  composerID++;\n  // watch root locale & fallbackLocale\n  if (__root && inBrowser) {\n    watch(__root.locale, val => {\n      if (_inheritLocale) {\n        _locale.value = val;\n        _context.locale = val;\n        updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\n      }\n    });\n    watch(__root.fallbackLocale, val => {\n      if (_inheritLocale) {\n        _fallbackLocale.value = val;\n        _context.fallbackLocale = val;\n        updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\n      }\n    });\n  }\n  // define basic composition API!\n  const composer = {\n    id: composerID,\n    locale,\n    fallbackLocale,\n    get inheritLocale() {\n      return _inheritLocale;\n    },\n    set inheritLocale(val) {\n      _inheritLocale = val;\n      if (val && __root) {\n        _locale.value = __root.locale.value;\n        _fallbackLocale.value = __root.fallbackLocale.value;\n        updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\n      }\n    },\n    get availableLocales() {\n      return Object.keys(_messages.value).sort();\n    },\n    messages,\n    get modifiers() {\n      return _modifiers;\n    },\n    get pluralRules() {\n      return _pluralRules || {};\n    },\n    get isGlobal() {\n      return _isGlobal;\n    },\n    get missingWarn() {\n      return _missingWarn;\n    },\n    set missingWarn(val) {\n      _missingWarn = val;\n      _context.missingWarn = _missingWarn;\n    },\n    get fallbackWarn() {\n      return _fallbackWarn;\n    },\n    set fallbackWarn(val) {\n      _fallbackWarn = val;\n      _context.fallbackWarn = _fallbackWarn;\n    },\n    get fallbackRoot() {\n      return _fallbackRoot;\n    },\n    set fallbackRoot(val) {\n      _fallbackRoot = val;\n    },\n    get fallbackFormat() {\n      return _fallbackFormat;\n    },\n    set fallbackFormat(val) {\n      _fallbackFormat = val;\n      _context.fallbackFormat = _fallbackFormat;\n    },\n    get warnHtmlMessage() {\n      return _warnHtmlMessage;\n    },\n    set warnHtmlMessage(val) {\n      _warnHtmlMessage = val;\n      _context.warnHtmlMessage = val;\n    },\n    get escapeParameter() {\n      return _escapeParameter;\n    },\n    set escapeParameter(val) {\n      _escapeParameter = val;\n      _context.escapeParameter = val;\n    },\n    t,\n    getLocaleMessage,\n    setLocaleMessage,\n    mergeLocaleMessage,\n    getPostTranslationHandler,\n    setPostTranslationHandler,\n    getMissingHandler,\n    setMissingHandler,\n    [SetPluralRulesSymbol]: setPluralRules\n  };\n  {\n    composer.datetimeFormats = datetimeFormats;\n    composer.numberFormats = numberFormats;\n    composer.rt = rt;\n    composer.te = te;\n    composer.tm = tm;\n    composer.d = d;\n    composer.n = n;\n    composer.getDateTimeFormat = getDateTimeFormat;\n    composer.setDateTimeFormat = setDateTimeFormat;\n    composer.mergeDateTimeFormat = mergeDateTimeFormat;\n    composer.getNumberFormat = getNumberFormat;\n    composer.setNumberFormat = setNumberFormat;\n    composer.mergeNumberFormat = mergeNumberFormat;\n    composer[InejctWithOptionSymbol] = __injectWithOption;\n    composer[TranslateVNodeSymbol] = translateVNode;\n    composer[DatetimePartsSymbol] = datetimeParts;\n    composer[NumberPartsSymbol] = numberParts;\n  }\n  // for vue-devtools timeline event\n  if (process.env.NODE_ENV !== 'production') {\n    composer[EnableEmitter] = emitter => {\n      _context.__v_emitter = emitter;\n    };\n    composer[DisableEmitter] = () => {\n      _context.__v_emitter = undefined;\n    };\n  }\n  return composer;\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Convert to I18n Composer Options from VueI18n Options\n *\n * @internal\n */\nfunction convertComposerOptions(options) {\n  const locale = isString(options.locale) ? options.locale : DEFAULT_LOCALE;\n  const fallbackLocale = isString(options.fallbackLocale) || isArray(options.fallbackLocale) || isPlainObject(options.fallbackLocale) || options.fallbackLocale === false ? options.fallbackLocale : locale;\n  const missing = isFunction(options.missing) ? options.missing : undefined;\n  const missingWarn = isBoolean(options.silentTranslationWarn) || isRegExp(options.silentTranslationWarn) ? !options.silentTranslationWarn : true;\n  const fallbackWarn = isBoolean(options.silentFallbackWarn) || isRegExp(options.silentFallbackWarn) ? !options.silentFallbackWarn : true;\n  const fallbackRoot = isBoolean(options.fallbackRoot) ? options.fallbackRoot : true;\n  const fallbackFormat = !!options.formatFallbackMessages;\n  const modifiers = isPlainObject(options.modifiers) ? options.modifiers : {};\n  const pluralizationRules = options.pluralizationRules;\n  const postTranslation = isFunction(options.postTranslation) ? options.postTranslation : undefined;\n  const warnHtmlMessage = isString(options.warnHtmlInMessage) ? options.warnHtmlInMessage !== 'off' : true;\n  const escapeParameter = !!options.escapeParameterHtml;\n  const inheritLocale = isBoolean(options.sync) ? options.sync : true;\n  if (process.env.NODE_ENV !== 'production' && options.formatter) {\n    warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));\n  }\n  if (process.env.NODE_ENV !== 'production' && options.preserveDirectiveContent) {\n    warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));\n  }\n  let messages = options.messages;\n  if (isPlainObject(options.sharedMessages)) {\n    const sharedMessages = options.sharedMessages;\n    const locales = Object.keys(sharedMessages);\n    messages = locales.reduce((messages, locale) => {\n      const message = messages[locale] || (messages[locale] = {});\n      assign(message, sharedMessages[locale]);\n      return messages;\n    }, messages || {});\n  }\n  const {\n    __i18n,\n    __root,\n    __injectWithOption\n  } = options;\n  const datetimeFormats = options.datetimeFormats;\n  const numberFormats = options.numberFormats;\n  const flatJson = options.flatJson;\n  const translateExistCompatible = options.translateExistCompatible;\n  return {\n    locale,\n    fallbackLocale,\n    messages,\n    flatJson,\n    datetimeFormats,\n    numberFormats,\n    missing,\n    missingWarn,\n    fallbackWarn,\n    fallbackRoot,\n    fallbackFormat,\n    modifiers,\n    pluralRules: pluralizationRules,\n    postTranslation,\n    warnHtmlMessage,\n    escapeParameter,\n    messageResolver: options.messageResolver,\n    inheritLocale,\n    translateExistCompatible,\n    __i18n,\n    __root,\n    __injectWithOption\n  };\n}\n/**\n * create VueI18n interface factory\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nfunction createVueI18n(options = {}, VueI18nLegacy) {\n  {\n    const composer = createComposer(convertComposerOptions(options));\n    const {\n      __extender\n    } = options;\n    // defines VueI18n\n    const vueI18n = {\n      // id\n      id: composer.id,\n      // locale\n      get locale() {\n        return composer.locale.value;\n      },\n      set locale(val) {\n        composer.locale.value = val;\n      },\n      // fallbackLocale\n      get fallbackLocale() {\n        return composer.fallbackLocale.value;\n      },\n      set fallbackLocale(val) {\n        composer.fallbackLocale.value = val;\n      },\n      // messages\n      get messages() {\n        return composer.messages.value;\n      },\n      // datetimeFormats\n      get datetimeFormats() {\n        return composer.datetimeFormats.value;\n      },\n      // numberFormats\n      get numberFormats() {\n        return composer.numberFormats.value;\n      },\n      // availableLocales\n      get availableLocales() {\n        return composer.availableLocales;\n      },\n      // formatter\n      get formatter() {\n        process.env.NODE_ENV !== 'production' && warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));\n        // dummy\n        return {\n          interpolate() {\n            return [];\n          }\n        };\n      },\n      set formatter(val) {\n        process.env.NODE_ENV !== 'production' && warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));\n      },\n      // missing\n      get missing() {\n        return composer.getMissingHandler();\n      },\n      set missing(handler) {\n        composer.setMissingHandler(handler);\n      },\n      // silentTranslationWarn\n      get silentTranslationWarn() {\n        return isBoolean(composer.missingWarn) ? !composer.missingWarn : composer.missingWarn;\n      },\n      set silentTranslationWarn(val) {\n        composer.missingWarn = isBoolean(val) ? !val : val;\n      },\n      // silentFallbackWarn\n      get silentFallbackWarn() {\n        return isBoolean(composer.fallbackWarn) ? !composer.fallbackWarn : composer.fallbackWarn;\n      },\n      set silentFallbackWarn(val) {\n        composer.fallbackWarn = isBoolean(val) ? !val : val;\n      },\n      // modifiers\n      get modifiers() {\n        return composer.modifiers;\n      },\n      // formatFallbackMessages\n      get formatFallbackMessages() {\n        return composer.fallbackFormat;\n      },\n      set formatFallbackMessages(val) {\n        composer.fallbackFormat = val;\n      },\n      // postTranslation\n      get postTranslation() {\n        return composer.getPostTranslationHandler();\n      },\n      set postTranslation(handler) {\n        composer.setPostTranslationHandler(handler);\n      },\n      // sync\n      get sync() {\n        return composer.inheritLocale;\n      },\n      set sync(val) {\n        composer.inheritLocale = val;\n      },\n      // warnInHtmlMessage\n      get warnHtmlInMessage() {\n        return composer.warnHtmlMessage ? 'warn' : 'off';\n      },\n      set warnHtmlInMessage(val) {\n        composer.warnHtmlMessage = val !== 'off';\n      },\n      // escapeParameterHtml\n      get escapeParameterHtml() {\n        return composer.escapeParameter;\n      },\n      set escapeParameterHtml(val) {\n        composer.escapeParameter = val;\n      },\n      // preserveDirectiveContent\n      get preserveDirectiveContent() {\n        process.env.NODE_ENV !== 'production' && warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));\n        return true;\n      },\n      set preserveDirectiveContent(val) {\n        process.env.NODE_ENV !== 'production' && warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));\n      },\n      // pluralizationRules\n      get pluralizationRules() {\n        return composer.pluralRules || {};\n      },\n      // for internal\n      __composer: composer,\n      // t\n      t(...args) {\n        const [arg1, arg2, arg3] = args;\n        const options = {};\n        let list = null;\n        let named = null;\n        if (!isString(arg1)) {\n          throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);\n        }\n        const key = arg1;\n        if (isString(arg2)) {\n          options.locale = arg2;\n        } else if (isArray(arg2)) {\n          list = arg2;\n        } else if (isPlainObject(arg2)) {\n          named = arg2;\n        }\n        if (isArray(arg3)) {\n          list = arg3;\n        } else if (isPlainObject(arg3)) {\n          named = arg3;\n        }\n        // return composer.t(key, (list || named || {}) as any, options)\n        return Reflect.apply(composer.t, composer, [key, list || named || {}, options]);\n      },\n      rt(...args) {\n        return Reflect.apply(composer.rt, composer, [...args]);\n      },\n      // tc\n      tc(...args) {\n        const [arg1, arg2, arg3] = args;\n        const options = {\n          plural: 1\n        };\n        let list = null;\n        let named = null;\n        if (!isString(arg1)) {\n          throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);\n        }\n        const key = arg1;\n        if (isString(arg2)) {\n          options.locale = arg2;\n        } else if (isNumber(arg2)) {\n          options.plural = arg2;\n        } else if (isArray(arg2)) {\n          list = arg2;\n        } else if (isPlainObject(arg2)) {\n          named = arg2;\n        }\n        if (isString(arg3)) {\n          options.locale = arg3;\n        } else if (isArray(arg3)) {\n          list = arg3;\n        } else if (isPlainObject(arg3)) {\n          named = arg3;\n        }\n        // return composer.t(key, (list || named || {}) as any, options)\n        return Reflect.apply(composer.t, composer, [key, list || named || {}, options]);\n      },\n      // te\n      te(key, locale) {\n        return composer.te(key, locale);\n      },\n      // tm\n      tm(key) {\n        return composer.tm(key);\n      },\n      // getLocaleMessage\n      getLocaleMessage(locale) {\n        return composer.getLocaleMessage(locale);\n      },\n      // setLocaleMessage\n      setLocaleMessage(locale, message) {\n        composer.setLocaleMessage(locale, message);\n      },\n      // mergeLocaleMessage\n      mergeLocaleMessage(locale, message) {\n        composer.mergeLocaleMessage(locale, message);\n      },\n      // d\n      d(...args) {\n        return Reflect.apply(composer.d, composer, [...args]);\n      },\n      // getDateTimeFormat\n      getDateTimeFormat(locale) {\n        return composer.getDateTimeFormat(locale);\n      },\n      // setDateTimeFormat\n      setDateTimeFormat(locale, format) {\n        composer.setDateTimeFormat(locale, format);\n      },\n      // mergeDateTimeFormat\n      mergeDateTimeFormat(locale, format) {\n        composer.mergeDateTimeFormat(locale, format);\n      },\n      // n\n      n(...args) {\n        return Reflect.apply(composer.n, composer, [...args]);\n      },\n      // getNumberFormat\n      getNumberFormat(locale) {\n        return composer.getNumberFormat(locale);\n      },\n      // setNumberFormat\n      setNumberFormat(locale, format) {\n        composer.setNumberFormat(locale, format);\n      },\n      // mergeNumberFormat\n      mergeNumberFormat(locale, format) {\n        composer.mergeNumberFormat(locale, format);\n      },\n      // getChoiceIndex\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      getChoiceIndex(choice, choicesLength) {\n        process.env.NODE_ENV !== 'production' && warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_GET_CHOICE_INDEX));\n        return -1;\n      }\n    };\n    vueI18n.__extender = __extender;\n    // for vue-devtools timeline event\n    if (process.env.NODE_ENV !== 'production') {\n      vueI18n.__enableEmitter = emitter => {\n        const __composer = composer;\n        __composer[EnableEmitter] && __composer[EnableEmitter](emitter);\n      };\n      vueI18n.__disableEmitter = () => {\n        const __composer = composer;\n        __composer[DisableEmitter] && __composer[DisableEmitter]();\n      };\n    }\n    return vueI18n;\n  }\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\nconst baseFormatProps = {\n  tag: {\n    type: [String, Object]\n  },\n  locale: {\n    type: String\n  },\n  scope: {\n    type: String,\n    // NOTE: avoid https://github.com/microsoft/rushstack/issues/1050\n    validator: (val /* ComponentI18nScope */) => val === 'parent' || val === 'global',\n    default: 'parent' /* ComponentI18nScope */\n  },\n  i18n: {\n    type: Object\n  }\n};\nfunction getInterpolateArg(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n{\n  slots\n},\n// SetupContext,\nkeys) {\n  if (keys.length === 1 && keys[0] === 'default') {\n    // default slot with list\n    const ret = slots.default ? slots.default() : [];\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return ret.reduce((slot, current) => {\n      return [...slot,\n      // prettier-ignore\n      ...(current.type === Fragment ? current.children : [current])];\n    }, []);\n  } else {\n    // named slots\n    return keys.reduce((arg, key) => {\n      const slot = slots[key];\n      if (slot) {\n        arg[key] = slot();\n      }\n      return arg;\n    }, create());\n  }\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getFragmentableTag(tag) {\n  return Fragment;\n}\nconst TranslationImpl = /*#__PURE__*/defineComponent({\n  /* eslint-disable */\n  name: 'i18n-t',\n  props: assign({\n    keypath: {\n      type: String,\n      required: true\n    },\n    plural: {\n      type: [Number, String],\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      validator: val => isNumber(val) || !isNaN(val)\n    }\n  }, baseFormatProps),\n  /* eslint-enable */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  setup(props, context) {\n    const {\n      slots,\n      attrs\n    } = context;\n    // NOTE: avoid https://github.com/microsoft/rushstack/issues/1050\n    const i18n = props.i18n || useI18n({\n      useScope: props.scope,\n      __useComponent: true\n    });\n    return () => {\n      const keys = Object.keys(slots).filter(key => key !== '_');\n      const options = create();\n      if (props.locale) {\n        options.locale = props.locale;\n      }\n      if (props.plural !== undefined) {\n        options.plural = isString(props.plural) ? +props.plural : props.plural;\n      }\n      const arg = getInterpolateArg(context, keys);\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const children = i18n[TranslateVNodeSymbol](props.keypath, arg, options);\n      const assignedAttrs = assign(create(), attrs);\n      const tag = isString(props.tag) || isObject(props.tag) ? props.tag : getFragmentableTag();\n      return h(tag, assignedAttrs, children);\n    };\n  }\n});\n/**\n * export the public type for h/tsx inference\n * also to avoid inline import() in generated d.ts files\n */\n/**\n * Translation Component\n *\n * @remarks\n * See the following items for property about details\n *\n * @VueI18nSee [TranslationProps](component#translationprops)\n * @VueI18nSee [BaseFormatProps](component#baseformatprops)\n * @VueI18nSee [Component Interpolation](../guide/advanced/component)\n *\n * @example\n * ```html\n * <div id=\"app\">\n *   <!-- ... -->\n *   <i18n keypath=\"term\" tag=\"label\" for=\"tos\">\n *     <a :href=\"url\" target=\"_blank\">{{ $t('tos') }}</a>\n *   </i18n>\n *   <!-- ... -->\n * </div>\n * ```\n * ```js\n * import { createApp } from 'vue'\n * import { createI18n } from 'vue-i18n'\n *\n * const messages = {\n *   en: {\n *     tos: 'Term of Service',\n *     term: 'I accept xxx {0}.'\n *   },\n *   ja: {\n *     tos: '利用規約',\n *     term: '私は xxx の{0}に同意します。'\n *   }\n * }\n *\n * const i18n = createI18n({\n *   locale: 'en',\n *   messages\n * })\n *\n * const app = createApp({\n *   data: {\n *     url: '/term'\n *   }\n * }).use(i18n).mount('#app')\n * ```\n *\n * @VueI18nComponent\n */\nconst Translation = TranslationImpl;\nconst I18nT = Translation;\nfunction isVNode(target) {\n  return isArray(target) && !isString(target[0]);\n}\nfunction renderFormatter(props, context, slotKeys, partFormatter) {\n  const {\n    slots,\n    attrs\n  } = context;\n  return () => {\n    const options = {\n      part: true\n    };\n    let overrides = create();\n    if (props.locale) {\n      options.locale = props.locale;\n    }\n    if (isString(props.format)) {\n      options.key = props.format;\n    } else if (isObject(props.format)) {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      if (isString(props.format.key)) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        options.key = props.format.key;\n      }\n      // Filter out number format options only\n      overrides = Object.keys(props.format).reduce((options, prop) => {\n        return slotKeys.includes(prop) ? assign(create(), options, {\n          [prop]: props.format[prop]\n        }) // eslint-disable-line @typescript-eslint/no-explicit-any\n        : options;\n      }, create());\n    }\n    const parts = partFormatter(...[props.value, options, overrides]);\n    let children = [options.key];\n    if (isArray(parts)) {\n      children = parts.map((part, index) => {\n        const slot = slots[part.type];\n        const node = slot ? slot({\n          [part.type]: part.value,\n          index,\n          parts\n        }) : [part.value];\n        if (isVNode(node)) {\n          node[0].key = `${part.type}-${index}`;\n        }\n        return node;\n      });\n    } else if (isString(parts)) {\n      children = [parts];\n    }\n    const assignedAttrs = assign(create(), attrs);\n    const tag = isString(props.tag) || isObject(props.tag) ? props.tag : getFragmentableTag();\n    return h(tag, assignedAttrs, children);\n  };\n}\nconst NumberFormatImpl = /*#__PURE__*/defineComponent({\n  /* eslint-disable */\n  name: 'i18n-n',\n  props: assign({\n    value: {\n      type: Number,\n      required: true\n    },\n    format: {\n      type: [String, Object]\n    }\n  }, baseFormatProps),\n  /* eslint-enable */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  setup(props, context) {\n    const i18n = props.i18n || useI18n({\n      useScope: props.scope,\n      __useComponent: true\n    });\n    return renderFormatter(props, context, NUMBER_FORMAT_OPTIONS_KEYS, (...args) =>\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    i18n[NumberPartsSymbol](...args));\n  }\n});\n/**\n * export the public type for h/tsx inference\n * also to avoid inline import() in generated d.ts files\n */\n/**\n * Number Format Component\n *\n * @remarks\n * See the following items for property about details\n *\n * @VueI18nSee [FormattableProps](component#formattableprops)\n * @VueI18nSee [BaseFormatProps](component#baseformatprops)\n * @VueI18nSee [Custom Formatting](../guide/essentials/number#custom-formatting)\n *\n * @VueI18nDanger\n * Not supported IE, due to no support `Intl.NumberFormat#formatToParts` in [IE](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/formatToParts)\n *\n * If you want to use it, you need to use [polyfill](https://github.com/formatjs/formatjs/tree/main/packages/intl-numberformat)\n *\n * @VueI18nComponent\n */\nconst NumberFormat = NumberFormatImpl;\nconst I18nN = NumberFormat;\nconst DatetimeFormatImpl = /* #__PURE__*/defineComponent({\n  /* eslint-disable */\n  name: 'i18n-d',\n  props: assign({\n    value: {\n      type: [Number, Date],\n      required: true\n    },\n    format: {\n      type: [String, Object]\n    }\n  }, baseFormatProps),\n  /* eslint-enable */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  setup(props, context) {\n    const i18n = props.i18n || useI18n({\n      useScope: props.scope,\n      __useComponent: true\n    });\n    return renderFormatter(props, context, DATETIME_FORMAT_OPTIONS_KEYS, (...args) =>\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    i18n[DatetimePartsSymbol](...args));\n  }\n});\n/**\n * Datetime Format Component\n *\n * @remarks\n * See the following items for property about details\n *\n * @VueI18nSee [FormattableProps](component#formattableprops)\n * @VueI18nSee [BaseFormatProps](component#baseformatprops)\n * @VueI18nSee [Custom Formatting](../guide/essentials/datetime#custom-formatting)\n *\n * @VueI18nDanger\n * Not supported IE, due to no support `Intl.DateTimeFormat#formatToParts` in [IE](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/formatToParts)\n *\n * If you want to use it, you need to use [polyfill](https://github.com/formatjs/formatjs/tree/main/packages/intl-datetimeformat)\n *\n * @VueI18nComponent\n */\nconst DatetimeFormat = DatetimeFormatImpl;\nconst I18nD = DatetimeFormat;\nfunction getComposer$2(i18n, instance) {\n  const i18nInternal = i18n;\n  if (i18n.mode === 'composition') {\n    return i18nInternal.__getInstance(instance) || i18n.global;\n  } else {\n    const vueI18n = i18nInternal.__getInstance(instance);\n    return vueI18n != null ? vueI18n.__composer : i18n.global.__composer;\n  }\n}\nfunction vTDirective(i18n) {\n  const _process = binding => {\n    const {\n      instance,\n      modifiers,\n      value\n    } = binding;\n    /* istanbul ignore if */\n    if (!instance || !instance.$) {\n      throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n    }\n    const composer = getComposer$2(i18n, instance.$);\n    if (process.env.NODE_ENV !== 'production' && modifiers.preserve) {\n      warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_PRESERVE));\n    }\n    const parsedValue = parseValue(value);\n    return [Reflect.apply(composer.t, composer, [...makeParams(parsedValue)]), composer];\n  };\n  const register = (el, binding) => {\n    const [textContent, composer] = _process(binding);\n    if (inBrowser && i18n.global === composer) {\n      // global scope only\n      el.__i18nWatcher = watch(composer.locale, () => {\n        binding.instance && binding.instance.$forceUpdate();\n      });\n    }\n    el.__composer = composer;\n    el.textContent = textContent;\n  };\n  const unregister = el => {\n    if (inBrowser && el.__i18nWatcher) {\n      el.__i18nWatcher();\n      el.__i18nWatcher = undefined;\n      delete el.__i18nWatcher;\n    }\n    if (el.__composer) {\n      el.__composer = undefined;\n      delete el.__composer;\n    }\n  };\n  const update = (el, {\n    value\n  }) => {\n    if (el.__composer) {\n      const composer = el.__composer;\n      const parsedValue = parseValue(value);\n      el.textContent = Reflect.apply(composer.t, composer, [...makeParams(parsedValue)]);\n    }\n  };\n  const getSSRProps = binding => {\n    const [textContent] = _process(binding);\n    return {\n      textContent\n    };\n  };\n  return {\n    created: register,\n    unmounted: unregister,\n    beforeUpdate: update,\n    getSSRProps\n  };\n}\nfunction parseValue(value) {\n  if (isString(value)) {\n    return {\n      path: value\n    };\n  } else if (isPlainObject(value)) {\n    if (!('path' in value)) {\n      throw createI18nError(I18nErrorCodes.REQUIRED_VALUE, 'path');\n    }\n    return value;\n  } else {\n    throw createI18nError(I18nErrorCodes.INVALID_VALUE);\n  }\n}\nfunction makeParams(value) {\n  const {\n    path,\n    locale,\n    args,\n    choice,\n    plural\n  } = value;\n  const options = {};\n  const named = args || {};\n  if (isString(locale)) {\n    options.locale = locale;\n  }\n  if (isNumber(choice)) {\n    options.plural = choice;\n  }\n  if (isNumber(plural)) {\n    options.plural = plural;\n  }\n  return [path, named, options];\n}\nfunction apply(app, i18n, ...options) {\n  const pluginOptions = isPlainObject(options[0]) ? options[0] : {};\n  const useI18nComponentName = !!pluginOptions.useI18nComponentName;\n  const globalInstall = isBoolean(pluginOptions.globalInstall) ? pluginOptions.globalInstall : true;\n  if (process.env.NODE_ENV !== 'production' && globalInstall && useI18nComponentName) {\n    warn(getWarnMessage(I18nWarnCodes.COMPONENT_NAME_LEGACY_COMPATIBLE, {\n      name: Translation.name\n    }));\n  }\n  if (globalInstall) {\n    [!useI18nComponentName ? Translation.name : 'i18n', 'I18nT'].forEach(name => app.component(name, Translation));\n    [NumberFormat.name, 'I18nN'].forEach(name => app.component(name, NumberFormat));\n    [DatetimeFormat.name, 'I18nD'].forEach(name => app.component(name, DatetimeFormat));\n  }\n  // install directive\n  {\n    app.directive('t', vTDirective(i18n));\n  }\n}\nconst VueDevToolsLabels = {\n  [\"vue-devtools-plugin-vue-i18n\" /* VueDevToolsIDs.PLUGIN */]: 'Vue I18n devtools',\n  [\"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */]: 'I18n Resources',\n  [\"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */]: 'Vue I18n'\n};\nconst VueDevToolsPlaceholders = {\n  [\"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */]: 'Search for scopes ...'\n};\nconst VueDevToolsTimelineColors = {\n  [\"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */]: 0xffcd19\n};\nconst VUE_I18N_COMPONENT_TYPES = 'vue-i18n: composer properties';\nlet devtoolsApi;\nasync function enableDevTools(app, i18n) {\n  return new Promise((resolve, reject) => {\n    try {\n      setupDevtoolsPlugin({\n        id: \"vue-devtools-plugin-vue-i18n\" /* VueDevToolsIDs.PLUGIN */,\n        label: VueDevToolsLabels[\"vue-devtools-plugin-vue-i18n\" /* VueDevToolsIDs.PLUGIN */],\n        packageName: 'vue-i18n',\n        homepage: 'https://vue-i18n.intlify.dev',\n        logo: 'https://vue-i18n.intlify.dev/vue-i18n-devtools-logo.png',\n        componentStateTypes: [VUE_I18N_COMPONENT_TYPES],\n        app: app // eslint-disable-line @typescript-eslint/no-explicit-any\n      }, api => {\n        devtoolsApi = api;\n        api.on.visitComponentTree(({\n          componentInstance,\n          treeNode\n        }) => {\n          updateComponentTreeTags(componentInstance, treeNode, i18n);\n        });\n        api.on.inspectComponent(({\n          componentInstance,\n          instanceData\n        }) => {\n          if (componentInstance.vnode.el && componentInstance.vnode.el.__VUE_I18N__ && instanceData) {\n            if (i18n.mode === 'legacy') {\n              // ignore global scope on legacy mode\n              if (componentInstance.vnode.el.__VUE_I18N__ !== i18n.global.__composer) {\n                inspectComposer(instanceData, componentInstance.vnode.el.__VUE_I18N__);\n              }\n            } else {\n              inspectComposer(instanceData, componentInstance.vnode.el.__VUE_I18N__);\n            }\n          }\n        });\n        api.addInspector({\n          id: \"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */,\n          label: VueDevToolsLabels[\"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */],\n          icon: 'language',\n          treeFilterPlaceholder: VueDevToolsPlaceholders[\"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */]\n        });\n        api.on.getInspectorTree(payload => {\n          if (payload.app === app && payload.inspectorId === \"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */) {\n            registerScope(payload, i18n);\n          }\n        });\n        const roots = new Map();\n        api.on.getInspectorState(async payload => {\n          if (payload.app === app && payload.inspectorId === \"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */) {\n            api.unhighlightElement();\n            inspectScope(payload, i18n);\n            if (payload.nodeId === 'global') {\n              if (!roots.has(payload.app)) {\n                const [root] = await api.getComponentInstances(payload.app);\n                roots.set(payload.app, root);\n              }\n              api.highlightElement(roots.get(payload.app));\n            } else {\n              const instance = getComponentInstance(payload.nodeId, i18n);\n              instance && api.highlightElement(instance);\n            }\n          }\n        });\n        api.on.editInspectorState(payload => {\n          if (payload.app === app && payload.inspectorId === \"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */) {\n            editScope(payload, i18n);\n          }\n        });\n        api.addTimelineLayer({\n          id: \"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */,\n          label: VueDevToolsLabels[\"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */],\n          color: VueDevToolsTimelineColors[\"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */]\n        });\n        resolve(true);\n      });\n    } catch (e) {\n      console.error(e);\n      reject(false);\n    }\n  });\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getI18nScopeLable(instance) {\n  return instance.type.name || instance.type.displayName || instance.type.__file || 'Anonymous';\n}\nfunction updateComponentTreeTags(instance,\n// eslint-disable-line @typescript-eslint/no-explicit-any\ntreeNode, i18n) {\n  // prettier-ignore\n  const global = i18n.mode === 'composition' ? i18n.global : i18n.global.__composer;\n  if (instance && instance.vnode.el && instance.vnode.el.__VUE_I18N__) {\n    // add custom tags local scope only\n    if (instance.vnode.el.__VUE_I18N__ !== global) {\n      const tag = {\n        label: `i18n (${getI18nScopeLable(instance)} Scope)`,\n        textColor: 0x000000,\n        backgroundColor: 0xffcd19\n      };\n      treeNode.tags.push(tag);\n    }\n  }\n}\nfunction inspectComposer(instanceData, composer) {\n  const type = VUE_I18N_COMPONENT_TYPES;\n  instanceData.state.push({\n    type,\n    key: 'locale',\n    editable: true,\n    value: composer.locale.value\n  });\n  instanceData.state.push({\n    type,\n    key: 'availableLocales',\n    editable: false,\n    value: composer.availableLocales\n  });\n  instanceData.state.push({\n    type,\n    key: 'fallbackLocale',\n    editable: true,\n    value: composer.fallbackLocale.value\n  });\n  instanceData.state.push({\n    type,\n    key: 'inheritLocale',\n    editable: true,\n    value: composer.inheritLocale\n  });\n  instanceData.state.push({\n    type,\n    key: 'messages',\n    editable: false,\n    value: getLocaleMessageValue(composer.messages.value)\n  });\n  {\n    instanceData.state.push({\n      type,\n      key: 'datetimeFormats',\n      editable: false,\n      value: composer.datetimeFormats.value\n    });\n    instanceData.state.push({\n      type,\n      key: 'numberFormats',\n      editable: false,\n      value: composer.numberFormats.value\n    });\n  }\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getLocaleMessageValue(messages) {\n  const value = {};\n  Object.keys(messages).forEach(key => {\n    const v = messages[key];\n    if (isFunction(v) && 'source' in v) {\n      value[key] = getMessageFunctionDetails(v);\n    } else if (isMessageAST(v) && v.loc && v.loc.source) {\n      value[key] = v.loc.source;\n    } else if (isObject(v)) {\n      value[key] = getLocaleMessageValue(v);\n    } else {\n      value[key] = v;\n    }\n  });\n  return value;\n}\nconst ESC = {\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  '&': '&amp;'\n};\nfunction escape(s) {\n  return s.replace(/[<>\"&]/g, escapeChar);\n}\nfunction escapeChar(a) {\n  return ESC[a] || a;\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getMessageFunctionDetails(func) {\n  const argString = func.source ? `(\"${escape(func.source)}\")` : `(?)`;\n  return {\n    _custom: {\n      type: 'function',\n      display: `<span>ƒ</span> ${argString}`\n    }\n  };\n}\nfunction registerScope(payload, i18n) {\n  payload.rootNodes.push({\n    id: 'global',\n    label: 'Global Scope'\n  });\n  // prettier-ignore\n  const global = i18n.mode === 'composition' ? i18n.global : i18n.global.__composer;\n  for (const [keyInstance, instance] of i18n.__instances) {\n    // prettier-ignore\n    const composer = i18n.mode === 'composition' ? instance : instance.__composer;\n    if (global === composer) {\n      continue;\n    }\n    payload.rootNodes.push({\n      id: composer.id.toString(),\n      label: `${getI18nScopeLable(keyInstance)} Scope`\n    });\n  }\n}\nfunction getComponentInstance(nodeId, i18n) {\n  let instance = null;\n  if (nodeId !== 'global') {\n    for (const [component, composer] of i18n.__instances.entries()) {\n      if (composer.id.toString() === nodeId) {\n        instance = component;\n        break;\n      }\n    }\n  }\n  return instance;\n}\nfunction getComposer$1(nodeId, i18n) {\n  if (nodeId === 'global') {\n    return i18n.mode === 'composition' ? i18n.global : i18n.global.__composer;\n  } else {\n    const instance = Array.from(i18n.__instances.values()).find(item => item.id.toString() === nodeId);\n    if (instance) {\n      return i18n.mode === 'composition' ? instance : instance.__composer;\n    } else {\n      return null;\n    }\n  }\n}\nfunction inspectScope(payload, i18n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n) {\n  const composer = getComposer$1(payload.nodeId, i18n);\n  if (composer) {\n    // TODO:\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    payload.state = makeScopeInspectState(composer);\n  }\n  return null;\n}\nfunction makeScopeInspectState(composer) {\n  const state = {};\n  const localeType = 'Locale related info';\n  const localeStates = [{\n    type: localeType,\n    key: 'locale',\n    editable: true,\n    value: composer.locale.value\n  }, {\n    type: localeType,\n    key: 'fallbackLocale',\n    editable: true,\n    value: composer.fallbackLocale.value\n  }, {\n    type: localeType,\n    key: 'availableLocales',\n    editable: false,\n    value: composer.availableLocales\n  }, {\n    type: localeType,\n    key: 'inheritLocale',\n    editable: true,\n    value: composer.inheritLocale\n  }];\n  state[localeType] = localeStates;\n  const localeMessagesType = 'Locale messages info';\n  const localeMessagesStates = [{\n    type: localeMessagesType,\n    key: 'messages',\n    editable: false,\n    value: getLocaleMessageValue(composer.messages.value)\n  }];\n  state[localeMessagesType] = localeMessagesStates;\n  {\n    const datetimeFormatsType = 'Datetime formats info';\n    const datetimeFormatsStates = [{\n      type: datetimeFormatsType,\n      key: 'datetimeFormats',\n      editable: false,\n      value: composer.datetimeFormats.value\n    }];\n    state[datetimeFormatsType] = datetimeFormatsStates;\n    const numberFormatsType = 'Datetime formats info';\n    const numberFormatsStates = [{\n      type: numberFormatsType,\n      key: 'numberFormats',\n      editable: false,\n      value: composer.numberFormats.value\n    }];\n    state[numberFormatsType] = numberFormatsStates;\n  }\n  return state;\n}\nfunction addTimelineEvent(event, payload) {\n  if (devtoolsApi) {\n    let groupId;\n    if (payload && 'groupId' in payload) {\n      groupId = payload.groupId;\n      delete payload.groupId;\n    }\n    devtoolsApi.addTimelineEvent({\n      layerId: \"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */,\n      event: {\n        title: event,\n        groupId,\n        time: Date.now(),\n        meta: {},\n        data: payload || {},\n        logType: event === \"compile-error\" /* VueDevToolsTimelineEvents.COMPILE_ERROR */ ? 'error' : event === \"fallback\" /* VueDevToolsTimelineEvents.FALBACK */ || event === \"missing\" /* VueDevToolsTimelineEvents.MISSING */ ? 'warning' : 'default'\n      }\n    });\n  }\n}\nfunction editScope(payload, i18n) {\n  const composer = getComposer$1(payload.nodeId, i18n);\n  if (composer) {\n    const [field] = payload.path;\n    if (field === 'locale' && isString(payload.state.value)) {\n      composer.locale.value = payload.state.value;\n    } else if (field === 'fallbackLocale' && (isString(payload.state.value) || isArray(payload.state.value) || isObject(payload.state.value))) {\n      composer.fallbackLocale.value = payload.state.value;\n    } else if (field === 'inheritLocale' && isBoolean(payload.state.value)) {\n      composer.inheritLocale = payload.state.value;\n    }\n  }\n}\n\n/**\n * Supports compatibility for legacy vue-i18n APIs\n * This mixin is used when we use vue-i18n@v9.x or later\n */\nfunction defineMixin(vuei18n, composer, i18n) {\n  return {\n    beforeCreate() {\n      const instance = getCurrentInstance();\n      /* istanbul ignore if */\n      if (!instance) {\n        throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n      }\n      const options = this.$options;\n      if (options.i18n) {\n        const optionsI18n = options.i18n;\n        if (options.__i18n) {\n          optionsI18n.__i18n = options.__i18n;\n        }\n        optionsI18n.__root = composer;\n        if (this === this.$root) {\n          // merge option and gttach global\n          this.$i18n = mergeToGlobal(vuei18n, optionsI18n);\n        } else {\n          optionsI18n.__injectWithOption = true;\n          optionsI18n.__extender = i18n.__vueI18nExtend;\n          // atttach local VueI18n instance\n          this.$i18n = createVueI18n(optionsI18n);\n          // extend VueI18n instance\n          const _vueI18n = this.$i18n;\n          if (_vueI18n.__extender) {\n            _vueI18n.__disposer = _vueI18n.__extender(this.$i18n);\n          }\n        }\n      } else if (options.__i18n) {\n        if (this === this.$root) {\n          // merge option and gttach global\n          this.$i18n = mergeToGlobal(vuei18n, options);\n        } else {\n          // atttach local VueI18n instance\n          this.$i18n = createVueI18n({\n            __i18n: options.__i18n,\n            __injectWithOption: true,\n            __extender: i18n.__vueI18nExtend,\n            __root: composer\n          });\n          // extend VueI18n instance\n          const _vueI18n = this.$i18n;\n          if (_vueI18n.__extender) {\n            _vueI18n.__disposer = _vueI18n.__extender(this.$i18n);\n          }\n        }\n      } else {\n        // attach global VueI18n instance\n        this.$i18n = vuei18n;\n      }\n      if (options.__i18nGlobal) {\n        adjustI18nResources(composer, options, options);\n      }\n      // defines vue-i18n legacy APIs\n      this.$t = (...args) => this.$i18n.t(...args);\n      this.$rt = (...args) => this.$i18n.rt(...args);\n      this.$tc = (...args) => this.$i18n.tc(...args);\n      this.$te = (key, locale) => this.$i18n.te(key, locale);\n      this.$d = (...args) => this.$i18n.d(...args);\n      this.$n = (...args) => this.$i18n.n(...args);\n      this.$tm = key => this.$i18n.tm(key);\n      i18n.__setInstance(instance, this.$i18n);\n    },\n    mounted() {\n      /* istanbul ignore if */\n      if ((process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) && !false && this.$el && this.$i18n) {\n        const _vueI18n = this.$i18n;\n        this.$el.__VUE_I18N__ = _vueI18n.__composer;\n        const emitter = this.__v_emitter = createEmitter();\n        _vueI18n.__enableEmitter && _vueI18n.__enableEmitter(emitter);\n        emitter.on('*', addTimelineEvent);\n      }\n    },\n    unmounted() {\n      const instance = getCurrentInstance();\n      /* istanbul ignore if */\n      if (!instance) {\n        throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n      }\n      const _vueI18n = this.$i18n;\n      /* istanbul ignore if */\n      if ((process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) && !false && this.$el && this.$el.__VUE_I18N__) {\n        if (this.__v_emitter) {\n          this.__v_emitter.off('*', addTimelineEvent);\n          delete this.__v_emitter;\n        }\n        if (this.$i18n) {\n          _vueI18n.__disableEmitter && _vueI18n.__disableEmitter();\n          delete this.$el.__VUE_I18N__;\n        }\n      }\n      delete this.$t;\n      delete this.$rt;\n      delete this.$tc;\n      delete this.$te;\n      delete this.$d;\n      delete this.$n;\n      delete this.$tm;\n      if (_vueI18n.__disposer) {\n        _vueI18n.__disposer();\n        delete _vueI18n.__disposer;\n        delete _vueI18n.__extender;\n      }\n      i18n.__deleteInstance(instance);\n      delete this.$i18n;\n    }\n  };\n}\nfunction mergeToGlobal(g, options) {\n  g.locale = options.locale || g.locale;\n  g.fallbackLocale = options.fallbackLocale || g.fallbackLocale;\n  g.missing = options.missing || g.missing;\n  g.silentTranslationWarn = options.silentTranslationWarn || g.silentFallbackWarn;\n  g.silentFallbackWarn = options.silentFallbackWarn || g.silentFallbackWarn;\n  g.formatFallbackMessages = options.formatFallbackMessages || g.formatFallbackMessages;\n  g.postTranslation = options.postTranslation || g.postTranslation;\n  g.warnHtmlInMessage = options.warnHtmlInMessage || g.warnHtmlInMessage;\n  g.escapeParameterHtml = options.escapeParameterHtml || g.escapeParameterHtml;\n  g.sync = options.sync || g.sync;\n  g.__composer[SetPluralRulesSymbol](options.pluralizationRules || g.pluralizationRules);\n  const messages = getLocaleMessages(g.locale, {\n    messages: options.messages,\n    __i18n: options.__i18n\n  });\n  Object.keys(messages).forEach(locale => g.mergeLocaleMessage(locale, messages[locale]));\n  if (options.datetimeFormats) {\n    Object.keys(options.datetimeFormats).forEach(locale => g.mergeDateTimeFormat(locale, options.datetimeFormats[locale]));\n  }\n  if (options.numberFormats) {\n    Object.keys(options.numberFormats).forEach(locale => g.mergeNumberFormat(locale, options.numberFormats[locale]));\n  }\n  return g;\n}\n\n/**\n * Injection key for {@link useI18n}\n *\n * @remarks\n * The global injection key for I18n instances with `useI18n`. this injection key is used in Web Components.\n * Specify the i18n instance created by {@link createI18n} together with `provide` function.\n *\n * @VueI18nGeneral\n */\nconst I18nInjectionKey = /* #__PURE__*/makeSymbol('global-vue-i18n');\n// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\nfunction createI18n(options = {}, VueI18nLegacy) {\n  // prettier-ignore\n  const __legacyMode = __VUE_I18N_LEGACY_API__ && isBoolean(options.legacy) ? options.legacy : __VUE_I18N_LEGACY_API__;\n  // prettier-ignore\n  const __globalInjection = isBoolean(options.globalInjection) ? options.globalInjection : true;\n  // prettier-ignore\n  const __allowComposition = __VUE_I18N_LEGACY_API__ && __legacyMode ? !!options.allowComposition : true;\n  const __instances = new Map();\n  const [globalScope, __global] = createGlobal(options, __legacyMode);\n  const symbol = /* #__PURE__*/makeSymbol(process.env.NODE_ENV !== 'production' ? 'vue-i18n' : '');\n  if (process.env.NODE_ENV !== 'production') {\n    if (__legacyMode && __allowComposition && !false) {\n      warn(getWarnMessage(I18nWarnCodes.NOTICE_DROP_ALLOW_COMPOSITION));\n    }\n  }\n  function __getInstance(component) {\n    return __instances.get(component) || null;\n  }\n  function __setInstance(component, instance) {\n    __instances.set(component, instance);\n  }\n  function __deleteInstance(component) {\n    __instances.delete(component);\n  }\n  {\n    const i18n = {\n      // mode\n      get mode() {\n        return __VUE_I18N_LEGACY_API__ && __legacyMode ? 'legacy' : 'composition';\n      },\n      // allowComposition\n      get allowComposition() {\n        return __allowComposition;\n      },\n      // install plugin\n      async install(app, ...options) {\n        if ((process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) && !false) {\n          app.__VUE_I18N__ = i18n;\n        }\n        // setup global provider\n        app.__VUE_I18N_SYMBOL__ = symbol;\n        app.provide(app.__VUE_I18N_SYMBOL__, i18n);\n        // set composer & vuei18n extend hook options from plugin options\n        if (isPlainObject(options[0])) {\n          const opts = options[0];\n          i18n.__composerExtend = opts.__composerExtend;\n          i18n.__vueI18nExtend = opts.__vueI18nExtend;\n        }\n        // global method and properties injection for Composition API\n        let globalReleaseHandler = null;\n        if (!__legacyMode && __globalInjection) {\n          globalReleaseHandler = injectGlobalFields(app, i18n.global);\n        }\n        // install built-in components and directive\n        if (__VUE_I18N_FULL_INSTALL__) {\n          apply(app, i18n, ...options);\n        }\n        // setup mixin for Legacy API\n        if (__VUE_I18N_LEGACY_API__ && __legacyMode) {\n          app.mixin(defineMixin(__global, __global.__composer, i18n));\n        }\n        // release global scope\n        const unmountApp = app.unmount;\n        app.unmount = () => {\n          globalReleaseHandler && globalReleaseHandler();\n          i18n.dispose();\n          unmountApp();\n        };\n        // setup vue-devtools plugin\n        if ((process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) && !false) {\n          const ret = await enableDevTools(app, i18n);\n          if (!ret) {\n            throw createI18nError(I18nErrorCodes.CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN);\n          }\n          const emitter = createEmitter();\n          if (__legacyMode) {\n            const _vueI18n = __global;\n            _vueI18n.__enableEmitter && _vueI18n.__enableEmitter(emitter);\n          } else {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const _composer = __global;\n            _composer[EnableEmitter] && _composer[EnableEmitter](emitter);\n          }\n          emitter.on('*', addTimelineEvent);\n        }\n      },\n      // global accessor\n      get global() {\n        return __global;\n      },\n      dispose() {\n        globalScope.stop();\n      },\n      // @internal\n      __instances,\n      // @internal\n      __getInstance,\n      // @internal\n      __setInstance,\n      // @internal\n      __deleteInstance\n    };\n    return i18n;\n  }\n}\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nfunction useI18n(options = {}) {\n  const instance = getCurrentInstance();\n  if (instance == null) {\n    throw createI18nError(I18nErrorCodes.MUST_BE_CALL_SETUP_TOP);\n  }\n  if (!instance.isCE && instance.appContext.app != null && !instance.appContext.app.__VUE_I18N_SYMBOL__) {\n    throw createI18nError(I18nErrorCodes.NOT_INSTALLED);\n  }\n  const i18n = getI18nInstance(instance);\n  const gl = getGlobalComposer(i18n);\n  const componentOptions = getComponentOptions(instance);\n  const scope = getScope(options, componentOptions);\n  if (__VUE_I18N_LEGACY_API__) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    if (i18n.mode === 'legacy' && !options.__useComponent) {\n      if (!i18n.allowComposition) {\n        throw createI18nError(I18nErrorCodes.NOT_AVAILABLE_IN_LEGACY_MODE);\n      }\n      return useI18nForLegacy(instance, scope, gl, options);\n    }\n  }\n  if (scope === 'global') {\n    adjustI18nResources(gl, options, componentOptions);\n    return gl;\n  }\n  if (scope === 'parent') {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let composer = getComposer(i18n, instance, options.__useComponent);\n    if (composer == null) {\n      if (process.env.NODE_ENV !== 'production') {\n        warn(getWarnMessage(I18nWarnCodes.NOT_FOUND_PARENT_SCOPE));\n      }\n      composer = gl;\n    }\n    return composer;\n  }\n  const i18nInternal = i18n;\n  let composer = i18nInternal.__getInstance(instance);\n  if (composer == null) {\n    const composerOptions = assign({}, options);\n    if ('__i18n' in componentOptions) {\n      composerOptions.__i18n = componentOptions.__i18n;\n    }\n    if (gl) {\n      composerOptions.__root = gl;\n    }\n    composer = createComposer(composerOptions);\n    if (i18nInternal.__composerExtend) {\n      composer[DisposeSymbol] = i18nInternal.__composerExtend(composer);\n    }\n    setupLifeCycle(i18nInternal, instance, composer);\n    i18nInternal.__setInstance(instance, composer);\n  }\n  return composer;\n}\n/**\n * Cast to VueI18n legacy compatible type\n *\n * @remarks\n * This API is provided only with [vue-i18n-bridge](https://vue-i18n.intlify.dev/guide/migration/ways.html#what-is-vue-i18n-bridge).\n *\n * The purpose of this function is to convert an {@link I18n} instance created with {@link createI18n | createI18n(legacy: true)} into a `vue-i18n@v8.x` compatible instance of `new VueI18n` in a TypeScript environment.\n *\n * @param i18n - An instance of {@link I18n}\n * @returns A i18n instance which is casted to {@link VueI18n} type\n *\n * @VueI18nTip\n * :new: provided by **vue-i18n-bridge only**\n *\n * @VueI18nGeneral\n */\n/* #__NO_SIDE_EFFECTS__ */\nconst castToVueI18n = (i18n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n) => {\n  if (!(__VUE_I18N_BRIDGE__ in i18n)) {\n    throw createI18nError(I18nErrorCodes.NOT_COMPATIBLE_LEGACY_VUE_I18N);\n  }\n  return i18n;\n};\nfunction createGlobal(options, legacyMode, VueI18nLegacy // eslint-disable-line @typescript-eslint/no-explicit-any\n) {\n  const scope = effectScope();\n  {\n    const obj = __VUE_I18N_LEGACY_API__ && legacyMode ? scope.run(() => createVueI18n(options)) : scope.run(() => createComposer(options));\n    if (obj == null) {\n      throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n    }\n    return [scope, obj];\n  }\n}\nfunction getI18nInstance(instance) {\n  {\n    const i18n = inject(!instance.isCE ? instance.appContext.app.__VUE_I18N_SYMBOL__ : I18nInjectionKey);\n    /* istanbul ignore if */\n    if (!i18n) {\n      throw createI18nError(!instance.isCE ? I18nErrorCodes.UNEXPECTED_ERROR : I18nErrorCodes.NOT_INSTALLED_WITH_PROVIDE);\n    }\n    return i18n;\n  }\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getScope(options, componentOptions) {\n  // prettier-ignore\n  return isEmptyObject(options) ? '__i18n' in componentOptions ? 'local' : 'global' : !options.useScope ? 'local' : options.useScope;\n}\nfunction getGlobalComposer(i18n) {\n  // prettier-ignore\n  return i18n.mode === 'composition' ? i18n.global : i18n.global.__composer;\n}\nfunction getComposer(i18n, target, useComponent = false) {\n  let composer = null;\n  const root = target.root;\n  let current = getParentComponentInstance(target, useComponent);\n  while (current != null) {\n    const i18nInternal = i18n;\n    if (i18n.mode === 'composition') {\n      composer = i18nInternal.__getInstance(current);\n    } else {\n      if (__VUE_I18N_LEGACY_API__) {\n        const vueI18n = i18nInternal.__getInstance(current);\n        if (vueI18n != null) {\n          composer = vueI18n.__composer;\n          if (useComponent && composer && !composer[InejctWithOptionSymbol] // eslint-disable-line @typescript-eslint/no-explicit-any\n          ) {\n            composer = null;\n          }\n        }\n      }\n    }\n    if (composer != null) {\n      break;\n    }\n    if (root === current) {\n      break;\n    }\n    current = current.parent;\n  }\n  return composer;\n}\nfunction getParentComponentInstance(target, useComponent = false) {\n  if (target == null) {\n    return null;\n  }\n  {\n    // if `useComponent: true` will be specified, we get lexical scope owner instance for use-case slots\n    return !useComponent ? target.parent : target.vnode.ctx || target.parent; // eslint-disable-line @typescript-eslint/no-explicit-any\n  }\n}\nfunction setupLifeCycle(i18n, target, composer) {\n  let emitter = null;\n  {\n    onMounted(() => {\n      // inject composer instance to DOM for intlify-devtools\n      if ((process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) && !false && target.vnode.el) {\n        target.vnode.el.__VUE_I18N__ = composer;\n        emitter = createEmitter();\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const _composer = composer;\n        _composer[EnableEmitter] && _composer[EnableEmitter](emitter);\n        emitter.on('*', addTimelineEvent);\n      }\n    }, target);\n    onUnmounted(() => {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const _composer = composer;\n      // remove composer instance from DOM for intlify-devtools\n      if ((process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) && !false && target.vnode.el && target.vnode.el.__VUE_I18N__) {\n        emitter && emitter.off('*', addTimelineEvent);\n        _composer[DisableEmitter] && _composer[DisableEmitter]();\n        delete target.vnode.el.__VUE_I18N__;\n      }\n      i18n.__deleteInstance(target);\n      // dispose extended resources\n      const dispose = _composer[DisposeSymbol];\n      if (dispose) {\n        dispose();\n        delete _composer[DisposeSymbol];\n      }\n    }, target);\n  }\n}\nfunction useI18nForLegacy(instance, scope, root, options = {} // eslint-disable-line @typescript-eslint/no-explicit-any\n) {\n  const isLocalScope = scope === 'local';\n  const _composer = shallowRef(null);\n  if (isLocalScope && instance.proxy && !(instance.proxy.$options.i18n || instance.proxy.$options.__i18n)) {\n    throw createI18nError(I18nErrorCodes.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);\n  }\n  const _inheritLocale = isBoolean(options.inheritLocale) ? options.inheritLocale : !isString(options.locale);\n  const _locale = ref(\n  // prettier-ignore\n  !isLocalScope || _inheritLocale ? root.locale.value : isString(options.locale) ? options.locale : DEFAULT_LOCALE);\n  const _fallbackLocale = ref(\n  // prettier-ignore\n  !isLocalScope || _inheritLocale ? root.fallbackLocale.value : isString(options.fallbackLocale) || isArray(options.fallbackLocale) || isPlainObject(options.fallbackLocale) || options.fallbackLocale === false ? options.fallbackLocale : _locale.value);\n  const _messages = ref(getLocaleMessages(_locale.value, options));\n  // prettier-ignore\n  const _datetimeFormats = ref(isPlainObject(options.datetimeFormats) ? options.datetimeFormats : {\n    [_locale.value]: {}\n  });\n  // prettier-ignore\n  const _numberFormats = ref(isPlainObject(options.numberFormats) ? options.numberFormats : {\n    [_locale.value]: {}\n  });\n  // prettier-ignore\n  const _missingWarn = isLocalScope ? root.missingWarn : isBoolean(options.missingWarn) || isRegExp(options.missingWarn) ? options.missingWarn : true;\n  // prettier-ignore\n  const _fallbackWarn = isLocalScope ? root.fallbackWarn : isBoolean(options.fallbackWarn) || isRegExp(options.fallbackWarn) ? options.fallbackWarn : true;\n  // prettier-ignore\n  const _fallbackRoot = isLocalScope ? root.fallbackRoot : isBoolean(options.fallbackRoot) ? options.fallbackRoot : true;\n  // configure fall back to root\n  const _fallbackFormat = !!options.fallbackFormat;\n  // runtime missing\n  const _missing = isFunction(options.missing) ? options.missing : null;\n  // postTranslation handler\n  const _postTranslation = isFunction(options.postTranslation) ? options.postTranslation : null;\n  // prettier-ignore\n  const _warnHtmlMessage = isLocalScope ? root.warnHtmlMessage : isBoolean(options.warnHtmlMessage) ? options.warnHtmlMessage : true;\n  const _escapeParameter = !!options.escapeParameter;\n  // prettier-ignore\n  const _modifiers = isLocalScope ? root.modifiers : isPlainObject(options.modifiers) ? options.modifiers : {};\n  // pluralRules\n  const _pluralRules = options.pluralRules || isLocalScope && root.pluralRules;\n  // track reactivity\n  function trackReactivityValues() {\n    return [_locale.value, _fallbackLocale.value, _messages.value, _datetimeFormats.value, _numberFormats.value];\n  }\n  // locale\n  const locale = computed({\n    get: () => {\n      return _composer.value ? _composer.value.locale.value : _locale.value;\n    },\n    set: val => {\n      if (_composer.value) {\n        _composer.value.locale.value = val;\n      }\n      _locale.value = val;\n    }\n  });\n  // fallbackLocale\n  const fallbackLocale = computed({\n    get: () => {\n      return _composer.value ? _composer.value.fallbackLocale.value : _fallbackLocale.value;\n    },\n    set: val => {\n      if (_composer.value) {\n        _composer.value.fallbackLocale.value = val;\n      }\n      _fallbackLocale.value = val;\n    }\n  });\n  // messages\n  const messages = computed(() => {\n    if (_composer.value) {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return _composer.value.messages.value;\n    } else {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return _messages.value;\n    }\n  });\n  const datetimeFormats = computed(() => _datetimeFormats.value);\n  const numberFormats = computed(() => _numberFormats.value);\n  function getPostTranslationHandler() {\n    return _composer.value ? _composer.value.getPostTranslationHandler() : _postTranslation;\n  }\n  function setPostTranslationHandler(handler) {\n    if (_composer.value) {\n      _composer.value.setPostTranslationHandler(handler);\n    }\n  }\n  function getMissingHandler() {\n    return _composer.value ? _composer.value.getMissingHandler() : _missing;\n  }\n  function setMissingHandler(handler) {\n    if (_composer.value) {\n      _composer.value.setMissingHandler(handler);\n    }\n  }\n  function warpWithDeps(fn) {\n    trackReactivityValues();\n    return fn();\n  }\n  function t(...args) {\n    return _composer.value ? warpWithDeps(() => Reflect.apply(_composer.value.t, null, [...args])) : warpWithDeps(() => '');\n  }\n  function rt(...args) {\n    return _composer.value ? Reflect.apply(_composer.value.rt, null, [...args]) : '';\n  }\n  function d(...args) {\n    return _composer.value ? warpWithDeps(() => Reflect.apply(_composer.value.d, null, [...args])) : warpWithDeps(() => '');\n  }\n  function n(...args) {\n    return _composer.value ? warpWithDeps(() => Reflect.apply(_composer.value.n, null, [...args])) : warpWithDeps(() => '');\n  }\n  function tm(key) {\n    return _composer.value ? _composer.value.tm(key) : {};\n  }\n  function te(key, locale) {\n    return _composer.value ? _composer.value.te(key, locale) : false;\n  }\n  function getLocaleMessage(locale) {\n    return _composer.value ? _composer.value.getLocaleMessage(locale) : {};\n  }\n  function setLocaleMessage(locale, message) {\n    if (_composer.value) {\n      _composer.value.setLocaleMessage(locale, message);\n      _messages.value[locale] = message;\n    }\n  }\n  function mergeLocaleMessage(locale, message) {\n    if (_composer.value) {\n      _composer.value.mergeLocaleMessage(locale, message);\n    }\n  }\n  function getDateTimeFormat(locale) {\n    return _composer.value ? _composer.value.getDateTimeFormat(locale) : {};\n  }\n  function setDateTimeFormat(locale, format) {\n    if (_composer.value) {\n      _composer.value.setDateTimeFormat(locale, format);\n      _datetimeFormats.value[locale] = format;\n    }\n  }\n  function mergeDateTimeFormat(locale, format) {\n    if (_composer.value) {\n      _composer.value.mergeDateTimeFormat(locale, format);\n    }\n  }\n  function getNumberFormat(locale) {\n    return _composer.value ? _composer.value.getNumberFormat(locale) : {};\n  }\n  function setNumberFormat(locale, format) {\n    if (_composer.value) {\n      _composer.value.setNumberFormat(locale, format);\n      _numberFormats.value[locale] = format;\n    }\n  }\n  function mergeNumberFormat(locale, format) {\n    if (_composer.value) {\n      _composer.value.mergeNumberFormat(locale, format);\n    }\n  }\n  const wrapper = {\n    get id() {\n      return _composer.value ? _composer.value.id : -1;\n    },\n    locale,\n    fallbackLocale,\n    messages,\n    datetimeFormats,\n    numberFormats,\n    get inheritLocale() {\n      return _composer.value ? _composer.value.inheritLocale : _inheritLocale;\n    },\n    set inheritLocale(val) {\n      if (_composer.value) {\n        _composer.value.inheritLocale = val;\n      }\n    },\n    get availableLocales() {\n      return _composer.value ? _composer.value.availableLocales : Object.keys(_messages.value);\n    },\n    get modifiers() {\n      return _composer.value ? _composer.value.modifiers : _modifiers;\n    },\n    get pluralRules() {\n      return _composer.value ? _composer.value.pluralRules : _pluralRules;\n    },\n    get isGlobal() {\n      return _composer.value ? _composer.value.isGlobal : false;\n    },\n    get missingWarn() {\n      return _composer.value ? _composer.value.missingWarn : _missingWarn;\n    },\n    set missingWarn(val) {\n      if (_composer.value) {\n        _composer.value.missingWarn = val;\n      }\n    },\n    get fallbackWarn() {\n      return _composer.value ? _composer.value.fallbackWarn : _fallbackWarn;\n    },\n    set fallbackWarn(val) {\n      if (_composer.value) {\n        _composer.value.missingWarn = val;\n      }\n    },\n    get fallbackRoot() {\n      return _composer.value ? _composer.value.fallbackRoot : _fallbackRoot;\n    },\n    set fallbackRoot(val) {\n      if (_composer.value) {\n        _composer.value.fallbackRoot = val;\n      }\n    },\n    get fallbackFormat() {\n      return _composer.value ? _composer.value.fallbackFormat : _fallbackFormat;\n    },\n    set fallbackFormat(val) {\n      if (_composer.value) {\n        _composer.value.fallbackFormat = val;\n      }\n    },\n    get warnHtmlMessage() {\n      return _composer.value ? _composer.value.warnHtmlMessage : _warnHtmlMessage;\n    },\n    set warnHtmlMessage(val) {\n      if (_composer.value) {\n        _composer.value.warnHtmlMessage = val;\n      }\n    },\n    get escapeParameter() {\n      return _composer.value ? _composer.value.escapeParameter : _escapeParameter;\n    },\n    set escapeParameter(val) {\n      if (_composer.value) {\n        _composer.value.escapeParameter = val;\n      }\n    },\n    t,\n    getPostTranslationHandler,\n    setPostTranslationHandler,\n    getMissingHandler,\n    setMissingHandler,\n    rt,\n    d,\n    n,\n    tm,\n    te,\n    getLocaleMessage,\n    setLocaleMessage,\n    mergeLocaleMessage,\n    getDateTimeFormat,\n    setDateTimeFormat,\n    mergeDateTimeFormat,\n    getNumberFormat,\n    setNumberFormat,\n    mergeNumberFormat\n  };\n  function sync(composer) {\n    composer.locale.value = _locale.value;\n    composer.fallbackLocale.value = _fallbackLocale.value;\n    Object.keys(_messages.value).forEach(locale => {\n      composer.mergeLocaleMessage(locale, _messages.value[locale]);\n    });\n    Object.keys(_datetimeFormats.value).forEach(locale => {\n      composer.mergeDateTimeFormat(locale, _datetimeFormats.value[locale]);\n    });\n    Object.keys(_numberFormats.value).forEach(locale => {\n      composer.mergeNumberFormat(locale, _numberFormats.value[locale]);\n    });\n    composer.escapeParameter = _escapeParameter;\n    composer.fallbackFormat = _fallbackFormat;\n    composer.fallbackRoot = _fallbackRoot;\n    composer.fallbackWarn = _fallbackWarn;\n    composer.missingWarn = _missingWarn;\n    composer.warnHtmlMessage = _warnHtmlMessage;\n  }\n  onBeforeMount(() => {\n    if (instance.proxy == null || instance.proxy.$i18n == null) {\n      throw createI18nError(I18nErrorCodes.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const composer = _composer.value = instance.proxy.$i18n.__composer;\n    if (scope === 'global') {\n      _locale.value = composer.locale.value;\n      _fallbackLocale.value = composer.fallbackLocale.value;\n      _messages.value = composer.messages.value;\n      _datetimeFormats.value = composer.datetimeFormats.value;\n      _numberFormats.value = composer.numberFormats.value;\n    } else if (isLocalScope) {\n      sync(composer);\n    }\n  });\n  return wrapper;\n}\nconst globalExportProps = ['locale', 'fallbackLocale', 'availableLocales'];\nconst globalExportMethods = ['t', 'rt', 'd', 'n', 'tm', 'te'];\nfunction injectGlobalFields(app, composer) {\n  const i18n = Object.create(null);\n  globalExportProps.forEach(prop => {\n    const desc = Object.getOwnPropertyDescriptor(composer, prop);\n    if (!desc) {\n      throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n    }\n    const wrap = isRef(desc.value) // check computed props\n    ? {\n      get() {\n        return desc.value.value;\n      },\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      set(val) {\n        desc.value.value = val;\n      }\n    } : {\n      get() {\n        return desc.get && desc.get();\n      }\n    };\n    Object.defineProperty(i18n, prop, wrap);\n  });\n  app.config.globalProperties.$i18n = i18n;\n  globalExportMethods.forEach(method => {\n    const desc = Object.getOwnPropertyDescriptor(composer, method);\n    if (!desc || !desc.value) {\n      throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n    }\n    Object.defineProperty(app.config.globalProperties, `$${method}`, desc);\n  });\n  const dispose = () => {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    delete app.config.globalProperties.$i18n;\n    globalExportMethods.forEach(method => {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      delete app.config.globalProperties[`$${method}`];\n    });\n  };\n  return dispose;\n}\n{\n  initFeatureFlags();\n}\n// register message compiler at vue-i18n\nif (__INTLIFY_JIT_COMPILATION__) {\n  registerMessageCompiler(compile);\n} else {\n  registerMessageCompiler(compileToFunction);\n}\n// register message resolver at vue-i18n\nregisterMessageResolver(resolveValue);\n// register fallback locale at vue-i18n\nregisterLocaleFallbacker(fallbackWithLocaleChain);\n// NOTE: experimental !!\nif (process.env.NODE_ENV !== 'production' || __INTLIFY_PROD_DEVTOOLS__) {\n  const target = getGlobalThis();\n  target.__INTLIFY__ = true;\n  setDevToolsHook(target.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__);\n}\nif (process.env.NODE_ENV !== 'production') ;\nexport { DatetimeFormat, I18nD, I18nInjectionKey, I18nN, I18nT, NumberFormat, Translation, VERSION, castToVueI18n, createI18n, useI18n, vTDirective };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}