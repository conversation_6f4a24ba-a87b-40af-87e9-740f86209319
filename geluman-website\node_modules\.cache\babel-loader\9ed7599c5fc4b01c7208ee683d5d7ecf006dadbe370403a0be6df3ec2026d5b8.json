{"ast": null, "code": "import { Clock, CircleClose } from '@element-plus/icons-vue';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { useEmptyValuesProps } from '../../../hooks/use-empty-values/index.mjs';\nconst timeSelectProps = buildProps({\n  format: {\n    type: String,\n    default: \"HH:mm\"\n  },\n  modelValue: String,\n  disabled: Boolean,\n  editable: {\n    type: Boolean,\n    default: true\n  },\n  effect: {\n    type: definePropType(String),\n    default: \"light\"\n  },\n  clearable: {\n    type: Boolean,\n    default: true\n  },\n  size: useSizeProp,\n  placeholder: String,\n  start: {\n    type: String,\n    default: \"09:00\"\n  },\n  end: {\n    type: String,\n    default: \"18:00\"\n  },\n  step: {\n    type: String,\n    default: \"00:30\"\n  },\n  minTime: String,\n  maxTime: String,\n  includeEndTime: {\n    type: Boolean,\n    default: false\n  },\n  name: String,\n  prefixIcon: {\n    type: definePropType([String, Object]),\n    default: () => Clock\n  },\n  clearIcon: {\n    type: definePropType([String, Object]),\n    default: () => CircleClose\n  },\n  ...useEmptyValuesProps\n});\nexport { timeSelectProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}