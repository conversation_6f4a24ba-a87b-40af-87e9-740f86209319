{"ast": null, "code": "import { dropdownProps } from '../../dropdown/src/dropdown.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { useTooltipTriggerProps } from '../../tooltip/src/trigger.mjs';\nimport { useTooltipContentProps } from '../../tooltip/src/content.mjs';\nimport { isBoolean } from '../../../utils/types.mjs';\nconst popoverProps = buildProps({\n  trigger: useTooltipTriggerProps.trigger,\n  triggerKeys: useTooltipTriggerProps.triggerKeys,\n  placement: dropdownProps.placement,\n  disabled: useTooltipTriggerProps.disabled,\n  visible: useTooltipContentProps.visible,\n  transition: useTooltipContentProps.transition,\n  popperOptions: dropdownProps.popperOptions,\n  tabindex: dropdownProps.tabindex,\n  content: useTooltipContentProps.content,\n  popperStyle: useTooltipContentProps.popperStyle,\n  popperClass: useTooltipContentProps.popperClass,\n  enterable: {\n    ...useTooltipContentProps.enterable,\n    default: true\n  },\n  effect: {\n    ...useTooltipContentProps.effect,\n    default: \"light\"\n  },\n  teleported: useTooltipContentProps.teleported,\n  title: String,\n  width: {\n    type: [String, Number],\n    default: 150\n  },\n  offset: {\n    type: Number,\n    default: void 0\n  },\n  showAfter: {\n    type: Number,\n    default: 0\n  },\n  hideAfter: {\n    type: Number,\n    default: 200\n  },\n  autoClose: {\n    type: Number,\n    default: 0\n  },\n  showArrow: {\n    type: Boolean,\n    default: true\n  },\n  persistent: {\n    type: Boolean,\n    default: true\n  },\n  \"onUpdate:visible\": {\n    type: Function\n  }\n});\nconst popoverEmits = {\n  \"update:visible\": value => isBoolean(value),\n  \"before-enter\": () => true,\n  \"before-leave\": () => true,\n  \"after-enter\": () => true,\n  \"after-leave\": () => true\n};\nexport { popoverEmits, popoverProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}