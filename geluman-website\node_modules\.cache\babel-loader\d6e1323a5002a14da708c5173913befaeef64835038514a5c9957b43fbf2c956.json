{"ast": null, "code": "import { isFunction } from '@vue/shared';\nconst REPEAT_INTERVAL = 100;\nconst REPEAT_DELAY = 600;\nconst vRepeatClick = {\n  beforeMount(el, binding) {\n    const value = binding.value;\n    const {\n      interval = REPEAT_INTERVAL,\n      delay = REPEAT_DELAY\n    } = isFunction(value) ? {} : value;\n    let intervalId;\n    let delayId;\n    const handler = () => isFunction(value) ? value() : value.handler();\n    const clear = () => {\n      if (delayId) {\n        clearTimeout(delayId);\n        delayId = void 0;\n      }\n      if (intervalId) {\n        clearInterval(intervalId);\n        intervalId = void 0;\n      }\n    };\n    el.addEventListener(\"mousedown\", evt => {\n      if (evt.button !== 0) return;\n      clear();\n      handler();\n      document.addEventListener(\"mouseup\", () => clear(), {\n        once: true\n      });\n      delayId = setTimeout(() => {\n        intervalId = setInterval(() => {\n          handler();\n        }, interval);\n      }, delay);\n    });\n  }\n};\nexport { REPEAT_DELAY, REPEAT_INTERVAL, vRepeatClick };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}