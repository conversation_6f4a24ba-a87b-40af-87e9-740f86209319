{"ast": null, "code": "import { VERTICAL } from './defaults.mjs';\nimport { buildProp, buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nconst itemSize = buildProp({\n  type: definePropType([Number, Function]),\n  required: true\n});\nconst estimatedItemSize = buildProp({\n  type: Number\n});\nconst cache = buildProp({\n  type: Number,\n  default: 2\n});\nconst direction = buildProp({\n  type: String,\n  values: [\"ltr\", \"rtl\"],\n  default: \"ltr\"\n});\nconst initScrollOffset = buildProp({\n  type: Number,\n  default: 0\n});\nconst total = buildProp({\n  type: Number,\n  required: true\n});\nconst layout = buildProp({\n  type: String,\n  values: [\"horizontal\", \"vertical\"],\n  default: VERTICAL\n});\nconst virtualizedProps = buildProps({\n  className: {\n    type: String,\n    default: \"\"\n  },\n  containerElement: {\n    type: definePropType([String, Object]),\n    default: \"div\"\n  },\n  data: {\n    type: definePropType(Array),\n    default: () => mutable([])\n  },\n  direction,\n  height: {\n    type: [String, Number],\n    required: true\n  },\n  innerElement: {\n    type: [String, Object],\n    default: \"div\"\n  },\n  style: {\n    type: definePropType([Object, String, Array])\n  },\n  useIsScrolling: {\n    type: Boolean,\n    default: false\n  },\n  width: {\n    type: [Number, String],\n    required: false\n  },\n  perfMode: {\n    type: Boolean,\n    default: true\n  },\n  scrollbarAlwaysOn: {\n    type: Boolean,\n    default: false\n  }\n});\nconst virtualizedListProps = buildProps({\n  cache,\n  estimatedItemSize,\n  layout,\n  initScrollOffset,\n  total,\n  itemSize,\n  ...virtualizedProps\n});\nconst scrollbarSize = {\n  type: Number,\n  default: 6\n};\nconst startGap = {\n  type: Number,\n  default: 0\n};\nconst endGap = {\n  type: Number,\n  default: 2\n};\nconst virtualizedGridProps = buildProps({\n  columnCache: cache,\n  columnWidth: itemSize,\n  estimatedColumnWidth: estimatedItemSize,\n  estimatedRowHeight: estimatedItemSize,\n  initScrollLeft: initScrollOffset,\n  initScrollTop: initScrollOffset,\n  itemKey: {\n    type: definePropType(Function),\n    default: ({\n      columnIndex,\n      rowIndex\n    }) => `${rowIndex}:${columnIndex}`\n  },\n  rowCache: cache,\n  rowHeight: itemSize,\n  totalColumn: total,\n  totalRow: total,\n  hScrollbarSize: scrollbarSize,\n  vScrollbarSize: scrollbarSize,\n  scrollbarStartGap: startGap,\n  scrollbarEndGap: endGap,\n  role: String,\n  ...virtualizedProps\n});\nconst virtualizedScrollbarProps = buildProps({\n  alwaysOn: Boolean,\n  class: String,\n  layout,\n  total,\n  ratio: {\n    type: Number,\n    required: true\n  },\n  clientSize: {\n    type: Number,\n    required: true\n  },\n  scrollFrom: {\n    type: Number,\n    required: true\n  },\n  scrollbarSize,\n  startGap,\n  endGap,\n  visible: Boolean\n});\nexport { virtualizedGridProps, virtualizedListProps, virtualizedProps, virtualizedScrollbarProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}