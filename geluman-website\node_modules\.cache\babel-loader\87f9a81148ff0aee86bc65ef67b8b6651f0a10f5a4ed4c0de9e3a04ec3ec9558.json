{"ast": null, "code": "import { unref as _unref, createElementVNode as _createElementVNode, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"product-page\"\n};\nconst _hoisted_2 = {\n  class: \"hero\"\n};\nconst _hoisted_3 = {\n  class: \"hero-content\"\n};\nconst _hoisted_4 = [\"src\"];\nconst _hoisted_5 = {\n  class: \"cta-buttons\"\n};\nconst _hoisted_6 = {\n  href: \"https://gengxin.geluman.cn/downloads/GLM-Translator-1.0.0.zip\",\n  class: \"download-btn\",\n  target: \"_blank\"\n};\nconst _hoisted_7 = {\n  class: \"demo\"\n};\nconst _hoisted_8 = {\n  class: \"container\"\n};\nconst _hoisted_9 = {\n  class: \"demo-gallery\"\n};\nconst _hoisted_10 = {\n  class: \"demo-item\"\n};\nconst _hoisted_11 = {\n  class: \"demo-image-container\"\n};\nconst _hoisted_12 = [\"src\"];\nconst _hoisted_13 = {\n  class: \"demo-item\"\n};\nconst _hoisted_14 = {\n  class: \"demo-image-container\"\n};\nconst _hoisted_15 = [\"src\"];\nconst _hoisted_16 = {\n  class: \"features\"\n};\nconst _hoisted_17 = {\n  class: \"container\"\n};\nconst _hoisted_18 = {\n  class: \"features-grid\"\n};\nconst _hoisted_19 = {\n  class: \"feature-icon\"\n};\nconst _hoisted_20 = {\n  class: \"usage\"\n};\nconst _hoisted_21 = {\n  class: \"container\"\n};\nconst _hoisted_22 = {\n  class: \"usage-steps\"\n};\nconst _hoisted_23 = {\n  class: \"step-number\"\n};\nimport { translator } from \"@/assets\";\nimport { Download } from \"@element-plus/icons-vue\";\nimport translatorHome from \"@/assets/img/translator-home.png\";\nimport translatorSetting from \"@/assets/img/translator-setting.png\";\nconst __default__ = {\n  name: \"TranslatorPage\"\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props) {\n    const features = [{\n      icon: \"🌐\",\n      title: \"多语言支持\",\n      description: \"支持中英文、日语、法语等多国语言之间的互译，满足不同场景的翻译需求\"\n    }, {\n      icon: \"✂️\",\n      title: \"划词翻译\",\n      description: \"选中网页上的任意文本，即可一键获取翻译结果，快速理解外语内容\"\n    }, {\n      icon: \"📝\",\n      title: \"智能识别\",\n      description: \"自动识别文本语言，无需手动选择源语言，让翻译更加便捷高效\"\n    }, {\n      icon: \"🔄\",\n      title: \"实时翻译\",\n      description: \"选中文本后立即获取翻译结果，无需等待，提高阅读和学习效率\"\n    }, {\n      icon: \"🎛️\",\n      title: \"个性化设置\",\n      description: \"支持设置默认目标语言、翻译显示方式等，打造个性化翻译体验\"\n    }, {\n      icon: \"📱\",\n      title: \"跨平台兼容\",\n      description: \"支持Chrome、Edge等主流浏览器，保证在不同设备上的稳定运行\"\n    }];\n    const steps = [{\n      number: \"1\",\n      title: \"安装插件\",\n      description: \"从下方下载按钮下载并解压，打开Chrome或者Edge浏览器，进入chrome://extensions/或者edge://extensions/，将解压后的文件夹拖入页面中，完成安装\"\n    }, {\n      number: \"2\",\n      title: \"选择文本\",\n      description: \"在任意网页上选择需要翻译的文本内容\"\n    }, {\n      number: \"3\",\n      title: \"查看翻译\",\n      description: \"选中后自动显示翻译结果，或点击插件图标进行翻译\"\n    }, {\n      number: \"4\",\n      title: \"调整设置\",\n      description: \"在插件设置中可以自定义目标语言和显示方式\"\n    }];\n    return (_ctx, _cache) => {\n      const _component_el_icon = _resolveComponent(\"el-icon\");\n      return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"img\", {\n        src: _unref(translator),\n        alt: \"翻译插件\",\n        class: \"plugin-logo\"\n      }, null, 8, _hoisted_4), _cache[1] || (_cache[1] = _createElementVNode(\"h1\", null, \"智能翻译助手\", -1)), _cache[2] || (_cache[2] = _createElementVNode(\"p\", {\n        class: \"subtitle\"\n      }, \"高效、准确的网页文本翻译工具，帮助您跨越语言障碍\", -1)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"a\", _hoisted_6, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_unref(Download))]),\n        _: 1\n      }), _cache[0] || (_cache[0] = _createTextVNode(\" 下载插件 \"))])])])]), _createElementVNode(\"section\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_cache[5] || (_cache[5] = _createElementVNode(\"h2\", {\n        class: \"section-title\"\n      }, \"产品演示\", -1)), _cache[6] || (_cache[6] = _createElementVNode(\"p\", {\n        class: \"section-subtitle\"\n      }, \"简单易用的界面，强大的翻译功能\", -1)), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"img\", {\n        src: _unref(translatorHome),\n        alt: \"智能翻译助手 - 翻译界面\",\n        class: \"demo-image\"\n      }, null, 8, _hoisted_12)]), _cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n        class: \"image-caption\"\n      }, \"智能翻译界面 - 选择文本即可快速翻译\", -1))]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"img\", {\n        src: _unref(translatorSetting),\n        alt: \"智能翻译助手 - 设置界面\",\n        class: \"demo-image\"\n      }, null, 8, _hoisted_15)]), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n        class: \"image-caption\"\n      }, \"个性化设置 - 自定义翻译体验\", -1))])])])]), _createElementVNode(\"section\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_cache[7] || (_cache[7] = _createElementVNode(\"h2\", null, \"核心功能\", -1)), _createElementVNode(\"div\", _hoisted_18, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(features, (feature, index) => {\n        return _createElementVNode(\"div\", {\n          key: feature.title,\n          class: \"feature-card\"\n        }, [_createElementVNode(\"div\", _hoisted_19, _toDisplayString(feature.icon), 1), _createElementVNode(\"h3\", null, _toDisplayString(feature.title), 1), _createElementVNode(\"p\", null, _toDisplayString(feature.description), 1)]);\n      }), 64))])])]), _createElementVNode(\"section\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_cache[8] || (_cache[8] = _createElementVNode(\"h2\", null, \"使用说明\", -1)), _createElementVNode(\"div\", _hoisted_22, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(steps, step => {\n        return _createElementVNode(\"div\", {\n          class: \"step\",\n          key: step.title\n        }, [_createElementVNode(\"div\", _hoisted_23, _toDisplayString(step.number), 1), _createElementVNode(\"h3\", null, _toDisplayString(step.title), 1), _createElementVNode(\"p\", null, _toDisplayString(step.description), 1)]);\n      }), 64))])])])]);\n    };\n  }\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}