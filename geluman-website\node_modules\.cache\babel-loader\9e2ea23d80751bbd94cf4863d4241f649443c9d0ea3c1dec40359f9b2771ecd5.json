{"ast": null, "code": "import { castArray } from 'lodash-unified';\nimport { isFirefox } from '../../../utils/browser.mjs';\nconst filterOption = (pattern, option) => {\n  const lowerCase = pattern.toLowerCase();\n  const label = option.label || option.value;\n  return label.toLowerCase().includes(lowerCase);\n};\nconst getMentionCtx = (inputEl, prefix, split) => {\n  const {\n    selectionEnd\n  } = inputEl;\n  if (selectionEnd === null) return;\n  const inputValue = inputEl.value;\n  const prefixArray = castArray(prefix);\n  let splitIndex = -1;\n  let mentionCtx;\n  for (let i = selectionEnd - 1; i >= 0; --i) {\n    const char = inputValue[i];\n    if (char === split || char === \"\\n\" || char === \"\\r\") {\n      splitIndex = i;\n      continue;\n    }\n    if (prefixArray.includes(char)) {\n      const end = splitIndex === -1 ? selectionEnd : splitIndex;\n      const pattern = inputValue.slice(i + 1, end);\n      mentionCtx = {\n        pattern,\n        start: i + 1,\n        end,\n        prefix: char,\n        prefixIndex: i,\n        splitIndex,\n        selectionEnd\n      };\n      break;\n    }\n  }\n  return mentionCtx;\n};\nconst getCursorPosition = (element, options = {\n  debug: false,\n  useSelectionEnd: false\n}) => {\n  const selectionStart = element.selectionStart !== null ? element.selectionStart : 0;\n  const selectionEnd = element.selectionEnd !== null ? element.selectionEnd : 0;\n  const position = options.useSelectionEnd ? selectionEnd : selectionStart;\n  const properties = [\"direction\", \"boxSizing\", \"width\", \"height\", \"overflowX\", \"overflowY\", \"borderTopWidth\", \"borderRightWidth\", \"borderBottomWidth\", \"borderLeftWidth\", \"borderStyle\", \"paddingTop\", \"paddingRight\", \"paddingBottom\", \"paddingLeft\", \"fontStyle\", \"fontVariant\", \"fontWeight\", \"fontStretch\", \"fontSize\", \"fontSizeAdjust\", \"lineHeight\", \"fontFamily\", \"textAlign\", \"textTransform\", \"textIndent\", \"textDecoration\", \"letterSpacing\", \"wordSpacing\", \"tabSize\", \"MozTabSize\"];\n  if (options.debug) {\n    const el = document.querySelector(\"#input-textarea-caret-position-mirror-div\");\n    if (el == null ? void 0 : el.parentNode) el.parentNode.removeChild(el);\n  }\n  const div = document.createElement(\"div\");\n  div.id = \"input-textarea-caret-position-mirror-div\";\n  document.body.appendChild(div);\n  const style = div.style;\n  const computed = window.getComputedStyle(element);\n  const isInput = element.nodeName === \"INPUT\";\n  style.whiteSpace = isInput ? \"nowrap\" : \"pre-wrap\";\n  if (!isInput) style.wordWrap = \"break-word\";\n  style.position = \"absolute\";\n  if (!options.debug) style.visibility = \"hidden\";\n  properties.forEach(prop => {\n    if (isInput && prop === \"lineHeight\") {\n      if (computed.boxSizing === \"border-box\") {\n        const height = Number.parseInt(computed.height);\n        const outerHeight = Number.parseInt(computed.paddingTop) + Number.parseInt(computed.paddingBottom) + Number.parseInt(computed.borderTopWidth) + Number.parseInt(computed.borderBottomWidth);\n        const targetHeight = outerHeight + Number.parseInt(computed.lineHeight);\n        if (height > targetHeight) {\n          style.lineHeight = `${height - outerHeight}px`;\n        } else if (height === targetHeight) {\n          style.lineHeight = computed.lineHeight;\n        } else {\n          style.lineHeight = \"0\";\n        }\n      } else {\n        style.lineHeight = computed.height;\n      }\n    } else {\n      style[prop] = computed[prop];\n    }\n  });\n  if (isFirefox()) {\n    if (element.scrollHeight > Number.parseInt(computed.height)) {\n      style.overflowY = \"scroll\";\n    }\n  } else {\n    style.overflow = \"hidden\";\n  }\n  div.textContent = element.value.slice(0, Math.max(0, position));\n  if (isInput && div.textContent) {\n    div.textContent = div.textContent.replace(/\\s/g, \"\\xA0\");\n  }\n  const span = document.createElement(\"span\");\n  span.textContent = element.value.slice(Math.max(0, position)) || \".\";\n  span.style.position = \"relative\";\n  span.style.left = `${-element.scrollLeft}px`;\n  span.style.top = `${-element.scrollTop}px`;\n  div.appendChild(span);\n  const relativePosition = {\n    top: span.offsetTop + Number.parseInt(computed.borderTopWidth),\n    left: span.offsetLeft + Number.parseInt(computed.borderLeftWidth),\n    height: Number.parseInt(computed.fontSize) * 1.5\n  };\n  if (options.debug) {\n    span.style.backgroundColor = \"#aaa\";\n  } else {\n    document.body.removeChild(div);\n  }\n  if (relativePosition.left >= element.clientWidth) {\n    relativePosition.left = element.clientWidth;\n  }\n  return relativePosition;\n};\nexport { filterOption, getCursorPosition, getMentionCtx };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}