{"ast": null, "code": "import { Promotion, Medal, Cpu, User, Message, Location } from \"@element-plus/icons-vue\";\nconst __default__ = {\n  name: \"AboutPage\"\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const values = [{\n      icon: Promotion,\n      title: \"创新驱动\",\n      description: \"持续创新，推动技术边界，为用户带来更好的产品体验\"\n    }, {\n      icon: Medal,\n      title: \"匠心品质\",\n      description: \"专注细节，精益求精，打造极致用户体验\"\n    }, {\n      icon: Cpu,\n      title: \"技术领先\",\n      description: \"拥抱新技术，保持技术敏锐度，引领行业发展\"\n    }, {\n      icon: User,\n      title: \"用户至上\",\n      description: \"以用户需求为中心，持续优化产品，提供贴心服务\"\n    }];\n    const __returned__ = {\n      values,\n      get Promotion() {\n        return Promotion;\n      },\n      get Medal() {\n        return Medal;\n      },\n      get Cpu() {\n        return Cpu;\n      },\n      get User() {\n        return User;\n      },\n      get Message() {\n        return Message;\n      },\n      get Location() {\n        return Location;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["Promotion", "Medal", "Cpu", "User", "Message", "Location", "__default__", "name", "values", "icon", "title", "description"], "sources": ["D:/codes/glmwebsite/geluman-website/src/views/About.vue"], "sourcesContent": ["<template>\r\n  <div class=\"about-page\">\r\n    <section class=\"about-hero\">\r\n      <div class=\"about-content\">\r\n        <h1 class=\"title\">关于格鲁曼</h1>\r\n        <p class=\"subtitle\">用创新驱动技术，以匠心创造价值</p>\r\n      </div>\r\n\r\n      <!-- 添加粒子网格背景 -->\r\n      <div class=\"hero-background\">\r\n        <!-- 科技感粒子 -->\r\n        <div class=\"tech-particles\">\r\n          <span\r\n            v-for=\"n in 30\"\r\n            :key=\"n\"\r\n            class=\"particle\"\r\n            :style=\"{\r\n              '--delay': `${Math.random() * 5}s`,\r\n              '--size': `${Math.random() * 3 + 1}px`,\r\n              '--x': `${Math.random() * 100}%`,\r\n              '--y': `${Math.random() * 100}%`,\r\n            }\"\r\n          ></span>\r\n        </div>\r\n        <!-- 网格背景 -->\r\n        <div class=\"grid-background\"></div>\r\n      </div>\r\n    </section>\r\n\r\n    <section class=\"company-intro\">\r\n      <div class=\"container\">\r\n        <div class=\"intro-grid\">\r\n          <div class=\"intro-text\">\r\n            <h2>我们的故事</h2>\r\n            <p>\r\n              成立于2024年，格鲁曼专注于开发浏览器插件和休闲小游戏，致力于为用户提供卓越的在线体验。\"匠心创造价值\"是我们的核心理念。我们相信，每一个细节都至关重要，只有通过精细打磨和不断优化，才能创造出真正有价值的产品。\r\n            </p>\r\n          </div>\r\n          <div class=\"intro-decoration\">\r\n            <div class=\"tech-circles\">\r\n              <span class=\"circle circle-1\"></span>\r\n              <span class=\"circle circle-2\"></span>\r\n              <span class=\"circle circle-3\"></span>\r\n            </div>\r\n            <div class=\"code-lines\">\r\n              <span class=\"line\"></span>\r\n              <span class=\"line\"></span>\r\n              <span class=\"line\"></span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <section class=\"values\">\r\n      <div class=\"container\">\r\n        <h2 class=\"section-title animate-fadeInUp\">我们的价值观</h2>\r\n        <div class=\"values-grid\">\r\n          <el-card\r\n            v-for=\"(value, index) in values\"\r\n            :key=\"value.title\"\r\n            class=\"value-card animate-fadeInUp\"\r\n            :style=\"{ animationDelay: `${(index + 1) * 0.2}s` }\"\r\n          >\r\n            <div class=\"value-icon animate-pulse\">\r\n              <el-icon :size=\"32\"><component :is=\"value.icon\" /></el-icon>\r\n            </div>\r\n            <h3>{{ value.title }}</h3>\r\n            <p>{{ value.description }}</p>\r\n          </el-card>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <section class=\"contact\">\r\n      <div class=\"container\">\r\n        <h2 class=\"section-title\">联系我们</h2>\r\n        <div class=\"contact-content\">\r\n          <div class=\"contact-info\">\r\n            <div class=\"info-item\">\r\n              <el-icon><Message /></el-icon>\r\n              <span><EMAIL></span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <el-icon><Location /></el-icon>\r\n              <span>中国·成都</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"AboutPage\",\r\n};\r\n</script>\r\n\r\n<script setup>\r\nimport {\r\n  Promotion,\r\n  Medal,\r\n  Cpu,\r\n  User,\r\n  Message,\r\n  Location,\r\n} from \"@element-plus/icons-vue\";\r\n\r\nconst values = [\r\n  {\r\n    icon: Promotion,\r\n    title: \"创新驱动\",\r\n    description: \"持续创新，推动技术边界，为用户带来更好的产品体验\",\r\n  },\r\n  {\r\n    icon: Medal,\r\n    title: \"匠心品质\",\r\n    description: \"专注细节，精益求精，打造极致用户体验\",\r\n  },\r\n  {\r\n    icon: Cpu,\r\n    title: \"技术领先\",\r\n    description: \"拥抱新技术，保持技术敏锐度，引领行业发展\",\r\n  },\r\n  {\r\n    icon: User,\r\n    title: \"用户至上\",\r\n    description: \"以用户需求为中心，持续优化产品，提供贴心服务\",\r\n  },\r\n];\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.about-page {\r\n  padding-top: var(--header-height);\r\n}\r\n\r\n.about-hero {\r\n  background: linear-gradient(\r\n    135deg,\r\n    var(--primary-color) 0%,\r\n    var(--primary-dark) 100%\r\n  );\r\n  padding: 6rem 0;\r\n  color: white;\r\n  text-align: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  .about-content {\r\n    position: relative;\r\n    z-index: 10;\r\n  }\r\n\r\n  .title {\r\n    font-size: 3rem;\r\n    margin-bottom: 1rem;\r\n    animation: fadeInUp 1s ease;\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 1.2rem;\r\n    opacity: 0.9;\r\n    animation: fadeInUp 1s ease 0.2s backwards;\r\n  }\r\n}\r\n\r\n.container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n.company-intro {\r\n  padding: 6rem 0;\r\n  background: var(--bg-secondary);\r\n\r\n  .intro-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr;\r\n    gap: 4rem;\r\n    align-items: center;\r\n\r\n    @media (max-width: 768px) {\r\n      grid-template-columns: 1fr;\r\n      gap: 2rem;\r\n    }\r\n  }\r\n\r\n  .intro-text {\r\n    h2 {\r\n      font-size: 2rem;\r\n      margin-bottom: 1.5rem;\r\n      color: var(--text-primary);\r\n    }\r\n\r\n    p {\r\n      color: var(--text-secondary);\r\n      line-height: 1.8;\r\n    }\r\n  }\r\n\r\n  .intro-decoration {\r\n    position: relative;\r\n    height: 300px;\r\n\r\n    .tech-circles {\r\n      position: absolute;\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n      .circle {\r\n        position: absolute;\r\n        border-radius: 50%;\r\n        background: var(--primary-color);\r\n        opacity: 0.1;\r\n        animation: float 6s infinite ease-in-out;\r\n\r\n        &.circle-1 {\r\n          width: 100px;\r\n          height: 100px;\r\n          top: 20%;\r\n          left: 20%;\r\n          animation-delay: 0s;\r\n        }\r\n\r\n        &.circle-2 {\r\n          width: 60px;\r\n          height: 60px;\r\n          top: 50%;\r\n          right: 30%;\r\n          animation-delay: -2s;\r\n        }\r\n\r\n        &.circle-3 {\r\n          width: 40px;\r\n          height: 40px;\r\n          bottom: 20%;\r\n          left: 40%;\r\n          animation-delay: -4s;\r\n        }\r\n      }\r\n    }\r\n\r\n    .code-lines {\r\n      position: absolute;\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n      .line {\r\n        position: absolute;\r\n        height: 2px;\r\n        background: linear-gradient(\r\n          90deg,\r\n          var(--primary-color) 0%,\r\n          transparent 100%\r\n        );\r\n        opacity: 0.2;\r\n        animation: slidein 3s infinite ease-in-out;\r\n\r\n        &:nth-child(1) {\r\n          width: 60%;\r\n          top: 30%;\r\n          left: -10%;\r\n          animation-delay: 0s;\r\n        }\r\n\r\n        &:nth-child(2) {\r\n          width: 40%;\r\n          top: 50%;\r\n          left: -10%;\r\n          animation-delay: -1s;\r\n        }\r\n\r\n        &:nth-child(3) {\r\n          width: 50%;\r\n          top: 70%;\r\n          left: -10%;\r\n          animation-delay: -2s;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.values {\r\n  padding: 6rem 0;\r\n\r\n  .section-title {\r\n    text-align: center;\r\n    font-size: 2rem;\r\n    margin-bottom: 3rem;\r\n  }\r\n\r\n  .values-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n    gap: 2rem;\r\n  }\r\n\r\n  .value-card {\r\n    text-align: center;\r\n    padding: 2rem;\r\n    transition: var(--transition-base);\r\n\r\n    &:hover {\r\n      transform: translateY(-5px);\r\n    }\r\n\r\n    .value-icon {\r\n      margin-bottom: 1.5rem;\r\n\r\n      .el-icon {\r\n        font-size: 2.5rem;\r\n        color: var(--primary-color);\r\n      }\r\n    }\r\n\r\n    h3 {\r\n      margin-bottom: 1rem;\r\n      color: var(--text-primary);\r\n    }\r\n\r\n    p {\r\n      color: var(--text-secondary);\r\n    }\r\n  }\r\n}\r\n\r\n.contact {\r\n  padding: 6rem 0;\r\n  background: var(--bg-secondary);\r\n\r\n  .section-title {\r\n    text-align: center;\r\n    font-size: 2rem;\r\n    margin-bottom: 3rem;\r\n  }\r\n\r\n  .contact-content {\r\n    max-width: 600px;\r\n    margin: 0 auto;\r\n  }\r\n\r\n  .contact-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 1.5rem;\r\n    align-items: center;\r\n\r\n    .info-item {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 0.5rem;\r\n      color: var(--text-secondary);\r\n\r\n      .el-icon {\r\n        color: var(--primary-color);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .about-hero {\r\n    padding: 4rem 1rem;\r\n\r\n    .title {\r\n      font-size: 2.5rem;\r\n    }\r\n  }\r\n\r\n  .company-intro,\r\n  .values,\r\n  .contact {\r\n    padding: 4rem 1rem;\r\n  }\r\n}\r\n\r\n.hero-background {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: hidden;\r\n  z-index: 0;\r\n}\r\n\r\n.tech-particles {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 2;\r\n\r\n  .particle {\r\n    position: absolute;\r\n    width: var(--size);\r\n    height: var(--size);\r\n    background: rgba(255, 255, 255, 0.5);\r\n    border-radius: 50%;\r\n    left: var(--x);\r\n    top: var(--y);\r\n    animation: pulse 2s infinite ease-in-out;\r\n    animation-delay: var(--delay);\r\n  }\r\n}\r\n\r\n.grid-background {\r\n  position: absolute;\r\n  width: 800%;\r\n  height: 800%;\r\n  background: linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),\r\n    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);\r\n  background-size: 50px 50px;\r\n  transform: rotate(45deg);\r\n  top: -350%;\r\n  left: -350%;\r\n  animation: grid-move 50s linear infinite;\r\n  pointer-events: none;\r\n}\r\n\r\n.value-card {\r\n  &:hover {\r\n    .value-icon {\r\n      animation: iconPop 0.5s ease;\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes particleMove {\r\n  0% {\r\n    background-position: 0% 0%;\r\n  }\r\n  100% {\r\n    background-position: 100% 100%;\r\n  }\r\n}\r\n\r\n@keyframes iconPop {\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.2);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n.animate-pulse {\r\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0%,\r\n  100% {\r\n    transform: scale(1);\r\n    opacity: 0.2;\r\n  }\r\n  50% {\r\n    transform: scale(1.5);\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n@keyframes float {\r\n  0%,\r\n  100% {\r\n    transform: translateY(0) scale(1);\r\n  }\r\n  50% {\r\n    transform: translateY(-20px) scale(1.1);\r\n  }\r\n}\r\n\r\n@keyframes slidein {\r\n  0% {\r\n    transform: translateX(-100%);\r\n    opacity: 0;\r\n  }\r\n  50% {\r\n    transform: translateX(0);\r\n    opacity: 0.2;\r\n  }\r\n  100% {\r\n    transform: translateX(100%);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n@keyframes grid-move {\r\n  0% {\r\n    transform: translate3d(-5%, -5%, 0) rotate(45deg);\r\n  }\r\n  100% {\r\n    transform: translate3d(-15%, -15%, 0) rotate(45deg);\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAqGA,SACEA,SAAS,EACTC,KAAK,EACLC,GAAG,EACHC,IAAI,EACJC,OAAO,EACPC,QAAQ,QACH,yBAAyB;AAbhC,MAAAC,WAAA,GAAe;EACbC,IAAI,EAAE;AACR,CAAC;;;;;;IAaD,MAAMC,MAAM,GAAG,CACb;MACEC,IAAI,EAAET,SAAS;MACfU,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,IAAI,EAAER,KAAK;MACXS,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,IAAI,EAAEP,GAAG;MACTQ,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,IAAI,EAAEN,IAAI;MACVO,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}