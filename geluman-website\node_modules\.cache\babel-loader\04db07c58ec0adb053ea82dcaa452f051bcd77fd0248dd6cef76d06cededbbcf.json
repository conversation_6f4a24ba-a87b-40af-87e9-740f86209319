{"ast": null, "code": "import { watch } from 'vue';\nimport { useEventListener } from '@vueuse/core';\nconst usePreventGlobal = (indicator, evt, cb) => {\n  const prevent = e => {\n    if (cb(e)) e.stopImmediatePropagation();\n  };\n  let stop = void 0;\n  watch(() => indicator.value, val => {\n    if (val) {\n      stop = useEventListener(document, evt, prevent, true);\n    } else {\n      stop == null ? void 0 : stop();\n    }\n  }, {\n    immediate: true\n  });\n};\nexport { usePreventGlobal };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}