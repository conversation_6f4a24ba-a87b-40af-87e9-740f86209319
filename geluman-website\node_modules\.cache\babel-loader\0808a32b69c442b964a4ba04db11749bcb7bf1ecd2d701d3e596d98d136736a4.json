{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, Fragment, createTextVNode, toDisplayString, renderSlot } from 'vue';\nimport { menuItemGroupProps } from './menu-item-group.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElMenuItemGroup\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: menuItemGroupProps,\n  setup(__props) {\n    const ns = useNamespace(\"menu-item-group\");\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"li\", {\n        class: normalizeClass(unref(ns).b())\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"title\"))\n      }, [!_ctx.$slots.title ? (openBlock(), createElementBlock(Fragment, {\n        key: 0\n      }, [createTextVNode(toDisplayString(_ctx.title), 1)], 64)) : renderSlot(_ctx.$slots, \"title\", {\n        key: 1\n      })], 2), createElementVNode(\"ul\", null, [renderSlot(_ctx.$slots, \"default\")])], 2);\n    };\n  }\n});\nvar MenuItemGroup = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"menu-item-group.vue\"]]);\nexport { MenuItemGroup as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}