{"ast": null, "code": "import assignValue from './_assignValue.js';\nimport castPath from './_castPath.js';\nimport isIndex from './_isIndex.js';\nimport isObject from './isObject.js';\nimport toKey from './_toKey.js';\n\n/**\n * The base implementation of `_.set`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to set.\n * @param {*} value The value to set.\n * @param {Function} [customizer] The function to customize path creation.\n * @returns {Object} Returns `object`.\n */\nfunction baseSet(object, path, value, customizer) {\n  if (!isObject(object)) {\n    return object;\n  }\n  path = castPath(path, object);\n  var index = -1,\n    length = path.length,\n    lastIndex = length - 1,\n    nested = object;\n  while (nested != null && ++index < length) {\n    var key = toKey(path[index]),\n      newValue = value;\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return object;\n    }\n    if (index != lastIndex) {\n      var objValue = nested[key];\n      newValue = customizer ? customizer(objValue, key, nested) : undefined;\n      if (newValue === undefined) {\n        newValue = isObject(objValue) ? objValue : isIndex(path[index + 1]) ? [] : {};\n      }\n    }\n    assignValue(nested, key, newValue);\n    nested = nested[key];\n  }\n  return object;\n}\nexport default baseSet;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}