{"ast": null, "code": "import { createElementVNode as _createElementVNode, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"product-page\"\n};\nconst _hoisted_2 = {\n  class: \"hero\"\n};\nconst _hoisted_3 = {\n  class: \"hero-content\"\n};\nconst _hoisted_4 = [\"src\"];\nconst _hoisted_5 = {\n  class: \"cta-buttons\"\n};\nconst _hoisted_6 = {\n  href: \"https://gengxin.geluman.cn/downloads/GLM-Translator-1.0.0.zip\",\n  class: \"download-btn\",\n  target: \"_blank\"\n};\nconst _hoisted_7 = {\n  class: \"demo\"\n};\nconst _hoisted_8 = {\n  class: \"container\"\n};\nconst _hoisted_9 = {\n  class: \"demo-gallery\"\n};\nconst _hoisted_10 = {\n  class: \"demo-item\"\n};\nconst _hoisted_11 = {\n  class: \"demo-image-container\"\n};\nconst _hoisted_12 = [\"src\"];\nconst _hoisted_13 = {\n  class: \"demo-item\"\n};\nconst _hoisted_14 = {\n  class: \"demo-image-container\"\n};\nconst _hoisted_15 = [\"src\"];\nconst _hoisted_16 = {\n  class: \"features\"\n};\nconst _hoisted_17 = {\n  class: \"container\"\n};\nconst _hoisted_18 = {\n  class: \"features-grid\"\n};\nconst _hoisted_19 = {\n  class: \"feature-icon\"\n};\nconst _hoisted_20 = {\n  class: \"usage\"\n};\nconst _hoisted_21 = {\n  class: \"container\"\n};\nconst _hoisted_22 = {\n  class: \"usage-steps\"\n};\nconst _hoisted_23 = {\n  class: \"step-number\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"img\", {\n    src: $setup.translator,\n    alt: \"翻译插件\",\n    class: \"plugin-logo\"\n  }, null, 8 /* PROPS */, _hoisted_4), _cache[1] || (_cache[1] = _createElementVNode(\"h1\", null, \"智能翻译助手\", -1 /* HOISTED */)), _cache[2] || (_cache[2] = _createElementVNode(\"p\", {\n    class: \"subtitle\"\n  }, \"高效、准确的网页文本翻译工具，帮助您跨越语言障碍\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"a\", _hoisted_6, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"Download\"])]),\n    _: 1 /* STABLE */\n  }), _cache[0] || (_cache[0] = _createTextVNode(\" 下载插件 \"))])])])]), _createElementVNode(\"section\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_cache[5] || (_cache[5] = _createElementVNode(\"h2\", {\n    class: \"section-title\"\n  }, \"产品演示\", -1 /* HOISTED */)), _cache[6] || (_cache[6] = _createElementVNode(\"p\", {\n    class: \"section-subtitle\"\n  }, \"简单易用的界面，强大的翻译功能\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"img\", {\n    src: $setup.translatorHome,\n    alt: \"智能翻译助手 - 翻译界面\",\n    class: \"demo-image\"\n  }, null, 8 /* PROPS */, _hoisted_12)]), _cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n    class: \"image-caption\"\n  }, \"智能翻译界面 - 选择文本即可快速翻译\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"img\", {\n    src: $setup.translatorSetting,\n    alt: \"智能翻译助手 - 设置界面\",\n    class: \"demo-image\"\n  }, null, 8 /* PROPS */, _hoisted_15)]), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n    class: \"image-caption\"\n  }, \"个性化设置 - 自定义翻译体验\", -1 /* HOISTED */))])])])]), _createElementVNode(\"section\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_cache[7] || (_cache[7] = _createElementVNode(\"h2\", null, \"核心功能\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_18, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList($setup.features, (feature, index) => {\n    return _createElementVNode(\"div\", {\n      key: feature.title,\n      class: \"feature-card\"\n    }, [_createElementVNode(\"div\", _hoisted_19, _toDisplayString(feature.icon), 1 /* TEXT */), _createElementVNode(\"h3\", null, _toDisplayString(feature.title), 1 /* TEXT */), _createElementVNode(\"p\", null, _toDisplayString(feature.description), 1 /* TEXT */)]);\n  }), 64 /* STABLE_FRAGMENT */))])])]), _createElementVNode(\"section\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_cache[8] || (_cache[8] = _createElementVNode(\"h2\", null, \"使用说明\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_22, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList($setup.steps, step => {\n    return _createElementVNode(\"div\", {\n      class: \"step\",\n      key: step.title\n    }, [_createElementVNode(\"div\", _hoisted_23, _toDisplayString(step.number), 1 /* TEXT */), _createElementVNode(\"h3\", null, _toDisplayString(step.title), 1 /* TEXT */), _createElementVNode(\"p\", null, _toDisplayString(step.description), 1 /* TEXT */)]);\n  }), 64 /* STABLE_FRAGMENT */))])])])]);\n}", "map": {"version": 3, "names": ["class", "href", "target", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "src", "$setup", "translator", "alt", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_createVNode", "_component_el_icon", "default", "_withCtx", "_", "_createTextVNode", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "translatorHome", "_hoisted_12", "_hoisted_13", "_hoisted_14", "translatorSetting", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_Fragment", "_renderList", "features", "feature", "index", "key", "title", "_hoisted_19", "_toDisplayString", "icon", "description", "_hoisted_20", "_hoisted_21", "_hoisted_22", "steps", "step", "_hoisted_23", "number"], "sources": ["D:\\codes\\glmwebsite\\geluman-website\\src\\views\\products\\Translator.vue"], "sourcesContent": ["<template>\n  <div class=\"product-page\">\n    <section class=\"hero\">\n      <div class=\"hero-content\">\n        <img :src=\"translator\" alt=\"翻译插件\" class=\"plugin-logo\" />\n        <h1>智能翻译助手</h1>\n        <p class=\"subtitle\">高效、准确的网页文本翻译工具，帮助您跨越语言障碍</p>\n        <div class=\"cta-buttons\">\n          <a\n            href=\"https://gengxin.geluman.cn/downloads/GLM-Translator-1.0.0.zip\"\n            class=\"download-btn\"\n            target=\"_blank\"\n          >\n            <el-icon><Download /></el-icon>\n            下载插件\n          </a>\n        </div>\n      </div>\n    </section>\n\n    <section class=\"demo\">\n      <div class=\"container\">\n        <h2 class=\"section-title\">产品演示</h2>\n        <p class=\"section-subtitle\">简单易用的界面，强大的翻译功能</p>\n        <div class=\"demo-gallery\">\n          <div class=\"demo-item\">\n            <div class=\"demo-image-container\">\n              <img\n                :src=\"translatorHome\"\n                alt=\"智能翻译助手 - 翻译界面\"\n                class=\"demo-image\"\n              />\n            </div>\n            <div class=\"image-caption\">智能翻译界面 - 选择文本即可快速翻译</div>\n          </div>\n          <div class=\"demo-item\">\n            <div class=\"demo-image-container\">\n              <img\n                :src=\"translatorSetting\"\n                alt=\"智能翻译助手 - 设置界面\"\n                class=\"demo-image\"\n              />\n            </div>\n            <div class=\"image-caption\">个性化设置 - 自定义翻译体验</div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <section class=\"features\">\n      <div class=\"container\">\n        <h2>核心功能</h2>\n        <div class=\"features-grid\">\n          <div\n            v-for=\"(feature, index) in features\"\n            :key=\"feature.title\"\n            class=\"feature-card\"\n          >\n            <div class=\"feature-icon\">{{ feature.icon }}</div>\n            <h3>{{ feature.title }}</h3>\n            <p>{{ feature.description }}</p>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <section class=\"usage\">\n      <div class=\"container\">\n        <h2>使用说明</h2>\n        <div class=\"usage-steps\">\n          <div class=\"step\" v-for=\"step in steps\" :key=\"step.title\">\n            <div class=\"step-number\">{{ step.number }}</div>\n            <h3>{{ step.title }}</h3>\n            <p>{{ step.description }}</p>\n          </div>\n        </div>\n      </div>\n    </section>\n  </div>\n</template>\n\n<script setup>\nimport { translator } from \"@/assets\";\nimport { Download } from \"@element-plus/icons-vue\";\nimport translatorHome from \"@/assets/img/translator-home.png\";\nimport translatorSetting from \"@/assets/img/translator-setting.png\";\n\nconst features = [\n  {\n    icon: \"🌐\",\n    title: \"多语言支持\",\n    description:\n      \"支持中英文、日语、法语等多国语言之间的互译，满足不同场景的翻译需求\",\n  },\n  {\n    icon: \"✂️\",\n    title: \"划词翻译\",\n    description: \"选中网页上的任意文本，即可一键获取翻译结果，快速理解外语内容\",\n  },\n  {\n    icon: \"📝\",\n    title: \"智能识别\",\n    description: \"自动识别文本语言，无需手动选择源语言，让翻译更加便捷高效\",\n  },\n  {\n    icon: \"🔄\",\n    title: \"实时翻译\",\n    description: \"选中文本后立即获取翻译结果，无需等待，提高阅读和学习效率\",\n  },\n  {\n    icon: \"🎛️\",\n    title: \"个性化设置\",\n    description: \"支持设置默认目标语言、翻译显示方式等，打造个性化翻译体验\",\n  },\n  {\n    icon: \"📱\",\n    title: \"跨平台兼容\",\n    description: \"支持Chrome、Edge等主流浏览器，保证在不同设备上的稳定运行\",\n  },\n];\n\nconst steps = [\n  {\n    number: \"1\",\n    title: \"安装插件\",\n    description:\n      \"从下方下载按钮下载并解压，打开Chrome或者Edge浏览器，进入chrome://extensions/或者edge://extensions/，将解压后的文件夹拖入页面中，完成安装\",\n  },\n  {\n    number: \"2\",\n    title: \"选择文本\",\n    description: \"在任意网页上选择需要翻译的文本内容\",\n  },\n  {\n    number: \"3\",\n    title: \"查看翻译\",\n    description: \"选中后自动显示翻译结果，或点击插件图标进行翻译\",\n  },\n  {\n    number: \"4\",\n    title: \"调整设置\",\n    description: \"在插件设置中可以自定义目标语言和显示方式\",\n  },\n];\n</script>\n\n<style lang=\"scss\" scoped>\n.hero {\n  background: linear-gradient(\n    135deg,\n    var(--primary-color) 0%,\n    var(--primary-dark) 100%\n  );\n  padding: 6rem 0;\n  text-align: center;\n  color: white;\n\n  .plugin-logo {\n    width: 120px;\n    height: auto;\n    margin-bottom: 2rem;\n  }\n\n  h1 {\n    font-size: 3rem;\n    margin-bottom: 1rem;\n  }\n\n  .subtitle {\n    font-size: 1.2rem;\n    opacity: 0.9;\n    margin-bottom: 2rem;\n  }\n\n  .download-btn {\n    display: inline-flex;\n    align-items: center;\n    gap: 0.8rem;\n    padding: 1rem 2rem;\n    background: white;\n    color: var(--primary-color);\n    text-decoration: none;\n    border-radius: 30px;\n    font-weight: 500;\n    font-size: 1.1rem;\n    transition: all 0.3s ease;\n    box-shadow: var(--shadow-md);\n\n    .el-icon {\n      font-size: 1.2rem;\n    }\n\n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: var(--shadow-lg);\n    }\n  }\n}\n\n.demo {\n  padding: 6rem 0;\n  background: white;\n\n  .container {\n    max-width: 1200px;\n    margin: 0 auto;\n    padding: 0 2rem;\n    text-align: center;\n  }\n\n  .section-title {\n    font-size: 2.5rem;\n    margin-bottom: 1rem;\n    color: var(--text-primary);\n  }\n\n  .section-subtitle {\n    font-size: 1.2rem;\n    color: var(--text-secondary);\n    margin-bottom: 3rem;\n    max-width: 800px;\n    margin-left: auto;\n    margin-right: auto;\n  }\n\n  .demo-gallery {\n    display: grid;\n    grid-template-columns: repeat(2, 1fr);\n    gap: 3rem;\n\n    @media (max-width: 992px) {\n      grid-template-columns: 1fr;\n      gap: 4rem;\n    }\n  }\n\n  .demo-item {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n\n    .demo-image-container {\n      width: 100%;\n      height: 350px;\n      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n      border-radius: 12px;\n      overflow: hidden;\n      transition: all 0.3s ease;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: #f8f9fa;\n\n      &:hover {\n        transform: translateY(-10px);\n        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);\n      }\n    }\n\n    .demo-image {\n      max-width: 100%;\n      max-height: 100%;\n      object-fit: contain;\n      display: block;\n      border-radius: 12px;\n    }\n\n    .image-caption {\n      margin-top: 1.5rem;\n      font-size: 1.1rem;\n      color: var(--text-secondary);\n    }\n  }\n}\n\n.features {\n  padding: 6rem 0;\n  background: var(--bg-secondary);\n\n  .container {\n    max-width: 1200px;\n    margin: 0 auto;\n    padding: 0 2rem;\n  }\n\n  h2 {\n    text-align: center;\n    font-size: 2.5rem;\n    margin-bottom: 3rem;\n  }\n\n  .features-grid {\n    display: grid;\n    grid-template-columns: repeat(3, 1fr);\n    gap: 2rem;\n\n    @media (max-width: 992px) {\n      grid-template-columns: repeat(2, 1fr);\n    }\n\n    @media (max-width: 768px) {\n      grid-template-columns: 1fr;\n    }\n  }\n}\n\n.feature-card {\n  background: white;\n  padding: 2rem;\n  border-radius: 20px;\n  text-align: center;\n  transition: var(--transition-base);\n\n  &:hover {\n    transform: translateY(-10px);\n\n    .feature-icon {\n      animation: iconBounce 0.5s ease;\n    }\n  }\n\n  .feature-icon {\n    font-size: 2.5rem;\n    margin-bottom: 1rem;\n  }\n\n  h3 {\n    font-size: 1.5rem;\n    margin-bottom: 1rem;\n    color: var(--text-primary);\n  }\n\n  p {\n    color: var(--text-secondary);\n    line-height: 1.6;\n  }\n}\n\n.usage {\n  padding: 6rem 0;\n\n  .container {\n    max-width: 1200px;\n    margin: 0 auto;\n    padding: 0 2rem;\n  }\n\n  h2 {\n    text-align: center;\n    font-size: 2.5rem;\n    margin-bottom: 3rem;\n  }\n\n  .usage-steps {\n    display: grid;\n    grid-template-columns: repeat(4, 1fr);\n    gap: 2rem;\n\n    @media (max-width: 992px) {\n      grid-template-columns: repeat(2, 1fr);\n    }\n\n    @media (max-width: 768px) {\n      grid-template-columns: 1fr;\n    }\n  }\n\n  .step {\n    text-align: center;\n    padding: 2rem;\n\n    .step-number {\n      width: 40px;\n      height: 40px;\n      background: var(--primary-color);\n      color: white;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin: 0 auto 1rem;\n      font-weight: 500;\n    }\n\n    h3 {\n      font-size: 1.3rem;\n      margin-bottom: 1rem;\n      color: var(--text-primary);\n    }\n\n    p {\n      color: var(--text-secondary);\n      line-height: 1.6;\n    }\n  }\n}\n\n@keyframes btnShine {\n  0% {\n    transform: translateX(-100%) translateY(-100%) rotate(45deg);\n  }\n  100% {\n    transform: translateX(100%) translateY(100%) rotate(45deg);\n  }\n}\n\n@keyframes iconBounce {\n  0%,\n  100% {\n    transform: translateY(0);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n}\n</style>\n\n<script>\nexport default {\n  name: \"TranslatorPage\",\n};\n</script>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAc;;EACdA,KAAK,EAAC;AAAM;;EACdA,KAAK,EAAC;AAAc;mBAH/B;;EAOaA,KAAK,EAAC;AAAa;;EAEpBC,IAAI,EAAC,+DAA+D;EACpED,KAAK,EAAC,cAAc;EACpBE,MAAM,EAAC;;;EASNF,KAAK,EAAC;AAAM;;EACdA,KAAK,EAAC;AAAW;;EAGfA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAsB;oBA1B7C;;EAmCeA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAsB;oBApC7C;;EAiDaA,KAAK,EAAC;AAAU;;EAClBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;EAMjBA,KAAK,EAAC;AAAc;;EAQxBA,KAAK,EAAC;AAAO;;EACfA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAa;;EAEfA,KAAK,EAAC;AAAa;;;uBAtElCG,mBAAA,CA6EM,OA7ENC,UA6EM,GA5EJC,mBAAA,CAgBU,WAhBVC,UAgBU,GAfRD,mBAAA,CAcM,OAdNE,UAcM,GAbJF,mBAAA,CAAwD;IAAlDG,GAAG,EAAEC,MAAA,CAAAC,UAAU;IAAEC,GAAG,EAAC,MAAM;IAACX,KAAK,EAAC;0BAJhDY,UAAA,G,0BAKQP,mBAAA,CAAe,YAAX,QAAM,sB,0BACVA,mBAAA,CAAgD;IAA7CL,KAAK,EAAC;EAAU,GAAC,0BAAwB,sBAC5CK,mBAAA,CASM,OATNQ,UASM,GARJR,mBAAA,CAOI,KAPJS,UAOI,GAFFC,YAAA,CAA+BC,kBAAA;IAb3CC,OAAA,EAAAC,QAAA,CAaqB,MAAY,CAAZH,YAAA,CAAYN,MAAA,c;IAbjCU,CAAA;gCAAAC,gBAAA,CAa2C,QAEjC,G,SAKNf,mBAAA,CA2BU,WA3BVgB,UA2BU,GA1BRhB,mBAAA,CAyBM,OAzBNiB,UAyBM,G,0BAxBJjB,mBAAA,CAAmC;IAA/BL,KAAK,EAAC;EAAe,GAAC,MAAI,sB,0BAC9BK,mBAAA,CAA+C;IAA5CL,KAAK,EAAC;EAAkB,GAAC,iBAAe,sBAC3CK,mBAAA,CAqBM,OArBNkB,UAqBM,GApBJlB,mBAAA,CASM,OATNmB,WASM,GARJnB,mBAAA,CAMM,OANNoB,WAMM,GALJpB,mBAAA,CAIE;IAHCG,GAAG,EAAEC,MAAA,CAAAiB,cAAc;IACpBf,GAAG,EAAC,eAAe;IACnBX,KAAK,EAAC;0BA9BtB2B,WAAA,E,6BAiCYtB,mBAAA,CAAoD;IAA/CL,KAAK,EAAC;EAAe,GAAC,qBAAmB,qB,GAEhDK,mBAAA,CASM,OATNuB,WASM,GARJvB,mBAAA,CAMM,OANNwB,WAMM,GALJxB,mBAAA,CAIE;IAHCG,GAAG,EAAEC,MAAA,CAAAqB,iBAAiB;IACvBnB,GAAG,EAAC,eAAe;IACnBX,KAAK,EAAC;0BAxCtB+B,WAAA,E,6BA2CY1B,mBAAA,CAAgD;IAA3CL,KAAK,EAAC;EAAe,GAAC,iBAAe,qB,SAMlDK,mBAAA,CAeU,WAfV2B,WAeU,GAdR3B,mBAAA,CAaM,OAbN4B,WAaM,G,0BAZJ5B,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAUM,OAVN6B,WAUM,I,cATJ/B,mBAAA,CAQMgC,SAAA,QA7DhBC,WAAA,CAsDuC3B,MAAA,CAAA4B,QAAQ,EAtD/C,CAsDoBC,OAAO,EAAEC,KAAK;WADxBlC,mBAAA,CAQM;MANHmC,GAAG,EAAEF,OAAO,CAACG,KAAK;MACnBzC,KAAK,EAAC;QAENK,mBAAA,CAAkD,OAAlDqC,WAAkD,EAAAC,gBAAA,CAArBL,OAAO,CAACM,IAAI,kBACzCvC,mBAAA,CAA4B,YAAAsC,gBAAA,CAArBL,OAAO,CAACG,KAAK,kBACpBpC,mBAAA,CAAgC,WAAAsC,gBAAA,CAA1BL,OAAO,CAACO,WAAW,iB;wCAMjCxC,mBAAA,CAWU,WAXVyC,WAWU,GAVRzC,mBAAA,CASM,OATN0C,WASM,G,0BARJ1C,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAMM,OANN2C,WAMM,I,cALJ7C,mBAAA,CAIMgC,SAAA,QA1EhBC,WAAA,CAsE2C3B,MAAA,CAAAwC,KAAK,EAAbC,IAAI;WAA7B7C,mBAAA,CAIM;MAJDL,KAAK,EAAC,MAAM;MAAwBwC,GAAG,EAAEU,IAAI,CAACT;QACjDpC,mBAAA,CAAgD,OAAhD8C,WAAgD,EAAAR,gBAAA,CAApBO,IAAI,CAACE,MAAM,kBACvC/C,mBAAA,CAAyB,YAAAsC,gBAAA,CAAlBO,IAAI,CAACT,KAAK,kBACjBpC,mBAAA,CAA6B,WAAAsC,gBAAA,CAAvBO,IAAI,CAACL,WAAW,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}