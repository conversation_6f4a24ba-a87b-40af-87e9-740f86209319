{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { defineComponent, getCurrentInstance, inject, watch, onUnmounted, h } from 'vue';\nimport useLayoutObserver from '../layout-observer.mjs';\nimport { removePopper } from '../util.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport useRender from './render-helper.mjs';\nimport defaultProps from './defaults.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { addClass, removeClass } from '../../../../utils/dom/style.mjs';\nimport { isClient } from '@vueuse/core';\nimport { rAF } from '../../../../utils/raf.mjs';\nvar TableBody = defineComponent({\n  name: \"ElTableBody\",\n  props: defaultProps,\n  setup(props) {\n    const instance = getCurrentInstance();\n    const parent = inject(TABLE_INJECTION_KEY);\n    const ns = useNamespace(\"table\");\n    const {\n      wrappedRowRender,\n      tooltipContent,\n      tooltipTrigger\n    } = useRender(props);\n    const {\n      onColumnsChange,\n      onScrollableChange\n    } = useLayoutObserver(parent);\n    const hoveredCellList = [];\n    watch(props.store.states.hoverRow, (newVal, oldVal) => {\n      var _a;\n      const el = instance == null ? void 0 : instance.vnode.el;\n      const rows = Array.from((el == null ? void 0 : el.children) || []).filter(e => e == null ? void 0 : e.classList.contains(`${ns.e(\"row\")}`));\n      let rowNum = newVal;\n      const childNodes = (_a = rows[rowNum]) == null ? void 0 : _a.childNodes;\n      if (childNodes == null ? void 0 : childNodes.length) {\n        let control = 0;\n        const indexes = Array.from(childNodes).reduce((acc, item, index) => {\n          var _a2, _b;\n          if (((_a2 = childNodes[index]) == null ? void 0 : _a2.colSpan) > 1) {\n            control = (_b = childNodes[index]) == null ? void 0 : _b.colSpan;\n          }\n          if (item.nodeName !== \"TD\" && control === 0) {\n            acc.push(index);\n          }\n          control > 0 && control--;\n          return acc;\n        }, []);\n        indexes.forEach(rowIndex => {\n          var _a2;\n          rowNum = newVal;\n          while (rowNum > 0) {\n            const preChildNodes = (_a2 = rows[rowNum - 1]) == null ? void 0 : _a2.childNodes;\n            if (preChildNodes[rowIndex] && preChildNodes[rowIndex].nodeName === \"TD\" && preChildNodes[rowIndex].rowSpan > 1) {\n              addClass(preChildNodes[rowIndex], \"hover-cell\");\n              hoveredCellList.push(preChildNodes[rowIndex]);\n              break;\n            }\n            rowNum--;\n          }\n        });\n      } else {\n        hoveredCellList.forEach(item => removeClass(item, \"hover-cell\"));\n        hoveredCellList.length = 0;\n      }\n      if (!props.store.states.isComplex.value || !isClient) return;\n      rAF(() => {\n        const oldRow = rows[oldVal];\n        const newRow = rows[newVal];\n        if (oldRow && !oldRow.classList.contains(\"hover-fixed-row\")) {\n          removeClass(oldRow, \"hover-row\");\n        }\n        if (newRow) {\n          addClass(newRow, \"hover-row\");\n        }\n      });\n    });\n    onUnmounted(() => {\n      var _a;\n      (_a = removePopper) == null ? void 0 : _a();\n    });\n    return {\n      ns,\n      onColumnsChange,\n      onScrollableChange,\n      wrappedRowRender,\n      tooltipContent,\n      tooltipTrigger\n    };\n  },\n  render() {\n    const {\n      wrappedRowRender,\n      store\n    } = this;\n    const data = store.states.data.value || [];\n    return h(\"tbody\", {\n      tabIndex: -1\n    }, [data.reduce((acc, row) => {\n      return acc.concat(wrappedRowRender(row, acc.length));\n    }, [])]);\n  }\n});\nexport { TableBody as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}