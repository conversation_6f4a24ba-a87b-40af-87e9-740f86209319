<template>
  <div class="product-page">
    <section class="hero">
      <div class="hero-content">
        <img :src="translator" alt="翻译插件" class="plugin-logo" />
        <h1>智能翻译助手</h1>
        <p class="subtitle">高效、准确的网页文本翻译工具，帮助您跨越语言障碍</p>
        <div class="cta-buttons">
          <a
            href="https://gengxin.geluman.cn/downloads/GLM-Translator-1.0.0.zip"
            class="download-btn"
            target="_blank"
          >
            <el-icon><Download /></el-icon>
            下载插件
          </a>
        </div>
      </div>
    </section>

    <section class="demo">
      <div class="container">
        <h2 class="section-title">产品演示</h2>
        <p class="section-subtitle">简单易用的界面，强大的翻译功能</p>
        <div class="demo-gallery">
          <div class="demo-item">
            <div class="demo-image-container">
              <img
                :src="translatorHome"
                alt="智能翻译助手 - 翻译界面"
                class="demo-image"
              />
            </div>
            <div class="image-caption">智能翻译界面 - 选择文本即可快速翻译</div>
          </div>
          <div class="demo-item">
            <div class="demo-image-container">
              <img
                :src="translatorSetting"
                alt="智能翻译助手 - 设置界面"
                class="demo-image"
              />
            </div>
            <div class="image-caption">个性化设置 - 自定义翻译体验</div>
          </div>
        </div>
      </div>
    </section>

    <section class="features">
      <div class="container">
        <h2>核心功能</h2>
        <div class="features-grid">
          <div
            v-for="(feature, index) in features"
            :key="feature.title"
            class="feature-card"
          >
            <div class="feature-icon">{{ feature.icon }}</div>
            <h3>{{ feature.title }}</h3>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <section class="usage">
      <div class="container">
        <h2>使用说明</h2>
        <div class="usage-steps">
          <div class="step" v-for="step in steps" :key="step.title">
            <div class="step-number">{{ step.number }}</div>
            <h3>{{ step.title }}</h3>
            <p>{{ step.description }}</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { translator } from "@/assets";
import { Download } from "@element-plus/icons-vue";
import translatorHome from "@/assets/img/translator-home.png";
import translatorSetting from "@/assets/img/translator-setting.png";

const features = [
  {
    icon: "🌐",
    title: "多语言支持",
    description:
      "支持中英文、日语、法语等多国语言之间的互译，满足不同场景的翻译需求",
  },
  {
    icon: "✂️",
    title: "划词翻译",
    description: "选中网页上的任意文本，即可一键获取翻译结果，快速理解外语内容",
  },
  {
    icon: "📝",
    title: "智能识别",
    description: "自动识别文本语言，无需手动选择源语言，让翻译更加便捷高效",
  },
  {
    icon: "🔄",
    title: "实时翻译",
    description: "选中文本后立即获取翻译结果，无需等待，提高阅读和学习效率",
  },
  {
    icon: "🎛️",
    title: "个性化设置",
    description: "支持设置默认目标语言、翻译显示方式等，打造个性化翻译体验",
  },
  {
    icon: "📱",
    title: "跨平台兼容",
    description: "支持Chrome、Edge等主流浏览器，保证在不同设备上的稳定运行",
  },
];

const steps = [
  {
    number: "1",
    title: "安装插件",
    description:
      "从下方下载按钮下载并解压，打开Chrome或者Edge浏览器，进入chrome://extensions/或者edge://extensions/，将解压后的文件夹拖入页面中，完成安装",
  },
  {
    number: "2",
    title: "选择文本",
    description: "在任意网页上选择需要翻译的文本内容",
  },
  {
    number: "3",
    title: "查看翻译",
    description: "选中后自动显示翻译结果，或点击插件图标进行翻译",
  },
  {
    number: "4",
    title: "调整设置",
    description: "在插件设置中可以自定义目标语言和显示方式",
  },
];
</script>

<style lang="scss" scoped>
.hero {
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-dark) 100%
  );
  padding: 6rem 0;
  text-align: center;
  color: white;

  .plugin-logo {
    width: 120px;
    height: auto;
    margin-bottom: 2rem;
  }

  h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
  }

  .download-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem 2rem;
    background: white;
    color: var(--primary-color);
    text-decoration: none;
    border-radius: 30px;
    font-weight: 500;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);

    .el-icon {
      font-size: 1.2rem;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
  }
}

.demo {
  padding: 6rem 0;
  background: white;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
  }

  .section-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
  }

  .section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }

  .demo-gallery {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3rem;

    @media (max-width: 992px) {
      grid-template-columns: 1fr;
      gap: 4rem;
    }
  }

  .demo-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .demo-image-container {
      width: 100%;
      height: 350px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      border-radius: 12px;
      overflow: hidden;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f8f9fa;

      &:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
      }
    }

    .demo-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      display: block;
      border-radius: 12px;
    }

    .image-caption {
      margin-top: 1.5rem;
      font-size: 1.1rem;
      color: var(--text-secondary);
    }
  }
}

.features {
  padding: 6rem 0;
  background: var(--bg-secondary);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;

    @media (max-width: 992px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
  transition: var(--transition-base);

  &:hover {
    transform: translateY(-10px);

    .feature-icon {
      animation: iconBounce 0.5s ease;
    }
  }

  .feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
  }

  p {
    color: var(--text-secondary);
    line-height: 1.6;
  }
}

.usage {
  padding: 6rem 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
  }

  .usage-steps {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;

    @media (max-width: 992px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .step {
    text-align: center;
    padding: 2rem;

    .step-number {
      width: 40px;
      height: 40px;
      background: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1rem;
      font-weight: 500;
    }

    h3 {
      font-size: 1.3rem;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    p {
      color: var(--text-secondary);
      line-height: 1.6;
    }
  }
}

@keyframes btnShine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

@keyframes iconBounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}
</style>

<script>
export default {
  name: "TranslatorPage",
};
</script>
