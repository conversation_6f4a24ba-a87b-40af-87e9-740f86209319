{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { inject } from 'vue';\nimport { getFixedColumnOffset, ensurePosition, getFixedColumnsClass } from '../util.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isFunction, isString } from '@vue/shared';\nfunction useStyle(props) {\n  const parent = inject(TABLE_INJECTION_KEY);\n  const ns = useNamespace(\"table\");\n  const getHeaderRowStyle = rowIndex => {\n    const headerRowStyle = parent == null ? void 0 : parent.props.headerRowStyle;\n    if (isFunction(headerRowStyle)) {\n      return headerRowStyle.call(null, {\n        rowIndex\n      });\n    }\n    return headerRowStyle;\n  };\n  const getHeaderRowClass = rowIndex => {\n    const classes = [];\n    const headerRowClassName = parent == null ? void 0 : parent.props.headerRowClassName;\n    if (isString(headerRowClassName)) {\n      classes.push(headerRowClassName);\n    } else if (isFunction(headerRowClassName)) {\n      classes.push(headerRowClassName.call(null, {\n        rowIndex\n      }));\n    }\n    return classes.join(\" \");\n  };\n  const getHeaderCellStyle = (rowIndex, columnIndex, row, column) => {\n    var _a;\n    let headerCellStyles = (_a = parent == null ? void 0 : parent.props.headerCellStyle) != null ? _a : {};\n    if (isFunction(headerCellStyles)) {\n      headerCellStyles = headerCellStyles.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column\n      });\n    }\n    const fixedStyle = getFixedColumnOffset(columnIndex, column.fixed, props.store, row);\n    ensurePosition(fixedStyle, \"left\");\n    ensurePosition(fixedStyle, \"right\");\n    return Object.assign({}, headerCellStyles, fixedStyle);\n  };\n  const getHeaderCellClass = (rowIndex, columnIndex, row, column) => {\n    const fixedClasses = getFixedColumnsClass(ns.b(), columnIndex, column.fixed, props.store, row);\n    const classes = [column.id, column.order, column.headerAlign, column.className, column.labelClassName, ...fixedClasses];\n    if (!column.children) {\n      classes.push(\"is-leaf\");\n    }\n    if (column.sortable) {\n      classes.push(\"is-sortable\");\n    }\n    const headerCellClassName = parent == null ? void 0 : parent.props.headerCellClassName;\n    if (isString(headerCellClassName)) {\n      classes.push(headerCellClassName);\n    } else if (isFunction(headerCellClassName)) {\n      classes.push(headerCellClassName.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column\n      }));\n    }\n    classes.push(ns.e(\"cell\"));\n    return classes.filter(className => Boolean(className)).join(\" \");\n  };\n  return {\n    getHeaderRowStyle,\n    getHeaderRowClass,\n    getHeaderCellStyle,\n    getHeaderCellClass\n  };\n}\nexport { useStyle as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}