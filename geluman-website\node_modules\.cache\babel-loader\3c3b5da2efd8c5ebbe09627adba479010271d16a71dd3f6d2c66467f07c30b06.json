{"ast": null, "code": "import { getCurrentInstance, inject, ref } from 'vue';\nimport { isNull } from 'lodash-unified';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport { isClient } from '@vueuse/core';\nimport { addClass, hasClass, removeClass } from '../../../../utils/dom/style.mjs';\nimport { isElement } from '../../../../utils/types.mjs';\nfunction useEvent(props, emit) {\n  const instance = getCurrentInstance();\n  const parent = inject(TABLE_INJECTION_KEY);\n  const handleFilterClick = event => {\n    event.stopPropagation();\n    return;\n  };\n  const handleHeaderClick = (event, column) => {\n    if (!column.filters && column.sortable) {\n      handleSortClick(event, column, false);\n    } else if (column.filterable && !column.sortable) {\n      handleFilterClick(event);\n    }\n    parent == null ? void 0 : parent.emit(\"header-click\", column, event);\n  };\n  const handleHeaderContextMenu = (event, column) => {\n    parent == null ? void 0 : parent.emit(\"header-contextmenu\", column, event);\n  };\n  const draggingColumn = ref(null);\n  const dragging = ref(false);\n  const dragState = ref({});\n  const handleMouseDown = (event, column) => {\n    if (!isClient) return;\n    if (column.children && column.children.length > 0) return;\n    if (draggingColumn.value && props.border) {\n      dragging.value = true;\n      const table = parent;\n      emit(\"set-drag-visible\", true);\n      const tableEl = table == null ? void 0 : table.vnode.el;\n      const tableLeft = tableEl.getBoundingClientRect().left;\n      const columnEl = instance.vnode.el.querySelector(`th.${column.id}`);\n      const columnRect = columnEl.getBoundingClientRect();\n      const minLeft = columnRect.left - tableLeft + 30;\n      addClass(columnEl, \"noclick\");\n      dragState.value = {\n        startMouseLeft: event.clientX,\n        startLeft: columnRect.right - tableLeft,\n        startColumnLeft: columnRect.left - tableLeft,\n        tableLeft\n      };\n      const resizeProxy = table == null ? void 0 : table.refs.resizeProxy;\n      resizeProxy.style.left = `${dragState.value.startLeft}px`;\n      document.onselectstart = function () {\n        return false;\n      };\n      document.ondragstart = function () {\n        return false;\n      };\n      const handleMouseMove2 = event2 => {\n        const deltaLeft = event2.clientX - dragState.value.startMouseLeft;\n        const proxyLeft = dragState.value.startLeft + deltaLeft;\n        resizeProxy.style.left = `${Math.max(minLeft, proxyLeft)}px`;\n      };\n      const handleMouseUp = () => {\n        if (dragging.value) {\n          const {\n            startColumnLeft,\n            startLeft\n          } = dragState.value;\n          const finalLeft = Number.parseInt(resizeProxy.style.left, 10);\n          const columnWidth = finalLeft - startColumnLeft;\n          column.width = column.realWidth = columnWidth;\n          table == null ? void 0 : table.emit(\"header-dragend\", column.width, startLeft - startColumnLeft, column, event);\n          requestAnimationFrame(() => {\n            props.store.scheduleLayout(false, true);\n          });\n          document.body.style.cursor = \"\";\n          dragging.value = false;\n          draggingColumn.value = null;\n          dragState.value = {};\n          emit(\"set-drag-visible\", false);\n        }\n        document.removeEventListener(\"mousemove\", handleMouseMove2);\n        document.removeEventListener(\"mouseup\", handleMouseUp);\n        document.onselectstart = null;\n        document.ondragstart = null;\n        setTimeout(() => {\n          removeClass(columnEl, \"noclick\");\n        }, 0);\n      };\n      document.addEventListener(\"mousemove\", handleMouseMove2);\n      document.addEventListener(\"mouseup\", handleMouseUp);\n    }\n  };\n  const handleMouseMove = (event, column) => {\n    var _a;\n    if (column.children && column.children.length > 0) return;\n    const el = event.target;\n    if (!isElement(el)) {\n      return;\n    }\n    const target = el == null ? void 0 : el.closest(\"th\");\n    if (!column || !column.resizable || !target) return;\n    if (!dragging.value && props.border) {\n      const rect = target.getBoundingClientRect();\n      const bodyStyle = document.body.style;\n      const isLastTh = ((_a = target.parentNode) == null ? void 0 : _a.lastElementChild) === target;\n      const allowDarg = props.allowDragLastColumn || !isLastTh;\n      if (rect.width > 12 && rect.right - event.clientX < 8 && allowDarg) {\n        bodyStyle.cursor = \"col-resize\";\n        if (hasClass(target, \"is-sortable\")) {\n          target.style.cursor = \"col-resize\";\n        }\n        draggingColumn.value = column;\n      } else if (!dragging.value) {\n        bodyStyle.cursor = \"\";\n        if (hasClass(target, \"is-sortable\")) {\n          target.style.cursor = \"pointer\";\n        }\n        draggingColumn.value = null;\n      }\n    }\n  };\n  const handleMouseOut = () => {\n    if (!isClient) return;\n    document.body.style.cursor = \"\";\n  };\n  const toggleOrder = ({\n    order,\n    sortOrders\n  }) => {\n    if (order === \"\") return sortOrders[0];\n    const index = sortOrders.indexOf(order || null);\n    return sortOrders[index > sortOrders.length - 2 ? 0 : index + 1];\n  };\n  const handleSortClick = (event, column, givenOrder) => {\n    var _a;\n    event.stopPropagation();\n    const order = column.order === givenOrder ? null : givenOrder || toggleOrder(column);\n    const target = (_a = event.target) == null ? void 0 : _a.closest(\"th\");\n    if (target) {\n      if (hasClass(target, \"noclick\")) {\n        removeClass(target, \"noclick\");\n        return;\n      }\n    }\n    if (!column.sortable) return;\n    const clickTarget = event.currentTarget;\n    if ([\"ascending\", \"descending\"].some(str => hasClass(clickTarget, str) && !column.sortOrders.includes(str))) {\n      return;\n    }\n    const states = props.store.states;\n    let sortProp = states.sortProp.value;\n    let sortOrder;\n    const sortingColumn = states.sortingColumn.value;\n    if (sortingColumn !== column || sortingColumn === column && isNull(sortingColumn.order)) {\n      if (sortingColumn) {\n        sortingColumn.order = null;\n      }\n      states.sortingColumn.value = column;\n      sortProp = column.property;\n    }\n    if (!order) {\n      sortOrder = column.order = null;\n    } else {\n      sortOrder = column.order = order;\n    }\n    states.sortProp.value = sortProp;\n    states.sortOrder.value = sortOrder;\n    parent == null ? void 0 : parent.store.commit(\"changeSortCondition\");\n  };\n  return {\n    handleHeaderClick,\n    handleHeaderContextMenu,\n    handleMouseDown,\n    handleMouseMove,\n    handleMouseOut,\n    handleSortClick,\n    handleFilterClick\n  };\n}\nexport { useEvent as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}