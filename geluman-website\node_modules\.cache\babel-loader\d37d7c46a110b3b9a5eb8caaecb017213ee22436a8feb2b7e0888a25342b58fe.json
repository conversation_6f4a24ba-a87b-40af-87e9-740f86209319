{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { computed } from 'vue';\nimport { debugWarn } from '../../../../utils/error.mjs';\nconst useStops = (props, initData, minValue, maxValue) => {\n  const stops = computed(() => {\n    if (!props.showStops || props.min > props.max) return [];\n    if (props.step === 0) {\n      debugWarn(\"ElSlider\", \"step should not be 0.\");\n      return [];\n    }\n    const stopCount = (props.max - props.min) / props.step;\n    const stepWidth = 100 * props.step / (props.max - props.min);\n    const result = Array.from({\n      length: stopCount - 1\n    }).map((_, index) => (index + 1) * stepWidth);\n    if (props.range) {\n      return result.filter(step => {\n        return step < 100 * (minValue.value - props.min) / (props.max - props.min) || step > 100 * (maxValue.value - props.min) / (props.max - props.min);\n      });\n    } else {\n      return result.filter(step => step > 100 * (initData.firstValue - props.min) / (props.max - props.min));\n    }\n  });\n  const getStopStyle = position => {\n    return props.vertical ? {\n      bottom: `${position}%`\n    } : {\n      left: `${position}%`\n    };\n  };\n  return {\n    stops,\n    getStopStyle\n  };\n};\nexport { useStops };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}