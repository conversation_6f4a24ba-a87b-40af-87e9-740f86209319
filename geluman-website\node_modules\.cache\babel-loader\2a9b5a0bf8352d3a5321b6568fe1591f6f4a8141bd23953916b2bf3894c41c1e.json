{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, unref, createVNode, mergeProps, inject, ref, computed, onMounted, nextTick } from 'vue';\nimport { tableV2RowProps } from '../row.mjs';\nimport { TableV2InjectionKey } from '../tokens.mjs';\nimport { placeholderSign } from '../private.mjs';\nimport { isArray, isFunction } from '@vue/shared';\nimport { isNumber } from '../../../../utils/types.mjs';\nconst useTableRow = props => {\n  const {\n    isScrolling\n  } = inject(TableV2InjectionKey);\n  const measured = ref(false);\n  const rowRef = ref();\n  const measurable = computed(() => {\n    return isNumber(props.estimatedRowHeight) && props.rowIndex >= 0;\n  });\n  const doMeasure = (isInit = false) => {\n    const $rowRef = unref(rowRef);\n    if (!$rowRef) return;\n    const {\n      columns,\n      onRowHeightChange,\n      rowKey,\n      rowIndex,\n      style\n    } = props;\n    const {\n      height\n    } = $rowRef.getBoundingClientRect();\n    measured.value = true;\n    nextTick(() => {\n      if (isInit || height !== Number.parseInt(style.height)) {\n        const firstColumn = columns[0];\n        const isPlaceholder = (firstColumn == null ? void 0 : firstColumn.placeholderSign) === placeholderSign;\n        onRowHeightChange == null ? void 0 : onRowHeightChange({\n          rowKey,\n          height,\n          rowIndex\n        }, firstColumn && !isPlaceholder && firstColumn.fixed);\n      }\n    });\n  };\n  const eventHandlers = computed(() => {\n    const {\n      rowData,\n      rowIndex,\n      rowKey,\n      onRowHover\n    } = props;\n    const handlers = props.rowEventHandlers || {};\n    const eventHandlers2 = {};\n    Object.entries(handlers).forEach(([eventName, handler]) => {\n      if (isFunction(handler)) {\n        eventHandlers2[eventName] = event => {\n          handler({\n            event,\n            rowData,\n            rowIndex,\n            rowKey\n          });\n        };\n      }\n    });\n    if (onRowHover) {\n      [{\n        name: \"onMouseleave\",\n        hovered: false\n      }, {\n        name: \"onMouseenter\",\n        hovered: true\n      }].forEach(({\n        name,\n        hovered\n      }) => {\n        const existedHandler = eventHandlers2[name];\n        eventHandlers2[name] = event => {\n          onRowHover({\n            event,\n            hovered,\n            rowData,\n            rowIndex,\n            rowKey\n          });\n          existedHandler == null ? void 0 : existedHandler(event);\n        };\n      });\n    }\n    return eventHandlers2;\n  });\n  const onExpand = expanded => {\n    const {\n      onRowExpand,\n      rowData,\n      rowIndex,\n      rowKey\n    } = props;\n    onRowExpand == null ? void 0 : onRowExpand({\n      expanded,\n      rowData,\n      rowIndex,\n      rowKey\n    });\n  };\n  onMounted(() => {\n    if (unref(measurable)) {\n      doMeasure(true);\n    }\n  });\n  return {\n    isScrolling,\n    measurable,\n    measured,\n    rowRef,\n    eventHandlers,\n    onExpand\n  };\n};\nconst COMPONENT_NAME = \"ElTableV2TableRow\";\nconst TableV2Row = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2RowProps,\n  setup(props, {\n    expose,\n    slots,\n    attrs\n  }) {\n    const {\n      eventHandlers,\n      isScrolling,\n      measurable,\n      measured,\n      rowRef,\n      onExpand\n    } = useTableRow(props);\n    expose({\n      onExpand\n    });\n    return () => {\n      const {\n        columns,\n        columnsStyles,\n        expandColumnKey,\n        depth,\n        rowData,\n        rowIndex,\n        style\n      } = props;\n      let ColumnCells = columns.map((column, columnIndex) => {\n        const expandable = isArray(rowData.children) && rowData.children.length > 0 && column.key === expandColumnKey;\n        return slots.cell({\n          column,\n          columns,\n          columnIndex,\n          depth,\n          style: columnsStyles[column.key],\n          rowData,\n          rowIndex,\n          isScrolling: unref(isScrolling),\n          expandIconProps: expandable ? {\n            rowData,\n            rowIndex,\n            onExpand\n          } : void 0\n        });\n      });\n      if (slots.row) {\n        ColumnCells = slots.row({\n          cells: ColumnCells.map(node => {\n            if (isArray(node) && node.length === 1) {\n              return node[0];\n            }\n            return node;\n          }),\n          style,\n          columns,\n          depth,\n          rowData,\n          rowIndex,\n          isScrolling: unref(isScrolling)\n        });\n      }\n      if (unref(measurable)) {\n        const {\n          height,\n          ...exceptHeightStyle\n        } = style || {};\n        const _measured = unref(measured);\n        return createVNode(\"div\", mergeProps({\n          \"ref\": rowRef,\n          \"class\": props.class,\n          \"style\": _measured ? style : exceptHeightStyle,\n          \"role\": \"row\"\n        }, attrs, unref(eventHandlers)), [ColumnCells]);\n      }\n      return createVNode(\"div\", mergeProps(attrs, {\n        \"ref\": rowRef,\n        \"class\": props.class,\n        \"style\": style,\n        \"role\": \"row\"\n      }, unref(eventHandlers)), [ColumnCells]);\n    };\n  }\n});\nvar Row = TableV2Row;\nexport { Row as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}