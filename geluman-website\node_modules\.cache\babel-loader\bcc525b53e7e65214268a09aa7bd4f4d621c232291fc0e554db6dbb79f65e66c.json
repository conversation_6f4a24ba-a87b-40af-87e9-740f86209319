{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, inject, ref, computed, unref, provide, nextTick, resolveComponent, openBlock, createBlock, withCtx, renderSlot } from 'vue';\nimport { ElCollectionItem, ROVING_FOCUS_COLLECTION_INJECTION_KEY as COLLECTION_INJECTION_KEY } from './roving-focus-group.mjs';\nimport { ROVING_FOCUS_GROUP_INJECTION_KEY, ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY } from './tokens.mjs';\nimport { getFocusIntent, reorderArray, focusFirst } from './utils.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { composeEventHandlers } from '../../../utils/dom/event.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nconst _sfc_main = defineComponent({\n  components: {\n    ElRovingFocusCollectionItem: ElCollectionItem\n  },\n  props: {\n    focusable: {\n      type: Boolean,\n      default: true\n    },\n    active: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: [\"mousedown\", \"focus\", \"keydown\"],\n  setup(props, {\n    emit\n  }) {\n    const {\n      currentTabbedId,\n      loop,\n      onItemFocus,\n      onItemShiftTab\n    } = inject(ROVING_FOCUS_GROUP_INJECTION_KEY, void 0);\n    const {\n      getItems\n    } = inject(COLLECTION_INJECTION_KEY, void 0);\n    const id = useId();\n    const rovingFocusGroupItemRef = ref();\n    const handleMousedown = composeEventHandlers(e => {\n      emit(\"mousedown\", e);\n    }, e => {\n      if (!props.focusable) {\n        e.preventDefault();\n      } else {\n        onItemFocus(unref(id));\n      }\n    });\n    const handleFocus = composeEventHandlers(e => {\n      emit(\"focus\", e);\n    }, () => {\n      onItemFocus(unref(id));\n    });\n    const handleKeydown = composeEventHandlers(e => {\n      emit(\"keydown\", e);\n    }, e => {\n      const {\n        code,\n        shiftKey,\n        target,\n        currentTarget\n      } = e;\n      if (code === EVENT_CODE.tab && shiftKey) {\n        onItemShiftTab();\n        return;\n      }\n      if (target !== currentTarget) return;\n      const focusIntent = getFocusIntent(e);\n      if (focusIntent) {\n        e.preventDefault();\n        const items = getItems().filter(item => item.focusable);\n        let elements = items.map(item => item.ref);\n        switch (focusIntent) {\n          case \"last\":\n            {\n              elements.reverse();\n              break;\n            }\n          case \"prev\":\n          case \"next\":\n            {\n              if (focusIntent === \"prev\") {\n                elements.reverse();\n              }\n              const currentIdx = elements.indexOf(currentTarget);\n              elements = loop.value ? reorderArray(elements, currentIdx + 1) : elements.slice(currentIdx + 1);\n              break;\n            }\n        }\n        nextTick(() => {\n          focusFirst(elements);\n        });\n      }\n    });\n    const isCurrentTab = computed(() => currentTabbedId.value === unref(id));\n    provide(ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY, {\n      rovingFocusGroupItemRef,\n      tabIndex: computed(() => unref(isCurrentTab) ? 0 : -1),\n      handleMousedown,\n      handleFocus,\n      handleKeydown\n    });\n    return {\n      id,\n      handleKeydown,\n      handleFocus,\n      handleMousedown\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_roving_focus_collection_item = resolveComponent(\"el-roving-focus-collection-item\");\n  return openBlock(), createBlock(_component_el_roving_focus_collection_item, {\n    id: _ctx.id,\n    focusable: _ctx.focusable,\n    active: _ctx.active\n  }, {\n    default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n    _: 3\n  }, 8, [\"id\", \"focusable\", \"active\"]);\n}\nvar ElRovingFocusItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"roving-focus-item.vue\"]]);\nexport { ElRovingFocusItem as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}