{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { componentSizes } from '../../../constants/size.mjs';\nconst formItemValidateStates = [\"\", \"error\", \"validating\", \"success\"];\nconst formItemProps = buildProps({\n  label: String,\n  labelWidth: {\n    type: [String, Number],\n    default: \"\"\n  },\n  labelPosition: {\n    type: String,\n    values: [\"left\", \"right\", \"top\", \"\"],\n    default: \"\"\n  },\n  prop: {\n    type: definePropType([String, Array])\n  },\n  required: {\n    type: Boolean,\n    default: void 0\n  },\n  rules: {\n    type: definePropType([Object, Array])\n  },\n  error: String,\n  validateStatus: {\n    type: String,\n    values: formItemValidateStates\n  },\n  for: String,\n  inlineMessage: {\n    type: [String, Boolean],\n    default: \"\"\n  },\n  showMessage: {\n    type: Boolean,\n    default: true\n  },\n  size: {\n    type: String,\n    values: componentSizes\n  }\n});\nexport { formItemProps, formItemValidateStates };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}