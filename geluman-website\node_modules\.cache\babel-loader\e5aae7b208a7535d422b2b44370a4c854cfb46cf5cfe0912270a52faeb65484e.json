{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, inject, computed, openBlock, createBlock, resolveDynamicComponent, normalizeClass, unref, normalizeStyle, withCtx, renderSlot } from 'vue';\nimport { colProps } from './col.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { rowContextKey } from '../../row/src/constants.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nimport { isObject } from '@vue/shared';\nconst __default__ = defineComponent({\n  name: \"ElCol\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: colProps,\n  setup(__props) {\n    const props = __props;\n    const {\n      gutter\n    } = inject(rowContextKey, {\n      gutter: computed(() => 0)\n    });\n    const ns = useNamespace(\"col\");\n    const style = computed(() => {\n      const styles = {};\n      if (gutter.value) {\n        styles.paddingLeft = styles.paddingRight = `${gutter.value / 2}px`;\n      }\n      return styles;\n    });\n    const colKls = computed(() => {\n      const classes = [];\n      const pos = [\"span\", \"offset\", \"pull\", \"push\"];\n      pos.forEach(prop => {\n        const size = props[prop];\n        if (isNumber(size)) {\n          if (prop === \"span\") classes.push(ns.b(`${props[prop]}`));else if (size > 0) classes.push(ns.b(`${prop}-${props[prop]}`));\n        }\n      });\n      const sizes = [\"xs\", \"sm\", \"md\", \"lg\", \"xl\"];\n      sizes.forEach(size => {\n        if (isNumber(props[size])) {\n          classes.push(ns.b(`${size}-${props[size]}`));\n        } else if (isObject(props[size])) {\n          Object.entries(props[size]).forEach(([prop, sizeProp]) => {\n            classes.push(prop !== \"span\" ? ns.b(`${size}-${prop}-${sizeProp}`) : ns.b(`${size}-${sizeProp}`));\n          });\n        }\n      });\n      if (gutter.value) {\n        classes.push(ns.is(\"guttered\"));\n      }\n      return [ns.b(), classes];\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(resolveDynamicComponent(_ctx.tag), {\n        class: normalizeClass(unref(colKls)),\n        style: normalizeStyle(unref(style))\n      }, {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 8, [\"class\", \"style\"]);\n    };\n  }\n});\nvar Col = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"col.vue\"]]);\nexport { Col as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}