{"ast": null, "code": "import { ref, onMounted, nextTick } from 'vue';\nimport { useEventListener } from '@vueuse/core';\nimport { isArray } from '@vue/shared';\nimport { isNumber } from '../../../../utils/types.mjs';\nconst useLifecycle = (props, initData, resetSize) => {\n  const sliderWrapper = ref();\n  onMounted(async () => {\n    if (props.range) {\n      if (isArray(props.modelValue)) {\n        initData.firstValue = Math.max(props.min, props.modelValue[0]);\n        initData.secondValue = Math.min(props.max, props.modelValue[1]);\n      } else {\n        initData.firstValue = props.min;\n        initData.secondValue = props.max;\n      }\n      initData.oldValue = [initData.firstValue, initData.secondValue];\n    } else {\n      if (!isNumber(props.modelValue) || Number.isNaN(props.modelValue)) {\n        initData.firstValue = props.min;\n      } else {\n        initData.firstValue = Math.min(props.max, Math.max(props.min, props.modelValue));\n      }\n      initData.oldValue = initData.firstValue;\n    }\n    useEventListener(window, \"resize\", resetSize);\n    await nextTick();\n    resetSize();\n  });\n  return {\n    sliderWrapper\n  };\n};\nexport { useLifecycle };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}