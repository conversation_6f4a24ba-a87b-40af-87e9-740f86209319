{"ast": null, "code": "import { panelRangeSharedProps } from './shared.mjs';\nimport { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nconst panelMonthRangeProps = buildProps({\n  ...panelRangeSharedProps\n});\nconst panelMonthRangeEmits = [\"pick\", \"set-picker-option\", \"calendar-change\"];\nexport { panelMonthRangeEmits, panelMonthRangeProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}