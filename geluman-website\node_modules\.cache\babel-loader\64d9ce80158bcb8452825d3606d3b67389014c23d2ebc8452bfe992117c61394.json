{"ast": null, "code": "import { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { isString } from '@vue/shared';\nconst inputProps = buildProps({\n  id: {\n    type: String,\n    default: void 0\n  },\n  size: useSizeProp,\n  disabled: Boolean,\n  modelValue: {\n    type: definePropType([String, Number, Object]),\n    default: \"\"\n  },\n  maxlength: {\n    type: [String, Number]\n  },\n  minlength: {\n    type: [String, Number]\n  },\n  type: {\n    type: String,\n    default: \"text\"\n  },\n  resize: {\n    type: String,\n    values: [\"none\", \"both\", \"horizontal\", \"vertical\"]\n  },\n  autosize: {\n    type: definePropType([Boolean, Object]),\n    default: false\n  },\n  autocomplete: {\n    type: String,\n    default: \"off\"\n  },\n  formatter: {\n    type: Function\n  },\n  parser: {\n    type: Function\n  },\n  placeholder: {\n    type: String\n  },\n  form: {\n    type: String\n  },\n  readonly: Boolean,\n  clearable: Boolean,\n  showPassword: Boolean,\n  showWordLimit: Boolean,\n  suffixIcon: {\n    type: iconPropType\n  },\n  prefixIcon: {\n    type: iconPropType\n  },\n  containerRole: {\n    type: String,\n    default: void 0\n  },\n  tabindex: {\n    type: [String, Number],\n    default: 0\n  },\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  inputStyle: {\n    type: definePropType([Object, Array, String]),\n    default: () => mutable({})\n  },\n  autofocus: Boolean,\n  rows: {\n    type: Number,\n    default: 2\n  },\n  ...useAriaProps([\"ariaLabel\"])\n});\nconst inputEmits = {\n  [UPDATE_MODEL_EVENT]: value => isString(value),\n  input: value => isString(value),\n  change: value => isString(value),\n  focus: evt => evt instanceof FocusEvent,\n  blur: evt => evt instanceof FocusEvent,\n  clear: () => true,\n  mouseleave: evt => evt instanceof MouseEvent,\n  mouseenter: evt => evt instanceof MouseEvent,\n  keydown: evt => evt instanceof Event,\n  compositionstart: evt => evt instanceof CompositionEvent,\n  compositionupdate: evt => evt instanceof CompositionEvent,\n  compositionend: evt => evt instanceof CompositionEvent\n};\nexport { inputEmits, inputProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}