{"ast": null, "code": "import { defineComponent, computed, h } from 'vue';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isString } from '@vue/shared';\nconst sliderMarkerProps = buildProps({\n  mark: {\n    type: definePropType([String, Object]),\n    default: void 0\n  }\n});\nvar SliderMarker = defineComponent({\n  name: \"ElSliderMarker\",\n  props: sliderMarkerProps,\n  setup(props) {\n    const ns = useNamespace(\"slider\");\n    const label = computed(() => {\n      return isString(props.mark) ? props.mark : props.mark.label;\n    });\n    const style = computed(() => isString(props.mark) ? void 0 : props.mark.style);\n    return () => h(\"div\", {\n      class: ns.e(\"marks-text\"),\n      style: style.value\n    }, label.value);\n  }\n});\nexport { SliderMarker as default, sliderMarkerProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}