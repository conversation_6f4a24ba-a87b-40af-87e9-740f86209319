{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, inject, ref, computed, markRaw, watch, openBlock, createElementBlock, unref, normalizeClass, normalizeStyle, Fragment, renderList, createVNode, withCtx, withDirectives, createBlock, resolveDynamicComponent, vShow, createCommentVNode, toDisplayString } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { rateProps, rateEmits } from './rate.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { formContextKey, formItemContextKey } from '../../form/src/constants.mjs';\nimport { useFormSize } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormItemInputId } from '../../form/src/hooks/use-form-item.mjs';\nimport { isArray, isObject, isString } from '@vue/shared';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { hasClass } from '../../../utils/dom/style.mjs';\nconst __default__ = defineComponent({\n  name: \"ElRate\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: rateProps,\n  emits: rateEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    function getValueFromMap(value, map) {\n      const isExcludedObject = val => isObject(val);\n      const matchedKeys = Object.keys(map).map(key => +key).filter(key => {\n        const val = map[key];\n        const excluded = isExcludedObject(val) ? val.excluded : false;\n        return excluded ? value < key : value <= key;\n      }).sort((a, b) => a - b);\n      const matchedValue = map[matchedKeys[0]];\n      return isExcludedObject(matchedValue) && matchedValue.value || matchedValue;\n    }\n    const formContext = inject(formContextKey, void 0);\n    const formItemContext = inject(formItemContextKey, void 0);\n    const rateSize = useFormSize();\n    const ns = useNamespace(\"rate\");\n    const {\n      inputId,\n      isLabeledByFormItem\n    } = useFormItemInputId(props, {\n      formItemContext\n    });\n    const currentValue = ref(props.modelValue);\n    const hoverIndex = ref(-1);\n    const pointerAtLeftHalf = ref(true);\n    const rateClasses = computed(() => [ns.b(), ns.m(rateSize.value)]);\n    const rateDisabled = computed(() => props.disabled || (formContext == null ? void 0 : formContext.disabled));\n    const rateStyles = computed(() => {\n      return ns.cssVarBlock({\n        \"void-color\": props.voidColor,\n        \"disabled-void-color\": props.disabledVoidColor,\n        \"fill-color\": activeColor.value\n      });\n    });\n    const text = computed(() => {\n      let result = \"\";\n      if (props.showScore) {\n        result = props.scoreTemplate.replace(/\\{\\s*value\\s*\\}/, rateDisabled.value ? `${props.modelValue}` : `${currentValue.value}`);\n      } else if (props.showText) {\n        result = props.texts[Math.ceil(currentValue.value) - 1];\n      }\n      return result;\n    });\n    const valueDecimal = computed(() => props.modelValue * 100 - Math.floor(props.modelValue) * 100);\n    const colorMap = computed(() => isArray(props.colors) ? {\n      [props.lowThreshold]: props.colors[0],\n      [props.highThreshold]: {\n        value: props.colors[1],\n        excluded: true\n      },\n      [props.max]: props.colors[2]\n    } : props.colors);\n    const activeColor = computed(() => {\n      const color = getValueFromMap(currentValue.value, colorMap.value);\n      return isObject(color) ? \"\" : color;\n    });\n    const decimalStyle = computed(() => {\n      let width = \"\";\n      if (rateDisabled.value) {\n        width = `${valueDecimal.value}%`;\n      } else if (props.allowHalf) {\n        width = \"50%\";\n      }\n      return {\n        color: activeColor.value,\n        width\n      };\n    });\n    const componentMap = computed(() => {\n      let icons = isArray(props.icons) ? [...props.icons] : {\n        ...props.icons\n      };\n      icons = markRaw(icons);\n      return isArray(icons) ? {\n        [props.lowThreshold]: icons[0],\n        [props.highThreshold]: {\n          value: icons[1],\n          excluded: true\n        },\n        [props.max]: icons[2]\n      } : icons;\n    });\n    const decimalIconComponent = computed(() => getValueFromMap(props.modelValue, componentMap.value));\n    const voidComponent = computed(() => rateDisabled.value ? isString(props.disabledVoidIcon) ? props.disabledVoidIcon : markRaw(props.disabledVoidIcon) : isString(props.voidIcon) ? props.voidIcon : markRaw(props.voidIcon));\n    const activeComponent = computed(() => getValueFromMap(currentValue.value, componentMap.value));\n    function showDecimalIcon(item) {\n      const showWhenDisabled = rateDisabled.value && valueDecimal.value > 0 && item - 1 < props.modelValue && item > props.modelValue;\n      const showWhenAllowHalf = props.allowHalf && pointerAtLeftHalf.value && item - 0.5 <= currentValue.value && item > currentValue.value;\n      return showWhenDisabled || showWhenAllowHalf;\n    }\n    function emitValue(value) {\n      if (props.clearable && value === props.modelValue) {\n        value = 0;\n      }\n      emit(UPDATE_MODEL_EVENT, value);\n      if (props.modelValue !== value) {\n        emit(CHANGE_EVENT, value);\n      }\n    }\n    function selectValue(value) {\n      if (rateDisabled.value) {\n        return;\n      }\n      if (props.allowHalf && pointerAtLeftHalf.value) {\n        emitValue(currentValue.value);\n      } else {\n        emitValue(value);\n      }\n    }\n    function handleKey(e) {\n      if (rateDisabled.value) {\n        return;\n      }\n      let _currentValue = currentValue.value;\n      const code = e.code;\n      if (code === EVENT_CODE.up || code === EVENT_CODE.right) {\n        if (props.allowHalf) {\n          _currentValue += 0.5;\n        } else {\n          _currentValue += 1;\n        }\n        e.stopPropagation();\n        e.preventDefault();\n      } else if (code === EVENT_CODE.left || code === EVENT_CODE.down) {\n        if (props.allowHalf) {\n          _currentValue -= 0.5;\n        } else {\n          _currentValue -= 1;\n        }\n        e.stopPropagation();\n        e.preventDefault();\n      }\n      _currentValue = _currentValue < 0 ? 0 : _currentValue;\n      _currentValue = _currentValue > props.max ? props.max : _currentValue;\n      emit(UPDATE_MODEL_EVENT, _currentValue);\n      emit(CHANGE_EVENT, _currentValue);\n      return _currentValue;\n    }\n    function setCurrentValue(value, event) {\n      if (rateDisabled.value) {\n        return;\n      }\n      if (props.allowHalf && event) {\n        let target = event.target;\n        if (hasClass(target, ns.e(\"item\"))) {\n          target = target.querySelector(`.${ns.e(\"icon\")}`);\n        }\n        if (target.clientWidth === 0 || hasClass(target, ns.e(\"decimal\"))) {\n          target = target.parentNode;\n        }\n        pointerAtLeftHalf.value = event.offsetX * 2 <= target.clientWidth;\n        currentValue.value = pointerAtLeftHalf.value ? value - 0.5 : value;\n      } else {\n        currentValue.value = value;\n      }\n      hoverIndex.value = value;\n    }\n    function resetCurrentValue() {\n      if (rateDisabled.value) {\n        return;\n      }\n      if (props.allowHalf) {\n        pointerAtLeftHalf.value = props.modelValue !== Math.floor(props.modelValue);\n      }\n      currentValue.value = props.modelValue;\n      hoverIndex.value = -1;\n    }\n    watch(() => props.modelValue, val => {\n      currentValue.value = val;\n      pointerAtLeftHalf.value = props.modelValue !== Math.floor(props.modelValue);\n    });\n    if (!props.modelValue) {\n      emit(UPDATE_MODEL_EVENT, 0);\n    }\n    expose({\n      setCurrentValue,\n      resetCurrentValue\n    });\n    return (_ctx, _cache) => {\n      var _a;\n      return openBlock(), createElementBlock(\"div\", {\n        id: unref(inputId),\n        class: normalizeClass([unref(rateClasses), unref(ns).is(\"disabled\", unref(rateDisabled))]),\n        role: \"slider\",\n        \"aria-label\": !unref(isLabeledByFormItem) ? _ctx.ariaLabel || \"rating\" : void 0,\n        \"aria-labelledby\": unref(isLabeledByFormItem) ? (_a = unref(formItemContext)) == null ? void 0 : _a.labelId : void 0,\n        \"aria-valuenow\": currentValue.value,\n        \"aria-valuetext\": unref(text) || void 0,\n        \"aria-valuemin\": \"0\",\n        \"aria-valuemax\": _ctx.max,\n        tabindex: \"0\",\n        style: normalizeStyle(unref(rateStyles)),\n        onKeydown: handleKey\n      }, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.max, (item, key) => {\n        return openBlock(), createElementBlock(\"span\", {\n          key,\n          class: normalizeClass(unref(ns).e(\"item\")),\n          onMousemove: $event => setCurrentValue(item, $event),\n          onMouseleave: resetCurrentValue,\n          onClick: $event => selectValue(item)\n        }, [createVNode(unref(ElIcon), {\n          class: normalizeClass([unref(ns).e(\"icon\"), {\n            hover: hoverIndex.value === item\n          }, unref(ns).is(\"active\", item <= currentValue.value)])\n        }, {\n          default: withCtx(() => [!showDecimalIcon(item) ? (openBlock(), createElementBlock(Fragment, {\n            key: 0\n          }, [withDirectives((openBlock(), createBlock(resolveDynamicComponent(unref(activeComponent)), null, null, 512)), [[vShow, item <= currentValue.value]]), withDirectives((openBlock(), createBlock(resolveDynamicComponent(unref(voidComponent)), null, null, 512)), [[vShow, !(item <= currentValue.value)]])], 64)) : createCommentVNode(\"v-if\", true), showDecimalIcon(item) ? (openBlock(), createElementBlock(Fragment, {\n            key: 1\n          }, [(openBlock(), createBlock(resolveDynamicComponent(unref(voidComponent)), {\n            class: normalizeClass([unref(ns).em(\"decimal\", \"box\")])\n          }, null, 8, [\"class\"])), createVNode(unref(ElIcon), {\n            style: normalizeStyle(unref(decimalStyle)),\n            class: normalizeClass([unref(ns).e(\"icon\"), unref(ns).e(\"decimal\")])\n          }, {\n            default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(decimalIconComponent))))]),\n            _: 1\n          }, 8, [\"style\", \"class\"])], 64)) : createCommentVNode(\"v-if\", true)]),\n          _: 2\n        }, 1032, [\"class\"])], 42, [\"onMousemove\", \"onClick\"]);\n      }), 128)), _ctx.showText || _ctx.showScore ? (openBlock(), createElementBlock(\"span\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"text\")),\n        style: normalizeStyle({\n          color: _ctx.textColor\n        })\n      }, toDisplayString(unref(text)), 7)) : createCommentVNode(\"v-if\", true)], 46, [\"id\", \"aria-label\", \"aria-labelledby\", \"aria-valuenow\", \"aria-valuetext\", \"aria-valuemax\"]);\n    };\n  }\n});\nvar Rate = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"rate.vue\"]]);\nexport { Rate as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}