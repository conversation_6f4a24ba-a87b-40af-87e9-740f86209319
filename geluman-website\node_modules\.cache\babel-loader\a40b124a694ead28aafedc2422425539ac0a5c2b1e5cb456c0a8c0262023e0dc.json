{"ast": null, "code": "import { createVNode, mergeProps, isVNode } from 'vue';\nimport Table from '../table-grid.mjs';\nfunction _isSlot(s) {\n  return typeof s === \"function\" || Object.prototype.toString.call(s) === \"[object Object]\" && !isVNode(s);\n}\nconst MainTable = (props, {\n  slots\n}) => {\n  const {\n    mainTableRef,\n    ...rest\n  } = props;\n  return createVNode(Table, mergeProps({\n    \"ref\": mainTableRef\n  }, rest), _isSlot(slots) ? slots : {\n    default: () => [slots]\n  });\n};\nvar MainTable$1 = MainTable;\nexport { MainTable$1 as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}