{"ast": null, "code": "import { defineComponent, ref, computed, watch, nextTick, provide, reactive, onActivated, onMounted, onUpdated, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, normalizeStyle, createBlock, resolveDynamicComponent, withCtx, renderSlot, createCommentVNode } from 'vue';\nimport { useResizeObserver, useEventListener } from '@vueuse/core';\nimport Bar from './bar2.mjs';\nimport { scrollbarContextKey } from './constants.mjs';\nimport { scrollbarProps, scrollbarEmits } from './scrollbar.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nimport { isObject } from '@vue/shared';\nimport { isNumber } from '../../../utils/types.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nconst COMPONENT_NAME = \"ElScrollbar\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: scrollbarProps,\n  emits: scrollbarEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"scrollbar\");\n    let stopResizeObserver = void 0;\n    let stopResizeListener = void 0;\n    let wrapScrollTop = 0;\n    let wrapScrollLeft = 0;\n    const scrollbarRef = ref();\n    const wrapRef = ref();\n    const resizeRef = ref();\n    const barRef = ref();\n    const wrapStyle = computed(() => {\n      const style = {};\n      if (props.height) style.height = addUnit(props.height);\n      if (props.maxHeight) style.maxHeight = addUnit(props.maxHeight);\n      return [props.wrapStyle, style];\n    });\n    const wrapKls = computed(() => {\n      return [props.wrapClass, ns.e(\"wrap\"), {\n        [ns.em(\"wrap\", \"hidden-default\")]: !props.native\n      }];\n    });\n    const resizeKls = computed(() => {\n      return [ns.e(\"view\"), props.viewClass];\n    });\n    const handleScroll = () => {\n      var _a;\n      if (wrapRef.value) {\n        (_a = barRef.value) == null ? void 0 : _a.handleScroll(wrapRef.value);\n        wrapScrollTop = wrapRef.value.scrollTop;\n        wrapScrollLeft = wrapRef.value.scrollLeft;\n        emit(\"scroll\", {\n          scrollTop: wrapRef.value.scrollTop,\n          scrollLeft: wrapRef.value.scrollLeft\n        });\n      }\n    };\n    function scrollTo(arg1, arg2) {\n      if (isObject(arg1)) {\n        wrapRef.value.scrollTo(arg1);\n      } else if (isNumber(arg1) && isNumber(arg2)) {\n        wrapRef.value.scrollTo(arg1, arg2);\n      }\n    }\n    const setScrollTop = value => {\n      if (!isNumber(value)) {\n        debugWarn(COMPONENT_NAME, \"value must be a number\");\n        return;\n      }\n      wrapRef.value.scrollTop = value;\n    };\n    const setScrollLeft = value => {\n      if (!isNumber(value)) {\n        debugWarn(COMPONENT_NAME, \"value must be a number\");\n        return;\n      }\n      wrapRef.value.scrollLeft = value;\n    };\n    const update = () => {\n      var _a;\n      (_a = barRef.value) == null ? void 0 : _a.update();\n    };\n    watch(() => props.noresize, noresize => {\n      if (noresize) {\n        stopResizeObserver == null ? void 0 : stopResizeObserver();\n        stopResizeListener == null ? void 0 : stopResizeListener();\n      } else {\n        ({\n          stop: stopResizeObserver\n        } = useResizeObserver(resizeRef, update));\n        stopResizeListener = useEventListener(\"resize\", update);\n      }\n    }, {\n      immediate: true\n    });\n    watch(() => [props.maxHeight, props.height], () => {\n      if (!props.native) nextTick(() => {\n        var _a;\n        update();\n        if (wrapRef.value) {\n          (_a = barRef.value) == null ? void 0 : _a.handleScroll(wrapRef.value);\n        }\n      });\n    });\n    provide(scrollbarContextKey, reactive({\n      scrollbarElement: scrollbarRef,\n      wrapElement: wrapRef\n    }));\n    onActivated(() => {\n      if (wrapRef.value) {\n        wrapRef.value.scrollTop = wrapScrollTop;\n        wrapRef.value.scrollLeft = wrapScrollLeft;\n      }\n    });\n    onMounted(() => {\n      if (!props.native) nextTick(() => {\n        update();\n      });\n    });\n    onUpdated(() => update());\n    expose({\n      wrapRef,\n      update,\n      scrollTo,\n      setScrollTop,\n      setScrollLeft,\n      handleScroll\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"scrollbarRef\",\n        ref: scrollbarRef,\n        class: normalizeClass(unref(ns).b())\n      }, [createElementVNode(\"div\", {\n        ref_key: \"wrapRef\",\n        ref: wrapRef,\n        class: normalizeClass(unref(wrapKls)),\n        style: normalizeStyle(unref(wrapStyle)),\n        tabindex: _ctx.tabindex,\n        onScroll: handleScroll\n      }, [(openBlock(), createBlock(resolveDynamicComponent(_ctx.tag), {\n        id: _ctx.id,\n        ref_key: \"resizeRef\",\n        ref: resizeRef,\n        class: normalizeClass(unref(resizeKls)),\n        style: normalizeStyle(_ctx.viewStyle),\n        role: _ctx.role,\n        \"aria-label\": _ctx.ariaLabel,\n        \"aria-orientation\": _ctx.ariaOrientation\n      }, {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 8, [\"id\", \"class\", \"style\", \"role\", \"aria-label\", \"aria-orientation\"]))], 46, [\"tabindex\"]), !_ctx.native ? (openBlock(), createBlock(Bar, {\n        key: 0,\n        ref_key: \"barRef\",\n        ref: barRef,\n        always: _ctx.always,\n        \"min-size\": _ctx.minSize\n      }, null, 8, [\"always\", \"min-size\"])) : createCommentVNode(\"v-if\", true)], 2);\n    };\n  }\n});\nvar Scrollbar = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"scrollbar.vue\"]]);\nexport { Scrollbar as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}