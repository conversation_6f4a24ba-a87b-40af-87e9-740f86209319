{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { watch } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport useStore from './index.mjs';\nimport { isObject } from '@vue/shared';\nconst InitialStateMap = {\n  rowKey: \"rowKey\",\n  defaultExpandAll: \"defaultExpandAll\",\n  selectOnIndeterminate: \"selectOnIndeterminate\",\n  indent: \"indent\",\n  lazy: \"lazy\",\n  data: \"data\",\n  [\"treeProps.hasChildren\"]: {\n    key: \"lazyColumnIdentifier\",\n    default: \"hasChildren\"\n  },\n  [\"treeProps.children\"]: {\n    key: \"childrenColumnName\",\n    default: \"children\"\n  },\n  [\"treeProps.checkStrictly\"]: {\n    key: \"checkStrictly\",\n    default: false\n  }\n};\nfunction createStore(table, props) {\n  if (!table) {\n    throw new Error(\"Table is required.\");\n  }\n  const store = useStore();\n  store.toggleAllSelection = debounce(store._toggleAllSelection, 10);\n  Object.keys(InitialStateMap).forEach(key => {\n    handleValue(getArrKeysValue(props, key), key, store);\n  });\n  proxyTableProps(store, props);\n  return store;\n}\nfunction proxyTableProps(store, props) {\n  Object.keys(InitialStateMap).forEach(key => {\n    watch(() => getArrKeysValue(props, key), value => {\n      handleValue(value, key, store);\n    });\n  });\n}\nfunction handleValue(value, propsKey, store) {\n  let newVal = value;\n  let storeKey = InitialStateMap[propsKey];\n  if (isObject(InitialStateMap[propsKey])) {\n    storeKey = storeKey.key;\n    newVal = newVal || InitialStateMap[propsKey].default;\n  }\n  store.states[storeKey].value = newVal;\n}\nfunction getArrKeysValue(props, keys) {\n  if (keys.includes(\".\")) {\n    const keyList = keys.split(\".\");\n    let value = props;\n    keyList.forEach(key => {\n      value = value[key];\n    });\n    return value;\n  } else {\n    return props[keys];\n  }\n}\nexport { createStore };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}