{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { inject, computed } from 'vue';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nconst getAllColumns = columns => {\n  const result = [];\n  columns.forEach(column => {\n    if (column.children) {\n      result.push(column);\n      result.push.apply(result, getAllColumns(column.children));\n    } else {\n      result.push(column);\n    }\n  });\n  return result;\n};\nconst convertToRows = originColumns => {\n  let maxLevel = 1;\n  const traverse = (column, parent) => {\n    if (parent) {\n      column.level = parent.level + 1;\n      if (maxLevel < column.level) {\n        maxLevel = column.level;\n      }\n    }\n    if (column.children) {\n      let colSpan = 0;\n      column.children.forEach(subColumn => {\n        traverse(subColumn, column);\n        colSpan += subColumn.colSpan;\n      });\n      column.colSpan = colSpan;\n    } else {\n      column.colSpan = 1;\n    }\n  };\n  originColumns.forEach(column => {\n    column.level = 1;\n    traverse(column, void 0);\n  });\n  const rows = [];\n  for (let i = 0; i < maxLevel; i++) {\n    rows.push([]);\n  }\n  const allColumns = getAllColumns(originColumns);\n  allColumns.forEach(column => {\n    if (!column.children) {\n      column.rowSpan = maxLevel - column.level + 1;\n    } else {\n      column.rowSpan = 1;\n      column.children.forEach(col => col.isSubColumn = true);\n    }\n    rows[column.level - 1].push(column);\n  });\n  return rows;\n};\nfunction useUtils(props) {\n  const parent = inject(TABLE_INJECTION_KEY);\n  const columnRows = computed(() => {\n    return convertToRows(props.store.states.originColumns.value);\n  });\n  const isGroup = computed(() => {\n    const result = columnRows.value.length > 1;\n    if (result && parent) {\n      parent.state.isGroup.value = true;\n    }\n    return result;\n  });\n  const toggleAllSelection = event => {\n    event.stopPropagation();\n    parent == null ? void 0 : parent.store.commit(\"toggleAllSelection\");\n  };\n  return {\n    isGroup,\n    toggleAllSelection,\n    columnRows\n  };\n}\nexport { convertToRows, useUtils as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}