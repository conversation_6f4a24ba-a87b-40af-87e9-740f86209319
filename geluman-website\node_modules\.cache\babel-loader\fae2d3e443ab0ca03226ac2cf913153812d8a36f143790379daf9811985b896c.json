{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, inject } from 'vue';\nimport { isEqual } from 'lodash-unified';\nimport { selectKey } from './token.mjs';\nimport { isArray, isString, isFunction } from '@vue/shared';\nvar ElOptions = defineComponent({\n  name: \"ElOptions\",\n  setup(_, {\n    slots\n  }) {\n    const select = inject(selectKey);\n    let cachedValueList = [];\n    return () => {\n      var _a, _b;\n      const children = (_a = slots.default) == null ? void 0 : _a.call(slots);\n      const valueList = [];\n      function filterOptions(children2) {\n        if (!isArray(children2)) return;\n        children2.forEach(item => {\n          var _a2, _b2, _c, _d;\n          const name = (_a2 = (item == null ? void 0 : item.type) || {}) == null ? void 0 : _a2.name;\n          if (name === \"ElOptionGroup\") {\n            filterOptions(!isString(item.children) && !isArray(item.children) && isFunction((_b2 = item.children) == null ? void 0 : _b2.default) ? (_c = item.children) == null ? void 0 : _c.default() : item.children);\n          } else if (name === \"ElOption\") {\n            valueList.push((_d = item.props) == null ? void 0 : _d.value);\n          } else if (isArray(item.children)) {\n            filterOptions(item.children);\n          }\n        });\n      }\n      if (children.length) {\n        filterOptions((_b = children[0]) == null ? void 0 : _b.children);\n      }\n      if (!isEqual(valueList, cachedValueList)) {\n        cachedValueList = valueList;\n        if (select) {\n          select.states.optionValues = valueList;\n        }\n      }\n      return children;\n    };\n  }\n});\nexport { ElOptions as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}