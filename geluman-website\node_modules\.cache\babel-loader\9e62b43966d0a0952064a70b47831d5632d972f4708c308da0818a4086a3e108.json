{"ast": null, "code": "import { defineComponent, shallowRef, ref, computed, watch, onMounted, watchEffect, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, createElementVNode, renderSlot, nextTick } from 'vue';\nimport { useWindowSize, useElementBounding, useEventListener } from '@vueuse/core';\nimport { affixProps, affixEmits } from './affix.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { getScrollContainer } from '../../../utils/dom/scroll.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nimport { CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nconst COMPONENT_NAME = \"ElAffix\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: affixProps,\n  emits: affixEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"affix\");\n    const target = shallowRef();\n    const root = shallowRef();\n    const scrollContainer = shallowRef();\n    const {\n      height: windowHeight\n    } = useWindowSize();\n    const {\n      height: rootHeight,\n      width: rootWidth,\n      top: rootTop,\n      bottom: rootBottom,\n      update: updateRoot\n    } = useElementBounding(root, {\n      windowScroll: false\n    });\n    const targetRect = useElementBounding(target);\n    const fixed = ref(false);\n    const scrollTop = ref(0);\n    const transform = ref(0);\n    const rootStyle = computed(() => {\n      return {\n        height: fixed.value ? `${rootHeight.value}px` : \"\",\n        width: fixed.value ? `${rootWidth.value}px` : \"\"\n      };\n    });\n    const affixStyle = computed(() => {\n      if (!fixed.value) return {};\n      const offset = props.offset ? addUnit(props.offset) : 0;\n      return {\n        height: `${rootHeight.value}px`,\n        width: `${rootWidth.value}px`,\n        top: props.position === \"top\" ? offset : \"\",\n        bottom: props.position === \"bottom\" ? offset : \"\",\n        transform: transform.value ? `translateY(${transform.value}px)` : \"\",\n        zIndex: props.zIndex\n      };\n    });\n    const update = () => {\n      if (!scrollContainer.value) return;\n      scrollTop.value = scrollContainer.value instanceof Window ? document.documentElement.scrollTop : scrollContainer.value.scrollTop || 0;\n      const {\n        position,\n        target: target2,\n        offset\n      } = props;\n      const rootHeightOffset = offset + rootHeight.value;\n      if (position === \"top\") {\n        if (target2) {\n          const difference = targetRect.bottom.value - rootHeightOffset;\n          fixed.value = offset > rootTop.value && targetRect.bottom.value > 0;\n          transform.value = difference < 0 ? difference : 0;\n        } else {\n          fixed.value = offset > rootTop.value;\n        }\n      } else if (target2) {\n        const difference = windowHeight.value - targetRect.top.value - rootHeightOffset;\n        fixed.value = windowHeight.value - offset < rootBottom.value && windowHeight.value > targetRect.top.value;\n        transform.value = difference < 0 ? -difference : 0;\n      } else {\n        fixed.value = windowHeight.value - offset < rootBottom.value;\n      }\n    };\n    const handleScroll = async () => {\n      updateRoot();\n      await nextTick();\n      emit(\"scroll\", {\n        scrollTop: scrollTop.value,\n        fixed: fixed.value\n      });\n    };\n    watch(fixed, val => emit(CHANGE_EVENT, val));\n    onMounted(() => {\n      var _a;\n      if (props.target) {\n        target.value = (_a = document.querySelector(props.target)) != null ? _a : void 0;\n        if (!target.value) throwError(COMPONENT_NAME, `Target does not exist: ${props.target}`);\n      } else {\n        target.value = document.documentElement;\n      }\n      scrollContainer.value = getScrollContainer(root.value, true);\n      updateRoot();\n    });\n    useEventListener(scrollContainer, \"scroll\", handleScroll);\n    watchEffect(update);\n    expose({\n      update,\n      updateRoot\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"root\",\n        ref: root,\n        class: normalizeClass(unref(ns).b()),\n        style: normalizeStyle(unref(rootStyle))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass({\n          [unref(ns).m(\"fixed\")]: fixed.value\n        }),\n        style: normalizeStyle(unref(affixStyle))\n      }, [renderSlot(_ctx.$slots, \"default\")], 6)], 6);\n    };\n  }\n});\nvar Affix = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"affix.vue\"]]);\nexport { Affix as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}