{"ast": null, "code": "import { highlight } from \"@/assets\";\nimport { Download } from \"@element-plus/icons-vue\";\nimport highlight2 from \"@/assets/img/highlight-2.png\";\nimport highlightSetting from \"@/assets/img/highlight-setting.png\";\nimport highlightShow from \"@/assets/img/highlight-show.png\";\nimport highlightShow1 from \"@/assets/img/highlight-show1.png\";\nconst __default__ = {\n  name: \"HighlightPage\"\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const features = [{\n      icon: \"📁\",\n      title: \"多分类高亮\",\n      description: \"支持创建多个高亮分类，每个分类可独立设置颜色和名称，灵活管理不同场景的高亮需求\"\n    }, {\n      icon: \"🎨\",\n      title: \"颜色自定义\",\n      description: \"提供20种精心挑选的预设颜色，可为每个分类设置不同的高亮颜色，让重点内容更显眼\"\n    }, {\n      icon: \"🔍\",\n      title: \"关键词管理\",\n      description: \"支持添加、编辑、删除关键词，每个分类可包含多个关键词，并支持关键词搜索和去重\"\n    }, {\n      icon: \"🔄\",\n      title: \"配置同步\",\n      description: \"支持配置导入导出，可生成分享码与他人分享配置，轻松备份和迁移您的设置\"\n    }, {\n      icon: \"⚡\",\n      title: \"实时高亮\",\n      description: \"输入关键词后立即在页面上高亮显示匹配文本，即时预览效果，快速调整\"\n    }, {\n      icon: \"🎚️\",\n      title: \"灵活控制\",\n      description: \"总开关控制所有高亮显示/隐藏，每个分类独立开关，满足不同场景需求\"\n    }];\n    const steps = [{\n      number: \"1\",\n      title: \"安装插件\",\n      description: \"从下方下载按钮下载并解压，打开Chrome或者Edge浏览器，进入chrome://extensions/或者edge://extensions/，将解压后的文件夹拖入页面中，完成安装\"\n    }, {\n      number: \"2\",\n      title: \"添加关键词\",\n      description: \"在插件设置中添加需要高亮的关键词\"\n    }, {\n      number: \"3\",\n      title: \"选择颜色\",\n      description: \"为关键词设置合适的高亮颜色\"\n    }, {\n      number: \"4\",\n      title: \"开始使用\",\n      description: \"浏览网页时自动高亮显示关键词\"\n    }];\n    const __returned__ = {\n      features,\n      steps,\n      get highlight() {\n        return highlight;\n      },\n      get Download() {\n        return Download;\n      },\n      get highlight2() {\n        return highlight2;\n      },\n      get highlightSetting() {\n        return highlightSetting;\n      },\n      get highlightShow() {\n        return highlightShow;\n      },\n      get highlightShow1() {\n        return highlightShow1;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["highlight", "Download", "highlight2", "highlightSetting", "highlightShow", "highlightShow1", "__default__", "name", "features", "icon", "title", "description", "steps", "number"], "sources": ["D:/codes/glmwebsite/geluman-website/src/views/products/Highlight.vue"], "sourcesContent": ["<template>\r\n  <div class=\"product-page\">\r\n    <section class=\"hero\">\r\n      <div class=\"hero-content\">\r\n        <img :src=\"highlight\" alt=\"高亮插件\" class=\"plugin-logo\" />\r\n        <h1>网页文本高亮插件</h1>\r\n        <p class=\"subtitle\">让重要信息一目了然，提升阅读效率</p>\r\n        <div class=\"cta-buttons\">\r\n          <a\r\n            href=\"https://gengxin.geluman.cn/downloads/GLM-Highlight-1.0.0.zip\"\r\n            class=\"download-btn\"\r\n            target=\"_blank\"\r\n          >\r\n            <el-icon><Download /></el-icon>\r\n            下载插件\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <section class=\"demo\">\r\n      <div class=\"container\">\r\n        <h2 class=\"section-title\">产品演示</h2>\r\n        <p class=\"section-subtitle\">简单易用的设置界面，强大的高亮功能</p>\r\n        <div class=\"demo-gallery\">\r\n          <div class=\"demo-item\">\r\n            <div class=\"demo-image-container\">\r\n              <img\r\n                :src=\"highlightShow1\"\r\n                alt=\"网页文本高亮插件 - 主界面\"\r\n                class=\"demo-image\"\r\n              />\r\n            </div>\r\n            <div class=\"image-caption\">主界面 - 轻松创建多个高亮分类</div>\r\n          </div>\r\n          <div class=\"demo-item\">\r\n            <div class=\"demo-image-container\">\r\n              <img\r\n                :src=\"highlight2\"\r\n                alt=\"网页文本高亮插件 - 效果展示2\"\r\n                class=\"demo-image\"\r\n              />\r\n            </div>\r\n            <div class=\"image-caption\">多彩高亮 - 不同类别信息区分明显</div>\r\n          </div>\r\n          <div class=\"demo-item animate-fadeInUp delay-8\">\r\n            <div class=\"demo-image-container\">\r\n              <img\r\n                :src=\"highlightSetting\"\r\n                alt=\"网页文本高亮插件 - 设置界面\"\r\n                class=\"demo-image\"\r\n              />\r\n            </div>\r\n            <div class=\"image-caption\">\r\n              个性化设置 - 自定义高亮模式，启用黑白名单控制网页高亮\r\n            </div>\r\n          </div>\r\n          <div class=\"demo-item animate-fadeInUp delay-10\">\r\n            <div class=\"demo-image-container\">\r\n              <img\r\n                :src=\"highlightShow\"\r\n                alt=\"网页文本高亮插件 - 效果展示\"\r\n                class=\"demo-image\"\r\n              />\r\n            </div>\r\n            <div class=\"image-caption\">高亮效果 - 重要信息一目了然</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <section class=\"features\">\r\n      <div class=\"container\">\r\n        <h2>核心功能</h2>\r\n        <div class=\"features-grid\">\r\n          <div\r\n            v-for=\"(feature, index) in features\"\r\n            :key=\"feature.title\"\r\n            class=\"feature-card\"\r\n          >\r\n            <div class=\"feature-icon\">{{ feature.icon }}</div>\r\n            <h3>{{ feature.title }}</h3>\r\n            <p>{{ feature.description }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <section class=\"usage\">\r\n      <div class=\"container\">\r\n        <h2>使用说明</h2>\r\n        <div class=\"usage-steps\">\r\n          <div class=\"step\" v-for=\"step in steps\" :key=\"step.title\">\r\n            <div class=\"step-number\">{{ step.number }}</div>\r\n            <h3>{{ step.title }}</h3>\r\n            <p>{{ step.description }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { highlight } from \"@/assets\";\r\nimport { Download } from \"@element-plus/icons-vue\";\r\nimport highlight2 from \"@/assets/img/highlight-2.png\";\r\nimport highlightSetting from \"@/assets/img/highlight-setting.png\";\r\nimport highlightShow from \"@/assets/img/highlight-show.png\";\r\nimport highlightShow1 from \"@/assets/img/highlight-show1.png\";\r\n\r\nconst features = [\r\n  {\r\n    icon: \"📁\",\r\n    title: \"多分类高亮\",\r\n    description:\r\n      \"支持创建多个高亮分类，每个分类可独立设置颜色和名称，灵活管理不同场景的高亮需求\",\r\n  },\r\n  {\r\n    icon: \"🎨\",\r\n    title: \"颜色自定义\",\r\n    description:\r\n      \"提供20种精心挑选的预设颜色，可为每个分类设置不同的高亮颜色，让重点内容更显眼\",\r\n  },\r\n  {\r\n    icon: \"🔍\",\r\n    title: \"关键词管理\",\r\n    description:\r\n      \"支持添加、编辑、删除关键词，每个分类可包含多个关键词，并支持关键词搜索和去重\",\r\n  },\r\n  {\r\n    icon: \"🔄\",\r\n    title: \"配置同步\",\r\n    description:\r\n      \"支持配置导入导出，可生成分享码与他人分享配置，轻松备份和迁移您的设置\",\r\n  },\r\n  {\r\n    icon: \"⚡\",\r\n    title: \"实时高亮\",\r\n    description:\r\n      \"输入关键词后立即在页面上高亮显示匹配文本，即时预览效果，快速调整\",\r\n  },\r\n  {\r\n    icon: \"🎚️\",\r\n    title: \"灵活控制\",\r\n    description:\r\n      \"总开关控制所有高亮显示/隐藏，每个分类独立开关，满足不同场景需求\",\r\n  },\r\n];\r\n\r\nconst steps = [\r\n  {\r\n    number: \"1\",\r\n    title: \"安装插件\",\r\n    description:\r\n      \"从下方下载按钮下载并解压，打开Chrome或者Edge浏览器，进入chrome://extensions/或者edge://extensions/，将解压后的文件夹拖入页面中，完成安装\",\r\n  },\r\n  {\r\n    number: \"2\",\r\n    title: \"添加关键词\",\r\n    description: \"在插件设置中添加需要高亮的关键词\",\r\n  },\r\n  {\r\n    number: \"3\",\r\n    title: \"选择颜色\",\r\n    description: \"为关键词设置合适的高亮颜色\",\r\n  },\r\n  {\r\n    number: \"4\",\r\n    title: \"开始使用\",\r\n    description: \"浏览网页时自动高亮显示关键词\",\r\n  },\r\n];\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.hero {\r\n  background: linear-gradient(\r\n    135deg,\r\n    var(--primary-color) 0%,\r\n    var(--primary-dark) 100%\r\n  );\r\n  padding: 6rem 0;\r\n  text-align: center;\r\n  color: white;\r\n\r\n  .plugin-logo {\r\n    width: 120px;\r\n    height: auto;\r\n    margin-bottom: 2rem;\r\n  }\r\n\r\n  h1 {\r\n    font-size: 3rem;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 1.2rem;\r\n    opacity: 0.9;\r\n    margin-bottom: 2rem;\r\n  }\r\n\r\n  .download-btn {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    gap: 0.8rem;\r\n    padding: 1rem 2rem;\r\n    background: white;\r\n    color: var(--primary-color);\r\n    text-decoration: none;\r\n    border-radius: 30px;\r\n    font-weight: 500;\r\n    font-size: 1.1rem;\r\n    transition: all 0.3s ease;\r\n    box-shadow: var(--shadow-md);\r\n\r\n    .el-icon {\r\n      font-size: 1.2rem;\r\n    }\r\n\r\n    &:hover {\r\n      transform: translateY(-2px);\r\n      box-shadow: var(--shadow-lg);\r\n    }\r\n  }\r\n}\r\n\r\n.demo {\r\n  padding: 6rem 0;\r\n  background: white;\r\n\r\n  .container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 0 2rem;\r\n    text-align: center;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 2.5rem;\r\n    margin-bottom: 1rem;\r\n    color: var(--text-primary);\r\n  }\r\n\r\n  .section-subtitle {\r\n    font-size: 1.2rem;\r\n    color: var(--text-secondary);\r\n    margin-bottom: 3rem;\r\n    max-width: 800px;\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n  }\r\n\r\n  .demo-gallery {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    grid-template-rows: repeat(2, auto);\r\n    gap: 3rem;\r\n\r\n    @media (max-width: 992px) {\r\n      grid-template-columns: 1fr;\r\n      grid-template-rows: auto;\r\n      gap: 4rem;\r\n    }\r\n  }\r\n\r\n  .demo-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n\r\n    .demo-image-container {\r\n      width: 100%;\r\n      height: 350px;\r\n      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\r\n      border-radius: 12px;\r\n      overflow: hidden;\r\n      transition: all 0.3s ease;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      background-color: #f8f9fa;\r\n\r\n      &:hover {\r\n        transform: translateY(-10px);\r\n        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);\r\n      }\r\n    }\r\n\r\n    .demo-image {\r\n      max-width: 100%;\r\n      max-height: 100%;\r\n      object-fit: contain;\r\n      display: block;\r\n      border-radius: 12px;\r\n    }\r\n\r\n    .image-caption {\r\n      margin-top: 1.5rem;\r\n      font-size: 1.1rem;\r\n      color: var(--text-secondary);\r\n    }\r\n  }\r\n}\r\n\r\n.features {\r\n  padding: 6rem 0;\r\n  background: var(--bg-secondary);\r\n\r\n  .container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 0 2rem;\r\n  }\r\n\r\n  h2 {\r\n    text-align: center;\r\n    font-size: 2.5rem;\r\n    margin-bottom: 3rem;\r\n  }\r\n\r\n  .features-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(3, 1fr);\r\n    gap: 2rem;\r\n\r\n    @media (max-width: 992px) {\r\n      grid-template-columns: repeat(2, 1fr);\r\n    }\r\n\r\n    @media (max-width: 768px) {\r\n      grid-template-columns: 1fr;\r\n    }\r\n  }\r\n}\r\n\r\n.feature-card {\r\n  background: white;\r\n  padding: 2rem;\r\n  border-radius: 20px;\r\n  text-align: center;\r\n  transition: var(--transition-base);\r\n\r\n  &:hover {\r\n    transform: translateY(-10px);\r\n\r\n    .feature-icon {\r\n      animation: iconBounce 0.5s ease;\r\n    }\r\n  }\r\n\r\n  .feature-icon {\r\n    font-size: 2.5rem;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  h3 {\r\n    font-size: 1.5rem;\r\n    margin-bottom: 1rem;\r\n    color: var(--text-primary);\r\n  }\r\n\r\n  p {\r\n    color: var(--text-secondary);\r\n    line-height: 1.6;\r\n  }\r\n}\r\n\r\n.usage {\r\n  padding: 6rem 0;\r\n\r\n  .container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 0 2rem;\r\n  }\r\n\r\n  h2 {\r\n    text-align: center;\r\n    font-size: 2.5rem;\r\n    margin-bottom: 3rem;\r\n  }\r\n\r\n  .usage-steps {\r\n    display: grid;\r\n    grid-template-columns: repeat(4, 1fr);\r\n    gap: 2rem;\r\n\r\n    @media (max-width: 992px) {\r\n      grid-template-columns: repeat(2, 1fr);\r\n    }\r\n\r\n    @media (max-width: 768px) {\r\n      grid-template-columns: 1fr;\r\n    }\r\n  }\r\n\r\n  .step {\r\n    text-align: center;\r\n    padding: 2rem;\r\n\r\n    .step-number {\r\n      width: 40px;\r\n      height: 40px;\r\n      background: var(--primary-color);\r\n      color: white;\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin: 0 auto 1rem;\r\n      font-weight: 500;\r\n    }\r\n\r\n    h3 {\r\n      font-size: 1.3rem;\r\n      margin-bottom: 1rem;\r\n      color: var(--text-primary);\r\n    }\r\n\r\n    p {\r\n      color: var(--text-secondary);\r\n      line-height: 1.6;\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes btnShine {\r\n  0% {\r\n    transform: translateX(-100%) translateY(-100%) rotate(45deg);\r\n  }\r\n  100% {\r\n    transform: translateX(100%) translateY(100%) rotate(45deg);\r\n  }\r\n}\r\n\r\n@keyframes iconBounce {\r\n  0%,\r\n  100% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(-10px);\r\n  }\r\n}\r\n</style>\r\n\r\n<script>\r\nexport default {\r\n  name: \"HighlightPage\",\r\n};\r\n</script>\r\n"], "mappings": "AAwGA,SAASA,SAAS,QAAQ,UAAU;AACpC,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,OAAOC,UAAU,MAAM,8BAA8B;AACrD,OAAOC,gBAAgB,MAAM,oCAAoC;AACjE,OAAOC,aAAa,MAAM,iCAAiC;AAC3D,OAAOC,cAAc,MAAM,kCAAkC;AAoV7D,MAAAC,WAAA,GAAe;EACbC,IAAI,EAAE;AACR,CAAC;;;;;;IApVD,MAAMC,QAAQ,GAAG,CACf;MACEC,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,OAAO;MACdC,WAAW,EACT;IACJ,CAAC,EACD;MACEF,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,OAAO;MACdC,WAAW,EACT;IACJ,CAAC,EACD;MACEF,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,OAAO;MACdC,WAAW,EACT;IACJ,CAAC,EACD;MACEF,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,MAAM;MACbC,WAAW,EACT;IACJ,CAAC,EACD;MACEF,IAAI,EAAE,GAAG;MACTC,KAAK,EAAE,MAAM;MACbC,WAAW,EACT;IACJ,CAAC,EACD;MACEF,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,WAAW,EACT;IACJ,CAAC,CACF;IAED,MAAMC,KAAK,GAAG,CACZ;MACEC,MAAM,EAAE,GAAG;MACXH,KAAK,EAAE,MAAM;MACbC,WAAW,EACT;IACJ,CAAC,EACD;MACEE,MAAM,EAAE,GAAG;MACXH,KAAK,EAAE,OAAO;MACdC,WAAW,EAAE;IACf,CAAC,EACD;MACEE,MAAM,EAAE,GAAG;MACXH,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,EACD;MACEE,MAAM,EAAE,GAAG;MACXH,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}