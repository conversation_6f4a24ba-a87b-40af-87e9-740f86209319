{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nfunction toLowercaseSeparator(key) {\n  return key.replace(/([A-Z])/g, \"-$1\").toLowerCase();\n}\nfunction getStyleStr(style) {\n  return Object.keys(style).map(key => `${toLowercaseSeparator(key)}: ${style[key]};`).join(\" \");\n}\nfunction getPixelRatio() {\n  return window.devicePixelRatio || 1;\n}\nconst reRendering = (mutation, watermarkElement) => {\n  let flag = false;\n  if (mutation.removedNodes.length && watermarkElement) {\n    flag = Array.from(mutation.removedNodes).includes(watermarkElement);\n  }\n  if (mutation.type === \"attributes\" && mutation.target === watermarkElement) {\n    flag = true;\n  }\n  return flag;\n};\nexport { getPixelRatio, getStyleStr, reRendering, toLowercaseSeparator };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}