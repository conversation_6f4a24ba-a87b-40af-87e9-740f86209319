{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { computed } from 'vue';\nimport dayjs from 'dayjs';\nimport localeData from 'dayjs/plugin/localeData.js';\nimport { getPrevMonthLastDays, getMonthDays, toNestedArr } from './date-table.mjs';\nimport { WEEK_DAYS } from '../../../constants/date.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { rangeArr } from '../../time-picker/src/utils.mjs';\nconst useDateTable = (props, emit) => {\n  dayjs.extend(localeData);\n  const firstDayOfWeek = dayjs.localeData().firstDayOfWeek();\n  const {\n    t,\n    lang\n  } = useLocale();\n  const now = dayjs().locale(lang.value);\n  const isInRange = computed(() => !!props.range && !!props.range.length);\n  const rows = computed(() => {\n    let days = [];\n    if (isInRange.value) {\n      const [start, end] = props.range;\n      const currentMonthRange = rangeArr(end.date() - start.date() + 1).map(index => ({\n        text: start.date() + index,\n        type: \"current\"\n      }));\n      let remaining = currentMonthRange.length % 7;\n      remaining = remaining === 0 ? 0 : 7 - remaining;\n      const nextMonthRange = rangeArr(remaining).map((_, index) => ({\n        text: index + 1,\n        type: \"next\"\n      }));\n      days = currentMonthRange.concat(nextMonthRange);\n    } else {\n      const firstDay = props.date.startOf(\"month\").day();\n      const prevMonthDays = getPrevMonthLastDays(props.date, (firstDay - firstDayOfWeek + 7) % 7).map(day => ({\n        text: day,\n        type: \"prev\"\n      }));\n      const currentMonthDays = getMonthDays(props.date).map(day => ({\n        text: day,\n        type: \"current\"\n      }));\n      days = [...prevMonthDays, ...currentMonthDays];\n      const remaining = 7 - (days.length % 7 || 7);\n      const nextMonthDays = rangeArr(remaining).map((_, index) => ({\n        text: index + 1,\n        type: \"next\"\n      }));\n      days = days.concat(nextMonthDays);\n    }\n    return toNestedArr(days);\n  });\n  const weekDays = computed(() => {\n    const start = firstDayOfWeek;\n    if (start === 0) {\n      return WEEK_DAYS.map(_ => t(`el.datepicker.weeks.${_}`));\n    } else {\n      return WEEK_DAYS.slice(start).concat(WEEK_DAYS.slice(0, start)).map(_ => t(`el.datepicker.weeks.${_}`));\n    }\n  });\n  const getFormattedDate = (day, type) => {\n    switch (type) {\n      case \"prev\":\n        return props.date.startOf(\"month\").subtract(1, \"month\").date(day);\n      case \"next\":\n        return props.date.startOf(\"month\").add(1, \"month\").date(day);\n      case \"current\":\n        return props.date.date(day);\n    }\n  };\n  const handlePickDay = ({\n    text,\n    type\n  }) => {\n    const date = getFormattedDate(text, type);\n    emit(\"pick\", date);\n  };\n  const getSlotData = ({\n    text,\n    type\n  }) => {\n    const day = getFormattedDate(text, type);\n    return {\n      isSelected: day.isSame(props.selectedDay),\n      type: `${type}-month`,\n      day: day.format(\"YYYY-MM-DD\"),\n      date: day.toDate()\n    };\n  };\n  return {\n    now,\n    isInRange,\n    rows,\n    weekDays,\n    getFormattedDate,\n    handlePickDay,\n    getSlotData\n  };\n};\nexport { useDateTable };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}