{"ast": null, "code": "import { defineComponent, ref, inject, getCurrentInstance, onMounted, watch, onBeforeUnmount, computed, reactive, openBlock, createElementBlock, normalizeStyle, unref, normalizeClass, createCommentVNode, createElementVNode, renderSlot, createBlock, withCtx, resolveDynamicComponent, createVNode, toDisplayString, createTextVNode } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { Check, Close } from '@element-plus/icons-vue';\nimport { stepProps } from './item.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nconst __default__ = defineComponent({\n  name: \"ElStep\"\n});\nconst _sfc_main = defineComponent({\n  ...__default__,\n  props: stepProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"step\");\n    const index = ref(-1);\n    const lineStyle = ref({});\n    const internalStatus = ref(\"\");\n    const parent = inject(\"ElSteps\");\n    const currentInstance = getCurrentInstance();\n    onMounted(() => {\n      watch([() => parent.props.active, () => parent.props.processStatus, () => parent.props.finishStatus], ([active]) => {\n        updateStatus(active);\n      }, {\n        immediate: true\n      });\n    });\n    onBeforeUnmount(() => {\n      parent.removeStep(stepItemState.uid);\n    });\n    const currentStatus = computed(() => {\n      return props.status || internalStatus.value;\n    });\n    const prevStatus = computed(() => {\n      const prevStep = parent.steps.value[index.value - 1];\n      return prevStep ? prevStep.currentStatus : \"wait\";\n    });\n    const isCenter = computed(() => {\n      return parent.props.alignCenter;\n    });\n    const isVertical = computed(() => {\n      return parent.props.direction === \"vertical\";\n    });\n    const isSimple = computed(() => {\n      return parent.props.simple;\n    });\n    const stepsCount = computed(() => {\n      return parent.steps.value.length;\n    });\n    const isLast = computed(() => {\n      var _a;\n      return ((_a = parent.steps.value[stepsCount.value - 1]) == null ? void 0 : _a.uid) === (currentInstance == null ? void 0 : currentInstance.uid);\n    });\n    const space = computed(() => {\n      return isSimple.value ? \"\" : parent.props.space;\n    });\n    const containerKls = computed(() => {\n      return [ns.b(), ns.is(isSimple.value ? \"simple\" : parent.props.direction), ns.is(\"flex\", isLast.value && !space.value && !isCenter.value), ns.is(\"center\", isCenter.value && !isVertical.value && !isSimple.value)];\n    });\n    const style = computed(() => {\n      const style2 = {\n        flexBasis: isNumber(space.value) ? `${space.value}px` : space.value ? space.value : `${100 / (stepsCount.value - (isCenter.value ? 0 : 1))}%`\n      };\n      if (isVertical.value) return style2;\n      if (isLast.value) {\n        style2.maxWidth = `${100 / stepsCount.value}%`;\n      }\n      return style2;\n    });\n    const setIndex = val => {\n      index.value = val;\n    };\n    const calcProgress = status => {\n      const isWait = status === \"wait\";\n      const style2 = {\n        transitionDelay: `${isWait ? \"-\" : \"\"}${150 * index.value}ms`\n      };\n      const step = status === parent.props.processStatus || isWait ? 0 : 100;\n      style2.borderWidth = step && !isSimple.value ? \"1px\" : 0;\n      style2[parent.props.direction === \"vertical\" ? \"height\" : \"width\"] = `${step}%`;\n      lineStyle.value = style2;\n    };\n    const updateStatus = activeIndex => {\n      if (activeIndex > index.value) {\n        internalStatus.value = parent.props.finishStatus;\n      } else if (activeIndex === index.value && prevStatus.value !== \"error\") {\n        internalStatus.value = parent.props.processStatus;\n      } else {\n        internalStatus.value = \"wait\";\n      }\n      const prevChild = parent.steps.value[index.value - 1];\n      if (prevChild) prevChild.calcProgress(internalStatus.value);\n    };\n    const stepItemState = reactive({\n      uid: currentInstance.uid,\n      currentStatus,\n      setIndex,\n      calcProgress\n    });\n    parent.addStep(stepItemState);\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        style: normalizeStyle(unref(style)),\n        class: normalizeClass(unref(containerKls))\n      }, [createCommentVNode(\" icon & line \"), createElementVNode(\"div\", {\n        class: normalizeClass([unref(ns).e(\"head\"), unref(ns).is(unref(currentStatus))])\n      }, [!unref(isSimple) ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"line\"))\n      }, [createElementVNode(\"i\", {\n        class: normalizeClass(unref(ns).e(\"line-inner\")),\n        style: normalizeStyle(lineStyle.value)\n      }, null, 6)], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass([unref(ns).e(\"icon\"), unref(ns).is(_ctx.icon || _ctx.$slots.icon ? \"icon\" : \"text\")])\n      }, [renderSlot(_ctx.$slots, \"icon\", {}, () => [_ctx.icon ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"icon-inner\"))\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.icon)))]),\n        _: 1\n      }, 8, [\"class\"])) : unref(currentStatus) === \"success\" ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 1,\n        class: normalizeClass([unref(ns).e(\"icon-inner\"), unref(ns).is(\"status\")])\n      }, {\n        default: withCtx(() => [createVNode(unref(Check))]),\n        _: 1\n      }, 8, [\"class\"])) : unref(currentStatus) === \"error\" ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 2,\n        class: normalizeClass([unref(ns).e(\"icon-inner\"), unref(ns).is(\"status\")])\n      }, {\n        default: withCtx(() => [createVNode(unref(Close))]),\n        _: 1\n      }, 8, [\"class\"])) : !unref(isSimple) ? (openBlock(), createElementBlock(\"div\", {\n        key: 3,\n        class: normalizeClass(unref(ns).e(\"icon-inner\"))\n      }, toDisplayString(index.value + 1), 3)) : createCommentVNode(\"v-if\", true)])], 2)], 2), createCommentVNode(\" title & description \"), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"main\"))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass([unref(ns).e(\"title\"), unref(ns).is(unref(currentStatus))])\n      }, [renderSlot(_ctx.$slots, \"title\", {}, () => [createTextVNode(toDisplayString(_ctx.title), 1)])], 2), unref(isSimple) ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"arrow\"))\n      }, null, 2)) : (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass([unref(ns).e(\"description\"), unref(ns).is(unref(currentStatus))])\n      }, [renderSlot(_ctx.$slots, \"description\", {}, () => [createTextVNode(toDisplayString(_ctx.description), 1)])], 2))], 2)], 6);\n    };\n  }\n});\nvar Step = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"item.vue\"]]);\nexport { Step as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}