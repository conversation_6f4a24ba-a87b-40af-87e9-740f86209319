{"ast": null, "code": "import { isClient } from '@vueuse/core';\nlet isDragging = false;\nfunction draggable(element, options) {\n  if (!isClient) return;\n  const moveFn = function (event) {\n    var _a;\n    (_a = options.drag) == null ? void 0 : _a.call(options, event);\n  };\n  const upFn = function (event) {\n    var _a;\n    document.removeEventListener(\"mousemove\", moveFn);\n    document.removeEventListener(\"mouseup\", upFn);\n    document.removeEventListener(\"touchmove\", moveFn);\n    document.removeEventListener(\"touchend\", upFn);\n    document.onselectstart = null;\n    document.ondragstart = null;\n    isDragging = false;\n    (_a = options.end) == null ? void 0 : _a.call(options, event);\n  };\n  const downFn = function (event) {\n    var _a;\n    if (isDragging) return;\n    event.preventDefault();\n    document.onselectstart = () => false;\n    document.ondragstart = () => false;\n    document.addEventListener(\"mousemove\", moveFn);\n    document.addEventListener(\"mouseup\", upFn);\n    document.addEventListener(\"touchmove\", moveFn);\n    document.addEventListener(\"touchend\", upFn);\n    isDragging = true;\n    (_a = options.start) == null ? void 0 : _a.call(options, event);\n  };\n  element.addEventListener(\"mousedown\", downFn);\n  element.addEventListener(\"touchstart\", downFn, {\n    passive: false\n  });\n}\nexport { draggable };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}