{"ast": null, "code": "import arrayReduceRight from './_arrayReduceRight.js';\nimport baseEachRight from './_baseEachRight.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseReduce from './_baseReduce.js';\nimport isArray from './isArray.js';\n\n/**\n * This method is like `_.reduce` except that it iterates over elements of\n * `collection` from right to left.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @returns {*} Returns the accumulated value.\n * @see _.reduce\n * @example\n *\n * var array = [[0, 1], [2, 3], [4, 5]];\n *\n * _.reduceRight(array, function(flattened, other) {\n *   return flattened.concat(other);\n * }, []);\n * // => [4, 5, 2, 3, 0, 1]\n */\nfunction reduceRight(collection, iteratee, accumulator) {\n  var func = isArray(collection) ? arrayReduceRight : baseReduce,\n    initAccum = arguments.length < 3;\n  return func(collection, baseIteratee(iteratee, 4), accumulator, initAccum, baseEachRight);\n}\nexport default reduceRight;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}