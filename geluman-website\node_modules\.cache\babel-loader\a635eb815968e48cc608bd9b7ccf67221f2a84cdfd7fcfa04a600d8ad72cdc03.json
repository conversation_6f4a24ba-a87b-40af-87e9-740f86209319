{"ast": null, "code": "import { getCurrentInstance, inject, unref } from 'vue';\nimport { isClient, computedEager } from '@vueuse/core';\nimport { useGetDerivedNamespace } from '../use-namespace/index.mjs';\nimport { debugWarn } from '../../utils/error.mjs';\nconst defaultIdInjection = {\n  prefix: Math.floor(Math.random() * 1e4),\n  current: 0\n};\nconst ID_INJECTION_KEY = Symbol(\"elIdInjection\");\nconst useIdInjection = () => {\n  return getCurrentInstance() ? inject(ID_INJECTION_KEY, defaultIdInjection) : defaultIdInjection;\n};\nconst useId = deterministicId => {\n  const idInjection = useIdInjection();\n  if (!isClient && idInjection === defaultIdInjection) {\n    debugWarn(\"IdInjection\", `Looks like you are using server rendering, you must provide a id provider to ensure the hydration process to be succeed\nusage: app.provide(ID_INJECTION_KEY, {\n  prefix: number,\n  current: number,\n})`);\n  }\n  const namespace = useGetDerivedNamespace();\n  const idRef = computedEager(() => unref(deterministicId) || `${namespace.value}-id-${idInjection.prefix}-${idInjection.current++}`);\n  return idRef;\n};\nexport { ID_INJECTION_KEY, useId, useIdInjection };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}