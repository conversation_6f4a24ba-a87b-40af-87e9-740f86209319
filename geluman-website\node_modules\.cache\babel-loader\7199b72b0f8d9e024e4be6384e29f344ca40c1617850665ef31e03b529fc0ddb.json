{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { defineComponent, inject, computed, resolveComponent, openBlock, createElementBlock, normalizeClass, createCommentVNode, createBlock, withModifiers, withCtx, createElementVNode, createVNode, Fragment } from 'vue';\nimport { ElCheckbox } from '../../checkbox/index.mjs';\nimport { ElRadio } from '../../radio/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { Check, Loading, ArrowRight } from '@element-plus/icons-vue';\nimport NodeContent from './node-content.mjs';\nimport { CASCADER_PANEL_INJECTION_KEY } from './types.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElCascaderNode\",\n  components: {\n    ElCheckbox,\n    ElRadio,\n    NodeContent,\n    ElIcon,\n    Check,\n    Loading,\n    ArrowRight\n  },\n  props: {\n    node: {\n      type: Object,\n      required: true\n    },\n    menuId: String\n  },\n  emits: [\"expand\"],\n  setup(props, {\n    emit\n  }) {\n    const panel = inject(CASCADER_PANEL_INJECTION_KEY);\n    const ns = useNamespace(\"cascader-node\");\n    const isHoverMenu = computed(() => panel.isHoverMenu);\n    const multiple = computed(() => panel.config.multiple);\n    const checkStrictly = computed(() => panel.config.checkStrictly);\n    const checkedNodeId = computed(() => {\n      var _a;\n      return (_a = panel.checkedNodes[0]) == null ? void 0 : _a.uid;\n    });\n    const isDisabled = computed(() => props.node.isDisabled);\n    const isLeaf = computed(() => props.node.isLeaf);\n    const expandable = computed(() => checkStrictly.value && !isLeaf.value || !isDisabled.value);\n    const inExpandingPath = computed(() => isInPath(panel.expandingNode));\n    const inCheckedPath = computed(() => checkStrictly.value && panel.checkedNodes.some(isInPath));\n    const isInPath = node => {\n      var _a;\n      const {\n        level,\n        uid\n      } = props.node;\n      return ((_a = node == null ? void 0 : node.pathNodes[level - 1]) == null ? void 0 : _a.uid) === uid;\n    };\n    const doExpand = () => {\n      if (inExpandingPath.value) return;\n      panel.expandNode(props.node);\n    };\n    const doCheck = checked => {\n      const {\n        node\n      } = props;\n      if (checked === node.checked) return;\n      panel.handleCheckChange(node, checked);\n    };\n    const doLoad = () => {\n      panel.lazyLoad(props.node, () => {\n        if (!isLeaf.value) doExpand();\n      });\n    };\n    const handleHoverExpand = e => {\n      if (!isHoverMenu.value) return;\n      handleExpand();\n      !isLeaf.value && emit(\"expand\", e);\n    };\n    const handleExpand = () => {\n      const {\n        node\n      } = props;\n      if (!expandable.value || node.loading) return;\n      node.loaded ? doExpand() : doLoad();\n    };\n    const handleClick = () => {\n      if (isHoverMenu.value && !isLeaf.value) return;\n      if (isLeaf.value && !isDisabled.value && !checkStrictly.value && !multiple.value) {\n        handleCheck(true);\n      } else {\n        handleExpand();\n      }\n    };\n    const handleSelectCheck = checked => {\n      if (checkStrictly.value) {\n        doCheck(checked);\n        if (props.node.loaded) {\n          doExpand();\n        }\n      } else {\n        handleCheck(checked);\n      }\n    };\n    const handleCheck = checked => {\n      if (!props.node.loaded) {\n        doLoad();\n      } else {\n        doCheck(checked);\n        !checkStrictly.value && doExpand();\n      }\n    };\n    return {\n      panel,\n      isHoverMenu,\n      multiple,\n      checkStrictly,\n      checkedNodeId,\n      isDisabled,\n      isLeaf,\n      expandable,\n      inExpandingPath,\n      inCheckedPath,\n      ns,\n      handleHoverExpand,\n      handleExpand,\n      handleClick,\n      handleCheck,\n      handleSelectCheck\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_checkbox = resolveComponent(\"el-checkbox\");\n  const _component_el_radio = resolveComponent(\"el-radio\");\n  const _component_check = resolveComponent(\"check\");\n  const _component_el_icon = resolveComponent(\"el-icon\");\n  const _component_node_content = resolveComponent(\"node-content\");\n  const _component_loading = resolveComponent(\"loading\");\n  const _component_arrow_right = resolveComponent(\"arrow-right\");\n  return openBlock(), createElementBlock(\"li\", {\n    id: `${_ctx.menuId}-${_ctx.node.uid}`,\n    role: \"menuitem\",\n    \"aria-haspopup\": !_ctx.isLeaf,\n    \"aria-owns\": _ctx.isLeaf ? void 0 : _ctx.menuId,\n    \"aria-expanded\": _ctx.inExpandingPath,\n    tabindex: _ctx.expandable ? -1 : void 0,\n    class: normalizeClass([_ctx.ns.b(), _ctx.ns.is(\"selectable\", _ctx.checkStrictly), _ctx.ns.is(\"active\", _ctx.node.checked), _ctx.ns.is(\"disabled\", !_ctx.expandable), _ctx.inExpandingPath && \"in-active-path\", _ctx.inCheckedPath && \"in-checked-path\"]),\n    onMouseenter: _ctx.handleHoverExpand,\n    onFocus: _ctx.handleHoverExpand,\n    onClick: _ctx.handleClick\n  }, [createCommentVNode(\" prefix \"), _ctx.multiple ? (openBlock(), createBlock(_component_el_checkbox, {\n    key: 0,\n    \"model-value\": _ctx.node.checked,\n    indeterminate: _ctx.node.indeterminate,\n    disabled: _ctx.isDisabled,\n    onClick: withModifiers(() => {}, [\"stop\"]),\n    \"onUpdate:modelValue\": _ctx.handleSelectCheck\n  }, null, 8, [\"model-value\", \"indeterminate\", \"disabled\", \"onClick\", \"onUpdate:modelValue\"])) : _ctx.checkStrictly ? (openBlock(), createBlock(_component_el_radio, {\n    key: 1,\n    \"model-value\": _ctx.checkedNodeId,\n    label: _ctx.node.uid,\n    disabled: _ctx.isDisabled,\n    \"onUpdate:modelValue\": _ctx.handleSelectCheck,\n    onClick: withModifiers(() => {}, [\"stop\"])\n  }, {\n    default: withCtx(() => [createCommentVNode(\"\\n        Add an empty element to avoid render label,\\n        do not use empty fragment here for https://github.com/vuejs/vue-next/pull/2485\\n      \"), createElementVNode(\"span\")]),\n    _: 1\n  }, 8, [\"model-value\", \"label\", \"disabled\", \"onUpdate:modelValue\", \"onClick\"])) : _ctx.isLeaf && _ctx.node.checked ? (openBlock(), createBlock(_component_el_icon, {\n    key: 2,\n    class: normalizeClass(_ctx.ns.e(\"prefix\"))\n  }, {\n    default: withCtx(() => [createVNode(_component_check)]),\n    _: 1\n  }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true), createCommentVNode(\" content \"), createVNode(_component_node_content), createCommentVNode(\" postfix \"), !_ctx.isLeaf ? (openBlock(), createElementBlock(Fragment, {\n    key: 3\n  }, [_ctx.node.loading ? (openBlock(), createBlock(_component_el_icon, {\n    key: 0,\n    class: normalizeClass([_ctx.ns.is(\"loading\"), _ctx.ns.e(\"postfix\")])\n  }, {\n    default: withCtx(() => [createVNode(_component_loading)]),\n    _: 1\n  }, 8, [\"class\"])) : (openBlock(), createBlock(_component_el_icon, {\n    key: 1,\n    class: normalizeClass([\"arrow-right\", _ctx.ns.e(\"postfix\")])\n  }, {\n    default: withCtx(() => [createVNode(_component_arrow_right)]),\n    _: 1\n  }, 8, [\"class\"]))], 64)) : createCommentVNode(\"v-if\", true)], 42, [\"id\", \"aria-haspopup\", \"aria-owns\", \"aria-expanded\", \"tabindex\", \"onMouseenter\", \"onFocus\", \"onClick\"]);\n}\nvar ElCascaderNode = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"node.vue\"]]);\nexport { ElCascaderNode as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}