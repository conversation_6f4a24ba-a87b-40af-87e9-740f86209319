{"ast": null, "code": "import { defineComponent, ref, computed, openBlock, createBlock, TransitionGroup, normalizeClass, unref, withCtx, createElementBlock, Fragment, renderList, with<PERSON><PERSON><PERSON>, renderSlot, createCommentVNode, createElementVNode, withModifiers, createVNode, toDisplayString, normalizeStyle } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { Document, CircleCheck, Check, Close, ZoomIn, Delete } from '@element-plus/icons-vue';\nimport { ElProgress } from '../../progress/index.mjs';\nimport { uploadListProps, uploadListEmits } from './upload-list.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nconst __default__ = defineComponent({\n  name: \"ElUploadList\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: uploadListProps,\n  emits: uploadListEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const nsUpload = useNamespace(\"upload\");\n    const nsIcon = useNamespace(\"icon\");\n    const nsList = useNamespace(\"list\");\n    const disabled = useFormDisabled();\n    const focusing = ref(false);\n    const containerKls = computed(() => [nsUpload.b(\"list\"), nsUpload.bm(\"list\", props.listType), nsUpload.is(\"disabled\", props.disabled)]);\n    const handleRemove = file => {\n      emit(\"remove\", file);\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(TransitionGroup, {\n        tag: \"ul\",\n        class: normalizeClass(unref(containerKls)),\n        name: unref(nsList).b()\n      }, {\n        default: withCtx(() => [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.files, (file, index) => {\n          return openBlock(), createElementBlock(\"li\", {\n            key: file.uid || file.name,\n            class: normalizeClass([unref(nsUpload).be(\"list\", \"item\"), unref(nsUpload).is(file.status), {\n              focusing: focusing.value\n            }]),\n            tabindex: \"0\",\n            onKeydown: withKeys($event => !unref(disabled) && handleRemove(file), [\"delete\"]),\n            onFocus: $event => focusing.value = true,\n            onBlur: $event => focusing.value = false,\n            onClick: $event => focusing.value = false\n          }, [renderSlot(_ctx.$slots, \"default\", {\n            file,\n            index\n          }, () => [_ctx.listType === \"picture\" || file.status !== \"uploading\" && _ctx.listType === \"picture-card\" ? (openBlock(), createElementBlock(\"img\", {\n            key: 0,\n            class: normalizeClass(unref(nsUpload).be(\"list\", \"item-thumbnail\")),\n            src: file.url,\n            crossorigin: _ctx.crossorigin,\n            alt: \"\"\n          }, null, 10, [\"src\", \"crossorigin\"])) : createCommentVNode(\"v-if\", true), file.status === \"uploading\" || _ctx.listType !== \"picture-card\" ? (openBlock(), createElementBlock(\"div\", {\n            key: 1,\n            class: normalizeClass(unref(nsUpload).be(\"list\", \"item-info\"))\n          }, [createElementVNode(\"a\", {\n            class: normalizeClass(unref(nsUpload).be(\"list\", \"item-name\")),\n            onClick: withModifiers($event => _ctx.handlePreview(file), [\"prevent\"])\n          }, [createVNode(unref(ElIcon), {\n            class: normalizeClass(unref(nsIcon).m(\"document\"))\n          }, {\n            default: withCtx(() => [createVNode(unref(Document))]),\n            _: 1\n          }, 8, [\"class\"]), createElementVNode(\"span\", {\n            class: normalizeClass(unref(nsUpload).be(\"list\", \"item-file-name\")),\n            title: file.name\n          }, toDisplayString(file.name), 11, [\"title\"])], 10, [\"onClick\"]), file.status === \"uploading\" ? (openBlock(), createBlock(unref(ElProgress), {\n            key: 0,\n            type: _ctx.listType === \"picture-card\" ? \"circle\" : \"line\",\n            \"stroke-width\": _ctx.listType === \"picture-card\" ? 6 : 2,\n            percentage: Number(file.percentage),\n            style: normalizeStyle(_ctx.listType === \"picture-card\" ? \"\" : \"margin-top: 0.5rem\")\n          }, null, 8, [\"type\", \"stroke-width\", \"percentage\", \"style\"])) : createCommentVNode(\"v-if\", true)], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"label\", {\n            class: normalizeClass(unref(nsUpload).be(\"list\", \"item-status-label\"))\n          }, [_ctx.listType === \"text\" ? (openBlock(), createBlock(unref(ElIcon), {\n            key: 0,\n            class: normalizeClass([unref(nsIcon).m(\"upload-success\"), unref(nsIcon).m(\"circle-check\")])\n          }, {\n            default: withCtx(() => [createVNode(unref(CircleCheck))]),\n            _: 1\n          }, 8, [\"class\"])) : [\"picture-card\", \"picture\"].includes(_ctx.listType) ? (openBlock(), createBlock(unref(ElIcon), {\n            key: 1,\n            class: normalizeClass([unref(nsIcon).m(\"upload-success\"), unref(nsIcon).m(\"check\")])\n          }, {\n            default: withCtx(() => [createVNode(unref(Check))]),\n            _: 1\n          }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true)], 2), !unref(disabled) ? (openBlock(), createBlock(unref(ElIcon), {\n            key: 2,\n            class: normalizeClass(unref(nsIcon).m(\"close\")),\n            onClick: $event => handleRemove(file)\n          }, {\n            default: withCtx(() => [createVNode(unref(Close))]),\n            _: 2\n          }, 1032, [\"class\", \"onClick\"])) : createCommentVNode(\"v-if\", true), createCommentVNode(\" Due to close btn only appears when li gets focused disappears after li gets blurred, thus keyboard navigation can never reach close btn\"), createCommentVNode(\" This is a bug which needs to be fixed \"), createCommentVNode(\" TODO: Fix the incorrect navigation interaction \"), !unref(disabled) ? (openBlock(), createElementBlock(\"i\", {\n            key: 3,\n            class: normalizeClass(unref(nsIcon).m(\"close-tip\"))\n          }, toDisplayString(unref(t)(\"el.upload.deleteTip\")), 3)) : createCommentVNode(\"v-if\", true), _ctx.listType === \"picture-card\" ? (openBlock(), createElementBlock(\"span\", {\n            key: 4,\n            class: normalizeClass(unref(nsUpload).be(\"list\", \"item-actions\"))\n          }, [createElementVNode(\"span\", {\n            class: normalizeClass(unref(nsUpload).be(\"list\", \"item-preview\")),\n            onClick: $event => _ctx.handlePreview(file)\n          }, [createVNode(unref(ElIcon), {\n            class: normalizeClass(unref(nsIcon).m(\"zoom-in\"))\n          }, {\n            default: withCtx(() => [createVNode(unref(ZoomIn))]),\n            _: 1\n          }, 8, [\"class\"])], 10, [\"onClick\"]), !unref(disabled) ? (openBlock(), createElementBlock(\"span\", {\n            key: 0,\n            class: normalizeClass(unref(nsUpload).be(\"list\", \"item-delete\")),\n            onClick: $event => handleRemove(file)\n          }, [createVNode(unref(ElIcon), {\n            class: normalizeClass(unref(nsIcon).m(\"delete\"))\n          }, {\n            default: withCtx(() => [createVNode(unref(Delete))]),\n            _: 1\n          }, 8, [\"class\"])], 10, [\"onClick\"])) : createCommentVNode(\"v-if\", true)], 2)) : createCommentVNode(\"v-if\", true)])], 42, [\"onKeydown\", \"onFocus\", \"onBlur\", \"onClick\"]);\n        }), 128)), renderSlot(_ctx.$slots, \"append\")]),\n        _: 3\n      }, 8, [\"class\", \"name\"]);\n    };\n  }\n});\nvar UploadList = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"upload-list.vue\"]]);\nexport { UploadList as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}