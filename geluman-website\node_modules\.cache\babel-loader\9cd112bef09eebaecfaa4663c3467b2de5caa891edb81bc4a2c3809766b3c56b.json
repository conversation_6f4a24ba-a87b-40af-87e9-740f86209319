{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { ref, watchEffect, watch, unref, computed, onMounted, nextTick } from 'vue';\nimport { useEventListener, useResizeObserver } from '@vueuse/core';\nimport { useFormSize } from '../../../form/src/hooks/use-form-common-props.mjs';\nfunction useStyle(props, layout, store, table) {\n  const isHidden = ref(false);\n  const renderExpanded = ref(null);\n  const resizeProxyVisible = ref(false);\n  const setDragVisible = visible => {\n    resizeProxyVisible.value = visible;\n  };\n  const resizeState = ref({\n    width: null,\n    height: null,\n    headerHeight: null\n  });\n  const isGroup = ref(false);\n  const scrollbarViewStyle = {\n    display: \"inline-block\",\n    verticalAlign: \"middle\"\n  };\n  const tableWidth = ref();\n  const tableScrollHeight = ref(0);\n  const bodyScrollHeight = ref(0);\n  const headerScrollHeight = ref(0);\n  const footerScrollHeight = ref(0);\n  const appendScrollHeight = ref(0);\n  watchEffect(() => {\n    layout.setHeight(props.height);\n  });\n  watchEffect(() => {\n    layout.setMaxHeight(props.maxHeight);\n  });\n  watch(() => [props.currentRowKey, store.states.rowKey], ([currentRowKey, rowKey]) => {\n    if (!unref(rowKey) || !unref(currentRowKey)) return;\n    store.setCurrentRowKey(`${currentRowKey}`);\n  }, {\n    immediate: true\n  });\n  watch(() => props.data, data => {\n    table.store.commit(\"setData\", data);\n  }, {\n    immediate: true,\n    deep: true\n  });\n  watchEffect(() => {\n    if (props.expandRowKeys) {\n      store.setExpandRowKeysAdapter(props.expandRowKeys);\n    }\n  });\n  const handleMouseLeave = () => {\n    table.store.commit(\"setHoverRow\", null);\n    if (table.hoverState) table.hoverState = null;\n  };\n  const handleHeaderFooterMousewheel = (event, data) => {\n    const {\n      pixelX,\n      pixelY\n    } = data;\n    if (Math.abs(pixelX) >= Math.abs(pixelY)) {\n      table.refs.bodyWrapper.scrollLeft += data.pixelX / 5;\n    }\n  };\n  const shouldUpdateHeight = computed(() => {\n    return props.height || props.maxHeight || store.states.fixedColumns.value.length > 0 || store.states.rightFixedColumns.value.length > 0;\n  });\n  const tableBodyStyles = computed(() => {\n    return {\n      width: layout.bodyWidth.value ? `${layout.bodyWidth.value}px` : \"\"\n    };\n  });\n  const doLayout = () => {\n    if (shouldUpdateHeight.value) {\n      layout.updateElsHeight();\n    }\n    layout.updateColumnsWidth();\n    if (typeof window === \"undefined\") return;\n    requestAnimationFrame(syncPosition);\n  };\n  onMounted(async () => {\n    await nextTick();\n    store.updateColumns();\n    bindEvents();\n    requestAnimationFrame(doLayout);\n    const el = table.vnode.el;\n    const tableHeader = table.refs.headerWrapper;\n    if (props.flexible && el && el.parentElement) {\n      el.parentElement.style.minWidth = \"0\";\n    }\n    resizeState.value = {\n      width: tableWidth.value = el.offsetWidth,\n      height: el.offsetHeight,\n      headerHeight: props.showHeader && tableHeader ? tableHeader.offsetHeight : null\n    };\n    store.states.columns.value.forEach(column => {\n      if (column.filteredValue && column.filteredValue.length) {\n        table.store.commit(\"filterChange\", {\n          column,\n          values: column.filteredValue,\n          silent: true\n        });\n      }\n    });\n    table.$ready = true;\n  });\n  const setScrollClassByEl = (el, className) => {\n    if (!el) return;\n    const classList = Array.from(el.classList).filter(item => !item.startsWith(\"is-scrolling-\"));\n    classList.push(layout.scrollX.value ? className : \"is-scrolling-none\");\n    el.className = classList.join(\" \");\n  };\n  const setScrollClass = className => {\n    const {\n      tableWrapper\n    } = table.refs;\n    setScrollClassByEl(tableWrapper, className);\n  };\n  const hasScrollClass = className => {\n    const {\n      tableWrapper\n    } = table.refs;\n    return !!(tableWrapper && tableWrapper.classList.contains(className));\n  };\n  const syncPosition = function () {\n    if (!table.refs.scrollBarRef) return;\n    if (!layout.scrollX.value) {\n      const scrollingNoneClass = \"is-scrolling-none\";\n      if (!hasScrollClass(scrollingNoneClass)) {\n        setScrollClass(scrollingNoneClass);\n      }\n      return;\n    }\n    const scrollContainer = table.refs.scrollBarRef.wrapRef;\n    if (!scrollContainer) return;\n    const {\n      scrollLeft,\n      offsetWidth,\n      scrollWidth\n    } = scrollContainer;\n    const {\n      headerWrapper,\n      footerWrapper\n    } = table.refs;\n    if (headerWrapper) headerWrapper.scrollLeft = scrollLeft;\n    if (footerWrapper) footerWrapper.scrollLeft = scrollLeft;\n    const maxScrollLeftPosition = scrollWidth - offsetWidth - 1;\n    if (scrollLeft >= maxScrollLeftPosition) {\n      setScrollClass(\"is-scrolling-right\");\n    } else if (scrollLeft === 0) {\n      setScrollClass(\"is-scrolling-left\");\n    } else {\n      setScrollClass(\"is-scrolling-middle\");\n    }\n  };\n  const bindEvents = () => {\n    if (!table.refs.scrollBarRef) return;\n    if (table.refs.scrollBarRef.wrapRef) {\n      useEventListener(table.refs.scrollBarRef.wrapRef, \"scroll\", syncPosition, {\n        passive: true\n      });\n    }\n    if (props.fit) {\n      useResizeObserver(table.vnode.el, resizeListener);\n    } else {\n      useEventListener(window, \"resize\", resizeListener);\n    }\n    useResizeObserver(table.refs.bodyWrapper, () => {\n      var _a, _b;\n      resizeListener();\n      (_b = (_a = table.refs) == null ? void 0 : _a.scrollBarRef) == null ? void 0 : _b.update();\n    });\n  };\n  const resizeListener = () => {\n    var _a, _b, _c, _d;\n    const el = table.vnode.el;\n    if (!table.$ready || !el) return;\n    let shouldUpdateLayout = false;\n    const {\n      width: oldWidth,\n      height: oldHeight,\n      headerHeight: oldHeaderHeight\n    } = resizeState.value;\n    const width = tableWidth.value = el.offsetWidth;\n    if (oldWidth !== width) {\n      shouldUpdateLayout = true;\n    }\n    const height = el.offsetHeight;\n    if ((props.height || shouldUpdateHeight.value) && oldHeight !== height) {\n      shouldUpdateLayout = true;\n    }\n    const tableHeader = props.tableLayout === \"fixed\" ? table.refs.headerWrapper : (_a = table.refs.tableHeaderRef) == null ? void 0 : _a.$el;\n    if (props.showHeader && (tableHeader == null ? void 0 : tableHeader.offsetHeight) !== oldHeaderHeight) {\n      shouldUpdateLayout = true;\n    }\n    tableScrollHeight.value = ((_b = table.refs.tableWrapper) == null ? void 0 : _b.scrollHeight) || 0;\n    headerScrollHeight.value = (tableHeader == null ? void 0 : tableHeader.scrollHeight) || 0;\n    footerScrollHeight.value = ((_c = table.refs.footerWrapper) == null ? void 0 : _c.offsetHeight) || 0;\n    appendScrollHeight.value = ((_d = table.refs.appendWrapper) == null ? void 0 : _d.offsetHeight) || 0;\n    bodyScrollHeight.value = tableScrollHeight.value - headerScrollHeight.value - footerScrollHeight.value - appendScrollHeight.value;\n    if (shouldUpdateLayout) {\n      resizeState.value = {\n        width,\n        height,\n        headerHeight: props.showHeader && (tableHeader == null ? void 0 : tableHeader.offsetHeight) || 0\n      };\n      doLayout();\n    }\n  };\n  const tableSize = useFormSize();\n  const bodyWidth = computed(() => {\n    const {\n      bodyWidth: bodyWidth_,\n      scrollY,\n      gutterWidth\n    } = layout;\n    return bodyWidth_.value ? `${bodyWidth_.value - (scrollY.value ? gutterWidth : 0)}px` : \"\";\n  });\n  const tableLayout = computed(() => {\n    if (props.maxHeight) return \"fixed\";\n    return props.tableLayout;\n  });\n  const emptyBlockStyle = computed(() => {\n    if (props.data && props.data.length) return null;\n    let height = \"100%\";\n    if (props.height && bodyScrollHeight.value) {\n      height = `${bodyScrollHeight.value}px`;\n    }\n    const width = tableWidth.value;\n    return {\n      width: width ? `${width}px` : \"\",\n      height\n    };\n  });\n  const scrollbarStyle = computed(() => {\n    if (props.height) {\n      return {\n        height: \"100%\"\n      };\n    }\n    if (props.maxHeight) {\n      if (!Number.isNaN(Number(props.maxHeight))) {\n        return {\n          maxHeight: `${props.maxHeight - headerScrollHeight.value - footerScrollHeight.value}px`\n        };\n      } else {\n        return {\n          maxHeight: `calc(${props.maxHeight} - ${headerScrollHeight.value + footerScrollHeight.value}px)`\n        };\n      }\n    }\n    return {};\n  });\n  const handleFixedMousewheel = (event, data) => {\n    const bodyWrapper = table.refs.bodyWrapper;\n    if (Math.abs(data.spinY) > 0) {\n      const currentScrollTop = bodyWrapper.scrollTop;\n      if (data.pixelY < 0 && currentScrollTop !== 0) {\n        event.preventDefault();\n      }\n      if (data.pixelY > 0 && bodyWrapper.scrollHeight - bodyWrapper.clientHeight > currentScrollTop) {\n        event.preventDefault();\n      }\n      bodyWrapper.scrollTop += Math.ceil(data.pixelY / 5);\n    } else {\n      bodyWrapper.scrollLeft += Math.ceil(data.pixelX / 5);\n    }\n  };\n  return {\n    isHidden,\n    renderExpanded,\n    setDragVisible,\n    isGroup,\n    handleMouseLeave,\n    handleHeaderFooterMousewheel,\n    tableSize,\n    emptyBlockStyle,\n    handleFixedMousewheel,\n    resizeProxyVisible,\n    bodyWidth,\n    resizeState,\n    doLayout,\n    tableBodyStyles,\n    tableLayout,\n    scrollbarViewStyle,\n    scrollbarStyle\n  };\n}\nexport { useStyle as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}