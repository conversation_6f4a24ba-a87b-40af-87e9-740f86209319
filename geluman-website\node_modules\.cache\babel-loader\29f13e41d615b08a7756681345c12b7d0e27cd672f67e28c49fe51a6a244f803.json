{"ast": null, "code": "import { defineComponent, ref, computed, provide, renderSlot } from 'vue';\nimport { POPPER_INJECTION_KEY } from './constants.mjs';\nimport { popperProps } from './popper.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst __default__ = defineComponent({\n  name: \"ElPopper\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: popperProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const triggerRef = ref();\n    const popperInstanceRef = ref();\n    const contentRef = ref();\n    const referenceRef = ref();\n    const role = computed(() => props.role);\n    const popperProvides = {\n      triggerRef,\n      popperInstanceRef,\n      contentRef,\n      referenceRef,\n      role\n    };\n    expose(popperProvides);\n    provide(POPPER_INJECTION_KEY, popperProvides);\n    return (_ctx, _cache) => {\n      return renderSlot(_ctx.$slots, \"default\");\n    };\n  }\n});\nvar Popper = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"popper.vue\"]]);\nexport { Popper as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}