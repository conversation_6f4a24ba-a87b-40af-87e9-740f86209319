{"ast": null, "code": "import baseFlatten from './_baseFlatten.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/**\n * Recursively flattens `array`.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to flatten.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * _.flattenDeep([1, [2, [3, [4]], 5]]);\n * // => [1, 2, 3, 4, 5]\n */\nfunction flattenDeep(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseFlatten(array, INFINITY) : [];\n}\nexport default flattenDeep;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}