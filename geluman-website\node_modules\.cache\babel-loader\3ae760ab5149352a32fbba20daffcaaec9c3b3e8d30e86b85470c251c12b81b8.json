{"ast": null, "code": "import { columns } from './common.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst tableV2HeaderRowProps = buildProps({\n  class: String,\n  columns,\n  columnsStyles: {\n    type: definePropType(Object),\n    required: true\n  },\n  headerIndex: Number,\n  style: {\n    type: definePropType(Object)\n  }\n});\nexport { tableV2HeaderRowProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}