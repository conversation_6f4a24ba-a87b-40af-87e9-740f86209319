{"ast": null, "code": "import { getCurrentInstance } from 'vue';\nconst AFTER_APPEAR = \"after-appear\";\nconst AFTER_ENTER = \"after-enter\";\nconst AFTER_LEAVE = \"after-leave\";\nconst APPEAR = \"appear\";\nconst APPEAR_CANCELLED = \"appear-cancelled\";\nconst BEFORE_ENTER = \"before-enter\";\nconst BEFORE_LEAVE = \"before-leave\";\nconst ENTER = \"enter\";\nconst ENTER_CANCELLED = \"enter-cancelled\";\nconst LEAVE = \"leave\";\nconst LEAVE_CANCELLED = \"leave-cancelled\";\nconst useTransitionFallthroughEmits = [AFTER_APPEAR, AFTER_ENTER, AFTER_LEAVE, APPEAR, APPEAR_CANCELLED, BEFORE_ENTER, BEFORE_LEAVE, ENTER, ENTER_CANCELLED, LEAVE, LEAVE_CANCELLED];\nconst useTransitionFallthrough = () => {\n  const {\n    emit\n  } = getCurrentInstance();\n  return {\n    onAfterAppear: () => {\n      emit(AFTER_APPEAR);\n    },\n    onAfterEnter: () => {\n      emit(AFTER_ENTER);\n    },\n    onAfterLeave: () => {\n      emit(AFTER_LEAVE);\n    },\n    onAppearCancelled: () => {\n      emit(APPEAR_CANCELLED);\n    },\n    onBeforeEnter: () => {\n      emit(BEFORE_ENTER);\n    },\n    onBeforeLeave: () => {\n      emit(BEFORE_LEAVE);\n    },\n    onEnter: () => {\n      emit(ENTER);\n    },\n    onEnterCancelled: () => {\n      emit(ENTER_CANCELLED);\n    },\n    onLeave: () => {\n      emit(LEAVE);\n    },\n    onLeaveCancelled: () => {\n      emit(LEAVE_CANCELLED);\n    }\n  };\n};\nexport { useTransitionFallthrough, useTransitionFallthroughEmits };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}