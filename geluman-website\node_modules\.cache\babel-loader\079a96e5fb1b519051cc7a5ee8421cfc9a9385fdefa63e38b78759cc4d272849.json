{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { computed, getCurrentInstance } from 'vue';\nimport { fromPairs } from 'lodash-unified';\nimport { debugWarn } from '../../utils/error.mjs';\nconst DEFAULT_EXCLUDE_KEYS = [\"class\", \"style\"];\nconst LISTENER_PREFIX = /^on[A-Z]/;\nconst useAttrs = (params = {}) => {\n  const {\n    excludeListeners = false,\n    excludeKeys\n  } = params;\n  const allExcludeKeys = computed(() => {\n    return ((excludeKeys == null ? void 0 : excludeKeys.value) || []).concat(DEFAULT_EXCLUDE_KEYS);\n  });\n  const instance = getCurrentInstance();\n  if (!instance) {\n    debugWarn(\"use-attrs\", \"getCurrentInstance() returned null. useAttrs() must be called at the top of a setup function\");\n    return computed(() => ({}));\n  }\n  return computed(() => {\n    var _a;\n    return fromPairs(Object.entries((_a = instance.proxy) == null ? void 0 : _a.$attrs).filter(([key]) => !allExcludeKeys.value.includes(key) && !(excludeListeners && LISTENER_PREFIX.test(key))));\n  });\n};\nexport { useAttrs };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}