{"ast": null, "code": "import baseToString from './_baseToString.js';\nimport castSlice from './_castSlice.js';\nimport charsStartIndex from './_charsStartIndex.js';\nimport stringToArray from './_stringToArray.js';\nimport toString from './toString.js';\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * Removes leading whitespace or specified characters from `string`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to trim.\n * @param {string} [chars=whitespace] The characters to trim.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {string} Returns the trimmed string.\n * @example\n *\n * _.trimStart('  abc  ');\n * // => 'abc  '\n *\n * _.trimStart('-_-abc-_-', '_-');\n * // => 'abc-_-'\n */\nfunction trimStart(string, chars, guard) {\n  string = toString(string);\n  if (string && (guard || chars === undefined)) {\n    return string.replace(reTrimStart, '');\n  }\n  if (!string || !(chars = baseToString(chars))) {\n    return string;\n  }\n  var strSymbols = stringToArray(string),\n    start = charsStartIndex(strSymbols, stringToArray(chars));\n  return castSlice(strSymbols, start).join('');\n}\nexport default trimStart;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}