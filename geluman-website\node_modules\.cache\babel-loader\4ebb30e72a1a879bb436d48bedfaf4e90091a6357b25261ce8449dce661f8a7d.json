{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, unref, toDisplayString, createBlock, withCtx, resolveDynamicComponent } from 'vue';\nimport { ElIcon } from '../../../icon/index.mjs';\nimport { paginationPrevProps, paginationPrevEmits } from './prev.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElPaginationPrev\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: paginationPrevProps,\n  emits: paginationPrevEmits,\n  setup(__props) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const internalDisabled = computed(() => props.disabled || props.currentPage <= 1);\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"button\", {\n        type: \"button\",\n        class: \"btn-prev\",\n        disabled: unref(internalDisabled),\n        \"aria-label\": _ctx.prevText || unref(t)(\"el.pagination.prev\"),\n        \"aria-disabled\": unref(internalDisabled),\n        onClick: $event => _ctx.$emit(\"click\", $event)\n      }, [_ctx.prevText ? (openBlock(), createElementBlock(\"span\", {\n        key: 0\n      }, toDisplayString(_ctx.prevText), 1)) : (openBlock(), createBlock(unref(ElIcon), {\n        key: 1\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.prevIcon)))]),\n        _: 1\n      }))], 8, [\"disabled\", \"aria-label\", \"aria-disabled\", \"onClick\"]);\n    };\n  }\n});\nvar Prev = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"prev.vue\"]]);\nexport { Prev as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}