{"ast": null, "code": "import { ref, reactive, defineComponent, h, Transition, withCtx, withDirectives, createVNode, vShow, createApp, toRefs } from 'vue';\nimport { useGlobalComponentSettings } from '../../config-provider/src/hooks/use-global-config.mjs';\nimport { removeClass } from '../../../utils/dom/style.mjs';\nfunction createLoadingComponent(options) {\n  let afterLeaveTimer;\n  const afterLeaveFlag = ref(false);\n  const data = reactive({\n    ...options,\n    originalPosition: \"\",\n    originalOverflow: \"\",\n    visible: false\n  });\n  function setText(text) {\n    data.text = text;\n  }\n  function destroySelf() {\n    const target = data.parent;\n    const ns = vm.ns;\n    if (!target.vLoadingAddClassList) {\n      let loadingNumber = target.getAttribute(\"loading-number\");\n      loadingNumber = Number.parseInt(loadingNumber) - 1;\n      if (!loadingNumber) {\n        removeClass(target, ns.bm(\"parent\", \"relative\"));\n        target.removeAttribute(\"loading-number\");\n      } else {\n        target.setAttribute(\"loading-number\", loadingNumber.toString());\n      }\n      removeClass(target, ns.bm(\"parent\", \"hidden\"));\n    }\n    removeElLoadingChild();\n    loadingInstance.unmount();\n  }\n  function removeElLoadingChild() {\n    var _a, _b;\n    (_b = (_a = vm.$el) == null ? void 0 : _a.parentNode) == null ? void 0 : _b.removeChild(vm.$el);\n  }\n  function close() {\n    var _a;\n    if (options.beforeClose && !options.beforeClose()) return;\n    afterLeaveFlag.value = true;\n    clearTimeout(afterLeaveTimer);\n    afterLeaveTimer = setTimeout(handleAfterLeave, 400);\n    data.visible = false;\n    (_a = options.closed) == null ? void 0 : _a.call(options);\n  }\n  function handleAfterLeave() {\n    if (!afterLeaveFlag.value) return;\n    const target = data.parent;\n    afterLeaveFlag.value = false;\n    target.vLoadingAddClassList = void 0;\n    destroySelf();\n  }\n  const elLoadingComponent = defineComponent({\n    name: \"ElLoading\",\n    setup(_, {\n      expose\n    }) {\n      const {\n        ns,\n        zIndex\n      } = useGlobalComponentSettings(\"loading\");\n      expose({\n        ns,\n        zIndex\n      });\n      return () => {\n        const svg = data.spinner || data.svg;\n        const spinner = h(\"svg\", {\n          class: \"circular\",\n          viewBox: data.svgViewBox ? data.svgViewBox : \"0 0 50 50\",\n          ...(svg ? {\n            innerHTML: svg\n          } : {})\n        }, [h(\"circle\", {\n          class: \"path\",\n          cx: \"25\",\n          cy: \"25\",\n          r: \"20\",\n          fill: \"none\"\n        })]);\n        const spinnerText = data.text ? h(\"p\", {\n          class: ns.b(\"text\")\n        }, [data.text]) : void 0;\n        return h(Transition, {\n          name: ns.b(\"fade\"),\n          onAfterLeave: handleAfterLeave\n        }, {\n          default: withCtx(() => [withDirectives(createVNode(\"div\", {\n            style: {\n              backgroundColor: data.background || \"\"\n            },\n            class: [ns.b(\"mask\"), data.customClass, data.fullscreen ? \"is-fullscreen\" : \"\"]\n          }, [h(\"div\", {\n            class: ns.b(\"spinner\")\n          }, [spinner, spinnerText])]), [[vShow, data.visible]])])\n        });\n      };\n    }\n  });\n  const loadingInstance = createApp(elLoadingComponent);\n  const vm = loadingInstance.mount(document.createElement(\"div\"));\n  return {\n    ...toRefs(data),\n    setText,\n    removeElLoadingChild,\n    close,\n    handleAfterLeave,\n    vm,\n    get $el() {\n      return vm.$el;\n    }\n  };\n}\nexport { createLoadingComponent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}