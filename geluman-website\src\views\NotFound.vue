<template>
  <div class="not-found">
    <div class="content">
      <h1 class="title">404</h1>
      <p class="subtitle">抱歉，您访问的页面不存在</p>
      <router-link to="/" class="back-home">
        <el-icon><HomeFilled /></el-icon>
        返回首页
      </router-link>
    </div>
    <div class="background-animation"></div>
  </div>
</template>

<script setup>
import { HomeFilled } from "@element-plus/icons-vue";
</script>

<style lang="scss" scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  position: relative;
  overflow: hidden;

  .content {
    text-align: center;
    z-index: 1;
  }

  .title {
    font-size: 8rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 700;
    text-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }

  .subtitle {
    font-size: 1.5rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
  }

  .back-home {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 30px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
  }

  .background-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        45deg,
        transparent 45%,
        rgba(99, 102, 241, 0.03) 50%,
        transparent 55%
      ),
      linear-gradient(
        -45deg,
        transparent 45%,
        rgba(99, 102, 241, 0.03) 50%,
        transparent 55%
      );
    background-size: 60px 60px;
    animation: moveBackground 20s linear infinite;
  }
}

@keyframes moveBackground {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 60px 60px;
  }
}

@media (max-width: 768px) {
  .not-found {
    .title {
      font-size: 6rem;
    }

    .subtitle {
      font-size: 1.2rem;
    }
  }
}
</style>
