{"ast": null, "code": "import { Close } from '@element-plus/icons-vue';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nconst notificationTypes = [\"success\", \"info\", \"warning\", \"error\"];\nconst notificationProps = buildProps({\n  customClass: {\n    type: String,\n    default: \"\"\n  },\n  dangerouslyUseHTMLString: Boolean,\n  duration: {\n    type: Number,\n    default: 4500\n  },\n  icon: {\n    type: iconPropType\n  },\n  id: {\n    type: String,\n    default: \"\"\n  },\n  message: {\n    type: definePropType([String, Object, Function]),\n    default: \"\"\n  },\n  offset: {\n    type: Number,\n    default: 0\n  },\n  onClick: {\n    type: definePropType(Function),\n    default: () => void 0\n  },\n  onClose: {\n    type: definePropType(Function),\n    required: true\n  },\n  position: {\n    type: String,\n    values: [\"top-right\", \"top-left\", \"bottom-right\", \"bottom-left\"],\n    default: \"top-right\"\n  },\n  showClose: {\n    type: Boolean,\n    default: true\n  },\n  title: {\n    type: String,\n    default: \"\"\n  },\n  type: {\n    type: String,\n    values: [...notificationTypes, \"\"],\n    default: \"\"\n  },\n  zIndex: Number,\n  closeIcon: {\n    type: iconPropType,\n    default: Close\n  }\n});\nconst notificationEmits = {\n  destroy: () => true\n};\nexport { notificationEmits, notificationProps, notificationTypes };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}