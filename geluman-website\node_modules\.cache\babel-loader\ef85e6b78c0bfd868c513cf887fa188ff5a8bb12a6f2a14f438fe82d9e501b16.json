{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport { filterOption } from './helper.mjs';\nimport { inputProps } from '../../input/src/input2.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { isString, isFunction } from '@vue/shared';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nconst mentionProps = buildProps({\n  ...inputProps,\n  options: {\n    type: definePropType(Array),\n    default: () => []\n  },\n  prefix: {\n    type: definePropType([String, Array]),\n    default: \"@\",\n    validator: val => {\n      if (isString(val)) return val.length === 1;\n      return val.every(v => isString(v) && v.length === 1);\n    }\n  },\n  split: {\n    type: String,\n    default: \" \",\n    validator: val => val.length === 1\n  },\n  filterOption: {\n    type: definePropType([Boolean, Function]),\n    default: () => filterOption,\n    validator: val => {\n      if (val === false) return true;\n      return isFunction(val);\n    }\n  },\n  placement: {\n    type: definePropType(String),\n    default: \"bottom\"\n  },\n  showArrow: Boolean,\n  offset: {\n    type: Number,\n    default: 0\n  },\n  whole: Boolean,\n  checkIsWhole: {\n    type: definePropType(Function)\n  },\n  modelValue: String,\n  loading: Boolean,\n  popperClass: {\n    type: String,\n    default: \"\"\n  },\n  popperOptions: {\n    type: definePropType(Object),\n    default: () => ({})\n  }\n});\nconst mentionEmits = {\n  [UPDATE_MODEL_EVENT]: value => isString(value),\n  input: value => isString(value),\n  search: (pattern, prefix) => isString(pattern) && isString(prefix),\n  select: (option, prefix) => isString(option.value) && isString(prefix),\n  focus: evt => evt instanceof FocusEvent,\n  blur: evt => evt instanceof FocusEvent\n};\nexport { mentionEmits, mentionProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}