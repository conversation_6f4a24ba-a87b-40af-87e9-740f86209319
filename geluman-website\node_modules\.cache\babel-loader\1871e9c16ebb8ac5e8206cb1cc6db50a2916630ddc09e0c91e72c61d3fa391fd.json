{"ast": null, "code": "/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\nimport { convertHexToDecimal, hslToRgb, hsvToRgb, parseIntFromHex, rgbToRgb } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { boundAlpha, convertToPercentage } from './util.js';\n/**\n * Given a string or object, convert that input to RGB\n *\n * Possible string inputs:\n * ```\n * \"red\"\n * \"#f00\" or \"f00\"\n * \"#ff0000\" or \"ff0000\"\n * \"#ff000000\" or \"ff000000\"\n * \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n * \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n * \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n * \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n * \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n * \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n * \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n * ```\n */\nexport function inputToRGB(color) {\n  var rgb = {\n    r: 0,\n    g: 0,\n    b: 0\n  };\n  var a = 1;\n  var s = null;\n  var v = null;\n  var l = null;\n  var ok = false;\n  var format = false;\n  if (typeof color === 'string') {\n    color = stringInputToObject(color);\n  }\n  if (typeof color === 'object') {\n    if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n      rgb = rgbToRgb(color.r, color.g, color.b);\n      ok = true;\n      format = String(color.r).substr(-1) === '%' ? 'prgb' : 'rgb';\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n      s = convertToPercentage(color.s);\n      v = convertToPercentage(color.v);\n      rgb = hsvToRgb(color.h, s, v);\n      ok = true;\n      format = 'hsv';\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n      s = convertToPercentage(color.s);\n      l = convertToPercentage(color.l);\n      rgb = hslToRgb(color.h, s, l);\n      ok = true;\n      format = 'hsl';\n    }\n    if (Object.prototype.hasOwnProperty.call(color, 'a')) {\n      a = color.a;\n    }\n  }\n  a = boundAlpha(a);\n  return {\n    ok: ok,\n    format: color.format || format,\n    r: Math.min(255, Math.max(rgb.r, 0)),\n    g: Math.min(255, Math.max(rgb.g, 0)),\n    b: Math.min(255, Math.max(rgb.b, 0)),\n    a: a\n  };\n}\n// <http://www.w3.org/TR/css3-values/#integers>\nvar CSS_INTEGER = '[-\\\\+]?\\\\d+%?';\n// <http://www.w3.org/TR/css3-values/#number-value>\nvar CSS_NUMBER = '[-\\\\+]?\\\\d*\\\\.\\\\d+%?';\n// Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\nvar CSS_UNIT = \"(?:\".concat(CSS_NUMBER, \")|(?:\").concat(CSS_INTEGER, \")\");\n// Actual matching.\n// Parentheses and commas are optional, but not required.\n// Whitespace can take the place of commas or opening paren\nvar PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar matchers = {\n  CSS_UNIT: new RegExp(CSS_UNIT),\n  rgb: new RegExp('rgb' + PERMISSIVE_MATCH3),\n  rgba: new RegExp('rgba' + PERMISSIVE_MATCH4),\n  hsl: new RegExp('hsl' + PERMISSIVE_MATCH3),\n  hsla: new RegExp('hsla' + PERMISSIVE_MATCH4),\n  hsv: new RegExp('hsv' + PERMISSIVE_MATCH3),\n  hsva: new RegExp('hsva' + PERMISSIVE_MATCH4),\n  hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n  hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n  hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n  hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/\n};\n/**\n * Permissive string parsing.  Take in a number of formats, and output an object\n * based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\n */\nexport function stringInputToObject(color) {\n  color = color.trim().toLowerCase();\n  if (color.length === 0) {\n    return false;\n  }\n  var named = false;\n  if (names[color]) {\n    color = names[color];\n    named = true;\n  } else if (color === 'transparent') {\n    return {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 0,\n      format: 'name'\n    };\n  }\n  // Try to match string input using regular expressions.\n  // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n  // Just return an object and let the conversion functions handle that.\n  // This way the result will be the same whether the tinycolor is initialized with string or object.\n  var match = matchers.rgb.exec(color);\n  if (match) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3]\n    };\n  }\n  match = matchers.rgba.exec(color);\n  if (match) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3],\n      a: match[4]\n    };\n  }\n  match = matchers.hsl.exec(color);\n  if (match) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3]\n    };\n  }\n  match = matchers.hsla.exec(color);\n  if (match) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3],\n      a: match[4]\n    };\n  }\n  match = matchers.hsv.exec(color);\n  if (match) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3]\n    };\n  }\n  match = matchers.hsva.exec(color);\n  if (match) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3],\n      a: match[4]\n    };\n  }\n  match = matchers.hex8.exec(color);\n  if (match) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      a: convertHexToDecimal(match[4]),\n      format: named ? 'name' : 'hex8'\n    };\n  }\n  match = matchers.hex6.exec(color);\n  if (match) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      format: named ? 'name' : 'hex'\n    };\n  }\n  match = matchers.hex4.exec(color);\n  if (match) {\n    return {\n      r: parseIntFromHex(match[1] + match[1]),\n      g: parseIntFromHex(match[2] + match[2]),\n      b: parseIntFromHex(match[3] + match[3]),\n      a: convertHexToDecimal(match[4] + match[4]),\n      format: named ? 'name' : 'hex8'\n    };\n  }\n  match = matchers.hex3.exec(color);\n  if (match) {\n    return {\n      r: parseIntFromHex(match[1] + match[1]),\n      g: parseIntFromHex(match[2] + match[2]),\n      b: parseIntFromHex(match[3] + match[3]),\n      format: named ? 'name' : 'hex'\n    };\n  }\n  return false;\n}\n/**\n * Check to see if it looks like a CSS unit\n * (see `matchers` above for definition).\n */\nexport function isValidCSSUnit(color) {\n  return Boolean(matchers.CSS_UNIT.exec(String(color)));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}