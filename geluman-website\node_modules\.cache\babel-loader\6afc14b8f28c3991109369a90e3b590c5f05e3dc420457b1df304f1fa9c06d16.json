{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { isString } from '@vue/shared';\nimport { isNumber, isBoolean } from '../../../utils/types.mjs';\nconst defaultProps = {\n  label: \"label\",\n  value: \"value\",\n  disabled: \"disabled\"\n};\nconst segmentedProps = buildProps({\n  direction: {\n    type: definePropType(String),\n    default: \"horizontal\"\n  },\n  options: {\n    type: definePropType(Array),\n    default: () => []\n  },\n  modelValue: {\n    type: [String, Number, Boolean],\n    default: void 0\n  },\n  props: {\n    type: definePropType(Object),\n    default: () => defaultProps\n  },\n  block: Boolean,\n  size: useSizeProp,\n  disabled: Boolean,\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  id: String,\n  name: String,\n  ...useAriaProps([\"ariaLabel\"])\n});\nconst segmentedEmits = {\n  [UPDATE_MODEL_EVENT]: val => isString(val) || isNumber(val) || isBoolean(val),\n  [CHANGE_EVENT]: val => isString(val) || isNumber(val) || isBoolean(val)\n};\nexport { defaultProps, segmentedEmits, segmentedProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}