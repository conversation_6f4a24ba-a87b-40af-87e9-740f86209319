{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { defineComponent, useSlots, computed, openBlock, createElementBlock, normalizeClass, unref, renderSlot } from 'vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElContainer\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: {\n    direction: {\n      type: String\n    }\n  },\n  setup(__props) {\n    const props = __props;\n    const slots = useSlots();\n    const ns = useNamespace(\"container\");\n    const isVertical = computed(() => {\n      if (props.direction === \"vertical\") {\n        return true;\n      } else if (props.direction === \"horizontal\") {\n        return false;\n      }\n      if (slots && slots.default) {\n        const vNodes = slots.default();\n        return vNodes.some(vNode => {\n          const tag = vNode.type.name;\n          return tag === \"ElHeader\" || tag === \"ElFooter\";\n        });\n      } else {\n        return false;\n      }\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"section\", {\n        class: normalizeClass([unref(ns).b(), unref(ns).is(\"vertical\", unref(isVertical))])\n      }, [renderSlot(_ctx.$slots, \"default\")], 2);\n    };\n  }\n});\nvar Container = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"container.vue\"]]);\nexport { Container as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}