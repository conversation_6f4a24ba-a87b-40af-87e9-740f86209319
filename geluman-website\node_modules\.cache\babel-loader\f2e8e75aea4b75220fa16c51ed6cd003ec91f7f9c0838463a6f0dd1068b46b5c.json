{"ast": null, "code": "import { defineComponent, provide, reactive, toRef, openBlock, createElementBlock, normalizeClass, unref, renderSlot } from 'vue';\nimport { buttonGroupProps } from './button-group.mjs';\nimport { buttonGroupContextKey } from './constants.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElButtonGroup\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: buttonGroupProps,\n  setup(__props) {\n    const props = __props;\n    provide(buttonGroupContextKey, reactive({\n      size: toRef(props, \"size\"),\n      type: toRef(props, \"type\")\n    }));\n    const ns = useNamespace(\"button\");\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(ns).b(\"group\"))\n      }, [renderSlot(_ctx.$slots, \"default\")], 2);\n    };\n  }\n});\nvar ButtonGroup = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"button-group.vue\"]]);\nexport { ButtonGroup as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}