{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { componentSizes } from '../../../constants/size.mjs';\nimport { isArray, isString } from '@vue/shared';\nimport { isBoolean } from '../../../utils/types.mjs';\nconst formMetaProps = buildProps({\n  size: {\n    type: String,\n    values: componentSizes\n  },\n  disabled: Boolean\n});\nconst formProps = buildProps({\n  ...formMetaProps,\n  model: Object,\n  rules: {\n    type: definePropType(Object)\n  },\n  labelPosition: {\n    type: String,\n    values: [\"left\", \"right\", \"top\"],\n    default: \"right\"\n  },\n  requireAsteriskPosition: {\n    type: String,\n    values: [\"left\", \"right\"],\n    default: \"left\"\n  },\n  labelWidth: {\n    type: [String, Number],\n    default: \"\"\n  },\n  labelSuffix: {\n    type: String,\n    default: \"\"\n  },\n  inline: <PERSON>olean,\n  inlineMessage: <PERSON>olean,\n  statusIcon: <PERSON>olean,\n  showMessage: {\n    type: Boolean,\n    default: true\n  },\n  validateOnRuleChange: {\n    type: Boolean,\n    default: true\n  },\n  hideRequiredAsterisk: <PERSON>ole<PERSON>,\n  scrollToError: <PERSON>olean,\n  scrollIntoViewOptions: {\n    type: [Object, Boolean]\n  }\n});\nconst formEmits = {\n  validate: (prop, isValid, message) => (isArray(prop) || isString(prop)) && isBoolean(isValid) && isString(message)\n};\nexport { formEmits, formMetaProps, formProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}