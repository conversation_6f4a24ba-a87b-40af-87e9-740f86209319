{"ast": null, "code": "import Upload from './src/upload2.mjs';\nexport { genFileId, uploadBaseProps, uploadListTypes, uploadProps } from './src/upload.mjs';\nexport { uploadContentProps } from './src/upload-content.mjs';\nexport { uploadListEmits, uploadListProps } from './src/upload-list.mjs';\nexport { uploadDraggerEmits, uploadDraggerProps } from './src/upload-dragger.mjs';\nexport { uploadContextKey } from './src/constants.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElUpload = withInstall(Upload);\nexport { ElUpload, ElUpload as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}