{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, computed, reactive, toRefs, provide, resolveComponent, resolveDirective, withDirectives, openBlock, createElement<PERSON><PERSON>, normalizeClass, toHandlerKey, createVNode, withCtx, createElementVNode, withModifiers, renderSlot, createCommentVNode, Fragment, renderList, normalizeStyle, createTextVNode, toDisplayString, createBlock, withKeys, vModelText, resolveDynamicComponent, vShow } from 'vue';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { ElScrollbar } from '../../scrollbar/index.mjs';\nimport { ElTag } from '../../tag/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport Option from './option2.mjs';\nimport ElSelectMenu from './select-dropdown.mjs';\nimport { useSelect } from './useSelect.mjs';\nimport { selectKey } from './token.mjs';\nimport ElOptions from './options.mjs';\nimport { SelectProps } from './select.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport ClickOutside from '../../../directives/click-outside/index.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { isArray } from '@vue/shared';\nimport { useCalcInputWidth } from '../../../hooks/use-calc-input-width/index.mjs';\nconst COMPONENT_NAME = \"ElSelect\";\nconst _sfc_main = defineComponent({\n  name: COMPONENT_NAME,\n  componentName: COMPONENT_NAME,\n  components: {\n    ElSelectMenu,\n    ElOption: Option,\n    ElOptions,\n    ElTag,\n    ElScrollbar,\n    ElTooltip,\n    ElIcon\n  },\n  directives: {\n    ClickOutside\n  },\n  props: SelectProps,\n  emits: [UPDATE_MODEL_EVENT, CHANGE_EVENT, \"remove-tag\", \"clear\", \"visible-change\", \"focus\", \"blur\", \"popup-scroll\"],\n  setup(props, {\n    emit\n  }) {\n    const modelValue = computed(() => {\n      const {\n        modelValue: rawModelValue,\n        multiple\n      } = props;\n      const fallback = multiple ? [] : void 0;\n      if (isArray(rawModelValue)) {\n        return multiple ? rawModelValue : fallback;\n      }\n      return multiple ? fallback : rawModelValue;\n    });\n    const _props = reactive({\n      ...toRefs(props),\n      modelValue\n    });\n    const API = useSelect(_props, emit);\n    const {\n      calculatorRef,\n      inputStyle\n    } = useCalcInputWidth();\n    provide(selectKey, reactive({\n      props: _props,\n      states: API.states,\n      selectRef: API.selectRef,\n      optionsArray: API.optionsArray,\n      setSelected: API.setSelected,\n      handleOptionSelect: API.handleOptionSelect,\n      onOptionCreate: API.onOptionCreate,\n      onOptionDestroy: API.onOptionDestroy\n    }));\n    const selectedLabel = computed(() => {\n      if (!props.multiple) {\n        return API.states.selectedLabel;\n      }\n      return API.states.selected.map(i => i.currentLabel);\n    });\n    return {\n      ...API,\n      modelValue,\n      selectedLabel,\n      calculatorRef,\n      inputStyle\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache) {\n  const _component_el_tag = resolveComponent(\"el-tag\");\n  const _component_el_tooltip = resolveComponent(\"el-tooltip\");\n  const _component_el_icon = resolveComponent(\"el-icon\");\n  const _component_el_option = resolveComponent(\"el-option\");\n  const _component_el_options = resolveComponent(\"el-options\");\n  const _component_el_scrollbar = resolveComponent(\"el-scrollbar\");\n  const _component_el_select_menu = resolveComponent(\"el-select-menu\");\n  const _directive_click_outside = resolveDirective(\"click-outside\");\n  return withDirectives((openBlock(), createElementBlock(\"div\", {\n    ref: \"selectRef\",\n    class: normalizeClass([_ctx.nsSelect.b(), _ctx.nsSelect.m(_ctx.selectSize)]),\n    [toHandlerKey(_ctx.mouseEnterEventName)]: $event => _ctx.states.inputHovering = true,\n    onMouseleave: $event => _ctx.states.inputHovering = false\n  }, [createVNode(_component_el_tooltip, {\n    ref: \"tooltipRef\",\n    visible: _ctx.dropdownMenuVisible,\n    placement: _ctx.placement,\n    teleported: _ctx.teleported,\n    \"popper-class\": [_ctx.nsSelect.e(\"popper\"), _ctx.popperClass],\n    \"popper-options\": _ctx.popperOptions,\n    \"fallback-placements\": _ctx.fallbackPlacements,\n    effect: _ctx.effect,\n    pure: \"\",\n    trigger: \"click\",\n    transition: `${_ctx.nsSelect.namespace.value}-zoom-in-top`,\n    \"stop-popper-mouse-event\": false,\n    \"gpu-acceleration\": false,\n    persistent: _ctx.persistent,\n    \"append-to\": _ctx.appendTo,\n    \"show-arrow\": _ctx.showArrow,\n    offset: _ctx.offset,\n    onBeforeShow: _ctx.handleMenuEnter,\n    onHide: $event => _ctx.states.isBeforeHide = false\n  }, {\n    default: withCtx(() => {\n      var _a;\n      return [createElementVNode(\"div\", {\n        ref: \"wrapperRef\",\n        class: normalizeClass([_ctx.nsSelect.e(\"wrapper\"), _ctx.nsSelect.is(\"focused\", _ctx.isFocused), _ctx.nsSelect.is(\"hovering\", _ctx.states.inputHovering), _ctx.nsSelect.is(\"filterable\", _ctx.filterable), _ctx.nsSelect.is(\"disabled\", _ctx.selectDisabled)]),\n        onClick: withModifiers(_ctx.toggleMenu, [\"prevent\"])\n      }, [_ctx.$slots.prefix ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        ref: \"prefixRef\",\n        class: normalizeClass(_ctx.nsSelect.e(\"prefix\"))\n      }, [renderSlot(_ctx.$slots, \"prefix\")], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        ref: \"selectionRef\",\n        class: normalizeClass([_ctx.nsSelect.e(\"selection\"), _ctx.nsSelect.is(\"near\", _ctx.multiple && !_ctx.$slots.prefix && !!_ctx.states.selected.length)])\n      }, [_ctx.multiple ? renderSlot(_ctx.$slots, \"tag\", {\n        key: 0\n      }, () => [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.showTagList, item => {\n        return openBlock(), createElementBlock(\"div\", {\n          key: _ctx.getValueKey(item),\n          class: normalizeClass(_ctx.nsSelect.e(\"selected-item\"))\n        }, [createVNode(_component_el_tag, {\n          closable: !_ctx.selectDisabled && !item.isDisabled,\n          size: _ctx.collapseTagSize,\n          type: _ctx.tagType,\n          effect: _ctx.tagEffect,\n          \"disable-transitions\": \"\",\n          style: normalizeStyle(_ctx.tagStyle),\n          onClose: $event => _ctx.deleteTag($event, item)\n        }, {\n          default: withCtx(() => [createElementVNode(\"span\", {\n            class: normalizeClass(_ctx.nsSelect.e(\"tags-text\"))\n          }, [renderSlot(_ctx.$slots, \"label\", {\n            label: item.currentLabel,\n            value: item.value\n          }, () => [createTextVNode(toDisplayString(item.currentLabel), 1)])], 2)]),\n          _: 2\n        }, 1032, [\"closable\", \"size\", \"type\", \"effect\", \"style\", \"onClose\"])], 2);\n      }), 128)), _ctx.collapseTags && _ctx.states.selected.length > _ctx.maxCollapseTags ? (openBlock(), createBlock(_component_el_tooltip, {\n        key: 0,\n        ref: \"tagTooltipRef\",\n        disabled: _ctx.dropdownMenuVisible || !_ctx.collapseTagsTooltip,\n        \"fallback-placements\": [\"bottom\", \"top\", \"right\", \"left\"],\n        effect: _ctx.effect,\n        placement: \"bottom\",\n        teleported: _ctx.teleported\n      }, {\n        default: withCtx(() => [createElementVNode(\"div\", {\n          ref: \"collapseItemRef\",\n          class: normalizeClass(_ctx.nsSelect.e(\"selected-item\"))\n        }, [createVNode(_component_el_tag, {\n          closable: false,\n          size: _ctx.collapseTagSize,\n          type: _ctx.tagType,\n          effect: _ctx.tagEffect,\n          \"disable-transitions\": \"\",\n          style: normalizeStyle(_ctx.collapseTagStyle)\n        }, {\n          default: withCtx(() => [createElementVNode(\"span\", {\n            class: normalizeClass(_ctx.nsSelect.e(\"tags-text\"))\n          }, \" + \" + toDisplayString(_ctx.states.selected.length - _ctx.maxCollapseTags), 3)]),\n          _: 1\n        }, 8, [\"size\", \"type\", \"effect\", \"style\"])], 2)]),\n        content: withCtx(() => [createElementVNode(\"div\", {\n          ref: \"tagMenuRef\",\n          class: normalizeClass(_ctx.nsSelect.e(\"selection\"))\n        }, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.collapseTagList, item => {\n          return openBlock(), createElementBlock(\"div\", {\n            key: _ctx.getValueKey(item),\n            class: normalizeClass(_ctx.nsSelect.e(\"selected-item\"))\n          }, [createVNode(_component_el_tag, {\n            class: \"in-tooltip\",\n            closable: !_ctx.selectDisabled && !item.isDisabled,\n            size: _ctx.collapseTagSize,\n            type: _ctx.tagType,\n            effect: _ctx.tagEffect,\n            \"disable-transitions\": \"\",\n            onClose: $event => _ctx.deleteTag($event, item)\n          }, {\n            default: withCtx(() => [createElementVNode(\"span\", {\n              class: normalizeClass(_ctx.nsSelect.e(\"tags-text\"))\n            }, [renderSlot(_ctx.$slots, \"label\", {\n              label: item.currentLabel,\n              value: item.value\n            }, () => [createTextVNode(toDisplayString(item.currentLabel), 1)])], 2)]),\n            _: 2\n          }, 1032, [\"closable\", \"size\", \"type\", \"effect\", \"onClose\"])], 2);\n        }), 128))], 2)]),\n        _: 3\n      }, 8, [\"disabled\", \"effect\", \"teleported\"])) : createCommentVNode(\"v-if\", true)]) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass([_ctx.nsSelect.e(\"selected-item\"), _ctx.nsSelect.e(\"input-wrapper\"), _ctx.nsSelect.is(\"hidden\", !_ctx.filterable)])\n      }, [withDirectives(createElementVNode(\"input\", {\n        id: _ctx.inputId,\n        ref: \"inputRef\",\n        \"onUpdate:modelValue\": $event => _ctx.states.inputValue = $event,\n        type: \"text\",\n        name: _ctx.name,\n        class: normalizeClass([_ctx.nsSelect.e(\"input\"), _ctx.nsSelect.is(_ctx.selectSize)]),\n        disabled: _ctx.selectDisabled,\n        autocomplete: _ctx.autocomplete,\n        style: normalizeStyle(_ctx.inputStyle),\n        tabindex: _ctx.tabindex,\n        role: \"combobox\",\n        readonly: !_ctx.filterable,\n        spellcheck: \"false\",\n        \"aria-activedescendant\": ((_a = _ctx.hoverOption) == null ? void 0 : _a.id) || \"\",\n        \"aria-controls\": _ctx.contentId,\n        \"aria-expanded\": _ctx.dropdownMenuVisible,\n        \"aria-label\": _ctx.ariaLabel,\n        \"aria-autocomplete\": \"none\",\n        \"aria-haspopup\": \"listbox\",\n        onKeydown: [withKeys(withModifiers($event => _ctx.navigateOptions(\"next\"), [\"stop\", \"prevent\"]), [\"down\"]), withKeys(withModifiers($event => _ctx.navigateOptions(\"prev\"), [\"stop\", \"prevent\"]), [\"up\"]), withKeys(withModifiers(_ctx.handleEsc, [\"stop\", \"prevent\"]), [\"esc\"]), withKeys(withModifiers(_ctx.selectOption, [\"stop\", \"prevent\"]), [\"enter\"]), withKeys(withModifiers(_ctx.deletePrevTag, [\"stop\"]), [\"delete\"])],\n        onCompositionstart: _ctx.handleCompositionStart,\n        onCompositionupdate: _ctx.handleCompositionUpdate,\n        onCompositionend: _ctx.handleCompositionEnd,\n        onInput: _ctx.onInput,\n        onClick: withModifiers(_ctx.toggleMenu, [\"stop\"])\n      }, null, 46, [\"id\", \"onUpdate:modelValue\", \"name\", \"disabled\", \"autocomplete\", \"tabindex\", \"readonly\", \"aria-activedescendant\", \"aria-controls\", \"aria-expanded\", \"aria-label\", \"onKeydown\", \"onCompositionstart\", \"onCompositionupdate\", \"onCompositionend\", \"onInput\", \"onClick\"]), [[vModelText, _ctx.states.inputValue]]), _ctx.filterable ? (openBlock(), createElementBlock(\"span\", {\n        key: 0,\n        ref: \"calculatorRef\",\n        \"aria-hidden\": \"true\",\n        class: normalizeClass(_ctx.nsSelect.e(\"input-calculator\")),\n        textContent: toDisplayString(_ctx.states.inputValue)\n      }, null, 10, [\"textContent\"])) : createCommentVNode(\"v-if\", true)], 2), _ctx.shouldShowPlaceholder ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass([_ctx.nsSelect.e(\"selected-item\"), _ctx.nsSelect.e(\"placeholder\"), _ctx.nsSelect.is(\"transparent\", !_ctx.hasModelValue || _ctx.expanded && !_ctx.states.inputValue)])\n      }, [_ctx.hasModelValue ? renderSlot(_ctx.$slots, \"label\", {\n        key: 0,\n        label: _ctx.currentPlaceholder,\n        value: _ctx.modelValue\n      }, () => [createElementVNode(\"span\", null, toDisplayString(_ctx.currentPlaceholder), 1)]) : (openBlock(), createElementBlock(\"span\", {\n        key: 1\n      }, toDisplayString(_ctx.currentPlaceholder), 1))], 2)) : createCommentVNode(\"v-if\", true)], 2), createElementVNode(\"div\", {\n        ref: \"suffixRef\",\n        class: normalizeClass(_ctx.nsSelect.e(\"suffix\"))\n      }, [_ctx.iconComponent && !_ctx.showClose ? (openBlock(), createBlock(_component_el_icon, {\n        key: 0,\n        class: normalizeClass([_ctx.nsSelect.e(\"caret\"), _ctx.nsSelect.e(\"icon\"), _ctx.iconReverse])\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.iconComponent)))]),\n        _: 1\n      }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true), _ctx.showClose && _ctx.clearIcon ? (openBlock(), createBlock(_component_el_icon, {\n        key: 1,\n        class: normalizeClass([_ctx.nsSelect.e(\"caret\"), _ctx.nsSelect.e(\"icon\"), _ctx.nsSelect.e(\"clear\")]),\n        onClick: _ctx.handleClearClick\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.clearIcon)))]),\n        _: 1\n      }, 8, [\"class\", \"onClick\"])) : createCommentVNode(\"v-if\", true), _ctx.validateState && _ctx.validateIcon && _ctx.needStatusIcon ? (openBlock(), createBlock(_component_el_icon, {\n        key: 2,\n        class: normalizeClass([_ctx.nsInput.e(\"icon\"), _ctx.nsInput.e(\"validateIcon\"), _ctx.nsInput.is(\"loading\", _ctx.validateState === \"validating\")])\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.validateIcon)))]),\n        _: 1\n      }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true)], 2)], 10, [\"onClick\"])];\n    }),\n    content: withCtx(() => [createVNode(_component_el_select_menu, {\n      ref: \"menuRef\"\n    }, {\n      default: withCtx(() => [_ctx.$slots.header ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(_ctx.nsSelect.be(\"dropdown\", \"header\")),\n        onClick: withModifiers(() => {}, [\"stop\"])\n      }, [renderSlot(_ctx.$slots, \"header\")], 10, [\"onClick\"])) : createCommentVNode(\"v-if\", true), withDirectives(createVNode(_component_el_scrollbar, {\n        id: _ctx.contentId,\n        ref: \"scrollbarRef\",\n        tag: \"ul\",\n        \"wrap-class\": _ctx.nsSelect.be(\"dropdown\", \"wrap\"),\n        \"view-class\": _ctx.nsSelect.be(\"dropdown\", \"list\"),\n        class: normalizeClass([_ctx.nsSelect.is(\"empty\", _ctx.filteredOptionsCount === 0)]),\n        role: \"listbox\",\n        \"aria-label\": _ctx.ariaLabel,\n        \"aria-orientation\": \"vertical\",\n        onScroll: _ctx.popupScroll\n      }, {\n        default: withCtx(() => [_ctx.showNewOption ? (openBlock(), createBlock(_component_el_option, {\n          key: 0,\n          value: _ctx.states.inputValue,\n          created: true\n        }, null, 8, [\"value\"])) : createCommentVNode(\"v-if\", true), createVNode(_component_el_options, null, {\n          default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n          _: 3\n        })]),\n        _: 3\n      }, 8, [\"id\", \"wrap-class\", \"view-class\", \"class\", \"aria-label\", \"onScroll\"]), [[vShow, _ctx.states.options.size > 0 && !_ctx.loading]]), _ctx.$slots.loading && _ctx.loading ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(_ctx.nsSelect.be(\"dropdown\", \"loading\"))\n      }, [renderSlot(_ctx.$slots, \"loading\")], 2)) : _ctx.loading || _ctx.filteredOptionsCount === 0 ? (openBlock(), createElementBlock(\"div\", {\n        key: 2,\n        class: normalizeClass(_ctx.nsSelect.be(\"dropdown\", \"empty\"))\n      }, [renderSlot(_ctx.$slots, \"empty\", {}, () => [createElementVNode(\"span\", null, toDisplayString(_ctx.emptyText), 1)])], 2)) : createCommentVNode(\"v-if\", true), _ctx.$slots.footer ? (openBlock(), createElementBlock(\"div\", {\n        key: 3,\n        class: normalizeClass(_ctx.nsSelect.be(\"dropdown\", \"footer\")),\n        onClick: withModifiers(() => {}, [\"stop\"])\n      }, [renderSlot(_ctx.$slots, \"footer\")], 10, [\"onClick\"])) : createCommentVNode(\"v-if\", true)]),\n      _: 3\n    }, 512)]),\n    _: 3\n  }, 8, [\"visible\", \"placement\", \"teleported\", \"popper-class\", \"popper-options\", \"fallback-placements\", \"effect\", \"transition\", \"persistent\", \"append-to\", \"show-arrow\", \"offset\", \"onBeforeShow\", \"onHide\"])], 16, [\"onMouseleave\"])), [[_directive_click_outside, _ctx.handleClickOutside, _ctx.popperRef]]);\n}\nvar Select = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"select.vue\"]]);\nexport { Select as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}