{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, useAttrs, ref, computed, watch, nextTick, onMounted, openBlock, createBlock, unref, withCtx, withDirectives, createElementBlock, normalizeClass, normalizeStyle, createVNode, createSlots, withModifiers, renderSlot, Fragment, renderList, toDisplayString, createElementVNode, withKeys, vModelText, createCommentVNode, isRef, vShow } from 'vue';\nimport { cloneDeep, debounce } from 'lodash-unified';\nimport { useCssVar, useResizeObserver, isClient } from '@vueuse/core';\nimport { ElCascaderPanel } from '../../cascader-panel/index.mjs';\nimport { ElInput } from '../../input/index.mjs';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { ElScrollbar } from '../../scrollbar/index.mjs';\nimport { ElTag } from '../../tag/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { CircleClose, ArrowDown, Check } from '@element-plus/icons-vue';\nimport { cascaderProps, cascaderEmits } from './cascader.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport ClickOutside from '../../../directives/click-outside/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useFormItem } from '../../form/src/hooks/use-form-item.mjs';\nimport { useEmptyValues } from '../../../hooks/use-empty-values/index.mjs';\nimport { useComposition } from '../../../hooks/use-composition/index.mjs';\nimport { useFormSize } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { isPromise } from '@vue/shared';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { focusNode, getSibling } from '../../../utils/dom/aria.mjs';\nconst COMPONENT_NAME = \"ElCascader\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: cascaderProps,\n  emits: cascaderEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const popperOptions = {\n      modifiers: [{\n        name: \"arrowPosition\",\n        enabled: true,\n        phase: \"main\",\n        fn: ({\n          state\n        }) => {\n          const {\n            modifiersData,\n            placement\n          } = state;\n          if ([\"right\", \"left\", \"bottom\", \"top\"].includes(placement)) return;\n          modifiersData.arrow.x = 35;\n        },\n        requires: [\"arrow\"]\n      }]\n    };\n    const attrs = useAttrs();\n    let inputInitialHeight = 0;\n    let pressDeleteCount = 0;\n    const nsCascader = useNamespace(\"cascader\");\n    const nsInput = useNamespace(\"input\");\n    const {\n      t\n    } = useLocale();\n    const {\n      form,\n      formItem\n    } = useFormItem();\n    const {\n      valueOnClear\n    } = useEmptyValues(props);\n    const {\n      isComposing,\n      handleComposition\n    } = useComposition({\n      afterComposition(event) {\n        var _a;\n        const text = (_a = event.target) == null ? void 0 : _a.value;\n        handleInput(text);\n      }\n    });\n    const tooltipRef = ref(null);\n    const input = ref(null);\n    const tagWrapper = ref(null);\n    const cascaderPanelRef = ref(null);\n    const suggestionPanel = ref(null);\n    const popperVisible = ref(false);\n    const inputHover = ref(false);\n    const filtering = ref(false);\n    const filterFocus = ref(false);\n    const inputValue = ref(\"\");\n    const searchInputValue = ref(\"\");\n    const presentTags = ref([]);\n    const allPresentTags = ref([]);\n    const suggestions = ref([]);\n    const cascaderStyle = computed(() => {\n      return attrs.style;\n    });\n    const isDisabled = computed(() => props.disabled || (form == null ? void 0 : form.disabled));\n    const inputPlaceholder = computed(() => props.placeholder || t(\"el.cascader.placeholder\"));\n    const currentPlaceholder = computed(() => searchInputValue.value || presentTags.value.length > 0 || isComposing.value ? \"\" : inputPlaceholder.value);\n    const realSize = useFormSize();\n    const tagSize = computed(() => realSize.value === \"small\" ? \"small\" : \"default\");\n    const multiple = computed(() => !!props.props.multiple);\n    const readonly = computed(() => !props.filterable || multiple.value);\n    const searchKeyword = computed(() => multiple.value ? searchInputValue.value : inputValue.value);\n    const checkedNodes = computed(() => {\n      var _a;\n      return ((_a = cascaderPanelRef.value) == null ? void 0 : _a.checkedNodes) || [];\n    });\n    const clearBtnVisible = computed(() => {\n      if (!props.clearable || isDisabled.value || filtering.value || !inputHover.value) return false;\n      return !!checkedNodes.value.length;\n    });\n    const presentText = computed(() => {\n      const {\n        showAllLevels,\n        separator\n      } = props;\n      const nodes = checkedNodes.value;\n      return nodes.length ? multiple.value ? \"\" : nodes[0].calcText(showAllLevels, separator) : \"\";\n    });\n    const validateState = computed(() => (formItem == null ? void 0 : formItem.validateState) || \"\");\n    const checkedValue = computed({\n      get() {\n        return cloneDeep(props.modelValue);\n      },\n      set(val) {\n        const value = val != null ? val : valueOnClear.value;\n        emit(UPDATE_MODEL_EVENT, value);\n        emit(CHANGE_EVENT, value);\n        if (props.validateEvent) {\n          formItem == null ? void 0 : formItem.validate(\"change\").catch(err => debugWarn(err));\n        }\n      }\n    });\n    const cascaderKls = computed(() => {\n      return [nsCascader.b(), nsCascader.m(realSize.value), nsCascader.is(\"disabled\", isDisabled.value), attrs.class];\n    });\n    const cascaderIconKls = computed(() => {\n      return [nsInput.e(\"icon\"), \"icon-arrow-down\", nsCascader.is(\"reverse\", popperVisible.value)];\n    });\n    const inputClass = computed(() => {\n      return nsCascader.is(\"focus\", popperVisible.value || filterFocus.value);\n    });\n    const contentRef = computed(() => {\n      var _a, _b;\n      return (_b = (_a = tooltipRef.value) == null ? void 0 : _a.popperRef) == null ? void 0 : _b.contentRef;\n    });\n    const togglePopperVisible = visible => {\n      var _a, _b, _c;\n      if (isDisabled.value) return;\n      visible = visible != null ? visible : !popperVisible.value;\n      if (visible !== popperVisible.value) {\n        popperVisible.value = visible;\n        (_b = (_a = input.value) == null ? void 0 : _a.input) == null ? void 0 : _b.setAttribute(\"aria-expanded\", `${visible}`);\n        if (visible) {\n          updatePopperPosition();\n          nextTick((_c = cascaderPanelRef.value) == null ? void 0 : _c.scrollToExpandingNode);\n        } else if (props.filterable) {\n          syncPresentTextValue();\n        }\n        emit(\"visibleChange\", visible);\n      }\n    };\n    const updatePopperPosition = () => {\n      nextTick(() => {\n        var _a;\n        (_a = tooltipRef.value) == null ? void 0 : _a.updatePopper();\n      });\n    };\n    const hideSuggestionPanel = () => {\n      filtering.value = false;\n    };\n    const genTag = node => {\n      const {\n        showAllLevels,\n        separator\n      } = props;\n      return {\n        node,\n        key: node.uid,\n        text: node.calcText(showAllLevels, separator),\n        hitState: false,\n        closable: !isDisabled.value && !node.isDisabled,\n        isCollapseTag: false\n      };\n    };\n    const deleteTag = tag => {\n      var _a;\n      const node = tag.node;\n      node.doCheck(false);\n      (_a = cascaderPanelRef.value) == null ? void 0 : _a.calculateCheckedValue();\n      emit(\"removeTag\", node.valueByOption);\n    };\n    const calculatePresentTags = () => {\n      if (!multiple.value) return;\n      const nodes = checkedNodes.value;\n      const tags = [];\n      const allTags = [];\n      nodes.forEach(node => allTags.push(genTag(node)));\n      allPresentTags.value = allTags;\n      if (nodes.length) {\n        nodes.slice(0, props.maxCollapseTags).forEach(node => tags.push(genTag(node)));\n        const rest = nodes.slice(props.maxCollapseTags);\n        const restCount = rest.length;\n        if (restCount) {\n          if (props.collapseTags) {\n            tags.push({\n              key: -1,\n              text: `+ ${restCount}`,\n              closable: false,\n              isCollapseTag: true\n            });\n          } else {\n            rest.forEach(node => tags.push(genTag(node)));\n          }\n        }\n      }\n      presentTags.value = tags;\n    };\n    const calculateSuggestions = () => {\n      var _a, _b;\n      const {\n        filterMethod,\n        showAllLevels,\n        separator\n      } = props;\n      const res = (_b = (_a = cascaderPanelRef.value) == null ? void 0 : _a.getFlattedNodes(!props.props.checkStrictly)) == null ? void 0 : _b.filter(node => {\n        if (node.isDisabled) return false;\n        node.calcText(showAllLevels, separator);\n        return filterMethod(node, searchKeyword.value);\n      });\n      if (multiple.value) {\n        presentTags.value.forEach(tag => {\n          tag.hitState = false;\n        });\n        allPresentTags.value.forEach(tag => {\n          tag.hitState = false;\n        });\n      }\n      filtering.value = true;\n      suggestions.value = res;\n      updatePopperPosition();\n    };\n    const focusFirstNode = () => {\n      var _a;\n      let firstNode;\n      if (filtering.value && suggestionPanel.value) {\n        firstNode = suggestionPanel.value.$el.querySelector(`.${nsCascader.e(\"suggestion-item\")}`);\n      } else {\n        firstNode = (_a = cascaderPanelRef.value) == null ? void 0 : _a.$el.querySelector(`.${nsCascader.b(\"node\")}[tabindex=\"-1\"]`);\n      }\n      if (firstNode) {\n        firstNode.focus();\n        !filtering.value && firstNode.click();\n      }\n    };\n    const updateStyle = () => {\n      var _a, _b;\n      const inputInner = (_a = input.value) == null ? void 0 : _a.input;\n      const tagWrapperEl = tagWrapper.value;\n      const suggestionPanelEl = (_b = suggestionPanel.value) == null ? void 0 : _b.$el;\n      if (!isClient || !inputInner) return;\n      if (suggestionPanelEl) {\n        const suggestionList = suggestionPanelEl.querySelector(`.${nsCascader.e(\"suggestion-list\")}`);\n        suggestionList.style.minWidth = `${inputInner.offsetWidth}px`;\n      }\n      if (tagWrapperEl) {\n        const {\n          offsetHeight\n        } = tagWrapperEl;\n        const height = presentTags.value.length > 0 ? `${Math.max(offsetHeight, inputInitialHeight) - 2}px` : `${inputInitialHeight}px`;\n        inputInner.style.height = height;\n        updatePopperPosition();\n      }\n    };\n    const getCheckedNodes = leafOnly => {\n      var _a;\n      return (_a = cascaderPanelRef.value) == null ? void 0 : _a.getCheckedNodes(leafOnly);\n    };\n    const handleExpandChange = value => {\n      updatePopperPosition();\n      emit(\"expandChange\", value);\n    };\n    const handleKeyDown = e => {\n      if (isComposing.value) return;\n      switch (e.code) {\n        case EVENT_CODE.enter:\n        case EVENT_CODE.numpadEnter:\n          togglePopperVisible();\n          break;\n        case EVENT_CODE.down:\n          togglePopperVisible(true);\n          nextTick(focusFirstNode);\n          e.preventDefault();\n          break;\n        case EVENT_CODE.esc:\n          if (popperVisible.value === true) {\n            e.preventDefault();\n            e.stopPropagation();\n            togglePopperVisible(false);\n          }\n          break;\n        case EVENT_CODE.tab:\n          togglePopperVisible(false);\n          break;\n      }\n    };\n    const handleClear = () => {\n      var _a;\n      (_a = cascaderPanelRef.value) == null ? void 0 : _a.clearCheckedNodes();\n      if (!popperVisible.value && props.filterable) {\n        syncPresentTextValue();\n      }\n      togglePopperVisible(false);\n      emit(\"clear\");\n    };\n    const syncPresentTextValue = () => {\n      const {\n        value\n      } = presentText;\n      inputValue.value = value;\n      searchInputValue.value = value;\n    };\n    const handleSuggestionClick = node => {\n      var _a, _b;\n      const {\n        checked\n      } = node;\n      if (multiple.value) {\n        (_a = cascaderPanelRef.value) == null ? void 0 : _a.handleCheckChange(node, !checked, false);\n      } else {\n        !checked && ((_b = cascaderPanelRef.value) == null ? void 0 : _b.handleCheckChange(node, true, false));\n        togglePopperVisible(false);\n      }\n    };\n    const handleSuggestionKeyDown = e => {\n      const target = e.target;\n      const {\n        code\n      } = e;\n      switch (code) {\n        case EVENT_CODE.up:\n        case EVENT_CODE.down:\n          {\n            e.preventDefault();\n            const distance = code === EVENT_CODE.up ? -1 : 1;\n            focusNode(getSibling(target, distance, `.${nsCascader.e(\"suggestion-item\")}[tabindex=\"-1\"]`));\n            break;\n          }\n        case EVENT_CODE.enter:\n        case EVENT_CODE.numpadEnter:\n          target.click();\n          break;\n      }\n    };\n    const handleDelete = () => {\n      const tags = presentTags.value;\n      const lastTag = tags[tags.length - 1];\n      pressDeleteCount = searchInputValue.value ? 0 : pressDeleteCount + 1;\n      if (!lastTag || !pressDeleteCount || props.collapseTags && tags.length > 1) return;\n      if (lastTag.hitState) {\n        deleteTag(lastTag);\n      } else {\n        lastTag.hitState = true;\n      }\n    };\n    const handleFocus = e => {\n      const el = e.target;\n      const name = nsCascader.e(\"search-input\");\n      if (el.className === name) {\n        filterFocus.value = true;\n      }\n      emit(\"focus\", e);\n    };\n    const handleBlur = e => {\n      filterFocus.value = false;\n      emit(\"blur\", e);\n    };\n    const handleFilter = debounce(() => {\n      const {\n        value\n      } = searchKeyword;\n      if (!value) return;\n      const passed = props.beforeFilter(value);\n      if (isPromise(passed)) {\n        passed.then(calculateSuggestions).catch(() => {});\n      } else if (passed !== false) {\n        calculateSuggestions();\n      } else {\n        hideSuggestionPanel();\n      }\n    }, props.debounce);\n    const handleInput = (val, e) => {\n      !popperVisible.value && togglePopperVisible(true);\n      if (e == null ? void 0 : e.isComposing) return;\n      val ? handleFilter() : hideSuggestionPanel();\n    };\n    const getInputInnerHeight = inputInner => Number.parseFloat(useCssVar(nsInput.cssVarName(\"input-height\"), inputInner).value) - 2;\n    watch(filtering, updatePopperPosition);\n    watch([checkedNodes, isDisabled, () => props.collapseTags], calculatePresentTags);\n    watch(presentTags, () => {\n      nextTick(() => updateStyle());\n    });\n    watch(realSize, async () => {\n      await nextTick();\n      const inputInner = input.value.input;\n      inputInitialHeight = getInputInnerHeight(inputInner) || inputInitialHeight;\n      updateStyle();\n    });\n    watch(presentText, syncPresentTextValue, {\n      immediate: true\n    });\n    onMounted(() => {\n      const inputInner = input.value.input;\n      const inputInnerHeight = getInputInnerHeight(inputInner);\n      inputInitialHeight = inputInner.offsetHeight || inputInnerHeight;\n      useResizeObserver(inputInner, updateStyle);\n    });\n    expose({\n      getCheckedNodes,\n      cascaderPanelRef,\n      togglePopperVisible,\n      contentRef,\n      presentText\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElTooltip), {\n        ref_key: \"tooltipRef\",\n        ref: tooltipRef,\n        visible: popperVisible.value,\n        teleported: _ctx.teleported,\n        \"popper-class\": [unref(nsCascader).e(\"dropdown\"), _ctx.popperClass],\n        \"popper-options\": popperOptions,\n        \"fallback-placements\": _ctx.fallbackPlacements,\n        \"stop-popper-mouse-event\": false,\n        \"gpu-acceleration\": false,\n        placement: _ctx.placement,\n        transition: `${unref(nsCascader).namespace.value}-zoom-in-top`,\n        effect: \"light\",\n        pure: \"\",\n        persistent: _ctx.persistent,\n        onHide: hideSuggestionPanel\n      }, {\n        default: withCtx(() => [withDirectives((openBlock(), createElementBlock(\"div\", {\n          class: normalizeClass(unref(cascaderKls)),\n          style: normalizeStyle(unref(cascaderStyle)),\n          onClick: () => togglePopperVisible(unref(readonly) ? void 0 : true),\n          onKeydown: handleKeyDown,\n          onMouseenter: $event => inputHover.value = true,\n          onMouseleave: $event => inputHover.value = false\n        }, [createVNode(unref(ElInput), {\n          ref_key: \"input\",\n          ref: input,\n          modelValue: inputValue.value,\n          \"onUpdate:modelValue\": $event => inputValue.value = $event,\n          placeholder: unref(currentPlaceholder),\n          readonly: unref(readonly),\n          disabled: unref(isDisabled),\n          \"validate-event\": false,\n          size: unref(realSize),\n          class: normalizeClass(unref(inputClass)),\n          tabindex: unref(multiple) && _ctx.filterable && !unref(isDisabled) ? -1 : void 0,\n          onCompositionstart: unref(handleComposition),\n          onCompositionupdate: unref(handleComposition),\n          onCompositionend: unref(handleComposition),\n          onFocus: handleFocus,\n          onBlur: handleBlur,\n          onInput: handleInput\n        }, createSlots({\n          suffix: withCtx(() => [unref(clearBtnVisible) ? (openBlock(), createBlock(unref(ElIcon), {\n            key: \"clear\",\n            class: normalizeClass([unref(nsInput).e(\"icon\"), \"icon-circle-close\"]),\n            onClick: withModifiers(handleClear, [\"stop\"])\n          }, {\n            default: withCtx(() => [createVNode(unref(CircleClose))]),\n            _: 1\n          }, 8, [\"class\", \"onClick\"])) : (openBlock(), createBlock(unref(ElIcon), {\n            key: \"arrow-down\",\n            class: normalizeClass(unref(cascaderIconKls)),\n            onClick: withModifiers($event => togglePopperVisible(), [\"stop\"])\n          }, {\n            default: withCtx(() => [createVNode(unref(ArrowDown))]),\n            _: 1\n          }, 8, [\"class\", \"onClick\"]))]),\n          _: 2\n        }, [_ctx.$slots.prefix ? {\n          name: \"prefix\",\n          fn: withCtx(() => [renderSlot(_ctx.$slots, \"prefix\")])\n        } : void 0]), 1032, [\"modelValue\", \"onUpdate:modelValue\", \"placeholder\", \"readonly\", \"disabled\", \"size\", \"class\", \"tabindex\", \"onCompositionstart\", \"onCompositionupdate\", \"onCompositionend\"]), unref(multiple) ? (openBlock(), createElementBlock(\"div\", {\n          key: 0,\n          ref_key: \"tagWrapper\",\n          ref: tagWrapper,\n          class: normalizeClass([unref(nsCascader).e(\"tags\"), unref(nsCascader).is(\"validate\", Boolean(unref(validateState)))])\n        }, [(openBlock(true), createElementBlock(Fragment, null, renderList(presentTags.value, tag => {\n          return openBlock(), createBlock(unref(ElTag), {\n            key: tag.key,\n            type: _ctx.tagType,\n            size: unref(tagSize),\n            effect: _ctx.tagEffect,\n            hit: tag.hitState,\n            closable: tag.closable,\n            \"disable-transitions\": \"\",\n            onClose: $event => deleteTag(tag)\n          }, {\n            default: withCtx(() => [tag.isCollapseTag === false ? (openBlock(), createElementBlock(\"span\", {\n              key: 0\n            }, toDisplayString(tag.text), 1)) : (openBlock(), createBlock(unref(ElTooltip), {\n              key: 1,\n              disabled: popperVisible.value || !_ctx.collapseTagsTooltip,\n              \"fallback-placements\": [\"bottom\", \"top\", \"right\", \"left\"],\n              placement: \"bottom\",\n              effect: \"light\"\n            }, {\n              default: withCtx(() => [createElementVNode(\"span\", null, toDisplayString(tag.text), 1)]),\n              content: withCtx(() => [createElementVNode(\"div\", {\n                class: normalizeClass(unref(nsCascader).e(\"collapse-tags\"))\n              }, [(openBlock(true), createElementBlock(Fragment, null, renderList(allPresentTags.value.slice(_ctx.maxCollapseTags), (tag2, idx) => {\n                return openBlock(), createElementBlock(\"div\", {\n                  key: idx,\n                  class: normalizeClass(unref(nsCascader).e(\"collapse-tag\"))\n                }, [(openBlock(), createBlock(unref(ElTag), {\n                  key: tag2.key,\n                  class: \"in-tooltip\",\n                  type: _ctx.tagType,\n                  size: unref(tagSize),\n                  effect: _ctx.tagEffect,\n                  hit: tag2.hitState,\n                  closable: tag2.closable,\n                  \"disable-transitions\": \"\",\n                  onClose: $event => deleteTag(tag2)\n                }, {\n                  default: withCtx(() => [createElementVNode(\"span\", null, toDisplayString(tag2.text), 1)]),\n                  _: 2\n                }, 1032, [\"type\", \"size\", \"effect\", \"hit\", \"closable\", \"onClose\"]))], 2);\n              }), 128))], 2)]),\n              _: 2\n            }, 1032, [\"disabled\"]))]),\n            _: 2\n          }, 1032, [\"type\", \"size\", \"effect\", \"hit\", \"closable\", \"onClose\"]);\n        }), 128)), _ctx.filterable && !unref(isDisabled) ? withDirectives((openBlock(), createElementBlock(\"input\", {\n          key: 0,\n          \"onUpdate:modelValue\": $event => searchInputValue.value = $event,\n          type: \"text\",\n          class: normalizeClass(unref(nsCascader).e(\"search-input\")),\n          placeholder: unref(presentText) ? \"\" : unref(inputPlaceholder),\n          onInput: e => handleInput(searchInputValue.value, e),\n          onClick: withModifiers($event => togglePopperVisible(true), [\"stop\"]),\n          onKeydown: withKeys(handleDelete, [\"delete\"]),\n          onCompositionstart: unref(handleComposition),\n          onCompositionupdate: unref(handleComposition),\n          onCompositionend: unref(handleComposition),\n          onFocus: handleFocus,\n          onBlur: handleBlur\n        }, null, 42, [\"onUpdate:modelValue\", \"placeholder\", \"onInput\", \"onClick\", \"onKeydown\", \"onCompositionstart\", \"onCompositionupdate\", \"onCompositionend\"])), [[vModelText, searchInputValue.value]]) : createCommentVNode(\"v-if\", true)], 2)) : createCommentVNode(\"v-if\", true)], 46, [\"onClick\", \"onMouseenter\", \"onMouseleave\"])), [[unref(ClickOutside), () => togglePopperVisible(false), unref(contentRef)]])]),\n        content: withCtx(() => [withDirectives(createVNode(unref(ElCascaderPanel), {\n          ref_key: \"cascaderPanelRef\",\n          ref: cascaderPanelRef,\n          modelValue: unref(checkedValue),\n          \"onUpdate:modelValue\": $event => isRef(checkedValue) ? checkedValue.value = $event : null,\n          options: _ctx.options,\n          props: props.props,\n          border: false,\n          \"render-label\": _ctx.$slots.default,\n          onExpandChange: handleExpandChange,\n          onClose: $event => _ctx.$nextTick(() => togglePopperVisible(false))\n        }, {\n          empty: withCtx(() => [renderSlot(_ctx.$slots, \"empty\")]),\n          _: 3\n        }, 8, [\"modelValue\", \"onUpdate:modelValue\", \"options\", \"props\", \"render-label\", \"onClose\"]), [[vShow, !filtering.value]]), _ctx.filterable ? withDirectives((openBlock(), createBlock(unref(ElScrollbar), {\n          key: 0,\n          ref_key: \"suggestionPanel\",\n          ref: suggestionPanel,\n          tag: \"ul\",\n          class: normalizeClass(unref(nsCascader).e(\"suggestion-panel\")),\n          \"view-class\": unref(nsCascader).e(\"suggestion-list\"),\n          onKeydown: handleSuggestionKeyDown\n        }, {\n          default: withCtx(() => [suggestions.value.length ? (openBlock(true), createElementBlock(Fragment, {\n            key: 0\n          }, renderList(suggestions.value, item => {\n            return openBlock(), createElementBlock(\"li\", {\n              key: item.uid,\n              class: normalizeClass([unref(nsCascader).e(\"suggestion-item\"), unref(nsCascader).is(\"checked\", item.checked)]),\n              tabindex: -1,\n              onClick: $event => handleSuggestionClick(item)\n            }, [renderSlot(_ctx.$slots, \"suggestion-item\", {\n              item\n            }, () => [createElementVNode(\"span\", null, toDisplayString(item.text), 1), item.checked ? (openBlock(), createBlock(unref(ElIcon), {\n              key: 0\n            }, {\n              default: withCtx(() => [createVNode(unref(Check))]),\n              _: 1\n            })) : createCommentVNode(\"v-if\", true)])], 10, [\"onClick\"]);\n          }), 128)) : renderSlot(_ctx.$slots, \"empty\", {\n            key: 1\n          }, () => [createElementVNode(\"li\", {\n            class: normalizeClass(unref(nsCascader).e(\"empty-text\"))\n          }, toDisplayString(unref(t)(\"el.cascader.noMatch\")), 3)])]),\n          _: 3\n        }, 8, [\"class\", \"view-class\"])), [[vShow, filtering.value]]) : createCommentVNode(\"v-if\", true)]),\n        _: 3\n      }, 8, [\"visible\", \"teleported\", \"popper-class\", \"fallback-placements\", \"placement\", \"transition\", \"persistent\"]);\n    };\n  }\n});\nvar Cascader = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"cascader.vue\"]]);\nexport { Cascader as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}