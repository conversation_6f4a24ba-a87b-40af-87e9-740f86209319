{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, ref, computed, onMounted, watch, provide, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, createCommentVNode, createElementVNode, renderSlot } from 'vue';\nimport { useEventListener } from '@vueuse/core';\nimport { anchorProps, anchorEmits } from './anchor.mjs';\nimport { anchorKey } from './constants.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { getElement } from '../../../utils/dom/element.mjs';\nimport { throttleByRaf } from '../../../utils/throttleByRaf.mjs';\nimport { isWindow, isUndefined } from '../../../utils/types.mjs';\nimport { getScrollElement, animateScrollTo, getScrollTop } from '../../../utils/dom/scroll.mjs';\nimport { getOffsetTopDistance } from '../../../utils/dom/position.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { CHANGE_EVENT } from '../../../constants/event.mjs';\nconst __default__ = defineComponent({\n  name: \"ElAnchor\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: anchorProps,\n  emits: anchorEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const currentAnchor = ref(\"\");\n    const anchorRef = ref(null);\n    const markerRef = ref(null);\n    const containerEl = ref();\n    const links = {};\n    let isScrolling = false;\n    let currentScrollTop = 0;\n    const ns = useNamespace(\"anchor\");\n    const cls = computed(() => [ns.b(), props.type === \"underline\" ? ns.m(\"underline\") : \"\", ns.m(props.direction)]);\n    const addLink = state => {\n      links[state.href] = state.el;\n    };\n    const removeLink = href => {\n      delete links[href];\n    };\n    const setCurrentAnchor = href => {\n      const activeHref = currentAnchor.value;\n      if (activeHref !== href) {\n        currentAnchor.value = href;\n        emit(CHANGE_EVENT, href);\n      }\n    };\n    let clearAnimate = null;\n    const scrollToAnchor = href => {\n      if (!containerEl.value) return;\n      const target = getElement(href);\n      if (!target) return;\n      if (clearAnimate) clearAnimate();\n      isScrolling = true;\n      const scrollEle = getScrollElement(target, containerEl.value);\n      const distance = getOffsetTopDistance(target, scrollEle);\n      const max = scrollEle.scrollHeight - scrollEle.clientHeight;\n      const to = Math.min(distance - props.offset, max);\n      clearAnimate = animateScrollTo(containerEl.value, currentScrollTop, to, props.duration, () => {\n        setTimeout(() => {\n          isScrolling = false;\n        }, 20);\n      });\n    };\n    const scrollTo = href => {\n      if (href) {\n        setCurrentAnchor(href);\n        scrollToAnchor(href);\n      }\n    };\n    const handleClick = (e, href) => {\n      emit(\"click\", e, href);\n      scrollTo(href);\n    };\n    const handleScroll = throttleByRaf(() => {\n      if (containerEl.value) {\n        currentScrollTop = getScrollTop(containerEl.value);\n      }\n      const currentHref = getCurrentHref();\n      if (isScrolling || isUndefined(currentHref)) return;\n      setCurrentAnchor(currentHref);\n    });\n    const getCurrentHref = () => {\n      if (!containerEl.value) return;\n      const scrollTop = getScrollTop(containerEl.value);\n      const anchorTopList = [];\n      for (const href of Object.keys(links)) {\n        const target = getElement(href);\n        if (!target) continue;\n        const scrollEle = getScrollElement(target, containerEl.value);\n        const distance = getOffsetTopDistance(target, scrollEle);\n        anchorTopList.push({\n          top: distance - props.offset - props.bound,\n          href\n        });\n      }\n      anchorTopList.sort((prev, next) => prev.top - next.top);\n      for (let i = 0; i < anchorTopList.length; i++) {\n        const item = anchorTopList[i];\n        const next = anchorTopList[i + 1];\n        if (i === 0 && scrollTop === 0) {\n          return props.selectScrollTop ? item.href : \"\";\n        }\n        if (item.top <= scrollTop && (!next || next.top > scrollTop)) {\n          return item.href;\n        }\n      }\n    };\n    const getContainer = () => {\n      const el = getElement(props.container);\n      if (!el || isWindow(el)) {\n        containerEl.value = window;\n      } else {\n        containerEl.value = el;\n      }\n    };\n    useEventListener(containerEl, \"scroll\", handleScroll);\n    const markerStyle = computed(() => {\n      if (!anchorRef.value || !markerRef.value || !currentAnchor.value) return {};\n      const currentLinkEl = links[currentAnchor.value];\n      if (!currentLinkEl) return {};\n      const anchorRect = anchorRef.value.getBoundingClientRect();\n      const markerRect = markerRef.value.getBoundingClientRect();\n      const linkRect = currentLinkEl.getBoundingClientRect();\n      if (props.direction === \"horizontal\") {\n        const left = linkRect.left - anchorRect.left;\n        return {\n          left: `${left}px`,\n          width: `${linkRect.width}px`,\n          opacity: 1\n        };\n      } else {\n        const top = linkRect.top - anchorRect.top + (linkRect.height - markerRect.height) / 2;\n        return {\n          top: `${top}px`,\n          opacity: 1\n        };\n      }\n    });\n    onMounted(() => {\n      getContainer();\n      const hash = decodeURIComponent(window.location.hash);\n      const target = getElement(hash);\n      if (target) {\n        scrollTo(hash);\n      } else {\n        handleScroll();\n      }\n    });\n    watch(() => props.container, () => {\n      getContainer();\n    });\n    provide(anchorKey, {\n      ns,\n      direction: props.direction,\n      currentAnchor,\n      addLink,\n      removeLink,\n      handleClick\n    });\n    expose({\n      scrollTo\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"anchorRef\",\n        ref: anchorRef,\n        class: normalizeClass(unref(cls))\n      }, [_ctx.marker ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        ref_key: \"markerRef\",\n        ref: markerRef,\n        class: normalizeClass(unref(ns).e(\"marker\")),\n        style: normalizeStyle(unref(markerStyle))\n      }, null, 6)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"list\"))\n      }, [renderSlot(_ctx.$slots, \"default\")], 2)], 2);\n    };\n  }\n});\nvar Anchor = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"anchor.vue\"]]);\nexport { Anchor as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}