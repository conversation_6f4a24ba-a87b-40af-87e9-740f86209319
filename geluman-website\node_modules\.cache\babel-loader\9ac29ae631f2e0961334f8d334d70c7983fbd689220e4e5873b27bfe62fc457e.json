{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nconst linkProps = buildProps({\n  type: {\n    type: String,\n    values: [\"primary\", \"success\", \"warning\", \"info\", \"danger\", \"default\"],\n    default: \"default\"\n  },\n  underline: {\n    type: [Boolean, String],\n    values: [true, false, \"always\", \"never\", \"hover\"],\n    default: \"hover\"\n  },\n  disabled: Boolean,\n  href: {\n    type: String,\n    default: \"\"\n  },\n  target: {\n    type: String,\n    default: \"_self\"\n  },\n  icon: {\n    type: iconPropType\n  }\n});\nconst linkEmits = {\n  click: evt => evt instanceof MouseEvent\n};\nexport { linkEmits, linkProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}