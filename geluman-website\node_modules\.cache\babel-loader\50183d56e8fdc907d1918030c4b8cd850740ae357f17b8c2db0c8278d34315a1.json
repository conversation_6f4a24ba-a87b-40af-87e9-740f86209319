{"ast": null, "code": "import { defineComponent, useAttrs as useAttrs$1, ref, computed, onBeforeUnmount, onMounted, openBlock, createBlock, unref, withCtx, createElementVNode, normalizeClass, normalizeStyle, createVNode, createElementBlock, renderSlot, Fragment, renderList, createTextVNode, toDisplayString, mergeProps, withKey<PERSON>, withModifiers, createSlots } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport { onClickOutside } from '@vueuse/core';\nimport { Loading } from '@element-plus/icons-vue';\nimport { ElInput } from '../../input/index.mjs';\nimport { ElScrollbar } from '../../scrollbar/index.mjs';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { autocompleteProps, autocompleteEmits } from './autocomplete.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useAttrs } from '../../../hooks/use-attrs/index.mjs';\nimport { useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { isArray } from '@vue/shared';\nimport { INPUT_EVENT, UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nconst COMPONENT_NAME = \"ElAutocomplete\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME,\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: autocompleteProps,\n  emits: autocompleteEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const attrs = useAttrs();\n    const rawAttrs = useAttrs$1();\n    const disabled = useFormDisabled();\n    const ns = useNamespace(\"autocomplete\");\n    const inputRef = ref();\n    const regionRef = ref();\n    const popperRef = ref();\n    const listboxRef = ref();\n    let readonly = false;\n    let ignoreFocusEvent = false;\n    const suggestions = ref([]);\n    const highlightedIndex = ref(-1);\n    const dropdownWidth = ref(\"\");\n    const activated = ref(false);\n    const suggestionDisabled = ref(false);\n    const loading = ref(false);\n    const listboxId = useId();\n    const styles = computed(() => rawAttrs.style);\n    const suggestionVisible = computed(() => {\n      const isValidData = suggestions.value.length > 0;\n      return (isValidData || loading.value) && activated.value;\n    });\n    const suggestionLoading = computed(() => !props.hideLoading && loading.value);\n    const refInput = computed(() => {\n      if (inputRef.value) {\n        return Array.from(inputRef.value.$el.querySelectorAll(\"input\"));\n      }\n      return [];\n    });\n    const onSuggestionShow = () => {\n      if (suggestionVisible.value) {\n        dropdownWidth.value = `${inputRef.value.$el.offsetWidth}px`;\n      }\n    };\n    const onHide = () => {\n      highlightedIndex.value = -1;\n    };\n    const getData = async queryString => {\n      if (suggestionDisabled.value) return;\n      const cb = suggestionList => {\n        loading.value = false;\n        if (suggestionDisabled.value) return;\n        if (isArray(suggestionList)) {\n          suggestions.value = suggestionList;\n          highlightedIndex.value = props.highlightFirstItem ? 0 : -1;\n        } else {\n          throwError(COMPONENT_NAME, \"autocomplete suggestions must be an array\");\n        }\n      };\n      loading.value = true;\n      if (isArray(props.fetchSuggestions)) {\n        cb(props.fetchSuggestions);\n      } else {\n        const result = await props.fetchSuggestions(queryString, cb);\n        if (isArray(result)) cb(result);\n      }\n    };\n    const debouncedGetData = debounce(getData, props.debounce);\n    const handleInput = value => {\n      const valuePresented = !!value;\n      emit(INPUT_EVENT, value);\n      emit(UPDATE_MODEL_EVENT, value);\n      suggestionDisabled.value = false;\n      activated.value || (activated.value = valuePresented);\n      if (!props.triggerOnFocus && !value) {\n        suggestionDisabled.value = true;\n        suggestions.value = [];\n        return;\n      }\n      debouncedGetData(value);\n    };\n    const handleMouseDown = event => {\n      var _a;\n      if (disabled.value) return;\n      if (((_a = event.target) == null ? void 0 : _a.tagName) !== \"INPUT\" || refInput.value.includes(document.activeElement)) {\n        activated.value = true;\n      }\n    };\n    const handleChange = value => {\n      emit(CHANGE_EVENT, value);\n    };\n    const handleFocus = evt => {\n      var _a;\n      if (!ignoreFocusEvent) {\n        activated.value = true;\n        emit(\"focus\", evt);\n        const queryString = (_a = props.modelValue) != null ? _a : \"\";\n        if (props.triggerOnFocus && !readonly) {\n          debouncedGetData(String(queryString));\n        }\n      } else {\n        ignoreFocusEvent = false;\n      }\n    };\n    const handleBlur = evt => {\n      setTimeout(() => {\n        var _a;\n        if ((_a = popperRef.value) == null ? void 0 : _a.isFocusInsideContent()) {\n          ignoreFocusEvent = true;\n          return;\n        }\n        activated.value && close();\n        emit(\"blur\", evt);\n      });\n    };\n    const handleClear = () => {\n      activated.value = false;\n      emit(UPDATE_MODEL_EVENT, \"\");\n      emit(\"clear\");\n    };\n    const handleKeyEnter = async () => {\n      if (suggestionVisible.value && highlightedIndex.value >= 0 && highlightedIndex.value < suggestions.value.length) {\n        handleSelect(suggestions.value[highlightedIndex.value]);\n      } else if (props.selectWhenUnmatched) {\n        emit(\"select\", {\n          value: props.modelValue\n        });\n        suggestions.value = [];\n        highlightedIndex.value = -1;\n      }\n    };\n    const handleKeyEscape = evt => {\n      if (suggestionVisible.value) {\n        evt.preventDefault();\n        evt.stopPropagation();\n        close();\n      }\n    };\n    const close = () => {\n      activated.value = false;\n    };\n    const focus = () => {\n      var _a;\n      (_a = inputRef.value) == null ? void 0 : _a.focus();\n    };\n    const blur = () => {\n      var _a;\n      (_a = inputRef.value) == null ? void 0 : _a.blur();\n    };\n    const handleSelect = async item => {\n      emit(INPUT_EVENT, item[props.valueKey]);\n      emit(UPDATE_MODEL_EVENT, item[props.valueKey]);\n      emit(\"select\", item);\n      suggestions.value = [];\n      highlightedIndex.value = -1;\n    };\n    const highlight = index => {\n      if (!suggestionVisible.value || loading.value) return;\n      if (index < 0) {\n        highlightedIndex.value = -1;\n        return;\n      }\n      if (index >= suggestions.value.length) {\n        index = suggestions.value.length - 1;\n      }\n      const suggestion = regionRef.value.querySelector(`.${ns.be(\"suggestion\", \"wrap\")}`);\n      const suggestionList = suggestion.querySelectorAll(`.${ns.be(\"suggestion\", \"list\")} li`);\n      const highlightItem = suggestionList[index];\n      const scrollTop = suggestion.scrollTop;\n      const {\n        offsetTop,\n        scrollHeight\n      } = highlightItem;\n      if (offsetTop + scrollHeight > scrollTop + suggestion.clientHeight) {\n        suggestion.scrollTop += scrollHeight;\n      }\n      if (offsetTop < scrollTop) {\n        suggestion.scrollTop -= scrollHeight;\n      }\n      highlightedIndex.value = index;\n      inputRef.value.ref.setAttribute(\"aria-activedescendant\", `${listboxId.value}-item-${highlightedIndex.value}`);\n    };\n    const stopHandle = onClickOutside(listboxRef, () => {\n      var _a;\n      if ((_a = popperRef.value) == null ? void 0 : _a.isFocusInsideContent()) return;\n      suggestionVisible.value && close();\n    });\n    onBeforeUnmount(() => {\n      stopHandle == null ? void 0 : stopHandle();\n    });\n    onMounted(() => {\n      inputRef.value.ref.setAttribute(\"role\", \"textbox\");\n      inputRef.value.ref.setAttribute(\"aria-autocomplete\", \"list\");\n      inputRef.value.ref.setAttribute(\"aria-controls\", \"id\");\n      inputRef.value.ref.setAttribute(\"aria-activedescendant\", `${listboxId.value}-item-${highlightedIndex.value}`);\n      readonly = inputRef.value.ref.hasAttribute(\"readonly\");\n    });\n    expose({\n      highlightedIndex,\n      activated,\n      loading,\n      inputRef,\n      popperRef,\n      suggestions,\n      handleSelect,\n      handleKeyEnter,\n      focus,\n      blur,\n      close,\n      highlight,\n      getData\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElTooltip), {\n        ref_key: \"popperRef\",\n        ref: popperRef,\n        visible: unref(suggestionVisible),\n        placement: _ctx.placement,\n        \"fallback-placements\": [\"bottom-start\", \"top-start\"],\n        \"popper-class\": [unref(ns).e(\"popper\"), _ctx.popperClass],\n        teleported: _ctx.teleported,\n        \"append-to\": _ctx.appendTo,\n        \"gpu-acceleration\": false,\n        pure: \"\",\n        \"manual-mode\": \"\",\n        effect: \"light\",\n        trigger: \"click\",\n        transition: `${unref(ns).namespace.value}-zoom-in-top`,\n        persistent: \"\",\n        role: \"listbox\",\n        onBeforeShow: onSuggestionShow,\n        onHide\n      }, {\n        content: withCtx(() => [createElementVNode(\"div\", {\n          ref_key: \"regionRef\",\n          ref: regionRef,\n          class: normalizeClass([unref(ns).b(\"suggestion\"), unref(ns).is(\"loading\", unref(suggestionLoading))]),\n          style: normalizeStyle({\n            [_ctx.fitInputWidth ? \"width\" : \"minWidth\"]: dropdownWidth.value,\n            outline: \"none\"\n          }),\n          role: \"region\"\n        }, [createVNode(unref(ElScrollbar), {\n          id: unref(listboxId),\n          tag: \"ul\",\n          \"wrap-class\": unref(ns).be(\"suggestion\", \"wrap\"),\n          \"view-class\": unref(ns).be(\"suggestion\", \"list\"),\n          role: \"listbox\"\n        }, {\n          default: withCtx(() => [unref(suggestionLoading) ? (openBlock(), createElementBlock(\"li\", {\n            key: 0\n          }, [renderSlot(_ctx.$slots, \"loading\", {}, () => [createVNode(unref(ElIcon), {\n            class: normalizeClass(unref(ns).is(\"loading\"))\n          }, {\n            default: withCtx(() => [createVNode(unref(Loading))]),\n            _: 1\n          }, 8, [\"class\"])])])) : (openBlock(true), createElementBlock(Fragment, {\n            key: 1\n          }, renderList(suggestions.value, (item, index) => {\n            return openBlock(), createElementBlock(\"li\", {\n              id: `${unref(listboxId)}-item-${index}`,\n              key: index,\n              class: normalizeClass({\n                highlighted: highlightedIndex.value === index\n              }),\n              role: \"option\",\n              \"aria-selected\": highlightedIndex.value === index,\n              onClick: $event => handleSelect(item)\n            }, [renderSlot(_ctx.$slots, \"default\", {\n              item\n            }, () => [createTextVNode(toDisplayString(item[_ctx.valueKey]), 1)])], 10, [\"id\", \"aria-selected\", \"onClick\"]);\n          }), 128))]),\n          _: 3\n        }, 8, [\"id\", \"wrap-class\", \"view-class\"])], 6)]),\n        default: withCtx(() => [createElementVNode(\"div\", {\n          ref_key: \"listboxRef\",\n          ref: listboxRef,\n          class: normalizeClass([unref(ns).b(), _ctx.$attrs.class]),\n          style: normalizeStyle(unref(styles)),\n          role: \"combobox\",\n          \"aria-haspopup\": \"listbox\",\n          \"aria-expanded\": unref(suggestionVisible),\n          \"aria-owns\": unref(listboxId)\n        }, [createVNode(unref(ElInput), mergeProps({\n          ref_key: \"inputRef\",\n          ref: inputRef\n        }, unref(attrs), {\n          clearable: _ctx.clearable,\n          disabled: unref(disabled),\n          name: _ctx.name,\n          \"model-value\": _ctx.modelValue,\n          \"aria-label\": _ctx.ariaLabel,\n          onInput: handleInput,\n          onChange: handleChange,\n          onFocus: handleFocus,\n          onBlur: handleBlur,\n          onClear: handleClear,\n          onKeydown: [withKeys(withModifiers($event => highlight(highlightedIndex.value - 1), [\"prevent\"]), [\"up\"]), withKeys(withModifiers($event => highlight(highlightedIndex.value + 1), [\"prevent\"]), [\"down\"]), withKeys(handleKeyEnter, [\"enter\"]), withKeys(close, [\"tab\"]), withKeys(handleKeyEscape, [\"esc\"])],\n          onMousedown: handleMouseDown\n        }), createSlots({\n          _: 2\n        }, [_ctx.$slots.prepend ? {\n          name: \"prepend\",\n          fn: withCtx(() => [renderSlot(_ctx.$slots, \"prepend\")])\n        } : void 0, _ctx.$slots.append ? {\n          name: \"append\",\n          fn: withCtx(() => [renderSlot(_ctx.$slots, \"append\")])\n        } : void 0, _ctx.$slots.prefix ? {\n          name: \"prefix\",\n          fn: withCtx(() => [renderSlot(_ctx.$slots, \"prefix\")])\n        } : void 0, _ctx.$slots.suffix ? {\n          name: \"suffix\",\n          fn: withCtx(() => [renderSlot(_ctx.$slots, \"suffix\")])\n        } : void 0]), 1040, [\"clearable\", \"disabled\", \"name\", \"model-value\", \"aria-label\", \"onKeydown\"])], 14, [\"aria-expanded\", \"aria-owns\"])]),\n        _: 3\n      }, 8, [\"visible\", \"placement\", \"popper-class\", \"teleported\", \"append-to\", \"transition\"]);\n    };\n  }\n});\nvar Autocomplete = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"autocomplete.vue\"]]);\nexport { Autocomplete as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}