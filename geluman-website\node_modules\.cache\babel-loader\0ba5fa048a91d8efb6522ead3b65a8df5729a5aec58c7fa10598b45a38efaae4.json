{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport dayjs from 'dayjs';\nimport { isEmpty } from '../../../utils/types.mjs';\nimport { isArray, isDate } from '@vue/shared';\nconst buildTimeList = (value, bound) => {\n  return [value > 0 ? value - 1 : void 0, value, value < bound ? value + 1 : void 0];\n};\nconst rangeArr = n => Array.from(Array.from({\n  length: n\n}).keys());\nconst extractDateFormat = format => {\n  return format.replace(/\\W?m{1,2}|\\W?ZZ/g, \"\").replace(/\\W?h{1,2}|\\W?s{1,3}|\\W?a/gi, \"\").trim();\n};\nconst extractTimeFormat = format => {\n  return format.replace(/\\W?D{1,2}|\\W?Do|\\W?d{1,4}|\\W?M{1,4}|\\W?Y{2,4}/g, \"\").trim();\n};\nconst dateEquals = function (a, b) {\n  const aIsDate = isDate(a);\n  const bIsDate = isDate(b);\n  if (aIsDate && bIsDate) {\n    return a.getTime() === b.getTime();\n  }\n  if (!aIsDate && !bIsDate) {\n    return a === b;\n  }\n  return false;\n};\nconst valueEquals = function (a, b) {\n  const aIsArray = isArray(a);\n  const bIsArray = isArray(b);\n  if (aIsArray && bIsArray) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    return a.every((item, index) => dateEquals(item, b[index]));\n  }\n  if (!aIsArray && !bIsArray) {\n    return dateEquals(a, b);\n  }\n  return false;\n};\nconst parseDate = function (date, format, lang) {\n  const day = isEmpty(format) || format === \"x\" ? dayjs(date).locale(lang) : dayjs(date, format).locale(lang);\n  return day.isValid() ? day : void 0;\n};\nconst formatter = function (date, format, lang) {\n  if (isEmpty(format)) return date;\n  if (format === \"x\") return +date;\n  return dayjs(date).locale(lang).format(format);\n};\nconst makeList = (total, method) => {\n  var _a;\n  const arr = [];\n  const disabledArr = method == null ? void 0 : method();\n  for (let i = 0; i < total; i++) {\n    arr.push((_a = disabledArr == null ? void 0 : disabledArr.includes(i)) != null ? _a : false);\n  }\n  return arr;\n};\nconst dayOrDaysToDate = dayOrDays => {\n  return isArray(dayOrDays) ? dayOrDays.map(d => d.toDate()) : dayOrDays.toDate();\n};\nexport { buildTimeList, dateEquals, dayOrDaysToDate, extractDateFormat, extractTimeFormat, formatter, makeList, parseDate, rangeArr, valueEquals };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}