{"ast": null, "code": "import { defineComponent, ref, computed, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, toDisplayString, createVNode } from 'vue';\nimport { ElInput } from '../../../input/index.mjs';\nimport { usePagination } from '../usePagination.mjs';\nimport { paginationJumperProps } from './jumper.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElPaginationJumper\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: paginationJumperProps,\n  setup(__props) {\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"pagination\");\n    const {\n      pageCount,\n      disabled,\n      currentPage,\n      changeEvent\n    } = usePagination();\n    const userInput = ref();\n    const innerValue = computed(() => {\n      var _a;\n      return (_a = userInput.value) != null ? _a : currentPage == null ? void 0 : currentPage.value;\n    });\n    function handleInput(val) {\n      userInput.value = val ? +val : \"\";\n    }\n    function handleChange(val) {\n      val = Math.trunc(+val);\n      changeEvent == null ? void 0 : changeEvent(val);\n      userInput.value = void 0;\n    }\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"span\", {\n        class: normalizeClass(unref(ns).e(\"jump\")),\n        disabled: unref(disabled)\n      }, [createElementVNode(\"span\", {\n        class: normalizeClass([unref(ns).e(\"goto\")])\n      }, toDisplayString(unref(t)(\"el.pagination.goto\")), 3), createVNode(unref(ElInput), {\n        size: _ctx.size,\n        class: normalizeClass([unref(ns).e(\"editor\"), unref(ns).is(\"in-pagination\")]),\n        min: 1,\n        max: unref(pageCount),\n        disabled: unref(disabled),\n        \"model-value\": unref(innerValue),\n        \"validate-event\": false,\n        \"aria-label\": unref(t)(\"el.pagination.page\"),\n        type: \"number\",\n        \"onUpdate:modelValue\": handleInput,\n        onChange: handleChange\n      }, null, 8, [\"size\", \"class\", \"max\", \"disabled\", \"model-value\", \"aria-label\"]), createElementVNode(\"span\", {\n        class: normalizeClass([unref(ns).e(\"classifier\")])\n      }, toDisplayString(unref(t)(\"el.pagination.pageClassifier\")), 3)], 10, [\"disabled\"]);\n    };\n  }\n});\nvar Jumper = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"jumper.vue\"]]);\nexport { Jumper as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}