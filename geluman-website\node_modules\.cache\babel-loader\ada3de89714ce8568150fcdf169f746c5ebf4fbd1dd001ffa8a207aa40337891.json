{"ast": null, "code": "import { defineComponent, computed, ref, reactive, markRaw, watch, nextTick, onMounted, onBeforeUnmount, toRefs, resolveComponent, openBlock, createBlock, Transition, withCtx, withDirectives, createVNode, createElementVNode, normalizeClass, normalizeStyle, withModifiers, createElementBlock, resolveDynamicComponent, createCommentVNode, toDisplayString, withKeys, renderSlot, createTextVNode, vShow } from 'vue';\nimport { ElButton } from '../../button/index.mjs';\nimport { ElInput } from '../../input/index.mjs';\nimport { ElOverlay } from '../../overlay/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { Loading } from '@element-plus/icons-vue';\nimport ElFocusTrap from '../../focus-trap/src/focus-trap.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport TrapFocus from '../../../directives/trap-focus/index.mjs';\nimport { TypeComponents, TypeComponentsMap } from '../../../utils/vue/icon.mjs';\nimport { isValidComponentSize } from '../../../utils/vue/validator.mjs';\nimport { useGlobalComponentSettings } from '../../config-provider/src/hooks/use-global-config.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { useDraggable } from '../../../hooks/use-draggable/index.mjs';\nimport { isFunction, isString } from '@vue/shared';\nimport { useLockscreen } from '../../../hooks/use-lockscreen/index.mjs';\nimport { useSameTarget } from '../../../hooks/use-same-target/index.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElMessageBox\",\n  directives: {\n    TrapFocus\n  },\n  components: {\n    ElButton,\n    ElFocusTrap,\n    ElInput,\n    ElOverlay,\n    ElIcon,\n    ...TypeComponents\n  },\n  inheritAttrs: false,\n  props: {\n    buttonSize: {\n      type: String,\n      validator: isValidComponentSize\n    },\n    modal: {\n      type: Boolean,\n      default: true\n    },\n    lockScroll: {\n      type: Boolean,\n      default: true\n    },\n    showClose: {\n      type: Boolean,\n      default: true\n    },\n    closeOnClickModal: {\n      type: Boolean,\n      default: true\n    },\n    closeOnPressEscape: {\n      type: Boolean,\n      default: true\n    },\n    closeOnHashChange: {\n      type: Boolean,\n      default: true\n    },\n    center: Boolean,\n    draggable: Boolean,\n    overflow: Boolean,\n    roundButton: {\n      default: false,\n      type: Boolean\n    },\n    container: {\n      type: String,\n      default: \"body\"\n    },\n    boxType: {\n      type: String,\n      default: \"\"\n    }\n  },\n  emits: [\"vanish\", \"action\"],\n  setup(props, {\n    emit\n  }) {\n    const {\n      locale,\n      zIndex,\n      ns,\n      size: btnSize\n    } = useGlobalComponentSettings(\"message-box\", computed(() => props.buttonSize));\n    const {\n      t\n    } = locale;\n    const {\n      nextZIndex\n    } = zIndex;\n    const visible = ref(false);\n    const state = reactive({\n      autofocus: true,\n      beforeClose: null,\n      callback: null,\n      cancelButtonText: \"\",\n      cancelButtonClass: \"\",\n      confirmButtonText: \"\",\n      confirmButtonClass: \"\",\n      customClass: \"\",\n      customStyle: {},\n      dangerouslyUseHTMLString: false,\n      distinguishCancelAndClose: false,\n      icon: \"\",\n      closeIcon: \"\",\n      inputPattern: null,\n      inputPlaceholder: \"\",\n      inputType: \"text\",\n      inputValue: \"\",\n      inputValidator: void 0,\n      inputErrorMessage: \"\",\n      message: \"\",\n      modalFade: true,\n      modalClass: \"\",\n      showCancelButton: false,\n      showConfirmButton: true,\n      type: \"\",\n      title: void 0,\n      showInput: false,\n      action: \"\",\n      confirmButtonLoading: false,\n      cancelButtonLoading: false,\n      confirmButtonLoadingIcon: markRaw(Loading),\n      cancelButtonLoadingIcon: markRaw(Loading),\n      confirmButtonDisabled: false,\n      editorErrorMessage: \"\",\n      validateError: false,\n      zIndex: nextZIndex()\n    });\n    const typeClass = computed(() => {\n      const type = state.type;\n      return {\n        [ns.bm(\"icon\", type)]: type && TypeComponentsMap[type]\n      };\n    });\n    const contentId = useId();\n    const inputId = useId();\n    const iconComponent = computed(() => {\n      const type = state.type;\n      return state.icon || type && TypeComponentsMap[type] || \"\";\n    });\n    const hasMessage = computed(() => !!state.message);\n    const rootRef = ref();\n    const headerRef = ref();\n    const focusStartRef = ref();\n    const inputRef = ref();\n    const confirmRef = ref();\n    const confirmButtonClasses = computed(() => state.confirmButtonClass);\n    watch(() => state.inputValue, async val => {\n      await nextTick();\n      if (props.boxType === \"prompt\" && val) {\n        validate();\n      }\n    }, {\n      immediate: true\n    });\n    watch(() => visible.value, val => {\n      var _a, _b;\n      if (val) {\n        if (props.boxType !== \"prompt\") {\n          if (state.autofocus) {\n            focusStartRef.value = (_b = (_a = confirmRef.value) == null ? void 0 : _a.$el) != null ? _b : rootRef.value;\n          } else {\n            focusStartRef.value = rootRef.value;\n          }\n        }\n        state.zIndex = nextZIndex();\n      }\n      if (props.boxType !== \"prompt\") return;\n      if (val) {\n        nextTick().then(() => {\n          var _a2;\n          if (inputRef.value && inputRef.value.$el) {\n            if (state.autofocus) {\n              focusStartRef.value = (_a2 = getInputElement()) != null ? _a2 : rootRef.value;\n            } else {\n              focusStartRef.value = rootRef.value;\n            }\n          }\n        });\n      } else {\n        state.editorErrorMessage = \"\";\n        state.validateError = false;\n      }\n    });\n    const draggable = computed(() => props.draggable);\n    const overflow = computed(() => props.overflow);\n    useDraggable(rootRef, headerRef, draggable, overflow);\n    onMounted(async () => {\n      await nextTick();\n      if (props.closeOnHashChange) {\n        window.addEventListener(\"hashchange\", doClose);\n      }\n    });\n    onBeforeUnmount(() => {\n      if (props.closeOnHashChange) {\n        window.removeEventListener(\"hashchange\", doClose);\n      }\n    });\n    function doClose() {\n      if (!visible.value) return;\n      visible.value = false;\n      nextTick(() => {\n        if (state.action) emit(\"action\", state.action);\n      });\n    }\n    const handleWrapperClick = () => {\n      if (props.closeOnClickModal) {\n        handleAction(state.distinguishCancelAndClose ? \"close\" : \"cancel\");\n      }\n    };\n    const overlayEvent = useSameTarget(handleWrapperClick);\n    const handleInputEnter = e => {\n      if (state.inputType !== \"textarea\") {\n        e.preventDefault();\n        return handleAction(\"confirm\");\n      }\n    };\n    const handleAction = action => {\n      var _a;\n      if (props.boxType === \"prompt\" && action === \"confirm\" && !validate()) {\n        return;\n      }\n      state.action = action;\n      if (state.beforeClose) {\n        (_a = state.beforeClose) == null ? void 0 : _a.call(state, action, state, doClose);\n      } else {\n        doClose();\n      }\n    };\n    const validate = () => {\n      if (props.boxType === \"prompt\") {\n        const inputPattern = state.inputPattern;\n        if (inputPattern && !inputPattern.test(state.inputValue || \"\")) {\n          state.editorErrorMessage = state.inputErrorMessage || t(\"el.messagebox.error\");\n          state.validateError = true;\n          return false;\n        }\n        const inputValidator = state.inputValidator;\n        if (isFunction(inputValidator)) {\n          const validateResult = inputValidator(state.inputValue);\n          if (validateResult === false) {\n            state.editorErrorMessage = state.inputErrorMessage || t(\"el.messagebox.error\");\n            state.validateError = true;\n            return false;\n          }\n          if (isString(validateResult)) {\n            state.editorErrorMessage = validateResult;\n            state.validateError = true;\n            return false;\n          }\n        }\n      }\n      state.editorErrorMessage = \"\";\n      state.validateError = false;\n      return true;\n    };\n    const getInputElement = () => {\n      var _a, _b;\n      const inputRefs = (_a = inputRef.value) == null ? void 0 : _a.$refs;\n      return (_b = inputRefs == null ? void 0 : inputRefs.input) != null ? _b : inputRefs == null ? void 0 : inputRefs.textarea;\n    };\n    const handleClose = () => {\n      handleAction(\"close\");\n    };\n    const onCloseRequested = () => {\n      if (props.closeOnPressEscape) {\n        handleClose();\n      }\n    };\n    if (props.lockScroll) {\n      useLockscreen(visible);\n    }\n    return {\n      ...toRefs(state),\n      ns,\n      overlayEvent,\n      visible,\n      hasMessage,\n      typeClass,\n      contentId,\n      inputId,\n      btnSize,\n      iconComponent,\n      confirmButtonClasses,\n      rootRef,\n      focusStartRef,\n      headerRef,\n      inputRef,\n      confirmRef,\n      doClose,\n      handleClose,\n      onCloseRequested,\n      handleWrapperClick,\n      handleInputEnter,\n      handleAction,\n      t\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = resolveComponent(\"el-icon\");\n  const _component_el_input = resolveComponent(\"el-input\");\n  const _component_el_button = resolveComponent(\"el-button\");\n  const _component_el_focus_trap = resolveComponent(\"el-focus-trap\");\n  const _component_el_overlay = resolveComponent(\"el-overlay\");\n  return openBlock(), createBlock(Transition, {\n    name: \"fade-in-linear\",\n    onAfterLeave: $event => _ctx.$emit(\"vanish\"),\n    persisted: \"\"\n  }, {\n    default: withCtx(() => [withDirectives(createVNode(_component_el_overlay, {\n      \"z-index\": _ctx.zIndex,\n      \"overlay-class\": [_ctx.ns.is(\"message-box\"), _ctx.modalClass],\n      mask: _ctx.modal\n    }, {\n      default: withCtx(() => [createElementVNode(\"div\", {\n        role: \"dialog\",\n        \"aria-label\": _ctx.title,\n        \"aria-modal\": \"true\",\n        \"aria-describedby\": !_ctx.showInput ? _ctx.contentId : void 0,\n        class: normalizeClass(`${_ctx.ns.namespace.value}-overlay-message-box`),\n        onClick: _ctx.overlayEvent.onClick,\n        onMousedown: _ctx.overlayEvent.onMousedown,\n        onMouseup: _ctx.overlayEvent.onMouseup\n      }, [createVNode(_component_el_focus_trap, {\n        loop: \"\",\n        trapped: _ctx.visible,\n        \"focus-trap-el\": _ctx.rootRef,\n        \"focus-start-el\": _ctx.focusStartRef,\n        onReleaseRequested: _ctx.onCloseRequested\n      }, {\n        default: withCtx(() => [createElementVNode(\"div\", {\n          ref: \"rootRef\",\n          class: normalizeClass([_ctx.ns.b(), _ctx.customClass, _ctx.ns.is(\"draggable\", _ctx.draggable), {\n            [_ctx.ns.m(\"center\")]: _ctx.center\n          }]),\n          style: normalizeStyle(_ctx.customStyle),\n          tabindex: \"-1\",\n          onClick: withModifiers(() => {}, [\"stop\"])\n        }, [_ctx.title !== null && _ctx.title !== void 0 ? (openBlock(), createElementBlock(\"div\", {\n          key: 0,\n          ref: \"headerRef\",\n          class: normalizeClass([_ctx.ns.e(\"header\"), {\n            \"show-close\": _ctx.showClose\n          }])\n        }, [createElementVNode(\"div\", {\n          class: normalizeClass(_ctx.ns.e(\"title\"))\n        }, [_ctx.iconComponent && _ctx.center ? (openBlock(), createBlock(_component_el_icon, {\n          key: 0,\n          class: normalizeClass([_ctx.ns.e(\"status\"), _ctx.typeClass])\n        }, {\n          default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.iconComponent)))]),\n          _: 1\n        }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true), createElementVNode(\"span\", null, toDisplayString(_ctx.title), 1)], 2), _ctx.showClose ? (openBlock(), createElementBlock(\"button\", {\n          key: 0,\n          type: \"button\",\n          class: normalizeClass(_ctx.ns.e(\"headerbtn\")),\n          \"aria-label\": _ctx.t(\"el.messagebox.close\"),\n          onClick: $event => _ctx.handleAction(_ctx.distinguishCancelAndClose ? \"close\" : \"cancel\"),\n          onKeydown: withKeys(withModifiers($event => _ctx.handleAction(_ctx.distinguishCancelAndClose ? \"close\" : \"cancel\"), [\"prevent\"]), [\"enter\"])\n        }, [createVNode(_component_el_icon, {\n          class: normalizeClass(_ctx.ns.e(\"close\"))\n        }, {\n          default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.closeIcon || \"close\")))]),\n          _: 1\n        }, 8, [\"class\"])], 42, [\"aria-label\", \"onClick\", \"onKeydown\"])) : createCommentVNode(\"v-if\", true)], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n          id: _ctx.contentId,\n          class: normalizeClass(_ctx.ns.e(\"content\"))\n        }, [createElementVNode(\"div\", {\n          class: normalizeClass(_ctx.ns.e(\"container\"))\n        }, [_ctx.iconComponent && !_ctx.center && _ctx.hasMessage ? (openBlock(), createBlock(_component_el_icon, {\n          key: 0,\n          class: normalizeClass([_ctx.ns.e(\"status\"), _ctx.typeClass])\n        }, {\n          default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.iconComponent)))]),\n          _: 1\n        }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true), _ctx.hasMessage ? (openBlock(), createElementBlock(\"div\", {\n          key: 1,\n          class: normalizeClass(_ctx.ns.e(\"message\"))\n        }, [renderSlot(_ctx.$slots, \"default\", {}, () => [!_ctx.dangerouslyUseHTMLString ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.showInput ? \"label\" : \"p\"), {\n          key: 0,\n          for: _ctx.showInput ? _ctx.inputId : void 0\n        }, {\n          default: withCtx(() => [createTextVNode(toDisplayString(!_ctx.dangerouslyUseHTMLString ? _ctx.message : \"\"), 1)]),\n          _: 1\n        }, 8, [\"for\"])) : (openBlock(), createBlock(resolveDynamicComponent(_ctx.showInput ? \"label\" : \"p\"), {\n          key: 1,\n          for: _ctx.showInput ? _ctx.inputId : void 0,\n          innerHTML: _ctx.message\n        }, null, 8, [\"for\", \"innerHTML\"]))])], 2)) : createCommentVNode(\"v-if\", true)], 2), withDirectives(createElementVNode(\"div\", {\n          class: normalizeClass(_ctx.ns.e(\"input\"))\n        }, [createVNode(_component_el_input, {\n          id: _ctx.inputId,\n          ref: \"inputRef\",\n          modelValue: _ctx.inputValue,\n          \"onUpdate:modelValue\": $event => _ctx.inputValue = $event,\n          type: _ctx.inputType,\n          placeholder: _ctx.inputPlaceholder,\n          \"aria-invalid\": _ctx.validateError,\n          class: normalizeClass({\n            invalid: _ctx.validateError\n          }),\n          onKeydown: withKeys(_ctx.handleInputEnter, [\"enter\"])\n        }, null, 8, [\"id\", \"modelValue\", \"onUpdate:modelValue\", \"type\", \"placeholder\", \"aria-invalid\", \"class\", \"onKeydown\"]), createElementVNode(\"div\", {\n          class: normalizeClass(_ctx.ns.e(\"errormsg\")),\n          style: normalizeStyle({\n            visibility: !!_ctx.editorErrorMessage ? \"visible\" : \"hidden\"\n          })\n        }, toDisplayString(_ctx.editorErrorMessage), 7)], 2), [[vShow, _ctx.showInput]])], 10, [\"id\"]), createElementVNode(\"div\", {\n          class: normalizeClass(_ctx.ns.e(\"btns\"))\n        }, [_ctx.showCancelButton ? (openBlock(), createBlock(_component_el_button, {\n          key: 0,\n          loading: _ctx.cancelButtonLoading,\n          \"loading-icon\": _ctx.cancelButtonLoadingIcon,\n          class: normalizeClass([_ctx.cancelButtonClass]),\n          round: _ctx.roundButton,\n          size: _ctx.btnSize,\n          onClick: $event => _ctx.handleAction(\"cancel\"),\n          onKeydown: withKeys(withModifiers($event => _ctx.handleAction(\"cancel\"), [\"prevent\"]), [\"enter\"])\n        }, {\n          default: withCtx(() => [createTextVNode(toDisplayString(_ctx.cancelButtonText || _ctx.t(\"el.messagebox.cancel\")), 1)]),\n          _: 1\n        }, 8, [\"loading\", \"loading-icon\", \"class\", \"round\", \"size\", \"onClick\", \"onKeydown\"])) : createCommentVNode(\"v-if\", true), withDirectives(createVNode(_component_el_button, {\n          ref: \"confirmRef\",\n          type: \"primary\",\n          loading: _ctx.confirmButtonLoading,\n          \"loading-icon\": _ctx.confirmButtonLoadingIcon,\n          class: normalizeClass([_ctx.confirmButtonClasses]),\n          round: _ctx.roundButton,\n          disabled: _ctx.confirmButtonDisabled,\n          size: _ctx.btnSize,\n          onClick: $event => _ctx.handleAction(\"confirm\"),\n          onKeydown: withKeys(withModifiers($event => _ctx.handleAction(\"confirm\"), [\"prevent\"]), [\"enter\"])\n        }, {\n          default: withCtx(() => [createTextVNode(toDisplayString(_ctx.confirmButtonText || _ctx.t(\"el.messagebox.confirm\")), 1)]),\n          _: 1\n        }, 8, [\"loading\", \"loading-icon\", \"class\", \"round\", \"disabled\", \"size\", \"onClick\", \"onKeydown\"]), [[vShow, _ctx.showConfirmButton]])], 2)], 14, [\"onClick\"])]),\n        _: 3\n      }, 8, [\"trapped\", \"focus-trap-el\", \"focus-start-el\", \"onReleaseRequested\"])], 42, [\"aria-label\", \"aria-describedby\", \"onClick\", \"onMousedown\", \"onMouseup\"])]),\n      _: 3\n    }, 8, [\"z-index\", \"overlay-class\", \"mask\"]), [[vShow, _ctx.visible]])]),\n    _: 3\n  }, 8, [\"onAfterLeave\"]);\n}\nvar MessageBoxConstructor = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"index.vue\"]]);\nexport { MessageBoxConstructor as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}