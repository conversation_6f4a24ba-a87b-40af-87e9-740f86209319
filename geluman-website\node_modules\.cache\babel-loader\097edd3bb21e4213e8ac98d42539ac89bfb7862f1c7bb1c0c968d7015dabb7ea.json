{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, ref, computed, openBlock, createBlock, unref, withCtx, normalizeClass, resolveDynamicComponent, createCommentVNode, createElementBlock, Fragment, renderList } from 'vue';\nimport dayjs from 'dayjs';\nimport customParseFormat from 'dayjs/plugin/customParseFormat.js';\nimport { ElSelect } from '../../select/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { timeSelectProps } from './time-select.mjs';\nimport { parseTime, formatTime, compareTime, nextTime } from './utils.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTimeSelect\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: timeSelectProps,\n  emits: [CHANGE_EVENT, \"blur\", \"focus\", \"clear\", UPDATE_MODEL_EVENT],\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    dayjs.extend(customParseFormat);\n    const {\n      Option: ElOption\n    } = ElSelect;\n    const nsInput = useNamespace(\"input\");\n    const select = ref();\n    const _disabled = useFormDisabled();\n    const {\n      lang\n    } = useLocale();\n    const value = computed(() => props.modelValue);\n    const start = computed(() => {\n      const time = parseTime(props.start);\n      return time ? formatTime(time) : null;\n    });\n    const end = computed(() => {\n      const time = parseTime(props.end);\n      return time ? formatTime(time) : null;\n    });\n    const step = computed(() => {\n      const time = parseTime(props.step);\n      return time ? formatTime(time) : null;\n    });\n    const minTime = computed(() => {\n      const time = parseTime(props.minTime || \"\");\n      return time ? formatTime(time) : null;\n    });\n    const maxTime = computed(() => {\n      const time = parseTime(props.maxTime || \"\");\n      return time ? formatTime(time) : null;\n    });\n    const items = computed(() => {\n      var _a;\n      const result = [];\n      const push = (formattedValue, rawValue) => {\n        result.push({\n          value: formattedValue,\n          disabled: compareTime(rawValue, minTime.value || \"-1:-1\") <= 0 || compareTime(rawValue, maxTime.value || \"100:100\") >= 0\n        });\n      };\n      if (props.start && props.end && props.step) {\n        let current = start.value;\n        let currentTime;\n        while (current && end.value && compareTime(current, end.value) <= 0) {\n          currentTime = dayjs(current, \"HH:mm\").locale(lang.value).format(props.format);\n          push(currentTime, current);\n          current = nextTime(current, step.value);\n        }\n        if (props.includeEndTime && end.value && ((_a = result[result.length - 1]) == null ? void 0 : _a.value) !== end.value) {\n          const formattedValue = dayjs(end.value, \"HH:mm\").locale(lang.value).format(props.format);\n          push(formattedValue, end.value);\n        }\n      }\n      return result;\n    });\n    const blur = () => {\n      var _a, _b;\n      (_b = (_a = select.value) == null ? void 0 : _a.blur) == null ? void 0 : _b.call(_a);\n    };\n    const focus = () => {\n      var _a, _b;\n      (_b = (_a = select.value) == null ? void 0 : _a.focus) == null ? void 0 : _b.call(_a);\n    };\n    expose({\n      blur,\n      focus\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElSelect), {\n        ref_key: \"select\",\n        ref: select,\n        \"model-value\": unref(value),\n        disabled: unref(_disabled),\n        clearable: _ctx.clearable,\n        \"clear-icon\": _ctx.clearIcon,\n        size: _ctx.size,\n        effect: _ctx.effect,\n        placeholder: _ctx.placeholder,\n        \"default-first-option\": \"\",\n        filterable: _ctx.editable,\n        \"empty-values\": _ctx.emptyValues,\n        \"value-on-clear\": _ctx.valueOnClear,\n        \"onUpdate:modelValue\": event => _ctx.$emit(unref(UPDATE_MODEL_EVENT), event),\n        onChange: event => _ctx.$emit(unref(CHANGE_EVENT), event),\n        onBlur: event => _ctx.$emit(\"blur\", event),\n        onFocus: event => _ctx.$emit(\"focus\", event),\n        onClear: () => _ctx.$emit(\"clear\")\n      }, {\n        prefix: withCtx(() => [_ctx.prefixIcon ? (openBlock(), createBlock(unref(ElIcon), {\n          key: 0,\n          class: normalizeClass(unref(nsInput).e(\"prefix-icon\"))\n        }, {\n          default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.prefixIcon)))]),\n          _: 1\n        }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true)]),\n        default: withCtx(() => [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(items), item => {\n          return openBlock(), createBlock(unref(ElOption), {\n            key: item.value,\n            label: item.value,\n            value: item.value,\n            disabled: item.disabled\n          }, null, 8, [\"label\", \"value\", \"disabled\"]);\n        }), 128))]),\n        _: 1\n      }, 8, [\"model-value\", \"disabled\", \"clearable\", \"clear-icon\", \"size\", \"effect\", \"placeholder\", \"filterable\", \"empty-values\", \"value-on-clear\", \"onUpdate:modelValue\", \"onChange\", \"onBlur\", \"onFocus\", \"onClear\"]);\n    };\n  }\n});\nvar TimeSelect = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"time-select.vue\"]]);\nexport { TimeSelect as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}