"use strict";(self["webpackChunkgeluman_website"]=self["webpackChunkgeluman_website"]||[]).push([[322],{677:function(e,t,n){n.d(t,{C8:function(){return Ve},E6:function(){return Q},FA:function(){return te},ID:function(){return S},Lf:function(){return ue},Tl:function(){return He},Up:function(){return it},XI:function(){return ne},XS:function(){return Je},Xn:function(){return B},YL:function(){return rt},a9:function(){return R},ai:function(){return ct},cX:function(){return lt},cz:function(){return ot},fS:function(){return H},fw:function(){return We},g$:function(){return _},gS:function(){return ee},i8:function(){return Ge},k3:function(){return le},kK:function(){return Y},nq:function(){return oe},q:function(){return se},qY:function(){return me},sx:function(){return st},w$:function(){return tt},w7:function(){return nt},wE:function(){return $e},wi:function(){return k}});n(1484),n(6961),n(9370),n(2807),n(8747),n(8200),n(6886),n(6831),n(4118),n(5981),n(3074),n(9724);var r=n(1953),o=n(5527);
/*!
  * core-base v9.14.2
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */
function c(){"boolean"!==typeof __INTLIFY_PROD_DEVTOOLS__&&((0,r.We)().__INTLIFY_PROD_DEVTOOLS__=!1),"boolean"!==typeof __INTLIFY_JIT_COMPILATION__&&((0,r.We)().__INTLIFY_JIT_COMPILATION__=!1),"boolean"!==typeof __INTLIFY_DROP_MESSAGE_COMPILER__&&((0,r.We)().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}const i=[];i[0]={["w"]:[0],["i"]:[3,0],["["]:[4],["o"]:[7]},i[1]={["w"]:[1],["."]:[2],["["]:[4],["o"]:[7]},i[2]={["w"]:[2],["i"]:[3,0],["0"]:[3,0]},i[3]={["i"]:[3,0],["0"]:[3,0],["w"]:[1,1],["."]:[2,1],["["]:[4,1],["o"]:[7,1]},i[4]={["'"]:[5,0],['"']:[6,0],["["]:[4,2],["]"]:[1,3],["o"]:8,["l"]:[4,0]},i[5]={["'"]:[4,0],["o"]:8,["l"]:[5,0]},i[6]={['"']:[4,0],["o"]:8,["l"]:[6,0]};const s=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function l(e){return s.test(e)}function a(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t!==n||34!==t&&39!==t?e:e.slice(1,-1)}function u(e){if(void 0===e||null===e)return"o";const t=e.charCodeAt(0);switch(t){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function f(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(l(t)?a(t):"*"+t)}function d(e){const t=[];let n,r,o,c,s,l,a,d=-1,m=0,p=0;const _=[];function E(){const t=e[d+1];if(5===m&&"'"===t||6===m&&'"'===t)return d++,o="\\"+t,_[0](),!0}_[0]=()=>{void 0===r?r=o:r+=o},_[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},_[2]=()=>{_[0](),p++},_[3]=()=>{if(p>0)p--,m=4,_[0]();else{if(p=0,void 0===r)return!1;if(r=f(r),!1===r)return!1;_[1]()}};while(null!==m)if(d++,n=e[d],"\\"!==n||!E()){if(c=u(n),a=i[m],s=a[c]||a["l"]||8,8===s)return;if(m=s[0],void 0!==s[1]&&(l=_[s[1]],l&&(o=n,!1===l())))return;if(7===m)return t}}const m=new Map;function p(e,t){return(0,r.Gv)(e)?e[t]:null}function _(e,t){if(!(0,r.Gv)(e))return null;let n=m.get(t);if(n||(n=d(t),n&&m.set(t,n)),!n)return null;const o=n.length;let c=e,i=0;while(i<o){const e=c[n[i]];if(void 0===e)return null;if((0,r.Tn)(c))return null;c=e,i++}return c}const E=e=>e,h=e=>"",g="text",y=e=>0===e.length?"":(0,r.fj)(e),L=r.v_;function T(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function N(e){const t=(0,r.Et)(e.pluralIndex)?e.pluralIndex:-1;return e.named&&((0,r.Et)(e.named.count)||(0,r.Et)(e.named.n))?(0,r.Et)(e.named.count)?e.named.count:(0,r.Et)(e.named.n)?e.named.n:t:t}function A(e,t){t.count||(t.count=e),t.n||(t.n=e)}function b(e={}){const t=e.locale,n=N(e),o=(0,r.Gv)(e.pluralRules)&&(0,r.Kg)(t)&&(0,r.Tn)(e.pluralRules[t])?e.pluralRules[t]:T,c=(0,r.Gv)(e.pluralRules)&&(0,r.Kg)(t)&&(0,r.Tn)(e.pluralRules[t])?T:void 0,i=e=>e[o(n,e.length,c)],s=e.list||[],l=e=>s[e],a=e.named||(0,r.vt)();(0,r.Et)(e.pluralIndex)&&A(n,a);const u=e=>a[e];function f(t){const n=(0,r.Tn)(e.messages)?e.messages(t):!!(0,r.Gv)(e.messages)&&e.messages[t];return n||(e.parent?e.parent.message(t):h)}const d=t=>e.modifiers?e.modifiers[t]:E,m=(0,r.Qd)(e.processor)&&(0,r.Tn)(e.processor.normalize)?e.processor.normalize:y,p=(0,r.Qd)(e.processor)&&(0,r.Tn)(e.processor.interpolate)?e.processor.interpolate:L,_=(0,r.Qd)(e.processor)&&(0,r.Kg)(e.processor.type)?e.processor.type:g,b=(e,...t)=>{const[n,o]=t;let c="text",i="";1===t.length?(0,r.Gv)(n)?(i=n.modifier||i,c=n.type||c):(0,r.Kg)(n)&&(i=n||i):2===t.length&&((0,r.Kg)(n)&&(i=n||i),(0,r.Kg)(o)&&(c=o||c));const s=f(e)(O),l="vnode"===c&&(0,r.cy)(s)&&i?s[0]:s;return i?d(i)(l,c):l},O={["list"]:l,["named"]:u,["plural"]:i,["linked"]:b,["message"]:f,["type"]:_,["interpolate"]:p,["normalize"]:m,["values"]:(0,r.kp)((0,r.vt)(),s,a)};return O}let O=null;function k(e){O=e}function w(e,t,n){O&&O.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}const I=v("function:translate");function v(e){return t=>O&&O.emit(e,t)}const C=o.sL.__EXTEND_POINT__,x=(0,r.Je)(C),S={NOT_FOUND_KEY:C,FALLBACK_TO_TRANSLATE:x(),CANNOT_FORMAT_NUMBER:x(),FALLBACK_TO_NUMBER_FORMAT:x(),CANNOT_FORMAT_DATE:x(),FALLBACK_TO_DATE_FORMAT:x(),EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:x(),__EXTEND_POINT__:x()};S.NOT_FOUND_KEY,S.FALLBACK_TO_TRANSLATE,S.CANNOT_FORMAT_NUMBER,S.FALLBACK_TO_NUMBER_FORMAT,S.CANNOT_FORMAT_DATE,S.FALLBACK_TO_DATE_FORMAT,S.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER;const P=o.Kx.__EXTEND_POINT__,D=(0,r.Je)(P),R={INVALID_ARGUMENT:P,INVALID_DATE_ARGUMENT:D(),INVALID_ISO_DATE_ARGUMENT:D(),NOT_SUPPORT_NON_STRING_MESSAGE:D(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:D(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:D(),NOT_SUPPORT_LOCALE_TYPE:D(),__EXTEND_POINT__:D()};function U(e){return(0,o.gN)(e,null,void 0)}R.INVALID_ARGUMENT,R.INVALID_DATE_ARGUMENT,R.INVALID_ISO_DATE_ARGUMENT,R.NOT_SUPPORT_NON_STRING_MESSAGE,R.NOT_SUPPORT_LOCALE_PROMISE_VALUE,R.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION,R.NOT_SUPPORT_LOCALE_TYPE;function M(e,t){return null!=t.locale?K(t.locale):K(e.locale)}let F;function K(e){if((0,r.Kg)(e))return e;if((0,r.Tn)(e)){if(e.resolvedOnce&&null!=F)return F;if("Function"===e.constructor.name){const t=e();if((0,r.yL)(t))throw U(R.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return F=t}throw U(R.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw U(R.NOT_SUPPORT_LOCALE_TYPE)}function G(e,t,n){return[...new Set([n,...(0,r.cy)(t)?t:(0,r.Gv)(t)?Object.keys(t):(0,r.Kg)(t)?[t]:[n]])]}function Y(e,t,n){const o=(0,r.Kg)(n)?n:B,c=e;c.__localeChainCache||(c.__localeChainCache=new Map);let i=c.__localeChainCache.get(o);if(!i){i=[];let e=[n];while((0,r.cy)(e))e=W(i,e,t);const s=(0,r.cy)(t)||!(0,r.Qd)(t)?t:t["default"]?t["default"]:null;e=(0,r.Kg)(s)?[s]:s,(0,r.cy)(e)&&W(i,e,!1),c.__localeChainCache.set(o,i)}return i}function W(e,t,n){let o=!0;for(let c=0;c<t.length&&(0,r.Lm)(o);c++){const i=t[c];(0,r.Kg)(i)&&(o=$(e,t[c],n))}return o}function $(e,t,n){let r;const o=t.split("-");do{const t=o.join("-");r=X(e,t,n),o.splice(-1,1)}while(o.length&&!0===r);return r}function X(e,t,n){let o=!1;if(!e.includes(t)&&(o=!0,t)){o="!"!==t[t.length-1];const c=t.replace(/!/g,"");e.push(c),((0,r.cy)(n)||(0,r.Qd)(n))&&n[c]&&(o=n[c])}return o}const V="9.14.2",H=-1,B="en-US",Q="",j=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function z(){return{upper:(e,t)=>"text"===t&&(0,r.Kg)(e)?e.toUpperCase():"vnode"===t&&(0,r.Gv)(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&(0,r.Kg)(e)?e.toLowerCase():"vnode"===t&&(0,r.Gv)(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&(0,r.Kg)(e)?j(e):"vnode"===t&&(0,r.Gv)(e)&&"__v_isVNode"in e?j(e.children):e}}let J,q,Z;function ee(e){J=e}function te(e){q=e}function ne(e){Z=e}let re=null;const oe=e=>{re=e},ce=()=>re;let ie=null;const se=e=>{ie=e},le=()=>ie;let ae=0;function ue(e={}){const t=(0,r.Tn)(e.onWarn)?e.onWarn:r.R8,n=(0,r.Kg)(e.version)?e.version:V,o=(0,r.Kg)(e.locale)||(0,r.Tn)(e.locale)?e.locale:B,c=(0,r.Tn)(o)?B:o,i=(0,r.cy)(e.fallbackLocale)||(0,r.Qd)(e.fallbackLocale)||(0,r.Kg)(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:c,s=(0,r.Qd)(e.messages)?e.messages:fe(c),l=(0,r.Qd)(e.datetimeFormats)?e.datetimeFormats:fe(c),a=(0,r.Qd)(e.numberFormats)?e.numberFormats:fe(c),u=(0,r.kp)((0,r.vt)(),e.modifiers,z()),f=e.pluralRules||(0,r.vt)(),d=(0,r.Tn)(e.missing)?e.missing:null,m=!(0,r.Lm)(e.missingWarn)&&!(0,r.gd)(e.missingWarn)||e.missingWarn,_=!(0,r.Lm)(e.fallbackWarn)&&!(0,r.gd)(e.fallbackWarn)||e.fallbackWarn,E=!!e.fallbackFormat,h=!!e.unresolving,g=(0,r.Tn)(e.postTranslation)?e.postTranslation:null,y=(0,r.Qd)(e.processor)?e.processor:null,L=!(0,r.Lm)(e.warnHtmlMessage)||e.warnHtmlMessage,T=!!e.escapeParameter,N=(0,r.Tn)(e.messageCompiler)?e.messageCompiler:J;const A=(0,r.Tn)(e.messageResolver)?e.messageResolver:q||p,b=(0,r.Tn)(e.localeFallbacker)?e.localeFallbacker:Z||G,O=(0,r.Gv)(e.fallbackContext)?e.fallbackContext:void 0,k=e,I=(0,r.Gv)(k.__datetimeFormatters)?k.__datetimeFormatters:new Map,v=(0,r.Gv)(k.__numberFormatters)?k.__numberFormatters:new Map,C=(0,r.Gv)(k.__meta)?k.__meta:{};ae++;const x={version:n,cid:ae,locale:o,fallbackLocale:i,messages:s,modifiers:u,pluralRules:f,missing:d,missingWarn:m,fallbackWarn:_,fallbackFormat:E,unresolving:h,postTranslation:g,processor:y,warnHtmlMessage:L,escapeParameter:T,messageCompiler:N,messageResolver:A,localeFallbacker:b,fallbackContext:O,onWarn:t,__meta:C};return x.datetimeFormats=l,x.numberFormats=a,x.__datetimeFormatters=I,x.__numberFormatters=v,__INTLIFY_PROD_DEVTOOLS__&&w(x,n,C),x}const fe=e=>({[e]:(0,r.vt)()});function de(e,t,n,o,c){const{missing:i,onWarn:s}=e;if(null!==i){const o=i(e,n,t,c);return(0,r.Kg)(o)?o:t}return t}function me(e,t,n){const r=e;r.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function pe(e,t){return e!==t&&e.split("-")[0]===t.split("-")[0]}function _e(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let r=n+1;r<t.length;r++)if(pe(e,t[r]))return!0;return!1}function Ee(e){const t=t=>he(t,e);return t}function he(e,t){const n=ye(t);if(null==n)throw Me(0);const r=ve(n);if(1===r){const t=n,r=Te(t);return e.plural(r.reduce(((t,n)=>[...t,Ne(e,n)]),[]))}return Ne(e,n)}const ge=["b","body"];function ye(e){return Ue(e,ge)}const Le=["c","cases"];function Te(e){return Ue(e,Le,[])}function Ne(e,t){const n=be(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=ke(t).reduce(((t,n)=>[...t,we(e,n)]),[]);return e.normalize(n)}}const Ae=["s","static"];function be(e){return Ue(e,Ae)}const Oe=["i","items"];function ke(e){return Ue(e,Oe,[])}function we(e,t){const n=ve(t);switch(n){case 3:return xe(t,n);case 9:return xe(t,n);case 4:{const o=t;if((0,r.$3)(o,"k")&&o.k)return e.interpolate(e.named(o.k));if((0,r.$3)(o,"key")&&o.key)return e.interpolate(e.named(o.key));throw Me(n)}case 5:{const o=t;if((0,r.$3)(o,"i")&&(0,r.Et)(o.i))return e.interpolate(e.list(o.i));if((0,r.$3)(o,"index")&&(0,r.Et)(o.index))return e.interpolate(e.list(o.index));throw Me(n)}case 6:{const n=t,r=Pe(n),o=Re(n);return e.linked(we(e,o),r?we(e,r):void 0,e.type)}case 7:return xe(t,n);case 8:return xe(t,n);default:throw new Error(`unhandled node on format message part: ${n}`)}}const Ie=["t","type"];function ve(e){return Ue(e,Ie)}const Ce=["v","value"];function xe(e,t){const n=Ue(e,Ce);if(n)return n;throw Me(t)}const Se=["m","modifier"];function Pe(e){return Ue(e,Se)}const De=["k","key"];function Re(e){const t=Ue(e,De);if(t)return t;throw Me(6)}function Ue(e,t,n){for(let o=0;o<t.length;o++){const n=t[o];if((0,r.$3)(e,n)&&null!=e[n])return e[n]}return n}function Me(e){return new Error(`unhandled node type: ${e}`)}const Fe=e=>e;let Ke=(0,r.vt)();function Ge(e){return(0,r.Gv)(e)&&0===ve(e)&&((0,r.$3)(e,"b")||(0,r.$3)(e,"body"))}function Ye(e,t={}){let n=!1;const r=t.onError||o.f4;return t.onError=e=>{n=!0,r(e)},{...(0,o.h)(e,t),detectError:n}}const We=(e,t)=>{if(!(0,r.Kg)(e))throw U(R.NOT_SUPPORT_NON_STRING_MESSAGE);{!(0,r.Lm)(t.warnHtmlMessage)||t.warnHtmlMessage;const n=t.onCacheKey||Fe,o=n(e),c=Ke[o];if(c)return c;const{code:i,detectError:s}=Ye(e,t),l=new Function(`return ${i}`)();return s?l:Ke[o]=l}};function $e(e,t){if(__INTLIFY_JIT_COMPILATION__&&!__INTLIFY_DROP_MESSAGE_COMPILER__&&(0,r.Kg)(e)){!(0,r.Lm)(t.warnHtmlMessage)||t.warnHtmlMessage;const n=t.onCacheKey||Fe,o=n(e),c=Ke[o];if(c)return c;const{ast:i,detectError:s}=Ye(e,{...t,location:!1,jit:!0}),l=Ee(i);return s?l:Ke[o]=l}{0;const t=e.cacheKey;if(t){const n=Ke[t];return n||(Ke[t]=Ee(e))}return Ee(e)}}const Xe=()=>"",Ve=e=>(0,r.Tn)(e);function He(e,...t){const{fallbackFormat:n,postTranslation:o,unresolving:c,messageCompiler:i,fallbackLocale:s,messages:l}=e,[a,u]=Je(...t),f=(0,r.Lm)(u.missingWarn)?u.missingWarn:e.missingWarn,d=(0,r.Lm)(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,m=(0,r.Lm)(u.escapeParameter)?u.escapeParameter:e.escapeParameter,p=!!u.resolvedMessage,_=(0,r.Kg)(u.default)||(0,r.Lm)(u.default)?(0,r.Lm)(u.default)?i?a:()=>a:u.default:n?i?a:()=>a:"",E=n||""!==_,h=M(e,u);m&&Be(u);let[g,y,L]=p?[a,h,l[h]||(0,r.vt)()]:Qe(e,a,h,s,d,f),T=g,N=a;if(p||(0,r.Kg)(T)||Ge(T)||Ve(T)||E&&(T=_,N=T),!p&&(!((0,r.Kg)(T)||Ge(T)||Ve(T))||!(0,r.Kg)(y)))return c?H:a;let A=!1;const O=()=>{A=!0},k=Ve(T)?T:je(e,a,y,T,N,O);if(A)return T;const w=Ze(e,y,L,u),v=b(w),C=ze(e,k,v),x=o?o(C,a):C;if(__INTLIFY_PROD_DEVTOOLS__){const t={timestamp:Date.now(),key:(0,r.Kg)(a)?a:Ve(T)?T.key:"",locale:y||(Ve(T)?T.locale:""),format:(0,r.Kg)(T)?T:Ve(T)?T.source:"",message:x};t.meta=(0,r.kp)({},e.__meta,ce()||{}),I(t)}return x}function Be(e){(0,r.cy)(e.list)?e.list=e.list.map((e=>(0,r.Kg)(e)?(0,r.ZD)(e):e)):(0,r.Gv)(e.named)&&Object.keys(e.named).forEach((t=>{(0,r.Kg)(e.named[t])&&(e.named[t]=(0,r.ZD)(e.named[t]))}))}function Qe(e,t,n,o,c,i){const{messages:s,onWarn:l,messageResolver:a,localeFallbacker:u}=e,f=u(e,o,n);let d,m=(0,r.vt)(),p=null,_=n,E=null;const h="translate";for(let g=0;g<f.length;g++){d=E=f[g],m=s[d]||(0,r.vt)();if(null===(p=a(m,t))&&(p=m[t]),(0,r.Kg)(p)||Ge(p)||Ve(p))break;if(!_e(d,f)){const n=de(e,t,d,i,h);n!==t&&(p=n)}_=E}return[p,d,m]}function je(e,t,n,r,o,c){const{messageCompiler:i,warnHtmlMessage:s}=e;if(Ve(r)){const e=r;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==i){const e=()=>r;return e.locale=n,e.key=t,e}const l=i(r,qe(e,n,o,r,s,c));return l.locale=n,l.key=t,l.source=r,l}function ze(e,t,n){const r=t(n);return r}function Je(...e){const[t,n,o]=e,c=(0,r.vt)();if(!(0,r.Kg)(t)&&!(0,r.Et)(t)&&!Ve(t)&&!Ge(t))throw U(R.INVALID_ARGUMENT);const i=(0,r.Et)(t)?String(t):(Ve(t),t);return(0,r.Et)(n)?c.plural=n:(0,r.Kg)(n)?c.default=n:(0,r.Qd)(n)&&!(0,r.RI)(n)?c.named=n:(0,r.cy)(n)&&(c.list=n),(0,r.Et)(o)?c.plural=o:(0,r.Kg)(o)?c.default=o:(0,r.Qd)(o)&&(0,r.kp)(c,o),[i,c]}function qe(e,t,n,o,c,i){return{locale:t,key:n,warnHtmlMessage:c,onError:e=>{throw i&&i(e),e},onCacheKey:e=>(0,r.YH)(t,n,e)}}function Ze(e,t,n,o){const{modifiers:c,pluralRules:i,messageResolver:s,fallbackLocale:l,fallbackWarn:a,missingWarn:u,fallbackContext:f}=e,d=o=>{let c=s(n,o);if(null==c&&f){const[,,e]=Qe(f,o,t,l,a,u);c=s(e,o)}if((0,r.Kg)(c)||Ge(c)){let n=!1;const r=()=>{n=!0},i=je(e,o,t,c,o,r);return n?Xe:i}return Ve(c)?c:Xe},m={locale:t,modifiers:c,pluralRules:i,messages:d};return e.processor&&(m.processor=e.processor),o.list&&(m.list=o.list),o.named&&(m.named=o.named),(0,r.Et)(o.plural)&&(m.pluralIndex=o.plural),m}const et="undefined"!==typeof Intl;et&&Intl.DateTimeFormat,et&&Intl.NumberFormat;function tt(e,...t){const{datetimeFormats:n,unresolving:o,fallbackLocale:c,onWarn:i,localeFallbacker:s}=e,{__datetimeFormatters:l}=e;const[a,u,f,d]=rt(...t),m=(0,r.Lm)(f.missingWarn)?f.missingWarn:e.missingWarn,p=((0,r.Lm)(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn,!!f.part),_=M(e,f),E=s(e,c,_);if(!(0,r.Kg)(a)||""===a)return new Intl.DateTimeFormat(_,d).format(u);let h,g={},y=null,L=_,T=null;const N="datetime format";for(let O=0;O<E.length;O++){if(h=T=E[O],g=n[h]||{},y=g[a],(0,r.Qd)(y))break;de(e,a,h,m,N),L=T}if(!(0,r.Qd)(y)||!(0,r.Kg)(h))return o?H:a;let A=`${h}__${a}`;(0,r.RI)(d)||(A=`${A}__${JSON.stringify(d)}`);let b=l.get(A);return b||(b=new Intl.DateTimeFormat(h,(0,r.kp)({},y,d)),l.set(A,b)),p?b.formatToParts(u):b.format(u)}const nt=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function rt(...e){const[t,n,o,c]=e,i=(0,r.vt)();let s,l=(0,r.vt)();if((0,r.Kg)(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw U(R.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();s=new Date(n);try{s.toISOString()}catch(a){throw U(R.INVALID_ISO_DATE_ARGUMENT)}}else if((0,r.$P)(t)){if(isNaN(t.getTime()))throw U(R.INVALID_DATE_ARGUMENT);s=t}else{if(!(0,r.Et)(t))throw U(R.INVALID_ARGUMENT);s=t}return(0,r.Kg)(n)?i.key=n:(0,r.Qd)(n)&&Object.keys(n).forEach((e=>{nt.includes(e)?l[e]=n[e]:i[e]=n[e]})),(0,r.Kg)(o)?i.locale=o:(0,r.Qd)(o)&&(l=o),(0,r.Qd)(c)&&(l=c),[i.key||"",s,i,l]}function ot(e,t,n){const r=e;for(const o in n){const e=`${t}__${o}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}}function ct(e,...t){const{numberFormats:n,unresolving:o,fallbackLocale:c,onWarn:i,localeFallbacker:s}=e,{__numberFormatters:l}=e;const[a,u,f,d]=st(...t),m=(0,r.Lm)(f.missingWarn)?f.missingWarn:e.missingWarn,p=((0,r.Lm)(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn,!!f.part),_=M(e,f),E=s(e,c,_);if(!(0,r.Kg)(a)||""===a)return new Intl.NumberFormat(_,d).format(u);let h,g={},y=null,L=_,T=null;const N="number format";for(let O=0;O<E.length;O++){if(h=T=E[O],g=n[h]||{},y=g[a],(0,r.Qd)(y))break;de(e,a,h,m,N),L=T}if(!(0,r.Qd)(y)||!(0,r.Kg)(h))return o?H:a;let A=`${h}__${a}`;(0,r.RI)(d)||(A=`${A}__${JSON.stringify(d)}`);let b=l.get(A);return b||(b=new Intl.NumberFormat(h,(0,r.kp)({},y,d)),l.set(A,b)),p?b.formatToParts(u):b.format(u)}const it=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function st(...e){const[t,n,o,c]=e,i=(0,r.vt)();let s=(0,r.vt)();if(!(0,r.Et)(t))throw U(R.INVALID_ARGUMENT);const l=t;return(0,r.Kg)(n)?i.key=n:(0,r.Qd)(n)&&Object.keys(n).forEach((e=>{it.includes(e)?s[e]=n[e]:i[e]=n[e]})),(0,r.Kg)(o)?i.locale=o:(0,r.Qd)(o)&&(s=o),(0,r.Qd)(c)&&(s=c),[i.key||"",l,i,s]}function lt(e,t,n){const r=e;for(const o in n){const e=`${t}__${o}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}}c()},1953:function(e,t,n){n.d(t,{$3:function(){return g},$P:function(){return l},A4:function(){return P},Et:function(){return s},Gv:function(){return A},Je:function(){return C},Kg:function(){return T},Lm:function(){return N},M:function(){return r},Qd:function(){return w},R8:function(){return x},RI:function(){return u},Tn:function(){return L},We:function(){return _},YH:function(){return c},ZD:function(){return E},cy:function(){return y},f4:function(){return o},fj:function(){return v},gd:function(){return a},kp:function(){return f},v_:function(){return I},vt:function(){return m},yL:function(){return b}});n(1484),n(6961),n(9370),n(2807),n(8747);
/*!
  * shared v9.14.2
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */
const r="undefined"!==typeof window;const o=(e,t=!1)=>t?Symbol.for(e):Symbol(e),c=(e,t,n)=>i({l:e,k:t,s:n}),i=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),s=e=>"number"===typeof e&&isFinite(e),l=e=>"[object Date]"===k(e),a=e=>"[object RegExp]"===k(e),u=e=>w(e)&&0===Object.keys(e).length,f=Object.assign,d=Object.create,m=(e=null)=>d(e);let p;const _=()=>p||(p="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:"undefined"!==typeof global?global:m());function E(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const h=Object.prototype.hasOwnProperty;function g(e,t){return h.call(e,t)}const y=Array.isArray,L=e=>"function"===typeof e,T=e=>"string"===typeof e,N=e=>"boolean"===typeof e,A=e=>null!==e&&"object"===typeof e,b=e=>A(e)&&L(e.then)&&L(e.catch),O=Object.prototype.toString,k=e=>O.call(e),w=e=>{if(!A(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t.constructor===Object},I=e=>null==e?"":y(e)||w(e)&&e.toString===O?JSON.stringify(e,null,2):String(e);function v(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}function C(e){let t=e;return()=>++t}function x(e,t){"undefined"!==typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const S=e=>!A(e)||y(e);function P(e,t){if(S(e)||S(t))throw new Error("Invalid value");const n=[{src:e,des:t}];while(n.length){const{src:e,des:t}=n.pop();Object.keys(e).forEach((r=>{"__proto__"!==r&&(A(e[r])&&!A(t[r])&&(t[r]=Array.isArray(e[r])?[]:m()),S(t[r])||S(e[r])?t[r]=e[r]:n.push({src:e[r],des:t[r]}))}))}}},5527:function(e,t,n){n.d(t,{Kx:function(){return p},f4:function(){return h},gN:function(){return E},h:function(){return j},sL:function(){return f}});n(1484),n(6961),n(9370),n(2807),n(8747),n(8200),n(6886),n(6831),n(4118),n(5981),n(3074),n(9724);function r(e,t,n){return{line:e,column:t,offset:n}}function o(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const c=/\{([0-9a-zA-Z]+)\}/g;function i(e,...t){return 1===t.length&&a(t[0])&&(t=t[0]),t&&t.hasOwnProperty||(t={}),e.replace(c,((e,n)=>t.hasOwnProperty(n)?t[n]:""))}const s=Object.assign,l=e=>"string"===typeof e,a=e=>null!==e&&"object"===typeof e;function u(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}const f={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2},d={[f.USE_MODULO_SYNTAX]:"Use modulo before '{{0}}'."};function m(e,t,...n){const r=i(d[e]||"",...n||[]),o={message:String(r),code:e};return t&&(o.location=t),o}const p={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17},_={[p.EXPECTED_TOKEN]:"Expected token: '{0}'",[p.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[p.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[p.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[p.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[p.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[p.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[p.EMPTY_PLACEHOLDER]:"Empty placeholder",[p.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[p.INVALID_LINKED_FORMAT]:"Invalid linked format",[p.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[p.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[p.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[p.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'",[p.UNHANDLED_CODEGEN_NODE_TYPE]:"unhandled codegen node type: '{0}'",[p.UNHANDLED_MINIFIER_NODE_TYPE]:"unhandled mimifier node type: '{0}'"};function E(e,t,n={}){const{domain:r,messages:o,args:c}=n,s=i((o||_)[e]||"",...c||[]),l=new SyntaxError(String(s));return l.code=e,t&&(l.location=t),l.domain=r,l}function h(e){throw e}const g=" ",y="\r",L="\n",T=String.fromCharCode(8232),N=String.fromCharCode(8233);function A(e){const t=e;let n=0,r=1,o=1,c=0;const i=e=>t[e]===y&&t[e+1]===L,s=e=>t[e]===L,l=e=>t[e]===N,a=e=>t[e]===T,u=e=>i(e)||s(e)||l(e)||a(e),f=()=>n,d=()=>r,m=()=>o,p=()=>c,_=e=>i(e)||l(e)||a(e)?L:t[e],E=()=>_(n),h=()=>_(n+c);function g(){return c=0,u(n)&&(r++,o=0),i(n)&&n++,n++,o++,t[n]}function A(){return i(n+c)&&c++,c++,t[n+c]}function b(){n=0,r=1,o=1,c=0}function O(e=0){c=e}function k(){const e=n+c;while(e!==n)g();c=0}return{index:f,line:d,column:m,peekOffset:p,charAt:_,currentChar:E,currentPeek:h,next:g,peek:A,reset:b,resetPeek:O,skipToPeek:k}}const b=void 0,O="'",k="tokenizer";function w(e,t={}){const n=!1!==t.location,c=A(e),i=()=>c.index(),s=()=>r(c.line(),c.column(),c.index()),l=s(),a=i(),u={currentType:14,offset:a,startLoc:l,endLoc:l,lastType:14,lastOffset:a,lastStartLoc:l,lastEndLoc:l,braceNest:0,inLinked:!1,text:""},f=()=>u,{onError:d}=t;function m(e,t,r,...c){const i=f();if(t.column+=r,t.offset+=r,d){const r=n?o(i.startLoc,t):null,s=E(e,r,{domain:k,args:c});d(s)}}function _(e,t,r){e.endLoc=s(),e.currentType=t;const c={type:t};return n&&(c.loc=o(e.startLoc,e.endLoc)),null!=r&&(c.value=r),c}const h=e=>_(e,14);function y(e,t){return e.currentChar()===t?(e.next(),t):(m(p.EXPECTED_TOKEN,s(),0,t),"")}function T(e){let t="";while(e.currentPeek()===g||e.currentPeek()===L)t+=e.currentPeek(),e.peek();return t}function N(e){const t=T(e);return e.skipToPeek(),t}function w(e){if(e===b)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function I(e){if(e===b)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}function v(e,t){const{currentType:n}=t;if(2!==n)return!1;T(e);const r=w(e.currentPeek());return e.resetPeek(),r}function C(e,t){const{currentType:n}=t;if(2!==n)return!1;T(e);const r="-"===e.currentPeek()?e.peek():e.currentPeek(),o=I(r);return e.resetPeek(),o}function x(e,t){const{currentType:n}=t;if(2!==n)return!1;T(e);const r=e.currentPeek()===O;return e.resetPeek(),r}function S(e,t){const{currentType:n}=t;if(8!==n)return!1;T(e);const r="."===e.currentPeek();return e.resetPeek(),r}function P(e,t){const{currentType:n}=t;if(9!==n)return!1;T(e);const r=w(e.currentPeek());return e.resetPeek(),r}function D(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;T(e);const r=":"===e.currentPeek();return e.resetPeek(),r}function R(e,t){const{currentType:n}=t;if(10!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?w(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||t===g||!t)&&(t===L?(e.peek(),r()):F(e,!1))},o=r();return e.resetPeek(),o}function U(e){T(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function M(e){const t=T(e),n="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:n,hasSpace:t.length>0}}function F(e,t=!0){const n=(t=!1,r="",o=!1)=>{const c=e.currentPeek();return"{"===c?"%"!==r&&t:"@"!==c&&c?"%"===c?(e.peek(),n(t,"%",!0)):"|"===c?!("%"!==r&&!o)||!(r===g||r===L):c===g?(e.peek(),n(!0,g,o)):c!==L||(e.peek(),n(!0,L,o)):"%"===r||t},r=n();return t&&e.resetPeek(),r}function K(e,t){const n=e.currentChar();return n===b?b:t(n)?(e.next(),n):null}function G(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function Y(e){return K(e,G)}function W(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function $(e){return K(e,W)}function X(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function V(e){return K(e,X)}function H(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function B(e){return K(e,H)}function Q(e){let t="",n="";while(t=V(e))n+=t;return n}function j(e){N(e);const t=e.currentChar();return"%"!==t&&m(p.EXPECTED_TOKEN,s(),0,t),e.next(),"%"}function z(e){let t="";while(1){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if("%"===n){if(!F(e))break;t+=n,e.next()}else if(n===g||n===L)if(F(e))t+=n,e.next();else{if(U(e))break;t+=n,e.next()}else t+=n,e.next()}return t}function J(e){N(e);let t="",n="";while(t=$(e))n+=t;return e.currentChar()===b&&m(p.UNTERMINATED_CLOSING_BRACE,s(),0),n}function q(e){N(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${Q(e)}`):t+=Q(e),e.currentChar()===b&&m(p.UNTERMINATED_CLOSING_BRACE,s(),0),t}function Z(e){return e!==O&&e!==L}function ee(e){N(e),y(e,"'");let t="",n="";while(t=K(e,Z))n+="\\"===t?te(e):t;const r=e.currentChar();return r===L||r===b?(m(p.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,s(),0),r===L&&(e.next(),y(e,"'")),n):(y(e,"'"),n)}function te(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return ne(e,t,4);case"U":return ne(e,t,6);default:return m(p.UNKNOWN_ESCAPE_SEQUENCE,s(),0,t),""}}function ne(e,t,n){y(e,t);let r="";for(let o=0;o<n;o++){const n=B(e);if(!n){m(p.INVALID_UNICODE_ESCAPE_SEQUENCE,s(),0,`\\${t}${r}${e.currentChar()}`);break}r+=n}return`\\${t}${r}`}function re(e){return"{"!==e&&"}"!==e&&e!==g&&e!==L}function oe(e){N(e);let t="",n="";while(t=K(e,re))n+=t;return n}function ce(e){let t="",n="";while(t=Y(e))n+=t;return n}function ie(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"%"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===g?n:(n+=r,e.next(),t(n)):n};return t("")}function se(e){N(e);const t=y(e,"|");return N(e),t}function le(e,t){let n=null;const r=e.currentChar();switch(r){case"{":return t.braceNest>=1&&m(p.NOT_ALLOW_NEST_PLACEHOLDER,s(),0),e.next(),n=_(t,2,"{"),N(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&m(p.EMPTY_PLACEHOLDER,s(),0),e.next(),n=_(t,3,"}"),t.braceNest--,t.braceNest>0&&N(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&m(p.UNTERMINATED_CLOSING_BRACE,s(),0),n=ae(e,t)||h(t),t.braceNest=0,n;default:{let r=!0,o=!0,c=!0;if(U(e))return t.braceNest>0&&m(p.UNTERMINATED_CLOSING_BRACE,s(),0),n=_(t,1,se(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return m(p.UNTERMINATED_CLOSING_BRACE,s(),0),t.braceNest=0,ue(e,t);if(r=v(e,t))return n=_(t,5,J(e)),N(e),n;if(o=C(e,t))return n=_(t,6,q(e)),N(e),n;if(c=x(e,t))return n=_(t,7,ee(e)),N(e),n;if(!r&&!o&&!c)return n=_(t,13,oe(e)),m(p.INVALID_TOKEN_IN_PLACEHOLDER,s(),0,n.value),N(e),n;break}}return n}function ae(e,t){const{currentType:n}=t;let r=null;const o=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||o!==L&&o!==g||m(p.INVALID_LINKED_FORMAT,s(),0),o){case"@":return e.next(),r=_(t,8,"@"),t.inLinked=!0,r;case".":return N(e),e.next(),_(t,9,".");case":":return N(e),e.next(),_(t,10,":");default:return U(e)?(r=_(t,1,se(e)),t.braceNest=0,t.inLinked=!1,r):S(e,t)||D(e,t)?(N(e),ae(e,t)):P(e,t)?(N(e),_(t,12,ce(e))):R(e,t)?(N(e),"{"===o?le(e,t)||r:_(t,11,ie(e))):(8===n&&m(p.INVALID_LINKED_FORMAT,s(),0),t.braceNest=0,t.inLinked=!1,ue(e,t))}}function ue(e,t){let n={type:14};if(t.braceNest>0)return le(e,t)||h(t);if(t.inLinked)return ae(e,t)||h(t);const r=e.currentChar();switch(r){case"{":return le(e,t)||h(t);case"}":return m(p.UNBALANCED_CLOSING_BRACE,s(),0),e.next(),_(t,3,"}");case"@":return ae(e,t)||h(t);default:{if(U(e))return n=_(t,1,se(e)),t.braceNest=0,t.inLinked=!1,n;const{isModulo:r,hasSpace:o}=M(e);if(r)return o?_(t,0,z(e)):_(t,4,j(e));if(F(e))return _(t,0,z(e));break}}return n}function fe(){const{currentType:e,offset:t,startLoc:n,endLoc:r}=u;return u.lastType=e,u.lastOffset=t,u.lastStartLoc=n,u.lastEndLoc=r,u.offset=i(),u.startLoc=s(),c.currentChar()===b?_(u,14):ue(c,u)}return{nextToken:fe,currentOffset:i,currentPosition:s,context:f}}const I="parser",v=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function C(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function x(e={}){const t=!1!==e.location,{onError:n,onWarn:r}=e;function c(e,r,c,i,...s){const l=e.currentPosition();if(l.offset+=i,l.column+=i,n){const e=t?o(c,l):null,i=E(r,e,{domain:I,args:s});n(i)}}function i(e,n,c,i,...s){const l=e.currentPosition();if(l.offset+=i,l.column+=i,r){const e=t?o(c,l):null;r(m(n,e,s))}}function l(e,n,r){const o={type:e};return t&&(o.start=n,o.end=n,o.loc={start:r,end:r}),o}function a(e,n,r,o){o&&(e.type=o),t&&(e.end=n,e.loc&&(e.loc.end=r))}function u(e,t){const n=e.context(),r=l(3,n.offset,n.startLoc);return r.value=t,a(r,e.currentOffset(),e.currentPosition()),r}function d(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:o}=n,c=l(5,r,o);return c.index=parseInt(t,10),e.nextToken(),a(c,e.currentOffset(),e.currentPosition()),c}function _(e,t,n){const r=e.context(),{lastOffset:o,lastStartLoc:c}=r,i=l(4,o,c);return i.key=t,!0===n&&(i.modulo=!0),e.nextToken(),a(i,e.currentOffset(),e.currentPosition()),i}function h(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:o}=n,c=l(9,r,o);return c.value=t.replace(v,C),e.nextToken(),a(c,e.currentOffset(),e.currentPosition()),c}function g(e){const t=e.nextToken(),n=e.context(),{lastOffset:r,lastStartLoc:o}=n,i=l(8,r,o);return 12!==t.type?(c(e,p.UNEXPECTED_EMPTY_LINKED_MODIFIER,n.lastStartLoc,0),i.value="",a(i,r,o),{nextConsumeToken:t,node:i}):(null==t.value&&c(e,p.UNEXPECTED_LEXICAL_ANALYSIS,n.lastStartLoc,0,S(t)),i.value=t.value||"",a(i,e.currentOffset(),e.currentPosition()),{node:i})}function y(e,t){const n=e.context(),r=l(7,n.offset,n.startLoc);return r.value=t,a(r,e.currentOffset(),e.currentPosition()),r}function L(e){const t=e.context(),n=l(6,t.offset,t.startLoc);let r=e.nextToken();if(9===r.type){const t=g(e);n.modifier=t.node,r=t.nextConsumeToken||e.nextToken()}switch(10!==r.type&&c(e,p.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,S(r)),r=e.nextToken(),2===r.type&&(r=e.nextToken()),r.type){case 11:null==r.value&&c(e,p.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,S(r)),n.key=y(e,r.value||"");break;case 5:null==r.value&&c(e,p.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,S(r)),n.key=_(e,r.value||"");break;case 6:null==r.value&&c(e,p.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,S(r)),n.key=d(e,r.value||"");break;case 7:null==r.value&&c(e,p.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,S(r)),n.key=h(e,r.value||"");break;default:{c(e,p.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc,0);const o=e.context(),i=l(7,o.offset,o.startLoc);return i.value="",a(i,o.offset,o.startLoc),n.key=i,a(n,o.offset,o.startLoc),{nextConsumeToken:r,node:n}}}return a(n,e.currentOffset(),e.currentPosition()),{node:n}}function T(e){const t=e.context(),n=1===t.currentType?e.currentOffset():t.offset,r=1===t.currentType?t.endLoc:t.startLoc,o=l(2,n,r);o.items=[];let s=null,m=null;do{const n=s||e.nextToken();switch(s=null,n.type){case 0:null==n.value&&c(e,p.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,S(n)),o.items.push(u(e,n.value||""));break;case 6:null==n.value&&c(e,p.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,S(n)),o.items.push(d(e,n.value||""));break;case 4:m=!0;break;case 5:null==n.value&&c(e,p.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,S(n)),o.items.push(_(e,n.value||"",!!m)),m&&(i(e,f.USE_MODULO_SYNTAX,t.lastStartLoc,0,S(n)),m=null);break;case 7:null==n.value&&c(e,p.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,S(n)),o.items.push(h(e,n.value||""));break;case 8:{const t=L(e);o.items.push(t.node),s=t.nextConsumeToken||null;break}}}while(14!==t.currentType&&1!==t.currentType);const E=1===t.currentType?t.lastOffset:e.currentOffset(),g=1===t.currentType?t.lastEndLoc:e.currentPosition();return a(o,E,g),o}function N(e,t,n,r){const o=e.context();let i=0===r.items.length;const s=l(1,t,n);s.cases=[],s.cases.push(r);do{const t=T(e);i||(i=0===t.items.length),s.cases.push(t)}while(14!==o.currentType);return i&&c(e,p.MUST_HAVE_MESSAGES_IN_PLURAL,n,0),a(s,e.currentOffset(),e.currentPosition()),s}function A(e){const t=e.context(),{offset:n,startLoc:r}=t,o=T(e);return 14===t.currentType?o:N(e,n,r,o)}function b(n){const r=w(n,s({},e)),o=r.context(),i=l(0,o.offset,o.startLoc);return t&&i.loc&&(i.loc.source=n),i.body=A(r),e.onCacheKey&&(i.cacheKey=e.onCacheKey(n)),14!==o.currentType&&c(r,p.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,n[o.offset]||""),a(i,r.currentOffset(),r.currentPosition()),i}return{parse:b}}function S(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function P(e,t={}){const n={ast:e,helpers:new Set},r=()=>n,o=e=>(n.helpers.add(e),e);return{context:r,helper:o}}function D(e,t){for(let n=0;n<e.length;n++)R(e[n],t)}function R(e,t){switch(e.type){case 1:D(e.cases,t),t.helper("plural");break;case 2:D(e.items,t);break;case 6:{const n=e;R(n.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function U(e,t={}){const n=P(e);n.helper("normalize"),e.body&&R(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function M(e){const t=e.body;return 2===t.type?F(t):t.cases.forEach((e=>F(e))),e}function F(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=u(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}const K="minifier";function G(e){switch(e.t=e.type,e.type){case 0:{const t=e;G(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)G(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)G(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;G(t.key),t.k=t.key,delete t.key,t.modifier&&(G(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}default:throw E(p.UNHANDLED_MINIFIER_NODE_TYPE,null,{domain:K,args:[e.type]})}delete e.type}const Y="parser";function W(e,t){const{sourceMap:n,filename:r,breakLineCode:o,needIndent:c}=t,i=!1!==t.location,s={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:o,needIndent:c,indentLevel:0};i&&e.loc&&(s.source=e.loc.source);const l=()=>s;function a(e,t){s.code+=e}function u(e,t=!0){const n=t?o:"";a(c?n+"  ".repeat(e):n)}function f(e=!0){const t=++s.indentLevel;e&&u(t)}function d(e=!0){const t=--s.indentLevel;e&&u(t)}function m(){u(s.indentLevel)}const p=e=>`_${e}`,_=()=>s.needIndent;return{context:l,push:a,indent:f,deindent:d,newline:m,helper:p,needIndent:_}}function $(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),B(e,t.key),t.modifier?(e.push(", "),B(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function X(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const o=t.items.length;for(let c=0;c<o;c++){if(B(e,t.items[c]),c===o-1)break;e.push(", ")}e.deindent(r()),e.push("])")}function V(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const o=t.cases.length;for(let n=0;n<o;n++){if(B(e,t.cases[n]),n===o-1)break;e.push(", ")}e.deindent(r()),e.push("])")}}function H(e,t){t.body?B(e,t.body):e.push("null")}function B(e,t){const{helper:n}=e;switch(t.type){case 0:H(e,t);break;case 1:V(e,t);break;case 2:X(e,t);break;case 6:$(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break;default:throw E(p.UNHANDLED_CODEGEN_NODE_TYPE,null,{domain:Y,args:[t.type]})}}const Q=(e,t={})=>{const n=l(t.mode)?t.mode:"normal",r=l(t.filename)?t.filename:"message.intl",o=!!t.sourceMap,c=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",i=t.needIndent?t.needIndent:"arrow"!==n,s=e.helpers||[],a=W(e,{mode:n,filename:r,sourceMap:o,breakLineCode:c,needIndent:i});a.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),a.indent(i),s.length>0&&(a.push(`const { ${u(s.map((e=>`${e}: _${e}`)),", ")} } = ctx`),a.newline()),a.push("return "),B(a,e),a.deindent(i),a.push("}"),delete e.helpers;const{code:f,map:d}=a.context();return{ast:e,code:f,map:d?d.toJSON():void 0}};function j(e,t={}){const n=s({},t),r=!!n.jit,o=!!n.minify,c=null==n.optimize||n.optimize,i=x(n),l=i.parse(e);return r?(c&&M(l),o&&G(l),{ast:l,code:""}):(U(l,n),Q(l,n))}},8969:function(e,t,n){n.d(t,{UE:function(){return Re},ll:function(){return Ce},rD:function(){return Ue},__:function(){return xe},UU:function(){return De},cY:function(){return Se},BN:function(){return Pe}});n(6961),n(4615),n(9370),n(2807);const r=Math.min,o=Math.max,c=Math.round,i=Math.floor,s=e=>({x:e,y:e}),l={left:"right",right:"left",bottom:"top",top:"bottom"},a={start:"end",end:"start"};function u(e,t,n){return o(e,r(t,n))}function f(e,t){return"function"===typeof e?e(t):e}function d(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function p(e){return"x"===e?"y":"x"}function _(e){return"y"===e?"height":"width"}function E(e){return["top","bottom"].includes(d(e))?"y":"x"}function h(e){return p(E(e))}function g(e,t,n){void 0===n&&(n=!1);const r=m(e),o=h(e),c=_(o);let i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[c]>t.floating[c]&&(i=A(i)),[i,A(i)]}function y(e){const t=A(e);return[L(e),t,L(t)]}function L(e){return e.replace(/start|end/g,(e=>a[e]))}function T(e,t,n){const r=["left","right"],o=["right","left"],c=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?c:i;default:return[]}}function N(e,t,n,r){const o=m(e);let c=T(d(e),"start"===n,r);return o&&(c=c.map((e=>e+"-"+o)),t&&(c=c.concat(c.map(L)))),c}function A(e){return e.replace(/left|right|bottom|top/g,(e=>l[e]))}function b(e){return{top:0,right:0,bottom:0,left:0,...e}}function O(e){return"number"!==typeof e?b(e):{top:e,right:e,bottom:e,left:e}}function k(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}n(1484),n(4126),n(7354),n(8747),n(4929);function w(e,t,n){let{reference:r,floating:o}=e;const c=E(t),i=h(t),s=_(i),l=d(t),a="y"===c,u=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,p=r[s]/2-o[s]/2;let g;switch(l){case"top":g={x:u,y:r.y-o.height};break;case"bottom":g={x:u,y:r.y+r.height};break;case"right":g={x:r.x+r.width,y:f};break;case"left":g={x:r.x-o.width,y:f};break;default:g={x:r.x,y:r.y}}switch(m(t)){case"start":g[i]-=p*(n&&a?-1:1);break;case"end":g[i]+=p*(n&&a?-1:1);break}return g}const I=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:c=[],platform:i}=n,s=c.filter(Boolean),l=await(null==i.isRTL?void 0:i.isRTL(t));let a=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:f}=w(a,r,l),d=r,m={},p=0;for(let _=0;_<s.length;_++){const{name:n,fn:c}=s[_],{x:E,y:h,data:g,reset:y}=await c({x:u,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:m,rects:a,platform:i,elements:{reference:e,floating:t}});u=null!=E?E:u,f=null!=h?h:f,m={...m,[n]:{...m[n],...g}},y&&p<=50&&(p++,"object"===typeof y&&(y.placement&&(d=y.placement),y.rects&&(a=!0===y.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):y.rects),({x:u,y:f}=w(a,d,l))),_=-1)}return{x:u,y:f,placement:d,strategy:o,middlewareData:m}};async function v(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:c,rects:i,elements:s,strategy:l}=e,{boundary:a="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:m=!1,padding:p=0}=f(t,e),_=O(p),E="floating"===d?"reference":"floating",h=s[m?E:d],g=k(await c.getClippingRect({element:null==(n=await(null==c.isElement?void 0:c.isElement(h)))||n?h:h.contextElement||await(null==c.getDocumentElement?void 0:c.getDocumentElement(s.floating)),boundary:a,rootBoundary:u,strategy:l})),y="floating"===d?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,L=await(null==c.getOffsetParent?void 0:c.getOffsetParent(s.floating)),T=await(null==c.isElement?void 0:c.isElement(L))&&await(null==c.getScale?void 0:c.getScale(L))||{x:1,y:1},N=k(c.convertOffsetParentRelativeRectToViewportRelativeRect?await c.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:y,offsetParent:L,strategy:l}):y);return{top:(g.top-N.top+_.top)/T.y,bottom:(N.bottom-g.bottom+_.bottom)/T.y,left:(g.left-N.left+_.left)/T.x,right:(N.right-g.right+_.right)/T.x}}const C=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:c,rects:i,platform:s,elements:l,middlewareData:a}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};const E=O(p),g={x:n,y:o},y=h(c),L=_(y),T=await s.getDimensions(d),N="y"===y,A=N?"top":"left",b=N?"bottom":"right",k=N?"clientHeight":"clientWidth",w=i.reference[L]+i.reference[y]-g[y]-i.floating[L],I=g[y]-i.reference[y],v=await(null==s.getOffsetParent?void 0:s.getOffsetParent(d));let C=v?v[k]:0;C&&await(null==s.isElement?void 0:s.isElement(v))||(C=l.floating[k]||i.floating[L]);const x=w/2-I/2,S=C/2-T[L]/2-1,P=r(E[A],S),D=r(E[b],S),R=P,U=C-T[L]-D,M=C/2-T[L]/2+x,F=u(R,M,U),K=!a.arrow&&null!=m(c)&&M!==F&&i.reference[L]/2-(M<R?P:D)-T[L]/2<0,G=K?M<R?M-R:M-U:0;return{[y]:g[y]+G,data:{[y]:F,centerOffset:M-F-G,...K&&{alignmentOffset:G}},reset:K}}});const x=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:c,rects:i,initialPlacement:s,platform:l,elements:a}=t,{mainAxis:u=!0,crossAxis:m=!0,fallbackPlacements:p,fallbackStrategy:_="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:L=!0,...T}=f(e,t);if(null!=(n=c.arrow)&&n.alignmentOffset)return{};const b=d(o),O=E(s),k=d(s)===s,w=await(null==l.isRTL?void 0:l.isRTL(a.floating)),I=p||(k||!L?[A(s)]:y(s)),C="none"!==h;!p&&C&&I.push(...N(s,L,h,w));const x=[s,...I],S=await v(t,T),P=[];let D=(null==(r=c.flip)?void 0:r.overflows)||[];if(u&&P.push(S[b]),m){const e=g(o,i,w);P.push(S[e[0]],S[e[1]])}if(D=[...D,{placement:o,overflows:P}],!P.every((e=>e<=0))){var R,U;const e=((null==(R=c.flip)?void 0:R.index)||0)+1,t=x[e];if(t)return{data:{index:e,overflows:D},reset:{placement:t}};let n=null==(U=D.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:U.placement;if(!n)switch(_){case"bestFit":{var M;const e=null==(M=D.filter((e=>{if(C){const t=E(e.placement);return t===O||"y"===t}return!0})).map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:M[0];e&&(n=e);break}case"initialPlacement":n=s;break}if(o!==n)return{reset:{placement:n}}}return{}}}};async function S(e,t){const{placement:n,platform:r,elements:o}=e,c=await(null==r.isRTL?void 0:r.isRTL(o.floating)),i=d(n),s=m(n),l="y"===E(n),a=["left","top"].includes(i)?-1:1,u=c&&l?-1:1,p=f(t,e);let{mainAxis:_,crossAxis:h,alignmentAxis:g}="number"===typeof p?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return s&&"number"===typeof g&&(h="end"===s?-1*g:g),l?{x:h*u,y:_*a}:{x:_*a,y:h*u}}const P=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:c,placement:i,middlewareData:s}=t,l=await S(t,e);return i===(null==(n=s.offset)?void 0:n.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:o+l.x,y:c+l.y,data:{...l,placement:i}}}}},D=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:c=!0,crossAxis:i=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...l}=f(e,t),a={x:n,y:r},m=await v(t,l),_=E(d(o)),h=p(_);let g=a[h],y=a[_];if(c){const e="y"===h?"top":"left",t="y"===h?"bottom":"right",n=g+m[e],r=g-m[t];g=u(n,g,r)}if(i){const e="y"===_?"top":"left",t="y"===_?"bottom":"right",n=y+m[e],r=y-m[t];y=u(n,y,r)}const L=s.fn({...t,[h]:g,[_]:y});return{...L,data:{x:L.x-n,y:L.y-r,enabled:{[h]:c,[_]:i}}}}}};function R(){return"undefined"!==typeof window}function U(e){return K(e)?(e.nodeName||"").toLowerCase():"#document"}function M(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function F(e){var t;return null==(t=(K(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function K(e){return!!R()&&(e instanceof Node||e instanceof M(e).Node)}function G(e){return!!R()&&(e instanceof Element||e instanceof M(e).Element)}function Y(e){return!!R()&&(e instanceof HTMLElement||e instanceof M(e).HTMLElement)}function W(e){return!(!R()||"undefined"===typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof M(e).ShadowRoot)}function $(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=z(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function X(e){return["table","td","th"].includes(U(e))}function V(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(n){return!1}}))}function H(e){const t=Q(),n=G(e)?z(e):e;return["transform","translate","scale","rotate","perspective"].some((e=>!!n[e]&&"none"!==n[e]))||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function B(e){let t=q(e);while(Y(t)&&!j(t)){if(H(t))return t;if(V(t))return null;t=q(t)}return null}function Q(){return!("undefined"===typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function j(e){return["html","body","#document"].includes(U(e))}function z(e){return M(e).getComputedStyle(e)}function J(e){return G(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function q(e){if("html"===U(e))return e;const t=e.assignedSlot||e.parentNode||W(e)&&e.host||F(e);return W(t)?t.host:t}function Z(e){const t=q(e);return j(t)?e.ownerDocument?e.ownerDocument.body:e.body:Y(t)&&$(t)?t:Z(t)}function ee(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=Z(e),c=o===(null==(r=e.ownerDocument)?void 0:r.body),i=M(o);if(c){const e=te(i);return t.concat(i,i.visualViewport||[],$(o)?o:[],e&&n?ee(e):[])}return t.concat(o,ee(o,[],n))}function te(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ne(e){const t=z(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Y(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,l=c(n)!==i||c(r)!==s;return l&&(n=i,r=s),{width:n,height:r,$:l}}function re(e){return G(e)?e:e.contextElement}function oe(e){const t=re(e);if(!Y(t))return s(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ne(t);let l=(i?c(n.width):n.width)/r,a=(i?c(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}const ce=s(0);function ie(e){const t=M(e);return Q()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ce}function se(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==M(e))&&t}function le(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),c=re(e);let i=s(1);t&&(r?G(r)&&(i=oe(r)):i=oe(e));const l=se(c,n,r)?ie(c):s(0);let a=(o.left+l.x)/i.x,u=(o.top+l.y)/i.y,f=o.width/i.x,d=o.height/i.y;if(c){const e=M(c),t=r&&G(r)?M(r):r;let n=e,o=te(n);while(o&&r&&t!==n){const e=oe(o),t=o.getBoundingClientRect(),r=z(o),c=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;a*=e.x,u*=e.y,f*=e.x,d*=e.y,a+=c,u+=i,n=M(o),o=te(n)}}return k({width:f,height:d,x:a,y:u})}function ae(e,t){const n=J(e).scrollLeft;return t?t.left+n:le(F(e)).left+n}function ue(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:ae(e,r)),c=r.top+t.scrollTop;return{x:o,y:c}}function fe(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const c="fixed"===o,i=F(r),l=!!t&&V(t.floating);if(r===i||l&&c)return n;let a={scrollLeft:0,scrollTop:0},u=s(1);const f=s(0),d=Y(r);if((d||!d&&!c)&&(("body"!==U(r)||$(i))&&(a=J(r)),Y(r))){const e=le(r);u=oe(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}const m=!i||d||c?s(0):ue(i,a,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-a.scrollLeft*u.x+f.x+m.x,y:n.y*u.y-a.scrollTop*u.y+f.y+m.y}}function de(e){return Array.from(e.getClientRects())}function me(e){const t=F(e),n=J(e),r=e.ownerDocument.body,c=o(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=o(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+ae(e);const l=-n.scrollTop;return"rtl"===z(r).direction&&(s+=o(t.clientWidth,r.clientWidth)-c),{width:c,height:i,x:s,y:l}}function pe(e,t){const n=M(e),r=F(e),o=n.visualViewport;let c=r.clientWidth,i=r.clientHeight,s=0,l=0;if(o){c=o.width,i=o.height;const e=Q();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,l=o.offsetTop)}return{width:c,height:i,x:s,y:l}}function _e(e,t){const n=le(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,c=Y(e)?oe(e):s(1),i=e.clientWidth*c.x,l=e.clientHeight*c.y,a=o*c.x,u=r*c.y;return{width:i,height:l,x:a,y:u}}function Ee(e,t,n){let r;if("viewport"===t)r=pe(e,n);else if("document"===t)r=me(F(e));else if(G(t))r=_e(t,n);else{const n=ie(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return k(r)}function he(e,t){const n=q(e);return!(n===t||!G(n)||j(n))&&("fixed"===z(n).position||he(n,t))}function ge(e,t){const n=t.get(e);if(n)return n;let r=ee(e,[],!1).filter((e=>G(e)&&"body"!==U(e))),o=null;const c="fixed"===z(e).position;let i=c?q(e):e;while(G(i)&&!j(i)){const t=z(i),n=H(i);n||"fixed"!==t.position||(o=null);const s=c?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||$(i)&&!n&&he(e,i);s?r=r.filter((e=>e!==i)):o=t,i=q(i)}return t.set(e,r),r}function ye(e){let{element:t,boundary:n,rootBoundary:c,strategy:i}=e;const s="clippingAncestors"===n?V(t)?[]:ge(t,this._c):[].concat(n),l=[...s,c],a=l[0],u=l.reduce(((e,n)=>{const c=Ee(t,n,i);return e.top=o(c.top,e.top),e.right=r(c.right,e.right),e.bottom=r(c.bottom,e.bottom),e.left=o(c.left,e.left),e}),Ee(t,a,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}}function Le(e){const{width:t,height:n}=ne(e);return{width:t,height:n}}function Te(e,t,n){const r=Y(t),o=F(t),c="fixed"===n,i=le(e,!0,c,t);let l={scrollLeft:0,scrollTop:0};const a=s(0);if(r||!r&&!c)if(("body"!==U(t)||$(o))&&(l=J(t)),r){const e=le(t,!0,c,t);a.x=e.x+t.clientLeft,a.y=e.y+t.clientTop}else o&&(a.x=ae(o));const u=!o||r||c?s(0):ue(o,l),f=i.left+l.scrollLeft-a.x-u.x,d=i.top+l.scrollTop-a.y-u.y;return{x:f,y:d,width:i.width,height:i.height}}function Ne(e){return"static"===z(e).position}function Ae(e,t){if(!Y(e)||"fixed"===z(e).position)return null;if(t)return t(e);let n=e.offsetParent;return F(e)===n&&(n=n.ownerDocument.body),n}function be(e,t){const n=M(e);if(V(e))return n;if(!Y(e)){let t=q(e);while(t&&!j(t)){if(G(t)&&!Ne(t))return t;t=q(t)}return n}let r=Ae(e,t);while(r&&X(r)&&Ne(r))r=Ae(r,t);return r&&j(r)&&Ne(r)&&!H(r)?n:r||B(e)||n}const Oe=async function(e){const t=this.getOffsetParent||be,n=this.getDimensions,r=await n(e.floating);return{reference:Te(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function ke(e){return"rtl"===z(e).direction}const we={convertOffsetParentRelativeRectToViewportRelativeRect:fe,getDocumentElement:F,getClippingRect:ye,getOffsetParent:be,getElementRects:Oe,getClientRects:de,getDimensions:Le,getScale:oe,isElement:G,isRTL:ke};function Ie(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function ve(e,t){let n,c=null;const s=F(e);function l(){var e;clearTimeout(n),null==(e=c)||e.disconnect(),c=null}function a(u,f){void 0===u&&(u=!1),void 0===f&&(f=1),l();const d=e.getBoundingClientRect(),{left:m,top:p,width:_,height:E}=d;if(u||t(),!_||!E)return;const h=i(p),g=i(s.clientWidth-(m+_)),y=i(s.clientHeight-(p+E)),L=i(m),T=-h+"px "+-g+"px "+-y+"px "+-L+"px",N={rootMargin:T,threshold:o(0,r(1,f))||1};let A=!0;function b(t){const r=t[0].intersectionRatio;if(r!==f){if(!A)return a();r?a(!1,r):n=setTimeout((()=>{a(!1,1e-7)}),1e3)}1!==r||Ie(d,e.getBoundingClientRect())||a(),A=!1}try{c=new IntersectionObserver(b,{...N,root:s.ownerDocument})}catch(O){c=new IntersectionObserver(b,N)}c.observe(e)}return a(!0),l}function Ce(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:c=!0,elementResize:i="function"===typeof ResizeObserver,layoutShift:s="function"===typeof IntersectionObserver,animationFrame:l=!1}=r,a=re(e),u=o||c?[...a?ee(a):[],...ee(t)]:[];u.forEach((e=>{o&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)}));const f=a&&s?ve(a,n):null;let d,m=-1,p=null;i&&(p=new ResizeObserver((e=>{let[r]=e;r&&r.target===a&&p&&(p.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame((()=>{var e;null==(e=p)||e.observe(t)}))),n()})),a&&!l&&p.observe(a),p.observe(t));let _=l?le(e):null;function E(){const t=le(e);_&&!Ie(_,t)&&n(),_=t,d=requestAnimationFrame(E)}return l&&E(),n(),()=>{var e;u.forEach((e=>{o&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)})),null==f||f(),null==(e=p)||e.disconnect(),p=null,l&&cancelAnimationFrame(d)}}const xe=v,Se=P,Pe=D,De=x,Re=C,Ue=(e,t,n)=>{const r=new Map,o={platform:we,...n},c={...o.platform,_c:r};return I(e,t,{...o,platform:c})}}}]);