{"ast": null, "code": "import baseToString from './_baseToString.js';\nimport castSlice from './_castSlice.js';\nimport charsEndIndex from './_charsEndIndex.js';\nimport stringToArray from './_stringToArray.js';\nimport toString from './toString.js';\nimport trimmedEndIndex from './_trimmedEndIndex.js';\n\n/**\n * Removes trailing whitespace or specified characters from `string`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to trim.\n * @param {string} [chars=whitespace] The characters to trim.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {string} Returns the trimmed string.\n * @example\n *\n * _.trimEnd('  abc  ');\n * // => '  abc'\n *\n * _.trimEnd('-_-abc-_-', '_-');\n * // => '-_-abc'\n */\nfunction trimEnd(string, chars, guard) {\n  string = toString(string);\n  if (string && (guard || chars === undefined)) {\n    return string.slice(0, trimmedEndIndex(string) + 1);\n  }\n  if (!string || !(chars = baseToString(chars))) {\n    return string;\n  }\n  var strSymbols = stringToArray(string),\n    end = charsEndIndex(strSymbols, stringToArray(chars)) + 1;\n  return castSlice(strSymbols, 0, end).join('');\n}\nexport default trimEnd;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}