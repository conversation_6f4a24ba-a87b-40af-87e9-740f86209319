{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, inject, ref, openBlock, createElementBlock, normalizeClass, unref, withModifiers, renderSlot } from 'vue';\nimport { throwError } from '../../../utils/error.mjs';\nimport { uploadContextKey } from './constants.mjs';\nimport { uploadDraggerProps, uploadDraggerEmits } from './upload-dragger.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nconst COMPONENT_NAME = \"ElUploadDrag\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: uploadDraggerProps,\n  emits: uploadDraggerEmits,\n  setup(__props, {\n    emit\n  }) {\n    const uploaderContext = inject(uploadContextKey);\n    if (!uploaderContext) {\n      throwError(COMPONENT_NAME, \"usage: <el-upload><el-upload-dragger /></el-upload>\");\n    }\n    const ns = useNamespace(\"upload\");\n    const dragover = ref(false);\n    const disabled = useFormDisabled();\n    const onDrop = e => {\n      if (disabled.value) return;\n      dragover.value = false;\n      e.stopPropagation();\n      const files = Array.from(e.dataTransfer.files);\n      const items = e.dataTransfer.items || [];\n      files.forEach((file, index) => {\n        var _a;\n        const item = items[index];\n        const entry = (_a = item == null ? void 0 : item.webkitGetAsEntry) == null ? void 0 : _a.call(item);\n        if (entry) {\n          file.isDirectory = entry.isDirectory;\n        }\n      });\n      emit(\"file\", files);\n    };\n    const onDragover = () => {\n      if (!disabled.value) dragover.value = true;\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ns).b(\"dragger\"), unref(ns).is(\"dragover\", dragover.value)]),\n        onDrop: withModifiers(onDrop, [\"prevent\"]),\n        onDragover: withModifiers(onDragover, [\"prevent\"]),\n        onDragleave: withModifiers($event => dragover.value = false, [\"prevent\"])\n      }, [renderSlot(_ctx.$slots, \"default\")], 42, [\"onDrop\", \"onDragover\", \"onDragleave\"]);\n    };\n  }\n});\nvar UploadDragger = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"upload-dragger.vue\"]]);\nexport { UploadDragger as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}