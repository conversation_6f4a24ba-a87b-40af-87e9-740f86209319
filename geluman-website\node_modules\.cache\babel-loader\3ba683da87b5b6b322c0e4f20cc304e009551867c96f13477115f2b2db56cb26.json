{"ast": null, "code": "import { defineComponent, useAttrs, useSlots, computed, shallowRef, ref, watch, nextTick, onMounted, toRef, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, createCommentVNode, Fragment, renderSlot, createElementVNode, createBlock, withCtx, resolveDynamicComponent, mergeProps, withModifiers, createVNode, toDisplayString } from 'vue';\nimport { useResizeObserver, isClient } from '@vueuse/core';\nimport { isNil } from 'lodash-unified';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { View, Hide, CircleClose } from '@element-plus/icons-vue';\nimport { calcTextareaHeight } from './utils.mjs';\nimport { inputProps, inputEmits } from './input2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useAttrs as useAttrs$1 } from '../../../hooks/use-attrs/index.mjs';\nimport { useFormItem, useFormItemInputId } from '../../form/src/hooks/use-form-item.mjs';\nimport { useFormSize, useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useFocusController } from '../../../hooks/use-focus-controller/index.mjs';\nimport { ValidateComponentsMap } from '../../../utils/vue/icon.mjs';\nimport { useComposition } from '../../../hooks/use-composition/index.mjs';\nimport { UPDATE_MODEL_EVENT, INPUT_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { useCursor } from '../../../hooks/use-cursor/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { NOOP, isObject } from '@vue/shared';\nconst COMPONENT_NAME = \"ElInput\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME,\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: inputProps,\n  emits: inputEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const rawAttrs = useAttrs();\n    const attrs = useAttrs$1();\n    const slots = useSlots();\n    const containerKls = computed(() => [props.type === \"textarea\" ? nsTextarea.b() : nsInput.b(), nsInput.m(inputSize.value), nsInput.is(\"disabled\", inputDisabled.value), nsInput.is(\"exceed\", inputExceed.value), {\n      [nsInput.b(\"group\")]: slots.prepend || slots.append,\n      [nsInput.m(\"prefix\")]: slots.prefix || props.prefixIcon,\n      [nsInput.m(\"suffix\")]: slots.suffix || props.suffixIcon || props.clearable || props.showPassword,\n      [nsInput.bm(\"suffix\", \"password-clear\")]: showClear.value && showPwdVisible.value,\n      [nsInput.b(\"hidden\")]: props.type === \"hidden\"\n    }, rawAttrs.class]);\n    const wrapperKls = computed(() => [nsInput.e(\"wrapper\"), nsInput.is(\"focus\", isFocused.value)]);\n    const {\n      form: elForm,\n      formItem: elFormItem\n    } = useFormItem();\n    const {\n      inputId\n    } = useFormItemInputId(props, {\n      formItemContext: elFormItem\n    });\n    const inputSize = useFormSize();\n    const inputDisabled = useFormDisabled();\n    const nsInput = useNamespace(\"input\");\n    const nsTextarea = useNamespace(\"textarea\");\n    const input = shallowRef();\n    const textarea = shallowRef();\n    const hovering = ref(false);\n    const passwordVisible = ref(false);\n    const countStyle = ref();\n    const textareaCalcStyle = shallowRef(props.inputStyle);\n    const _ref = computed(() => input.value || textarea.value);\n    const {\n      wrapperRef,\n      isFocused,\n      handleFocus,\n      handleBlur\n    } = useFocusController(_ref, {\n      beforeFocus() {\n        return inputDisabled.value;\n      },\n      afterBlur() {\n        var _a;\n        if (props.validateEvent) {\n          (_a = elFormItem == null ? void 0 : elFormItem.validate) == null ? void 0 : _a.call(elFormItem, \"blur\").catch(err => debugWarn(err));\n        }\n      }\n    });\n    const needStatusIcon = computed(() => {\n      var _a;\n      return (_a = elForm == null ? void 0 : elForm.statusIcon) != null ? _a : false;\n    });\n    const validateState = computed(() => (elFormItem == null ? void 0 : elFormItem.validateState) || \"\");\n    const validateIcon = computed(() => validateState.value && ValidateComponentsMap[validateState.value]);\n    const passwordIcon = computed(() => passwordVisible.value ? View : Hide);\n    const containerStyle = computed(() => [rawAttrs.style]);\n    const textareaStyle = computed(() => [props.inputStyle, textareaCalcStyle.value, {\n      resize: props.resize\n    }]);\n    const nativeInputValue = computed(() => isNil(props.modelValue) ? \"\" : String(props.modelValue));\n    const showClear = computed(() => props.clearable && !inputDisabled.value && !props.readonly && !!nativeInputValue.value && (isFocused.value || hovering.value));\n    const showPwdVisible = computed(() => props.showPassword && !inputDisabled.value && !!nativeInputValue.value && (!!nativeInputValue.value || isFocused.value));\n    const isWordLimitVisible = computed(() => props.showWordLimit && !!props.maxlength && (props.type === \"text\" || props.type === \"textarea\") && !inputDisabled.value && !props.readonly && !props.showPassword);\n    const textLength = computed(() => nativeInputValue.value.length);\n    const inputExceed = computed(() => !!isWordLimitVisible.value && textLength.value > Number(props.maxlength));\n    const suffixVisible = computed(() => !!slots.suffix || !!props.suffixIcon || showClear.value || props.showPassword || isWordLimitVisible.value || !!validateState.value && needStatusIcon.value);\n    const [recordCursor, setCursor] = useCursor(input);\n    useResizeObserver(textarea, entries => {\n      onceInitSizeTextarea();\n      if (!isWordLimitVisible.value || props.resize !== \"both\") return;\n      const entry = entries[0];\n      const {\n        width\n      } = entry.contentRect;\n      countStyle.value = {\n        right: `calc(100% - ${width + 15 + 6}px)`\n      };\n    });\n    const resizeTextarea = () => {\n      const {\n        type,\n        autosize\n      } = props;\n      if (!isClient || type !== \"textarea\" || !textarea.value) return;\n      if (autosize) {\n        const minRows = isObject(autosize) ? autosize.minRows : void 0;\n        const maxRows = isObject(autosize) ? autosize.maxRows : void 0;\n        const textareaStyle2 = calcTextareaHeight(textarea.value, minRows, maxRows);\n        textareaCalcStyle.value = {\n          overflowY: \"hidden\",\n          ...textareaStyle2\n        };\n        nextTick(() => {\n          textarea.value.offsetHeight;\n          textareaCalcStyle.value = textareaStyle2;\n        });\n      } else {\n        textareaCalcStyle.value = {\n          minHeight: calcTextareaHeight(textarea.value).minHeight\n        };\n      }\n    };\n    const createOnceInitResize = resizeTextarea2 => {\n      let isInit = false;\n      return () => {\n        var _a;\n        if (isInit || !props.autosize) return;\n        const isElHidden = ((_a = textarea.value) == null ? void 0 : _a.offsetParent) === null;\n        if (!isElHidden) {\n          resizeTextarea2();\n          isInit = true;\n        }\n      };\n    };\n    const onceInitSizeTextarea = createOnceInitResize(resizeTextarea);\n    const setNativeInputValue = () => {\n      const input2 = _ref.value;\n      const formatterValue = props.formatter ? props.formatter(nativeInputValue.value) : nativeInputValue.value;\n      if (!input2 || input2.value === formatterValue) return;\n      input2.value = formatterValue;\n    };\n    const handleInput = async event => {\n      recordCursor();\n      let {\n        value\n      } = event.target;\n      if (props.formatter && props.parser) {\n        value = props.parser(value);\n      }\n      if (isComposing.value) return;\n      if (value === nativeInputValue.value) {\n        setNativeInputValue();\n        return;\n      }\n      emit(UPDATE_MODEL_EVENT, value);\n      emit(INPUT_EVENT, value);\n      await nextTick();\n      setNativeInputValue();\n      setCursor();\n    };\n    const handleChange = event => {\n      let {\n        value\n      } = event.target;\n      if (props.formatter && props.parser) {\n        value = props.parser(value);\n      }\n      emit(CHANGE_EVENT, value);\n    };\n    const {\n      isComposing,\n      handleCompositionStart,\n      handleCompositionUpdate,\n      handleCompositionEnd\n    } = useComposition({\n      emit,\n      afterComposition: handleInput\n    });\n    const handlePasswordVisible = () => {\n      recordCursor();\n      passwordVisible.value = !passwordVisible.value;\n      setTimeout(setCursor);\n    };\n    const focus = () => {\n      var _a;\n      return (_a = _ref.value) == null ? void 0 : _a.focus();\n    };\n    const blur = () => {\n      var _a;\n      return (_a = _ref.value) == null ? void 0 : _a.blur();\n    };\n    const handleMouseLeave = evt => {\n      hovering.value = false;\n      emit(\"mouseleave\", evt);\n    };\n    const handleMouseEnter = evt => {\n      hovering.value = true;\n      emit(\"mouseenter\", evt);\n    };\n    const handleKeydown = evt => {\n      emit(\"keydown\", evt);\n    };\n    const select = () => {\n      var _a;\n      (_a = _ref.value) == null ? void 0 : _a.select();\n    };\n    const clear = () => {\n      emit(UPDATE_MODEL_EVENT, \"\");\n      emit(CHANGE_EVENT, \"\");\n      emit(\"clear\");\n      emit(INPUT_EVENT, \"\");\n    };\n    watch(() => props.modelValue, () => {\n      var _a;\n      nextTick(() => resizeTextarea());\n      if (props.validateEvent) {\n        (_a = elFormItem == null ? void 0 : elFormItem.validate) == null ? void 0 : _a.call(elFormItem, \"change\").catch(err => debugWarn(err));\n      }\n    });\n    watch(nativeInputValue, () => setNativeInputValue());\n    watch(() => props.type, async () => {\n      await nextTick();\n      setNativeInputValue();\n      resizeTextarea();\n    });\n    onMounted(() => {\n      if (!props.formatter && props.parser) {\n        debugWarn(COMPONENT_NAME, \"If you set the parser, you also need to set the formatter.\");\n      }\n      setNativeInputValue();\n      nextTick(resizeTextarea);\n    });\n    expose({\n      input,\n      textarea,\n      ref: _ref,\n      textareaStyle,\n      autosize: toRef(props, \"autosize\"),\n      isComposing,\n      focus,\n      blur,\n      select,\n      clear,\n      resizeTextarea\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(containerKls), {\n          [unref(nsInput).bm(\"group\", \"append\")]: _ctx.$slots.append,\n          [unref(nsInput).bm(\"group\", \"prepend\")]: _ctx.$slots.prepend\n        }]),\n        style: normalizeStyle(unref(containerStyle)),\n        onMouseenter: handleMouseEnter,\n        onMouseleave: handleMouseLeave\n      }, [createCommentVNode(\" input \"), _ctx.type !== \"textarea\" ? (openBlock(), createElementBlock(Fragment, {\n        key: 0\n      }, [createCommentVNode(\" prepend slot \"), _ctx.$slots.prepend ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(nsInput).be(\"group\", \"prepend\"))\n      }, [renderSlot(_ctx.$slots, \"prepend\")], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        ref_key: \"wrapperRef\",\n        ref: wrapperRef,\n        class: normalizeClass(unref(wrapperKls))\n      }, [createCommentVNode(\" prefix slot \"), _ctx.$slots.prefix || _ctx.prefixIcon ? (openBlock(), createElementBlock(\"span\", {\n        key: 0,\n        class: normalizeClass(unref(nsInput).e(\"prefix\"))\n      }, [createElementVNode(\"span\", {\n        class: normalizeClass(unref(nsInput).e(\"prefix-inner\"))\n      }, [renderSlot(_ctx.$slots, \"prefix\"), _ctx.prefixIcon ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0,\n        class: normalizeClass(unref(nsInput).e(\"icon\"))\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.prefixIcon)))]),\n        _: 1\n      }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true)], 2)], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"input\", mergeProps({\n        id: unref(inputId),\n        ref_key: \"input\",\n        ref: input,\n        class: unref(nsInput).e(\"inner\")\n      }, unref(attrs), {\n        minlength: _ctx.minlength,\n        maxlength: _ctx.maxlength,\n        type: _ctx.showPassword ? passwordVisible.value ? \"text\" : \"password\" : _ctx.type,\n        disabled: unref(inputDisabled),\n        readonly: _ctx.readonly,\n        autocomplete: _ctx.autocomplete,\n        tabindex: _ctx.tabindex,\n        \"aria-label\": _ctx.ariaLabel,\n        placeholder: _ctx.placeholder,\n        style: _ctx.inputStyle,\n        form: _ctx.form,\n        autofocus: _ctx.autofocus,\n        role: _ctx.containerRole,\n        onCompositionstart: unref(handleCompositionStart),\n        onCompositionupdate: unref(handleCompositionUpdate),\n        onCompositionend: unref(handleCompositionEnd),\n        onInput: handleInput,\n        onChange: handleChange,\n        onKeydown: handleKeydown\n      }), null, 16, [\"id\", \"minlength\", \"maxlength\", \"type\", \"disabled\", \"readonly\", \"autocomplete\", \"tabindex\", \"aria-label\", \"placeholder\", \"form\", \"autofocus\", \"role\", \"onCompositionstart\", \"onCompositionupdate\", \"onCompositionend\"]), createCommentVNode(\" suffix slot \"), unref(suffixVisible) ? (openBlock(), createElementBlock(\"span\", {\n        key: 1,\n        class: normalizeClass(unref(nsInput).e(\"suffix\"))\n      }, [createElementVNode(\"span\", {\n        class: normalizeClass(unref(nsInput).e(\"suffix-inner\"))\n      }, [!unref(showClear) || !unref(showPwdVisible) || !unref(isWordLimitVisible) ? (openBlock(), createElementBlock(Fragment, {\n        key: 0\n      }, [renderSlot(_ctx.$slots, \"suffix\"), _ctx.suffixIcon ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0,\n        class: normalizeClass(unref(nsInput).e(\"icon\"))\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.suffixIcon)))]),\n        _: 1\n      }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true)], 64)) : createCommentVNode(\"v-if\", true), unref(showClear) ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 1,\n        class: normalizeClass([unref(nsInput).e(\"icon\"), unref(nsInput).e(\"clear\")]),\n        onMousedown: withModifiers(unref(NOOP), [\"prevent\"]),\n        onClick: clear\n      }, {\n        default: withCtx(() => [createVNode(unref(CircleClose))]),\n        _: 1\n      }, 8, [\"class\", \"onMousedown\"])) : createCommentVNode(\"v-if\", true), unref(showPwdVisible) ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 2,\n        class: normalizeClass([unref(nsInput).e(\"icon\"), unref(nsInput).e(\"password\")]),\n        onClick: handlePasswordVisible\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(passwordIcon))))]),\n        _: 1\n      }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true), unref(isWordLimitVisible) ? (openBlock(), createElementBlock(\"span\", {\n        key: 3,\n        class: normalizeClass(unref(nsInput).e(\"count\"))\n      }, [createElementVNode(\"span\", {\n        class: normalizeClass(unref(nsInput).e(\"count-inner\"))\n      }, toDisplayString(unref(textLength)) + \" / \" + toDisplayString(_ctx.maxlength), 3)], 2)) : createCommentVNode(\"v-if\", true), unref(validateState) && unref(validateIcon) && unref(needStatusIcon) ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 4,\n        class: normalizeClass([unref(nsInput).e(\"icon\"), unref(nsInput).e(\"validateIcon\"), unref(nsInput).is(\"loading\", unref(validateState) === \"validating\")])\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(validateIcon))))]),\n        _: 1\n      }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true)], 2)], 2)) : createCommentVNode(\"v-if\", true)], 2), createCommentVNode(\" append slot \"), _ctx.$slots.append ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(unref(nsInput).be(\"group\", \"append\"))\n      }, [renderSlot(_ctx.$slots, \"append\")], 2)) : createCommentVNode(\"v-if\", true)], 64)) : (openBlock(), createElementBlock(Fragment, {\n        key: 1\n      }, [createCommentVNode(\" textarea \"), createElementVNode(\"textarea\", mergeProps({\n        id: unref(inputId),\n        ref_key: \"textarea\",\n        ref: textarea,\n        class: [unref(nsTextarea).e(\"inner\"), unref(nsInput).is(\"focus\", unref(isFocused))]\n      }, unref(attrs), {\n        minlength: _ctx.minlength,\n        maxlength: _ctx.maxlength,\n        tabindex: _ctx.tabindex,\n        disabled: unref(inputDisabled),\n        readonly: _ctx.readonly,\n        autocomplete: _ctx.autocomplete,\n        style: unref(textareaStyle),\n        \"aria-label\": _ctx.ariaLabel,\n        placeholder: _ctx.placeholder,\n        form: _ctx.form,\n        autofocus: _ctx.autofocus,\n        rows: _ctx.rows,\n        role: _ctx.containerRole,\n        onCompositionstart: unref(handleCompositionStart),\n        onCompositionupdate: unref(handleCompositionUpdate),\n        onCompositionend: unref(handleCompositionEnd),\n        onInput: handleInput,\n        onFocus: unref(handleFocus),\n        onBlur: unref(handleBlur),\n        onChange: handleChange,\n        onKeydown: handleKeydown\n      }), null, 16, [\"id\", \"minlength\", \"maxlength\", \"tabindex\", \"disabled\", \"readonly\", \"autocomplete\", \"aria-label\", \"placeholder\", \"form\", \"autofocus\", \"rows\", \"role\", \"onCompositionstart\", \"onCompositionupdate\", \"onCompositionend\", \"onFocus\", \"onBlur\"]), unref(isWordLimitVisible) ? (openBlock(), createElementBlock(\"span\", {\n        key: 0,\n        style: normalizeStyle(countStyle.value),\n        class: normalizeClass(unref(nsInput).e(\"count\"))\n      }, toDisplayString(unref(textLength)) + \" / \" + toDisplayString(_ctx.maxlength), 7)) : createCommentVNode(\"v-if\", true)], 64))], 38);\n    };\n  }\n});\nvar Input = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"input.vue\"]]);\nexport { Input as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}