{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { nextTick } from 'vue';\nimport { obtainAllFocusableElements } from '../../utils/dom/aria.mjs';\nimport { EVENT_CODE } from '../../constants/aria.mjs';\nconst FOCUSABLE_CHILDREN = \"_trap-focus-children\";\nconst TRAP_FOCUS_HANDLER = \"_trap-focus-handler\";\nconst FOCUS_STACK = [];\nconst FOCUS_HANDLER = e => {\n  var _a;\n  if (FOCUS_STACK.length === 0) return;\n  const focusableElement = FOCUS_STACK[FOCUS_STACK.length - 1][FOCUSABLE_CHILDREN];\n  if (focusableElement.length > 0 && e.code === EVENT_CODE.tab) {\n    if (focusableElement.length === 1) {\n      e.preventDefault();\n      if (document.activeElement !== focusableElement[0]) {\n        focusableElement[0].focus();\n      }\n      return;\n    }\n    const goingBackward = e.shiftKey;\n    const isFirst = e.target === focusableElement[0];\n    const isLast = e.target === focusableElement[focusableElement.length - 1];\n    if (isFirst && goingBackward) {\n      e.preventDefault();\n      focusableElement[focusableElement.length - 1].focus();\n    }\n    if (isLast && !goingBackward) {\n      e.preventDefault();\n      focusableElement[0].focus();\n    }\n    if (process.env.NODE_ENV === \"test\") {\n      const index = focusableElement.indexOf(e.target);\n      if (index !== -1) {\n        (_a = focusableElement[goingBackward ? index - 1 : index + 1]) == null ? void 0 : _a.focus();\n      }\n    }\n  }\n};\nconst TrapFocus = {\n  beforeMount(el) {\n    el[FOCUSABLE_CHILDREN] = obtainAllFocusableElements(el);\n    FOCUS_STACK.push(el);\n    if (FOCUS_STACK.length <= 1) {\n      document.addEventListener(\"keydown\", FOCUS_HANDLER);\n    }\n  },\n  updated(el) {\n    nextTick(() => {\n      el[FOCUSABLE_CHILDREN] = obtainAllFocusableElements(el);\n    });\n  },\n  unmounted() {\n    FOCUS_STACK.shift();\n    if (FOCUS_STACK.length === 0) {\n      document.removeEventListener(\"keydown\", FOCUS_HANDLER);\n    }\n  }\n};\nexport { FOCUSABLE_CHILDREN, TRAP_FOCUS_HANDLER, TrapFocus as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}