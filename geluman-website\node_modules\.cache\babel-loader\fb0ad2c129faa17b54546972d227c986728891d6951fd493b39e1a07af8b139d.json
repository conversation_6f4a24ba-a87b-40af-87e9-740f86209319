{"ast": null, "code": "import { defineComponent, useSlots, reactive, computed, toRefs, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, createVNode, isRef, withCtx, createTextVNode, toDisplayString, createBlock, createCommentVNode, withDirectives, Fragment, renderList, vShow, renderSlot } from 'vue';\nimport { ElCheckbox, ElCheckboxGroup } from '../../checkbox/index.mjs';\nimport { ElInput } from '../../input/index.mjs';\nimport { Search } from '@element-plus/icons-vue';\nimport { transferPanelProps, transferPanelEmits } from './transfer-panel.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { usePropsAlias } from './composables/use-props-alias.mjs';\nimport { useCheck } from './composables/use-check.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isEmpty } from '../../../utils/types.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTransferPanel\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: transferPanelProps,\n  emits: transferPanelEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const slots = useSlots();\n    const OptionContent = ({\n      option\n    }) => option;\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"transfer\");\n    const panelState = reactive({\n      checked: [],\n      allChecked: false,\n      query: \"\",\n      checkChangeByUser: true\n    });\n    const propsAlias = usePropsAlias(props);\n    const {\n      filteredData,\n      checkedSummary,\n      isIndeterminate,\n      handleAllCheckedChange\n    } = useCheck(props, panelState, emit);\n    const hasNoMatch = computed(() => !isEmpty(panelState.query) && isEmpty(filteredData.value));\n    const hasFooter = computed(() => !isEmpty(slots.default()[0].children));\n    const {\n      checked,\n      allChecked,\n      query\n    } = toRefs(panelState);\n    expose({\n      query\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(ns).b(\"panel\"))\n      }, [createElementVNode(\"p\", {\n        class: normalizeClass(unref(ns).be(\"panel\", \"header\"))\n      }, [createVNode(unref(ElCheckbox), {\n        modelValue: unref(allChecked),\n        \"onUpdate:modelValue\": $event => isRef(allChecked) ? allChecked.value = $event : null,\n        indeterminate: unref(isIndeterminate),\n        \"validate-event\": false,\n        onChange: unref(handleAllCheckedChange)\n      }, {\n        default: withCtx(() => [createTextVNode(toDisplayString(_ctx.title) + \" \", 1), createElementVNode(\"span\", null, toDisplayString(unref(checkedSummary)), 1)]),\n        _: 1\n      }, 8, [\"modelValue\", \"onUpdate:modelValue\", \"indeterminate\", \"onChange\"])], 2), createElementVNode(\"div\", {\n        class: normalizeClass([unref(ns).be(\"panel\", \"body\"), unref(ns).is(\"with-footer\", unref(hasFooter))])\n      }, [_ctx.filterable ? (openBlock(), createBlock(unref(ElInput), {\n        key: 0,\n        modelValue: unref(query),\n        \"onUpdate:modelValue\": $event => isRef(query) ? query.value = $event : null,\n        class: normalizeClass(unref(ns).be(\"panel\", \"filter\")),\n        size: \"default\",\n        placeholder: _ctx.placeholder,\n        \"prefix-icon\": unref(Search),\n        clearable: \"\",\n        \"validate-event\": false\n      }, null, 8, [\"modelValue\", \"onUpdate:modelValue\", \"class\", \"placeholder\", \"prefix-icon\"])) : createCommentVNode(\"v-if\", true), withDirectives(createVNode(unref(ElCheckboxGroup), {\n        modelValue: unref(checked),\n        \"onUpdate:modelValue\": $event => isRef(checked) ? checked.value = $event : null,\n        \"validate-event\": false,\n        class: normalizeClass([unref(ns).is(\"filterable\", _ctx.filterable), unref(ns).be(\"panel\", \"list\")])\n      }, {\n        default: withCtx(() => [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(filteredData), item => {\n          return openBlock(), createBlock(unref(ElCheckbox), {\n            key: item[unref(propsAlias).key],\n            class: normalizeClass(unref(ns).be(\"panel\", \"item\")),\n            value: item[unref(propsAlias).key],\n            disabled: item[unref(propsAlias).disabled],\n            \"validate-event\": false\n          }, {\n            default: withCtx(() => {\n              var _a;\n              return [createVNode(OptionContent, {\n                option: (_a = _ctx.optionRender) == null ? void 0 : _a.call(_ctx, item)\n              }, null, 8, [\"option\"])];\n            }),\n            _: 2\n          }, 1032, [\"class\", \"value\", \"disabled\"]);\n        }), 128))]),\n        _: 1\n      }, 8, [\"modelValue\", \"onUpdate:modelValue\", \"class\"]), [[vShow, !unref(hasNoMatch) && !unref(isEmpty)(_ctx.data)]]), withDirectives(createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).be(\"panel\", \"empty\"))\n      }, [renderSlot(_ctx.$slots, \"empty\", {}, () => [createTextVNode(toDisplayString(unref(hasNoMatch) ? unref(t)(\"el.transfer.noMatch\") : unref(t)(\"el.transfer.noData\")), 1)])], 2), [[vShow, unref(hasNoMatch) || unref(isEmpty)(_ctx.data)]])], 2), unref(hasFooter) ? (openBlock(), createElementBlock(\"p\", {\n        key: 0,\n        class: normalizeClass(unref(ns).be(\"panel\", \"footer\"))\n      }, [renderSlot(_ctx.$slots, \"default\")], 2)) : createCommentVNode(\"v-if\", true)], 2);\n    };\n  }\n});\nvar TransferPanel = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"transfer-panel.vue\"]]);\nexport { TransferPanel as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}