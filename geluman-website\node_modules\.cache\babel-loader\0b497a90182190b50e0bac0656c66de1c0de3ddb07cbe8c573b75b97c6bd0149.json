{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport { defineComponent, ref, computed, watch, openBlock, createElementBlock, normalizeClass, unref, renderSlot, createCommentVNode, withDirectives, createVNode, withCtx, Fragment, renderList, withModifiers, createElementVNode, toDisplayString, vShow, createTextVNode, nextTick } from 'vue';\nimport { ElScrollbar } from '../../scrollbar/index.mjs';\nimport { mentionDropdownProps, mentionDropdownEmits } from './mention-dropdown.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { scrollIntoView } from '../../../utils/dom/scroll.mjs';\nconst __default__ = defineComponent({\n  name: \"ElMentionDropdown\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: mentionDropdownProps,\n  emits: mentionDropdownEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"mention\");\n    const {\n      t\n    } = useLocale();\n    const hoveringIndex = ref(-1);\n    const scrollbarRef = ref();\n    const optionRefs = ref();\n    const dropdownRef = ref();\n    const optionkls = (item, index) => [ns.be(\"dropdown\", \"item\"), ns.is(\"hovering\", hoveringIndex.value === index), ns.is(\"disabled\", item.disabled || props.disabled)];\n    const handleSelect = item => {\n      if (item.disabled || props.disabled) return;\n      emit(\"select\", item);\n    };\n    const handleMouseEnter = index => {\n      hoveringIndex.value = index;\n    };\n    const filteredAllDisabled = computed(() => props.disabled || props.options.every(item => item.disabled));\n    const hoverOption = computed(() => props.options[hoveringIndex.value]);\n    const selectHoverOption = () => {\n      if (!hoverOption.value) return;\n      emit(\"select\", hoverOption.value);\n    };\n    const navigateOptions = direction => {\n      const {\n        options\n      } = props;\n      if (options.length === 0 || filteredAllDisabled.value) return;\n      if (direction === \"next\") {\n        hoveringIndex.value++;\n        if (hoveringIndex.value === options.length) {\n          hoveringIndex.value = 0;\n        }\n      } else if (direction === \"prev\") {\n        hoveringIndex.value--;\n        if (hoveringIndex.value < 0) {\n          hoveringIndex.value = options.length - 1;\n        }\n      }\n      const option = options[hoveringIndex.value];\n      if (option.disabled) {\n        navigateOptions(direction);\n        return;\n      }\n      nextTick(() => scrollToOption(option));\n    };\n    const scrollToOption = option => {\n      var _a, _b, _c, _d;\n      const {\n        options\n      } = props;\n      const index = options.findIndex(item => item.value === option.value);\n      const target = (_a = optionRefs.value) == null ? void 0 : _a[index];\n      if (target) {\n        const menu = (_c = (_b = dropdownRef.value) == null ? void 0 : _b.querySelector) == null ? void 0 : _c.call(_b, `.${ns.be(\"dropdown\", \"wrap\")}`);\n        if (menu) {\n          scrollIntoView(menu, target);\n        }\n      }\n      (_d = scrollbarRef.value) == null ? void 0 : _d.handleScroll();\n    };\n    const resetHoveringIndex = () => {\n      if (filteredAllDisabled.value || props.options.length === 0) {\n        hoveringIndex.value = -1;\n      } else {\n        hoveringIndex.value = 0;\n      }\n    };\n    watch(() => props.options, resetHoveringIndex, {\n      immediate: true\n    });\n    expose({\n      hoveringIndex,\n      navigateOptions,\n      selectHoverOption,\n      hoverOption\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"dropdownRef\",\n        ref: dropdownRef,\n        class: normalizeClass(unref(ns).b(\"dropdown\"))\n      }, [_ctx.$slots.header ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).be(\"dropdown\", \"header\"))\n      }, [renderSlot(_ctx.$slots, \"header\")], 2)) : createCommentVNode(\"v-if\", true), withDirectives(createVNode(unref(ElScrollbar), {\n        id: _ctx.contentId,\n        ref_key: \"scrollbarRef\",\n        ref: scrollbarRef,\n        tag: \"ul\",\n        \"wrap-class\": unref(ns).be(\"dropdown\", \"wrap\"),\n        \"view-class\": unref(ns).be(\"dropdown\", \"list\"),\n        role: \"listbox\",\n        \"aria-label\": _ctx.ariaLabel,\n        \"aria-orientation\": \"vertical\"\n      }, {\n        default: withCtx(() => [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.options, (item, index) => {\n          return openBlock(), createElementBlock(\"li\", {\n            id: `${_ctx.contentId}-${index}`,\n            ref_for: true,\n            ref_key: \"optionRefs\",\n            ref: optionRefs,\n            key: index,\n            class: normalizeClass(optionkls(item, index)),\n            role: \"option\",\n            \"aria-disabled\": item.disabled || _ctx.disabled || void 0,\n            \"aria-selected\": hoveringIndex.value === index,\n            onMousemove: $event => handleMouseEnter(index),\n            onClick: withModifiers($event => handleSelect(item), [\"stop\"])\n          }, [renderSlot(_ctx.$slots, \"label\", {\n            item,\n            index\n          }, () => {\n            var _a;\n            return [createElementVNode(\"span\", null, toDisplayString((_a = item.label) != null ? _a : item.value), 1)];\n          })], 42, [\"id\", \"aria-disabled\", \"aria-selected\", \"onMousemove\", \"onClick\"]);\n        }), 128))]),\n        _: 3\n      }, 8, [\"id\", \"wrap-class\", \"view-class\", \"aria-label\"]), [[vShow, _ctx.options.length > 0 && !_ctx.loading]]), _ctx.loading ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(unref(ns).be(\"dropdown\", \"loading\"))\n      }, [renderSlot(_ctx.$slots, \"loading\", {}, () => [createTextVNode(toDisplayString(unref(t)(\"el.mention.loading\")), 1)])], 2)) : createCommentVNode(\"v-if\", true), _ctx.$slots.footer ? (openBlock(), createElementBlock(\"div\", {\n        key: 2,\n        class: normalizeClass(unref(ns).be(\"dropdown\", \"footer\"))\n      }, [renderSlot(_ctx.$slots, \"footer\")], 2)) : createCommentVNode(\"v-if\", true)], 2);\n    };\n  }\n});\nvar ElMentionDropdown = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"mention-dropdown.vue\"]]);\nexport { ElMentionDropdown as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}