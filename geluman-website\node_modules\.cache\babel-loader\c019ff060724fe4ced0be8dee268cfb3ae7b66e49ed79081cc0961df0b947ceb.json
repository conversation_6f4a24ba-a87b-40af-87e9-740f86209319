{"ast": null, "code": "import { getCurrentInstance, computed, watch, onMounted } from 'vue';\nimport { buildProp, definePropType } from '../../utils/vue/props/runtime.mjs';\nimport { isBoolean } from '../../utils/types.mjs';\nimport { isFunction } from '@vue/shared';\nimport { isClient } from '@vueuse/core';\nconst _prop = buildProp({\n  type: definePropType(Boolean),\n  default: null\n});\nconst _event = buildProp({\n  type: definePropType(Function)\n});\nconst createModelToggleComposable = name => {\n  const updateEventKey = `update:${name}`;\n  const updateEventKeyRaw = `onUpdate:${name}`;\n  const useModelToggleEmits2 = [updateEventKey];\n  const useModelToggleProps2 = {\n    [name]: _prop,\n    [updateEventKeyRaw]: _event\n  };\n  const useModelToggle2 = ({\n    indicator,\n    toggleReason,\n    shouldHideWhenRouteChanges,\n    shouldProceed,\n    onShow,\n    onHide\n  }) => {\n    const instance = getCurrentInstance();\n    const {\n      emit\n    } = instance;\n    const props = instance.props;\n    const hasUpdateHandler = computed(() => isFunction(props[updateEventKeyRaw]));\n    const isModelBindingAbsent = computed(() => props[name] === null);\n    const doShow = event => {\n      if (indicator.value === true) {\n        return;\n      }\n      indicator.value = true;\n      if (toggleReason) {\n        toggleReason.value = event;\n      }\n      if (isFunction(onShow)) {\n        onShow(event);\n      }\n    };\n    const doHide = event => {\n      if (indicator.value === false) {\n        return;\n      }\n      indicator.value = false;\n      if (toggleReason) {\n        toggleReason.value = event;\n      }\n      if (isFunction(onHide)) {\n        onHide(event);\n      }\n    };\n    const show = event => {\n      if (props.disabled === true || isFunction(shouldProceed) && !shouldProceed()) return;\n      const shouldEmit = hasUpdateHandler.value && isClient;\n      if (shouldEmit) {\n        emit(updateEventKey, true);\n      }\n      if (isModelBindingAbsent.value || !shouldEmit) {\n        doShow(event);\n      }\n    };\n    const hide = event => {\n      if (props.disabled === true || !isClient) return;\n      const shouldEmit = hasUpdateHandler.value && isClient;\n      if (shouldEmit) {\n        emit(updateEventKey, false);\n      }\n      if (isModelBindingAbsent.value || !shouldEmit) {\n        doHide(event);\n      }\n    };\n    const onChange = val => {\n      if (!isBoolean(val)) return;\n      if (props.disabled && val) {\n        if (hasUpdateHandler.value) {\n          emit(updateEventKey, false);\n        }\n      } else if (indicator.value !== val) {\n        if (val) {\n          doShow();\n        } else {\n          doHide();\n        }\n      }\n    };\n    const toggle = () => {\n      if (indicator.value) {\n        hide();\n      } else {\n        show();\n      }\n    };\n    watch(() => props[name], onChange);\n    if (shouldHideWhenRouteChanges && instance.appContext.config.globalProperties.$route !== void 0) {\n      watch(() => ({\n        ...instance.proxy.$route\n      }), () => {\n        if (shouldHideWhenRouteChanges.value && indicator.value) {\n          hide();\n        }\n      });\n    }\n    onMounted(() => {\n      onChange(props[name]);\n    });\n    return {\n      hide,\n      show,\n      toggle,\n      hasUpdateHandler\n    };\n  };\n  return {\n    useModelToggle: useModelToggle2,\n    useModelToggleProps: useModelToggleProps2,\n    useModelToggleEmits: useModelToggleEmits2\n  };\n};\nconst {\n  useModelToggle,\n  useModelToggleProps,\n  useModelToggleEmits\n} = createModelToggleComposable(\"modelValue\");\nexport { createModelToggleComposable, useModelToggle, useModelToggleEmits, useModelToggleProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}