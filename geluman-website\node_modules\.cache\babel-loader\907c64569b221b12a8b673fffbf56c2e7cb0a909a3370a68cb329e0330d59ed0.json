{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, onMounted, onBeforeUnmount } from 'vue';\nimport { FOCUSOUT_PREVENTED, FOCUSOUT_PREVENTED_OPTS } from './tokens.mjs';\nimport { isElement } from '../../../utils/types.mjs';\nimport { isFocusable } from '../../../utils/dom/aria.mjs';\nconst focusReason = ref();\nconst lastUserFocusTimestamp = ref(0);\nconst lastAutomatedFocusTimestamp = ref(0);\nlet focusReasonUserCount = 0;\nconst obtainAllFocusableElements = element => {\n  const nodes = [];\n  const walker = document.createTreeWalker(element, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: node => {\n      const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      return node.tabIndex >= 0 || node === document.activeElement ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    }\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode);\n  return nodes;\n};\nconst getVisibleElement = (elements, container) => {\n  for (const element of elements) {\n    if (!isHidden(element, container)) return element;\n  }\n};\nconst isHidden = (element, container) => {\n  if (process.env.NODE_ENV === \"test\") return false;\n  if (getComputedStyle(element).visibility === \"hidden\") return true;\n  while (element) {\n    if (container && element === container) return false;\n    if (getComputedStyle(element).display === \"none\") return true;\n    element = element.parentElement;\n  }\n  return false;\n};\nconst getEdges = container => {\n  const focusable = obtainAllFocusableElements(container);\n  const first = getVisibleElement(focusable, container);\n  const last = getVisibleElement(focusable.reverse(), container);\n  return [first, last];\n};\nconst isSelectable = element => {\n  return element instanceof HTMLInputElement && \"select\" in element;\n};\nconst tryFocus = (element, shouldSelect) => {\n  if (element && element.focus) {\n    const prevFocusedElement = document.activeElement;\n    let cleanup = false;\n    if (isElement(element) && !isFocusable(element) && !element.getAttribute(\"tabindex\")) {\n      element.setAttribute(\"tabindex\", \"-1\");\n      cleanup = true;\n    }\n    element.focus({\n      preventScroll: true\n    });\n    lastAutomatedFocusTimestamp.value = window.performance.now();\n    if (element !== prevFocusedElement && isSelectable(element) && shouldSelect) {\n      element.select();\n    }\n    if (isElement(element) && cleanup) {\n      element.removeAttribute(\"tabindex\");\n    }\n  }\n};\nfunction removeFromStack(list, item) {\n  const copy = [...list];\n  const idx = list.indexOf(item);\n  if (idx !== -1) {\n    copy.splice(idx, 1);\n  }\n  return copy;\n}\nconst createFocusableStack = () => {\n  let stack = [];\n  const push = layer => {\n    const currentLayer = stack[0];\n    if (currentLayer && layer !== currentLayer) {\n      currentLayer.pause();\n    }\n    stack = removeFromStack(stack, layer);\n    stack.unshift(layer);\n  };\n  const remove = layer => {\n    var _a, _b;\n    stack = removeFromStack(stack, layer);\n    (_b = (_a = stack[0]) == null ? void 0 : _a.resume) == null ? void 0 : _b.call(_a);\n  };\n  return {\n    push,\n    remove\n  };\n};\nconst focusFirstDescendant = (elements, shouldSelect = false) => {\n  const prevFocusedElement = document.activeElement;\n  for (const element of elements) {\n    tryFocus(element, shouldSelect);\n    if (document.activeElement !== prevFocusedElement) return;\n  }\n};\nconst focusableStack = createFocusableStack();\nconst isFocusCausedByUserEvent = () => {\n  return lastUserFocusTimestamp.value > lastAutomatedFocusTimestamp.value;\n};\nconst notifyFocusReasonPointer = () => {\n  focusReason.value = \"pointer\";\n  lastUserFocusTimestamp.value = window.performance.now();\n};\nconst notifyFocusReasonKeydown = () => {\n  focusReason.value = \"keyboard\";\n  lastUserFocusTimestamp.value = window.performance.now();\n};\nconst useFocusReason = () => {\n  onMounted(() => {\n    if (focusReasonUserCount === 0) {\n      document.addEventListener(\"mousedown\", notifyFocusReasonPointer);\n      document.addEventListener(\"touchstart\", notifyFocusReasonPointer);\n      document.addEventListener(\"keydown\", notifyFocusReasonKeydown);\n    }\n    focusReasonUserCount++;\n  });\n  onBeforeUnmount(() => {\n    focusReasonUserCount--;\n    if (focusReasonUserCount <= 0) {\n      document.removeEventListener(\"mousedown\", notifyFocusReasonPointer);\n      document.removeEventListener(\"touchstart\", notifyFocusReasonPointer);\n      document.removeEventListener(\"keydown\", notifyFocusReasonKeydown);\n    }\n  });\n  return {\n    focusReason,\n    lastUserFocusTimestamp,\n    lastAutomatedFocusTimestamp\n  };\n};\nconst createFocusOutPreventedEvent = detail => {\n  return new CustomEvent(FOCUSOUT_PREVENTED, {\n    ...FOCUSOUT_PREVENTED_OPTS,\n    detail\n  });\n};\nexport { createFocusOutPreventedEvent, focusFirstDescendant, focusableStack, getEdges, getVisibleElement, isFocusCausedByUserEvent, isHidden, obtainAllFocusableElements, tryFocus, useFocusReason };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}