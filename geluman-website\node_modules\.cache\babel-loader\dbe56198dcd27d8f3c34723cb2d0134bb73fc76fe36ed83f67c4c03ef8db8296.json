{"ast": null, "code": "import { createVNode, renderSlot, mergeProps } from 'vue';\nimport { get } from 'lodash-unified';\nimport { Alignment } from '../constants.mjs';\nimport { placeholderSign } from '../private.mjs';\nimport { enforceUnit, tryCall, componentToSlot } from '../utils.mjs';\nimport TableCell from '../components/cell.mjs';\nimport ExpandIcon from '../components/expand-icon.mjs';\nimport { isFunction, isObject } from '@vue/shared';\nconst CellRenderer = ({\n  columns,\n  column,\n  columnIndex,\n  depth,\n  expandIconProps,\n  isScrolling,\n  rowData,\n  rowIndex,\n  style,\n  expandedRowKeys,\n  ns,\n  cellProps: _cellProps,\n  expandColumnKey,\n  indentSize,\n  iconSize,\n  rowKey\n}, {\n  slots\n}) => {\n  const cellStyle = enforceUnit(style);\n  if (column.placeholderSign === placeholderSign) {\n    return createVNode(\"div\", {\n      \"class\": ns.em(\"row-cell\", \"placeholder\"),\n      \"style\": cellStyle\n    }, null);\n  }\n  const {\n    cellRenderer,\n    dataKey,\n    dataGetter\n  } = column;\n  const cellData = isFunction(dataGetter) ? dataGetter({\n    columns,\n    column,\n    columnIndex,\n    rowData,\n    rowIndex\n  }) : get(rowData, dataKey != null ? dataKey : \"\");\n  const extraCellProps = tryCall(_cellProps, {\n    cellData,\n    columns,\n    column,\n    columnIndex,\n    rowIndex,\n    rowData\n  });\n  const cellProps = {\n    class: ns.e(\"cell-text\"),\n    columns,\n    column,\n    columnIndex,\n    cellData,\n    isScrolling,\n    rowData,\n    rowIndex\n  };\n  const columnCellRenderer = componentToSlot(cellRenderer);\n  const Cell = columnCellRenderer ? columnCellRenderer(cellProps) : renderSlot(slots, \"default\", cellProps, () => [createVNode(TableCell, cellProps, null)]);\n  const kls = [ns.e(\"row-cell\"), column.class, column.align === Alignment.CENTER && ns.is(\"align-center\"), column.align === Alignment.RIGHT && ns.is(\"align-right\")];\n  const expandable = rowIndex >= 0 && expandColumnKey && column.key === expandColumnKey;\n  const expanded = rowIndex >= 0 && expandedRowKeys.includes(rowData[rowKey]);\n  let IconOrPlaceholder;\n  const iconStyle = `margin-inline-start: ${depth * indentSize}px;`;\n  if (expandable) {\n    if (isObject(expandIconProps)) {\n      IconOrPlaceholder = createVNode(ExpandIcon, mergeProps(expandIconProps, {\n        \"class\": [ns.e(\"expand-icon\"), ns.is(\"expanded\", expanded)],\n        \"size\": iconSize,\n        \"expanded\": expanded,\n        \"style\": iconStyle,\n        \"expandable\": true\n      }), null);\n    } else {\n      IconOrPlaceholder = createVNode(\"div\", {\n        \"style\": [iconStyle, `width: ${iconSize}px; height: ${iconSize}px;`].join(\" \")\n      }, null);\n    }\n  }\n  return createVNode(\"div\", mergeProps({\n    \"class\": kls,\n    \"style\": cellStyle\n  }, extraCellProps, {\n    \"role\": \"cell\"\n  }), [IconOrPlaceholder, Cell]);\n};\nCellRenderer.inheritAttrs = false;\nvar Cell = CellRenderer;\nexport { Cell as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}