{"ast": null, "code": "import Container from './src/container.mjs';\nimport Aside from './src/aside.mjs';\nimport Footer from './src/footer.mjs';\nimport Header from './src/header.mjs';\nimport Main from './src/main.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElContainer = withInstall(Container, {\n  Aside,\n  Footer,\n  Header,\n  Main\n});\nconst ElAside = withNoopInstall(Aside);\nconst ElFooter = withNoopInstall(Footer);\nconst ElHeader = withNoopInstall(Header);\nconst ElMain = withNoopInstall(Main);\nexport { ElAside, ElContainer, ElFooter, ElHeader, ElMain, ElContainer as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}