{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, createBlock, withCtx, resolveDynamicComponent, createCommentVNode, renderSlot } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { linkProps, linkEmits } from './link.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useDeprecated } from '../../../hooks/use-deprecated/index.mjs';\nimport { isBoolean } from '../../../utils/types.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElLink\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: linkProps,\n  emits: linkEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    useDeprecated({\n      scope: \"el-link\",\n      from: \"The underline option (boolean)\",\n      replacement: \"'always' | 'hover' | 'never'\",\n      version: \"3.0.0\",\n      ref: \"https://element-plus.org/en-US/component/link.html#underline\"\n    }, computed(() => isBoolean(props.underline)));\n    const ns = useNamespace(\"link\");\n    const linkKls = computed(() => [ns.b(), ns.m(props.type), ns.is(\"disabled\", props.disabled), ns.is(\"underline\", underline.value === \"always\"), ns.is(\"hover-underline\", underline.value === \"hover\" && !props.disabled)]);\n    const underline = computed(() => {\n      if (isBoolean(props.underline)) {\n        return props.underline ? \"hover\" : \"never\";\n      } else return props.underline;\n    });\n    function handleClick(event) {\n      if (!props.disabled) emit(\"click\", event);\n    }\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"a\", {\n        class: normalizeClass(unref(linkKls)),\n        href: _ctx.disabled || !_ctx.href ? void 0 : _ctx.href,\n        target: _ctx.disabled || !_ctx.href ? void 0 : _ctx.target,\n        onClick: handleClick\n      }, [_ctx.icon ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.icon)))]),\n        _: 1\n      })) : createCommentVNode(\"v-if\", true), _ctx.$slots.default ? (openBlock(), createElementBlock(\"span\", {\n        key: 1,\n        class: normalizeClass(unref(ns).e(\"inner\"))\n      }, [renderSlot(_ctx.$slots, \"default\")], 2)) : createCommentVNode(\"v-if\", true), _ctx.$slots.icon ? renderSlot(_ctx.$slots, \"icon\", {\n        key: 2\n      }) : createCommentVNode(\"v-if\", true)], 10, [\"href\", \"target\"]);\n    };\n  }\n});\nvar Link = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"link.vue\"]]);\nexport { Link as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}