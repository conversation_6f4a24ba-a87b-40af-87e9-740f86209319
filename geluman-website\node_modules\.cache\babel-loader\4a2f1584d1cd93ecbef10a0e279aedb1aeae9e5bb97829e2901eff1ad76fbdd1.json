{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, ref, computed, watch, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, createBlock, withCtx, resolveDynamicComponent, renderSlot } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { avatarProps, avatarEmits } from './avatar2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isString } from '@vue/shared';\nimport { isNumber } from '../../../utils/types.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nconst __default__ = defineComponent({\n  name: \"ElAvatar\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: avatarProps,\n  emits: avatarEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"avatar\");\n    const hasLoadError = ref(false);\n    const avatarClass = computed(() => {\n      const {\n        size,\n        icon,\n        shape\n      } = props;\n      const classList = [ns.b()];\n      if (isString(size)) classList.push(ns.m(size));\n      if (icon) classList.push(ns.m(\"icon\"));\n      if (shape) classList.push(ns.m(shape));\n      return classList;\n    });\n    const sizeStyle = computed(() => {\n      const {\n        size\n      } = props;\n      return isNumber(size) ? ns.cssVarBlock({\n        size: addUnit(size) || \"\"\n      }) : void 0;\n    });\n    const fitStyle = computed(() => ({\n      objectFit: props.fit\n    }));\n    watch(() => props.src, () => hasLoadError.value = false);\n    function handleError(e) {\n      hasLoadError.value = true;\n      emit(\"error\", e);\n    }\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"span\", {\n        class: normalizeClass(unref(avatarClass)),\n        style: normalizeStyle(unref(sizeStyle))\n      }, [(_ctx.src || _ctx.srcSet) && !hasLoadError.value ? (openBlock(), createElementBlock(\"img\", {\n        key: 0,\n        src: _ctx.src,\n        alt: _ctx.alt,\n        srcset: _ctx.srcSet,\n        style: normalizeStyle(unref(fitStyle)),\n        onError: handleError\n      }, null, 44, [\"src\", \"alt\", \"srcset\"])) : _ctx.icon ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 1\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.icon)))]),\n        _: 1\n      })) : renderSlot(_ctx.$slots, \"default\", {\n        key: 2\n      })], 6);\n    };\n  }\n});\nvar Avatar = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"avatar.vue\"]]);\nexport { Avatar as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}