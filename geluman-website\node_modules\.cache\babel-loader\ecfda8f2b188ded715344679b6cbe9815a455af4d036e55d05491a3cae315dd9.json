{"ast": null, "code": "import { ref } from 'vue';\nimport { isNumber } from '../../../../utils/types.mjs';\nconst useScrollbar = () => {\n  const scrollBarRef = ref();\n  const scrollTo = (options, yCoord) => {\n    const scrollbar = scrollBarRef.value;\n    if (scrollbar) {\n      scrollbar.scrollTo(options, yCoord);\n    }\n  };\n  const setScrollPosition = (position, offset) => {\n    const scrollbar = scrollBarRef.value;\n    if (scrollbar && isNumber(offset) && [\"Top\", \"Left\"].includes(position)) {\n      scrollbar[`setScroll${position}`](offset);\n    }\n  };\n  const setScrollTop = top => setScrollPosition(\"Top\", top);\n  const setScrollLeft = left => setScrollPosition(\"Left\", left);\n  return {\n    scrollBarRef,\n    scrollTo,\n    setScrollTop,\n    setScrollLeft\n  };\n};\nexport { useScrollbar };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}