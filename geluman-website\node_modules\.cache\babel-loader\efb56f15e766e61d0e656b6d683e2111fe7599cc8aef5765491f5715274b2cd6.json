{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, normalizeClass, unref, renderSlot } from 'vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElMain\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  setup(__props) {\n    const ns = useNamespace(\"main\");\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"main\", {\n        class: normalizeClass(unref(ns).b())\n      }, [renderSlot(_ctx.$slots, \"default\")], 2);\n    };\n  }\n});\nvar Main = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"main.vue\"]]);\nexport { Main as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}