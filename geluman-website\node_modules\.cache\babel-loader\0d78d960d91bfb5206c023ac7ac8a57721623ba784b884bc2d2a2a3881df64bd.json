{"ast": null, "code": "import { defineComponent, inject, ref, computed, onBeforeUnmount, toRef, openBlock, createBlock, Transition, unref, withCtx, withDirectives, createElementVNode, normalizeClass, withModifiers, normalizeStyle, vShow } from 'vue';\nimport { useEventListener, isClient } from '@vueuse/core';\nimport { scrollbarContextKey } from './constants.mjs';\nimport { BAR_MAP, renderThumbStyle } from './util.mjs';\nimport { thumbProps } from './thumb.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nconst COMPONENT_NAME = \"Thumb\";\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"thumb\",\n  props: thumbProps,\n  setup(__props) {\n    const props = __props;\n    const scrollbar = inject(scrollbarContextKey);\n    const ns = useNamespace(\"scrollbar\");\n    if (!scrollbar) throwError(COMPONENT_NAME, \"can not inject scrollbar context\");\n    const instance = ref();\n    const thumb = ref();\n    const thumbState = ref({});\n    const visible = ref(false);\n    let cursorDown = false;\n    let cursorLeave = false;\n    let originalOnSelectStart = isClient ? document.onselectstart : null;\n    const bar = computed(() => BAR_MAP[props.vertical ? \"vertical\" : \"horizontal\"]);\n    const thumbStyle = computed(() => renderThumbStyle({\n      size: props.size,\n      move: props.move,\n      bar: bar.value\n    }));\n    const offsetRatio = computed(() => instance.value[bar.value.offset] ** 2 / scrollbar.wrapElement[bar.value.scrollSize] / props.ratio / thumb.value[bar.value.offset]);\n    const clickThumbHandler = e => {\n      var _a;\n      e.stopPropagation();\n      if (e.ctrlKey || [1, 2].includes(e.button)) return;\n      (_a = window.getSelection()) == null ? void 0 : _a.removeAllRanges();\n      startDrag(e);\n      const el = e.currentTarget;\n      if (!el) return;\n      thumbState.value[bar.value.axis] = el[bar.value.offset] - (e[bar.value.client] - el.getBoundingClientRect()[bar.value.direction]);\n    };\n    const clickTrackHandler = e => {\n      if (!thumb.value || !instance.value || !scrollbar.wrapElement) return;\n      const offset = Math.abs(e.target.getBoundingClientRect()[bar.value.direction] - e[bar.value.client]);\n      const thumbHalf = thumb.value[bar.value.offset] / 2;\n      const thumbPositionPercentage = (offset - thumbHalf) * 100 * offsetRatio.value / instance.value[bar.value.offset];\n      scrollbar.wrapElement[bar.value.scroll] = thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize] / 100;\n    };\n    const startDrag = e => {\n      e.stopImmediatePropagation();\n      cursorDown = true;\n      document.addEventListener(\"mousemove\", mouseMoveDocumentHandler);\n      document.addEventListener(\"mouseup\", mouseUpDocumentHandler);\n      originalOnSelectStart = document.onselectstart;\n      document.onselectstart = () => false;\n    };\n    const mouseMoveDocumentHandler = e => {\n      if (!instance.value || !thumb.value) return;\n      if (cursorDown === false) return;\n      const prevPage = thumbState.value[bar.value.axis];\n      if (!prevPage) return;\n      const offset = (instance.value.getBoundingClientRect()[bar.value.direction] - e[bar.value.client]) * -1;\n      const thumbClickPosition = thumb.value[bar.value.offset] - prevPage;\n      const thumbPositionPercentage = (offset - thumbClickPosition) * 100 * offsetRatio.value / instance.value[bar.value.offset];\n      scrollbar.wrapElement[bar.value.scroll] = thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize] / 100;\n    };\n    const mouseUpDocumentHandler = () => {\n      cursorDown = false;\n      thumbState.value[bar.value.axis] = 0;\n      document.removeEventListener(\"mousemove\", mouseMoveDocumentHandler);\n      document.removeEventListener(\"mouseup\", mouseUpDocumentHandler);\n      restoreOnselectstart();\n      if (cursorLeave) visible.value = false;\n    };\n    const mouseMoveScrollbarHandler = () => {\n      cursorLeave = false;\n      visible.value = !!props.size;\n    };\n    const mouseLeaveScrollbarHandler = () => {\n      cursorLeave = true;\n      visible.value = cursorDown;\n    };\n    onBeforeUnmount(() => {\n      restoreOnselectstart();\n      document.removeEventListener(\"mouseup\", mouseUpDocumentHandler);\n    });\n    const restoreOnselectstart = () => {\n      if (document.onselectstart !== originalOnSelectStart) document.onselectstart = originalOnSelectStart;\n    };\n    useEventListener(toRef(scrollbar, \"scrollbarElement\"), \"mousemove\", mouseMoveScrollbarHandler);\n    useEventListener(toRef(scrollbar, \"scrollbarElement\"), \"mouseleave\", mouseLeaveScrollbarHandler);\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(Transition, {\n        name: unref(ns).b(\"fade\"),\n        persisted: \"\"\n      }, {\n        default: withCtx(() => [withDirectives(createElementVNode(\"div\", {\n          ref_key: \"instance\",\n          ref: instance,\n          class: normalizeClass([unref(ns).e(\"bar\"), unref(ns).is(unref(bar).key)]),\n          onMousedown: clickTrackHandler,\n          onClick: withModifiers(() => {}, [\"stop\"])\n        }, [createElementVNode(\"div\", {\n          ref_key: \"thumb\",\n          ref: thumb,\n          class: normalizeClass(unref(ns).e(\"thumb\")),\n          style: normalizeStyle(unref(thumbStyle)),\n          onMousedown: clickThumbHandler\n        }, null, 38)], 42, [\"onClick\"]), [[vShow, _ctx.always || visible.value]])]),\n        _: 1\n      }, 8, [\"name\"]);\n    };\n  }\n});\nvar Thumb = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"thumb.vue\"]]);\nexport { Thumb as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}