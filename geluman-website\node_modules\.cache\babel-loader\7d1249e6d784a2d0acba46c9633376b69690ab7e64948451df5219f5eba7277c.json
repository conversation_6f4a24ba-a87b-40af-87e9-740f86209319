{"ast": null, "code": "import { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nimport { datePickTypes } from '../../../../constants/date.mjs';\nimport { isArray } from '@vue/shared';\nconst selectionModes = [\"date\", \"dates\", \"year\", \"years\", \"month\", \"months\", \"week\", \"range\"];\nconst datePickerSharedProps = buildProps({\n  disabledDate: {\n    type: definePropType(Function)\n  },\n  date: {\n    type: definePropType(Object),\n    required: true\n  },\n  minDate: {\n    type: definePropType(Object)\n  },\n  maxDate: {\n    type: definePropType(Object)\n  },\n  parsedValue: {\n    type: definePropType([Object, Array])\n  },\n  rangeState: {\n    type: definePropType(Object),\n    default: () => ({\n      endDate: null,\n      selecting: false\n    })\n  }\n});\nconst panelSharedProps = buildProps({\n  type: {\n    type: definePropType(String),\n    required: true,\n    values: datePickTypes\n  },\n  dateFormat: String,\n  timeFormat: String,\n  showNow: {\n    type: Boolean,\n    default: true\n  }\n});\nconst panelRangeSharedProps = buildProps({\n  unlinkPanels: Boolean,\n  parsedValue: {\n    type: definePropType(Array)\n  }\n});\nconst selectionModeWithDefault = mode => {\n  return {\n    type: String,\n    values: selectionModes,\n    default: mode\n  };\n};\nconst rangePickerSharedEmits = {\n  pick: range => isArray(range)\n};\nexport { datePickerSharedProps, panelRangeSharedProps, panelSharedProps, rangePickerSharedEmits, selectionModeWithDefault };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}