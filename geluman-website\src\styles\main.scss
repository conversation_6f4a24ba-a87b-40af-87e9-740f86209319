// Reset & Base styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "微软雅黑", system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-primary);
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes twinkle {
  0%,
  100% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
}

@keyframes shoot {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100vw);
    opacity: 0;
  }
}

// Common Components
.section-header {
  text-align: center;
  margin-bottom: 3rem;

  h2 {
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
  }

  p {
    color: var(--text-secondary);
    font-size: 1.1rem;
  }
}

// Product Cards
.product-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  height: 100%;

  .product-image {
    flex: 1;
    padding: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      max-width: 100%;
      height: auto;
      object-fit: contain;
    }
  }

  .product-info {
    flex: 1;
    padding: 2rem;
    display: flex;
    flex-direction: column;

    h3 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    p {
      color: var(--text-secondary);
      margin-bottom: 1.5rem;
    }

    .features {
      list-style: none;
      margin-bottom: 2rem;

      li {
        margin-bottom: 0.5rem;
        padding-left: 1.5rem;
        position: relative;

        &::before {
          content: "•";
          color: var(--primary-color);
          position: absolute;
          left: 0;
        }
      }
    }
  }
}
