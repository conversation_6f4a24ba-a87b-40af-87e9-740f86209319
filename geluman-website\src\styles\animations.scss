// 淡入上升
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 淡入
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 缩放淡入
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 滑入
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 弹跳
@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

// 闪烁
@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 旋转
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 波纹
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

// 动画类
.animate {
  &-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
  }

  &-fadeIn {
    animation: fadeIn 0.6s ease-out;
  }

  &-scaleIn {
    animation: scaleIn 0.6s ease-out;
  }

  &-slideIn {
    animation: slideIn 0.6s ease-out;
  }

  &-bounce {
    animation: bounce 1s ease-in-out;
  }

  &-blink {
    animation: blink 1.5s infinite;
  }

  &-rotate {
    animation: rotate 2s linear infinite;
  }

  &-ripple {
    animation: ripple 1s cubic-bezier(0, 0, 0.2, 1) infinite;
  }
}

// 动画延迟类
.delay {
  @for $i from 1 through 10 {
    &-#{$i} {
      animation-delay: #{$i * 0.1}s;
    }
  }
}
