{"ast": null, "code": "import { createVNode as _createVNode, resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, withCtx as _withCtx, resolveDynamicComponent as _resolveDynamicComponent, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nimport { ref, onMounted, onUnmounted } from \"vue\";\nimport NavHeader from \"@/components/layout/NavHeader.vue\";\nimport SiteFooter from \"@/components/layout/SiteFooter.vue\";\nexport default {\n  __name: 'App',\n  setup(__props) {\n    const isScrolled = ref(false);\n    const handleScroll = () => {\n      isScrolled.value = window.scrollY > 50;\n    };\n    onMounted(() => {\n      window.addEventListener(\"scroll\", handleScroll);\n    });\n    onUnmounted(() => {\n      window.removeEventListener(\"scroll\", handleScroll);\n    });\n    return (_ctx, _cache) => {\n      const _component_el_header = _resolveComponent(\"el-header\");\n      const _component_router_view = _resolveComponent(\"router-view\");\n      const _component_el_main = _resolveComponent(\"el-main\");\n      const _component_el_footer = _resolveComponent(\"el-footer\");\n      const _component_el_container = _resolveComponent(\"el-container\");\n      return _openBlock(), _createBlock(_component_el_container, {\n        class: \"layout-container\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_header, {\n          class: _normalizeClass([\"header\", {\n            'header-scrolled': isScrolled.value\n          }])\n        }, {\n          default: _withCtx(() => [_createVNode(NavHeader)]),\n          _: 1\n        }, 8, [\"class\"]), _createVNode(_component_el_main, null, {\n          default: _withCtx(() => [_createVNode(_component_router_view, null, {\n            default: _withCtx(({\n              Component\n            }) => [(_openBlock(), _createBlock(_resolveDynamicComponent(Component)))]),\n            _: 1\n          })]),\n          _: 1\n        }), _createVNode(_component_el_footer, null, {\n          default: _withCtx(() => [_createVNode(SiteFooter)]),\n          _: 1\n        })]),\n        _: 1\n      });\n    };\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}