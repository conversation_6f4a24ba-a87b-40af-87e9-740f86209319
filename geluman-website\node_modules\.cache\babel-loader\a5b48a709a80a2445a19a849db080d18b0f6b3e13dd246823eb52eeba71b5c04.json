{"ast": null, "code": "import { defineComponent, getCurrentInstance, inject, ref, computed, resolveComponent, openBlock, createBlock, normalizeClass, withCtx, createElementBlock, Fragment, renderList, createVNode, createTextVNode, toDisplayString, renderSlot, createCommentVNode } from 'vue';\nimport { ElScrollbar } from '../../scrollbar/index.mjs';\nimport { Loading } from '@element-plus/icons-vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport ElCascaderNode from './node2.mjs';\nimport { CASCADER_PANEL_INJECTION_KEY } from './types.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElCascaderMenu\",\n  components: {\n    Loading,\n    ElIcon,\n    ElScrollbar,\n    ElCascaderNode\n  },\n  props: {\n    nodes: {\n      type: Array,\n      required: true\n    },\n    index: {\n      type: Number,\n      required: true\n    }\n  },\n  setup(props) {\n    const instance = getCurrentInstance();\n    const ns = useNamespace(\"cascader-menu\");\n    const {\n      t\n    } = useLocale();\n    const id = useId();\n    let activeNode = null;\n    let hoverTimer = null;\n    const panel = inject(CASCADER_PANEL_INJECTION_KEY);\n    const hoverZone = ref(null);\n    const isEmpty = computed(() => !props.nodes.length);\n    const isLoading = computed(() => !panel.initialLoaded);\n    const menuId = computed(() => `${id.value}-${props.index}`);\n    const handleExpand = e => {\n      activeNode = e.target;\n    };\n    const handleMouseMove = e => {\n      if (!panel.isHoverMenu || !activeNode || !hoverZone.value) return;\n      if (activeNode.contains(e.target)) {\n        clearHoverTimer();\n        const el = instance.vnode.el;\n        const {\n          left\n        } = el.getBoundingClientRect();\n        const {\n          offsetWidth,\n          offsetHeight\n        } = el;\n        const startX = e.clientX - left;\n        const top = activeNode.offsetTop;\n        const bottom = top + activeNode.offsetHeight;\n        hoverZone.value.innerHTML = `\n          <path style=\"pointer-events: auto;\" fill=\"transparent\" d=\"M${startX} ${top} L${offsetWidth} 0 V${top} Z\" />\n          <path style=\"pointer-events: auto;\" fill=\"transparent\" d=\"M${startX} ${bottom} L${offsetWidth} ${offsetHeight} V${bottom} Z\" />\n        `;\n      } else if (!hoverTimer) {\n        hoverTimer = window.setTimeout(clearHoverZone, panel.config.hoverThreshold);\n      }\n    };\n    const clearHoverTimer = () => {\n      if (!hoverTimer) return;\n      clearTimeout(hoverTimer);\n      hoverTimer = null;\n    };\n    const clearHoverZone = () => {\n      if (!hoverZone.value) return;\n      hoverZone.value.innerHTML = \"\";\n      clearHoverTimer();\n    };\n    return {\n      ns,\n      panel,\n      hoverZone,\n      isEmpty,\n      isLoading,\n      menuId,\n      t,\n      handleExpand,\n      handleMouseMove,\n      clearHoverZone\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_cascader_node = resolveComponent(\"el-cascader-node\");\n  const _component_loading = resolveComponent(\"loading\");\n  const _component_el_icon = resolveComponent(\"el-icon\");\n  const _component_el_scrollbar = resolveComponent(\"el-scrollbar\");\n  return openBlock(), createBlock(_component_el_scrollbar, {\n    key: _ctx.menuId,\n    tag: \"ul\",\n    role: \"menu\",\n    class: normalizeClass(_ctx.ns.b()),\n    \"wrap-class\": _ctx.ns.e(\"wrap\"),\n    \"view-class\": [_ctx.ns.e(\"list\"), _ctx.ns.is(\"empty\", _ctx.isEmpty)],\n    onMousemove: _ctx.handleMouseMove,\n    onMouseleave: _ctx.clearHoverZone\n  }, {\n    default: withCtx(() => {\n      var _a;\n      return [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.nodes, node => {\n        return openBlock(), createBlock(_component_el_cascader_node, {\n          key: node.uid,\n          node,\n          \"menu-id\": _ctx.menuId,\n          onExpand: _ctx.handleExpand\n        }, null, 8, [\"node\", \"menu-id\", \"onExpand\"]);\n      }), 128)), _ctx.isLoading ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(_ctx.ns.e(\"empty-text\"))\n      }, [createVNode(_component_el_icon, {\n        size: \"14\",\n        class: normalizeClass(_ctx.ns.is(\"loading\"))\n      }, {\n        default: withCtx(() => [createVNode(_component_loading)]),\n        _: 1\n      }, 8, [\"class\"]), createTextVNode(\" \" + toDisplayString(_ctx.t(\"el.cascader.loading\")), 1)], 2)) : _ctx.isEmpty ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(_ctx.ns.e(\"empty-text\"))\n      }, [renderSlot(_ctx.$slots, \"empty\", {}, () => [createTextVNode(toDisplayString(_ctx.t(\"el.cascader.noData\")), 1)])], 2)) : ((_a = _ctx.panel) == null ? void 0 : _a.isHoverMenu) ? (openBlock(), createElementBlock(Fragment, {\n        key: 2\n      }, [createCommentVNode(\" eslint-disable-next-line vue/html-self-closing \"), (openBlock(), createElementBlock(\"svg\", {\n        ref: \"hoverZone\",\n        class: normalizeClass(_ctx.ns.e(\"hover-zone\"))\n      }, null, 2))], 2112)) : createCommentVNode(\"v-if\", true)];\n    }),\n    _: 3\n  }, 8, [\"class\", \"wrap-class\", \"view-class\", \"onMousemove\", \"onMouseleave\"]);\n}\nvar ElCascaderMenu = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"menu.vue\"]]);\nexport { ElCascaderMenu as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}