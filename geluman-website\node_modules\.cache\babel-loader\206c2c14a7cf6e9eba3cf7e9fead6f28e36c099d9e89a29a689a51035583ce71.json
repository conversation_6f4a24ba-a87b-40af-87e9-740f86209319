{"ast": null, "code": "import { defineComponent, getCurrentInstance, nextTick, watch } from 'vue';\nimport { ElOption } from '../../select/index.mjs';\nconst component = defineComponent({\n  extends: ElOption,\n  setup(props, ctx) {\n    const result = ElOption.setup(props, ctx);\n    delete result.selectOptionClick;\n    const vm = getCurrentInstance().proxy;\n    nextTick(() => {\n      if (!result.select.states.cachedOptions.get(vm.value)) {\n        result.select.onOptionCreate(vm);\n      }\n    });\n    watch(() => ctx.attrs.visible, val => {\n      nextTick(() => {\n        result.states.visible = val;\n      });\n    }, {\n      immediate: true\n    });\n    return result;\n  },\n  methods: {\n    selectOptionClick() {\n      this.$el.parentElement.click();\n    }\n  }\n});\nexport { component as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}