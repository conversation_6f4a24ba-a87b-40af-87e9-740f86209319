{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { ref, computed, unref, watch, nextTick } from 'vue';\nimport dayjs from 'dayjs';\nimport { flatten } from 'lodash-unified';\nimport { buildPickerTable } from '../utils.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { castArray } from '../../../../utils/arrays.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isArray } from '@vue/shared';\nconst isNormalDay = (type = \"\") => {\n  return [\"normal\", \"today\"].includes(type);\n};\nconst useBasicDateTable = (props, emit) => {\n  const {\n    lang\n  } = useLocale();\n  const tbodyRef = ref();\n  const currentCellRef = ref();\n  const lastRow = ref();\n  const lastColumn = ref();\n  const tableRows = ref([[], [], [], [], [], []]);\n  let focusWithClick = false;\n  const firstDayOfWeek = props.date.$locale().weekStart || 7;\n  const WEEKS_CONSTANT = props.date.locale(\"en\").localeData().weekdaysShort().map(_ => _.toLowerCase());\n  const offsetDay = computed(() => {\n    return firstDayOfWeek > 3 ? 7 - firstDayOfWeek : -firstDayOfWeek;\n  });\n  const startDate = computed(() => {\n    const startDayOfMonth = props.date.startOf(\"month\");\n    return startDayOfMonth.subtract(startDayOfMonth.day() || 7, \"day\");\n  });\n  const WEEKS = computed(() => {\n    return WEEKS_CONSTANT.concat(WEEKS_CONSTANT).slice(firstDayOfWeek, firstDayOfWeek + 7);\n  });\n  const hasCurrent = computed(() => {\n    return flatten(unref(rows)).some(row => {\n      return row.isCurrent;\n    });\n  });\n  const days = computed(() => {\n    const startOfMonth = props.date.startOf(\"month\");\n    const startOfMonthDay = startOfMonth.day() || 7;\n    const dateCountOfMonth = startOfMonth.daysInMonth();\n    const dateCountOfLastMonth = startOfMonth.subtract(1, \"month\").daysInMonth();\n    return {\n      startOfMonthDay,\n      dateCountOfMonth,\n      dateCountOfLastMonth\n    };\n  });\n  const selectedDate = computed(() => {\n    return props.selectionMode === \"dates\" ? castArray(props.parsedValue) : [];\n  });\n  const setDateText = (cell, {\n    count,\n    rowIndex,\n    columnIndex\n  }) => {\n    const {\n      startOfMonthDay,\n      dateCountOfMonth,\n      dateCountOfLastMonth\n    } = unref(days);\n    const offset = unref(offsetDay);\n    if (rowIndex >= 0 && rowIndex <= 1) {\n      const numberOfDaysFromPreviousMonth = startOfMonthDay + offset < 0 ? 7 + startOfMonthDay + offset : startOfMonthDay + offset;\n      if (columnIndex + rowIndex * 7 >= numberOfDaysFromPreviousMonth) {\n        cell.text = count;\n        return true;\n      } else {\n        cell.text = dateCountOfLastMonth - (numberOfDaysFromPreviousMonth - columnIndex % 7) + 1 + rowIndex * 7;\n        cell.type = \"prev-month\";\n      }\n    } else {\n      if (count <= dateCountOfMonth) {\n        cell.text = count;\n      } else {\n        cell.text = count - dateCountOfMonth;\n        cell.type = \"next-month\";\n      }\n      return true;\n    }\n    return false;\n  };\n  const setCellMetadata = (cell, {\n    columnIndex,\n    rowIndex\n  }, count) => {\n    const {\n      disabledDate,\n      cellClassName\n    } = props;\n    const _selectedDate = unref(selectedDate);\n    const shouldIncrement = setDateText(cell, {\n      count,\n      rowIndex,\n      columnIndex\n    });\n    const cellDate = cell.dayjs.toDate();\n    cell.selected = _selectedDate.find(d => d.isSame(cell.dayjs, \"day\"));\n    cell.isSelected = !!cell.selected;\n    cell.isCurrent = isCurrent(cell);\n    cell.disabled = disabledDate == null ? void 0 : disabledDate(cellDate);\n    cell.customClass = cellClassName == null ? void 0 : cellClassName(cellDate);\n    return shouldIncrement;\n  };\n  const setRowMetadata = row => {\n    if (props.selectionMode === \"week\") {\n      const [start, end] = props.showWeekNumber ? [1, 7] : [0, 6];\n      const isActive = isWeekActive(row[start + 1]);\n      row[start].inRange = isActive;\n      row[start].start = isActive;\n      row[end].inRange = isActive;\n      row[end].end = isActive;\n    }\n  };\n  const rows = computed(() => {\n    const {\n      minDate,\n      maxDate,\n      rangeState,\n      showWeekNumber\n    } = props;\n    const offset = unref(offsetDay);\n    const rows_ = unref(tableRows);\n    const dateUnit = \"day\";\n    let count = 1;\n    if (showWeekNumber) {\n      for (let rowIndex = 0; rowIndex < 6; rowIndex++) {\n        if (!rows_[rowIndex][0]) {\n          rows_[rowIndex][0] = {\n            type: \"week\",\n            text: unref(startDate).add(rowIndex * 7 + 1, dateUnit).week()\n          };\n        }\n      }\n    }\n    buildPickerTable({\n      row: 6,\n      column: 7\n    }, rows_, {\n      startDate: minDate,\n      columnIndexOffset: showWeekNumber ? 1 : 0,\n      nextEndDate: rangeState.endDate || maxDate || rangeState.selecting && minDate || null,\n      now: dayjs().locale(unref(lang)).startOf(dateUnit),\n      unit: dateUnit,\n      relativeDateGetter: idx => unref(startDate).add(idx - offset, dateUnit),\n      setCellMetadata: (...args) => {\n        if (setCellMetadata(...args, count)) {\n          count += 1;\n        }\n      },\n      setRowMetadata\n    });\n    return rows_;\n  });\n  watch(() => props.date, async () => {\n    var _a;\n    if ((_a = unref(tbodyRef)) == null ? void 0 : _a.contains(document.activeElement)) {\n      await nextTick();\n      await focus();\n    }\n  });\n  const focus = async () => {\n    var _a;\n    return (_a = unref(currentCellRef)) == null ? void 0 : _a.focus();\n  };\n  const isCurrent = cell => {\n    return props.selectionMode === \"date\" && isNormalDay(cell.type) && cellMatchesDate(cell, props.parsedValue);\n  };\n  const cellMatchesDate = (cell, date) => {\n    if (!date) return false;\n    return dayjs(date).locale(unref(lang)).isSame(props.date.date(Number(cell.text)), \"day\");\n  };\n  const getDateOfCell = (row, column) => {\n    const offsetFromStart = row * 7 + (column - (props.showWeekNumber ? 1 : 0)) - unref(offsetDay);\n    return unref(startDate).add(offsetFromStart, \"day\");\n  };\n  const handleMouseMove = event => {\n    var _a;\n    if (!props.rangeState.selecting) return;\n    let target = event.target;\n    if (target.tagName === \"SPAN\") {\n      target = (_a = target.parentNode) == null ? void 0 : _a.parentNode;\n    }\n    if (target.tagName === \"DIV\") {\n      target = target.parentNode;\n    }\n    if (target.tagName !== \"TD\") return;\n    const row = target.parentNode.rowIndex - 1;\n    const column = target.cellIndex;\n    if (unref(rows)[row][column].disabled) return;\n    if (row !== unref(lastRow) || column !== unref(lastColumn)) {\n      lastRow.value = row;\n      lastColumn.value = column;\n      emit(\"changerange\", {\n        selecting: true,\n        endDate: getDateOfCell(row, column)\n      });\n    }\n  };\n  const isSelectedCell = cell => {\n    return !unref(hasCurrent) && (cell == null ? void 0 : cell.text) === 1 && cell.type === \"normal\" || cell.isCurrent;\n  };\n  const handleFocus = event => {\n    if (focusWithClick || unref(hasCurrent) || props.selectionMode !== \"date\") return;\n    handlePickDate(event, true);\n  };\n  const handleMouseDown = event => {\n    const target = event.target.closest(\"td\");\n    if (!target) return;\n    focusWithClick = true;\n  };\n  const handleMouseUp = event => {\n    const target = event.target.closest(\"td\");\n    if (!target) return;\n    focusWithClick = false;\n  };\n  const handleRangePick = newDate => {\n    if (!props.rangeState.selecting || !props.minDate) {\n      emit(\"pick\", {\n        minDate: newDate,\n        maxDate: null\n      });\n      emit(\"select\", true);\n    } else {\n      if (newDate >= props.minDate) {\n        emit(\"pick\", {\n          minDate: props.minDate,\n          maxDate: newDate\n        });\n      } else {\n        emit(\"pick\", {\n          minDate: newDate,\n          maxDate: props.minDate\n        });\n      }\n      emit(\"select\", false);\n    }\n  };\n  const handleWeekPick = newDate => {\n    const weekNumber = newDate.week();\n    const value = `${newDate.year()}w${weekNumber}`;\n    emit(\"pick\", {\n      year: newDate.year(),\n      week: weekNumber,\n      value,\n      date: newDate.startOf(\"week\")\n    });\n  };\n  const handleDatesPick = (newDate, selected) => {\n    const newValue = selected ? castArray(props.parsedValue).filter(d => (d == null ? void 0 : d.valueOf()) !== newDate.valueOf()) : castArray(props.parsedValue).concat([newDate]);\n    emit(\"pick\", newValue);\n  };\n  const handlePickDate = (event, isKeyboardMovement = false) => {\n    const target = event.target.closest(\"td\");\n    if (!target) return;\n    const row = target.parentNode.rowIndex - 1;\n    const column = target.cellIndex;\n    const cell = unref(rows)[row][column];\n    if (cell.disabled || cell.type === \"week\") return;\n    const newDate = getDateOfCell(row, column);\n    switch (props.selectionMode) {\n      case \"range\":\n        {\n          handleRangePick(newDate);\n          break;\n        }\n      case \"date\":\n        {\n          emit(\"pick\", newDate, isKeyboardMovement);\n          break;\n        }\n      case \"week\":\n        {\n          handleWeekPick(newDate);\n          break;\n        }\n      case \"dates\":\n        {\n          handleDatesPick(newDate, !!cell.selected);\n          break;\n        }\n    }\n  };\n  const isWeekActive = cell => {\n    if (props.selectionMode !== \"week\") return false;\n    let newDate = props.date.startOf(\"day\");\n    if (cell.type === \"prev-month\") {\n      newDate = newDate.subtract(1, \"month\");\n    }\n    if (cell.type === \"next-month\") {\n      newDate = newDate.add(1, \"month\");\n    }\n    newDate = newDate.date(Number.parseInt(cell.text, 10));\n    if (props.parsedValue && !isArray(props.parsedValue)) {\n      const dayOffset = (props.parsedValue.day() - firstDayOfWeek + 7) % 7 - 1;\n      const weekDate = props.parsedValue.subtract(dayOffset, \"day\");\n      return weekDate.isSame(newDate, \"day\");\n    }\n    return false;\n  };\n  return {\n    WEEKS,\n    rows,\n    tbodyRef,\n    currentCellRef,\n    focus,\n    isCurrent,\n    isWeekActive,\n    isSelectedCell,\n    handlePickDate,\n    handleMouseUp,\n    handleMouseDown,\n    handleMouseMove,\n    handleFocus\n  };\n};\nconst useBasicDateTableDOM = (props, {\n  isCurrent,\n  isWeekActive\n}) => {\n  const ns = useNamespace(\"date-table\");\n  const {\n    t\n  } = useLocale();\n  const tableKls = computed(() => [ns.b(), {\n    \"is-week-mode\": props.selectionMode === \"week\"\n  }]);\n  const tableLabel = computed(() => t(\"el.datepicker.dateTablePrompt\"));\n  const weekLabel = computed(() => t(\"el.datepicker.week\"));\n  const getCellClasses = cell => {\n    const classes = [];\n    if (isNormalDay(cell.type) && !cell.disabled) {\n      classes.push(\"available\");\n      if (cell.type === \"today\") {\n        classes.push(\"today\");\n      }\n    } else {\n      classes.push(cell.type);\n    }\n    if (isCurrent(cell)) {\n      classes.push(\"current\");\n    }\n    if (cell.inRange && (isNormalDay(cell.type) || props.selectionMode === \"week\")) {\n      classes.push(\"in-range\");\n      if (cell.start) {\n        classes.push(\"start-date\");\n      }\n      if (cell.end) {\n        classes.push(\"end-date\");\n      }\n    }\n    if (cell.disabled) {\n      classes.push(\"disabled\");\n    }\n    if (cell.selected) {\n      classes.push(\"selected\");\n    }\n    if (cell.customClass) {\n      classes.push(cell.customClass);\n    }\n    return classes.join(\" \");\n  };\n  const getRowKls = cell => [ns.e(\"row\"), {\n    current: isWeekActive(cell)\n  }];\n  return {\n    tableKls,\n    tableLabel,\n    weekLabel,\n    getCellClasses,\n    getRowKls,\n    t\n  };\n};\nexport { useBasicDateTable, useBasicDateTableDOM };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}