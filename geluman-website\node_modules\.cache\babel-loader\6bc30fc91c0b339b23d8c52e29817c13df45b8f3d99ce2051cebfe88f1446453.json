{"ast": null, "code": "import Slider from './src/slider2.mjs';\nexport { sliderEmits, sliderProps } from './src/slider.mjs';\nexport { sliderContextKey } from './src/constants.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElSlider = withInstall(Slider);\nexport { ElSlider, ElSlider as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}