{"ast": null, "code": "import arrayEach from './_arrayEach.js';\nimport baseCreate from './_baseCreate.js';\nimport baseForOwn from './_baseForOwn.js';\nimport baseIteratee from './_baseIteratee.js';\nimport getPrototype from './_getPrototype.js';\nimport isArray from './isArray.js';\nimport isBuffer from './isBuffer.js';\nimport isFunction from './isFunction.js';\nimport isObject from './isObject.js';\nimport isTypedArray from './isTypedArray.js';\n\n/**\n * An alternative to `_.reduce`; this method transforms `object` to a new\n * `accumulator` object which is the result of running each of its own\n * enumerable string keyed properties thru `iteratee`, with each invocation\n * potentially mutating the `accumulator` object. If `accumulator` is not\n * provided, a new object with the same `[[Prototype]]` will be used. The\n * iteratee is invoked with four arguments: (accumulator, value, key, object).\n * Iteratee functions may exit iteration early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 1.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @param {*} [accumulator] The custom accumulator value.\n * @returns {*} Returns the accumulated value.\n * @example\n *\n * _.transform([2, 3, 4], function(result, n) {\n *   result.push(n *= n);\n *   return n % 2 == 0;\n * }, []);\n * // => [4, 9]\n *\n * _.transform({ 'a': 1, 'b': 2, 'c': 1 }, function(result, value, key) {\n *   (result[value] || (result[value] = [])).push(key);\n * }, {});\n * // => { '1': ['a', 'c'], '2': ['b'] }\n */\nfunction transform(object, iteratee, accumulator) {\n  var isArr = isArray(object),\n    isArrLike = isArr || isBuffer(object) || isTypedArray(object);\n  iteratee = baseIteratee(iteratee, 4);\n  if (accumulator == null) {\n    var Ctor = object && object.constructor;\n    if (isArrLike) {\n      accumulator = isArr ? new Ctor() : [];\n    } else if (isObject(object)) {\n      accumulator = isFunction(Ctor) ? baseCreate(getPrototype(object)) : {};\n    } else {\n      accumulator = {};\n    }\n  }\n  (isArrLike ? arrayEach : baseForOwn)(object, function (value, index, object) {\n    return iteratee(accumulator, value, index, object);\n  });\n  return accumulator;\n}\nexport default transform;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}