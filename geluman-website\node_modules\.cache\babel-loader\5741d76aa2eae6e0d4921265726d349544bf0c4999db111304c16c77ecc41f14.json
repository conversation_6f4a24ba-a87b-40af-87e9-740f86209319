{"ast": null, "code": "import { tooltipV2RootProps } from './root.mjs';\nimport { tooltipV2TriggerProps } from './trigger.mjs';\nimport { tooltipV2ArrowProps } from './arrow.mjs';\nimport { tooltipV2ContentProps } from './content.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst tooltipV2Props = buildProps({\n  ...tooltipV2RootProps,\n  ...tooltipV2ArrowProps,\n  ...tooltipV2TriggerProps,\n  ...tooltipV2ContentProps,\n  alwaysOn: Boolean,\n  fullTransition: Boolean,\n  transitionProps: {\n    type: definePropType(Object),\n    default: null\n  },\n  teleported: Boolean,\n  to: {\n    type: definePropType(String),\n    default: \"body\"\n  }\n});\nexport { tooltipV2Props };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}