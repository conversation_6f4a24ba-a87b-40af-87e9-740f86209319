{"ast": null, "code": "import { renderSlot, createVNode } from 'vue';\nconst TableV2Cell = (props, {\n  slots\n}) => {\n  var _a;\n  const {\n    cellData,\n    style\n  } = props;\n  const displayText = ((_a = cellData == null ? void 0 : cellData.toString) == null ? void 0 : _a.call(cellData)) || \"\";\n  const defaultSlot = renderSlot(slots, \"default\", props, () => [displayText]);\n  return createVNode(\"div\", {\n    \"class\": props.class,\n    \"title\": displayText,\n    \"style\": style\n  }, [defaultSlot]);\n};\nTableV2Cell.displayName = \"ElTableV2Cell\";\nTableV2Cell.inheritAttrs = false;\nvar TableCell = TableV2Cell;\nexport { TableCell as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}