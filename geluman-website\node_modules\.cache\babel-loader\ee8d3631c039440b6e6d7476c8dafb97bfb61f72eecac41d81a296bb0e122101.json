{"ast": null, "code": "import createWrap from './_createWrap.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_FLIP_FLAG = 512;\n\n/**\n * Creates a function that invokes `func` with arguments reversed.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Function\n * @param {Function} func The function to flip arguments for.\n * @returns {Function} Returns the new flipped function.\n * @example\n *\n * var flipped = _.flip(function() {\n *   return _.toArray(arguments);\n * });\n *\n * flipped('a', 'b', 'c', 'd');\n * // => ['d', 'c', 'b', 'a']\n */\nfunction flip(func) {\n  return createWrap(func, WRAP_FLIP_FLAG);\n}\nexport default flip;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}