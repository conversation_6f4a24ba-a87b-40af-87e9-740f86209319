{"ast": null, "code": "import { popperTriggerProps } from '../../popper/src/trigger.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nconst useTooltipTriggerProps = buildProps({\n  ...popperTriggerProps,\n  disabled: Boolean,\n  trigger: {\n    type: definePropType([String, Array]),\n    default: \"hover\"\n  },\n  triggerKeys: {\n    type: definePropType(Array),\n    default: () => [EVENT_CODE.enter, EVENT_CODE.numpadEnter, EVENT_CODE.space]\n  }\n});\nexport { useTooltipTriggerProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}