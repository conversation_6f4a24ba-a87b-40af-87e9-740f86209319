{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { inject } from 'vue';\nimport { getFixedColumnOffset, ensurePosition, getFixedColumnsClass } from '../util.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isFunction, isString, isArray, isObject } from '@vue/shared';\nfunction useStyles(props) {\n  const parent = inject(TABLE_INJECTION_KEY);\n  const ns = useNamespace(\"table\");\n  const getRowStyle = (row, rowIndex) => {\n    const rowStyle = parent == null ? void 0 : parent.props.rowStyle;\n    if (isFunction(rowStyle)) {\n      return rowStyle.call(null, {\n        row,\n        rowIndex\n      });\n    }\n    return rowStyle || null;\n  };\n  const getRowClass = (row, rowIndex) => {\n    const classes = [ns.e(\"row\")];\n    if ((parent == null ? void 0 : parent.props.highlightCurrentRow) && row === props.store.states.currentRow.value) {\n      classes.push(\"current-row\");\n    }\n    if (props.stripe && rowIndex % 2 === 1) {\n      classes.push(ns.em(\"row\", \"striped\"));\n    }\n    const rowClassName = parent == null ? void 0 : parent.props.rowClassName;\n    if (isString(rowClassName)) {\n      classes.push(rowClassName);\n    } else if (isFunction(rowClassName)) {\n      classes.push(rowClassName.call(null, {\n        row,\n        rowIndex\n      }));\n    }\n    return classes;\n  };\n  const getCellStyle = (rowIndex, columnIndex, row, column) => {\n    const cellStyle = parent == null ? void 0 : parent.props.cellStyle;\n    let cellStyles = cellStyle != null ? cellStyle : {};\n    if (isFunction(cellStyle)) {\n      cellStyles = cellStyle.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column\n      });\n    }\n    const fixedStyle = getFixedColumnOffset(columnIndex, props == null ? void 0 : props.fixed, props.store);\n    ensurePosition(fixedStyle, \"left\");\n    ensurePosition(fixedStyle, \"right\");\n    return Object.assign({}, cellStyles, fixedStyle);\n  };\n  const getCellClass = (rowIndex, columnIndex, row, column, offset) => {\n    const fixedClasses = getFixedColumnsClass(ns.b(), columnIndex, props == null ? void 0 : props.fixed, props.store, void 0, offset);\n    const classes = [column.id, column.align, column.className, ...fixedClasses];\n    const cellClassName = parent == null ? void 0 : parent.props.cellClassName;\n    if (isString(cellClassName)) {\n      classes.push(cellClassName);\n    } else if (isFunction(cellClassName)) {\n      classes.push(cellClassName.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column\n      }));\n    }\n    classes.push(ns.e(\"cell\"));\n    return classes.filter(className => Boolean(className)).join(\" \");\n  };\n  const getSpan = (row, column, rowIndex, columnIndex) => {\n    let rowspan = 1;\n    let colspan = 1;\n    const fn = parent == null ? void 0 : parent.props.spanMethod;\n    if (isFunction(fn)) {\n      const result = fn({\n        row,\n        column,\n        rowIndex,\n        columnIndex\n      });\n      if (isArray(result)) {\n        rowspan = result[0];\n        colspan = result[1];\n      } else if (isObject(result)) {\n        rowspan = result.rowspan;\n        colspan = result.colspan;\n      }\n    }\n    return {\n      rowspan,\n      colspan\n    };\n  };\n  const getColspanRealWidth = (columns, colspan, index) => {\n    if (colspan < 1) {\n      return columns[index].realWidth;\n    }\n    const widthArr = columns.map(({\n      realWidth,\n      width\n    }) => realWidth || width).slice(index, index + colspan);\n    return Number(widthArr.reduce((acc, width) => Number(acc) + Number(width), -1));\n  };\n  return {\n    getRowStyle,\n    getRowClass,\n    getCellStyle,\n    getCellClass,\n    getSpan,\n    getColspanRealWidth\n  };\n}\nexport { useStyles as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}