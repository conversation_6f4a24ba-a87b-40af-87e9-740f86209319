{"ast": null, "code": "import baseClone from './_baseClone.js';\nimport baseConforms from './_baseConforms.js';\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1;\n\n/**\n * Creates a function that invokes the predicate properties of `source` with\n * the corresponding property values of a given object, returning `true` if\n * all predicates return truthy, else `false`.\n *\n * **Note:** The created function is equivalent to `_.conformsTo` with\n * `source` partially applied.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Util\n * @param {Object} source The object of property predicates to conform to.\n * @returns {Function} Returns the new spec function.\n * @example\n *\n * var objects = [\n *   { 'a': 2, 'b': 1 },\n *   { 'a': 1, 'b': 2 }\n * ];\n *\n * _.filter(objects, _.conforms({ 'b': function(n) { return n > 1; } }));\n * // => [{ 'a': 1, 'b': 2 }]\n */\nfunction conforms(source) {\n  return baseConforms(baseClone(source, CLONE_DEEP_FLAG));\n}\nexport default conforms;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}