{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { computed } from 'vue';\nimport { useCheckboxDisabled } from './use-checkbox-disabled.mjs';\nimport { useCheckboxEvent } from './use-checkbox-event.mjs';\nimport { useCheckboxModel } from './use-checkbox-model.mjs';\nimport { useCheckboxStatus } from './use-checkbox-status.mjs';\nimport { useFormItem, useFormItemInputId } from '../../../form/src/hooks/use-form-item.mjs';\nimport { useDeprecated } from '../../../../hooks/use-deprecated/index.mjs';\nimport { isPropAbsent } from '../../../../utils/types.mjs';\nimport { isArray } from '@vue/shared';\nconst useCheckbox = (props, slots) => {\n  const {\n    formItem: elFormItem\n  } = useFormItem();\n  const {\n    model,\n    isGroup,\n    isLimitExceeded\n  } = useCheckboxModel(props);\n  const {\n    isFocused,\n    isChecked,\n    checkboxButtonSize,\n    checkboxSize,\n    hasOwnLabel,\n    actualValue\n  } = useCheckboxStatus(props, slots, {\n    model\n  });\n  const {\n    isDisabled\n  } = useCheckboxDisabled({\n    model,\n    isChecked\n  });\n  const {\n    inputId,\n    isLabeledByFormItem\n  } = useFormItemInputId(props, {\n    formItemContext: elFormItem,\n    disableIdGeneration: hasOwnLabel,\n    disableIdManagement: isGroup\n  });\n  const {\n    handleChange,\n    onClickRoot\n  } = useCheckboxEvent(props, {\n    model,\n    isLimitExceeded,\n    hasOwnLabel,\n    isDisabled,\n    isLabeledByFormItem\n  });\n  const setStoreValue = () => {\n    function addToStore() {\n      var _a, _b;\n      if (isArray(model.value) && !model.value.includes(actualValue.value)) {\n        model.value.push(actualValue.value);\n      } else {\n        model.value = (_b = (_a = props.trueValue) != null ? _a : props.trueLabel) != null ? _b : true;\n      }\n    }\n    props.checked && addToStore();\n  };\n  setStoreValue();\n  useDeprecated({\n    from: \"label act as value\",\n    replacement: \"value\",\n    version: \"3.0.0\",\n    scope: \"el-checkbox\",\n    ref: \"https://element-plus.org/en-US/component/checkbox.html\"\n  }, computed(() => isGroup.value && isPropAbsent(props.value)));\n  useDeprecated({\n    from: \"true-label\",\n    replacement: \"true-value\",\n    version: \"3.0.0\",\n    scope: \"el-checkbox\",\n    ref: \"https://element-plus.org/en-US/component/checkbox.html\"\n  }, computed(() => !!props.trueLabel));\n  useDeprecated({\n    from: \"false-label\",\n    replacement: \"false-value\",\n    version: \"3.0.0\",\n    scope: \"el-checkbox\",\n    ref: \"https://element-plus.org/en-US/component/checkbox.html\"\n  }, computed(() => !!props.falseLabel));\n  return {\n    inputId,\n    isLabeledByFormItem,\n    isChecked,\n    isDisabled,\n    isFocused,\n    checkboxButtonSize,\n    checkboxSize,\n    hasOwnLabel,\n    model,\n    actualValue,\n    handleChange,\n    onClickRoot\n  };\n};\nexport { useCheckbox };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}