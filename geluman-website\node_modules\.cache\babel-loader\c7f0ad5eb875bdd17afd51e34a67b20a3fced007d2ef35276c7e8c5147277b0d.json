{"ast": null, "code": "import { defineComponent, ref, computed, reactive, onMounted, h } from 'vue';\nimport { pick } from 'lodash-unified';\nimport { ElSelect } from '../../select/index.mjs';\nimport { ElTree } from '../../tree/index.mjs';\nimport { useSelect } from './select.mjs';\nimport { useTree } from './tree.mjs';\nimport CacheOptions from './cache-options.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElTreeSelect\",\n  inheritAttrs: false,\n  props: {\n    ...ElSelect.props,\n    ...ElTree.props,\n    cacheData: {\n      type: Array,\n      default: () => []\n    }\n  },\n  setup(props, context) {\n    const {\n      slots,\n      expose\n    } = context;\n    const select = ref();\n    const tree = ref();\n    const key = computed(() => props.nodeKey || props.valueKey || \"value\");\n    const selectProps = useSelect(props, context, {\n      select,\n      tree,\n      key\n    });\n    const {\n      cacheOptions,\n      ...treeProps\n    } = useTree(props, context, {\n      select,\n      tree,\n      key\n    });\n    const methods = reactive({});\n    expose(methods);\n    onMounted(() => {\n      Object.assign(methods, {\n        ...pick(tree.value, [\"filter\", \"updateKeyChildren\", \"getCheckedNodes\", \"setCheckedNodes\", \"getCheckedKeys\", \"setCheckedKeys\", \"setChecked\", \"getHalfCheckedNodes\", \"getHalfCheckedKeys\", \"getCurrentKey\", \"getCurrentNode\", \"setCurrentKey\", \"setCurrentNode\", \"getNode\", \"remove\", \"append\", \"insertBefore\", \"insertAfter\"]),\n        ...pick(select.value, [\"focus\", \"blur\", \"selectedLabel\"])\n      });\n    });\n    return () => h(ElSelect, reactive({\n      ...selectProps,\n      ref: ref2 => select.value = ref2\n    }), {\n      ...slots,\n      default: () => [h(CacheOptions, {\n        data: cacheOptions.value\n      }), h(ElTree, reactive({\n        ...treeProps,\n        ref: ref2 => tree.value = ref2\n      }))]\n    });\n  }\n});\nvar TreeSelect = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tree-select.vue\"]]);\nexport { TreeSelect as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}