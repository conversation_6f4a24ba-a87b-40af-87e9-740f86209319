{"ast": null, "code": "function useOption(props, {\n  emit\n}) {\n  return {\n    hoverItem: () => {\n      if (!props.disabled) {\n        emit(\"hover\", props.index);\n      }\n    },\n    selectOptionClick: () => {\n      if (!props.disabled) {\n        emit(\"select\", props.item, props.index);\n      }\n    }\n  };\n}\nexport { useOption };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}