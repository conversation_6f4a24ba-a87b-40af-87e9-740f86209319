{"ast": null, "code": "import { computed, unref, ref, inject } from 'vue';\nimport { formContextKey, formItemContextKey } from '../constants.mjs';\nimport { useGlobalSize } from '../../../../hooks/use-size/index.mjs';\nimport { useProp } from '../../../../hooks/use-prop/index.mjs';\nconst useFormSize = (fallback, ignore = {}) => {\n  const emptyRef = ref(void 0);\n  const size = ignore.prop ? emptyRef : useProp(\"size\");\n  const globalConfig = ignore.global ? emptyRef : useGlobalSize();\n  const form = ignore.form ? {\n    size: void 0\n  } : inject(formContextKey, void 0);\n  const formItem = ignore.formItem ? {\n    size: void 0\n  } : inject(formItemContextKey, void 0);\n  return computed(() => size.value || unref(fallback) || (formItem == null ? void 0 : formItem.size) || (form == null ? void 0 : form.size) || globalConfig.value || \"\");\n};\nconst useFormDisabled = fallback => {\n  const disabled = useProp(\"disabled\");\n  const form = inject(formContextKey, void 0);\n  return computed(() => disabled.value || unref(fallback) || (form == null ? void 0 : form.disabled) || false);\n};\nconst useSize = useFormSize;\nconst useDisabled = useFormDisabled;\nexport { useDisabled, useFormDisabled, useFormSize, useSize };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}