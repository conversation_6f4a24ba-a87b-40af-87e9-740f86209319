{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { hasOwn, isString } from '@vue/shared';\nconst hsv2hsl = function (hue, sat, val) {\n  return [hue, sat * val / ((hue = (2 - sat) * val) < 1 ? hue : 2 - hue) || 0, hue / 2];\n};\nconst isOnePointZero = function (n) {\n  return isString(n) && n.includes(\".\") && Number.parseFloat(n) === 1;\n};\nconst isPercentage = function (n) {\n  return isString(n) && n.includes(\"%\");\n};\nconst bound01 = function (value, max) {\n  if (isOnePointZero(value)) value = \"100%\";\n  const processPercent = isPercentage(value);\n  value = Math.min(max, Math.max(0, Number.parseFloat(`${value}`)));\n  if (processPercent) {\n    value = Number.parseInt(`${value * max}`, 10) / 100;\n  }\n  if (Math.abs(value - max) < 1e-6) {\n    return 1;\n  }\n  return value % max / Number.parseFloat(max);\n};\nconst INT_HEX_MAP = {\n  10: \"A\",\n  11: \"B\",\n  12: \"C\",\n  13: \"D\",\n  14: \"E\",\n  15: \"F\"\n};\nconst hexOne = value => {\n  value = Math.min(Math.round(value), 255);\n  const high = Math.floor(value / 16);\n  const low = value % 16;\n  return `${INT_HEX_MAP[high] || high}${INT_HEX_MAP[low] || low}`;\n};\nconst toHex = function ({\n  r,\n  g,\n  b\n}) {\n  if (Number.isNaN(+r) || Number.isNaN(+g) || Number.isNaN(+b)) return \"\";\n  return `#${hexOne(r)}${hexOne(g)}${hexOne(b)}`;\n};\nconst HEX_INT_MAP = {\n  A: 10,\n  B: 11,\n  C: 12,\n  D: 13,\n  E: 14,\n  F: 15\n};\nconst parseHexChannel = function (hex) {\n  if (hex.length === 2) {\n    return (HEX_INT_MAP[hex[0].toUpperCase()] || +hex[0]) * 16 + (HEX_INT_MAP[hex[1].toUpperCase()] || +hex[1]);\n  }\n  return HEX_INT_MAP[hex[1].toUpperCase()] || +hex[1];\n};\nconst hsl2hsv = function (hue, sat, light) {\n  sat = sat / 100;\n  light = light / 100;\n  let smin = sat;\n  const lmin = Math.max(light, 0.01);\n  light *= 2;\n  sat *= light <= 1 ? light : 2 - light;\n  smin *= lmin <= 1 ? lmin : 2 - lmin;\n  const v = (light + sat) / 2;\n  const sv = light === 0 ? 2 * smin / (lmin + smin) : 2 * sat / (light + sat);\n  return {\n    h: hue,\n    s: sv * 100,\n    v: v * 100\n  };\n};\nconst rgb2hsv = (r, g, b) => {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  let h;\n  const v = max;\n  const d = max - min;\n  const s = max === 0 ? 0 : d / max;\n  if (max === min) {\n    h = 0;\n  } else {\n    switch (max) {\n      case r:\n        {\n          h = (g - b) / d + (g < b ? 6 : 0);\n          break;\n        }\n      case g:\n        {\n          h = (b - r) / d + 2;\n          break;\n        }\n      case b:\n        {\n          h = (r - g) / d + 4;\n          break;\n        }\n    }\n    h /= 6;\n  }\n  return {\n    h: h * 360,\n    s: s * 100,\n    v: v * 100\n  };\n};\nconst hsv2rgb = function (h, s, v) {\n  h = bound01(h, 360) * 6;\n  s = bound01(s, 100);\n  v = bound01(v, 100);\n  const i = Math.floor(h);\n  const f = h - i;\n  const p = v * (1 - s);\n  const q = v * (1 - f * s);\n  const t = v * (1 - (1 - f) * s);\n  const mod = i % 6;\n  const r = [v, q, p, p, t, v][mod];\n  const g = [t, v, v, q, p, p][mod];\n  const b = [p, p, t, v, v, q][mod];\n  return {\n    r: Math.round(r * 255),\n    g: Math.round(g * 255),\n    b: Math.round(b * 255)\n  };\n};\nclass Color {\n  constructor(options = {}) {\n    this._hue = 0;\n    this._saturation = 100;\n    this._value = 100;\n    this._alpha = 100;\n    this.enableAlpha = false;\n    this.format = \"hex\";\n    this.value = \"\";\n    for (const option in options) {\n      if (hasOwn(options, option)) {\n        this[option] = options[option];\n      }\n    }\n    if (options.value) {\n      this.fromString(options.value);\n    } else {\n      this.doOnChange();\n    }\n  }\n  set(prop, value) {\n    if (arguments.length === 1 && typeof prop === \"object\") {\n      for (const p in prop) {\n        if (hasOwn(prop, p)) {\n          this.set(p, prop[p]);\n        }\n      }\n      return;\n    }\n    this[`_${prop}`] = value;\n    this.doOnChange();\n  }\n  get(prop) {\n    if (prop === \"alpha\") {\n      return Math.floor(this[`_${prop}`]);\n    }\n    return this[`_${prop}`];\n  }\n  toRgb() {\n    return hsv2rgb(this._hue, this._saturation, this._value);\n  }\n  fromString(value) {\n    if (!value) {\n      this._hue = 0;\n      this._saturation = 100;\n      this._value = 100;\n      this.doOnChange();\n      return;\n    }\n    const fromHSV = (h, s, v) => {\n      this._hue = Math.max(0, Math.min(360, h));\n      this._saturation = Math.max(0, Math.min(100, s));\n      this._value = Math.max(0, Math.min(100, v));\n      this.doOnChange();\n    };\n    if (value.includes(\"hsl\")) {\n      const parts = value.replace(/hsla|hsl|\\(|\\)/gm, \"\").split(/\\s|,/g).filter(val => val !== \"\").map((val, index) => index > 2 ? Number.parseFloat(val) : Number.parseInt(val, 10));\n      if (parts.length === 4) {\n        this._alpha = Number.parseFloat(parts[3]) * 100;\n      } else if (parts.length === 3) {\n        this._alpha = 100;\n      }\n      if (parts.length >= 3) {\n        const {\n          h,\n          s,\n          v\n        } = hsl2hsv(parts[0], parts[1], parts[2]);\n        fromHSV(h, s, v);\n      }\n    } else if (value.includes(\"hsv\")) {\n      const parts = value.replace(/hsva|hsv|\\(|\\)/gm, \"\").split(/\\s|,/g).filter(val => val !== \"\").map((val, index) => index > 2 ? Number.parseFloat(val) : Number.parseInt(val, 10));\n      if (parts.length === 4) {\n        this._alpha = Number.parseFloat(parts[3]) * 100;\n      } else if (parts.length === 3) {\n        this._alpha = 100;\n      }\n      if (parts.length >= 3) {\n        fromHSV(parts[0], parts[1], parts[2]);\n      }\n    } else if (value.includes(\"rgb\")) {\n      const parts = value.replace(/rgba|rgb|\\(|\\)/gm, \"\").split(/\\s|,/g).filter(val => val !== \"\").map((val, index) => index > 2 ? Number.parseFloat(val) : Number.parseInt(val, 10));\n      if (parts.length === 4) {\n        this._alpha = Number.parseFloat(parts[3]) * 100;\n      } else if (parts.length === 3) {\n        this._alpha = 100;\n      }\n      if (parts.length >= 3) {\n        const {\n          h,\n          s,\n          v\n        } = rgb2hsv(parts[0], parts[1], parts[2]);\n        fromHSV(h, s, v);\n      }\n    } else if (value.includes(\"#\")) {\n      const hex = value.replace(\"#\", \"\").trim();\n      if (!/^[0-9a-fA-F]{3}$|^[0-9a-fA-F]{6}$|^[0-9a-fA-F]{8}$/.test(hex)) return;\n      let r, g, b;\n      if (hex.length === 3) {\n        r = parseHexChannel(hex[0] + hex[0]);\n        g = parseHexChannel(hex[1] + hex[1]);\n        b = parseHexChannel(hex[2] + hex[2]);\n      } else if (hex.length === 6 || hex.length === 8) {\n        r = parseHexChannel(hex.slice(0, 2));\n        g = parseHexChannel(hex.slice(2, 4));\n        b = parseHexChannel(hex.slice(4, 6));\n      }\n      if (hex.length === 8) {\n        this._alpha = parseHexChannel(hex.slice(6)) / 255 * 100;\n      } else if (hex.length === 3 || hex.length === 6) {\n        this._alpha = 100;\n      }\n      const {\n        h,\n        s,\n        v\n      } = rgb2hsv(r, g, b);\n      fromHSV(h, s, v);\n    }\n  }\n  compare(color) {\n    return Math.abs(color._hue - this._hue) < 2 && Math.abs(color._saturation - this._saturation) < 1 && Math.abs(color._value - this._value) < 1 && Math.abs(color._alpha - this._alpha) < 1;\n  }\n  doOnChange() {\n    const {\n      _hue,\n      _saturation,\n      _value,\n      _alpha,\n      format\n    } = this;\n    if (this.enableAlpha) {\n      switch (format) {\n        case \"hsl\":\n          {\n            const hsl = hsv2hsl(_hue, _saturation / 100, _value / 100);\n            this.value = `hsla(${_hue}, ${Math.round(hsl[1] * 100)}%, ${Math.round(hsl[2] * 100)}%, ${this.get(\"alpha\") / 100})`;\n            break;\n          }\n        case \"hsv\":\n          {\n            this.value = `hsva(${_hue}, ${Math.round(_saturation)}%, ${Math.round(_value)}%, ${this.get(\"alpha\") / 100})`;\n            break;\n          }\n        case \"hex\":\n          {\n            this.value = `${toHex(hsv2rgb(_hue, _saturation, _value))}${hexOne(_alpha * 255 / 100)}`;\n            break;\n          }\n        default:\n          {\n            const {\n              r,\n              g,\n              b\n            } = hsv2rgb(_hue, _saturation, _value);\n            this.value = `rgba(${r}, ${g}, ${b}, ${this.get(\"alpha\") / 100})`;\n          }\n      }\n    } else {\n      switch (format) {\n        case \"hsl\":\n          {\n            const hsl = hsv2hsl(_hue, _saturation / 100, _value / 100);\n            this.value = `hsl(${_hue}, ${Math.round(hsl[1] * 100)}%, ${Math.round(hsl[2] * 100)}%)`;\n            break;\n          }\n        case \"hsv\":\n          {\n            this.value = `hsv(${_hue}, ${Math.round(_saturation)}%, ${Math.round(_value)}%)`;\n            break;\n          }\n        case \"rgb\":\n          {\n            const {\n              r,\n              g,\n              b\n            } = hsv2rgb(_hue, _saturation, _value);\n            this.value = `rgb(${r}, ${g}, ${b})`;\n            break;\n          }\n        default:\n          {\n            this.value = toHex(hsv2rgb(_hue, _saturation, _value));\n          }\n      }\n    }\n  }\n}\nexport { Color as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}