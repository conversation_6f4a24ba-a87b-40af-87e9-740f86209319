{"ast": null, "code": "import baseForOwn from './_baseForOwn.js';\n\n/**\n * The base implementation of `_.invert` and `_.invertBy` which inverts\n * `object` with values transformed by `iteratee` and set by `setter`.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} setter The function to set `accumulator` values.\n * @param {Function} iteratee The iteratee to transform values.\n * @param {Object} accumulator The initial inverted object.\n * @returns {Function} Returns `accumulator`.\n */\nfunction baseInverter(object, setter, iteratee, accumulator) {\n  baseForOwn(object, function (value, key, object) {\n    setter(accumulator, iteratee(value), key, object);\n  });\n  return accumulator;\n}\nexport default baseInverter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}