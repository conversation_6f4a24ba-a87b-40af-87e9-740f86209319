{"ast": null, "code": "import { defineComponent, provide, unref, createVNode, isVNode, mergeProps } from 'vue';\nimport { useTable } from './use-table.mjs';\nimport { TableV2InjectionKey } from './tokens.mjs';\nimport { tableV2Props } from './table.mjs';\nimport MainTable from './renderers/main-table.mjs';\nimport LeftTable from './renderers/left-table.mjs';\nimport RightTable from './renderers/right-table.mjs';\nimport Row from './renderers/row.mjs';\nimport Cell from './renderers/cell.mjs';\nimport Header from './renderers/header.mjs';\nimport HeaderCell from './renderers/header-cell.mjs';\nimport Footer from './renderers/footer.mjs';\nimport Empty from './renderers/empty.mjs';\nimport Overlay from './renderers/overlay.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nfunction _isSlot(s) {\n  return typeof s === \"function\" || Object.prototype.toString.call(s) === \"[object Object]\" && !isVNode(s);\n}\nconst COMPONENT_NAME = \"ElTableV2\";\nconst TableV2 = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2Props,\n  setup(props, {\n    slots,\n    expose\n  }) {\n    const ns = useNamespace(\"table-v2\");\n    const {\n      columnsStyles,\n      fixedColumnsOnLeft,\n      fixedColumnsOnRight,\n      mainColumns,\n      mainTableHeight,\n      fixedTableHeight,\n      leftTableWidth,\n      rightTableWidth,\n      data,\n      depthMap,\n      expandedRowKeys,\n      hasFixedColumns,\n      mainTableRef,\n      leftTableRef,\n      rightTableRef,\n      isDynamic,\n      isResetting,\n      isScrolling,\n      bodyWidth,\n      emptyStyle,\n      rootStyle,\n      headerWidth,\n      footerHeight,\n      showEmpty,\n      scrollTo,\n      scrollToLeft,\n      scrollToTop,\n      scrollToRow,\n      getRowHeight,\n      onColumnSorted,\n      onRowHeightChange,\n      onRowHovered,\n      onRowExpanded,\n      onRowsRendered,\n      onScroll,\n      onVerticalScroll\n    } = useTable(props);\n    expose({\n      scrollTo,\n      scrollToLeft,\n      scrollToTop,\n      scrollToRow\n    });\n    provide(TableV2InjectionKey, {\n      ns,\n      isResetting,\n      isScrolling\n    });\n    return () => {\n      const {\n        cache,\n        cellProps,\n        estimatedRowHeight,\n        expandColumnKey,\n        fixedData,\n        headerHeight,\n        headerClass,\n        headerProps,\n        headerCellProps,\n        sortBy,\n        sortState,\n        rowHeight,\n        rowClass,\n        rowEventHandlers,\n        rowKey,\n        rowProps,\n        scrollbarAlwaysOn,\n        indentSize,\n        iconSize,\n        useIsScrolling,\n        vScrollbarSize,\n        width\n      } = props;\n      const _data = unref(data);\n      const mainTableProps = {\n        cache,\n        class: ns.e(\"main\"),\n        columns: unref(mainColumns),\n        data: _data,\n        fixedData,\n        estimatedRowHeight,\n        bodyWidth: unref(bodyWidth) + vScrollbarSize,\n        headerHeight,\n        headerWidth: unref(headerWidth),\n        height: unref(mainTableHeight),\n        mainTableRef,\n        rowKey,\n        rowHeight,\n        scrollbarAlwaysOn,\n        scrollbarStartGap: 2,\n        scrollbarEndGap: vScrollbarSize,\n        useIsScrolling,\n        width,\n        getRowHeight,\n        onRowsRendered,\n        onScroll\n      };\n      const leftColumnsWidth = unref(leftTableWidth);\n      const _fixedTableHeight = unref(fixedTableHeight);\n      const leftTableProps = {\n        cache,\n        class: ns.e(\"left\"),\n        columns: unref(fixedColumnsOnLeft),\n        data: _data,\n        fixedData,\n        estimatedRowHeight,\n        leftTableRef,\n        rowHeight,\n        bodyWidth: leftColumnsWidth,\n        headerWidth: leftColumnsWidth,\n        headerHeight,\n        height: _fixedTableHeight,\n        rowKey,\n        scrollbarAlwaysOn,\n        scrollbarStartGap: 2,\n        scrollbarEndGap: vScrollbarSize,\n        useIsScrolling,\n        width: leftColumnsWidth,\n        getRowHeight,\n        onScroll: onVerticalScroll\n      };\n      const rightColumnsWidth = unref(rightTableWidth);\n      const rightColumnsWidthWithScrollbar = rightColumnsWidth + vScrollbarSize;\n      const rightTableProps = {\n        cache,\n        class: ns.e(\"right\"),\n        columns: unref(fixedColumnsOnRight),\n        data: _data,\n        fixedData,\n        estimatedRowHeight,\n        rightTableRef,\n        rowHeight,\n        bodyWidth: rightColumnsWidthWithScrollbar,\n        headerWidth: rightColumnsWidthWithScrollbar,\n        headerHeight,\n        height: _fixedTableHeight,\n        rowKey,\n        scrollbarAlwaysOn,\n        scrollbarStartGap: 2,\n        scrollbarEndGap: vScrollbarSize,\n        width: rightColumnsWidthWithScrollbar,\n        style: `--${unref(ns.namespace)}-table-scrollbar-size: ${vScrollbarSize}px`,\n        useIsScrolling,\n        getRowHeight,\n        onScroll: onVerticalScroll\n      };\n      const _columnsStyles = unref(columnsStyles);\n      const tableRowProps = {\n        ns,\n        depthMap: unref(depthMap),\n        columnsStyles: _columnsStyles,\n        expandColumnKey,\n        expandedRowKeys: unref(expandedRowKeys),\n        estimatedRowHeight,\n        hasFixedColumns: unref(hasFixedColumns),\n        rowProps,\n        rowClass,\n        rowKey,\n        rowEventHandlers,\n        onRowHovered,\n        onRowExpanded,\n        onRowHeightChange\n      };\n      const tableCellProps = {\n        cellProps,\n        expandColumnKey,\n        indentSize,\n        iconSize,\n        rowKey,\n        expandedRowKeys: unref(expandedRowKeys),\n        ns\n      };\n      const tableHeaderProps = {\n        ns,\n        headerClass,\n        headerProps,\n        columnsStyles: _columnsStyles\n      };\n      const tableHeaderCellProps = {\n        ns,\n        sortBy,\n        sortState,\n        headerCellProps,\n        onColumnSorted\n      };\n      const tableSlots = {\n        row: props2 => createVNode(Row, mergeProps(props2, tableRowProps), {\n          row: slots.row,\n          cell: props3 => {\n            let _slot;\n            return slots.cell ? createVNode(Cell, mergeProps(props3, tableCellProps, {\n              \"style\": _columnsStyles[props3.column.key]\n            }), _isSlot(_slot = slots.cell(props3)) ? _slot : {\n              default: () => [_slot]\n            }) : createVNode(Cell, mergeProps(props3, tableCellProps, {\n              \"style\": _columnsStyles[props3.column.key]\n            }), null);\n          }\n        }),\n        header: props2 => createVNode(Header, mergeProps(props2, tableHeaderProps), {\n          header: slots.header,\n          cell: props3 => {\n            let _slot2;\n            return slots[\"header-cell\"] ? createVNode(HeaderCell, mergeProps(props3, tableHeaderCellProps, {\n              \"style\": _columnsStyles[props3.column.key]\n            }), _isSlot(_slot2 = slots[\"header-cell\"](props3)) ? _slot2 : {\n              default: () => [_slot2]\n            }) : createVNode(HeaderCell, mergeProps(props3, tableHeaderCellProps, {\n              \"style\": _columnsStyles[props3.column.key]\n            }), null);\n          }\n        })\n      };\n      const rootKls = [props.class, ns.b(), ns.e(\"root\"), {\n        [ns.is(\"dynamic\")]: unref(isDynamic)\n      }];\n      const footerProps = {\n        class: ns.e(\"footer\"),\n        style: unref(footerHeight)\n      };\n      return createVNode(\"div\", {\n        \"class\": rootKls,\n        \"style\": unref(rootStyle)\n      }, [createVNode(MainTable, mainTableProps, _isSlot(tableSlots) ? tableSlots : {\n        default: () => [tableSlots]\n      }), createVNode(LeftTable, leftTableProps, _isSlot(tableSlots) ? tableSlots : {\n        default: () => [tableSlots]\n      }), createVNode(RightTable, rightTableProps, _isSlot(tableSlots) ? tableSlots : {\n        default: () => [tableSlots]\n      }), slots.footer && createVNode(Footer, footerProps, {\n        default: slots.footer\n      }), unref(showEmpty) && createVNode(Empty, {\n        \"class\": ns.e(\"empty\"),\n        \"style\": unref(emptyStyle)\n      }, {\n        default: slots.empty\n      }), slots.overlay && createVNode(Overlay, {\n        \"class\": ns.e(\"overlay\")\n      }, {\n        default: slots.overlay\n      })]);\n    };\n  }\n});\nvar TableV2$1 = TableV2;\nexport { TableV2$1 as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}