{"ast": null, "code": "import { getCurrentInstance, ref, computed, watch, nextTick, onMounted } from 'vue';\nimport { useTimeoutFn, isClient } from '@vueuse/core';\nimport { isUndefined } from 'lodash-unified';\nimport { useLockscreen } from '../../../hooks/use-lockscreen/index.mjs';\nimport { useZIndex } from '../../../hooks/use-z-index/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { useGlobalConfig } from '../../config-provider/src/hooks/use-global-config.mjs';\nimport { defaultNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nconst useDialog = (props, targetRef) => {\n  var _a;\n  const instance = getCurrentInstance();\n  const emit = instance.emit;\n  const {\n    nextZIndex\n  } = useZIndex();\n  let lastPosition = \"\";\n  const titleId = useId();\n  const bodyId = useId();\n  const visible = ref(false);\n  const closed = ref(false);\n  const rendered = ref(false);\n  const zIndex = ref((_a = props.zIndex) != null ? _a : nextZIndex());\n  let openTimer = void 0;\n  let closeTimer = void 0;\n  const namespace = useGlobalConfig(\"namespace\", defaultNamespace);\n  const style = computed(() => {\n    const style2 = {};\n    const varPrefix = `--${namespace.value}-dialog`;\n    if (!props.fullscreen) {\n      if (props.top) {\n        style2[`${varPrefix}-margin-top`] = props.top;\n      }\n      if (props.width) {\n        style2[`${varPrefix}-width`] = addUnit(props.width);\n      }\n    }\n    return style2;\n  });\n  const overlayDialogStyle = computed(() => {\n    if (props.alignCenter) {\n      return {\n        display: \"flex\"\n      };\n    }\n    return {};\n  });\n  function afterEnter() {\n    emit(\"opened\");\n  }\n  function afterLeave() {\n    emit(\"closed\");\n    emit(UPDATE_MODEL_EVENT, false);\n    if (props.destroyOnClose) {\n      rendered.value = false;\n    }\n  }\n  function beforeLeave() {\n    emit(\"close\");\n  }\n  function open() {\n    closeTimer == null ? void 0 : closeTimer();\n    openTimer == null ? void 0 : openTimer();\n    if (props.openDelay && props.openDelay > 0) {\n      ({\n        stop: openTimer\n      } = useTimeoutFn(() => doOpen(), props.openDelay));\n    } else {\n      doOpen();\n    }\n  }\n  function close() {\n    openTimer == null ? void 0 : openTimer();\n    closeTimer == null ? void 0 : closeTimer();\n    if (props.closeDelay && props.closeDelay > 0) {\n      ({\n        stop: closeTimer\n      } = useTimeoutFn(() => doClose(), props.closeDelay));\n    } else {\n      doClose();\n    }\n  }\n  function handleClose() {\n    function hide(shouldCancel) {\n      if (shouldCancel) return;\n      closed.value = true;\n      visible.value = false;\n    }\n    if (props.beforeClose) {\n      props.beforeClose(hide);\n    } else {\n      close();\n    }\n  }\n  function onModalClick() {\n    if (props.closeOnClickModal) {\n      handleClose();\n    }\n  }\n  function doOpen() {\n    if (!isClient) return;\n    visible.value = true;\n  }\n  function doClose() {\n    visible.value = false;\n  }\n  function onOpenAutoFocus() {\n    emit(\"openAutoFocus\");\n  }\n  function onCloseAutoFocus() {\n    emit(\"closeAutoFocus\");\n  }\n  function onFocusoutPrevented(event) {\n    var _a2;\n    if (((_a2 = event.detail) == null ? void 0 : _a2.focusReason) === \"pointer\") {\n      event.preventDefault();\n    }\n  }\n  if (props.lockScroll) {\n    useLockscreen(visible);\n  }\n  function onCloseRequested() {\n    if (props.closeOnPressEscape) {\n      handleClose();\n    }\n  }\n  watch(() => props.modelValue, val => {\n    if (val) {\n      closed.value = false;\n      open();\n      rendered.value = true;\n      zIndex.value = isUndefined(props.zIndex) ? nextZIndex() : zIndex.value++;\n      nextTick(() => {\n        emit(\"open\");\n        if (targetRef.value) {\n          targetRef.value.parentElement.scrollTop = 0;\n          targetRef.value.parentElement.scrollLeft = 0;\n          targetRef.value.scrollTop = 0;\n        }\n      });\n    } else {\n      if (visible.value) {\n        close();\n      }\n    }\n  });\n  watch(() => props.fullscreen, val => {\n    if (!targetRef.value) return;\n    if (val) {\n      lastPosition = targetRef.value.style.transform;\n      targetRef.value.style.transform = \"\";\n    } else {\n      targetRef.value.style.transform = lastPosition;\n    }\n  });\n  onMounted(() => {\n    if (props.modelValue) {\n      visible.value = true;\n      rendered.value = true;\n      open();\n    }\n  });\n  return {\n    afterEnter,\n    afterLeave,\n    beforeLeave,\n    handleClose,\n    onModalClick,\n    close,\n    doClose,\n    onOpenAutoFocus,\n    onCloseAutoFocus,\n    onCloseRequested,\n    onFocusoutPrevented,\n    titleId,\n    bodyId,\n    closed,\n    style,\n    overlayDialogStyle,\n    rendered,\n    visible,\n    zIndex\n  };\n};\nexport { useDialog };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}