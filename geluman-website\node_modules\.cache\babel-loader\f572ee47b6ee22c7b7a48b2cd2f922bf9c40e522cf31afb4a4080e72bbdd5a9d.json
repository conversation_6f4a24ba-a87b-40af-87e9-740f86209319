{"ast": null, "code": "import { createVNode, isVNode } from 'vue';\nimport { tryCall } from '../utils.mjs';\nimport HeaderRow from '../components/header-row.mjs';\nfunction _isSlot(s) {\n  return typeof s === \"function\" || Object.prototype.toString.call(s) === \"[object Object]\" && !isVNode(s);\n}\nconst HeaderRenderer = ({\n  columns,\n  columnsStyles,\n  headerIndex,\n  style,\n  headerClass,\n  headerProps,\n  ns\n}, {\n  slots\n}) => {\n  const param = {\n    columns,\n    headerIndex\n  };\n  const kls = [ns.e(\"header-row\"), tryCall(headerClass, param, \"\"), {\n    [ns.is(\"customized\")]: Boolean(slots.header)\n  }];\n  const extraProps = {\n    ...tryCall(headerProps, param),\n    columnsStyles,\n    class: kls,\n    columns,\n    headerIndex,\n    style\n  };\n  return createVNode(HeaderRow, extraProps, _isSlot(slots) ? slots : {\n    default: () => [slots]\n  });\n};\nvar Header = HeaderRenderer;\nexport { Header as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}