{"ast": null, "code": "import { dialogContentProps } from './dialog-content.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { teleportProps } from '../../teleport/src/teleport.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { isBoolean } from '../../../utils/types.mjs';\nconst dialogProps = buildProps({\n  ...dialogContentProps,\n  appendToBody: Boolean,\n  appendTo: {\n    type: teleportProps.to.type,\n    default: \"body\"\n  },\n  beforeClose: {\n    type: definePropType(Function)\n  },\n  destroyOnClose: Boolean,\n  closeOnClickModal: {\n    type: Boolean,\n    default: true\n  },\n  closeOnPressEscape: {\n    type: Boolean,\n    default: true\n  },\n  lockScroll: {\n    type: Boolean,\n    default: true\n  },\n  modal: {\n    type: Boolean,\n    default: true\n  },\n  openDelay: {\n    type: Number,\n    default: 0\n  },\n  closeDelay: {\n    type: Number,\n    default: 0\n  },\n  top: {\n    type: String\n  },\n  modelValue: Boolean,\n  modalClass: String,\n  headerClass: String,\n  bodyClass: String,\n  footerClass: String,\n  width: {\n    type: [String, Number]\n  },\n  zIndex: {\n    type: Number\n  },\n  trapFocus: Boolean,\n  headerAriaLevel: {\n    type: String,\n    default: \"2\"\n  }\n});\nconst dialogEmits = {\n  open: () => true,\n  opened: () => true,\n  close: () => true,\n  closed: () => true,\n  [UPDATE_MODEL_EVENT]: value => isBoolean(value),\n  openAutoFocus: () => true,\n  closeAutoFocus: () => true\n};\nexport { dialogEmits, dialogProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}