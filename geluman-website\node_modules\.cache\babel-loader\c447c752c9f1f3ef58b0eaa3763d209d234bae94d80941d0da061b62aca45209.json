{"ast": null, "code": "import { tooltipV2Sides } from './common.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst tooltipV2ArrowProps = buildProps({\n  width: {\n    type: Number,\n    default: 10\n  },\n  height: {\n    type: Number,\n    default: 10\n  },\n  style: {\n    type: definePropType(Object),\n    default: null\n  }\n});\nconst tooltipV2ArrowSpecialProps = buildProps({\n  side: {\n    type: definePropType(String),\n    values: tooltipV2Sides,\n    required: true\n  }\n});\nexport { tooltipV2ArrowProps, tooltipV2ArrowSpecialProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}