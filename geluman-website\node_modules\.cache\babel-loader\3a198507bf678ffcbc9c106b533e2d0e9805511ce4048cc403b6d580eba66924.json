{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst tooltipV2CommonProps = buildProps({\n  nowrap: Boolean\n});\nvar TooltipV2Sides = /* @__PURE__ */(TooltipV2Sides2 => {\n  TooltipV2Sides2[\"top\"] = \"top\";\n  TooltipV2Sides2[\"bottom\"] = \"bottom\";\n  TooltipV2Sides2[\"left\"] = \"left\";\n  TooltipV2Sides2[\"right\"] = \"right\";\n  return TooltipV2Sides2;\n})(TooltipV2Sides || {});\nconst tooltipV2Sides = Object.values(TooltipV2Sides);\nconst tooltipV2OppositeSide = {\n  [\"top\" /* top */]: \"bottom\" /* bottom */,\n  [\"bottom\" /* bottom */]: \"top\" /* top */,\n  [\"left\" /* left */]: \"right\" /* right */,\n  [\"right\" /* right */]: \"left\" /* left */\n};\nconst tooltipV2ArrowBorders = {\n  [\"top\" /* top */]: [\"left\" /* left */, \"top\" /* top */],\n  [\"bottom\" /* bottom */]: [\"bottom\" /* bottom */, \"right\" /* right */],\n  [\"left\" /* left */]: [\"bottom\" /* bottom */, \"left\" /* left */],\n  [\"right\" /* right */]: [\"top\" /* top */, \"right\" /* right */]\n};\nexport { TooltipV2Sides, tooltipV2ArrowBorders, tooltipV2CommonProps, tooltipV2OppositeSide, tooltipV2Sides };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}