{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, with<PERSON><PERSON><PERSON>, withModifiers, renderSlot, createTextVNode, toDisplayString, createVNode, withCtx, createBlock, resolveDynamicComponent, withDirectives, vShow } from 'vue';\nimport { ElCollapseTransition } from '../../collapse-transition/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { collapseItemProps } from './collapse-item.mjs';\nimport { useCollapseItem, useCollapseItemDOM } from './use-collapse-item.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst __default__ = defineComponent({\n  name: \"ElCollapseItem\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: collapseItemProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const {\n      focusing,\n      id,\n      isActive,\n      handleFocus,\n      handleHeaderClick,\n      handleEnterClick\n    } = useCollapseItem(props);\n    const {\n      arrowKls,\n      headKls,\n      rootKls,\n      itemWrapperKls,\n      itemContentKls,\n      scopedContentId,\n      scopedHeadId\n    } = useCollapseItemDOM(props, {\n      focusing,\n      isActive,\n      id\n    });\n    expose({\n      isActive\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(rootKls))\n      }, [createElementVNode(\"button\", {\n        id: unref(scopedHeadId),\n        class: normalizeClass(unref(headKls)),\n        \"aria-expanded\": unref(isActive),\n        \"aria-controls\": unref(scopedContentId),\n        \"aria-describedby\": unref(scopedContentId),\n        tabindex: _ctx.disabled ? -1 : 0,\n        type: \"button\",\n        onClick: unref(handleHeaderClick),\n        onKeydown: withKeys(withModifiers(unref(handleEnterClick), [\"stop\", \"prevent\"]), [\"space\", \"enter\"]),\n        onFocus: unref(handleFocus),\n        onBlur: $event => focusing.value = false\n      }, [renderSlot(_ctx.$slots, \"title\", {}, () => [createTextVNode(toDisplayString(_ctx.title), 1)]), renderSlot(_ctx.$slots, \"icon\", {\n        isActive: unref(isActive)\n      }, () => [createVNode(unref(ElIcon), {\n        class: normalizeClass(unref(arrowKls))\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.icon)))]),\n        _: 1\n      }, 8, [\"class\"])])], 42, [\"id\", \"aria-expanded\", \"aria-controls\", \"aria-describedby\", \"tabindex\", \"onClick\", \"onKeydown\", \"onFocus\", \"onBlur\"]), createVNode(unref(ElCollapseTransition), null, {\n        default: withCtx(() => [withDirectives(createElementVNode(\"div\", {\n          id: unref(scopedContentId),\n          role: \"region\",\n          class: normalizeClass(unref(itemWrapperKls)),\n          \"aria-hidden\": !unref(isActive),\n          \"aria-labelledby\": unref(scopedHeadId)\n        }, [createElementVNode(\"div\", {\n          class: normalizeClass(unref(itemContentKls))\n        }, [renderSlot(_ctx.$slots, \"default\")], 2)], 10, [\"id\", \"aria-hidden\", \"aria-labelledby\"]), [[vShow, unref(isActive)]])]),\n        _: 3\n      })], 2);\n    };\n  }\n});\nvar CollapseItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"collapse-item.vue\"]]);\nexport { CollapseItem as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}