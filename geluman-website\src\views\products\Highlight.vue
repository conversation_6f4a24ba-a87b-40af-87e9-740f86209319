<template>
  <div class="product-page">
    <section class="hero">
      <div class="hero-content">
        <img
          :src="highlight"
          alt="高亮插件"
          class="plugin-logo animate-fadeInUp"
        />
        <h1 class="animate-fadeInUp delay-2">网页文本高亮插件</h1>
        <p class="subtitle animate-fadeInUp delay-4">
          让重要信息一目了然，提升阅读效率
        </p>
        <div class="cta-buttons animate-fadeInUp delay-6">
          <a
            href="https://gengxin.geluman.cn/downloads/GLM-Highlight-1.0.0.zip"
            class="download-btn"
            target="_blank"
          >
            <el-icon><Download /></el-icon>
            下载插件
          </a>
        </div>
      </div>
    </section>

    <section class="demo">
      <div class="container">
        <h2 class="section-title animate-fadeInUp">产品演示</h2>
        <p class="section-subtitle animate-fadeInUp delay-2">
          简单易用的设置界面，强大的高亮功能
        </p>
        <div class="demo-gallery">
          <div class="demo-item animate-fadeInUp delay-4">
            <div class="demo-image-container">
              <img
                :src="highlightShow1"
                alt="网页文本高亮插件 - 主界面"
                class="demo-image"
              />
            </div>
            <div class="image-caption">主界面 - 轻松创建多个高亮分类</div>
          </div>
          <div class="demo-item animate-fadeInUp delay-6">
            <div class="demo-image-container">
              <img
                :src="highlight2"
                alt="网页文本高亮插件 - 效果展示2"
                class="demo-image"
              />
            </div>
            <div class="image-caption">多彩高亮 - 不同类别信息区分明显</div>
          </div>
          <div class="demo-item animate-fadeInUp delay-8">
            <div class="demo-image-container">
              <img
                :src="highlightSetting"
                alt="网页文本高亮插件 - 设置界面"
                class="demo-image"
              />
            </div>
            <div class="image-caption">
              个性化设置 - 自定义高亮模式，启用黑白名单控制网页高亮
            </div>
          </div>
          <div class="demo-item animate-fadeInUp delay-10">
            <div class="demo-image-container">
              <img
                :src="highlightShow"
                alt="网页文本高亮插件 - 效果展示"
                class="demo-image"
              />
            </div>
            <div class="image-caption">高亮效果 - 重要信息一目了然</div>
          </div>
        </div>
      </div>
    </section>

    <section class="features">
      <div class="container">
        <h2>核心功能</h2>
        <div class="features-grid">
          <div
            v-for="(feature, index) in features"
            :key="feature.title"
            class="feature-card"
          >
            <div class="feature-icon">{{ feature.icon }}</div>
            <h3>{{ feature.title }}</h3>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <section class="usage">
      <div class="container">
        <h2>使用说明</h2>
        <div class="usage-steps">
          <div class="step" v-for="step in steps" :key="step.title">
            <div class="step-number">{{ step.number }}</div>
            <h3>{{ step.title }}</h3>
            <p>{{ step.description }}</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { highlight } from "@/assets";
import { Download } from "@element-plus/icons-vue";
import highlight2 from "@/assets/img/highlight-2.png";
import highlightSetting from "@/assets/img/highlight-setting.png";
import highlightShow from "@/assets/img/highlight-show.png";
import highlightShow1 from "@/assets/img/highlight-show1.png";

const features = [
  {
    icon: "📁",
    title: "多分类高亮",
    description:
      "支持创建多个高亮分类，每个分类可独立设置颜色和名称，灵活管理不同场景的高亮需求",
  },
  {
    icon: "🎨",
    title: "颜色自定义",
    description:
      "提供20种精心挑选的预设颜色，可为每个分类设置不同的高亮颜色，让重点内容更显眼",
  },
  {
    icon: "🔍",
    title: "关键词管理",
    description:
      "支持添加、编辑、删除关键词，每个分类可包含多个关键词，并支持关键词搜索和去重",
  },
  {
    icon: "🔄",
    title: "配置同步",
    description:
      "支持配置导入导出，可生成分享码与他人分享配置，轻松备份和迁移您的设置",
  },
  {
    icon: "⚡",
    title: "实时高亮",
    description:
      "输入关键词后立即在页面上高亮显示匹配文本，即时预览效果，快速调整",
  },
  {
    icon: "🎚️",
    title: "灵活控制",
    description:
      "总开关控制所有高亮显示/隐藏，每个分类独立开关，满足不同场景需求",
  },
];

const steps = [
  {
    number: "1",
    title: "安装插件",
    description:
      "从下方下载按钮下载并解压，打开Chrome或者Edge浏览器，进入chrome://extensions/或者edge://extensions/，将解压后的文件夹拖入页面中，完成安装",
  },
  {
    number: "2",
    title: "添加关键词",
    description: "在插件设置中添加需要高亮的关键词",
  },
  {
    number: "3",
    title: "选择颜色",
    description: "为关键词设置合适的高亮颜色",
  },
  {
    number: "4",
    title: "开始使用",
    description: "浏览网页时自动高亮显示关键词",
  },
];
</script>

<style lang="scss" scoped>
.hero {
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-dark) 100%
  );
  padding: 6rem 0;
  text-align: center;
  color: white;

  .plugin-logo {
    width: 120px;
    height: auto;
    margin-bottom: 2rem;
  }

  h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
  }

  .download-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem 2rem;
    background: white;
    color: var(--primary-color);
    text-decoration: none;
    border-radius: 30px;
    font-weight: 500;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);

    .el-icon {
      font-size: 1.2rem;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
  }
}

.demo {
  padding: 6rem 0;
  background: white;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
  }

  .section-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
  }

  .section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }

  .demo-gallery {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, auto);
    gap: 3rem;

    @media (max-width: 992px) {
      grid-template-columns: 1fr;
      grid-template-rows: auto;
      gap: 4rem;
    }
  }

  .demo-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .demo-image-container {
      width: 100%;
      height: 350px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      border-radius: 12px;
      overflow: hidden;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f8f9fa;

      &:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
      }
    }

    .demo-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      display: block;
      border-radius: 12px;
    }

    .image-caption {
      margin-top: 1.5rem;
      font-size: 1.1rem;
      color: var(--text-secondary);
    }
  }
}

.features {
  padding: 6rem 0;
  background: var(--bg-secondary);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;

    @media (max-width: 992px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
  transition: var(--transition-base);

  &:hover {
    transform: translateY(-10px);

    .feature-icon {
      animation: iconBounce 0.5s ease;
    }
  }

  .feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
  }

  p {
    color: var(--text-secondary);
    line-height: 1.6;
  }
}

.usage {
  padding: 6rem 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
  }

  .usage-steps {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;

    @media (max-width: 992px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .step {
    text-align: center;
    padding: 2rem;

    .step-number {
      width: 40px;
      height: 40px;
      background: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1rem;
      font-weight: 500;
    }

    h3 {
      font-size: 1.3rem;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    p {
      color: var(--text-secondary);
      line-height: 1.6;
    }
  }
}

@keyframes btnShine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

@keyframes iconBounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}
</style>

<script>
export default {
  name: "HighlightPage",
};
</script>
