{"ast": null, "code": "import { timePickerDefaultProps } from '../../../time-picker/src/common/props.mjs';\nimport { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nconst datePickerProps = buildProps({\n  ...timePickerDefaultProps,\n  type: {\n    type: definePropType(String),\n    default: \"date\"\n  }\n});\nexport { datePickerProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}