{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nvar E = \"top\",\n  R = \"bottom\",\n  W = \"right\",\n  P = \"left\",\n  me = \"auto\",\n  G = [E, R, W, P],\n  U = \"start\",\n  J = \"end\",\n  Xe = \"clippingParents\",\n  je = \"viewport\",\n  K = \"popper\",\n  Ye = \"reference\",\n  De = G.reduce(function (t, e) {\n    return t.concat([e + \"-\" + U, e + \"-\" + J]);\n  }, []),\n  Ee = [].concat(G, [me]).reduce(function (t, e) {\n    return t.concat([e, e + \"-\" + U, e + \"-\" + J]);\n  }, []),\n  Ge = \"beforeRead\",\n  Je = \"read\",\n  Ke = \"afterRead\",\n  Qe = \"beforeMain\",\n  Ze = \"main\",\n  et = \"afterMain\",\n  tt = \"beforeWrite\",\n  nt = \"write\",\n  rt = \"afterWrite\",\n  ot = [Ge, Je, Ke, Qe, Ze, et, tt, nt, rt];\nfunction C(t) {\n  return t ? (t.nodeName || \"\").toLowerCase() : null;\n}\nfunction H(t) {\n  if (t == null) return window;\n  if (t.toString() !== \"[object Window]\") {\n    var e = t.ownerDocument;\n    return e && e.defaultView || window;\n  }\n  return t;\n}\nfunction Q(t) {\n  var e = H(t).Element;\n  return t instanceof e || t instanceof Element;\n}\nfunction B(t) {\n  var e = H(t).HTMLElement;\n  return t instanceof e || t instanceof HTMLElement;\n}\nfunction Pe(t) {\n  if (typeof ShadowRoot == \"undefined\") return !1;\n  var e = H(t).ShadowRoot;\n  return t instanceof e || t instanceof ShadowRoot;\n}\nfunction Mt(t) {\n  var e = t.state;\n  Object.keys(e.elements).forEach(function (n) {\n    var r = e.styles[n] || {},\n      o = e.attributes[n] || {},\n      i = e.elements[n];\n    !B(i) || !C(i) || (Object.assign(i.style, r), Object.keys(o).forEach(function (a) {\n      var s = o[a];\n      s === !1 ? i.removeAttribute(a) : i.setAttribute(a, s === !0 ? \"\" : s);\n    }));\n  });\n}\nfunction Rt(t) {\n  var e = t.state,\n    n = {\n      popper: {\n        position: e.options.strategy,\n        left: \"0\",\n        top: \"0\",\n        margin: \"0\"\n      },\n      arrow: {\n        position: \"absolute\"\n      },\n      reference: {}\n    };\n  return Object.assign(e.elements.popper.style, n.popper), e.styles = n, e.elements.arrow && Object.assign(e.elements.arrow.style, n.arrow), function () {\n    Object.keys(e.elements).forEach(function (r) {\n      var o = e.elements[r],\n        i = e.attributes[r] || {},\n        a = Object.keys(e.styles.hasOwnProperty(r) ? e.styles[r] : n[r]),\n        s = a.reduce(function (f, c) {\n          return f[c] = \"\", f;\n        }, {});\n      !B(o) || !C(o) || (Object.assign(o.style, s), Object.keys(i).forEach(function (f) {\n        o.removeAttribute(f);\n      }));\n    });\n  };\n}\nvar Ae = {\n  name: \"applyStyles\",\n  enabled: !0,\n  phase: \"write\",\n  fn: Mt,\n  effect: Rt,\n  requires: [\"computeStyles\"]\n};\nfunction q(t) {\n  return t.split(\"-\")[0];\n}\nvar X = Math.max,\n  ve = Math.min,\n  Z = Math.round;\nfunction ee(t, e) {\n  e === void 0 && (e = !1);\n  var n = t.getBoundingClientRect(),\n    r = 1,\n    o = 1;\n  if (B(t) && e) {\n    var i = t.offsetHeight,\n      a = t.offsetWidth;\n    a > 0 && (r = Z(n.width) / a || 1), i > 0 && (o = Z(n.height) / i || 1);\n  }\n  return {\n    width: n.width / r,\n    height: n.height / o,\n    top: n.top / o,\n    right: n.right / r,\n    bottom: n.bottom / o,\n    left: n.left / r,\n    x: n.left / r,\n    y: n.top / o\n  };\n}\nfunction ke(t) {\n  var e = ee(t),\n    n = t.offsetWidth,\n    r = t.offsetHeight;\n  return Math.abs(e.width - n) <= 1 && (n = e.width), Math.abs(e.height - r) <= 1 && (r = e.height), {\n    x: t.offsetLeft,\n    y: t.offsetTop,\n    width: n,\n    height: r\n  };\n}\nfunction it(t, e) {\n  var n = e.getRootNode && e.getRootNode();\n  if (t.contains(e)) return !0;\n  if (n && Pe(n)) {\n    var r = e;\n    do {\n      if (r && t.isSameNode(r)) return !0;\n      r = r.parentNode || r.host;\n    } while (r);\n  }\n  return !1;\n}\nfunction N(t) {\n  return H(t).getComputedStyle(t);\n}\nfunction Wt(t) {\n  return [\"table\", \"td\", \"th\"].indexOf(C(t)) >= 0;\n}\nfunction I(t) {\n  return ((Q(t) ? t.ownerDocument : t.document) || window.document).documentElement;\n}\nfunction ge(t) {\n  return C(t) === \"html\" ? t : t.assignedSlot || t.parentNode || (Pe(t) ? t.host : null) || I(t);\n}\nfunction at(t) {\n  return !B(t) || N(t).position === \"fixed\" ? null : t.offsetParent;\n}\nfunction Bt(t) {\n  var e = navigator.userAgent.toLowerCase().indexOf(\"firefox\") !== -1,\n    n = navigator.userAgent.indexOf(\"Trident\") !== -1;\n  if (n && B(t)) {\n    var r = N(t);\n    if (r.position === \"fixed\") return null;\n  }\n  var o = ge(t);\n  for (Pe(o) && (o = o.host); B(o) && [\"html\", \"body\"].indexOf(C(o)) < 0;) {\n    var i = N(o);\n    if (i.transform !== \"none\" || i.perspective !== \"none\" || i.contain === \"paint\" || [\"transform\", \"perspective\"].indexOf(i.willChange) !== -1 || e && i.willChange === \"filter\" || e && i.filter && i.filter !== \"none\") return o;\n    o = o.parentNode;\n  }\n  return null;\n}\nfunction se(t) {\n  for (var e = H(t), n = at(t); n && Wt(n) && N(n).position === \"static\";) n = at(n);\n  return n && (C(n) === \"html\" || C(n) === \"body\" && N(n).position === \"static\") ? e : n || Bt(t) || e;\n}\nfunction Le(t) {\n  return [\"top\", \"bottom\"].indexOf(t) >= 0 ? \"x\" : \"y\";\n}\nfunction fe(t, e, n) {\n  return X(t, ve(e, n));\n}\nfunction St(t, e, n) {\n  var r = fe(t, e, n);\n  return r > n ? n : r;\n}\nfunction st() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}\nfunction ft(t) {\n  return Object.assign({}, st(), t);\n}\nfunction ct(t, e) {\n  return e.reduce(function (n, r) {\n    return n[r] = t, n;\n  }, {});\n}\nvar Tt = function (t, e) {\n  return t = typeof t == \"function\" ? t(Object.assign({}, e.rects, {\n    placement: e.placement\n  })) : t, ft(typeof t != \"number\" ? t : ct(t, G));\n};\nfunction Ht(t) {\n  var e,\n    n = t.state,\n    r = t.name,\n    o = t.options,\n    i = n.elements.arrow,\n    a = n.modifiersData.popperOffsets,\n    s = q(n.placement),\n    f = Le(s),\n    c = [P, W].indexOf(s) >= 0,\n    u = c ? \"height\" : \"width\";\n  if (!(!i || !a)) {\n    var m = Tt(o.padding, n),\n      v = ke(i),\n      l = f === \"y\" ? E : P,\n      h = f === \"y\" ? R : W,\n      p = n.rects.reference[u] + n.rects.reference[f] - a[f] - n.rects.popper[u],\n      g = a[f] - n.rects.reference[f],\n      x = se(i),\n      y = x ? f === \"y\" ? x.clientHeight || 0 : x.clientWidth || 0 : 0,\n      $ = p / 2 - g / 2,\n      d = m[l],\n      b = y - v[u] - m[h],\n      w = y / 2 - v[u] / 2 + $,\n      O = fe(d, w, b),\n      j = f;\n    n.modifiersData[r] = (e = {}, e[j] = O, e.centerOffset = O - w, e);\n  }\n}\nfunction Ct(t) {\n  var e = t.state,\n    n = t.options,\n    r = n.element,\n    o = r === void 0 ? \"[data-popper-arrow]\" : r;\n  o != null && (typeof o == \"string\" && (o = e.elements.popper.querySelector(o), !o) || !it(e.elements.popper, o) || (e.elements.arrow = o));\n}\nvar pt = {\n  name: \"arrow\",\n  enabled: !0,\n  phase: \"main\",\n  fn: Ht,\n  effect: Ct,\n  requires: [\"popperOffsets\"],\n  requiresIfExists: [\"preventOverflow\"]\n};\nfunction te(t) {\n  return t.split(\"-\")[1];\n}\nvar qt = {\n  top: \"auto\",\n  right: \"auto\",\n  bottom: \"auto\",\n  left: \"auto\"\n};\nfunction Vt(t) {\n  var e = t.x,\n    n = t.y,\n    r = window,\n    o = r.devicePixelRatio || 1;\n  return {\n    x: Z(e * o) / o || 0,\n    y: Z(n * o) / o || 0\n  };\n}\nfunction ut(t) {\n  var e,\n    n = t.popper,\n    r = t.popperRect,\n    o = t.placement,\n    i = t.variation,\n    a = t.offsets,\n    s = t.position,\n    f = t.gpuAcceleration,\n    c = t.adaptive,\n    u = t.roundOffsets,\n    m = t.isFixed,\n    v = a.x,\n    l = v === void 0 ? 0 : v,\n    h = a.y,\n    p = h === void 0 ? 0 : h,\n    g = typeof u == \"function\" ? u({\n      x: l,\n      y: p\n    }) : {\n      x: l,\n      y: p\n    };\n  l = g.x, p = g.y;\n  var x = a.hasOwnProperty(\"x\"),\n    y = a.hasOwnProperty(\"y\"),\n    $ = P,\n    d = E,\n    b = window;\n  if (c) {\n    var w = se(n),\n      O = \"clientHeight\",\n      j = \"clientWidth\";\n    if (w === H(n) && (w = I(n), N(w).position !== \"static\" && s === \"absolute\" && (O = \"scrollHeight\", j = \"scrollWidth\")), w = w, o === E || (o === P || o === W) && i === J) {\n      d = R;\n      var A = m && w === b && b.visualViewport ? b.visualViewport.height : w[O];\n      p -= A - r.height, p *= f ? 1 : -1;\n    }\n    if (o === P || (o === E || o === R) && i === J) {\n      $ = W;\n      var k = m && w === b && b.visualViewport ? b.visualViewport.width : w[j];\n      l -= k - r.width, l *= f ? 1 : -1;\n    }\n  }\n  var D = Object.assign({\n      position: s\n    }, c && qt),\n    S = u === !0 ? Vt({\n      x: l,\n      y: p\n    }) : {\n      x: l,\n      y: p\n    };\n  if (l = S.x, p = S.y, f) {\n    var L;\n    return Object.assign({}, D, (L = {}, L[d] = y ? \"0\" : \"\", L[$] = x ? \"0\" : \"\", L.transform = (b.devicePixelRatio || 1) <= 1 ? \"translate(\" + l + \"px, \" + p + \"px)\" : \"translate3d(\" + l + \"px, \" + p + \"px, 0)\", L));\n  }\n  return Object.assign({}, D, (e = {}, e[d] = y ? p + \"px\" : \"\", e[$] = x ? l + \"px\" : \"\", e.transform = \"\", e));\n}\nfunction Nt(t) {\n  var e = t.state,\n    n = t.options,\n    r = n.gpuAcceleration,\n    o = r === void 0 ? !0 : r,\n    i = n.adaptive,\n    a = i === void 0 ? !0 : i,\n    s = n.roundOffsets,\n    f = s === void 0 ? !0 : s,\n    c = {\n      placement: q(e.placement),\n      variation: te(e.placement),\n      popper: e.elements.popper,\n      popperRect: e.rects.popper,\n      gpuAcceleration: o,\n      isFixed: e.options.strategy === \"fixed\"\n    };\n  e.modifiersData.popperOffsets != null && (e.styles.popper = Object.assign({}, e.styles.popper, ut(Object.assign({}, c, {\n    offsets: e.modifiersData.popperOffsets,\n    position: e.options.strategy,\n    adaptive: a,\n    roundOffsets: f\n  })))), e.modifiersData.arrow != null && (e.styles.arrow = Object.assign({}, e.styles.arrow, ut(Object.assign({}, c, {\n    offsets: e.modifiersData.arrow,\n    position: \"absolute\",\n    adaptive: !1,\n    roundOffsets: f\n  })))), e.attributes.popper = Object.assign({}, e.attributes.popper, {\n    \"data-popper-placement\": e.placement\n  });\n}\nvar Me = {\n    name: \"computeStyles\",\n    enabled: !0,\n    phase: \"beforeWrite\",\n    fn: Nt,\n    data: {}\n  },\n  ye = {\n    passive: !0\n  };\nfunction It(t) {\n  var e = t.state,\n    n = t.instance,\n    r = t.options,\n    o = r.scroll,\n    i = o === void 0 ? !0 : o,\n    a = r.resize,\n    s = a === void 0 ? !0 : a,\n    f = H(e.elements.popper),\n    c = [].concat(e.scrollParents.reference, e.scrollParents.popper);\n  return i && c.forEach(function (u) {\n    u.addEventListener(\"scroll\", n.update, ye);\n  }), s && f.addEventListener(\"resize\", n.update, ye), function () {\n    i && c.forEach(function (u) {\n      u.removeEventListener(\"scroll\", n.update, ye);\n    }), s && f.removeEventListener(\"resize\", n.update, ye);\n  };\n}\nvar Re = {\n    name: \"eventListeners\",\n    enabled: !0,\n    phase: \"write\",\n    fn: function () {},\n    effect: It,\n    data: {}\n  },\n  _t = {\n    left: \"right\",\n    right: \"left\",\n    bottom: \"top\",\n    top: \"bottom\"\n  };\nfunction be(t) {\n  return t.replace(/left|right|bottom|top/g, function (e) {\n    return _t[e];\n  });\n}\nvar zt = {\n  start: \"end\",\n  end: \"start\"\n};\nfunction lt(t) {\n  return t.replace(/start|end/g, function (e) {\n    return zt[e];\n  });\n}\nfunction We(t) {\n  var e = H(t),\n    n = e.pageXOffset,\n    r = e.pageYOffset;\n  return {\n    scrollLeft: n,\n    scrollTop: r\n  };\n}\nfunction Be(t) {\n  return ee(I(t)).left + We(t).scrollLeft;\n}\nfunction Ft(t) {\n  var e = H(t),\n    n = I(t),\n    r = e.visualViewport,\n    o = n.clientWidth,\n    i = n.clientHeight,\n    a = 0,\n    s = 0;\n  return r && (o = r.width, i = r.height, /^((?!chrome|android).)*safari/i.test(navigator.userAgent) || (a = r.offsetLeft, s = r.offsetTop)), {\n    width: o,\n    height: i,\n    x: a + Be(t),\n    y: s\n  };\n}\nfunction Ut(t) {\n  var e,\n    n = I(t),\n    r = We(t),\n    o = (e = t.ownerDocument) == null ? void 0 : e.body,\n    i = X(n.scrollWidth, n.clientWidth, o ? o.scrollWidth : 0, o ? o.clientWidth : 0),\n    a = X(n.scrollHeight, n.clientHeight, o ? o.scrollHeight : 0, o ? o.clientHeight : 0),\n    s = -r.scrollLeft + Be(t),\n    f = -r.scrollTop;\n  return N(o || n).direction === \"rtl\" && (s += X(n.clientWidth, o ? o.clientWidth : 0) - i), {\n    width: i,\n    height: a,\n    x: s,\n    y: f\n  };\n}\nfunction Se(t) {\n  var e = N(t),\n    n = e.overflow,\n    r = e.overflowX,\n    o = e.overflowY;\n  return /auto|scroll|overlay|hidden/.test(n + o + r);\n}\nfunction dt(t) {\n  return [\"html\", \"body\", \"#document\"].indexOf(C(t)) >= 0 ? t.ownerDocument.body : B(t) && Se(t) ? t : dt(ge(t));\n}\nfunction ce(t, e) {\n  var n;\n  e === void 0 && (e = []);\n  var r = dt(t),\n    o = r === ((n = t.ownerDocument) == null ? void 0 : n.body),\n    i = H(r),\n    a = o ? [i].concat(i.visualViewport || [], Se(r) ? r : []) : r,\n    s = e.concat(a);\n  return o ? s : s.concat(ce(ge(a)));\n}\nfunction Te(t) {\n  return Object.assign({}, t, {\n    left: t.x,\n    top: t.y,\n    right: t.x + t.width,\n    bottom: t.y + t.height\n  });\n}\nfunction Xt(t) {\n  var e = ee(t);\n  return e.top = e.top + t.clientTop, e.left = e.left + t.clientLeft, e.bottom = e.top + t.clientHeight, e.right = e.left + t.clientWidth, e.width = t.clientWidth, e.height = t.clientHeight, e.x = e.left, e.y = e.top, e;\n}\nfunction ht(t, e) {\n  return e === je ? Te(Ft(t)) : Q(e) ? Xt(e) : Te(Ut(I(t)));\n}\nfunction Yt(t) {\n  var e = ce(ge(t)),\n    n = [\"absolute\", \"fixed\"].indexOf(N(t).position) >= 0,\n    r = n && B(t) ? se(t) : t;\n  return Q(r) ? e.filter(function (o) {\n    return Q(o) && it(o, r) && C(o) !== \"body\";\n  }) : [];\n}\nfunction Gt(t, e, n) {\n  var r = e === \"clippingParents\" ? Yt(t) : [].concat(e),\n    o = [].concat(r, [n]),\n    i = o[0],\n    a = o.reduce(function (s, f) {\n      var c = ht(t, f);\n      return s.top = X(c.top, s.top), s.right = ve(c.right, s.right), s.bottom = ve(c.bottom, s.bottom), s.left = X(c.left, s.left), s;\n    }, ht(t, i));\n  return a.width = a.right - a.left, a.height = a.bottom - a.top, a.x = a.left, a.y = a.top, a;\n}\nfunction mt(t) {\n  var e = t.reference,\n    n = t.element,\n    r = t.placement,\n    o = r ? q(r) : null,\n    i = r ? te(r) : null,\n    a = e.x + e.width / 2 - n.width / 2,\n    s = e.y + e.height / 2 - n.height / 2,\n    f;\n  switch (o) {\n    case E:\n      f = {\n        x: a,\n        y: e.y - n.height\n      };\n      break;\n    case R:\n      f = {\n        x: a,\n        y: e.y + e.height\n      };\n      break;\n    case W:\n      f = {\n        x: e.x + e.width,\n        y: s\n      };\n      break;\n    case P:\n      f = {\n        x: e.x - n.width,\n        y: s\n      };\n      break;\n    default:\n      f = {\n        x: e.x,\n        y: e.y\n      };\n  }\n  var c = o ? Le(o) : null;\n  if (c != null) {\n    var u = c === \"y\" ? \"height\" : \"width\";\n    switch (i) {\n      case U:\n        f[c] = f[c] - (e[u] / 2 - n[u] / 2);\n        break;\n      case J:\n        f[c] = f[c] + (e[u] / 2 - n[u] / 2);\n        break;\n    }\n  }\n  return f;\n}\nfunction ne(t, e) {\n  e === void 0 && (e = {});\n  var n = e,\n    r = n.placement,\n    o = r === void 0 ? t.placement : r,\n    i = n.boundary,\n    a = i === void 0 ? Xe : i,\n    s = n.rootBoundary,\n    f = s === void 0 ? je : s,\n    c = n.elementContext,\n    u = c === void 0 ? K : c,\n    m = n.altBoundary,\n    v = m === void 0 ? !1 : m,\n    l = n.padding,\n    h = l === void 0 ? 0 : l,\n    p = ft(typeof h != \"number\" ? h : ct(h, G)),\n    g = u === K ? Ye : K,\n    x = t.rects.popper,\n    y = t.elements[v ? g : u],\n    $ = Gt(Q(y) ? y : y.contextElement || I(t.elements.popper), a, f),\n    d = ee(t.elements.reference),\n    b = mt({\n      reference: d,\n      element: x,\n      strategy: \"absolute\",\n      placement: o\n    }),\n    w = Te(Object.assign({}, x, b)),\n    O = u === K ? w : d,\n    j = {\n      top: $.top - O.top + p.top,\n      bottom: O.bottom - $.bottom + p.bottom,\n      left: $.left - O.left + p.left,\n      right: O.right - $.right + p.right\n    },\n    A = t.modifiersData.offset;\n  if (u === K && A) {\n    var k = A[o];\n    Object.keys(j).forEach(function (D) {\n      var S = [W, R].indexOf(D) >= 0 ? 1 : -1,\n        L = [E, R].indexOf(D) >= 0 ? \"y\" : \"x\";\n      j[D] += k[L] * S;\n    });\n  }\n  return j;\n}\nfunction Jt(t, e) {\n  e === void 0 && (e = {});\n  var n = e,\n    r = n.placement,\n    o = n.boundary,\n    i = n.rootBoundary,\n    a = n.padding,\n    s = n.flipVariations,\n    f = n.allowedAutoPlacements,\n    c = f === void 0 ? Ee : f,\n    u = te(r),\n    m = u ? s ? De : De.filter(function (h) {\n      return te(h) === u;\n    }) : G,\n    v = m.filter(function (h) {\n      return c.indexOf(h) >= 0;\n    });\n  v.length === 0 && (v = m);\n  var l = v.reduce(function (h, p) {\n    return h[p] = ne(t, {\n      placement: p,\n      boundary: o,\n      rootBoundary: i,\n      padding: a\n    })[q(p)], h;\n  }, {});\n  return Object.keys(l).sort(function (h, p) {\n    return l[h] - l[p];\n  });\n}\nfunction Kt(t) {\n  if (q(t) === me) return [];\n  var e = be(t);\n  return [lt(t), e, lt(e)];\n}\nfunction Qt(t) {\n  var e = t.state,\n    n = t.options,\n    r = t.name;\n  if (!e.modifiersData[r]._skip) {\n    for (var o = n.mainAxis, i = o === void 0 ? !0 : o, a = n.altAxis, s = a === void 0 ? !0 : a, f = n.fallbackPlacements, c = n.padding, u = n.boundary, m = n.rootBoundary, v = n.altBoundary, l = n.flipVariations, h = l === void 0 ? !0 : l, p = n.allowedAutoPlacements, g = e.options.placement, x = q(g), y = x === g, $ = f || (y || !h ? [be(g)] : Kt(g)), d = [g].concat($).reduce(function (z, V) {\n        return z.concat(q(V) === me ? Jt(e, {\n          placement: V,\n          boundary: u,\n          rootBoundary: m,\n          padding: c,\n          flipVariations: h,\n          allowedAutoPlacements: p\n        }) : V);\n      }, []), b = e.rects.reference, w = e.rects.popper, O = new Map(), j = !0, A = d[0], k = 0; k < d.length; k++) {\n      var D = d[k],\n        S = q(D),\n        L = te(D) === U,\n        re = [E, R].indexOf(S) >= 0,\n        oe = re ? \"width\" : \"height\",\n        M = ne(e, {\n          placement: D,\n          boundary: u,\n          rootBoundary: m,\n          altBoundary: v,\n          padding: c\n        }),\n        T = re ? L ? W : P : L ? R : E;\n      b[oe] > w[oe] && (T = be(T));\n      var pe = be(T),\n        _ = [];\n      if (i && _.push(M[S] <= 0), s && _.push(M[T] <= 0, M[pe] <= 0), _.every(function (z) {\n        return z;\n      })) {\n        A = D, j = !1;\n        break;\n      }\n      O.set(D, _);\n    }\n    if (j) for (var ue = h ? 3 : 1, xe = function (z) {\n        var V = d.find(function (de) {\n          var ae = O.get(de);\n          if (ae) return ae.slice(0, z).every(function (Y) {\n            return Y;\n          });\n        });\n        if (V) return A = V, \"break\";\n      }, ie = ue; ie > 0; ie--) {\n      var le = xe(ie);\n      if (le === \"break\") break;\n    }\n    e.placement !== A && (e.modifiersData[r]._skip = !0, e.placement = A, e.reset = !0);\n  }\n}\nvar vt = {\n  name: \"flip\",\n  enabled: !0,\n  phase: \"main\",\n  fn: Qt,\n  requiresIfExists: [\"offset\"],\n  data: {\n    _skip: !1\n  }\n};\nfunction gt(t, e, n) {\n  return n === void 0 && (n = {\n    x: 0,\n    y: 0\n  }), {\n    top: t.top - e.height - n.y,\n    right: t.right - e.width + n.x,\n    bottom: t.bottom - e.height + n.y,\n    left: t.left - e.width - n.x\n  };\n}\nfunction yt(t) {\n  return [E, W, R, P].some(function (e) {\n    return t[e] >= 0;\n  });\n}\nfunction Zt(t) {\n  var e = t.state,\n    n = t.name,\n    r = e.rects.reference,\n    o = e.rects.popper,\n    i = e.modifiersData.preventOverflow,\n    a = ne(e, {\n      elementContext: \"reference\"\n    }),\n    s = ne(e, {\n      altBoundary: !0\n    }),\n    f = gt(a, r),\n    c = gt(s, o, i),\n    u = yt(f),\n    m = yt(c);\n  e.modifiersData[n] = {\n    referenceClippingOffsets: f,\n    popperEscapeOffsets: c,\n    isReferenceHidden: u,\n    hasPopperEscaped: m\n  }, e.attributes.popper = Object.assign({}, e.attributes.popper, {\n    \"data-popper-reference-hidden\": u,\n    \"data-popper-escaped\": m\n  });\n}\nvar bt = {\n  name: \"hide\",\n  enabled: !0,\n  phase: \"main\",\n  requiresIfExists: [\"preventOverflow\"],\n  fn: Zt\n};\nfunction en(t, e, n) {\n  var r = q(t),\n    o = [P, E].indexOf(r) >= 0 ? -1 : 1,\n    i = typeof n == \"function\" ? n(Object.assign({}, e, {\n      placement: t\n    })) : n,\n    a = i[0],\n    s = i[1];\n  return a = a || 0, s = (s || 0) * o, [P, W].indexOf(r) >= 0 ? {\n    x: s,\n    y: a\n  } : {\n    x: a,\n    y: s\n  };\n}\nfunction tn(t) {\n  var e = t.state,\n    n = t.options,\n    r = t.name,\n    o = n.offset,\n    i = o === void 0 ? [0, 0] : o,\n    a = Ee.reduce(function (u, m) {\n      return u[m] = en(m, e.rects, i), u;\n    }, {}),\n    s = a[e.placement],\n    f = s.x,\n    c = s.y;\n  e.modifiersData.popperOffsets != null && (e.modifiersData.popperOffsets.x += f, e.modifiersData.popperOffsets.y += c), e.modifiersData[r] = a;\n}\nvar wt = {\n  name: \"offset\",\n  enabled: !0,\n  phase: \"main\",\n  requires: [\"popperOffsets\"],\n  fn: tn\n};\nfunction nn(t) {\n  var e = t.state,\n    n = t.name;\n  e.modifiersData[n] = mt({\n    reference: e.rects.reference,\n    element: e.rects.popper,\n    strategy: \"absolute\",\n    placement: e.placement\n  });\n}\nvar He = {\n  name: \"popperOffsets\",\n  enabled: !0,\n  phase: \"read\",\n  fn: nn,\n  data: {}\n};\nfunction rn(t) {\n  return t === \"x\" ? \"y\" : \"x\";\n}\nfunction on(t) {\n  var e = t.state,\n    n = t.options,\n    r = t.name,\n    o = n.mainAxis,\n    i = o === void 0 ? !0 : o,\n    a = n.altAxis,\n    s = a === void 0 ? !1 : a,\n    f = n.boundary,\n    c = n.rootBoundary,\n    u = n.altBoundary,\n    m = n.padding,\n    v = n.tether,\n    l = v === void 0 ? !0 : v,\n    h = n.tetherOffset,\n    p = h === void 0 ? 0 : h,\n    g = ne(e, {\n      boundary: f,\n      rootBoundary: c,\n      padding: m,\n      altBoundary: u\n    }),\n    x = q(e.placement),\n    y = te(e.placement),\n    $ = !y,\n    d = Le(x),\n    b = rn(d),\n    w = e.modifiersData.popperOffsets,\n    O = e.rects.reference,\n    j = e.rects.popper,\n    A = typeof p == \"function\" ? p(Object.assign({}, e.rects, {\n      placement: e.placement\n    })) : p,\n    k = typeof A == \"number\" ? {\n      mainAxis: A,\n      altAxis: A\n    } : Object.assign({\n      mainAxis: 0,\n      altAxis: 0\n    }, A),\n    D = e.modifiersData.offset ? e.modifiersData.offset[e.placement] : null,\n    S = {\n      x: 0,\n      y: 0\n    };\n  if (w) {\n    if (i) {\n      var L,\n        re = d === \"y\" ? E : P,\n        oe = d === \"y\" ? R : W,\n        M = d === \"y\" ? \"height\" : \"width\",\n        T = w[d],\n        pe = T + g[re],\n        _ = T - g[oe],\n        ue = l ? -j[M] / 2 : 0,\n        xe = y === U ? O[M] : j[M],\n        ie = y === U ? -j[M] : -O[M],\n        le = e.elements.arrow,\n        z = l && le ? ke(le) : {\n          width: 0,\n          height: 0\n        },\n        V = e.modifiersData[\"arrow#persistent\"] ? e.modifiersData[\"arrow#persistent\"].padding : st(),\n        de = V[re],\n        ae = V[oe],\n        Y = fe(0, O[M], z[M]),\n        jt = $ ? O[M] / 2 - ue - Y - de - k.mainAxis : xe - Y - de - k.mainAxis,\n        Dt = $ ? -O[M] / 2 + ue + Y + ae + k.mainAxis : ie + Y + ae + k.mainAxis,\n        Oe = e.elements.arrow && se(e.elements.arrow),\n        Et = Oe ? d === \"y\" ? Oe.clientTop || 0 : Oe.clientLeft || 0 : 0,\n        Ce = (L = D == null ? void 0 : D[d]) != null ? L : 0,\n        Pt = T + jt - Ce - Et,\n        At = T + Dt - Ce,\n        qe = fe(l ? ve(pe, Pt) : pe, T, l ? X(_, At) : _);\n      w[d] = qe, S[d] = qe - T;\n    }\n    if (s) {\n      var Ve,\n        kt = d === \"x\" ? E : P,\n        Lt = d === \"x\" ? R : W,\n        F = w[b],\n        he = b === \"y\" ? \"height\" : \"width\",\n        Ne = F + g[kt],\n        Ie = F - g[Lt],\n        $e = [E, P].indexOf(x) !== -1,\n        _e = (Ve = D == null ? void 0 : D[b]) != null ? Ve : 0,\n        ze = $e ? Ne : F - O[he] - j[he] - _e + k.altAxis,\n        Fe = $e ? F + O[he] + j[he] - _e - k.altAxis : Ie,\n        Ue = l && $e ? St(ze, F, Fe) : fe(l ? ze : Ne, F, l ? Fe : Ie);\n      w[b] = Ue, S[b] = Ue - F;\n    }\n    e.modifiersData[r] = S;\n  }\n}\nvar xt = {\n  name: \"preventOverflow\",\n  enabled: !0,\n  phase: \"main\",\n  fn: on,\n  requiresIfExists: [\"offset\"]\n};\nfunction an(t) {\n  return {\n    scrollLeft: t.scrollLeft,\n    scrollTop: t.scrollTop\n  };\n}\nfunction sn(t) {\n  return t === H(t) || !B(t) ? We(t) : an(t);\n}\nfunction fn(t) {\n  var e = t.getBoundingClientRect(),\n    n = Z(e.width) / t.offsetWidth || 1,\n    r = Z(e.height) / t.offsetHeight || 1;\n  return n !== 1 || r !== 1;\n}\nfunction cn(t, e, n) {\n  n === void 0 && (n = !1);\n  var r = B(e),\n    o = B(e) && fn(e),\n    i = I(e),\n    a = ee(t, o),\n    s = {\n      scrollLeft: 0,\n      scrollTop: 0\n    },\n    f = {\n      x: 0,\n      y: 0\n    };\n  return (r || !r && !n) && ((C(e) !== \"body\" || Se(i)) && (s = sn(e)), B(e) ? (f = ee(e, !0), f.x += e.clientLeft, f.y += e.clientTop) : i && (f.x = Be(i))), {\n    x: a.left + s.scrollLeft - f.x,\n    y: a.top + s.scrollTop - f.y,\n    width: a.width,\n    height: a.height\n  };\n}\nfunction pn(t) {\n  var e = new Map(),\n    n = new Set(),\n    r = [];\n  t.forEach(function (i) {\n    e.set(i.name, i);\n  });\n  function o(i) {\n    n.add(i.name);\n    var a = [].concat(i.requires || [], i.requiresIfExists || []);\n    a.forEach(function (s) {\n      if (!n.has(s)) {\n        var f = e.get(s);\n        f && o(f);\n      }\n    }), r.push(i);\n  }\n  return t.forEach(function (i) {\n    n.has(i.name) || o(i);\n  }), r;\n}\nfunction un(t) {\n  var e = pn(t);\n  return ot.reduce(function (n, r) {\n    return n.concat(e.filter(function (o) {\n      return o.phase === r;\n    }));\n  }, []);\n}\nfunction ln(t) {\n  var e;\n  return function () {\n    return e || (e = new Promise(function (n) {\n      Promise.resolve().then(function () {\n        e = void 0, n(t());\n      });\n    })), e;\n  };\n}\nfunction dn(t) {\n  var e = t.reduce(function (n, r) {\n    var o = n[r.name];\n    return n[r.name] = o ? Object.assign({}, o, r, {\n      options: Object.assign({}, o.options, r.options),\n      data: Object.assign({}, o.data, r.data)\n    }) : r, n;\n  }, {});\n  return Object.keys(e).map(function (n) {\n    return e[n];\n  });\n}\nvar Ot = {\n  placement: \"bottom\",\n  modifiers: [],\n  strategy: \"absolute\"\n};\nfunction $t() {\n  for (var t = arguments.length, e = new Array(t), n = 0; n < t; n++) e[n] = arguments[n];\n  return !e.some(function (r) {\n    return !(r && typeof r.getBoundingClientRect == \"function\");\n  });\n}\nfunction we(t) {\n  t === void 0 && (t = {});\n  var e = t,\n    n = e.defaultModifiers,\n    r = n === void 0 ? [] : n,\n    o = e.defaultOptions,\n    i = o === void 0 ? Ot : o;\n  return function (a, s, f) {\n    f === void 0 && (f = i);\n    var c = {\n        placement: \"bottom\",\n        orderedModifiers: [],\n        options: Object.assign({}, Ot, i),\n        modifiersData: {},\n        elements: {\n          reference: a,\n          popper: s\n        },\n        attributes: {},\n        styles: {}\n      },\n      u = [],\n      m = !1,\n      v = {\n        state: c,\n        setOptions: function (p) {\n          var g = typeof p == \"function\" ? p(c.options) : p;\n          h(), c.options = Object.assign({}, i, c.options, g), c.scrollParents = {\n            reference: Q(a) ? ce(a) : a.contextElement ? ce(a.contextElement) : [],\n            popper: ce(s)\n          };\n          var x = un(dn([].concat(r, c.options.modifiers)));\n          return c.orderedModifiers = x.filter(function (y) {\n            return y.enabled;\n          }), l(), v.update();\n        },\n        forceUpdate: function () {\n          if (!m) {\n            var p = c.elements,\n              g = p.reference,\n              x = p.popper;\n            if ($t(g, x)) {\n              c.rects = {\n                reference: cn(g, se(x), c.options.strategy === \"fixed\"),\n                popper: ke(x)\n              }, c.reset = !1, c.placement = c.options.placement, c.orderedModifiers.forEach(function (j) {\n                return c.modifiersData[j.name] = Object.assign({}, j.data);\n              });\n              for (var y = 0; y < c.orderedModifiers.length; y++) {\n                if (c.reset === !0) {\n                  c.reset = !1, y = -1;\n                  continue;\n                }\n                var $ = c.orderedModifiers[y],\n                  d = $.fn,\n                  b = $.options,\n                  w = b === void 0 ? {} : b,\n                  O = $.name;\n                typeof d == \"function\" && (c = d({\n                  state: c,\n                  options: w,\n                  name: O,\n                  instance: v\n                }) || c);\n              }\n            }\n          }\n        },\n        update: ln(function () {\n          return new Promise(function (p) {\n            v.forceUpdate(), p(c);\n          });\n        }),\n        destroy: function () {\n          h(), m = !0;\n        }\n      };\n    if (!$t(a, s)) return v;\n    v.setOptions(f).then(function (p) {\n      !m && f.onFirstUpdate && f.onFirstUpdate(p);\n    });\n    function l() {\n      c.orderedModifiers.forEach(function (p) {\n        var g = p.name,\n          x = p.options,\n          y = x === void 0 ? {} : x,\n          $ = p.effect;\n        if (typeof $ == \"function\") {\n          var d = $({\n              state: c,\n              name: g,\n              instance: v,\n              options: y\n            }),\n            b = function () {};\n          u.push(d || b);\n        }\n      });\n    }\n    function h() {\n      u.forEach(function (p) {\n        return p();\n      }), u = [];\n    }\n    return v;\n  };\n}\nvar hn = we(),\n  mn = [Re, He, Me, Ae],\n  vn = we({\n    defaultModifiers: mn\n  }),\n  gn = [Re, He, Me, Ae, wt, vt, xt, pt, bt],\n  yn = we({\n    defaultModifiers: gn\n  });\nexport { et as afterMain, Ke as afterRead, rt as afterWrite, Ae as applyStyles, pt as arrow, me as auto, G as basePlacements, Qe as beforeMain, Ge as beforeRead, tt as beforeWrite, R as bottom, Xe as clippingParents, Me as computeStyles, yn as createPopper, hn as createPopperBase, vn as createPopperLite, ne as detectOverflow, J as end, Re as eventListeners, vt as flip, bt as hide, P as left, Ze as main, ot as modifierPhases, wt as offset, Ee as placements, K as popper, we as popperGenerator, He as popperOffsets, xt as preventOverflow, Je as read, Ye as reference, W as right, U as start, E as top, De as variationPlacements, je as viewport, nt as write };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}