{"ast": null, "code": "import arrayMap from './_arrayMap.js';\nimport baseIteratee from './_baseIteratee.js';\nimport basePickBy from './_basePickBy.js';\nimport getAllKeysIn from './_getAllKeysIn.js';\n\n/**\n * Creates an object composed of the `object` properties `predicate` returns\n * truthy for. The predicate is invoked with two arguments: (value, key).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The source object.\n * @param {Function} [predicate=_.identity] The function invoked per property.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pickBy(object, _.isNumber);\n * // => { 'a': 1, 'c': 3 }\n */\nfunction pickBy(object, predicate) {\n  if (object == null) {\n    return {};\n  }\n  var props = arrayMap(getAllKeysIn(object), function (prop) {\n    return [prop];\n  });\n  predicate = baseIteratee(predicate);\n  return basePickBy(object, props, function (value, path) {\n    return predicate(value, path[0]);\n  });\n}\nexport default pickBy;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}