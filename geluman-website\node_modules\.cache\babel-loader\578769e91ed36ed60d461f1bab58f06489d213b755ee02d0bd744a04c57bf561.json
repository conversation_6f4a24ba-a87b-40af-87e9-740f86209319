{"ast": null, "code": "import { defineComponent, createVNode } from 'vue';\nimport { autoResizerProps } from '../auto-resizer.mjs';\nimport { useAutoResize } from '../composables/use-auto-resize.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nconst AutoResizer = defineComponent({\n  name: \"ElAutoResizer\",\n  props: autoResizerProps,\n  setup(props, {\n    slots\n  }) {\n    const ns = useNamespace(\"auto-resizer\");\n    const {\n      height,\n      width,\n      sizer\n    } = useAutoResize(props);\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    return () => {\n      var _a;\n      return createVNode(\"div\", {\n        \"ref\": sizer,\n        \"class\": ns.b(),\n        \"style\": style\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots, {\n        height: height.value,\n        width: width.value\n      })]);\n    };\n  }\n});\nvar AutoResizer$1 = AutoResizer;\nexport { AutoResizer$1 as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}