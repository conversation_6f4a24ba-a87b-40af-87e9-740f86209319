{"ast": null, "code": "import { useEmptyValuesProps } from '../../../hooks/use-empty-values/index.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nconst configProviderProps = buildProps({\n  a11y: {\n    type: Boolean,\n    default: true\n  },\n  locale: {\n    type: definePropType(Object)\n  },\n  size: useSizeProp,\n  button: {\n    type: definePropType(Object)\n  },\n  experimentalFeatures: {\n    type: definePropType(Object)\n  },\n  keyboardNavigation: {\n    type: Boolean,\n    default: true\n  },\n  message: {\n    type: definePropType(Object)\n  },\n  zIndex: Number,\n  namespace: {\n    type: String,\n    default: \"el\"\n  },\n  ...useEmptyValuesProps\n});\nexport { configProviderProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}