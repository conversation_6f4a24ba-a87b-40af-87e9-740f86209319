{"ast": null, "code": "import { defineComponent, inject, computed, ref, onMounted, openBlock, createElementBlock, normalizeClass, normalizeStyle, renderSlot, createCommentVNode } from 'vue';\nimport { useResizeObserver } from '@vueuse/core';\nimport { selectKey } from './token.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElSelectDropdown\",\n  componentName: \"ElSelectDropdown\",\n  setup() {\n    const select = inject(selectKey);\n    const ns = useNamespace(\"select\");\n    const popperClass = computed(() => select.props.popperClass);\n    const isMultiple = computed(() => select.props.multiple);\n    const isFitInputWidth = computed(() => select.props.fitInputWidth);\n    const minWidth = ref(\"\");\n    function updateMinWidth() {\n      var _a;\n      minWidth.value = `${(_a = select.selectRef) == null ? void 0 : _a.offsetWidth}px`;\n    }\n    onMounted(() => {\n      updateMinWidth();\n      useResizeObserver(select.selectRef, updateMinWidth);\n    });\n    return {\n      ns,\n      minWidth,\n      popperClass,\n      isMultiple,\n      isFitInputWidth\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"div\", {\n    class: normalizeClass([_ctx.ns.b(\"dropdown\"), _ctx.ns.is(\"multiple\", _ctx.isMultiple), _ctx.popperClass]),\n    style: normalizeStyle({\n      [_ctx.isFitInputWidth ? \"width\" : \"minWidth\"]: _ctx.minWidth\n    })\n  }, [_ctx.$slots.header ? (openBlock(), createElementBlock(\"div\", {\n    key: 0,\n    class: normalizeClass(_ctx.ns.be(\"dropdown\", \"header\"))\n  }, [renderSlot(_ctx.$slots, \"header\")], 2)) : createCommentVNode(\"v-if\", true), renderSlot(_ctx.$slots, \"default\"), _ctx.$slots.footer ? (openBlock(), createElementBlock(\"div\", {\n    key: 1,\n    class: normalizeClass(_ctx.ns.be(\"dropdown\", \"footer\"))\n  }, [renderSlot(_ctx.$slots, \"footer\")], 2)) : createCommentVNode(\"v-if\", true)], 6);\n}\nvar ElSelectMenu = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"select-dropdown.vue\"]]);\nexport { ElSelectMenu as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}