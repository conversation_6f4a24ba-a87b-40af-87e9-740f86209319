{"ast": null, "code": "import { createVNode } from 'vue';\nconst Footer = (props, {\n  slots\n}) => {\n  var _a;\n  return createVNode(\"div\", {\n    \"class\": props.class,\n    \"style\": props.style\n  }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n};\nFooter.displayName = \"ElTableV2Footer\";\nvar Footer$1 = Footer;\nexport { Footer$1 as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}