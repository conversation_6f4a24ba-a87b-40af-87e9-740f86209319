{"ast": null, "code": "import { createRouter, createWebHistory } from \"vue-router\";\nimport Home from \"../views/Home.vue\";\nimport About from \"../views/About.vue\";\nimport Products from \"../views/Products.vue\";\nimport Highlight from \"../views/products/Highlight.vue\";\nimport Eyeshield from \"../views/products/Eyeshield.vue\";\nimport Translator from \"../views/products/Translator.vue\";\nimport NotFound from \"../views/NotFound.vue\";\nconst routes = [{\n  path: \"/\",\n  name: \"Home\",\n  component: Home\n}, {\n  path: \"/about\",\n  name: \"About\",\n  component: About\n}, {\n  path: \"/products\",\n  name: \"Products\",\n  component: Products\n}, {\n  path: \"/products/highlight\",\n  name: \"Highlight\",\n  component: Highlight\n}, {\n  path: \"/products/eyeshield\",\n  name: \"Eyeshield\",\n  component: Eyeshield\n}, {\n  path: \"/products/translator\",\n  name: \"Translator\",\n  component: Translator\n}, {\n  path: \"/:pathMatch(.*)*\",\n  name: \"NotFound\",\n  component: NotFound\n}];\nconst router = createRouter({\n  history: createWebHistory(),\n  routes,\n  scrollBehavior(to, from, savedPosition) {\n    if (savedPosition) {\n      return savedPosition;\n    }\n    return {\n      top: 0\n    };\n  }\n});\nexport default router;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}