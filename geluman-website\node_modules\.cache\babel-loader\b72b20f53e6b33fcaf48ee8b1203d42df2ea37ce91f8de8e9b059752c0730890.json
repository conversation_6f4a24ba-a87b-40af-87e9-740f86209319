{"ast": null, "code": "import { createVNode } from 'vue';\nimport { ElIcon } from '../../../icon/index.mjs';\nimport { SortUp, SortDown } from '@element-plus/icons-vue';\nimport { SortOrder } from '../constants.mjs';\nconst SortIcon = props => {\n  const {\n    sortOrder\n  } = props;\n  return createVNode(ElIcon, {\n    \"size\": 14,\n    \"class\": props.class\n  }, {\n    default: () => [sortOrder === SortOrder.ASC ? createVNode(SortUp, null, null) : createVNode(SortDown, null, null)]\n  });\n};\nvar SortIcon$1 = SortIcon;\nexport { SortIcon$1 as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}