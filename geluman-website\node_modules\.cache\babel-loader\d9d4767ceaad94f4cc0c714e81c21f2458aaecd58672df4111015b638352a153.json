{"ast": null, "code": "const calcColumnStyle = (column, fixedColumn, fixed) => {\n  var _a;\n  const flex = {\n    flexGrow: 0,\n    flexShrink: 0,\n    ...(fixed ? {} : {\n      flexGrow: column.flexGrow || 0,\n      flexShrink: column.flexShrink || 1\n    })\n  };\n  if (!fixed) {\n    flex.flexShrink = 1;\n  }\n  const style = {\n    ...((_a = column.style) != null ? _a : {}),\n    ...flex,\n    flexBasis: \"auto\",\n    width: column.width\n  };\n  if (!fixedColumn) {\n    if (column.maxWidth) style.maxWidth = column.maxWidth;\n    if (column.minWidth) style.minWidth = column.minWidth;\n  }\n  return style;\n};\nexport { calcColumnStyle };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}