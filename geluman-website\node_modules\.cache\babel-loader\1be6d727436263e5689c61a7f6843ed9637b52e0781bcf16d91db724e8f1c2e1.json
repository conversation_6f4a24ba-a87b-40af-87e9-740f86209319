{"ast": null, "code": "import { ref } from 'vue';\nfunction useHovering() {\n  const hovering = ref(false);\n  const handleMouseEnter = () => {\n    hovering.value = true;\n  };\n  const handleMouseLeave = () => {\n    hovering.value = false;\n  };\n  return {\n    hovering,\n    handleMouseEnter,\n    handleMouseLeave\n  };\n}\nexport { useHovering };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}