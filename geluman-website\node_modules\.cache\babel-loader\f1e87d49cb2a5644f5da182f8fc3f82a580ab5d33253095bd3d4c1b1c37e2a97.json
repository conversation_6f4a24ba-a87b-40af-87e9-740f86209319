{"ast": null, "code": "import { defineComponent, inject, openBlock, createElementBlock, normalizeStyle, normalizeClass, withModifiers, renderSlot, createElementVNode, toDisplayString } from 'vue';\nimport { useOption } from './useOption.mjs';\nimport { useProps } from './useProps.mjs';\nimport { OptionProps, optionEmits } from './defaults.mjs';\nimport { selectV2InjectionKey } from './token.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst _sfc_main = defineComponent({\n  props: OptionProps,\n  emits: optionEmits,\n  setup(props, {\n    emit\n  }) {\n    const select = inject(selectV2InjectionKey);\n    const ns = useNamespace(\"select\");\n    const {\n      hoverItem,\n      selectOptionClick\n    } = useOption(props, {\n      emit\n    });\n    const {\n      getLabel\n    } = useProps(select.props);\n    return {\n      ns,\n      hoverItem,\n      selectOptionClick,\n      getLabel\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"li\", {\n    \"aria-selected\": _ctx.selected,\n    style: normalizeStyle(_ctx.style),\n    class: normalizeClass([_ctx.ns.be(\"dropdown\", \"item\"), _ctx.ns.is(\"selected\", _ctx.selected), _ctx.ns.is(\"disabled\", _ctx.disabled), _ctx.ns.is(\"created\", _ctx.created), _ctx.ns.is(\"hovering\", _ctx.hovering)]),\n    onMousemove: _ctx.hoverItem,\n    onClick: withModifiers(_ctx.selectOptionClick, [\"stop\"])\n  }, [renderSlot(_ctx.$slots, \"default\", {\n    item: _ctx.item,\n    index: _ctx.index,\n    disabled: _ctx.disabled\n  }, () => [createElementVNode(\"span\", null, toDisplayString(_ctx.getLabel(_ctx.item)), 1)])], 46, [\"aria-selected\", \"onMousemove\", \"onClick\"]);\n}\nvar OptionItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"option-item.vue\"]]);\nexport { OptionItem as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}