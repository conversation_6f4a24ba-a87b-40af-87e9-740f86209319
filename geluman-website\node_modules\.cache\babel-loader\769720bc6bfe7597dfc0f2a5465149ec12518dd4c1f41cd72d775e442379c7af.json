{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, inject, toRef, ref, watch, computed, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, renderSlot, Fragment, renderList, toDisplayString, createCommentVNode, createVNode, withDirectives, withCtx, createBlock, createTextVNode } from 'vue';\nimport dayjs from 'dayjs';\nimport { ElButton } from '../../../button/index.mjs';\nimport { ElInput } from '../../../input/index.mjs';\nimport '../../../time-picker/index.mjs';\nimport { ElIcon } from '../../../icon/index.mjs';\nimport { ArrowRight, DArrowLeft, ArrowLeft, DArrowRight } from '@element-plus/icons-vue';\nimport { panelDateRangeProps } from '../props/panel-date-range.mjs';\nimport { useRangePicker } from '../composables/use-range-picker.mjs';\nimport { isValidRange, getDefaultValue, correctlyParseUserInput } from '../utils.mjs';\nimport DateTable from './basic-date-table.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport TimePickPanel from '../../../time-picker/src/time-picker-com/panel-time-pick.mjs';\nimport ClickOutside from '../../../../directives/click-outside/index.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { extractTimeFormat, extractDateFormat } from '../../../time-picker/src/utils.mjs';\nimport { isArray } from '@vue/shared';\nconst unit = \"month\";\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"panel-date-range\",\n  props: panelDateRangeProps,\n  emits: [\"pick\", \"set-picker-option\", \"calendar-change\", \"panel-change\"],\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const pickerBase = inject(\"EP_PICKER_BASE\");\n    const isDefaultFormat = inject(\"ElIsDefaultFormat\");\n    const {\n      disabledDate,\n      cellClassName,\n      defaultTime,\n      clearable\n    } = pickerBase.props;\n    const format = toRef(pickerBase.props, \"format\");\n    const shortcuts = toRef(pickerBase.props, \"shortcuts\");\n    const defaultValue = toRef(pickerBase.props, \"defaultValue\");\n    const {\n      lang\n    } = useLocale();\n    const leftDate = ref(dayjs().locale(lang.value));\n    const rightDate = ref(dayjs().locale(lang.value).add(1, unit));\n    const {\n      minDate,\n      maxDate,\n      rangeState,\n      ppNs,\n      drpNs,\n      handleChangeRange,\n      handleRangeConfirm,\n      handleShortcutClick,\n      onSelect,\n      onReset,\n      t\n    } = useRangePicker(props, {\n      defaultValue,\n      leftDate,\n      rightDate,\n      unit,\n      onParsedValueChanged\n    });\n    watch(() => props.visible, visible => {\n      if (!visible && rangeState.value.selecting) {\n        onReset(props.parsedValue);\n        onSelect(false);\n      }\n    });\n    const dateUserInput = ref({\n      min: null,\n      max: null\n    });\n    const timeUserInput = ref({\n      min: null,\n      max: null\n    });\n    const leftLabel = computed(() => {\n      return `${leftDate.value.year()} ${t(\"el.datepicker.year\")} ${t(`el.datepicker.month${leftDate.value.month() + 1}`)}`;\n    });\n    const rightLabel = computed(() => {\n      return `${rightDate.value.year()} ${t(\"el.datepicker.year\")} ${t(`el.datepicker.month${rightDate.value.month() + 1}`)}`;\n    });\n    const leftYear = computed(() => {\n      return leftDate.value.year();\n    });\n    const leftMonth = computed(() => {\n      return leftDate.value.month();\n    });\n    const rightYear = computed(() => {\n      return rightDate.value.year();\n    });\n    const rightMonth = computed(() => {\n      return rightDate.value.month();\n    });\n    const hasShortcuts = computed(() => !!shortcuts.value.length);\n    const minVisibleDate = computed(() => {\n      if (dateUserInput.value.min !== null) return dateUserInput.value.min;\n      if (minDate.value) return minDate.value.format(dateFormat.value);\n      return \"\";\n    });\n    const maxVisibleDate = computed(() => {\n      if (dateUserInput.value.max !== null) return dateUserInput.value.max;\n      if (maxDate.value || minDate.value) return (maxDate.value || minDate.value).format(dateFormat.value);\n      return \"\";\n    });\n    const minVisibleTime = computed(() => {\n      if (timeUserInput.value.min !== null) return timeUserInput.value.min;\n      if (minDate.value) return minDate.value.format(timeFormat.value);\n      return \"\";\n    });\n    const maxVisibleTime = computed(() => {\n      if (timeUserInput.value.max !== null) return timeUserInput.value.max;\n      if (maxDate.value || minDate.value) return (maxDate.value || minDate.value).format(timeFormat.value);\n      return \"\";\n    });\n    const timeFormat = computed(() => {\n      return props.timeFormat || extractTimeFormat(format.value);\n    });\n    const dateFormat = computed(() => {\n      return props.dateFormat || extractDateFormat(format.value);\n    });\n    const isValidValue = date => {\n      return isValidRange(date) && (disabledDate ? !disabledDate(date[0].toDate()) && !disabledDate(date[1].toDate()) : true);\n    };\n    const leftPrevYear = () => {\n      leftDate.value = leftDate.value.subtract(1, \"year\");\n      if (!props.unlinkPanels) {\n        rightDate.value = leftDate.value.add(1, \"month\");\n      }\n      handlePanelChange(\"year\");\n    };\n    const leftPrevMonth = () => {\n      leftDate.value = leftDate.value.subtract(1, \"month\");\n      if (!props.unlinkPanels) {\n        rightDate.value = leftDate.value.add(1, \"month\");\n      }\n      handlePanelChange(\"month\");\n    };\n    const rightNextYear = () => {\n      if (!props.unlinkPanels) {\n        leftDate.value = leftDate.value.add(1, \"year\");\n        rightDate.value = leftDate.value.add(1, \"month\");\n      } else {\n        rightDate.value = rightDate.value.add(1, \"year\");\n      }\n      handlePanelChange(\"year\");\n    };\n    const rightNextMonth = () => {\n      if (!props.unlinkPanels) {\n        leftDate.value = leftDate.value.add(1, \"month\");\n        rightDate.value = leftDate.value.add(1, \"month\");\n      } else {\n        rightDate.value = rightDate.value.add(1, \"month\");\n      }\n      handlePanelChange(\"month\");\n    };\n    const leftNextYear = () => {\n      leftDate.value = leftDate.value.add(1, \"year\");\n      handlePanelChange(\"year\");\n    };\n    const leftNextMonth = () => {\n      leftDate.value = leftDate.value.add(1, \"month\");\n      handlePanelChange(\"month\");\n    };\n    const rightPrevYear = () => {\n      rightDate.value = rightDate.value.subtract(1, \"year\");\n      handlePanelChange(\"year\");\n    };\n    const rightPrevMonth = () => {\n      rightDate.value = rightDate.value.subtract(1, \"month\");\n      handlePanelChange(\"month\");\n    };\n    const handlePanelChange = mode => {\n      emit(\"panel-change\", [leftDate.value.toDate(), rightDate.value.toDate()], mode);\n    };\n    const enableMonthArrow = computed(() => {\n      const nextMonth = (leftMonth.value + 1) % 12;\n      const yearOffset = leftMonth.value + 1 >= 12 ? 1 : 0;\n      return props.unlinkPanels && new Date(leftYear.value + yearOffset, nextMonth) < new Date(rightYear.value, rightMonth.value);\n    });\n    const enableYearArrow = computed(() => {\n      return props.unlinkPanels && rightYear.value * 12 + rightMonth.value - (leftYear.value * 12 + leftMonth.value + 1) >= 12;\n    });\n    const btnDisabled = computed(() => {\n      return !(minDate.value && maxDate.value && !rangeState.value.selecting && isValidRange([minDate.value, maxDate.value]));\n    });\n    const showTime = computed(() => props.type === \"datetime\" || props.type === \"datetimerange\");\n    const formatEmit = (emitDayjs, index) => {\n      if (!emitDayjs) return;\n      if (defaultTime) {\n        const defaultTimeD = dayjs(defaultTime[index] || defaultTime).locale(lang.value);\n        return defaultTimeD.year(emitDayjs.year()).month(emitDayjs.month()).date(emitDayjs.date());\n      }\n      return emitDayjs;\n    };\n    const handleRangePick = (val, close = true) => {\n      const min_ = val.minDate;\n      const max_ = val.maxDate;\n      const minDate_ = formatEmit(min_, 0);\n      const maxDate_ = formatEmit(max_, 1);\n      if (maxDate.value === maxDate_ && minDate.value === minDate_) {\n        return;\n      }\n      emit(\"calendar-change\", [min_.toDate(), max_ && max_.toDate()]);\n      maxDate.value = maxDate_;\n      minDate.value = minDate_;\n      if (!close || showTime.value) return;\n      handleRangeConfirm();\n    };\n    const minTimePickerVisible = ref(false);\n    const maxTimePickerVisible = ref(false);\n    const handleMinTimeClose = () => {\n      minTimePickerVisible.value = false;\n    };\n    const handleMaxTimeClose = () => {\n      maxTimePickerVisible.value = false;\n    };\n    const handleDateInput = (value, type) => {\n      dateUserInput.value[type] = value;\n      const parsedValueD = dayjs(value, dateFormat.value).locale(lang.value);\n      if (parsedValueD.isValid()) {\n        if (disabledDate && disabledDate(parsedValueD.toDate())) {\n          return;\n        }\n        if (type === \"min\") {\n          leftDate.value = parsedValueD;\n          minDate.value = (minDate.value || leftDate.value).year(parsedValueD.year()).month(parsedValueD.month()).date(parsedValueD.date());\n          if (!props.unlinkPanels && (!maxDate.value || maxDate.value.isBefore(minDate.value))) {\n            rightDate.value = parsedValueD.add(1, \"month\");\n            maxDate.value = minDate.value.add(1, \"month\");\n          }\n        } else {\n          rightDate.value = parsedValueD;\n          maxDate.value = (maxDate.value || rightDate.value).year(parsedValueD.year()).month(parsedValueD.month()).date(parsedValueD.date());\n          if (!props.unlinkPanels && (!minDate.value || minDate.value.isAfter(maxDate.value))) {\n            leftDate.value = parsedValueD.subtract(1, \"month\");\n            minDate.value = maxDate.value.subtract(1, \"month\");\n          }\n        }\n      }\n    };\n    const handleDateChange = (_, type) => {\n      dateUserInput.value[type] = null;\n    };\n    const handleTimeInput = (value, type) => {\n      timeUserInput.value[type] = value;\n      const parsedValueD = dayjs(value, timeFormat.value).locale(lang.value);\n      if (parsedValueD.isValid()) {\n        if (type === \"min\") {\n          minTimePickerVisible.value = true;\n          minDate.value = (minDate.value || leftDate.value).hour(parsedValueD.hour()).minute(parsedValueD.minute()).second(parsedValueD.second());\n        } else {\n          maxTimePickerVisible.value = true;\n          maxDate.value = (maxDate.value || rightDate.value).hour(parsedValueD.hour()).minute(parsedValueD.minute()).second(parsedValueD.second());\n          rightDate.value = maxDate.value;\n        }\n      }\n    };\n    const handleTimeChange = (value, type) => {\n      timeUserInput.value[type] = null;\n      if (type === \"min\") {\n        leftDate.value = minDate.value;\n        minTimePickerVisible.value = false;\n        if (!maxDate.value || maxDate.value.isBefore(minDate.value)) {\n          maxDate.value = minDate.value;\n        }\n      } else {\n        rightDate.value = maxDate.value;\n        maxTimePickerVisible.value = false;\n        if (maxDate.value && maxDate.value.isBefore(minDate.value)) {\n          minDate.value = maxDate.value;\n        }\n      }\n    };\n    const handleMinTimePick = (value, visible, first) => {\n      if (timeUserInput.value.min) return;\n      if (value) {\n        leftDate.value = value;\n        minDate.value = (minDate.value || leftDate.value).hour(value.hour()).minute(value.minute()).second(value.second());\n      }\n      if (!first) {\n        minTimePickerVisible.value = visible;\n      }\n      if (!maxDate.value || maxDate.value.isBefore(minDate.value)) {\n        maxDate.value = minDate.value;\n        rightDate.value = value;\n      }\n    };\n    const handleMaxTimePick = (value, visible, first) => {\n      if (timeUserInput.value.max) return;\n      if (value) {\n        rightDate.value = value;\n        maxDate.value = (maxDate.value || rightDate.value).hour(value.hour()).minute(value.minute()).second(value.second());\n      }\n      if (!first) {\n        maxTimePickerVisible.value = visible;\n      }\n      if (maxDate.value && maxDate.value.isBefore(minDate.value)) {\n        minDate.value = maxDate.value;\n      }\n    };\n    const handleClear = () => {\n      leftDate.value = getDefaultValue(unref(defaultValue), {\n        lang: unref(lang),\n        unit: \"month\",\n        unlinkPanels: props.unlinkPanels\n      })[0];\n      rightDate.value = leftDate.value.add(1, \"month\");\n      maxDate.value = void 0;\n      minDate.value = void 0;\n      emit(\"pick\", null);\n    };\n    const formatToString = value => {\n      return isArray(value) ? value.map(_ => _.format(format.value)) : value.format(format.value);\n    };\n    const parseUserInput = value => {\n      return correctlyParseUserInput(value, format.value, lang.value, isDefaultFormat);\n    };\n    function onParsedValueChanged(minDate2, maxDate2) {\n      if (props.unlinkPanels && maxDate2) {\n        const minDateYear = (minDate2 == null ? void 0 : minDate2.year()) || 0;\n        const minDateMonth = (minDate2 == null ? void 0 : minDate2.month()) || 0;\n        const maxDateYear = maxDate2.year();\n        const maxDateMonth = maxDate2.month();\n        rightDate.value = minDateYear === maxDateYear && minDateMonth === maxDateMonth ? maxDate2.add(1, unit) : maxDate2;\n      } else {\n        rightDate.value = leftDate.value.add(1, unit);\n        if (maxDate2) {\n          rightDate.value = rightDate.value.hour(maxDate2.hour()).minute(maxDate2.minute()).second(maxDate2.second());\n        }\n      }\n    }\n    emit(\"set-picker-option\", [\"isValidValue\", isValidValue]);\n    emit(\"set-picker-option\", [\"parseUserInput\", parseUserInput]);\n    emit(\"set-picker-option\", [\"formatToString\", formatToString]);\n    emit(\"set-picker-option\", [\"handleClear\", handleClear]);\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ppNs).b(), unref(drpNs).b(), {\n          \"has-sidebar\": _ctx.$slots.sidebar || unref(hasShortcuts),\n          \"has-time\": unref(showTime)\n        }])\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ppNs).e(\"body-wrapper\"))\n      }, [renderSlot(_ctx.$slots, \"sidebar\", {\n        class: normalizeClass(unref(ppNs).e(\"sidebar\"))\n      }), unref(hasShortcuts) ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ppNs).e(\"sidebar\"))\n      }, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(shortcuts), (shortcut, key) => {\n        return openBlock(), createElementBlock(\"button\", {\n          key,\n          type: \"button\",\n          class: normalizeClass(unref(ppNs).e(\"shortcut\")),\n          onClick: $event => unref(handleShortcutClick)(shortcut)\n        }, toDisplayString(shortcut.text), 11, [\"onClick\"]);\n      }), 128))], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ppNs).e(\"body\"))\n      }, [unref(showTime) ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(drpNs).e(\"time-header\"))\n      }, [createElementVNode(\"span\", {\n        class: normalizeClass(unref(drpNs).e(\"editors-wrap\"))\n      }, [createElementVNode(\"span\", {\n        class: normalizeClass(unref(drpNs).e(\"time-picker-wrap\"))\n      }, [createVNode(unref(ElInput), {\n        size: \"small\",\n        disabled: unref(rangeState).selecting,\n        placeholder: unref(t)(\"el.datepicker.startDate\"),\n        class: normalizeClass(unref(drpNs).e(\"editor\")),\n        \"model-value\": unref(minVisibleDate),\n        \"validate-event\": false,\n        onInput: val => handleDateInput(val, \"min\"),\n        onChange: val => handleDateChange(val, \"min\")\n      }, null, 8, [\"disabled\", \"placeholder\", \"class\", \"model-value\", \"onInput\", \"onChange\"])], 2), withDirectives((openBlock(), createElementBlock(\"span\", {\n        class: normalizeClass(unref(drpNs).e(\"time-picker-wrap\"))\n      }, [createVNode(unref(ElInput), {\n        size: \"small\",\n        class: normalizeClass(unref(drpNs).e(\"editor\")),\n        disabled: unref(rangeState).selecting,\n        placeholder: unref(t)(\"el.datepicker.startTime\"),\n        \"model-value\": unref(minVisibleTime),\n        \"validate-event\": false,\n        onFocus: $event => minTimePickerVisible.value = true,\n        onInput: val => handleTimeInput(val, \"min\"),\n        onChange: val => handleTimeChange(val, \"min\")\n      }, null, 8, [\"class\", \"disabled\", \"placeholder\", \"model-value\", \"onFocus\", \"onInput\", \"onChange\"]), createVNode(unref(TimePickPanel), {\n        visible: minTimePickerVisible.value,\n        format: unref(timeFormat),\n        \"datetime-role\": \"start\",\n        \"parsed-value\": leftDate.value,\n        onPick: handleMinTimePick\n      }, null, 8, [\"visible\", \"format\", \"parsed-value\"])], 2)), [[unref(ClickOutside), handleMinTimeClose]])], 2), createElementVNode(\"span\", null, [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [createVNode(unref(ArrowRight))]),\n        _: 1\n      })]), createElementVNode(\"span\", {\n        class: normalizeClass([unref(drpNs).e(\"editors-wrap\"), \"is-right\"])\n      }, [createElementVNode(\"span\", {\n        class: normalizeClass(unref(drpNs).e(\"time-picker-wrap\"))\n      }, [createVNode(unref(ElInput), {\n        size: \"small\",\n        class: normalizeClass(unref(drpNs).e(\"editor\")),\n        disabled: unref(rangeState).selecting,\n        placeholder: unref(t)(\"el.datepicker.endDate\"),\n        \"model-value\": unref(maxVisibleDate),\n        readonly: !unref(minDate),\n        \"validate-event\": false,\n        onInput: val => handleDateInput(val, \"max\"),\n        onChange: val => handleDateChange(val, \"max\")\n      }, null, 8, [\"class\", \"disabled\", \"placeholder\", \"model-value\", \"readonly\", \"onInput\", \"onChange\"])], 2), withDirectives((openBlock(), createElementBlock(\"span\", {\n        class: normalizeClass(unref(drpNs).e(\"time-picker-wrap\"))\n      }, [createVNode(unref(ElInput), {\n        size: \"small\",\n        class: normalizeClass(unref(drpNs).e(\"editor\")),\n        disabled: unref(rangeState).selecting,\n        placeholder: unref(t)(\"el.datepicker.endTime\"),\n        \"model-value\": unref(maxVisibleTime),\n        readonly: !unref(minDate),\n        \"validate-event\": false,\n        onFocus: $event => unref(minDate) && (maxTimePickerVisible.value = true),\n        onInput: val => handleTimeInput(val, \"max\"),\n        onChange: val => handleTimeChange(val, \"max\")\n      }, null, 8, [\"class\", \"disabled\", \"placeholder\", \"model-value\", \"readonly\", \"onFocus\", \"onInput\", \"onChange\"]), createVNode(unref(TimePickPanel), {\n        \"datetime-role\": \"end\",\n        visible: maxTimePickerVisible.value,\n        format: unref(timeFormat),\n        \"parsed-value\": rightDate.value,\n        onPick: handleMaxTimePick\n      }, null, 8, [\"visible\", \"format\", \"parsed-value\"])], 2)), [[unref(ClickOutside), handleMaxTimeClose]])], 2)], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass([[unref(ppNs).e(\"content\"), unref(drpNs).e(\"content\")], \"is-left\"])\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(drpNs).e(\"header\"))\n      }, [createElementVNode(\"button\", {\n        type: \"button\",\n        class: normalizeClass([unref(ppNs).e(\"icon-btn\"), \"d-arrow-left\"]),\n        \"aria-label\": unref(t)(`el.datepicker.prevYear`),\n        onClick: leftPrevYear\n      }, [renderSlot(_ctx.$slots, \"prev-year\", {}, () => [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [createVNode(unref(DArrowLeft))]),\n        _: 1\n      })])], 10, [\"aria-label\"]), createElementVNode(\"button\", {\n        type: \"button\",\n        class: normalizeClass([unref(ppNs).e(\"icon-btn\"), \"arrow-left\"]),\n        \"aria-label\": unref(t)(`el.datepicker.prevMonth`),\n        onClick: leftPrevMonth\n      }, [renderSlot(_ctx.$slots, \"prev-month\", {}, () => [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [createVNode(unref(ArrowLeft))]),\n        _: 1\n      })])], 10, [\"aria-label\"]), _ctx.unlinkPanels ? (openBlock(), createElementBlock(\"button\", {\n        key: 0,\n        type: \"button\",\n        disabled: !unref(enableYearArrow),\n        class: normalizeClass([[unref(ppNs).e(\"icon-btn\"), {\n          \"is-disabled\": !unref(enableYearArrow)\n        }], \"d-arrow-right\"]),\n        \"aria-label\": unref(t)(`el.datepicker.nextYear`),\n        onClick: leftNextYear\n      }, [renderSlot(_ctx.$slots, \"next-year\", {}, () => [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [createVNode(unref(DArrowRight))]),\n        _: 1\n      })])], 10, [\"disabled\", \"aria-label\"])) : createCommentVNode(\"v-if\", true), _ctx.unlinkPanels ? (openBlock(), createElementBlock(\"button\", {\n        key: 1,\n        type: \"button\",\n        disabled: !unref(enableMonthArrow),\n        class: normalizeClass([[unref(ppNs).e(\"icon-btn\"), {\n          \"is-disabled\": !unref(enableMonthArrow)\n        }], \"arrow-right\"]),\n        \"aria-label\": unref(t)(`el.datepicker.nextMonth`),\n        onClick: leftNextMonth\n      }, [renderSlot(_ctx.$slots, \"next-month\", {}, () => [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [createVNode(unref(ArrowRight))]),\n        _: 1\n      })])], 10, [\"disabled\", \"aria-label\"])) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", null, toDisplayString(unref(leftLabel)), 1)], 2), createVNode(DateTable, {\n        \"selection-mode\": \"range\",\n        date: leftDate.value,\n        \"min-date\": unref(minDate),\n        \"max-date\": unref(maxDate),\n        \"range-state\": unref(rangeState),\n        \"disabled-date\": unref(disabledDate),\n        \"cell-class-name\": unref(cellClassName),\n        onChangerange: unref(handleChangeRange),\n        onPick: handleRangePick,\n        onSelect: unref(onSelect)\n      }, null, 8, [\"date\", \"min-date\", \"max-date\", \"range-state\", \"disabled-date\", \"cell-class-name\", \"onChangerange\", \"onSelect\"])], 2), createElementVNode(\"div\", {\n        class: normalizeClass([[unref(ppNs).e(\"content\"), unref(drpNs).e(\"content\")], \"is-right\"])\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(drpNs).e(\"header\"))\n      }, [_ctx.unlinkPanels ? (openBlock(), createElementBlock(\"button\", {\n        key: 0,\n        type: \"button\",\n        disabled: !unref(enableYearArrow),\n        class: normalizeClass([[unref(ppNs).e(\"icon-btn\"), {\n          \"is-disabled\": !unref(enableYearArrow)\n        }], \"d-arrow-left\"]),\n        \"aria-label\": unref(t)(`el.datepicker.prevYear`),\n        onClick: rightPrevYear\n      }, [renderSlot(_ctx.$slots, \"prev-year\", {}, () => [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [createVNode(unref(DArrowLeft))]),\n        _: 1\n      })])], 10, [\"disabled\", \"aria-label\"])) : createCommentVNode(\"v-if\", true), _ctx.unlinkPanels ? (openBlock(), createElementBlock(\"button\", {\n        key: 1,\n        type: \"button\",\n        disabled: !unref(enableMonthArrow),\n        class: normalizeClass([[unref(ppNs).e(\"icon-btn\"), {\n          \"is-disabled\": !unref(enableMonthArrow)\n        }], \"arrow-left\"]),\n        \"aria-label\": unref(t)(`el.datepicker.prevMonth`),\n        onClick: rightPrevMonth\n      }, [renderSlot(_ctx.$slots, \"prev-month\", {}, () => [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [createVNode(unref(ArrowLeft))]),\n        _: 1\n      })])], 10, [\"disabled\", \"aria-label\"])) : createCommentVNode(\"v-if\", true), createElementVNode(\"button\", {\n        type: \"button\",\n        \"aria-label\": unref(t)(`el.datepicker.nextYear`),\n        class: normalizeClass([unref(ppNs).e(\"icon-btn\"), \"d-arrow-right\"]),\n        onClick: rightNextYear\n      }, [renderSlot(_ctx.$slots, \"next-year\", {}, () => [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [createVNode(unref(DArrowRight))]),\n        _: 1\n      })])], 10, [\"aria-label\"]), createElementVNode(\"button\", {\n        type: \"button\",\n        class: normalizeClass([unref(ppNs).e(\"icon-btn\"), \"arrow-right\"]),\n        \"aria-label\": unref(t)(`el.datepicker.nextMonth`),\n        onClick: rightNextMonth\n      }, [renderSlot(_ctx.$slots, \"next-month\", {}, () => [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [createVNode(unref(ArrowRight))]),\n        _: 1\n      })])], 10, [\"aria-label\"]), createElementVNode(\"div\", null, toDisplayString(unref(rightLabel)), 1)], 2), createVNode(DateTable, {\n        \"selection-mode\": \"range\",\n        date: rightDate.value,\n        \"min-date\": unref(minDate),\n        \"max-date\": unref(maxDate),\n        \"range-state\": unref(rangeState),\n        \"disabled-date\": unref(disabledDate),\n        \"cell-class-name\": unref(cellClassName),\n        onChangerange: unref(handleChangeRange),\n        onPick: handleRangePick,\n        onSelect: unref(onSelect)\n      }, null, 8, [\"date\", \"min-date\", \"max-date\", \"range-state\", \"disabled-date\", \"cell-class-name\", \"onChangerange\", \"onSelect\"])], 2)], 2)], 2), unref(showTime) ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ppNs).e(\"footer\"))\n      }, [unref(clearable) ? (openBlock(), createBlock(unref(ElButton), {\n        key: 0,\n        text: \"\",\n        size: \"small\",\n        class: normalizeClass(unref(ppNs).e(\"link-btn\")),\n        onClick: handleClear\n      }, {\n        default: withCtx(() => [createTextVNode(toDisplayString(unref(t)(\"el.datepicker.clear\")), 1)]),\n        _: 1\n      }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true), createVNode(unref(ElButton), {\n        plain: \"\",\n        size: \"small\",\n        class: normalizeClass(unref(ppNs).e(\"link-btn\")),\n        disabled: unref(btnDisabled),\n        onClick: $event => unref(handleRangeConfirm)(false)\n      }, {\n        default: withCtx(() => [createTextVNode(toDisplayString(unref(t)(\"el.datepicker.confirm\")), 1)]),\n        _: 1\n      }, 8, [\"class\", \"disabled\", \"onClick\"])], 2)) : createCommentVNode(\"v-if\", true)], 2);\n    };\n  }\n});\nvar DateRangePickPanel = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"panel-date-range.vue\"]]);\nexport { DateRangePickPanel as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}