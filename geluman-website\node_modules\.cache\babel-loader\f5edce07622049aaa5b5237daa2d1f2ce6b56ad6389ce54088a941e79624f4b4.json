{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { createElementVNode as _createElementVNode, unref as _unref, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createTextVNode as _createTextVNode, createStaticVNode as _createStaticVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"products-page\"\n};\nconst _hoisted_2 = {\n  class: \"products-grid\"\n};\nconst _hoisted_3 = {\n  class: \"container\"\n};\nconst _hoisted_4 = {\n  class: \"product-card\"\n};\nconst _hoisted_5 = {\n  class: \"features\"\n};\nconst _hoisted_6 = {\n  class: \"feature\"\n};\nconst _hoisted_7 = {\n  class: \"feature\"\n};\nconst _hoisted_8 = {\n  class: \"feature\"\n};\nconst _hoisted_9 = {\n  class: \"actions\"\n};\nconst _hoisted_10 = {\n  class: \"product-card\"\n};\nconst _hoisted_11 = {\n  class: \"features\"\n};\nconst _hoisted_12 = {\n  class: \"feature\"\n};\nconst _hoisted_13 = {\n  class: \"feature\"\n};\nconst _hoisted_14 = {\n  class: \"feature\"\n};\nconst _hoisted_15 = {\n  class: \"actions\"\n};\nconst _hoisted_16 = {\n  class: \"product-card\"\n};\nconst _hoisted_17 = {\n  class: \"features\"\n};\nconst _hoisted_18 = {\n  class: \"feature\"\n};\nconst _hoisted_19 = {\n  class: \"feature\"\n};\nconst _hoisted_20 = {\n  class: \"feature\"\n};\nconst _hoisted_21 = {\n  class: \"actions\"\n};\nimport { useI18n } from \"vue-i18n\";\nimport { useRouter } from \"vue-router\";\nimport { highlight, eyeshield, clock } from \"@/assets\";\nimport { HomeFilled, ArrowRight, Files, Brush, Share, Monitor, MagicStick, Setting, ChatLineRound, Scissors, Connection } from \"@element-plus/icons-vue\";\nconst __default__ = {\n  name: \"ProductsPage\"\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props) {\n    const {\n      t\n    } = useI18n();\n    const router = useRouter();\n    const navigateToProduct = path => {\n      router.push(path);\n    };\n    return (_ctx, _cache) => {\n      const _component_el_icon = _resolveComponent(\"el-icon\");\n      const _component_router_link = _resolveComponent(\"router-link\");\n      return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[18] || (_cache[18] = _createStaticVNode(\"<section class=\\\"products-hero\\\" data-v-53217229><div class=\\\"hero-content\\\" data-v-53217229><h1 class=\\\"title\\\" data-v-53217229>创新产品</h1><p class=\\\"subtitle\\\" data-v-53217229>打造极致用户体验的数字化工具</p></div><div class=\\\"hero-background\\\" data-v-53217229><div class=\\\"floating-circle\\\" data-v-53217229></div><div class=\\\"floating-dots\\\" data-v-53217229></div></div></section>\", 1)), _createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n        class: \"product-badge\"\n      }, \"Browser Extension\", -1)), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n        class: \"product-header\"\n      }, [_createElementVNode(\"h2\", null, \"智能高亮助手\"), _createElementVNode(\"p\", {\n        class: \"description\"\n      }, \" 专业的网页文本智能高亮工具，让阅读和学习更高效。支持多分类管理、 颜色自定义、关键词管理和配置导出分享。 \")], -1)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_unref(Files))]),\n        _: 1\n      }), _cache[0] || (_cache[0] = _createElementVNode(\"span\", null, \"多分类高亮\", -1))]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_unref(Brush))]),\n        _: 1\n      }), _cache[1] || (_cache[1] = _createElementVNode(\"span\", null, \"颜色自定义\", -1))]), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_unref(Share))]),\n        _: 1\n      }), _cache[2] || (_cache[2] = _createElementVNode(\"span\", null, \"配置分享\", -1))])]), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_router_link, {\n        to: \"/products/highlight\",\n        class: \"learn-more\"\n      }, {\n        default: _withCtx(() => [_cache[3] || (_cache[3] = _createTextVNode(\" 了解更多 \")), _createVNode(_component_el_icon, {\n          class: \"icon-right\"\n        }, {\n          default: _withCtx(() => [_createVNode(_unref(ArrowRight))]),\n          _: 1\n        })]),\n        _: 1\n      })])]), _createElementVNode(\"div\", _hoisted_10, [_cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n        class: \"product-badge\"\n      }, \"Browser Extension\", -1)), _cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n        class: \"product-header\"\n      }, [_createElementVNode(\"h2\", null, \"网页护眼助手\"), _createElementVNode(\"p\", {\n        class: \"description\"\n      }, \" 智能护眼工具，让网页浏览更舒适，保护您的眼睛健康。 通过智能算法，自动调节屏幕显示效果。 \")], -1)), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_unref(Monitor))]),\n        _: 1\n      }), _cache[6] || (_cache[6] = _createElementVNode(\"span\", null, \"智能护眼模式\", -1))]), _createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_unref(MagicStick))]),\n        _: 1\n      }), _cache[7] || (_cache[7] = _createElementVNode(\"span\", null, \"场景自适应\", -1))]), _createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_unref(Setting))]),\n        _: 1\n      }), _cache[8] || (_cache[8] = _createElementVNode(\"span\", null, \"全局控制\", -1))])]), _createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_router_link, {\n        to: \"/products/eyeshield\",\n        class: \"learn-more\"\n      }, {\n        default: _withCtx(() => [_cache[9] || (_cache[9] = _createTextVNode(\" 了解更多 \")), _createVNode(_component_el_icon, {\n          class: \"icon-right\"\n        }, {\n          default: _withCtx(() => [_createVNode(_unref(ArrowRight))]),\n          _: 1\n        })]),\n        _: 1\n      })])]), _createElementVNode(\"div\", _hoisted_16, [_cache[16] || (_cache[16] = _createElementVNode(\"div\", {\n        class: \"product-badge\"\n      }, \"Browser Extension\", -1)), _cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n        class: \"product-header\"\n      }, [_createElementVNode(\"h2\", null, \"智能翻译助手\"), _createElementVNode(\"p\", {\n        class: \"description\"\n      }, \" 高效、准确的网页文本翻译工具，帮助您跨越语言障碍。 支持多语言翻译、划词翻译、智能识别等功能。 \")], -1)), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_unref(ChatLineRound))]),\n        _: 1\n      }), _cache[12] || (_cache[12] = _createElementVNode(\"span\", null, \"多语言支持\", -1))]), _createElementVNode(\"div\", _hoisted_19, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_unref(Scissors))]),\n        _: 1\n      }), _cache[13] || (_cache[13] = _createElementVNode(\"span\", null, \"划词翻译\", -1))]), _createElementVNode(\"div\", _hoisted_20, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_unref(Connection))]),\n        _: 1\n      }), _cache[14] || (_cache[14] = _createElementVNode(\"span\", null, \"实时翻译\", -1))])]), _createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_router_link, {\n        to: \"/products/translator\",\n        class: \"learn-more\"\n      }, {\n        default: _withCtx(() => [_cache[15] || (_cache[15] = _createTextVNode(\" 了解更多 \")), _createVNode(_component_el_icon, {\n          class: \"icon-right\"\n        }, {\n          default: _withCtx(() => [_createVNode(_unref(ArrowRight))]),\n          _: 1\n        })]),\n        _: 1\n      })])])])])]);\n    };\n  }\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}