{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { isClient } from '@vueuse/core';\nimport { isArray } from '@vue/shared';\nimport { isElement } from '../../utils/types.mjs';\nconst nodeList = /* @__PURE__ */new Map();\nif (isClient) {\n  let startClick;\n  document.addEventListener(\"mousedown\", e => startClick = e);\n  document.addEventListener(\"mouseup\", e => {\n    if (startClick) {\n      for (const handlers of nodeList.values()) {\n        for (const {\n          documentHandler\n        } of handlers) {\n          documentHandler(e, startClick);\n        }\n      }\n      startClick = void 0;\n    }\n  });\n}\nfunction createDocumentHandler(el, binding) {\n  let excludes = [];\n  if (isArray(binding.arg)) {\n    excludes = binding.arg;\n  } else if (isElement(binding.arg)) {\n    excludes.push(binding.arg);\n  }\n  return function (mouseup, mousedown) {\n    const popperRef = binding.instance.popperRef;\n    const mouseUpTarget = mouseup.target;\n    const mouseDownTarget = mousedown == null ? void 0 : mousedown.target;\n    const isBound = !binding || !binding.instance;\n    const isTargetExists = !mouseUpTarget || !mouseDownTarget;\n    const isContainedByEl = el.contains(mouseUpTarget) || el.contains(mouseDownTarget);\n    const isSelf = el === mouseUpTarget;\n    const isTargetExcluded = excludes.length && excludes.some(item => item == null ? void 0 : item.contains(mouseUpTarget)) || excludes.length && excludes.includes(mouseDownTarget);\n    const isContainedByPopper = popperRef && (popperRef.contains(mouseUpTarget) || popperRef.contains(mouseDownTarget));\n    if (isBound || isTargetExists || isContainedByEl || isSelf || isTargetExcluded || isContainedByPopper) {\n      return;\n    }\n    binding.value(mouseup, mousedown);\n  };\n}\nconst ClickOutside = {\n  beforeMount(el, binding) {\n    if (!nodeList.has(el)) {\n      nodeList.set(el, []);\n    }\n    nodeList.get(el).push({\n      documentHandler: createDocumentHandler(el, binding),\n      bindingFn: binding.value\n    });\n  },\n  updated(el, binding) {\n    if (!nodeList.has(el)) {\n      nodeList.set(el, []);\n    }\n    const handlers = nodeList.get(el);\n    const oldHandlerIndex = handlers.findIndex(item => item.bindingFn === binding.oldValue);\n    const newHandler = {\n      documentHandler: createDocumentHandler(el, binding),\n      bindingFn: binding.value\n    };\n    if (oldHandlerIndex >= 0) {\n      handlers.splice(oldHandlerIndex, 1, newHandler);\n    } else {\n      handlers.push(newHandler);\n    }\n  },\n  unmounted(el) {\n    nodeList.delete(el);\n  }\n};\nexport { ClickOutside as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}