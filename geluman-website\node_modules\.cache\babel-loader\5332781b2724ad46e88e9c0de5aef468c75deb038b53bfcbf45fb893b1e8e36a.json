{"ast": null, "code": "import baseToString from './_baseToString.js';\nimport baseTrim from './_baseTrim.js';\nimport castSlice from './_castSlice.js';\nimport charsEndIndex from './_charsEndIndex.js';\nimport charsStartIndex from './_charsStartIndex.js';\nimport stringToArray from './_stringToArray.js';\nimport toString from './toString.js';\n\n/**\n * Removes leading and trailing whitespace or specified characters from `string`.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to trim.\n * @param {string} [chars=whitespace] The characters to trim.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {string} Returns the trimmed string.\n * @example\n *\n * _.trim('  abc  ');\n * // => 'abc'\n *\n * _.trim('-_-abc-_-', '_-');\n * // => 'abc'\n *\n * _.map(['  foo  ', '  bar  '], _.trim);\n * // => ['foo', 'bar']\n */\nfunction trim(string, chars, guard) {\n  string = toString(string);\n  if (string && (guard || chars === undefined)) {\n    return baseTrim(string);\n  }\n  if (!string || !(chars = baseToString(chars))) {\n    return string;\n  }\n  var strSymbols = stringToArray(string),\n    chrSymbols = stringToArray(chars),\n    start = charsStartIndex(strSymbols, chrSymbols),\n    end = charsEndIndex(strSymbols, chrSymbols) + 1;\n  return castSlice(strSymbols, start, end).join('');\n}\nexport default trim;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}