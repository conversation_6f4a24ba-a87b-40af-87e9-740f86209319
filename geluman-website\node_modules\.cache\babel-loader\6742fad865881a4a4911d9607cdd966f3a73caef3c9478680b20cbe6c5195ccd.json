{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, inject, ref, computed, watch, onMounted, onUpdated, createVNode, nextTick } from 'vue';\nimport { useDocumentVisibility, useWindowFocus, useResizeObserver } from '@vueuse/core';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { ArrowLeft, ArrowRight, Close } from '@element-plus/icons-vue';\nimport TabBar from './tab-bar2.mjs';\nimport { tabsRootContextKey } from './constants.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { capitalize } from '../../../utils/strings.mjs';\nconst tabNavProps = buildProps({\n  panes: {\n    type: definePropType(Array),\n    default: () => mutable([])\n  },\n  currentName: {\n    type: [String, Number],\n    default: \"\"\n  },\n  editable: Boolean,\n  type: {\n    type: String,\n    values: [\"card\", \"border-card\", \"\"],\n    default: \"\"\n  },\n  stretch: Boolean\n});\nconst tabNavEmits = {\n  tabClick: (tab, tabName, ev) => ev instanceof Event,\n  tabRemove: (tab, ev) => ev instanceof Event\n};\nconst COMPONENT_NAME = \"ElTabNav\";\nconst TabNav = defineComponent({\n  name: COMPONENT_NAME,\n  props: tabNavProps,\n  emits: tabNavEmits,\n  setup(props, {\n    expose,\n    emit\n  }) {\n    const rootTabs = inject(tabsRootContextKey);\n    if (!rootTabs) throwError(COMPONENT_NAME, `<el-tabs><tab-nav /></el-tabs>`);\n    const ns = useNamespace(\"tabs\");\n    const visibility = useDocumentVisibility();\n    const focused = useWindowFocus();\n    const navScroll$ = ref();\n    const nav$ = ref();\n    const el$ = ref();\n    const tabBarRef = ref();\n    const scrollable = ref(false);\n    const navOffset = ref(0);\n    const isFocus = ref(false);\n    const focusable = ref(true);\n    const sizeName = computed(() => [\"top\", \"bottom\"].includes(rootTabs.props.tabPosition) ? \"width\" : \"height\");\n    const navStyle = computed(() => {\n      const dir = sizeName.value === \"width\" ? \"X\" : \"Y\";\n      return {\n        transform: `translate${dir}(-${navOffset.value}px)`\n      };\n    });\n    const scrollPrev = () => {\n      if (!navScroll$.value) return;\n      const containerSize = navScroll$.value[`offset${capitalize(sizeName.value)}`];\n      const currentOffset = navOffset.value;\n      if (!currentOffset) return;\n      const newOffset = currentOffset > containerSize ? currentOffset - containerSize : 0;\n      navOffset.value = newOffset;\n    };\n    const scrollNext = () => {\n      if (!navScroll$.value || !nav$.value) return;\n      const navSize = nav$.value[`offset${capitalize(sizeName.value)}`];\n      const containerSize = navScroll$.value[`offset${capitalize(sizeName.value)}`];\n      const currentOffset = navOffset.value;\n      if (navSize - currentOffset <= containerSize) return;\n      const newOffset = navSize - currentOffset > containerSize * 2 ? currentOffset + containerSize : navSize - containerSize;\n      navOffset.value = newOffset;\n    };\n    const scrollToActiveTab = async () => {\n      const nav = nav$.value;\n      if (!scrollable.value || !el$.value || !navScroll$.value || !nav) return;\n      await nextTick();\n      const activeTab = el$.value.querySelector(\".is-active\");\n      if (!activeTab) return;\n      const navScroll = navScroll$.value;\n      const isHorizontal = [\"top\", \"bottom\"].includes(rootTabs.props.tabPosition);\n      const activeTabBounding = activeTab.getBoundingClientRect();\n      const navScrollBounding = navScroll.getBoundingClientRect();\n      const maxOffset = isHorizontal ? nav.offsetWidth - navScrollBounding.width : nav.offsetHeight - navScrollBounding.height;\n      const currentOffset = navOffset.value;\n      let newOffset = currentOffset;\n      if (isHorizontal) {\n        if (activeTabBounding.left < navScrollBounding.left) {\n          newOffset = currentOffset - (navScrollBounding.left - activeTabBounding.left);\n        }\n        if (activeTabBounding.right > navScrollBounding.right) {\n          newOffset = currentOffset + activeTabBounding.right - navScrollBounding.right;\n        }\n      } else {\n        if (activeTabBounding.top < navScrollBounding.top) {\n          newOffset = currentOffset - (navScrollBounding.top - activeTabBounding.top);\n        }\n        if (activeTabBounding.bottom > navScrollBounding.bottom) {\n          newOffset = currentOffset + (activeTabBounding.bottom - navScrollBounding.bottom);\n        }\n      }\n      newOffset = Math.max(newOffset, 0);\n      navOffset.value = Math.min(newOffset, maxOffset);\n    };\n    const update = () => {\n      var _a;\n      if (!nav$.value || !navScroll$.value) return;\n      props.stretch && ((_a = tabBarRef.value) == null ? void 0 : _a.update());\n      const navSize = nav$.value[`offset${capitalize(sizeName.value)}`];\n      const containerSize = navScroll$.value[`offset${capitalize(sizeName.value)}`];\n      const currentOffset = navOffset.value;\n      if (containerSize < navSize) {\n        scrollable.value = scrollable.value || {};\n        scrollable.value.prev = currentOffset;\n        scrollable.value.next = currentOffset + containerSize < navSize;\n        if (navSize - currentOffset < containerSize) {\n          navOffset.value = navSize - containerSize;\n        }\n      } else {\n        scrollable.value = false;\n        if (currentOffset > 0) {\n          navOffset.value = 0;\n        }\n      }\n    };\n    const changeTab = event => {\n      let step = 0;\n      switch (event.code) {\n        case EVENT_CODE.left:\n        case EVENT_CODE.up:\n          step = -1;\n          break;\n        case EVENT_CODE.right:\n        case EVENT_CODE.down:\n          step = 1;\n          break;\n        default:\n          return;\n      }\n      const tabList = Array.from(event.currentTarget.querySelectorAll(\"[role=tab]:not(.is-disabled)\"));\n      const currentIndex = tabList.indexOf(event.target);\n      let nextIndex = currentIndex + step;\n      if (nextIndex < 0) {\n        nextIndex = tabList.length - 1;\n      } else if (nextIndex >= tabList.length) {\n        nextIndex = 0;\n      }\n      tabList[nextIndex].focus({\n        preventScroll: true\n      });\n      tabList[nextIndex].click();\n      setFocus();\n    };\n    const setFocus = () => {\n      if (focusable.value) isFocus.value = true;\n    };\n    const removeFocus = () => isFocus.value = false;\n    watch(visibility, visibility2 => {\n      if (visibility2 === \"hidden\") {\n        focusable.value = false;\n      } else if (visibility2 === \"visible\") {\n        setTimeout(() => focusable.value = true, 50);\n      }\n    });\n    watch(focused, focused2 => {\n      if (focused2) {\n        setTimeout(() => focusable.value = true, 50);\n      } else {\n        focusable.value = false;\n      }\n    });\n    useResizeObserver(el$, update);\n    onMounted(() => setTimeout(() => scrollToActiveTab(), 0));\n    onUpdated(() => update());\n    expose({\n      scrollToActiveTab,\n      removeFocus\n    });\n    return () => {\n      const scrollBtn = scrollable.value ? [createVNode(\"span\", {\n        \"class\": [ns.e(\"nav-prev\"), ns.is(\"disabled\", !scrollable.value.prev)],\n        \"onClick\": scrollPrev\n      }, [createVNode(ElIcon, null, {\n        default: () => [createVNode(ArrowLeft, null, null)]\n      })]), createVNode(\"span\", {\n        \"class\": [ns.e(\"nav-next\"), ns.is(\"disabled\", !scrollable.value.next)],\n        \"onClick\": scrollNext\n      }, [createVNode(ElIcon, null, {\n        default: () => [createVNode(ArrowRight, null, null)]\n      })])] : null;\n      const tabs = props.panes.map((pane, index) => {\n        var _a, _b, _c, _d;\n        const uid = pane.uid;\n        const disabled = pane.props.disabled;\n        const tabName = (_b = (_a = pane.props.name) != null ? _a : pane.index) != null ? _b : `${index}`;\n        const closable = !disabled && (pane.isClosable || props.editable);\n        pane.index = `${index}`;\n        const btnClose = closable ? createVNode(ElIcon, {\n          \"class\": \"is-icon-close\",\n          \"onClick\": ev => emit(\"tabRemove\", pane, ev)\n        }, {\n          default: () => [createVNode(Close, null, null)]\n        }) : null;\n        const tabLabelContent = ((_d = (_c = pane.slots).label) == null ? void 0 : _d.call(_c)) || pane.props.label;\n        const tabindex = !disabled && pane.active ? 0 : -1;\n        return createVNode(\"div\", {\n          \"ref\": `tab-${uid}`,\n          \"class\": [ns.e(\"item\"), ns.is(rootTabs.props.tabPosition), ns.is(\"active\", pane.active), ns.is(\"disabled\", disabled), ns.is(\"closable\", closable), ns.is(\"focus\", isFocus.value)],\n          \"id\": `tab-${tabName}`,\n          \"key\": `tab-${uid}`,\n          \"aria-controls\": `pane-${tabName}`,\n          \"role\": \"tab\",\n          \"aria-selected\": pane.active,\n          \"tabindex\": tabindex,\n          \"onFocus\": () => setFocus(),\n          \"onBlur\": () => removeFocus(),\n          \"onClick\": ev => {\n            removeFocus();\n            emit(\"tabClick\", pane, tabName, ev);\n          },\n          \"onKeydown\": ev => {\n            if (closable && (ev.code === EVENT_CODE.delete || ev.code === EVENT_CODE.backspace)) {\n              emit(\"tabRemove\", pane, ev);\n            }\n          }\n        }, [...[tabLabelContent, btnClose]]);\n      });\n      return createVNode(\"div\", {\n        \"ref\": el$,\n        \"class\": [ns.e(\"nav-wrap\"), ns.is(\"scrollable\", !!scrollable.value), ns.is(rootTabs.props.tabPosition)]\n      }, [scrollBtn, createVNode(\"div\", {\n        \"class\": ns.e(\"nav-scroll\"),\n        \"ref\": navScroll$\n      }, [createVNode(\"div\", {\n        \"class\": [ns.e(\"nav\"), ns.is(rootTabs.props.tabPosition), ns.is(\"stretch\", props.stretch && [\"top\", \"bottom\"].includes(rootTabs.props.tabPosition))],\n        \"ref\": nav$,\n        \"style\": navStyle.value,\n        \"role\": \"tablist\",\n        \"onKeydown\": changeTab\n      }, [...[!props.type ? createVNode(TabBar, {\n        \"ref\": tabBarRef,\n        \"tabs\": [...props.panes]\n      }, null) : null, tabs]])])]);\n    };\n  }\n});\nexport { TabNav as default, tabNavEmits, tabNavProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}