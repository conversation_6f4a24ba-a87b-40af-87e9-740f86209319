{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createTextVNode as _createTextVNode, createStaticVNode as _createStaticVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"products-page\"\n};\nconst _hoisted_2 = {\n  class: \"products-grid\"\n};\nconst _hoisted_3 = {\n  class: \"container\"\n};\nconst _hoisted_4 = {\n  class: \"product-card\"\n};\nconst _hoisted_5 = {\n  class: \"features\"\n};\nconst _hoisted_6 = {\n  class: \"feature\"\n};\nconst _hoisted_7 = {\n  class: \"feature\"\n};\nconst _hoisted_8 = {\n  class: \"feature\"\n};\nconst _hoisted_9 = {\n  class: \"actions\"\n};\nconst _hoisted_10 = {\n  class: \"product-card\"\n};\nconst _hoisted_11 = {\n  class: \"features\"\n};\nconst _hoisted_12 = {\n  class: \"feature\"\n};\nconst _hoisted_13 = {\n  class: \"feature\"\n};\nconst _hoisted_14 = {\n  class: \"feature\"\n};\nconst _hoisted_15 = {\n  class: \"actions\"\n};\nconst _hoisted_16 = {\n  class: \"product-card\"\n};\nconst _hoisted_17 = {\n  class: \"features\"\n};\nconst _hoisted_18 = {\n  class: \"feature\"\n};\nconst _hoisted_19 = {\n  class: \"feature\"\n};\nconst _hoisted_20 = {\n  class: \"feature\"\n};\nconst _hoisted_21 = {\n  class: \"actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[18] || (_cache[18] = _createStaticVNode(\"<section class=\\\"products-hero\\\" data-v-7177119e><div class=\\\"hero-content\\\" data-v-7177119e><h1 class=\\\"title\\\" data-v-7177119e>创新产品</h1><p class=\\\"subtitle\\\" data-v-7177119e>打造极致用户体验的数字化工具</p></div><div class=\\\"hero-background\\\" data-v-7177119e><div class=\\\"floating-circle\\\" data-v-7177119e></div><div class=\\\"floating-dots\\\" data-v-7177119e></div></div></section>\", 1)), _createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" 智能高亮助手 \"), _createElementVNode(\"div\", _hoisted_4, [_cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n    class: \"product-badge\"\n  }, \"Browser Extension\", -1 /* HOISTED */)), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"product-header\"\n  }, [_createElementVNode(\"h2\", null, \"智能高亮助手\"), _createElementVNode(\"p\", {\n    class: \"description\"\n  }, \" 专业的网页文本智能高亮工具，让阅读和学习更高效。支持多分类管理、 颜色自定义、关键词管理和配置导出分享。 \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"Files\"])]),\n    _: 1 /* STABLE */\n  }), _cache[0] || (_cache[0] = _createElementVNode(\"span\", null, \"多分类高亮\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"Brush\"])]),\n    _: 1 /* STABLE */\n  }), _cache[1] || (_cache[1] = _createElementVNode(\"span\", null, \"颜色自定义\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"Share\"])]),\n    _: 1 /* STABLE */\n  }), _cache[2] || (_cache[2] = _createElementVNode(\"span\", null, \"配置分享\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_router_link, {\n    to: \"/products/highlight\",\n    class: \"learn-more\"\n  }, {\n    default: _withCtx(() => [_cache[3] || (_cache[3] = _createTextVNode(\" 了解更多 \")), _createVNode(_component_el_icon, {\n      class: \"icon-right\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"ArrowRight\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })])]), _createCommentVNode(\" 网页护眼助手 \"), _createElementVNode(\"div\", _hoisted_10, [_cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n    class: \"product-badge\"\n  }, \"Browser Extension\", -1 /* HOISTED */)), _cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n    class: \"product-header\"\n  }, [_createElementVNode(\"h2\", null, \"网页护眼助手\"), _createElementVNode(\"p\", {\n    class: \"description\"\n  }, \" 智能护眼工具，让网页浏览更舒适，保护您的眼睛健康。 通过智能算法，自动调节屏幕显示效果。 \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"Monitor\"])]),\n    _: 1 /* STABLE */\n  }), _cache[6] || (_cache[6] = _createElementVNode(\"span\", null, \"智能护眼模式\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"MagicStick\"])]),\n    _: 1 /* STABLE */\n  }), _cache[7] || (_cache[7] = _createElementVNode(\"span\", null, \"场景自适应\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"Setting\"])]),\n    _: 1 /* STABLE */\n  }), _cache[8] || (_cache[8] = _createElementVNode(\"span\", null, \"全局控制\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_router_link, {\n    to: \"/products/eyeshield\",\n    class: \"learn-more\"\n  }, {\n    default: _withCtx(() => [_cache[9] || (_cache[9] = _createTextVNode(\" 了解更多 \")), _createVNode(_component_el_icon, {\n      class: \"icon-right\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"ArrowRight\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })])]), _createCommentVNode(\" 智能翻译助手 \"), _createElementVNode(\"div\", _hoisted_16, [_cache[16] || (_cache[16] = _createElementVNode(\"div\", {\n    class: \"product-badge\"\n  }, \"Browser Extension\", -1 /* HOISTED */)), _cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n    class: \"product-header\"\n  }, [_createElementVNode(\"h2\", null, \"智能翻译助手\"), _createElementVNode(\"p\", {\n    class: \"description\"\n  }, \" 高效、准确的网页文本翻译工具，帮助您跨越语言障碍。 支持多语言翻译、划词翻译、智能识别等功能。 \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"ChatLineRound\"])]),\n    _: 1 /* STABLE */\n  }), _cache[12] || (_cache[12] = _createElementVNode(\"span\", null, \"多语言支持\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_19, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"Scissors\"])]),\n    _: 1 /* STABLE */\n  }), _cache[13] || (_cache[13] = _createElementVNode(\"span\", null, \"划词翻译\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_20, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"Connection\"])]),\n    _: 1 /* STABLE */\n  }), _cache[14] || (_cache[14] = _createElementVNode(\"span\", null, \"实时翻译\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_router_link, {\n    to: \"/products/translator\",\n    class: \"learn-more\"\n  }, {\n    default: _withCtx(() => [_cache[15] || (_cache[15] = _createTextVNode(\" 了解更多 \")), _createVNode(_component_el_icon, {\n      class: \"icon-right\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"ArrowRight\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })])])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createStaticVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createCommentVNode", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_createVNode", "_component_el_icon", "default", "_withCtx", "$setup", "_", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_component_router_link", "to", "_createTextVNode", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21"], "sources": ["D:\\codes\\glmwebsite\\geluman-website\\src\\views\\Products.vue"], "sourcesContent": ["<template>\r\n  <div class=\"products-page\">\r\n    <section class=\"products-hero\">\r\n      <div class=\"hero-content\">\r\n        <h1 class=\"title\">创新产品</h1>\r\n        <p class=\"subtitle\">打造极致用户体验的数字化工具</p>\r\n      </div>\r\n      <div class=\"hero-background\">\r\n        <div class=\"floating-circle\"></div>\r\n        <div class=\"floating-dots\"></div>\r\n      </div>\r\n    </section>\r\n\r\n    <section class=\"products-grid\">\r\n      <div class=\"container\">\r\n        <!-- 智能高亮助手 -->\r\n        <div class=\"product-card\">\r\n          <div class=\"product-badge\">Browser Extension</div>\r\n          <div class=\"product-header\">\r\n            <h2>智能高亮助手</h2>\r\n            <p class=\"description\">\r\n              专业的网页文本智能高亮工具，让阅读和学习更高效。支持多分类管理、\r\n              颜色自定义、关键词管理和配置导出分享。\r\n            </p>\r\n          </div>\r\n          <div class=\"features\">\r\n            <div class=\"feature\">\r\n              <el-icon><Files /></el-icon>\r\n              <span>多分类高亮</span>\r\n            </div>\r\n            <div class=\"feature\">\r\n              <el-icon><Brush /></el-icon>\r\n              <span>颜色自定义</span>\r\n            </div>\r\n            <div class=\"feature\">\r\n              <el-icon><Share /></el-icon>\r\n              <span>配置分享</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"actions\">\r\n            <router-link to=\"/products/highlight\" class=\"learn-more\">\r\n              了解更多\r\n              <el-icon class=\"icon-right\"><ArrowRight /></el-icon>\r\n            </router-link>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 网页护眼助手 -->\r\n        <div class=\"product-card\">\r\n          <div class=\"product-badge\">Browser Extension</div>\r\n          <div class=\"product-header\">\r\n            <h2>网页护眼助手</h2>\r\n            <p class=\"description\">\r\n              智能护眼工具，让网页浏览更舒适，保护您的眼睛健康。\r\n              通过智能算法，自动调节屏幕显示效果。\r\n            </p>\r\n          </div>\r\n          <div class=\"features\">\r\n            <div class=\"feature\">\r\n              <el-icon><Monitor /></el-icon>\r\n              <span>智能护眼模式</span>\r\n            </div>\r\n            <div class=\"feature\">\r\n              <el-icon><MagicStick /></el-icon>\r\n              <span>场景自适应</span>\r\n            </div>\r\n            <div class=\"feature\">\r\n              <el-icon><Setting /></el-icon>\r\n              <span>全局控制</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"actions\">\r\n            <router-link to=\"/products/eyeshield\" class=\"learn-more\">\r\n              了解更多\r\n              <el-icon class=\"icon-right\"><ArrowRight /></el-icon>\r\n            </router-link>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 智能翻译助手 -->\r\n        <div class=\"product-card\">\r\n          <div class=\"product-badge\">Browser Extension</div>\r\n          <div class=\"product-header\">\r\n            <h2>智能翻译助手</h2>\r\n            <p class=\"description\">\r\n              高效、准确的网页文本翻译工具，帮助您跨越语言障碍。\r\n              支持多语言翻译、划词翻译、智能识别等功能。\r\n            </p>\r\n          </div>\r\n          <div class=\"features\">\r\n            <div class=\"feature\">\r\n              <el-icon><ChatLineRound /></el-icon>\r\n              <span>多语言支持</span>\r\n            </div>\r\n            <div class=\"feature\">\r\n              <el-icon><Scissors /></el-icon>\r\n              <span>划词翻译</span>\r\n            </div>\r\n            <div class=\"feature\">\r\n              <el-icon><Connection /></el-icon>\r\n              <span>实时翻译</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"actions\">\r\n            <router-link to=\"/products/translator\" class=\"learn-more\">\r\n              了解更多\r\n              <el-icon class=\"icon-right\"><ArrowRight /></el-icon>\r\n            </router-link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { useI18n } from \"vue-i18n\";\r\nimport { useRouter } from \"vue-router\";\r\nimport { highlight, eyeshield, clock } from \"@/assets\";\r\nimport {\r\n  HomeFilled,\r\n  ArrowRight,\r\n  Files,\r\n  Brush,\r\n  Share,\r\n  Monitor,\r\n  MagicStick,\r\n  Setting,\r\n  ChatLineRound,\r\n  Scissors,\r\n  Connection,\r\n} from \"@element-plus/icons-vue\";\r\n\r\nconst { t } = useI18n();\r\nconst router = useRouter();\r\n\r\nconst navigateToProduct = (path) => {\r\n  router.push(path);\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.products-page {\r\n  padding-top: var(--header-height);\r\n}\r\n\r\n.products-hero {\r\n  background: linear-gradient(\r\n    135deg,\r\n    var(--primary-color) 0%,\r\n    var(--primary-dark) 100%\r\n  );\r\n  padding: 4rem 0;\r\n  text-align: center;\r\n  color: white;\r\n\r\n  .title {\r\n    font-size: 2.5rem;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 1.2rem;\r\n    opacity: 0.9;\r\n  }\r\n}\r\n\r\n.products-grid {\r\n  padding: 4rem 0;\r\n  background: var(--bg-secondary);\r\n\r\n  .container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 0 2rem;\r\n    display: grid;\r\n    gap: 3rem;\r\n  }\r\n}\r\n\r\n.product-card {\r\n  background: white;\r\n  border-radius: 20px;\r\n  padding: 2rem;\r\n  box-shadow: var(--shadow-sm);\r\n  transition: var(--transition-base);\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2rem;\r\n\r\n  &:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: var(--shadow-lg);\r\n  }\r\n\r\n  .product-badge {\r\n    display: inline-block;\r\n    padding: 0.5rem 1rem;\r\n    background: var(--bg-accent);\r\n    color: var(--primary-color);\r\n    border-radius: 20px;\r\n    font-size: 0.9rem;\r\n    width: fit-content;\r\n  }\r\n\r\n  .product-header {\r\n    h2 {\r\n      font-size: 1.8rem;\r\n      margin-bottom: 1rem;\r\n      color: var(--text-primary);\r\n    }\r\n\r\n    .description {\r\n      color: var(--text-secondary);\r\n      line-height: 1.6;\r\n    }\r\n  }\r\n\r\n  .features {\r\n    display: grid;\r\n    grid-template-columns: repeat(3, 1fr);\r\n    gap: 1.5rem;\r\n\r\n    .feature {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 0.8rem;\r\n      color: var(--text-secondary);\r\n\r\n      .el-icon {\r\n        color: var(--primary-color);\r\n        font-size: 1.2rem;\r\n      }\r\n    }\r\n  }\r\n\r\n  .actions {\r\n    margin-top: auto;\r\n    display: flex;\r\n    justify-content: flex-end;\r\n\r\n    .learn-more {\r\n      display: inline-flex;\r\n      align-items: center;\r\n      gap: 0.5rem;\r\n      color: var(--primary-color);\r\n      text-decoration: none;\r\n      font-weight: 500;\r\n      padding: 0.5rem 1rem;\r\n      border-radius: 20px;\r\n      transition: all 0.3s ease;\r\n      position: relative;\r\n      z-index: 10;\r\n      pointer-events: auto;\r\n\r\n      .icon-right {\r\n        transition: transform 0.3s ease;\r\n      }\r\n\r\n      &:hover {\r\n        background: var(--bg-accent);\r\n\r\n        .icon-right {\r\n          transform: translateX(4px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .products-hero {\r\n    padding: 3rem 1rem;\r\n  }\r\n\r\n  .products-grid {\r\n    padding: 2rem 1rem;\r\n\r\n    .product-card {\r\n      .features {\r\n        grid-template-columns: 1fr;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.hero-background {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: hidden;\r\n  z-index: 0;\r\n\r\n  .floating-circle {\r\n    position: absolute;\r\n    width: 300px;\r\n    height: 300px;\r\n    border-radius: 50%;\r\n    background: linear-gradient(\r\n      45deg,\r\n      rgba(255, 255, 255, 0.1),\r\n      rgba(255, 255, 255, 0.05)\r\n    );\r\n    animation: floatCircle 20s infinite linear;\r\n  }\r\n\r\n  .floating-dots {\r\n    position: absolute;\r\n    width: 100%;\r\n    height: 100%;\r\n    background-image: radial-gradient(\r\n      rgba(255, 255, 255, 0.1) 1px,\r\n      transparent 1px\r\n    );\r\n    background-size: 30px 30px;\r\n    animation: floatDots 40s infinite linear;\r\n  }\r\n}\r\n\r\n.product-card {\r\n  .features .feature {\r\n    .el-icon {\r\n      transition: transform 0.3s ease;\r\n    }\r\n\r\n    &:hover .el-icon {\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes floatCircle {\r\n  0% {\r\n    transform: translate(-50%, -50%) rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: translate(-50%, -50%) rotate(360deg);\r\n  }\r\n}\r\n\r\n@keyframes floatDots {\r\n  0% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(-20px);\r\n  }\r\n  100% {\r\n    transform: translateY(0);\r\n  }\r\n}\r\n</style>\r\n\r\n<script>\r\nexport default {\r\n  name: \"ProductsPage\",\r\n};\r\n</script>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAe;;EAYfA,KAAK,EAAC;AAAe;;EACvBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAc;;EASlBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAS;;EAIfA,KAAK,EAAC;AAAS;;EAIfA,KAAK,EAAC;AAAS;;EAKjBA,KAAK,EAAC;AAAS;;EASjBA,KAAK,EAAC;AAAc;;EASlBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAS;;EAIfA,KAAK,EAAC;AAAS;;EAIfA,KAAK,EAAC;AAAS;;EAKjBA,KAAK,EAAC;AAAS;;EASjBA,KAAK,EAAC;AAAc;;EASlBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAS;;EAIfA,KAAK,EAAC;AAAS;;EAIfA,KAAK,EAAC;AAAS;;EAKjBA,KAAK,EAAC;AAAS;;;;uBAtG5BC,mBAAA,CA+GM,OA/GNC,UA+GM,G,4BAhHRC,kBAAA,yXAaIC,mBAAA,CAkGU,WAlGVC,UAkGU,GAjGRD,mBAAA,CAgGM,OAhGNE,UAgGM,GA/FJC,mBAAA,YAAe,EACfH,mBAAA,CA6BM,OA7BNI,UA6BM,G,0BA5BJJ,mBAAA,CAAkD;IAA7CJ,KAAK,EAAC;EAAe,GAAC,mBAAiB,sB,0BAC5CI,mBAAA,CAMM;IANDJ,KAAK,EAAC;EAAgB,IACzBI,mBAAA,CAAe,YAAX,QAAM,GACVA,mBAAA,CAGI;IAHDJ,KAAK,EAAC;EAAa,GAAC,wDAGvB,E,sBAEFI,mBAAA,CAaM,OAbNK,UAaM,GAZJL,mBAAA,CAGM,OAHNM,UAGM,GAFJC,YAAA,CAA4BC,kBAAA;IA3B1CC,OAAA,EAAAC,QAAA,CA2BuB,MAAS,CAATH,YAAA,CAASI,MAAA,W;IA3BhCC,CAAA;gCA4BcZ,mBAAA,CAAkB,cAAZ,OAAK,qB,GAEbA,mBAAA,CAGM,OAHNa,UAGM,GAFJN,YAAA,CAA4BC,kBAAA;IA/B1CC,OAAA,EAAAC,QAAA,CA+BuB,MAAS,CAATH,YAAA,CAASI,MAAA,W;IA/BhCC,CAAA;gCAgCcZ,mBAAA,CAAkB,cAAZ,OAAK,qB,GAEbA,mBAAA,CAGM,OAHNc,UAGM,GAFJP,YAAA,CAA4BC,kBAAA;IAnC1CC,OAAA,EAAAC,QAAA,CAmCuB,MAAS,CAATH,YAAA,CAASI,MAAA,W;IAnChCC,CAAA;gCAoCcZ,mBAAA,CAAiB,cAAX,MAAI,qB,KAGdA,mBAAA,CAKM,OALNe,UAKM,GAJJR,YAAA,CAGcS,sBAAA;IAHDC,EAAE,EAAC,qBAAqB;IAACrB,KAAK,EAAC;;IAxCxDa,OAAA,EAAAC,QAAA,CAwCqE,MAEvD,C,0BA1CdQ,gBAAA,CAwCqE,QAEvD,IAAAX,YAAA,CAAoDC,kBAAA;MAA3CZ,KAAK,EAAC;IAAY;MA1CzCa,OAAA,EAAAC,QAAA,CA0C0C,MAAc,CAAdH,YAAA,CAAcI,MAAA,gB;MA1CxDC,CAAA;;IAAAA,CAAA;UA+CQT,mBAAA,YAAe,EACfH,mBAAA,CA6BM,OA7BNmB,WA6BM,G,4BA5BJnB,mBAAA,CAAkD;IAA7CJ,KAAK,EAAC;EAAe,GAAC,mBAAiB,sB,4BAC5CI,mBAAA,CAMM;IANDJ,KAAK,EAAC;EAAgB,IACzBI,mBAAA,CAAe,YAAX,QAAM,GACVA,mBAAA,CAGI;IAHDJ,KAAK,EAAC;EAAa,GAAC,gDAGvB,E,sBAEFI,mBAAA,CAaM,OAbNoB,WAaM,GAZJpB,mBAAA,CAGM,OAHNqB,WAGM,GAFJd,YAAA,CAA8BC,kBAAA;IA3D5CC,OAAA,EAAAC,QAAA,CA2DuB,MAAW,CAAXH,YAAA,CAAWI,MAAA,a;IA3DlCC,CAAA;gCA4DcZ,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAGM,OAHNsB,WAGM,GAFJf,YAAA,CAAiCC,kBAAA;IA/D/CC,OAAA,EAAAC,QAAA,CA+DuB,MAAc,CAAdH,YAAA,CAAcI,MAAA,gB;IA/DrCC,CAAA;gCAgEcZ,mBAAA,CAAkB,cAAZ,OAAK,qB,GAEbA,mBAAA,CAGM,OAHNuB,WAGM,GAFJhB,YAAA,CAA8BC,kBAAA;IAnE5CC,OAAA,EAAAC,QAAA,CAmEuB,MAAW,CAAXH,YAAA,CAAWI,MAAA,a;IAnElCC,CAAA;gCAoEcZ,mBAAA,CAAiB,cAAX,MAAI,qB,KAGdA,mBAAA,CAKM,OALNwB,WAKM,GAJJjB,YAAA,CAGcS,sBAAA;IAHDC,EAAE,EAAC,qBAAqB;IAACrB,KAAK,EAAC;;IAxExDa,OAAA,EAAAC,QAAA,CAwEqE,MAEvD,C,0BA1EdQ,gBAAA,CAwEqE,QAEvD,IAAAX,YAAA,CAAoDC,kBAAA;MAA3CZ,KAAK,EAAC;IAAY;MA1EzCa,OAAA,EAAAC,QAAA,CA0E0C,MAAc,CAAdH,YAAA,CAAcI,MAAA,gB;MA1ExDC,CAAA;;IAAAA,CAAA;UA+EQT,mBAAA,YAAe,EACfH,mBAAA,CA6BM,OA7BNyB,WA6BM,G,4BA5BJzB,mBAAA,CAAkD;IAA7CJ,KAAK,EAAC;EAAe,GAAC,mBAAiB,sB,4BAC5CI,mBAAA,CAMM;IANDJ,KAAK,EAAC;EAAgB,IACzBI,mBAAA,CAAe,YAAX,QAAM,GACVA,mBAAA,CAGI;IAHDJ,KAAK,EAAC;EAAa,GAAC,mDAGvB,E,sBAEFI,mBAAA,CAaM,OAbN0B,WAaM,GAZJ1B,mBAAA,CAGM,OAHN2B,WAGM,GAFJpB,YAAA,CAAoCC,kBAAA;IA3FlDC,OAAA,EAAAC,QAAA,CA2FuB,MAAiB,CAAjBH,YAAA,CAAiBI,MAAA,mB;IA3FxCC,CAAA;kCA4FcZ,mBAAA,CAAkB,cAAZ,OAAK,qB,GAEbA,mBAAA,CAGM,OAHN4B,WAGM,GAFJrB,YAAA,CAA+BC,kBAAA;IA/F7CC,OAAA,EAAAC,QAAA,CA+FuB,MAAY,CAAZH,YAAA,CAAYI,MAAA,c;IA/FnCC,CAAA;kCAgGcZ,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGM,OAHN6B,WAGM,GAFJtB,YAAA,CAAiCC,kBAAA;IAnG/CC,OAAA,EAAAC,QAAA,CAmGuB,MAAc,CAAdH,YAAA,CAAcI,MAAA,gB;IAnGrCC,CAAA;kCAoGcZ,mBAAA,CAAiB,cAAX,MAAI,qB,KAGdA,mBAAA,CAKM,OALN8B,WAKM,GAJJvB,YAAA,CAGcS,sBAAA;IAHDC,EAAE,EAAC,sBAAsB;IAACrB,KAAK,EAAC;;IAxGzDa,OAAA,EAAAC,QAAA,CAwGsE,MAExD,C,4BA1GdQ,gBAAA,CAwGsE,QAExD,IAAAX,YAAA,CAAoDC,kBAAA;MAA3CZ,KAAK,EAAC;IAAY;MA1GzCa,OAAA,EAAAC,QAAA,CA0G0C,MAAc,CAAdH,YAAA,CAAcI,MAAA,gB;MA1GxDC,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}