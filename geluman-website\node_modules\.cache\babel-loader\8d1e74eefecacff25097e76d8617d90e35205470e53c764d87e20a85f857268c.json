{"ast": null, "code": "import identity from './identity.js';\nimport metaMap from './_metaMap.js';\n\n/**\n * The base implementation of `setData` without support for hot loop shorting.\n *\n * @private\n * @param {Function} func The function to associate metadata with.\n * @param {*} data The metadata.\n * @returns {Function} Returns `func`.\n */\nvar baseSetData = !metaMap ? identity : function (func, data) {\n  metaMap.set(func, data);\n  return func;\n};\nexport default baseSetData;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}