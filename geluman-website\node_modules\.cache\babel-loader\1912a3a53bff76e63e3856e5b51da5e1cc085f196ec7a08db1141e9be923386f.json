{"ast": null, "code": "import Transfer from './src/transfer2.mjs';\nexport { LEFT_CHECK_CHANGE_EVENT, RIGHT_CHECK_CHANGE_EVENT, transferCheckedChangeFn, transferEmits, transferProps } from './src/transfer.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElTransfer = withInstall(Transfer);\nexport { ElTransfer, ElTransfer as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}