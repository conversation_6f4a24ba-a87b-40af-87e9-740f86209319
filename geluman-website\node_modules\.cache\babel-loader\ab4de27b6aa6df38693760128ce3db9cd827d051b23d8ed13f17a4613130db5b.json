{"ast": null, "code": "import { capitalize as capitalize$1, hyphenate } from '@vue/shared';\nexport { camelize, hyphenate } from '@vue/shared';\nconst kebabCase = hyphenate;\nconst escapeStringRegexp = (string = \"\") => string.replace(/[|\\\\{}()[\\]^$+*?.]/g, \"\\\\$&\").replace(/-/g, \"\\\\x2d\");\nconst capitalize = str => capitalize$1(str);\nexport { capitalize, escapeStringRegexp, kebabCase };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}