{"ast": null, "code": "import Carousel from './src/carousel2.mjs';\nimport CarouselItem from './src/carousel-item2.mjs';\nexport { carouselEmits, carouselProps } from './src/carousel.mjs';\nexport { carouselItemProps } from './src/carousel-item.mjs';\nexport { CAROUSEL_ITEM_NAME, carouselContextKey } from './src/constants.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElCarousel = withInstall(Carousel, {\n  CarouselItem\n});\nconst ElCarouselItem = withNoopInstall(CarouselItem);\nexport { ElCarousel, ElCarouselItem, ElCarousel as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}