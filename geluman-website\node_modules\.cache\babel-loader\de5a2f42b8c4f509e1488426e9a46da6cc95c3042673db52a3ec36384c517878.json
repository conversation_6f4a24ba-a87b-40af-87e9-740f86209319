{"ast": null, "code": "import { renderSlot, createVNode } from 'vue';\nconst HeaderCell = (props, {\n  slots\n}) => renderSlot(slots, \"default\", props, () => {\n  var _a, _b;\n  return [createVNode(\"div\", {\n    \"class\": props.class,\n    \"title\": (_a = props.column) == null ? void 0 : _a.title\n  }, [(_b = props.column) == null ? void 0 : _b.title])];\n});\nHeaderCell.displayName = \"ElTableV2HeaderCell\";\nHeaderCell.inheritAttrs = false;\nvar HeaderCell$1 = HeaderCell;\nexport { HeaderCell$1 as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}