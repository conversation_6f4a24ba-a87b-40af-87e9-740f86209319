{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst tabPaneProps = buildProps({\n  label: {\n    type: String,\n    default: \"\"\n  },\n  name: {\n    type: [String, Number]\n  },\n  closable: <PERSON><PERSON>an,\n  disabled: <PERSON><PERSON><PERSON>,\n  lazy: <PERSON><PERSON><PERSON>\n});\nexport { tabPaneProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}