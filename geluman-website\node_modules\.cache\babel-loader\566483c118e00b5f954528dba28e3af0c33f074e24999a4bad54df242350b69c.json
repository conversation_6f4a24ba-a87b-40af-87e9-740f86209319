{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\n/*!\n  * message-compiler v9.14.2\n  * (c) 2024 kazuya kawaguchi\n  * Released under the MIT License.\n  */\nconst LOCATION_STUB = {\n  start: {\n    line: 1,\n    column: 1,\n    offset: 0\n  },\n  end: {\n    line: 1,\n    column: 1,\n    offset: 0\n  }\n};\nfunction createPosition(line, column, offset) {\n  return {\n    line,\n    column,\n    offset\n  };\n}\nfunction createLocation(start, end, source) {\n  const loc = {\n    start,\n    end\n  };\n  if (source != null) {\n    loc.source = source;\n  }\n  return loc;\n}\n\n/**\n * Original Utilities\n * written by kazuya kawaguchi\n */\nconst RE_ARGS = /\\{([0-9a-zA-Z]+)\\}/g;\n/* eslint-disable */\nfunction format(message, ...args) {\n  if (args.length === 1 && isObject(args[0])) {\n    args = args[0];\n  }\n  if (!args || !args.hasOwnProperty) {\n    args = {};\n  }\n  return message.replace(RE_ARGS, (match, identifier) => {\n    return args.hasOwnProperty(identifier) ? args[identifier] : '';\n  });\n}\nconst assign = Object.assign;\nconst isString = val => typeof val === 'string';\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst isObject = val => val !== null && typeof val === 'object';\nfunction join(items, separator = '') {\n  return items.reduce((str, item, index) => index === 0 ? str + item : str + separator + item, '');\n}\nconst CompileWarnCodes = {\n  USE_MODULO_SYNTAX: 1,\n  __EXTEND_POINT__: 2\n};\n/** @internal */\nconst warnMessages = {\n  [CompileWarnCodes.USE_MODULO_SYNTAX]: `Use modulo before '{{0}}'.`\n};\nfunction createCompileWarn(code, loc, ...args) {\n  const msg = format(warnMessages[code] || '', ...(args || []));\n  const message = {\n    message: String(msg),\n    code\n  };\n  if (loc) {\n    message.location = loc;\n  }\n  return message;\n}\nconst CompileErrorCodes = {\n  // tokenizer error codes\n  EXPECTED_TOKEN: 1,\n  INVALID_TOKEN_IN_PLACEHOLDER: 2,\n  UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER: 3,\n  UNKNOWN_ESCAPE_SEQUENCE: 4,\n  INVALID_UNICODE_ESCAPE_SEQUENCE: 5,\n  UNBALANCED_CLOSING_BRACE: 6,\n  UNTERMINATED_CLOSING_BRACE: 7,\n  EMPTY_PLACEHOLDER: 8,\n  NOT_ALLOW_NEST_PLACEHOLDER: 9,\n  INVALID_LINKED_FORMAT: 10,\n  // parser error codes\n  MUST_HAVE_MESSAGES_IN_PLURAL: 11,\n  UNEXPECTED_EMPTY_LINKED_MODIFIER: 12,\n  UNEXPECTED_EMPTY_LINKED_KEY: 13,\n  UNEXPECTED_LEXICAL_ANALYSIS: 14,\n  // generator error codes\n  UNHANDLED_CODEGEN_NODE_TYPE: 15,\n  // minifier error codes\n  UNHANDLED_MINIFIER_NODE_TYPE: 16,\n  // Special value for higher-order compilers to pick up the last code\n  // to avoid collision of error codes. This should always be kept as the last\n  // item.\n  __EXTEND_POINT__: 17\n};\n/** @internal */\nconst errorMessages = {\n  // tokenizer error messages\n  [CompileErrorCodes.EXPECTED_TOKEN]: `Expected token: '{0}'`,\n  [CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER]: `Invalid token in placeholder: '{0}'`,\n  [CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]: `Unterminated single quote in placeholder`,\n  [CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE]: `Unknown escape sequence: \\\\{0}`,\n  [CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE]: `Invalid unicode escape sequence: {0}`,\n  [CompileErrorCodes.UNBALANCED_CLOSING_BRACE]: `Unbalanced closing brace`,\n  [CompileErrorCodes.UNTERMINATED_CLOSING_BRACE]: `Unterminated closing brace`,\n  [CompileErrorCodes.EMPTY_PLACEHOLDER]: `Empty placeholder`,\n  [CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER]: `Not allowed nest placeholder`,\n  [CompileErrorCodes.INVALID_LINKED_FORMAT]: `Invalid linked format`,\n  // parser error messages\n  [CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL]: `Plural must have messages`,\n  [CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER]: `Unexpected empty linked modifier`,\n  [CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY]: `Unexpected empty linked key`,\n  [CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS]: `Unexpected lexical analysis in token: '{0}'`,\n  // generator error messages\n  [CompileErrorCodes.UNHANDLED_CODEGEN_NODE_TYPE]: `unhandled codegen node type: '{0}'`,\n  // minimizer error messages\n  [CompileErrorCodes.UNHANDLED_MINIFIER_NODE_TYPE]: `unhandled mimifier node type: '{0}'`\n};\nfunction createCompileError(code, loc, options = {}) {\n  const {\n    domain,\n    messages,\n    args\n  } = options;\n  const msg = format((messages || errorMessages)[code] || '', ...(args || []));\n  const error = new SyntaxError(String(msg));\n  error.code = code;\n  if (loc) {\n    error.location = loc;\n  }\n  error.domain = domain;\n  return error;\n}\n/** @internal */\nfunction defaultOnError(error) {\n  throw error;\n}\n\n// eslint-disable-next-line no-useless-escape\nconst RE_HTML_TAG = /<\\/?[\\w\\s=\"/.':;#-\\/]+>/;\nconst detectHtmlTag = source => RE_HTML_TAG.test(source);\nconst CHAR_SP = ' ';\nconst CHAR_CR = '\\r';\nconst CHAR_LF = '\\n';\nconst CHAR_LS = String.fromCharCode(0x2028);\nconst CHAR_PS = String.fromCharCode(0x2029);\nfunction createScanner(str) {\n  const _buf = str;\n  let _index = 0;\n  let _line = 1;\n  let _column = 1;\n  let _peekOffset = 0;\n  const isCRLF = index => _buf[index] === CHAR_CR && _buf[index + 1] === CHAR_LF;\n  const isLF = index => _buf[index] === CHAR_LF;\n  const isPS = index => _buf[index] === CHAR_PS;\n  const isLS = index => _buf[index] === CHAR_LS;\n  const isLineEnd = index => isCRLF(index) || isLF(index) || isPS(index) || isLS(index);\n  const index = () => _index;\n  const line = () => _line;\n  const column = () => _column;\n  const peekOffset = () => _peekOffset;\n  const charAt = offset => isCRLF(offset) || isPS(offset) || isLS(offset) ? CHAR_LF : _buf[offset];\n  const currentChar = () => charAt(_index);\n  const currentPeek = () => charAt(_index + _peekOffset);\n  function next() {\n    _peekOffset = 0;\n    if (isLineEnd(_index)) {\n      _line++;\n      _column = 0;\n    }\n    if (isCRLF(_index)) {\n      _index++;\n    }\n    _index++;\n    _column++;\n    return _buf[_index];\n  }\n  function peek() {\n    if (isCRLF(_index + _peekOffset)) {\n      _peekOffset++;\n    }\n    _peekOffset++;\n    return _buf[_index + _peekOffset];\n  }\n  function reset() {\n    _index = 0;\n    _line = 1;\n    _column = 1;\n    _peekOffset = 0;\n  }\n  function resetPeek(offset = 0) {\n    _peekOffset = offset;\n  }\n  function skipToPeek() {\n    const target = _index + _peekOffset;\n    // eslint-disable-next-line no-unmodified-loop-condition\n    while (target !== _index) {\n      next();\n    }\n    _peekOffset = 0;\n  }\n  return {\n    index,\n    line,\n    column,\n    peekOffset,\n    charAt,\n    currentChar,\n    currentPeek,\n    next,\n    peek,\n    reset,\n    resetPeek,\n    skipToPeek\n  };\n}\nconst EOF = undefined;\nconst DOT = '.';\nconst LITERAL_DELIMITER = \"'\";\nconst ERROR_DOMAIN$3 = 'tokenizer';\nfunction createTokenizer(source, options = {}) {\n  const location = options.location !== false;\n  const _scnr = createScanner(source);\n  const currentOffset = () => _scnr.index();\n  const currentPosition = () => createPosition(_scnr.line(), _scnr.column(), _scnr.index());\n  const _initLoc = currentPosition();\n  const _initOffset = currentOffset();\n  const _context = {\n    currentType: 14 /* TokenTypes.EOF */,\n    offset: _initOffset,\n    startLoc: _initLoc,\n    endLoc: _initLoc,\n    lastType: 14 /* TokenTypes.EOF */,\n    lastOffset: _initOffset,\n    lastStartLoc: _initLoc,\n    lastEndLoc: _initLoc,\n    braceNest: 0,\n    inLinked: false,\n    text: ''\n  };\n  const context = () => _context;\n  const {\n    onError\n  } = options;\n  function emitError(code, pos, offset, ...args) {\n    const ctx = context();\n    pos.column += offset;\n    pos.offset += offset;\n    if (onError) {\n      const loc = location ? createLocation(ctx.startLoc, pos) : null;\n      const err = createCompileError(code, loc, {\n        domain: ERROR_DOMAIN$3,\n        args\n      });\n      onError(err);\n    }\n  }\n  function getToken(context, type, value) {\n    context.endLoc = currentPosition();\n    context.currentType = type;\n    const token = {\n      type\n    };\n    if (location) {\n      token.loc = createLocation(context.startLoc, context.endLoc);\n    }\n    if (value != null) {\n      token.value = value;\n    }\n    return token;\n  }\n  const getEndToken = context => getToken(context, 14 /* TokenTypes.EOF */);\n  function eat(scnr, ch) {\n    if (scnr.currentChar() === ch) {\n      scnr.next();\n      return ch;\n    } else {\n      emitError(CompileErrorCodes.EXPECTED_TOKEN, currentPosition(), 0, ch);\n      return '';\n    }\n  }\n  function peekSpaces(scnr) {\n    let buf = '';\n    while (scnr.currentPeek() === CHAR_SP || scnr.currentPeek() === CHAR_LF) {\n      buf += scnr.currentPeek();\n      scnr.peek();\n    }\n    return buf;\n  }\n  function skipSpaces(scnr) {\n    const buf = peekSpaces(scnr);\n    scnr.skipToPeek();\n    return buf;\n  }\n  function isIdentifierStart(ch) {\n    if (ch === EOF) {\n      return false;\n    }\n    const cc = ch.charCodeAt(0);\n    return cc >= 97 && cc <= 122 ||\n    // a-z\n    cc >= 65 && cc <= 90 ||\n    // A-Z\n    cc === 95 // _\n    ;\n  }\n  function isNumberStart(ch) {\n    if (ch === EOF) {\n      return false;\n    }\n    const cc = ch.charCodeAt(0);\n    return cc >= 48 && cc <= 57; // 0-9\n  }\n  function isNamedIdentifierStart(scnr, context) {\n    const {\n      currentType\n    } = context;\n    if (currentType !== 2 /* TokenTypes.BraceLeft */) {\n      return false;\n    }\n    peekSpaces(scnr);\n    const ret = isIdentifierStart(scnr.currentPeek());\n    scnr.resetPeek();\n    return ret;\n  }\n  function isListIdentifierStart(scnr, context) {\n    const {\n      currentType\n    } = context;\n    if (currentType !== 2 /* TokenTypes.BraceLeft */) {\n      return false;\n    }\n    peekSpaces(scnr);\n    const ch = scnr.currentPeek() === '-' ? scnr.peek() : scnr.currentPeek();\n    const ret = isNumberStart(ch);\n    scnr.resetPeek();\n    return ret;\n  }\n  function isLiteralStart(scnr, context) {\n    const {\n      currentType\n    } = context;\n    if (currentType !== 2 /* TokenTypes.BraceLeft */) {\n      return false;\n    }\n    peekSpaces(scnr);\n    const ret = scnr.currentPeek() === LITERAL_DELIMITER;\n    scnr.resetPeek();\n    return ret;\n  }\n  function isLinkedDotStart(scnr, context) {\n    const {\n      currentType\n    } = context;\n    if (currentType !== 8 /* TokenTypes.LinkedAlias */) {\n      return false;\n    }\n    peekSpaces(scnr);\n    const ret = scnr.currentPeek() === \".\" /* TokenChars.LinkedDot */;\n    scnr.resetPeek();\n    return ret;\n  }\n  function isLinkedModifierStart(scnr, context) {\n    const {\n      currentType\n    } = context;\n    if (currentType !== 9 /* TokenTypes.LinkedDot */) {\n      return false;\n    }\n    peekSpaces(scnr);\n    const ret = isIdentifierStart(scnr.currentPeek());\n    scnr.resetPeek();\n    return ret;\n  }\n  function isLinkedDelimiterStart(scnr, context) {\n    const {\n      currentType\n    } = context;\n    if (!(currentType === 8 /* TokenTypes.LinkedAlias */ || currentType === 12 /* TokenTypes.LinkedModifier */)) {\n      return false;\n    }\n    peekSpaces(scnr);\n    const ret = scnr.currentPeek() === \":\" /* TokenChars.LinkedDelimiter */;\n    scnr.resetPeek();\n    return ret;\n  }\n  function isLinkedReferStart(scnr, context) {\n    const {\n      currentType\n    } = context;\n    if (currentType !== 10 /* TokenTypes.LinkedDelimiter */) {\n      return false;\n    }\n    const fn = () => {\n      const ch = scnr.currentPeek();\n      if (ch === \"{\" /* TokenChars.BraceLeft */) {\n        return isIdentifierStart(scnr.peek());\n      } else if (ch === \"@\" /* TokenChars.LinkedAlias */ || ch === \"%\" /* TokenChars.Modulo */ || ch === \"|\" /* TokenChars.Pipe */ || ch === \":\" /* TokenChars.LinkedDelimiter */ || ch === \".\" /* TokenChars.LinkedDot */ || ch === CHAR_SP || !ch) {\n        return false;\n      } else if (ch === CHAR_LF) {\n        scnr.peek();\n        return fn();\n      } else {\n        // other characters\n        return isTextStart(scnr, false);\n      }\n    };\n    const ret = fn();\n    scnr.resetPeek();\n    return ret;\n  }\n  function isPluralStart(scnr) {\n    peekSpaces(scnr);\n    const ret = scnr.currentPeek() === \"|\" /* TokenChars.Pipe */;\n    scnr.resetPeek();\n    return ret;\n  }\n  function detectModuloStart(scnr) {\n    const spaces = peekSpaces(scnr);\n    const ret = scnr.currentPeek() === \"%\" /* TokenChars.Modulo */ && scnr.peek() === \"{\" /* TokenChars.BraceLeft */;\n    scnr.resetPeek();\n    return {\n      isModulo: ret,\n      hasSpace: spaces.length > 0\n    };\n  }\n  function isTextStart(scnr, reset = true) {\n    const fn = (hasSpace = false, prev = '', detectModulo = false) => {\n      const ch = scnr.currentPeek();\n      if (ch === \"{\" /* TokenChars.BraceLeft */) {\n        return prev === \"%\" /* TokenChars.Modulo */ ? false : hasSpace;\n      } else if (ch === \"@\" /* TokenChars.LinkedAlias */ || !ch) {\n        return prev === \"%\" /* TokenChars.Modulo */ ? true : hasSpace;\n      } else if (ch === \"%\" /* TokenChars.Modulo */) {\n        scnr.peek();\n        return fn(hasSpace, \"%\" /* TokenChars.Modulo */, true);\n      } else if (ch === \"|\" /* TokenChars.Pipe */) {\n        return prev === \"%\" /* TokenChars.Modulo */ || detectModulo ? true : !(prev === CHAR_SP || prev === CHAR_LF);\n      } else if (ch === CHAR_SP) {\n        scnr.peek();\n        return fn(true, CHAR_SP, detectModulo);\n      } else if (ch === CHAR_LF) {\n        scnr.peek();\n        return fn(true, CHAR_LF, detectModulo);\n      } else {\n        return true;\n      }\n    };\n    const ret = fn();\n    reset && scnr.resetPeek();\n    return ret;\n  }\n  function takeChar(scnr, fn) {\n    const ch = scnr.currentChar();\n    if (ch === EOF) {\n      return EOF;\n    }\n    if (fn(ch)) {\n      scnr.next();\n      return ch;\n    }\n    return null;\n  }\n  function isIdentifier(ch) {\n    const cc = ch.charCodeAt(0);\n    return cc >= 97 && cc <= 122 ||\n    // a-z\n    cc >= 65 && cc <= 90 ||\n    // A-Z\n    cc >= 48 && cc <= 57 ||\n    // 0-9\n    cc === 95 ||\n    // _\n    cc === 36 // $\n    ;\n  }\n  function takeIdentifierChar(scnr) {\n    return takeChar(scnr, isIdentifier);\n  }\n  function isNamedIdentifier(ch) {\n    const cc = ch.charCodeAt(0);\n    return cc >= 97 && cc <= 122 ||\n    // a-z\n    cc >= 65 && cc <= 90 ||\n    // A-Z\n    cc >= 48 && cc <= 57 ||\n    // 0-9\n    cc === 95 ||\n    // _\n    cc === 36 ||\n    // $\n    cc === 45 // -\n    ;\n  }\n  function takeNamedIdentifierChar(scnr) {\n    return takeChar(scnr, isNamedIdentifier);\n  }\n  function isDigit(ch) {\n    const cc = ch.charCodeAt(0);\n    return cc >= 48 && cc <= 57; // 0-9\n  }\n  function takeDigit(scnr) {\n    return takeChar(scnr, isDigit);\n  }\n  function isHexDigit(ch) {\n    const cc = ch.charCodeAt(0);\n    return cc >= 48 && cc <= 57 ||\n    // 0-9\n    cc >= 65 && cc <= 70 ||\n    // A-F\n    cc >= 97 && cc <= 102; // a-f\n  }\n  function takeHexDigit(scnr) {\n    return takeChar(scnr, isHexDigit);\n  }\n  function getDigits(scnr) {\n    let ch = '';\n    let num = '';\n    while (ch = takeDigit(scnr)) {\n      num += ch;\n    }\n    return num;\n  }\n  function readModulo(scnr) {\n    skipSpaces(scnr);\n    const ch = scnr.currentChar();\n    if (ch !== \"%\" /* TokenChars.Modulo */) {\n      emitError(CompileErrorCodes.EXPECTED_TOKEN, currentPosition(), 0, ch);\n    }\n    scnr.next();\n    return \"%\" /* TokenChars.Modulo */;\n  }\n  function readText(scnr) {\n    let buf = '';\n    // eslint-disable-next-line no-constant-condition\n    while (true) {\n      const ch = scnr.currentChar();\n      if (ch === \"{\" /* TokenChars.BraceLeft */ || ch === \"}\" /* TokenChars.BraceRight */ || ch === \"@\" /* TokenChars.LinkedAlias */ || ch === \"|\" /* TokenChars.Pipe */ || !ch) {\n        break;\n      } else if (ch === \"%\" /* TokenChars.Modulo */) {\n        if (isTextStart(scnr)) {\n          buf += ch;\n          scnr.next();\n        } else {\n          break;\n        }\n      } else if (ch === CHAR_SP || ch === CHAR_LF) {\n        if (isTextStart(scnr)) {\n          buf += ch;\n          scnr.next();\n        } else if (isPluralStart(scnr)) {\n          break;\n        } else {\n          buf += ch;\n          scnr.next();\n        }\n      } else {\n        buf += ch;\n        scnr.next();\n      }\n    }\n    return buf;\n  }\n  function readNamedIdentifier(scnr) {\n    skipSpaces(scnr);\n    let ch = '';\n    let name = '';\n    while (ch = takeNamedIdentifierChar(scnr)) {\n      name += ch;\n    }\n    if (scnr.currentChar() === EOF) {\n      emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n    }\n    return name;\n  }\n  function readListIdentifier(scnr) {\n    skipSpaces(scnr);\n    let value = '';\n    if (scnr.currentChar() === '-') {\n      scnr.next();\n      value += `-${getDigits(scnr)}`;\n    } else {\n      value += getDigits(scnr);\n    }\n    if (scnr.currentChar() === EOF) {\n      emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n    }\n    return value;\n  }\n  function isLiteral(ch) {\n    return ch !== LITERAL_DELIMITER && ch !== CHAR_LF;\n  }\n  function readLiteral(scnr) {\n    skipSpaces(scnr);\n    // eslint-disable-next-line no-useless-escape\n    eat(scnr, `\\'`);\n    let ch = '';\n    let literal = '';\n    while (ch = takeChar(scnr, isLiteral)) {\n      if (ch === '\\\\') {\n        literal += readEscapeSequence(scnr);\n      } else {\n        literal += ch;\n      }\n    }\n    const current = scnr.currentChar();\n    if (current === CHAR_LF || current === EOF) {\n      emitError(CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER, currentPosition(), 0);\n      // TODO: Is it correct really?\n      if (current === CHAR_LF) {\n        scnr.next();\n        // eslint-disable-next-line no-useless-escape\n        eat(scnr, `\\'`);\n      }\n      return literal;\n    }\n    // eslint-disable-next-line no-useless-escape\n    eat(scnr, `\\'`);\n    return literal;\n  }\n  function readEscapeSequence(scnr) {\n    const ch = scnr.currentChar();\n    switch (ch) {\n      case '\\\\':\n      case `\\'`:\n        // eslint-disable-line no-useless-escape\n        scnr.next();\n        return `\\\\${ch}`;\n      case 'u':\n        return readUnicodeEscapeSequence(scnr, ch, 4);\n      case 'U':\n        return readUnicodeEscapeSequence(scnr, ch, 6);\n      default:\n        emitError(CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE, currentPosition(), 0, ch);\n        return '';\n    }\n  }\n  function readUnicodeEscapeSequence(scnr, unicode, digits) {\n    eat(scnr, unicode);\n    let sequence = '';\n    for (let i = 0; i < digits; i++) {\n      const ch = takeHexDigit(scnr);\n      if (!ch) {\n        emitError(CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE, currentPosition(), 0, `\\\\${unicode}${sequence}${scnr.currentChar()}`);\n        break;\n      }\n      sequence += ch;\n    }\n    return `\\\\${unicode}${sequence}`;\n  }\n  function isInvalidIdentifier(ch) {\n    return ch !== \"{\" /* TokenChars.BraceLeft */ && ch !== \"}\" /* TokenChars.BraceRight */ && ch !== CHAR_SP && ch !== CHAR_LF;\n  }\n  function readInvalidIdentifier(scnr) {\n    skipSpaces(scnr);\n    let ch = '';\n    let identifiers = '';\n    while (ch = takeChar(scnr, isInvalidIdentifier)) {\n      identifiers += ch;\n    }\n    return identifiers;\n  }\n  function readLinkedModifier(scnr) {\n    let ch = '';\n    let name = '';\n    while (ch = takeIdentifierChar(scnr)) {\n      name += ch;\n    }\n    return name;\n  }\n  function readLinkedRefer(scnr) {\n    const fn = buf => {\n      const ch = scnr.currentChar();\n      if (ch === \"{\" /* TokenChars.BraceLeft */ || ch === \"%\" /* TokenChars.Modulo */ || ch === \"@\" /* TokenChars.LinkedAlias */ || ch === \"|\" /* TokenChars.Pipe */ || ch === \"(\" /* TokenChars.ParenLeft */ || ch === \")\" /* TokenChars.ParenRight */ || !ch) {\n        return buf;\n      } else if (ch === CHAR_SP) {\n        return buf;\n      } else if (ch === CHAR_LF || ch === DOT) {\n        buf += ch;\n        scnr.next();\n        return fn(buf);\n      } else {\n        buf += ch;\n        scnr.next();\n        return fn(buf);\n      }\n    };\n    return fn('');\n  }\n  function readPlural(scnr) {\n    skipSpaces(scnr);\n    const plural = eat(scnr, \"|\" /* TokenChars.Pipe */);\n    skipSpaces(scnr);\n    return plural;\n  }\n  // TODO: We need refactoring of token parsing ...\n  function readTokenInPlaceholder(scnr, context) {\n    let token = null;\n    const ch = scnr.currentChar();\n    switch (ch) {\n      case \"{\" /* TokenChars.BraceLeft */:\n        if (context.braceNest >= 1) {\n          emitError(CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER, currentPosition(), 0);\n        }\n        scnr.next();\n        token = getToken(context, 2 /* TokenTypes.BraceLeft */, \"{\" /* TokenChars.BraceLeft */);\n        skipSpaces(scnr);\n        context.braceNest++;\n        return token;\n      case \"}\" /* TokenChars.BraceRight */:\n        if (context.braceNest > 0 && context.currentType === 2 /* TokenTypes.BraceLeft */) {\n          emitError(CompileErrorCodes.EMPTY_PLACEHOLDER, currentPosition(), 0);\n        }\n        scnr.next();\n        token = getToken(context, 3 /* TokenTypes.BraceRight */, \"}\" /* TokenChars.BraceRight */);\n        context.braceNest--;\n        context.braceNest > 0 && skipSpaces(scnr);\n        if (context.inLinked && context.braceNest === 0) {\n          context.inLinked = false;\n        }\n        return token;\n      case \"@\" /* TokenChars.LinkedAlias */:\n        if (context.braceNest > 0) {\n          emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n        }\n        token = readTokenInLinked(scnr, context) || getEndToken(context);\n        context.braceNest = 0;\n        return token;\n      default:\n        {\n          let validNamedIdentifier = true;\n          let validListIdentifier = true;\n          let validLiteral = true;\n          if (isPluralStart(scnr)) {\n            if (context.braceNest > 0) {\n              emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n            }\n            token = getToken(context, 1 /* TokenTypes.Pipe */, readPlural(scnr));\n            // reset\n            context.braceNest = 0;\n            context.inLinked = false;\n            return token;\n          }\n          if (context.braceNest > 0 && (context.currentType === 5 /* TokenTypes.Named */ || context.currentType === 6 /* TokenTypes.List */ || context.currentType === 7 /* TokenTypes.Literal */)) {\n            emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n            context.braceNest = 0;\n            return readToken(scnr, context);\n          }\n          if (validNamedIdentifier = isNamedIdentifierStart(scnr, context)) {\n            token = getToken(context, 5 /* TokenTypes.Named */, readNamedIdentifier(scnr));\n            skipSpaces(scnr);\n            return token;\n          }\n          if (validListIdentifier = isListIdentifierStart(scnr, context)) {\n            token = getToken(context, 6 /* TokenTypes.List */, readListIdentifier(scnr));\n            skipSpaces(scnr);\n            return token;\n          }\n          if (validLiteral = isLiteralStart(scnr, context)) {\n            token = getToken(context, 7 /* TokenTypes.Literal */, readLiteral(scnr));\n            skipSpaces(scnr);\n            return token;\n          }\n          if (!validNamedIdentifier && !validListIdentifier && !validLiteral) {\n            // TODO: we should be re-designed invalid cases, when we will extend message syntax near the future ...\n            token = getToken(context, 13 /* TokenTypes.InvalidPlace */, readInvalidIdentifier(scnr));\n            emitError(CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER, currentPosition(), 0, token.value);\n            skipSpaces(scnr);\n            return token;\n          }\n          break;\n        }\n    }\n    return token;\n  }\n  // TODO: We need refactoring of token parsing ...\n  function readTokenInLinked(scnr, context) {\n    const {\n      currentType\n    } = context;\n    let token = null;\n    const ch = scnr.currentChar();\n    if ((currentType === 8 /* TokenTypes.LinkedAlias */ || currentType === 9 /* TokenTypes.LinkedDot */ || currentType === 12 /* TokenTypes.LinkedModifier */ || currentType === 10 /* TokenTypes.LinkedDelimiter */) && (ch === CHAR_LF || ch === CHAR_SP)) {\n      emitError(CompileErrorCodes.INVALID_LINKED_FORMAT, currentPosition(), 0);\n    }\n    switch (ch) {\n      case \"@\" /* TokenChars.LinkedAlias */:\n        scnr.next();\n        token = getToken(context, 8 /* TokenTypes.LinkedAlias */, \"@\" /* TokenChars.LinkedAlias */);\n        context.inLinked = true;\n        return token;\n      case \".\" /* TokenChars.LinkedDot */:\n        skipSpaces(scnr);\n        scnr.next();\n        return getToken(context, 9 /* TokenTypes.LinkedDot */, \".\" /* TokenChars.LinkedDot */);\n      case \":\" /* TokenChars.LinkedDelimiter */:\n        skipSpaces(scnr);\n        scnr.next();\n        return getToken(context, 10 /* TokenTypes.LinkedDelimiter */, \":\" /* TokenChars.LinkedDelimiter */);\n      default:\n        if (isPluralStart(scnr)) {\n          token = getToken(context, 1 /* TokenTypes.Pipe */, readPlural(scnr));\n          // reset\n          context.braceNest = 0;\n          context.inLinked = false;\n          return token;\n        }\n        if (isLinkedDotStart(scnr, context) || isLinkedDelimiterStart(scnr, context)) {\n          skipSpaces(scnr);\n          return readTokenInLinked(scnr, context);\n        }\n        if (isLinkedModifierStart(scnr, context)) {\n          skipSpaces(scnr);\n          return getToken(context, 12 /* TokenTypes.LinkedModifier */, readLinkedModifier(scnr));\n        }\n        if (isLinkedReferStart(scnr, context)) {\n          skipSpaces(scnr);\n          if (ch === \"{\" /* TokenChars.BraceLeft */) {\n            // scan the placeholder\n            return readTokenInPlaceholder(scnr, context) || token;\n          } else {\n            return getToken(context, 11 /* TokenTypes.LinkedKey */, readLinkedRefer(scnr));\n          }\n        }\n        if (currentType === 8 /* TokenTypes.LinkedAlias */) {\n          emitError(CompileErrorCodes.INVALID_LINKED_FORMAT, currentPosition(), 0);\n        }\n        context.braceNest = 0;\n        context.inLinked = false;\n        return readToken(scnr, context);\n    }\n  }\n  // TODO: We need refactoring of token parsing ...\n  function readToken(scnr, context) {\n    let token = {\n      type: 14 /* TokenTypes.EOF */\n    };\n    if (context.braceNest > 0) {\n      return readTokenInPlaceholder(scnr, context) || getEndToken(context);\n    }\n    if (context.inLinked) {\n      return readTokenInLinked(scnr, context) || getEndToken(context);\n    }\n    const ch = scnr.currentChar();\n    switch (ch) {\n      case \"{\" /* TokenChars.BraceLeft */:\n        return readTokenInPlaceholder(scnr, context) || getEndToken(context);\n      case \"}\" /* TokenChars.BraceRight */:\n        emitError(CompileErrorCodes.UNBALANCED_CLOSING_BRACE, currentPosition(), 0);\n        scnr.next();\n        return getToken(context, 3 /* TokenTypes.BraceRight */, \"}\" /* TokenChars.BraceRight */);\n      case \"@\" /* TokenChars.LinkedAlias */:\n        return readTokenInLinked(scnr, context) || getEndToken(context);\n      default:\n        {\n          if (isPluralStart(scnr)) {\n            token = getToken(context, 1 /* TokenTypes.Pipe */, readPlural(scnr));\n            // reset\n            context.braceNest = 0;\n            context.inLinked = false;\n            return token;\n          }\n          const {\n            isModulo,\n            hasSpace\n          } = detectModuloStart(scnr);\n          if (isModulo) {\n            return hasSpace ? getToken(context, 0 /* TokenTypes.Text */, readText(scnr)) : getToken(context, 4 /* TokenTypes.Modulo */, readModulo(scnr));\n          }\n          if (isTextStart(scnr)) {\n            return getToken(context, 0 /* TokenTypes.Text */, readText(scnr));\n          }\n          break;\n        }\n    }\n    return token;\n  }\n  function nextToken() {\n    const {\n      currentType,\n      offset,\n      startLoc,\n      endLoc\n    } = _context;\n    _context.lastType = currentType;\n    _context.lastOffset = offset;\n    _context.lastStartLoc = startLoc;\n    _context.lastEndLoc = endLoc;\n    _context.offset = currentOffset();\n    _context.startLoc = currentPosition();\n    if (_scnr.currentChar() === EOF) {\n      return getToken(_context, 14 /* TokenTypes.EOF */);\n    }\n    return readToken(_scnr, _context);\n  }\n  return {\n    nextToken,\n    currentOffset,\n    currentPosition,\n    context\n  };\n}\nconst ERROR_DOMAIN$2 = 'parser';\n// Backslash backslash, backslash quote, uHHHH, UHHHHHH.\nconst KNOWN_ESCAPES = /(?:\\\\\\\\|\\\\'|\\\\u([0-9a-fA-F]{4})|\\\\U([0-9a-fA-F]{6}))/g;\nfunction fromEscapeSequence(match, codePoint4, codePoint6) {\n  switch (match) {\n    case `\\\\\\\\`:\n      return `\\\\`;\n    // eslint-disable-next-line no-useless-escape\n    case `\\\\\\'`:\n      // eslint-disable-next-line no-useless-escape\n      return `\\'`;\n    default:\n      {\n        const codePoint = parseInt(codePoint4 || codePoint6, 16);\n        if (codePoint <= 0xd7ff || codePoint >= 0xe000) {\n          return String.fromCodePoint(codePoint);\n        }\n        // invalid ...\n        // Replace them with U+FFFD REPLACEMENT CHARACTER.\n        return '�';\n      }\n  }\n}\nfunction createParser(options = {}) {\n  const location = options.location !== false;\n  const {\n    onError,\n    onWarn\n  } = options;\n  function emitError(tokenzer, code, start, offset, ...args) {\n    const end = tokenzer.currentPosition();\n    end.offset += offset;\n    end.column += offset;\n    if (onError) {\n      const loc = location ? createLocation(start, end) : null;\n      const err = createCompileError(code, loc, {\n        domain: ERROR_DOMAIN$2,\n        args\n      });\n      onError(err);\n    }\n  }\n  function emitWarn(tokenzer, code, start, offset, ...args) {\n    const end = tokenzer.currentPosition();\n    end.offset += offset;\n    end.column += offset;\n    if (onWarn) {\n      const loc = location ? createLocation(start, end) : null;\n      onWarn(createCompileWarn(code, loc, args));\n    }\n  }\n  function startNode(type, offset, loc) {\n    const node = {\n      type\n    };\n    if (location) {\n      node.start = offset;\n      node.end = offset;\n      node.loc = {\n        start: loc,\n        end: loc\n      };\n    }\n    return node;\n  }\n  function endNode(node, offset, pos, type) {\n    if (type) {\n      node.type = type;\n    }\n    if (location) {\n      node.end = offset;\n      if (node.loc) {\n        node.loc.end = pos;\n      }\n    }\n  }\n  function parseText(tokenizer, value) {\n    const context = tokenizer.context();\n    const node = startNode(3 /* NodeTypes.Text */, context.offset, context.startLoc);\n    node.value = value;\n    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return node;\n  }\n  function parseList(tokenizer, index) {\n    const context = tokenizer.context();\n    const {\n      lastOffset: offset,\n      lastStartLoc: loc\n    } = context; // get brace left loc\n    const node = startNode(5 /* NodeTypes.List */, offset, loc);\n    node.index = parseInt(index, 10);\n    tokenizer.nextToken(); // skip brach right\n    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return node;\n  }\n  function parseNamed(tokenizer, key, modulo) {\n    const context = tokenizer.context();\n    const {\n      lastOffset: offset,\n      lastStartLoc: loc\n    } = context; // get brace left loc\n    const node = startNode(4 /* NodeTypes.Named */, offset, loc);\n    node.key = key;\n    if (modulo === true) {\n      node.modulo = true;\n    }\n    tokenizer.nextToken(); // skip brach right\n    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return node;\n  }\n  function parseLiteral(tokenizer, value) {\n    const context = tokenizer.context();\n    const {\n      lastOffset: offset,\n      lastStartLoc: loc\n    } = context; // get brace left loc\n    const node = startNode(9 /* NodeTypes.Literal */, offset, loc);\n    node.value = value.replace(KNOWN_ESCAPES, fromEscapeSequence);\n    tokenizer.nextToken(); // skip brach right\n    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return node;\n  }\n  function parseLinkedModifier(tokenizer) {\n    const token = tokenizer.nextToken();\n    const context = tokenizer.context();\n    const {\n      lastOffset: offset,\n      lastStartLoc: loc\n    } = context; // get linked dot loc\n    const node = startNode(8 /* NodeTypes.LinkedModifier */, offset, loc);\n    if (token.type !== 12 /* TokenTypes.LinkedModifier */) {\n      // empty modifier\n      emitError(tokenizer, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER, context.lastStartLoc, 0);\n      node.value = '';\n      endNode(node, offset, loc);\n      return {\n        nextConsumeToken: token,\n        node\n      };\n    }\n    // check token\n    if (token.value == null) {\n      emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n    }\n    node.value = token.value || '';\n    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return {\n      node\n    };\n  }\n  function parseLinkedKey(tokenizer, value) {\n    const context = tokenizer.context();\n    const node = startNode(7 /* NodeTypes.LinkedKey */, context.offset, context.startLoc);\n    node.value = value;\n    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return node;\n  }\n  function parseLinked(tokenizer) {\n    const context = tokenizer.context();\n    const linkedNode = startNode(6 /* NodeTypes.Linked */, context.offset, context.startLoc);\n    let token = tokenizer.nextToken();\n    if (token.type === 9 /* TokenTypes.LinkedDot */) {\n      const parsed = parseLinkedModifier(tokenizer);\n      linkedNode.modifier = parsed.node;\n      token = parsed.nextConsumeToken || tokenizer.nextToken();\n    }\n    // asset check token\n    if (token.type !== 10 /* TokenTypes.LinkedDelimiter */) {\n      emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n    }\n    token = tokenizer.nextToken();\n    // skip brace left\n    if (token.type === 2 /* TokenTypes.BraceLeft */) {\n      token = tokenizer.nextToken();\n    }\n    switch (token.type) {\n      case 11 /* TokenTypes.LinkedKey */:\n        if (token.value == null) {\n          emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n        }\n        linkedNode.key = parseLinkedKey(tokenizer, token.value || '');\n        break;\n      case 5 /* TokenTypes.Named */:\n        if (token.value == null) {\n          emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n        }\n        linkedNode.key = parseNamed(tokenizer, token.value || '');\n        break;\n      case 6 /* TokenTypes.List */:\n        if (token.value == null) {\n          emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n        }\n        linkedNode.key = parseList(tokenizer, token.value || '');\n        break;\n      case 7 /* TokenTypes.Literal */:\n        if (token.value == null) {\n          emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n        }\n        linkedNode.key = parseLiteral(tokenizer, token.value || '');\n        break;\n      default:\n        {\n          // empty key\n          emitError(tokenizer, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY, context.lastStartLoc, 0);\n          const nextContext = tokenizer.context();\n          const emptyLinkedKeyNode = startNode(7 /* NodeTypes.LinkedKey */, nextContext.offset, nextContext.startLoc);\n          emptyLinkedKeyNode.value = '';\n          endNode(emptyLinkedKeyNode, nextContext.offset, nextContext.startLoc);\n          linkedNode.key = emptyLinkedKeyNode;\n          endNode(linkedNode, nextContext.offset, nextContext.startLoc);\n          return {\n            nextConsumeToken: token,\n            node: linkedNode\n          };\n        }\n    }\n    endNode(linkedNode, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return {\n      node: linkedNode\n    };\n  }\n  function parseMessage(tokenizer) {\n    const context = tokenizer.context();\n    const startOffset = context.currentType === 1 /* TokenTypes.Pipe */ ? tokenizer.currentOffset() : context.offset;\n    const startLoc = context.currentType === 1 /* TokenTypes.Pipe */ ? context.endLoc : context.startLoc;\n    const node = startNode(2 /* NodeTypes.Message */, startOffset, startLoc);\n    node.items = [];\n    let nextToken = null;\n    let modulo = null;\n    do {\n      const token = nextToken || tokenizer.nextToken();\n      nextToken = null;\n      switch (token.type) {\n        case 0 /* TokenTypes.Text */:\n          if (token.value == null) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n          }\n          node.items.push(parseText(tokenizer, token.value || ''));\n          break;\n        case 6 /* TokenTypes.List */:\n          if (token.value == null) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n          }\n          node.items.push(parseList(tokenizer, token.value || ''));\n          break;\n        case 4 /* TokenTypes.Modulo */:\n          modulo = true;\n          break;\n        case 5 /* TokenTypes.Named */:\n          if (token.value == null) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n          }\n          node.items.push(parseNamed(tokenizer, token.value || '', !!modulo));\n          if (modulo) {\n            emitWarn(tokenizer, CompileWarnCodes.USE_MODULO_SYNTAX, context.lastStartLoc, 0, getTokenCaption(token));\n            modulo = null;\n          }\n          break;\n        case 7 /* TokenTypes.Literal */:\n          if (token.value == null) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n          }\n          node.items.push(parseLiteral(tokenizer, token.value || ''));\n          break;\n        case 8 /* TokenTypes.LinkedAlias */:\n          {\n            const parsed = parseLinked(tokenizer);\n            node.items.push(parsed.node);\n            nextToken = parsed.nextConsumeToken || null;\n            break;\n          }\n      }\n    } while (context.currentType !== 14 /* TokenTypes.EOF */ && context.currentType !== 1 /* TokenTypes.Pipe */);\n    // adjust message node loc\n    const endOffset = context.currentType === 1 /* TokenTypes.Pipe */ ? context.lastOffset : tokenizer.currentOffset();\n    const endLoc = context.currentType === 1 /* TokenTypes.Pipe */ ? context.lastEndLoc : tokenizer.currentPosition();\n    endNode(node, endOffset, endLoc);\n    return node;\n  }\n  function parsePlural(tokenizer, offset, loc, msgNode) {\n    const context = tokenizer.context();\n    let hasEmptyMessage = msgNode.items.length === 0;\n    const node = startNode(1 /* NodeTypes.Plural */, offset, loc);\n    node.cases = [];\n    node.cases.push(msgNode);\n    do {\n      const msg = parseMessage(tokenizer);\n      if (!hasEmptyMessage) {\n        hasEmptyMessage = msg.items.length === 0;\n      }\n      node.cases.push(msg);\n    } while (context.currentType !== 14 /* TokenTypes.EOF */);\n    if (hasEmptyMessage) {\n      emitError(tokenizer, CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL, loc, 0);\n    }\n    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return node;\n  }\n  function parseResource(tokenizer) {\n    const context = tokenizer.context();\n    const {\n      offset,\n      startLoc\n    } = context;\n    const msgNode = parseMessage(tokenizer);\n    if (context.currentType === 14 /* TokenTypes.EOF */) {\n      return msgNode;\n    } else {\n      return parsePlural(tokenizer, offset, startLoc, msgNode);\n    }\n  }\n  function parse(source) {\n    const tokenizer = createTokenizer(source, assign({}, options));\n    const context = tokenizer.context();\n    const node = startNode(0 /* NodeTypes.Resource */, context.offset, context.startLoc);\n    if (location && node.loc) {\n      node.loc.source = source;\n    }\n    node.body = parseResource(tokenizer);\n    if (options.onCacheKey) {\n      node.cacheKey = options.onCacheKey(source);\n    }\n    // assert whether achieved to EOF\n    if (context.currentType !== 14 /* TokenTypes.EOF */) {\n      emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, source[context.offset] || '');\n    }\n    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return node;\n  }\n  return {\n    parse\n  };\n}\nfunction getTokenCaption(token) {\n  if (token.type === 14 /* TokenTypes.EOF */) {\n    return 'EOF';\n  }\n  const name = (token.value || '').replace(/\\r?\\n/gu, '\\\\n');\n  return name.length > 10 ? name.slice(0, 9) + '…' : name;\n}\nfunction createTransformer(ast, options = {} // eslint-disable-line\n) {\n  const _context = {\n    ast,\n    helpers: new Set()\n  };\n  const context = () => _context;\n  const helper = name => {\n    _context.helpers.add(name);\n    return name;\n  };\n  return {\n    context,\n    helper\n  };\n}\nfunction traverseNodes(nodes, transformer) {\n  for (let i = 0; i < nodes.length; i++) {\n    traverseNode(nodes[i], transformer);\n  }\n}\nfunction traverseNode(node, transformer) {\n  // TODO: if we need pre-hook of transform, should be implemented to here\n  switch (node.type) {\n    case 1 /* NodeTypes.Plural */:\n      traverseNodes(node.cases, transformer);\n      transformer.helper(\"plural\" /* HelperNameMap.PLURAL */);\n      break;\n    case 2 /* NodeTypes.Message */:\n      traverseNodes(node.items, transformer);\n      break;\n    case 6 /* NodeTypes.Linked */:\n      {\n        const linked = node;\n        traverseNode(linked.key, transformer);\n        transformer.helper(\"linked\" /* HelperNameMap.LINKED */);\n        transformer.helper(\"type\" /* HelperNameMap.TYPE */);\n        break;\n      }\n    case 5 /* NodeTypes.List */:\n      transformer.helper(\"interpolate\" /* HelperNameMap.INTERPOLATE */);\n      transformer.helper(\"list\" /* HelperNameMap.LIST */);\n      break;\n    case 4 /* NodeTypes.Named */:\n      transformer.helper(\"interpolate\" /* HelperNameMap.INTERPOLATE */);\n      transformer.helper(\"named\" /* HelperNameMap.NAMED */);\n      break;\n  }\n  // TODO: if we need post-hook of transform, should be implemented to here\n}\n// transform AST\nfunction transform(ast, options = {} // eslint-disable-line\n) {\n  const transformer = createTransformer(ast);\n  transformer.helper(\"normalize\" /* HelperNameMap.NORMALIZE */);\n  // traverse\n  ast.body && traverseNode(ast.body, transformer);\n  // set meta information\n  const context = transformer.context();\n  ast.helpers = Array.from(context.helpers);\n}\nfunction optimize(ast) {\n  const body = ast.body;\n  if (body.type === 2 /* NodeTypes.Message */) {\n    optimizeMessageNode(body);\n  } else {\n    body.cases.forEach(c => optimizeMessageNode(c));\n  }\n  return ast;\n}\nfunction optimizeMessageNode(message) {\n  if (message.items.length === 1) {\n    const item = message.items[0];\n    if (item.type === 3 /* NodeTypes.Text */ || item.type === 9 /* NodeTypes.Literal */) {\n      message.static = item.value;\n      delete item.value; // optimization for size\n    }\n  } else {\n    const values = [];\n    for (let i = 0; i < message.items.length; i++) {\n      const item = message.items[i];\n      if (!(item.type === 3 /* NodeTypes.Text */ || item.type === 9 /* NodeTypes.Literal */)) {\n        break;\n      }\n      if (item.value == null) {\n        break;\n      }\n      values.push(item.value);\n    }\n    if (values.length === message.items.length) {\n      message.static = join(values);\n      for (let i = 0; i < message.items.length; i++) {\n        const item = message.items[i];\n        if (item.type === 3 /* NodeTypes.Text */ || item.type === 9 /* NodeTypes.Literal */) {\n          delete item.value; // optimization for size\n        }\n      }\n    }\n  }\n}\nconst ERROR_DOMAIN$1 = 'minifier';\n/* eslint-disable @typescript-eslint/no-explicit-any */\nfunction minify(node) {\n  node.t = node.type;\n  switch (node.type) {\n    case 0 /* NodeTypes.Resource */:\n      {\n        const resource = node;\n        minify(resource.body);\n        resource.b = resource.body;\n        delete resource.body;\n        break;\n      }\n    case 1 /* NodeTypes.Plural */:\n      {\n        const plural = node;\n        const cases = plural.cases;\n        for (let i = 0; i < cases.length; i++) {\n          minify(cases[i]);\n        }\n        plural.c = cases;\n        delete plural.cases;\n        break;\n      }\n    case 2 /* NodeTypes.Message */:\n      {\n        const message = node;\n        const items = message.items;\n        for (let i = 0; i < items.length; i++) {\n          minify(items[i]);\n        }\n        message.i = items;\n        delete message.items;\n        if (message.static) {\n          message.s = message.static;\n          delete message.static;\n        }\n        break;\n      }\n    case 3 /* NodeTypes.Text */:\n    case 9 /* NodeTypes.Literal */:\n    case 8 /* NodeTypes.LinkedModifier */:\n    case 7 /* NodeTypes.LinkedKey */:\n      {\n        const valueNode = node;\n        if (valueNode.value) {\n          valueNode.v = valueNode.value;\n          delete valueNode.value;\n        }\n        break;\n      }\n    case 6 /* NodeTypes.Linked */:\n      {\n        const linked = node;\n        minify(linked.key);\n        linked.k = linked.key;\n        delete linked.key;\n        if (linked.modifier) {\n          minify(linked.modifier);\n          linked.m = linked.modifier;\n          delete linked.modifier;\n        }\n        break;\n      }\n    case 5 /* NodeTypes.List */:\n      {\n        const list = node;\n        list.i = list.index;\n        delete list.index;\n        break;\n      }\n    case 4 /* NodeTypes.Named */:\n      {\n        const named = node;\n        named.k = named.key;\n        delete named.key;\n        break;\n      }\n    default:\n      {\n        throw createCompileError(CompileErrorCodes.UNHANDLED_MINIFIER_NODE_TYPE, null, {\n          domain: ERROR_DOMAIN$1,\n          args: [node.type]\n        });\n      }\n  }\n  delete node.type;\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\n// eslint-disable-next-line @typescript-eslint/triple-slash-reference\n/// <reference types=\"source-map-js\" />\nconst ERROR_DOMAIN = 'parser';\nfunction createCodeGenerator(ast, options) {\n  const {\n    sourceMap,\n    filename,\n    breakLineCode,\n    needIndent: _needIndent\n  } = options;\n  const location = options.location !== false;\n  const _context = {\n    filename,\n    code: '',\n    column: 1,\n    line: 1,\n    offset: 0,\n    map: undefined,\n    breakLineCode,\n    needIndent: _needIndent,\n    indentLevel: 0\n  };\n  if (location && ast.loc) {\n    _context.source = ast.loc.source;\n  }\n  const context = () => _context;\n  function push(code, node) {\n    _context.code += code;\n  }\n  function _newline(n, withBreakLine = true) {\n    const _breakLineCode = withBreakLine ? breakLineCode : '';\n    push(_needIndent ? _breakLineCode + `  `.repeat(n) : _breakLineCode);\n  }\n  function indent(withNewLine = true) {\n    const level = ++_context.indentLevel;\n    withNewLine && _newline(level);\n  }\n  function deindent(withNewLine = true) {\n    const level = --_context.indentLevel;\n    withNewLine && _newline(level);\n  }\n  function newline() {\n    _newline(_context.indentLevel);\n  }\n  const helper = key => `_${key}`;\n  const needIndent = () => _context.needIndent;\n  return {\n    context,\n    push,\n    indent,\n    deindent,\n    newline,\n    helper,\n    needIndent\n  };\n}\nfunction generateLinkedNode(generator, node) {\n  const {\n    helper\n  } = generator;\n  generator.push(`${helper(\"linked\" /* HelperNameMap.LINKED */)}(`);\n  generateNode(generator, node.key);\n  if (node.modifier) {\n    generator.push(`, `);\n    generateNode(generator, node.modifier);\n    generator.push(`, _type`);\n  } else {\n    generator.push(`, undefined, _type`);\n  }\n  generator.push(`)`);\n}\nfunction generateMessageNode(generator, node) {\n  const {\n    helper,\n    needIndent\n  } = generator;\n  generator.push(`${helper(\"normalize\" /* HelperNameMap.NORMALIZE */)}([`);\n  generator.indent(needIndent());\n  const length = node.items.length;\n  for (let i = 0; i < length; i++) {\n    generateNode(generator, node.items[i]);\n    if (i === length - 1) {\n      break;\n    }\n    generator.push(', ');\n  }\n  generator.deindent(needIndent());\n  generator.push('])');\n}\nfunction generatePluralNode(generator, node) {\n  const {\n    helper,\n    needIndent\n  } = generator;\n  if (node.cases.length > 1) {\n    generator.push(`${helper(\"plural\" /* HelperNameMap.PLURAL */)}([`);\n    generator.indent(needIndent());\n    const length = node.cases.length;\n    for (let i = 0; i < length; i++) {\n      generateNode(generator, node.cases[i]);\n      if (i === length - 1) {\n        break;\n      }\n      generator.push(', ');\n    }\n    generator.deindent(needIndent());\n    generator.push(`])`);\n  }\n}\nfunction generateResource(generator, node) {\n  if (node.body) {\n    generateNode(generator, node.body);\n  } else {\n    generator.push('null');\n  }\n}\nfunction generateNode(generator, node) {\n  const {\n    helper\n  } = generator;\n  switch (node.type) {\n    case 0 /* NodeTypes.Resource */:\n      generateResource(generator, node);\n      break;\n    case 1 /* NodeTypes.Plural */:\n      generatePluralNode(generator, node);\n      break;\n    case 2 /* NodeTypes.Message */:\n      generateMessageNode(generator, node);\n      break;\n    case 6 /* NodeTypes.Linked */:\n      generateLinkedNode(generator, node);\n      break;\n    case 8 /* NodeTypes.LinkedModifier */:\n      generator.push(JSON.stringify(node.value), node);\n      break;\n    case 7 /* NodeTypes.LinkedKey */:\n      generator.push(JSON.stringify(node.value), node);\n      break;\n    case 5 /* NodeTypes.List */:\n      generator.push(`${helper(\"interpolate\" /* HelperNameMap.INTERPOLATE */)}(${helper(\"list\" /* HelperNameMap.LIST */)}(${node.index}))`, node);\n      break;\n    case 4 /* NodeTypes.Named */:\n      generator.push(`${helper(\"interpolate\" /* HelperNameMap.INTERPOLATE */)}(${helper(\"named\" /* HelperNameMap.NAMED */)}(${JSON.stringify(node.key)}))`, node);\n      break;\n    case 9 /* NodeTypes.Literal */:\n      generator.push(JSON.stringify(node.value), node);\n      break;\n    case 3 /* NodeTypes.Text */:\n      generator.push(JSON.stringify(node.value), node);\n      break;\n    default:\n      {\n        throw createCompileError(CompileErrorCodes.UNHANDLED_CODEGEN_NODE_TYPE, null, {\n          domain: ERROR_DOMAIN,\n          args: [node.type]\n        });\n      }\n  }\n}\n// generate code from AST\nconst generate = (ast, options = {} // eslint-disable-line\n) => {\n  const mode = isString(options.mode) ? options.mode : 'normal';\n  const filename = isString(options.filename) ? options.filename : 'message.intl';\n  const sourceMap = !!options.sourceMap;\n  // prettier-ignore\n  const breakLineCode = options.breakLineCode != null ? options.breakLineCode : mode === 'arrow' ? ';' : '\\n';\n  const needIndent = options.needIndent ? options.needIndent : mode !== 'arrow';\n  const helpers = ast.helpers || [];\n  const generator = createCodeGenerator(ast, {\n    mode,\n    filename,\n    sourceMap,\n    breakLineCode,\n    needIndent\n  });\n  generator.push(mode === 'normal' ? `function __msg__ (ctx) {` : `(ctx) => {`);\n  generator.indent(needIndent);\n  if (helpers.length > 0) {\n    generator.push(`const { ${join(helpers.map(s => `${s}: _${s}`), ', ')} } = ctx`);\n    generator.newline();\n  }\n  generator.push(`return `);\n  generateNode(generator, ast);\n  generator.deindent(needIndent);\n  generator.push(`}`);\n  delete ast.helpers;\n  const {\n    code,\n    map\n  } = generator.context();\n  return {\n    ast,\n    code,\n    map: map ? map.toJSON() : undefined // eslint-disable-line @typescript-eslint/no-explicit-any\n  };\n};\nfunction baseCompile(source, options = {}) {\n  const assignedOptions = assign({}, options);\n  const jit = !!assignedOptions.jit;\n  const enalbeMinify = !!assignedOptions.minify;\n  const enambeOptimize = assignedOptions.optimize == null ? true : assignedOptions.optimize;\n  // parse source codes\n  const parser = createParser(assignedOptions);\n  const ast = parser.parse(source);\n  if (!jit) {\n    // transform ASTs\n    transform(ast, assignedOptions);\n    // generate javascript codes\n    return generate(ast, assignedOptions);\n  } else {\n    // optimize ASTs\n    enambeOptimize && optimize(ast);\n    // minimize ASTs\n    enalbeMinify && minify(ast);\n    // In JIT mode, no ast transform, no code generation.\n    return {\n      ast,\n      code: ''\n    };\n  }\n}\nexport { CompileErrorCodes, CompileWarnCodes, ERROR_DOMAIN$2 as ERROR_DOMAIN, LOCATION_STUB, baseCompile, createCompileError, createCompileWarn, createLocation, createParser, createPosition, defaultOnError, detectHtmlTag, errorMessages, warnMessages };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}