{"ast": null, "code": "import { defineComponent, useSlots, computed, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, renderSlot, createCommentVNode, createElementVNode, Fragment, renderList, createBlock, withModifiers, withCtx, createTextVNode, toDisplayString, withDirectives, mergeProps, isRef, vModelText, vShow, createVNode, resolveDynamicComponent } from 'vue';\nimport { CircleClose } from '@element-plus/icons-vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { ElTag } from '../../tag/index.mjs';\nimport { inputTagProps, inputTagEmits } from './input-tag.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useInputTag } from './composables/use-input-tag.mjs';\nimport { useHovering } from './composables/use-hovering.mjs';\nimport { useCalcInputWidth } from '../../../hooks/use-calc-input-width/index.mjs';\nimport { useDragTag } from './composables/use-drag-tag.mjs';\nimport { useInputTagDom } from './composables/use-input-tag-dom.mjs';\nimport { useAttrs } from '../../../hooks/use-attrs/index.mjs';\nimport { useFormItem, useFormItemInputId } from '../../form/src/hooks/use-form-item.mjs';\nimport { ValidateComponentsMap } from '../../../utils/vue/icon.mjs';\nimport { NOOP } from '@vue/shared';\nconst __default__ = defineComponent({\n  name: \"ElInputTag\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: inputTagProps,\n  emits: inputTagEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const attrs = useAttrs();\n    const slots = useSlots();\n    const {\n      form,\n      formItem\n    } = useFormItem();\n    const {\n      inputId\n    } = useFormItemInputId(props, {\n      formItemContext: formItem\n    });\n    const needStatusIcon = computed(() => {\n      var _a;\n      return (_a = form == null ? void 0 : form.statusIcon) != null ? _a : false;\n    });\n    const validateState = computed(() => (formItem == null ? void 0 : formItem.validateState) || \"\");\n    const validateIcon = computed(() => {\n      return validateState.value && ValidateComponentsMap[validateState.value];\n    });\n    const {\n      inputRef,\n      wrapperRef,\n      isFocused,\n      inputValue,\n      size,\n      tagSize,\n      placeholder,\n      closable,\n      disabled,\n      handleDragged,\n      handleInput,\n      handleKeydown,\n      handleRemoveTag,\n      handleClear,\n      handleCompositionStart,\n      handleCompositionUpdate,\n      handleCompositionEnd,\n      focus,\n      blur\n    } = useInputTag({\n      props,\n      emit,\n      formItem\n    });\n    const {\n      hovering,\n      handleMouseEnter,\n      handleMouseLeave\n    } = useHovering();\n    const {\n      calculatorRef,\n      inputStyle\n    } = useCalcInputWidth();\n    const {\n      dropIndicatorRef,\n      showDropIndicator,\n      handleDragStart,\n      handleDragOver,\n      handleDragEnd\n    } = useDragTag({\n      wrapperRef,\n      handleDragged,\n      afterDragged: focus\n    });\n    const {\n      ns,\n      nsInput,\n      containerKls,\n      containerStyle,\n      innerKls,\n      showClear,\n      showSuffix\n    } = useInputTagDom({\n      props,\n      hovering,\n      isFocused,\n      inputValue,\n      disabled,\n      size,\n      validateState,\n      validateIcon,\n      needStatusIcon\n    });\n    expose({\n      focus,\n      blur\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"wrapperRef\",\n        ref: wrapperRef,\n        class: normalizeClass(unref(containerKls)),\n        style: normalizeStyle(unref(containerStyle)),\n        onMouseenter: unref(handleMouseEnter),\n        onMouseleave: unref(handleMouseLeave)\n      }, [unref(slots).prefix ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"prefix\"))\n      }, [renderSlot(_ctx.$slots, \"prefix\")], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass(unref(innerKls))\n      }, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.modelValue, (item, index) => {\n        return openBlock(), createBlock(unref(ElTag), {\n          key: index,\n          size: unref(tagSize),\n          closable: unref(closable),\n          type: _ctx.tagType,\n          effect: _ctx.tagEffect,\n          draggable: unref(closable) && _ctx.draggable,\n          \"disable-transitions\": \"\",\n          onClose: $event => unref(handleRemoveTag)(index),\n          onDragstart: event => unref(handleDragStart)(event, index),\n          onDragover: event => unref(handleDragOver)(event, index),\n          onDragend: unref(handleDragEnd),\n          onDrop: withModifiers(() => {}, [\"stop\"])\n        }, {\n          default: withCtx(() => [renderSlot(_ctx.$slots, \"tag\", {\n            value: item,\n            index\n          }, () => [createTextVNode(toDisplayString(item), 1)])]),\n          _: 2\n        }, 1032, [\"size\", \"closable\", \"type\", \"effect\", \"draggable\", \"onClose\", \"onDragstart\", \"onDragover\", \"onDragend\", \"onDrop\"]);\n      }), 128)), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"input-wrapper\"))\n      }, [withDirectives(createElementVNode(\"input\", mergeProps({\n        id: unref(inputId),\n        ref_key: \"inputRef\",\n        ref: inputRef,\n        \"onUpdate:modelValue\": $event => isRef(inputValue) ? inputValue.value = $event : null\n      }, unref(attrs), {\n        type: \"text\",\n        minlength: _ctx.minlength,\n        maxlength: _ctx.maxlength,\n        disabled: unref(disabled),\n        readonly: _ctx.readonly,\n        autocomplete: _ctx.autocomplete,\n        tabindex: _ctx.tabindex,\n        placeholder: unref(placeholder),\n        autofocus: _ctx.autofocus,\n        ariaLabel: _ctx.ariaLabel,\n        class: unref(ns).e(\"input\"),\n        style: unref(inputStyle),\n        onCompositionstart: unref(handleCompositionStart),\n        onCompositionupdate: unref(handleCompositionUpdate),\n        onCompositionend: unref(handleCompositionEnd),\n        onInput: unref(handleInput),\n        onKeydown: unref(handleKeydown)\n      }), null, 16, [\"id\", \"onUpdate:modelValue\", \"minlength\", \"maxlength\", \"disabled\", \"readonly\", \"autocomplete\", \"tabindex\", \"placeholder\", \"autofocus\", \"ariaLabel\", \"onCompositionstart\", \"onCompositionupdate\", \"onCompositionend\", \"onInput\", \"onKeydown\"]), [[vModelText, unref(inputValue)]]), createElementVNode(\"span\", {\n        ref_key: \"calculatorRef\",\n        ref: calculatorRef,\n        \"aria-hidden\": \"true\",\n        class: normalizeClass(unref(ns).e(\"input-calculator\")),\n        textContent: toDisplayString(unref(inputValue))\n      }, null, 10, [\"textContent\"])], 2), withDirectives(createElementVNode(\"div\", {\n        ref_key: \"dropIndicatorRef\",\n        ref: dropIndicatorRef,\n        class: normalizeClass(unref(ns).e(\"drop-indicator\"))\n      }, null, 2), [[vShow, unref(showDropIndicator)]])], 2), unref(showSuffix) ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(unref(ns).e(\"suffix\"))\n      }, [renderSlot(_ctx.$slots, \"suffix\"), unref(showClear) ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0,\n        class: normalizeClass([unref(ns).e(\"icon\"), unref(ns).e(\"clear\")]),\n        onMousedown: withModifiers(unref(NOOP), [\"prevent\"]),\n        onClick: unref(handleClear)\n      }, {\n        default: withCtx(() => [createVNode(unref(CircleClose))]),\n        _: 1\n      }, 8, [\"class\", \"onMousedown\", \"onClick\"])) : createCommentVNode(\"v-if\", true), unref(validateState) && unref(validateIcon) && unref(needStatusIcon) ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 1,\n        class: normalizeClass([unref(nsInput).e(\"icon\"), unref(nsInput).e(\"validateIcon\"), unref(nsInput).is(\"loading\", unref(validateState) === \"validating\")])\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(validateIcon))))]),\n        _: 1\n      }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true)], 2)) : createCommentVNode(\"v-if\", true)], 46, [\"onMouseenter\", \"onMouseleave\"]);\n    };\n  }\n});\nvar InputTag = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"input-tag.vue\"]]);\nexport { InputTag as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}