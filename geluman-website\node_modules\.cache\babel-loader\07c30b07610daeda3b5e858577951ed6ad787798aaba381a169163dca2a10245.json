{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, normalizeStyle, renderSlot, createVNode, toDisplayString, createCommentVNode } from 'vue';\nimport ImgEmpty from './img-empty.mjs';\nimport { emptyProps } from './empty.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nconst __default__ = defineComponent({\n  name: \"ElEmpty\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: emptyProps,\n  setup(__props) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"empty\");\n    const emptyDescription = computed(() => props.description || t(\"el.table.emptyText\"));\n    const imageStyle = computed(() => ({\n      width: addUnit(props.imageSize)\n    }));\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(ns).b())\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"image\")),\n        style: normalizeStyle(unref(imageStyle))\n      }, [_ctx.image ? (openBlock(), createElementBlock(\"img\", {\n        key: 0,\n        src: _ctx.image,\n        ondragstart: \"return false\"\n      }, null, 8, [\"src\"])) : renderSlot(_ctx.$slots, \"image\", {\n        key: 1\n      }, () => [createVNode(ImgEmpty)])], 6), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"description\"))\n      }, [_ctx.$slots.description ? renderSlot(_ctx.$slots, \"description\", {\n        key: 0\n      }) : (openBlock(), createElementBlock(\"p\", {\n        key: 1\n      }, toDisplayString(unref(emptyDescription)), 1))], 2), _ctx.$slots.default ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"bottom\"))\n      }, [renderSlot(_ctx.$slots, \"default\")], 2)) : createCommentVNode(\"v-if\", true)], 2);\n    };\n  }\n});\nvar Empty = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"empty.vue\"]]);\nexport { Empty as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}