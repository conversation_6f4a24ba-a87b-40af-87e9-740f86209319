{"ast": null, "code": "import { defineComponent, getCurrentInstance, computed, inject, ref, reactive, watch, provide, onMounted, onBeforeUnmount, h, Fragment, withDirectives, vShow } from 'vue';\nimport { useTimeoutFn } from '@vueuse/core';\nimport { ElCollapseTransition } from '../../collapse-transition/index.mjs';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { ArrowDown, ArrowRight } from '@element-plus/icons-vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport useMenu from './use-menu.mjs';\nimport { useMenuCssVar } from './use-menu-css-var.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nimport { isUndefined } from '../../../utils/types.mjs';\nimport { isString } from '@vue/shared';\nconst subMenuProps = buildProps({\n  index: {\n    type: String,\n    required: true\n  },\n  showTimeout: Number,\n  hideTimeout: Number,\n  popperClass: String,\n  disabled: Boolean,\n  teleported: {\n    type: Boolean,\n    default: void 0\n  },\n  popperOffset: Number,\n  expandCloseIcon: {\n    type: iconPropType\n  },\n  expandOpenIcon: {\n    type: iconPropType\n  },\n  collapseCloseIcon: {\n    type: iconPropType\n  },\n  collapseOpenIcon: {\n    type: iconPropType\n  }\n});\nconst COMPONENT_NAME = \"ElSubMenu\";\nvar SubMenu = defineComponent({\n  name: COMPONENT_NAME,\n  props: subMenuProps,\n  setup(props, {\n    slots,\n    expose\n  }) {\n    const instance = getCurrentInstance();\n    const {\n      indexPath,\n      parentMenu\n    } = useMenu(instance, computed(() => props.index));\n    const nsMenu = useNamespace(\"menu\");\n    const nsSubMenu = useNamespace(\"sub-menu\");\n    const rootMenu = inject(\"rootMenu\");\n    if (!rootMenu) throwError(COMPONENT_NAME, \"can not inject root menu\");\n    const subMenu = inject(`subMenu:${parentMenu.value.uid}`);\n    if (!subMenu) throwError(COMPONENT_NAME, \"can not inject sub menu\");\n    const items = ref({});\n    const subMenus = ref({});\n    let timeout;\n    const mouseInChild = ref(false);\n    const verticalTitleRef = ref();\n    const vPopper = ref();\n    const currentPlacement = computed(() => mode.value === \"horizontal\" && isFirstLevel.value ? \"bottom-start\" : \"right-start\");\n    const subMenuTitleIcon = computed(() => {\n      return mode.value === \"horizontal\" && isFirstLevel.value || mode.value === \"vertical\" && !rootMenu.props.collapse ? props.expandCloseIcon && props.expandOpenIcon ? opened.value ? props.expandOpenIcon : props.expandCloseIcon : ArrowDown : props.collapseCloseIcon && props.collapseOpenIcon ? opened.value ? props.collapseOpenIcon : props.collapseCloseIcon : ArrowRight;\n    });\n    const isFirstLevel = computed(() => subMenu.level === 0);\n    const appendToBody = computed(() => {\n      const value = props.teleported;\n      return isUndefined(value) ? isFirstLevel.value : value;\n    });\n    const menuTransitionName = computed(() => rootMenu.props.collapse ? `${nsMenu.namespace.value}-zoom-in-left` : `${nsMenu.namespace.value}-zoom-in-top`);\n    const fallbackPlacements = computed(() => mode.value === \"horizontal\" && isFirstLevel.value ? [\"bottom-start\", \"bottom-end\", \"top-start\", \"top-end\", \"right-start\", \"left-start\"] : [\"right-start\", \"right\", \"right-end\", \"left-start\", \"bottom-start\", \"bottom-end\", \"top-start\", \"top-end\"]);\n    const opened = computed(() => rootMenu.openedMenus.includes(props.index));\n    const active = computed(() => [...Object.values(items.value), ...Object.values(subMenus.value)].some(({\n      active: active2\n    }) => active2));\n    const mode = computed(() => rootMenu.props.mode);\n    const persistent = computed(() => rootMenu.props.persistent);\n    const item = reactive({\n      index: props.index,\n      indexPath,\n      active\n    });\n    const ulStyle = useMenuCssVar(rootMenu.props, subMenu.level + 1);\n    const subMenuPopperOffset = computed(() => {\n      var _a;\n      return (_a = props.popperOffset) != null ? _a : rootMenu.props.popperOffset;\n    });\n    const subMenuPopperClass = computed(() => {\n      var _a;\n      return (_a = props.popperClass) != null ? _a : rootMenu.props.popperClass;\n    });\n    const subMenuShowTimeout = computed(() => {\n      var _a;\n      return (_a = props.showTimeout) != null ? _a : rootMenu.props.showTimeout;\n    });\n    const subMenuHideTimeout = computed(() => {\n      var _a;\n      return (_a = props.hideTimeout) != null ? _a : rootMenu.props.hideTimeout;\n    });\n    const doDestroy = () => {\n      var _a, _b, _c;\n      return (_c = (_b = (_a = vPopper.value) == null ? void 0 : _a.popperRef) == null ? void 0 : _b.popperInstanceRef) == null ? void 0 : _c.destroy();\n    };\n    const handleCollapseToggle = value => {\n      if (!value) {\n        doDestroy();\n      }\n    };\n    const handleClick = () => {\n      if (rootMenu.props.menuTrigger === \"hover\" && rootMenu.props.mode === \"horizontal\" || rootMenu.props.collapse && rootMenu.props.mode === \"vertical\" || props.disabled) return;\n      rootMenu.handleSubMenuClick({\n        index: props.index,\n        indexPath: indexPath.value,\n        active: active.value\n      });\n    };\n    const handleMouseenter = (event, showTimeout = subMenuShowTimeout.value) => {\n      var _a;\n      if (event.type === \"focus\") return;\n      if (rootMenu.props.menuTrigger === \"click\" && rootMenu.props.mode === \"horizontal\" || !rootMenu.props.collapse && rootMenu.props.mode === \"vertical\" || props.disabled) {\n        subMenu.mouseInChild.value = true;\n        return;\n      }\n      subMenu.mouseInChild.value = true;\n      timeout == null ? void 0 : timeout();\n      ({\n        stop: timeout\n      } = useTimeoutFn(() => {\n        rootMenu.openMenu(props.index, indexPath.value);\n      }, showTimeout));\n      if (appendToBody.value) {\n        (_a = parentMenu.value.vnode.el) == null ? void 0 : _a.dispatchEvent(new MouseEvent(\"mouseenter\"));\n      }\n    };\n    const handleMouseleave = (deepDispatch = false) => {\n      var _a;\n      if (rootMenu.props.menuTrigger === \"click\" && rootMenu.props.mode === \"horizontal\" || !rootMenu.props.collapse && rootMenu.props.mode === \"vertical\") {\n        subMenu.mouseInChild.value = false;\n        return;\n      }\n      timeout == null ? void 0 : timeout();\n      subMenu.mouseInChild.value = false;\n      ({\n        stop: timeout\n      } = useTimeoutFn(() => !mouseInChild.value && rootMenu.closeMenu(props.index, indexPath.value), subMenuHideTimeout.value));\n      if (appendToBody.value && deepDispatch) {\n        (_a = subMenu.handleMouseleave) == null ? void 0 : _a.call(subMenu, true);\n      }\n    };\n    watch(() => rootMenu.props.collapse, value => handleCollapseToggle(Boolean(value)));\n    {\n      const addSubMenu = item2 => {\n        subMenus.value[item2.index] = item2;\n      };\n      const removeSubMenu = item2 => {\n        delete subMenus.value[item2.index];\n      };\n      provide(`subMenu:${instance.uid}`, {\n        addSubMenu,\n        removeSubMenu,\n        handleMouseleave,\n        mouseInChild,\n        level: subMenu.level + 1\n      });\n    }\n    expose({\n      opened\n    });\n    onMounted(() => {\n      rootMenu.addSubMenu(item);\n      subMenu.addSubMenu(item);\n    });\n    onBeforeUnmount(() => {\n      subMenu.removeSubMenu(item);\n      rootMenu.removeSubMenu(item);\n    });\n    return () => {\n      var _a;\n      const titleTag = [(_a = slots.title) == null ? void 0 : _a.call(slots), h(ElIcon, {\n        class: nsSubMenu.e(\"icon-arrow\"),\n        style: {\n          transform: opened.value ? props.expandCloseIcon && props.expandOpenIcon || props.collapseCloseIcon && props.collapseOpenIcon && rootMenu.props.collapse ? \"none\" : \"rotateZ(180deg)\" : \"none\"\n        }\n      }, {\n        default: () => isString(subMenuTitleIcon.value) ? h(instance.appContext.components[subMenuTitleIcon.value]) : h(subMenuTitleIcon.value)\n      })];\n      const child = rootMenu.isMenuPopup ? h(ElTooltip, {\n        ref: vPopper,\n        visible: opened.value,\n        effect: \"light\",\n        pure: true,\n        offset: subMenuPopperOffset.value,\n        showArrow: false,\n        persistent: persistent.value,\n        popperClass: subMenuPopperClass.value,\n        placement: currentPlacement.value,\n        teleported: appendToBody.value,\n        fallbackPlacements: fallbackPlacements.value,\n        transition: menuTransitionName.value,\n        gpuAcceleration: false\n      }, {\n        content: () => {\n          var _a2;\n          return h(\"div\", {\n            class: [nsMenu.m(mode.value), nsMenu.m(\"popup-container\"), subMenuPopperClass.value],\n            onMouseenter: evt => handleMouseenter(evt, 100),\n            onMouseleave: () => handleMouseleave(true),\n            onFocus: evt => handleMouseenter(evt, 100)\n          }, [h(\"ul\", {\n            class: [nsMenu.b(), nsMenu.m(\"popup\"), nsMenu.m(`popup-${currentPlacement.value}`)],\n            style: ulStyle.value\n          }, [(_a2 = slots.default) == null ? void 0 : _a2.call(slots)])]);\n        },\n        default: () => h(\"div\", {\n          class: nsSubMenu.e(\"title\"),\n          onClick: handleClick\n        }, titleTag)\n      }) : h(Fragment, {}, [h(\"div\", {\n        class: nsSubMenu.e(\"title\"),\n        ref: verticalTitleRef,\n        onClick: handleClick\n      }, titleTag), h(ElCollapseTransition, {}, {\n        default: () => {\n          var _a2;\n          return withDirectives(h(\"ul\", {\n            role: \"menu\",\n            class: [nsMenu.b(), nsMenu.m(\"inline\")],\n            style: ulStyle.value\n          }, [(_a2 = slots.default) == null ? void 0 : _a2.call(slots)]), [[vShow, opened.value]]);\n        }\n      })]);\n      return h(\"li\", {\n        class: [nsSubMenu.b(), nsSubMenu.is(\"active\", active.value), nsSubMenu.is(\"opened\", opened.value), nsSubMenu.is(\"disabled\", props.disabled)],\n        role: \"menuitem\",\n        ariaHaspopup: true,\n        ariaExpanded: opened.value,\n        onMouseenter: handleMouseenter,\n        onMouseleave: () => handleMouseleave(),\n        onFocus: handleMouseenter\n      }, [child]);\n    };\n  }\n});\nexport { SubMenu as default, subMenuProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}