{"ast": null, "code": "import { defineComponent, ref, computed, openBlock, createBlock, unref, mergeProps, withCtx, createElementVNode, normalizeClass, normalizeStyle, resolveDynamicComponent, createCommentVNode, createTextVNode, toDisplayString, renderSlot, createVNode } from 'vue';\nimport { ElButton } from '../../button/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { popconfirmProps, popconfirmEmits } from './popconfirm2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nconst __default__ = defineComponent({\n  name: \"ElPopconfirm\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: popconfirmProps,\n  emits: popconfirmEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"popconfirm\");\n    const tooltipRef = ref();\n    const hidePopper = () => {\n      var _a, _b;\n      (_b = (_a = tooltipRef.value) == null ? void 0 : _a.onClose) == null ? void 0 : _b.call(_a);\n    };\n    const style = computed(() => {\n      return {\n        width: addUnit(props.width)\n      };\n    });\n    const confirm = e => {\n      emit(\"confirm\", e);\n      hidePopper();\n    };\n    const cancel = e => {\n      emit(\"cancel\", e);\n      hidePopper();\n    };\n    const finalConfirmButtonText = computed(() => props.confirmButtonText || t(\"el.popconfirm.confirmButtonText\"));\n    const finalCancelButtonText = computed(() => props.cancelButtonText || t(\"el.popconfirm.cancelButtonText\"));\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElTooltip), mergeProps({\n        ref_key: \"tooltipRef\",\n        ref: tooltipRef,\n        trigger: \"click\",\n        effect: \"light\"\n      }, _ctx.$attrs, {\n        \"popper-class\": `${unref(ns).namespace.value}-popover`,\n        \"popper-style\": unref(style),\n        teleported: _ctx.teleported,\n        \"fallback-placements\": [\"bottom\", \"top\", \"right\", \"left\"],\n        \"hide-after\": _ctx.hideAfter,\n        persistent: _ctx.persistent\n      }), {\n        content: withCtx(() => [createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).b())\n        }, [createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).e(\"main\"))\n        }, [!_ctx.hideIcon && _ctx.icon ? (openBlock(), createBlock(unref(ElIcon), {\n          key: 0,\n          class: normalizeClass(unref(ns).e(\"icon\")),\n          style: normalizeStyle({\n            color: _ctx.iconColor\n          })\n        }, {\n          default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.icon)))]),\n          _: 1\n        }, 8, [\"class\", \"style\"])) : createCommentVNode(\"v-if\", true), createTextVNode(\" \" + toDisplayString(_ctx.title), 1)], 2), createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).e(\"action\"))\n        }, [renderSlot(_ctx.$slots, \"actions\", {\n          confirm,\n          cancel\n        }, () => [createVNode(unref(ElButton), {\n          size: \"small\",\n          type: _ctx.cancelButtonType === \"text\" ? \"\" : _ctx.cancelButtonType,\n          text: _ctx.cancelButtonType === \"text\",\n          onClick: cancel\n        }, {\n          default: withCtx(() => [createTextVNode(toDisplayString(unref(finalCancelButtonText)), 1)]),\n          _: 1\n        }, 8, [\"type\", \"text\"]), createVNode(unref(ElButton), {\n          size: \"small\",\n          type: _ctx.confirmButtonType === \"text\" ? \"\" : _ctx.confirmButtonType,\n          text: _ctx.confirmButtonType === \"text\",\n          onClick: confirm\n        }, {\n          default: withCtx(() => [createTextVNode(toDisplayString(unref(finalConfirmButtonText)), 1)]),\n          _: 1\n        }, 8, [\"type\", \"text\"])])], 2)], 2)]),\n        default: withCtx(() => [_ctx.$slots.reference ? renderSlot(_ctx.$slots, \"reference\", {\n          key: 0\n        }) : createCommentVNode(\"v-if\", true)]),\n        _: 3\n      }, 16, [\"popper-class\", \"popper-style\", \"teleported\", \"hide-after\", \"persistent\"]);\n    };\n  }\n});\nvar Popconfirm = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"popconfirm.vue\"]]);\nexport { Popconfirm as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}