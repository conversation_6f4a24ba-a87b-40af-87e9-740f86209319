{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { isVNode, Fragment, Text, Comment, openBlock, createBlock, createCommentVNode } from 'vue';\nimport { isArray, hasOwn, camelize } from '@vue/shared';\nimport { debugWarn } from '../error.mjs';\nconst SCOPE = \"utils/vue/vnode\";\nvar PatchFlags = /* @__PURE__ */(PatchFlags2 => {\n  PatchFlags2[PatchFlags2[\"TEXT\"] = 1] = \"TEXT\";\n  PatchFlags2[PatchFlags2[\"CLASS\"] = 2] = \"CLASS\";\n  PatchFlags2[PatchFlags2[\"STYLE\"] = 4] = \"STYLE\";\n  PatchFlags2[PatchFlags2[\"PROPS\"] = 8] = \"PROPS\";\n  PatchFlags2[PatchFlags2[\"FULL_PROPS\"] = 16] = \"FULL_PROPS\";\n  PatchFlags2[PatchFlags2[\"HYDRATE_EVENTS\"] = 32] = \"HYDRATE_EVENTS\";\n  PatchFlags2[PatchFlags2[\"STABLE_FRAGMENT\"] = 64] = \"STABLE_FRAGMENT\";\n  PatchFlags2[PatchFlags2[\"KEYED_FRAGMENT\"] = 128] = \"KEYED_FRAGMENT\";\n  PatchFlags2[PatchFlags2[\"UNKEYED_FRAGMENT\"] = 256] = \"UNKEYED_FRAGMENT\";\n  PatchFlags2[PatchFlags2[\"NEED_PATCH\"] = 512] = \"NEED_PATCH\";\n  PatchFlags2[PatchFlags2[\"DYNAMIC_SLOTS\"] = 1024] = \"DYNAMIC_SLOTS\";\n  PatchFlags2[PatchFlags2[\"HOISTED\"] = -1] = \"HOISTED\";\n  PatchFlags2[PatchFlags2[\"BAIL\"] = -2] = \"BAIL\";\n  return PatchFlags2;\n})(PatchFlags || {});\nfunction isFragment(node) {\n  return isVNode(node) && node.type === Fragment;\n}\nfunction isText(node) {\n  return isVNode(node) && node.type === Text;\n}\nfunction isComment(node) {\n  return isVNode(node) && node.type === Comment;\n}\nconst TEMPLATE = \"template\";\nfunction isTemplate(node) {\n  return isVNode(node) && node.type === TEMPLATE;\n}\nfunction isValidElementNode(node) {\n  return isVNode(node) && !isFragment(node) && !isComment(node);\n}\nfunction getChildren(node, depth) {\n  if (isComment(node)) return;\n  if (isFragment(node) || isTemplate(node)) {\n    return depth > 0 ? getFirstValidNode(node.children, depth - 1) : void 0;\n  }\n  return node;\n}\nconst getFirstValidNode = (nodes, maxDepth = 3) => {\n  if (isArray(nodes)) {\n    return getChildren(nodes[0], maxDepth);\n  } else {\n    return getChildren(nodes, maxDepth);\n  }\n};\nfunction renderIf(condition, ...args) {\n  return condition ? renderBlock(...args) : createCommentVNode(\"v-if\", true);\n}\nfunction renderBlock(...args) {\n  return openBlock(), createBlock(...args);\n}\nconst getNormalizedProps = node => {\n  if (!isVNode(node)) {\n    debugWarn(SCOPE, \"[getNormalizedProps] must be a VNode\");\n    return {};\n  }\n  const raw = node.props || {};\n  const type = (isVNode(node.type) ? node.type.props : void 0) || {};\n  const props = {};\n  Object.keys(type).forEach(key => {\n    if (hasOwn(type[key], \"default\")) {\n      props[key] = type[key].default;\n    }\n  });\n  Object.keys(raw).forEach(key => {\n    props[camelize(key)] = raw[key];\n  });\n  return props;\n};\nconst ensureOnlyChild = children => {\n  if (!isArray(children) || children.length > 1) {\n    throw new Error(\"expect to receive a single Vue element child\");\n  }\n  return children[0];\n};\nconst flattedChildren = children => {\n  const vNodes = isArray(children) ? children : [children];\n  const result = [];\n  vNodes.forEach(child => {\n    var _a;\n    if (isArray(child)) {\n      result.push(...flattedChildren(child));\n    } else if (isVNode(child) && ((_a = child.component) == null ? void 0 : _a.subTree)) {\n      result.push(child, ...flattedChildren(child.component.subTree));\n    } else if (isVNode(child) && isArray(child.children)) {\n      result.push(...flattedChildren(child.children));\n    } else if (isVNode(child) && child.shapeFlag === 2) {\n      result.push(...flattedChildren(child.type()));\n    } else {\n      result.push(child);\n    }\n  });\n  return result;\n};\nexport { PatchFlags, ensureOnlyChild, flattedChildren, getFirstValidNode, getNormalizedProps, isComment, isFragment, isTemplate, isText, isValidElementNode, renderBlock, renderIf };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}