{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nconst timelineItemProps = buildProps({\n  timestamp: {\n    type: String,\n    default: \"\"\n  },\n  hideTimestamp: <PERSON><PERSON><PERSON>,\n  center: <PERSON><PERSON><PERSON>,\n  placement: {\n    type: String,\n    values: [\"top\", \"bottom\"],\n    default: \"bottom\"\n  },\n  type: {\n    type: String,\n    values: [\"primary\", \"success\", \"warning\", \"danger\", \"info\"],\n    default: \"\"\n  },\n  color: {\n    type: String,\n    default: \"\"\n  },\n  size: {\n    type: String,\n    values: [\"normal\", \"large\"],\n    default: \"normal\"\n  },\n  icon: {\n    type: iconPropType\n  },\n  hollow: <PERSON><PERSON>an\n});\nexport { timelineItemProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}