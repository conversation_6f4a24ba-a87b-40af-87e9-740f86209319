{"ast": null, "code": "import { isNumber } from '../../../utils/types.mjs';\nconst timeUnits = [[\"Y\", 1e3 * 60 * 60 * 24 * 365], [\"M\", 1e3 * 60 * 60 * 24 * 30], [\"D\", 1e3 * 60 * 60 * 24], [\"H\", 1e3 * 60 * 60], [\"m\", 1e3 * 60], [\"s\", 1e3], [\"S\", 1]];\nconst getTime = value => {\n  return isNumber(value) ? new Date(value).getTime() : value.valueOf();\n};\nconst formatTime = (timestamp, format) => {\n  let timeLeft = timestamp;\n  const escapeRegex = /\\[([^\\]]*)]/g;\n  const replacedText = timeUnits.reduce((current, [name, unit]) => {\n    const replaceRegex = new RegExp(`${name}+(?![^\\\\[\\\\]]*\\\\])`, \"g\");\n    if (replaceRegex.test(current)) {\n      const value = Math.floor(timeLeft / unit);\n      timeLeft -= value * unit;\n      return current.replace(replaceRegex, match => String(value).padStart(match.length, \"0\"));\n    }\n    return current;\n  }, format);\n  return replacedText.replace(escapeRegex, \"$1\");\n};\nexport { formatTime, getTime };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}