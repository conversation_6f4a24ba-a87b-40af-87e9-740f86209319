{"ast": null, "code": "import { defineComponent, getCurrentInstance, provide, computed, onBeforeUnmount, resolveComponent, resolveDirective, openBlock, createElementBlock, normalizeClass, normalizeStyle, createElementVNode, renderSlot, withDirectives, createVNode, createCommentVNode, withCtx, createBlock, createTextVNode, toDisplayString, vShow } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport { ElScrollbar } from '../../scrollbar/index.mjs';\nimport { createStore } from './store/helper.mjs';\nimport TableLayout from './table-layout.mjs';\nimport TableHeader from './table-header/index.mjs';\nimport TableBody from './table-body/index.mjs';\nimport TableFooter from './table-footer/index.mjs';\nimport useUtils from './table/utils-helper.mjs';\nimport { convertToRows } from './table-header/utils-helper.mjs';\nimport useStyle from './table/style-helper.mjs';\nimport useKeyRender from './table/key-render-helper.mjs';\nimport defaultProps from './table/defaults.mjs';\nimport { TABLE_INJECTION_KEY } from './tokens.mjs';\nimport { hColgroup } from './h-helper.mjs';\nimport { useScrollbar } from './composables/use-scrollbar.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport Mousewheel from '../../../directives/mousewheel/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nlet tableIdSeed = 1;\nconst _sfc_main = defineComponent({\n  name: \"ElTable\",\n  directives: {\n    Mousewheel\n  },\n  components: {\n    TableHeader,\n    TableBody,\n    TableFooter,\n    ElScrollbar,\n    hColgroup\n  },\n  props: defaultProps,\n  emits: [\"select\", \"select-all\", \"selection-change\", \"cell-mouse-enter\", \"cell-mouse-leave\", \"cell-contextmenu\", \"cell-click\", \"cell-dblclick\", \"row-click\", \"row-contextmenu\", \"row-dblclick\", \"header-click\", \"header-contextmenu\", \"sort-change\", \"filter-change\", \"current-change\", \"header-dragend\", \"expand-change\", \"scroll\"],\n  setup(props) {\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"table\");\n    const table = getCurrentInstance();\n    provide(TABLE_INJECTION_KEY, table);\n    const store = createStore(table, props);\n    table.store = store;\n    const layout = new TableLayout({\n      store: table.store,\n      table,\n      fit: props.fit,\n      showHeader: props.showHeader\n    });\n    table.layout = layout;\n    const isEmpty = computed(() => (store.states.data.value || []).length === 0);\n    const {\n      setCurrentRow,\n      getSelectionRows,\n      toggleRowSelection,\n      clearSelection,\n      clearFilter,\n      toggleAllSelection,\n      toggleRowExpansion,\n      clearSort,\n      sort,\n      updateKeyChildren\n    } = useUtils(store);\n    const {\n      isHidden,\n      renderExpanded,\n      setDragVisible,\n      isGroup,\n      handleMouseLeave,\n      handleHeaderFooterMousewheel,\n      tableSize,\n      emptyBlockStyle,\n      handleFixedMousewheel,\n      resizeProxyVisible,\n      bodyWidth,\n      resizeState,\n      doLayout,\n      tableBodyStyles,\n      tableLayout,\n      scrollbarViewStyle,\n      scrollbarStyle\n    } = useStyle(props, layout, store, table);\n    const {\n      scrollBarRef,\n      scrollTo,\n      setScrollLeft,\n      setScrollTop\n    } = useScrollbar();\n    const debouncedUpdateLayout = debounce(doLayout, 50);\n    const tableId = `${ns.namespace.value}-table_${tableIdSeed++}`;\n    table.tableId = tableId;\n    table.state = {\n      isGroup,\n      resizeState,\n      doLayout,\n      debouncedUpdateLayout\n    };\n    const computedSumText = computed(() => {\n      var _a;\n      return (_a = props.sumText) != null ? _a : t(\"el.table.sumText\");\n    });\n    const computedEmptyText = computed(() => {\n      var _a;\n      return (_a = props.emptyText) != null ? _a : t(\"el.table.emptyText\");\n    });\n    const columns = computed(() => {\n      return convertToRows(store.states.originColumns.value)[0];\n    });\n    useKeyRender(table);\n    onBeforeUnmount(() => {\n      debouncedUpdateLayout.cancel();\n    });\n    return {\n      ns,\n      layout,\n      store,\n      columns,\n      handleHeaderFooterMousewheel,\n      handleMouseLeave,\n      tableId,\n      tableSize,\n      isHidden,\n      isEmpty,\n      renderExpanded,\n      resizeProxyVisible,\n      resizeState,\n      isGroup,\n      bodyWidth,\n      tableBodyStyles,\n      emptyBlockStyle,\n      debouncedUpdateLayout,\n      handleFixedMousewheel,\n      setCurrentRow,\n      getSelectionRows,\n      toggleRowSelection,\n      clearSelection,\n      clearFilter,\n      toggleAllSelection,\n      toggleRowExpansion,\n      clearSort,\n      doLayout,\n      sort,\n      updateKeyChildren,\n      t,\n      setDragVisible,\n      context: table,\n      computedSumText,\n      computedEmptyText,\n      tableLayout,\n      scrollbarViewStyle,\n      scrollbarStyle,\n      scrollBarRef,\n      scrollTo,\n      setScrollLeft,\n      setScrollTop,\n      allowDragLastColumn: props.allowDragLastColumn\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_hColgroup = resolveComponent(\"hColgroup\");\n  const _component_table_header = resolveComponent(\"table-header\");\n  const _component_table_body = resolveComponent(\"table-body\");\n  const _component_table_footer = resolveComponent(\"table-footer\");\n  const _component_el_scrollbar = resolveComponent(\"el-scrollbar\");\n  const _directive_mousewheel = resolveDirective(\"mousewheel\");\n  return openBlock(), createElementBlock(\"div\", {\n    ref: \"tableWrapper\",\n    class: normalizeClass([{\n      [_ctx.ns.m(\"fit\")]: _ctx.fit,\n      [_ctx.ns.m(\"striped\")]: _ctx.stripe,\n      [_ctx.ns.m(\"border\")]: _ctx.border || _ctx.isGroup,\n      [_ctx.ns.m(\"hidden\")]: _ctx.isHidden,\n      [_ctx.ns.m(\"group\")]: _ctx.isGroup,\n      [_ctx.ns.m(\"fluid-height\")]: _ctx.maxHeight,\n      [_ctx.ns.m(\"scrollable-x\")]: _ctx.layout.scrollX.value,\n      [_ctx.ns.m(\"scrollable-y\")]: _ctx.layout.scrollY.value,\n      [_ctx.ns.m(\"enable-row-hover\")]: !_ctx.store.states.isComplex.value,\n      [_ctx.ns.m(\"enable-row-transition\")]: (_ctx.store.states.data.value || []).length !== 0 && (_ctx.store.states.data.value || []).length < 100,\n      \"has-footer\": _ctx.showSummary\n    }, _ctx.ns.m(_ctx.tableSize), _ctx.className, _ctx.ns.b(), _ctx.ns.m(`layout-${_ctx.tableLayout}`)]),\n    style: normalizeStyle(_ctx.style),\n    \"data-prefix\": _ctx.ns.namespace.value,\n    onMouseleave: _ctx.handleMouseLeave\n  }, [createElementVNode(\"div\", {\n    class: normalizeClass(_ctx.ns.e(\"inner-wrapper\"))\n  }, [createElementVNode(\"div\", {\n    ref: \"hiddenColumns\",\n    class: \"hidden-columns\"\n  }, [renderSlot(_ctx.$slots, \"default\")], 512), _ctx.showHeader && _ctx.tableLayout === \"fixed\" ? withDirectives((openBlock(), createElementBlock(\"div\", {\n    key: 0,\n    ref: \"headerWrapper\",\n    class: normalizeClass(_ctx.ns.e(\"header-wrapper\"))\n  }, [createElementVNode(\"table\", {\n    ref: \"tableHeader\",\n    class: normalizeClass(_ctx.ns.e(\"header\")),\n    style: normalizeStyle(_ctx.tableBodyStyles),\n    border: \"0\",\n    cellpadding: \"0\",\n    cellspacing: \"0\"\n  }, [createVNode(_component_hColgroup, {\n    columns: _ctx.store.states.columns.value,\n    \"table-layout\": _ctx.tableLayout\n  }, null, 8, [\"columns\", \"table-layout\"]), createVNode(_component_table_header, {\n    ref: \"tableHeaderRef\",\n    border: _ctx.border,\n    \"default-sort\": _ctx.defaultSort,\n    store: _ctx.store,\n    \"append-filter-panel-to\": _ctx.appendFilterPanelTo,\n    \"allow-drag-last-column\": _ctx.allowDragLastColumn,\n    onSetDragVisible: _ctx.setDragVisible\n  }, null, 8, [\"border\", \"default-sort\", \"store\", \"append-filter-panel-to\", \"allow-drag-last-column\", \"onSetDragVisible\"])], 6)], 2)), [[_directive_mousewheel, _ctx.handleHeaderFooterMousewheel]]) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n    ref: \"bodyWrapper\",\n    class: normalizeClass(_ctx.ns.e(\"body-wrapper\"))\n  }, [createVNode(_component_el_scrollbar, {\n    ref: \"scrollBarRef\",\n    \"view-style\": _ctx.scrollbarViewStyle,\n    \"wrap-style\": _ctx.scrollbarStyle,\n    always: _ctx.scrollbarAlwaysOn,\n    tabindex: _ctx.scrollbarTabindex,\n    onScroll: $event => _ctx.$emit(\"scroll\", $event)\n  }, {\n    default: withCtx(() => [createElementVNode(\"table\", {\n      ref: \"tableBody\",\n      class: normalizeClass(_ctx.ns.e(\"body\")),\n      cellspacing: \"0\",\n      cellpadding: \"0\",\n      border: \"0\",\n      style: normalizeStyle({\n        width: _ctx.bodyWidth,\n        tableLayout: _ctx.tableLayout\n      })\n    }, [createVNode(_component_hColgroup, {\n      columns: _ctx.store.states.columns.value,\n      \"table-layout\": _ctx.tableLayout\n    }, null, 8, [\"columns\", \"table-layout\"]), _ctx.showHeader && _ctx.tableLayout === \"auto\" ? (openBlock(), createBlock(_component_table_header, {\n      key: 0,\n      ref: \"tableHeaderRef\",\n      class: normalizeClass(_ctx.ns.e(\"body-header\")),\n      border: _ctx.border,\n      \"default-sort\": _ctx.defaultSort,\n      store: _ctx.store,\n      \"append-filter-panel-to\": _ctx.appendFilterPanelTo,\n      onSetDragVisible: _ctx.setDragVisible\n    }, null, 8, [\"class\", \"border\", \"default-sort\", \"store\", \"append-filter-panel-to\", \"onSetDragVisible\"])) : createCommentVNode(\"v-if\", true), createVNode(_component_table_body, {\n      context: _ctx.context,\n      highlight: _ctx.highlightCurrentRow,\n      \"row-class-name\": _ctx.rowClassName,\n      \"tooltip-effect\": _ctx.tooltipEffect,\n      \"tooltip-options\": _ctx.tooltipOptions,\n      \"row-style\": _ctx.rowStyle,\n      store: _ctx.store,\n      stripe: _ctx.stripe\n    }, null, 8, [\"context\", \"highlight\", \"row-class-name\", \"tooltip-effect\", \"tooltip-options\", \"row-style\", \"store\", \"stripe\"]), _ctx.showSummary && _ctx.tableLayout === \"auto\" ? (openBlock(), createBlock(_component_table_footer, {\n      key: 1,\n      class: normalizeClass(_ctx.ns.e(\"body-footer\")),\n      border: _ctx.border,\n      \"default-sort\": _ctx.defaultSort,\n      store: _ctx.store,\n      \"sum-text\": _ctx.computedSumText,\n      \"summary-method\": _ctx.summaryMethod\n    }, null, 8, [\"class\", \"border\", \"default-sort\", \"store\", \"sum-text\", \"summary-method\"])) : createCommentVNode(\"v-if\", true)], 6), _ctx.isEmpty ? (openBlock(), createElementBlock(\"div\", {\n      key: 0,\n      ref: \"emptyBlock\",\n      style: normalizeStyle(_ctx.emptyBlockStyle),\n      class: normalizeClass(_ctx.ns.e(\"empty-block\"))\n    }, [createElementVNode(\"span\", {\n      class: normalizeClass(_ctx.ns.e(\"empty-text\"))\n    }, [renderSlot(_ctx.$slots, \"empty\", {}, () => [createTextVNode(toDisplayString(_ctx.computedEmptyText), 1)])], 2)], 6)) : createCommentVNode(\"v-if\", true), _ctx.$slots.append ? (openBlock(), createElementBlock(\"div\", {\n      key: 1,\n      ref: \"appendWrapper\",\n      class: normalizeClass(_ctx.ns.e(\"append-wrapper\"))\n    }, [renderSlot(_ctx.$slots, \"append\")], 2)) : createCommentVNode(\"v-if\", true)]),\n    _: 3\n  }, 8, [\"view-style\", \"wrap-style\", \"always\", \"tabindex\", \"onScroll\"])], 2), _ctx.showSummary && _ctx.tableLayout === \"fixed\" ? withDirectives((openBlock(), createElementBlock(\"div\", {\n    key: 1,\n    ref: \"footerWrapper\",\n    class: normalizeClass(_ctx.ns.e(\"footer-wrapper\"))\n  }, [createElementVNode(\"table\", {\n    class: normalizeClass(_ctx.ns.e(\"footer\")),\n    cellspacing: \"0\",\n    cellpadding: \"0\",\n    border: \"0\",\n    style: normalizeStyle(_ctx.tableBodyStyles)\n  }, [createVNode(_component_hColgroup, {\n    columns: _ctx.store.states.columns.value,\n    \"table-layout\": _ctx.tableLayout\n  }, null, 8, [\"columns\", \"table-layout\"]), createVNode(_component_table_footer, {\n    border: _ctx.border,\n    \"default-sort\": _ctx.defaultSort,\n    store: _ctx.store,\n    \"sum-text\": _ctx.computedSumText,\n    \"summary-method\": _ctx.summaryMethod\n  }, null, 8, [\"border\", \"default-sort\", \"store\", \"sum-text\", \"summary-method\"])], 6)], 2)), [[vShow, !_ctx.isEmpty], [_directive_mousewheel, _ctx.handleHeaderFooterMousewheel]]) : createCommentVNode(\"v-if\", true), _ctx.border || _ctx.isGroup ? (openBlock(), createElementBlock(\"div\", {\n    key: 2,\n    class: normalizeClass(_ctx.ns.e(\"border-left-patch\"))\n  }, null, 2)) : createCommentVNode(\"v-if\", true)], 2), withDirectives(createElementVNode(\"div\", {\n    ref: \"resizeProxy\",\n    class: normalizeClass(_ctx.ns.e(\"column-resize-proxy\"))\n  }, null, 2), [[vShow, _ctx.resizeProxyVisible]])], 46, [\"data-prefix\", \"onMouseleave\"]);\n}\nvar Table = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"table.vue\"]]);\nexport { Table as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}