{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, ref, computed, provide, reactive, watch, onBeforeUpdate, onMounted, nextTick, resolveComponent, openBlock, createElementBlock, normalizeClass, Fragment, renderList, createBlock, withCtx, renderSlot } from 'vue';\nimport { isEqual, flattenDeep, cloneDeep } from 'lodash-unified';\nimport ElCascaderMenu from './menu.mjs';\nimport Store from './store.mjs';\nimport Node from './node.mjs';\nimport { CommonProps, useCascaderConfig } from './config.mjs';\nimport { sortByOriginalOrder, checkNode, getMenuIndex } from './utils.mjs';\nimport { CASCADER_PANEL_INJECTION_KEY } from './types.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { unique, castArray } from '../../../utils/arrays.mjs';\nimport { scrollIntoView } from '../../../utils/dom/scroll.mjs';\nimport { focusNode, getSibling } from '../../../utils/dom/aria.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isEmpty } from '../../../utils/types.mjs';\nimport { isClient } from '@vueuse/core';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElCascaderPanel\",\n  components: {\n    ElCascaderMenu\n  },\n  props: {\n    ...CommonProps,\n    border: {\n      type: Boolean,\n      default: true\n    },\n    renderLabel: Function\n  },\n  emits: [UPDATE_MODEL_EVENT, CHANGE_EVENT, \"close\", \"expand-change\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    let manualChecked = false;\n    const ns = useNamespace(\"cascader\");\n    const config = useCascaderConfig(props);\n    let store = null;\n    const initialLoaded = ref(true);\n    const menuList = ref([]);\n    const checkedValue = ref(null);\n    const menus = ref([]);\n    const expandingNode = ref(null);\n    const checkedNodes = ref([]);\n    const isHoverMenu = computed(() => config.value.expandTrigger === \"hover\");\n    const renderLabelFn = computed(() => props.renderLabel || slots.default);\n    const initStore = () => {\n      const {\n        options\n      } = props;\n      const cfg = config.value;\n      manualChecked = false;\n      store = new Store(options, cfg);\n      menus.value = [store.getNodes()];\n      if (cfg.lazy && isEmpty(props.options)) {\n        initialLoaded.value = false;\n        lazyLoad(void 0, list => {\n          if (list) {\n            store = new Store(list, cfg);\n            menus.value = [store.getNodes()];\n          }\n          initialLoaded.value = true;\n          syncCheckedValue(false, true);\n        });\n      } else {\n        syncCheckedValue(false, true);\n      }\n    };\n    const lazyLoad = (node, cb) => {\n      const cfg = config.value;\n      node = node || new Node({}, cfg, void 0, true);\n      node.loading = true;\n      const resolve = dataList => {\n        const _node = node;\n        const parent = _node.root ? null : _node;\n        dataList && (store == null ? void 0 : store.appendNodes(dataList, parent));\n        _node.loading = false;\n        _node.loaded = true;\n        _node.childrenData = _node.childrenData || [];\n        cb && cb(dataList);\n      };\n      cfg.lazyLoad(node, resolve);\n    };\n    const expandNode = (node, silent) => {\n      var _a;\n      const {\n        level\n      } = node;\n      const newMenus = menus.value.slice(0, level);\n      let newExpandingNode;\n      if (node.isLeaf) {\n        newExpandingNode = node.pathNodes[level - 2];\n      } else {\n        newExpandingNode = node;\n        newMenus.push(node.children);\n      }\n      if (((_a = expandingNode.value) == null ? void 0 : _a.uid) !== (newExpandingNode == null ? void 0 : newExpandingNode.uid)) {\n        expandingNode.value = node;\n        menus.value = newMenus;\n        !silent && emit(\"expand-change\", (node == null ? void 0 : node.pathValues) || []);\n      }\n    };\n    const handleCheckChange = (node, checked, emitClose = true) => {\n      const {\n        checkStrictly,\n        multiple\n      } = config.value;\n      const oldNode = checkedNodes.value[0];\n      manualChecked = true;\n      !multiple && (oldNode == null ? void 0 : oldNode.doCheck(false));\n      node.doCheck(checked);\n      calculateCheckedValue();\n      emitClose && !multiple && !checkStrictly && emit(\"close\");\n      !emitClose && !multiple && !checkStrictly && expandParentNode(node);\n    };\n    const expandParentNode = node => {\n      if (!node) return;\n      node = node.parent;\n      expandParentNode(node);\n      node && expandNode(node);\n    };\n    const getFlattedNodes = leafOnly => {\n      return store == null ? void 0 : store.getFlattedNodes(leafOnly);\n    };\n    const getCheckedNodes = leafOnly => {\n      var _a;\n      return (_a = getFlattedNodes(leafOnly)) == null ? void 0 : _a.filter(node => node.checked !== false);\n    };\n    const clearCheckedNodes = () => {\n      checkedNodes.value.forEach(node => node.doCheck(false));\n      calculateCheckedValue();\n      menus.value = menus.value.slice(0, 1);\n      expandingNode.value = null;\n      emit(\"expand-change\", []);\n    };\n    const calculateCheckedValue = () => {\n      var _a;\n      const {\n        checkStrictly,\n        multiple\n      } = config.value;\n      const oldNodes = checkedNodes.value;\n      const newNodes = getCheckedNodes(!checkStrictly);\n      const nodes = sortByOriginalOrder(oldNodes, newNodes);\n      const values = nodes.map(node => node.valueByOption);\n      checkedNodes.value = nodes;\n      checkedValue.value = multiple ? values : (_a = values[0]) != null ? _a : null;\n    };\n    const syncCheckedValue = (loaded = false, forced = false) => {\n      const {\n        modelValue\n      } = props;\n      const {\n        lazy,\n        multiple,\n        checkStrictly\n      } = config.value;\n      const leafOnly = !checkStrictly;\n      if (!initialLoaded.value || manualChecked || !forced && isEqual(modelValue, checkedValue.value)) return;\n      if (lazy && !loaded) {\n        const values = unique(flattenDeep(castArray(modelValue)));\n        const nodes = values.map(val => store == null ? void 0 : store.getNodeByValue(val)).filter(node => !!node && !node.loaded && !node.loading);\n        if (nodes.length) {\n          nodes.forEach(node => {\n            lazyLoad(node, () => syncCheckedValue(false, forced));\n          });\n        } else {\n          syncCheckedValue(true, forced);\n        }\n      } else {\n        const values = multiple ? castArray(modelValue) : [modelValue];\n        const nodes = unique(values.map(val => store == null ? void 0 : store.getNodeByValue(val, leafOnly)));\n        syncMenuState(nodes, forced);\n        checkedValue.value = cloneDeep(modelValue);\n      }\n    };\n    const syncMenuState = (newCheckedNodes, reserveExpandingState = true) => {\n      const {\n        checkStrictly\n      } = config.value;\n      const oldNodes = checkedNodes.value;\n      const newNodes = newCheckedNodes.filter(node => !!node && (checkStrictly || node.isLeaf));\n      const oldExpandingNode = store == null ? void 0 : store.getSameNode(expandingNode.value);\n      const newExpandingNode = reserveExpandingState && oldExpandingNode || newNodes[0];\n      if (newExpandingNode) {\n        newExpandingNode.pathNodes.forEach(node => expandNode(node, true));\n      } else {\n        expandingNode.value = null;\n      }\n      oldNodes.forEach(node => node.doCheck(false));\n      reactive(newNodes).forEach(node => node.doCheck(true));\n      checkedNodes.value = newNodes;\n      nextTick(scrollToExpandingNode);\n    };\n    const scrollToExpandingNode = () => {\n      if (!isClient) return;\n      menuList.value.forEach(menu => {\n        const menuElement = menu == null ? void 0 : menu.$el;\n        if (menuElement) {\n          const container = menuElement.querySelector(`.${ns.namespace.value}-scrollbar__wrap`);\n          const activeNode = menuElement.querySelector(`.${ns.b(\"node\")}.${ns.is(\"active\")}`) || menuElement.querySelector(`.${ns.b(\"node\")}.in-active-path`);\n          scrollIntoView(container, activeNode);\n        }\n      });\n    };\n    const handleKeyDown = e => {\n      const target = e.target;\n      const {\n        code\n      } = e;\n      switch (code) {\n        case EVENT_CODE.up:\n        case EVENT_CODE.down:\n          {\n            e.preventDefault();\n            const distance = code === EVENT_CODE.up ? -1 : 1;\n            focusNode(getSibling(target, distance, `.${ns.b(\"node\")}[tabindex=\"-1\"]`));\n            break;\n          }\n        case EVENT_CODE.left:\n          {\n            e.preventDefault();\n            const preMenu = menuList.value[getMenuIndex(target) - 1];\n            const expandedNode = preMenu == null ? void 0 : preMenu.$el.querySelector(`.${ns.b(\"node\")}[aria-expanded=\"true\"]`);\n            focusNode(expandedNode);\n            break;\n          }\n        case EVENT_CODE.right:\n          {\n            e.preventDefault();\n            const nextMenu = menuList.value[getMenuIndex(target) + 1];\n            const firstNode = nextMenu == null ? void 0 : nextMenu.$el.querySelector(`.${ns.b(\"node\")}[tabindex=\"-1\"]`);\n            focusNode(firstNode);\n            break;\n          }\n        case EVENT_CODE.enter:\n        case EVENT_CODE.numpadEnter:\n          checkNode(target);\n          break;\n      }\n    };\n    provide(CASCADER_PANEL_INJECTION_KEY, reactive({\n      config,\n      expandingNode,\n      checkedNodes,\n      isHoverMenu,\n      initialLoaded,\n      renderLabelFn,\n      lazyLoad,\n      expandNode,\n      handleCheckChange\n    }));\n    watch([config, () => props.options], initStore, {\n      deep: true,\n      immediate: true\n    });\n    watch(() => props.modelValue, () => {\n      manualChecked = false;\n      syncCheckedValue();\n    }, {\n      deep: true\n    });\n    watch(() => checkedValue.value, val => {\n      if (!isEqual(val, props.modelValue)) {\n        emit(UPDATE_MODEL_EVENT, val);\n        emit(CHANGE_EVENT, val);\n      }\n    });\n    onBeforeUpdate(() => menuList.value = []);\n    onMounted(() => !isEmpty(props.modelValue) && syncCheckedValue());\n    return {\n      ns,\n      menuList,\n      menus,\n      checkedNodes,\n      handleKeyDown,\n      handleCheckChange,\n      getFlattedNodes,\n      getCheckedNodes,\n      clearCheckedNodes,\n      calculateCheckedValue,\n      scrollToExpandingNode\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_cascader_menu = resolveComponent(\"el-cascader-menu\");\n  return openBlock(), createElementBlock(\"div\", {\n    class: normalizeClass([_ctx.ns.b(\"panel\"), _ctx.ns.is(\"bordered\", _ctx.border)]),\n    onKeydown: _ctx.handleKeyDown\n  }, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.menus, (menu, index) => {\n    return openBlock(), createBlock(_component_el_cascader_menu, {\n      key: index,\n      ref_for: true,\n      ref: item => _ctx.menuList[index] = item,\n      index,\n      nodes: [...menu]\n    }, {\n      empty: withCtx(() => [renderSlot(_ctx.$slots, \"empty\")]),\n      _: 2\n    }, 1032, [\"index\", \"nodes\"]);\n  }), 128))], 42, [\"onKeydown\"]);\n}\nvar CascaderPanel = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"index.vue\"]]);\nexport { CascaderPanel as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}