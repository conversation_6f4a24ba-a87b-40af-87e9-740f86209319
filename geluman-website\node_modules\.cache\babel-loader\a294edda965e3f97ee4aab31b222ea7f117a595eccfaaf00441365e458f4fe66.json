{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define(t) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_dayOfYear = t();\n}(this, function () {\n  \"use strict\";\n\n  return function (e, t, n) {\n    t.prototype.dayOfYear = function (e) {\n      var t = Math.round((n(this).startOf(\"day\") - n(this).startOf(\"year\")) / 864e5) + 1;\n      return null == e ? t : this.add(e - t, \"day\");\n    };\n  };\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}