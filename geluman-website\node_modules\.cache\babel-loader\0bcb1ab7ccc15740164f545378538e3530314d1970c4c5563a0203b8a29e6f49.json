{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { isArray } from '@vue/shared';\nconst FontGap = 3;\nfunction prepareCanvas(width, height, ratio = 1) {\n  const canvas = document.createElement(\"canvas\");\n  const ctx = canvas.getContext(\"2d\");\n  const realWidth = width * ratio;\n  const realHeight = height * ratio;\n  canvas.setAttribute(\"width\", `${realWidth}px`);\n  canvas.setAttribute(\"height\", `${realHeight}px`);\n  ctx.save();\n  return [ctx, canvas, realWidth, realHeight];\n}\nfunction useClips() {\n  function getClips(content, rotate, ratio, width, height, font, gapX, gapY) {\n    const [ctx, canvas, contentWidth, contentHeight] = prepareCanvas(width, height, ratio);\n    if (content instanceof HTMLImageElement) {\n      ctx.drawImage(content, 0, 0, contentWidth, contentHeight);\n    } else {\n      const {\n        color,\n        fontSize,\n        fontStyle,\n        fontWeight,\n        fontFamily,\n        textAlign,\n        textBaseline\n      } = font;\n      const mergedFontSize = Number(fontSize) * ratio;\n      ctx.font = `${fontStyle} normal ${fontWeight} ${mergedFontSize}px/${height}px ${fontFamily}`;\n      ctx.fillStyle = color;\n      ctx.textAlign = textAlign;\n      ctx.textBaseline = textBaseline;\n      const contents = isArray(content) ? content : [content];\n      contents == null ? void 0 : contents.forEach((item, index) => {\n        ctx.fillText(item != null ? item : \"\", contentWidth / 2, index * (mergedFontSize + FontGap * ratio));\n      });\n    }\n    const angle = Math.PI / 180 * Number(rotate);\n    const maxSize = Math.max(width, height);\n    const [rCtx, rCanvas, realMaxSize] = prepareCanvas(maxSize, maxSize, ratio);\n    rCtx.translate(realMaxSize / 2, realMaxSize / 2);\n    rCtx.rotate(angle);\n    if (contentWidth > 0 && contentHeight > 0) {\n      rCtx.drawImage(canvas, -contentWidth / 2, -contentHeight / 2);\n    }\n    function getRotatePos(x, y) {\n      const targetX = x * Math.cos(angle) - y * Math.sin(angle);\n      const targetY = x * Math.sin(angle) + y * Math.cos(angle);\n      return [targetX, targetY];\n    }\n    let left = 0;\n    let right = 0;\n    let top = 0;\n    let bottom = 0;\n    const halfWidth = contentWidth / 2;\n    const halfHeight = contentHeight / 2;\n    const points = [[0 - halfWidth, 0 - halfHeight], [0 + halfWidth, 0 - halfHeight], [0 + halfWidth, 0 + halfHeight], [0 - halfWidth, 0 + halfHeight]];\n    points.forEach(([x, y]) => {\n      const [targetX, targetY] = getRotatePos(x, y);\n      left = Math.min(left, targetX);\n      right = Math.max(right, targetX);\n      top = Math.min(top, targetY);\n      bottom = Math.max(bottom, targetY);\n    });\n    const cutLeft = left + realMaxSize / 2;\n    const cutTop = top + realMaxSize / 2;\n    const cutWidth = right - left;\n    const cutHeight = bottom - top;\n    const realGapX = gapX * ratio;\n    const realGapY = gapY * ratio;\n    const filledWidth = (cutWidth + realGapX) * 2;\n    const filledHeight = cutHeight + realGapY;\n    const [fCtx, fCanvas] = prepareCanvas(filledWidth, filledHeight);\n    function drawImg(targetX = 0, targetY = 0) {\n      fCtx.drawImage(rCanvas, cutLeft, cutTop, cutWidth, cutHeight, targetX, targetY, cutWidth, cutHeight);\n    }\n    drawImg();\n    drawImg(cutWidth + realGapX, -cutHeight / 2 - realGapY / 2);\n    drawImg(cutWidth + realGapX, +cutHeight / 2 + realGapY / 2);\n    return [fCanvas.toDataURL(), filledWidth / ratio, filledHeight / ratio];\n  }\n  return getClips;\n}\nexport { FontGap, useClips as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}