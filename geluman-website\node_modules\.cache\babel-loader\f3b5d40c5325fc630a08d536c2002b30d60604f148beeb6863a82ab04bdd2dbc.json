{"ast": null, "code": "import { NOOP } from '@vue/shared';\nconst useSameTarget = handleClick => {\n  if (!handleClick) {\n    return {\n      onClick: NOOP,\n      onMousedown: NOOP,\n      onMouseup: NOOP\n    };\n  }\n  let mousedownTarget = false;\n  let mouseupTarget = false;\n  const onClick = e => {\n    if (mousedownTarget && mouseupTarget) {\n      handleClick(e);\n    }\n    mousedownTarget = mouseupTarget = false;\n  };\n  const onMousedown = e => {\n    mousedownTarget = e.target === e.currentTarget;\n  };\n  const onMouseup = e => {\n    mouseupTarget = e.target === e.currentTarget;\n  };\n  return {\n    onClick,\n    onMousedown,\n    onMouseup\n  };\n};\nexport { useSameTarget };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}