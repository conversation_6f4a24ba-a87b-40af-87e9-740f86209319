{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst RowJustify = [\"start\", \"center\", \"end\", \"space-around\", \"space-between\", \"space-evenly\"];\nconst RowAlign = [\"top\", \"middle\", \"bottom\"];\nconst rowProps = buildProps({\n  tag: {\n    type: String,\n    default: \"div\"\n  },\n  gutter: {\n    type: Number,\n    default: 0\n  },\n  justify: {\n    type: String,\n    values: RowJustify,\n    default: \"start\"\n  },\n  align: {\n    type: String,\n    values: RowAlign\n  }\n});\nexport { RowAlign, RowJustify, rowProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}