"use strict";(self["webpackChunkgeluman_website"]=self["webpackChunkgeluman_website"]||[]).push([[586],{5172:function(e,t,n){n.d(t,{LA:function(){return le},aE:function(){return ot},rd:function(){return at}});n(1484),n(6961),n(4126),n(4615),n(7354),n(9370),n(2807),n(8747),n(4929),n(8200),n(6886),n(6831),n(4118),n(5981),n(3074),n(9724);var r=n(8450),o=n(8018);
/*!
  * vue-router v4.5.0
  * (c) 2024 <PERSON>
  * @license MIT
  */
const c="undefined"!==typeof document;function a(e){return"object"===typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}function s(e){return e.__esModule||"Module"===e[Symbol.toStringTag]||e.default&&a(e.default)}const l=Object.assign;function i(e,t){const n={};for(const r in t){const o=t[r];n[r]=f(o)?o.map(e):e(o)}return n}const u=()=>{},f=Array.isArray;const p=/#/g,h=/&/g,d=/\//g,m=/=/g,g=/\?/g,v=/\+/g,y=/%5B/g,b=/%5D/g,w=/%5E/g,E=/%60/g,R=/%7B/g,k=/%7C/g,O=/%7D/g,C=/%20/g;function P(e){return encodeURI(""+e).replace(k,"|").replace(y,"[").replace(b,"]")}function x(e){return P(e).replace(R,"{").replace(O,"}").replace(w,"^")}function W(e){return P(e).replace(v,"%2B").replace(C,"+").replace(p,"%23").replace(h,"%26").replace(E,"`").replace(R,"{").replace(O,"}").replace(w,"^")}function $(e){return W(e).replace(m,"%3D")}function j(e){return P(e).replace(p,"%23").replace(g,"%3F")}function S(e){return null==e?"":j(e).replace(d,"%2F")}function A(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const q=/\/$/,M=e=>e.replace(q,"");function G(e,t,n="/"){let r,o={},c="",a="";const s=t.indexOf("#");let l=t.indexOf("?");return s<l&&s>=0&&(l=-1),l>-1&&(r=t.slice(0,l),c=t.slice(l+1,s>-1?s:t.length),o=e(c)),s>-1&&(r=r||t.slice(0,s),a=t.slice(s,t.length)),r=U(null!=r?r:t,n),{fullPath:r+(c&&"?")+c+a,path:r,query:o,hash:A(a)}}function L(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function B(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function _(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&I(t.matched[r],n.matched[o])&&T(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function I(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function T(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Q(e[n],t[n]))return!1;return!0}function Q(e,t){return f(e)?D(e,t):f(t)?D(t,e):e===t}function D(e,t){return f(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}function U(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];".."!==o&&"."!==o||r.push("");let c,a,s=n.length-1;for(c=0;c<r.length;c++)if(a=r[c],"."!==a){if(".."!==a)break;s>1&&s--}return n.slice(0,s).join("/")+"/"+r.slice(c).join("/")}const V={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var F,K;(function(e){e["pop"]="pop",e["push"]="push"})(F||(F={})),function(e){e["back"]="back",e["forward"]="forward",e["unknown"]=""}(K||(K={}));function z(e){if(!e)if(c){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),M(e)}const H=/^[^#]+#/;function Y(e,t){return e.replace(H,"#")+t}function X(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const J=()=>({left:window.scrollX,top:window.scrollY});function N(e){let t;if("el"in e){const n=e.el,r="string"===typeof n&&n.startsWith("#");0;const o="string"===typeof n?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=X(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function Z(e,t){const n=history.state?history.state.position-t:-1;return n+e}const ee=new Map;function te(e,t){ee.set(e,t)}function ne(e){const t=ee.get(e);return ee.delete(e),t}let re=()=>location.protocol+"//"+location.host;function oe(e,t){const{pathname:n,search:r,hash:o}=t,c=e.indexOf("#");if(c>-1){let t=o.includes(e.slice(c))?e.slice(c).length:1,n=o.slice(t);return"/"!==n[0]&&(n="/"+n),B(n,"")}const a=B(n,e);return a+r+o}function ce(e,t,n,r){let o=[],c=[],a=null;const s=({state:c})=>{const s=oe(e,location),l=n.value,i=t.value;let u=0;if(c){if(n.value=s,t.value=c,a&&a===l)return void(a=null);u=i?c.position-i.position:0}else r(s);o.forEach((e=>{e(n.value,l,{delta:u,type:F.pop,direction:u?u>0?K.forward:K.back:K.unknown})}))};function i(){a=n.value}function u(e){o.push(e);const t=()=>{const t=o.indexOf(e);t>-1&&o.splice(t,1)};return c.push(t),t}function f(){const{history:e}=window;e.state&&e.replaceState(l({},e.state,{scroll:J()}),"")}function p(){for(const e of c)e();c=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",f)}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",f,{passive:!0}),{pauseListeners:i,listen:u,destroy:p}}function ae(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?J():null}}function se(e){const{history:t,location:n}=window,r={value:oe(e,n)},o={value:t.state};function c(r,c,a){const s=e.indexOf("#"),l=s>-1?(n.host&&document.querySelector("base")?e:e.slice(s))+r:re()+e+r;try{t[a?"replaceState":"pushState"](c,"",l),o.value=c}catch(i){console.error(i),n[a?"replace":"assign"](l)}}function a(e,n){const a=l({},t.state,ae(o.value.back,e,o.value.forward,!0),n,{position:o.value.position});c(e,a,!0),r.value=e}function s(e,n){const a=l({},o.value,t.state,{forward:e,scroll:J()});c(a.current,a,!0);const s=l({},ae(r.value,e,null),{position:a.position+1},n);c(e,s,!1),r.value=e}return o.value||c(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:r,state:o,push:s,replace:a}}function le(e){e=z(e);const t=se(e),n=ce(e,t.state,t.location,t.replace);function r(e,t=!0){t||n.pauseListeners(),history.go(e)}const o=l({location:"",base:e,go:r,createHref:Y.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function ie(e){return"string"===typeof e||e&&"object"===typeof e}function ue(e){return"string"===typeof e||"symbol"===typeof e}const fe=Symbol("");var pe;(function(e){e[e["aborted"]=4]="aborted",e[e["cancelled"]=8]="cancelled",e[e["duplicated"]=16]="duplicated"})(pe||(pe={}));function he(e,t){return l(new Error,{type:e,[fe]:!0},t)}function de(e,t){return e instanceof Error&&fe in e&&(null==t||!!(e.type&t))}const me="[^/]+?",ge={sensitive:!1,strict:!1,start:!0,end:!0},ve=/[.+*?^${}()[\]/\\]/g;function ye(e,t){const n=l({},ge,t),r=[];let o=n.start?"^":"";const c=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(o+="/");for(let t=0;t<l.length;t++){const r=l[t];let a=40+(n.sensitive?.25:0);if(0===r.type)t||(o+="/"),o+=r.value.replace(ve,"\\$&"),a+=40;else if(1===r.type){const{value:e,repeatable:n,optional:s,regexp:i}=r;c.push({name:e,repeatable:n,optional:s});const f=i||me;if(f!==me){a+=10;try{new RegExp(`(${f})`)}catch(u){throw new Error(`Invalid custom RegExp for param "${e}" (${f}): `+u.message)}}let p=n?`((?:${f})(?:/(?:${f}))*)`:`(${f})`;t||(p=s&&l.length<2?`(?:/${p})`:"/"+p),s&&(p+="?"),o+=p,a+=20,s&&(a+=-8),n&&(a+=-20),".*"===f&&(a+=-50)}e.push(a)}r.push(e)}if(n.strict&&n.end){const e=r.length-1;r[e][r[e].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const a=new RegExp(o,n.sensitive?"":"i");function s(e){const t=e.match(a),n={};if(!t)return null;for(let r=1;r<t.length;r++){const e=t[r]||"",o=c[r-1];n[o.name]=e&&o.repeatable?e.split("/"):e}return n}function i(t){let n="",r=!1;for(const o of e){r&&n.endsWith("/")||(n+="/"),r=!1;for(const e of o)if(0===e.type)n+=e.value;else if(1===e.type){const{value:c,repeatable:a,optional:s}=e,l=c in t?t[c]:"";if(f(l)&&!a)throw new Error(`Provided param "${c}" is an array but it is not repeatable (* or + modifiers)`);const i=f(l)?l.join("/"):l;if(!i){if(!s)throw new Error(`Missing required param "${c}"`);o.length<2&&(n.endsWith("/")?n=n.slice(0,-1):r=!0)}n+=i}}return n||"/"}return{re:a,score:r,keys:c,parse:s,stringify:i}}function be(e,t){let n=0;while(n<e.length&&n<t.length){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function we(e,t){let n=0;const r=e.score,o=t.score;while(n<r.length&&n<o.length){const e=be(r[n],o[n]);if(e)return e;n++}if(1===Math.abs(o.length-r.length)){if(Ee(r))return 1;if(Ee(o))return-1}return o.length-r.length}function Ee(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Re={type:0,value:""},ke=/[a-zA-Z0-9_]/;function Oe(e){if(!e)return[[]];if("/"===e)return[[Re]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${i}": ${e}`)}let n=0,r=n;const o=[];let c;function a(){c&&o.push(c),c=[]}let s,l=0,i="",u="";function f(){i&&(0===n?c.push({type:0,value:i}):1===n||2===n||3===n?(c.length>1&&("*"===s||"+"===s)&&t(`A repeatable param (${i}) must be alone in its segment. eg: '/:ids+.`),c.push({type:1,value:i,regexp:u,repeatable:"*"===s||"+"===s,optional:"*"===s||"?"===s})):t("Invalid state to consume buffer"),i="")}function p(){i+=s}while(l<e.length)if(s=e[l++],"\\"!==s||2===n)switch(n){case 0:"/"===s?(i&&f(),a()):":"===s?(f(),n=1):p();break;case 4:p(),n=r;break;case 1:"("===s?n=2:ke.test(s)?p():(f(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&l--);break;case 2:")"===s?"\\"==u[u.length-1]?u=u.slice(0,-1)+s:n=3:u+=s;break;case 3:f(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&l--,u="";break;default:t("Unknown state");break}else r=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${i}"`),f(),a(),o}function Ce(e,t,n){const r=ye(Oe(e.path),n);const o=l(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf===!t.record.aliasOf&&t.children.push(o),o}function Pe(e,t){const n=[],r=new Map;function o(e){return r.get(e)}function c(e,n,r){const o=!r,s=We(e);s.aliasOf=r&&r.record;const f=Ae(t,e),p=[s];if("alias"in e){const t="string"===typeof e.alias?[e.alias]:e.alias;for(const e of t)p.push(We(l({},s,{components:r?r.record.components:s.components,path:e,aliasOf:r?r.record:s})))}let h,d;for(const t of p){const{path:l}=t;if(n&&"/"!==l[0]){const e=n.record.path,r="/"===e[e.length-1]?"":"/";t.path=n.record.path+(l&&r+l)}if(h=Ce(t,n,f),r?r.alias.push(h):(d=d||h,d!==h&&d.alias.push(h),o&&e.name&&!je(h)&&a(e.name)),Ge(h)&&i(h),s.children){const e=s.children;for(let t=0;t<e.length;t++)c(e[t],h,r&&r.children[t])}r=r||h}return d?()=>{a(d)}:u}function a(e){if(ue(e)){const t=r.get(e);t&&(r.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(a),t.alias.forEach(a))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&r.delete(e.record.name),e.children.forEach(a),e.alias.forEach(a))}}function s(){return n}function i(e){const t=qe(e,n);n.splice(t,0,e),e.record.name&&!je(e)&&r.set(e.record.name,e)}function f(e,t){let o,c,a,s={};if("name"in e&&e.name){if(o=r.get(e.name),!o)throw he(1,{location:e});0,a=o.record.name,s=l(xe(t.params,o.keys.filter((e=>!e.optional)).concat(o.parent?o.parent.keys.filter((e=>e.optional)):[]).map((e=>e.name))),e.params&&xe(e.params,o.keys.map((e=>e.name)))),c=o.stringify(s)}else if(null!=e.path)c=e.path,o=n.find((e=>e.re.test(c))),o&&(s=o.parse(c),a=o.record.name);else{if(o=t.name?r.get(t.name):n.find((e=>e.re.test(t.path))),!o)throw he(1,{location:e,currentLocation:t});a=o.record.name,s=l({},t.params,e.params),c=o.stringify(s)}const i=[];let u=o;while(u)i.unshift(u.record),u=u.parent;return{name:a,path:c,params:s,matched:i,meta:Se(i)}}function p(){n.length=0,r.clear()}return t=Ae({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>c(e))),{addRoute:c,resolve:f,removeRoute:a,clearRoutes:p,getRoutes:s,getRecordMatcher:o}}function xe(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function We(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:$e(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function $e(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]="object"===typeof n?n[r]:n;return t}function je(e){while(e){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Se(e){return e.reduce(((e,t)=>l(e,t.meta)),{})}function Ae(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function qe(e,t){let n=0,r=t.length;while(n!==r){const o=n+r>>1,c=we(e,t[o]);c<0?r=o:n=o+1}const o=Me(e);return o&&(r=t.lastIndexOf(o,r-1)),r}function Me(e){let t=e;while(t=t.parent)if(Ge(t)&&0===we(e,t))return t}function Ge({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Le(e){const t={};if(""===e||"?"===e)return t;const n="?"===e[0],r=(n?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const e=r[o].replace(v," "),n=e.indexOf("="),c=A(n<0?e:e.slice(0,n)),a=n<0?null:A(e.slice(n+1));if(c in t){let e=t[c];f(e)||(e=t[c]=[e]),e.push(a)}else t[c]=a}return t}function Be(e){let t="";for(let n in e){const r=e[n];if(n=$(n),null==r){void 0!==r&&(t+=(t.length?"&":"")+n);continue}const o=f(r)?r.map((e=>e&&W(e))):[r&&W(r)];o.forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function _e(e){const t={};for(const n in e){const r=e[n];void 0!==r&&(t[n]=f(r)?r.map((e=>null==e?null:""+e)):null==r?r:""+r)}return t}const Ie=Symbol(""),Te=Symbol(""),Qe=Symbol(""),De=Symbol(""),Ue=Symbol("");function Ve(){let e=[];function t(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Fe(e,t,n,r,o,c=e=>e()){const a=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise(((s,l)=>{const i=e=>{!1===e?l(he(4,{from:n,to:t})):e instanceof Error?l(e):ie(e)?l(he(2,{from:t,to:e})):(a&&r.enterCallbacks[o]===a&&"function"===typeof e&&a.push(e),s())},u=c((()=>e.call(r&&r.instances[o],t,n,i)));let f=Promise.resolve(u);e.length<3&&(f=f.then(i)),f.catch((e=>l(e)))}))}function Ke(e,t,n,r,o=e=>e()){const c=[];for(const l of e){0;for(const e in l.components){let i=l.components[e];if("beforeRouteEnter"===t||l.instances[e])if(a(i)){const a=i.__vccOpts||i,s=a[t];s&&c.push(Fe(s,n,r,l,e,o))}else{let a=i();0,c.push((()=>a.then((c=>{if(!c)throw new Error(`Couldn't resolve component "${e}" at "${l.path}"`);const a=s(c)?c.default:c;l.mods[e]=c,l.components[e]=a;const i=a.__vccOpts||a,u=i[t];return u&&Fe(u,n,r,l,e,o)()}))))}}}return c}function ze(e){const t=(0,r.WQ)(Qe),n=(0,r.WQ)(De);const c=(0,r.EW)((()=>{const n=(0,o.R1)(e.to);return t.resolve(n)})),a=(0,r.EW)((()=>{const{matched:e}=c.value,{length:t}=e,r=e[t-1],o=n.matched;if(!r||!o.length)return-1;const a=o.findIndex(I.bind(null,r));if(a>-1)return a;const s=Ze(e[t-2]);return t>1&&Ze(r)===s&&o[o.length-1].path!==s?o.findIndex(I.bind(null,e[t-2])):a})),s=(0,r.EW)((()=>a.value>-1&&Ne(n.params,c.value.params))),l=(0,r.EW)((()=>a.value>-1&&a.value===n.matched.length-1&&T(n.params,c.value.params)));function i(n={}){if(Je(n)){const n=t[(0,o.R1)(e.replace)?"replace":"push"]((0,o.R1)(e.to)).catch(u);return e.viewTransition&&"undefined"!==typeof document&&"startViewTransition"in document&&document.startViewTransition((()=>n)),n}return Promise.resolve()}return{route:c,href:(0,r.EW)((()=>c.value.href)),isActive:s,isExactActive:l,navigate:i}}function He(e){return 1===e.length?e[0]:e}const Ye=(0,r.pM)({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:ze,setup(e,{slots:t}){const n=(0,o.Kh)(ze(e)),{options:c}=(0,r.WQ)(Qe),a=(0,r.EW)((()=>({[et(e.activeClass,c.linkActiveClass,"router-link-active")]:n.isActive,[et(e.exactActiveClass,c.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&He(t.default(n));return e.custom?o:(0,r.h)("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:a.value},o)}}}),Xe=Ye;function Je(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&(void 0===e.button||0===e.button)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Ne(e,t){for(const n in t){const r=t[n],o=e[n];if("string"===typeof r){if(r!==o)return!1}else if(!f(o)||o.length!==r.length||r.some(((e,t)=>e!==o[t])))return!1}return!0}function Ze(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const et=(e,t,n)=>null!=e?e:null!=t?t:n,tt=(0,r.pM)({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const c=(0,r.WQ)(Ue),a=(0,r.EW)((()=>e.route||c.value)),s=(0,r.WQ)(Te,0),i=(0,r.EW)((()=>{let e=(0,o.R1)(s);const{matched:t}=a.value;let n;while((n=t[e])&&!n.components)e++;return e})),u=(0,r.EW)((()=>a.value.matched[i.value]));(0,r.Gt)(Te,(0,r.EW)((()=>i.value+1))),(0,r.Gt)(Ie,u),(0,r.Gt)(Ue,a);const f=(0,o.KR)();return(0,r.wB)((()=>[f.value,u.value,e.name]),(([e,t,n],[r,o,c])=>{t&&(t.instances[n]=e,o&&o!==t&&e&&e===r&&(t.leaveGuards.size||(t.leaveGuards=o.leaveGuards),t.updateGuards.size||(t.updateGuards=o.updateGuards))),!e||!t||o&&I(t,o)&&r||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=a.value,c=e.name,s=u.value,i=s&&s.components[c];if(!i)return nt(n.default,{Component:i,route:o});const p=s.props[c],h=p?!0===p?o.params:"function"===typeof p?p(o):p:null,d=e=>{e.component.isUnmounted&&(s.instances[c]=null)},m=(0,r.h)(i,l({},h,t,{onVnodeUnmounted:d,ref:f}));return nt(n.default,{Component:m,route:o})||m}}});function nt(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const rt=tt;function ot(e){const t=Pe(e.routes,e),n=e.parseQuery||Le,a=e.stringifyQuery||Be,s=e.history;const p=Ve(),h=Ve(),d=Ve(),m=(0,o.IJ)(V);let g=V;c&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const v=i.bind(null,(e=>""+e)),y=i.bind(null,S),b=i.bind(null,A);function w(e,n){let r,o;return ue(e)?(r=t.getRecordMatcher(e),o=n):o=e,t.addRoute(o,r)}function E(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)}function R(){return t.getRoutes().map((e=>e.record))}function k(e){return!!t.getRecordMatcher(e)}function O(e,r){if(r=l({},r||m.value),"string"===typeof e){const o=G(n,e,r.path),c=t.resolve({path:o.path},r),a=s.createHref(o.fullPath);return l(o,c,{params:b(c.params),hash:A(o.hash),redirectedFrom:void 0,href:a})}let o;if(null!=e.path)o=l({},e,{path:G(n,e.path,r.path).path});else{const t=l({},e.params);for(const e in t)null==t[e]&&delete t[e];o=l({},e,{params:y(t)}),r.params=y(r.params)}const c=t.resolve(o,r),i=e.hash||"";c.params=v(b(c.params));const u=L(a,l({},e,{hash:x(i),path:c.path})),f=s.createHref(u);return l({fullPath:u,hash:i,query:a===Be?_e(e.query):e.query||{}},c,{redirectedFrom:void 0,href:f})}function C(e){return"string"===typeof e?G(n,e,m.value.path):l({},e)}function P(e,t){if(g!==e)return he(8,{from:t,to:e})}function W(e){return q(e)}function $(e){return W(l(C(e),{replace:!0}))}function j(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let r="function"===typeof n?n(e):n;return"string"===typeof r&&(r=r.includes("?")||r.includes("#")?r=C(r):{path:r},r.params={}),l({query:e.query,hash:e.hash,params:null!=r.path?{}:e.params},r)}}function q(e,t){const n=g=O(e),r=m.value,o=e.state,c=e.force,s=!0===e.replace,i=j(n);if(i)return q(l(C(i),{state:"object"===typeof i?l({},o,i.state):o,force:c,replace:s}),t||n);const u=n;let f;return u.redirectedFrom=t,!c&&_(a,r,n)&&(f=he(16,{to:u,from:r}),re(r,r,!0,!1)),(f?Promise.resolve(f):I(u,r)).catch((e=>de(e)?de(e,2)?e:ee(e):Y(e,u,r))).then((e=>{if(e){if(de(e,2))return q(l({replace:s},C(e.to),{state:"object"===typeof e.to?l({},o,e.to.state):o,force:c}),t||u)}else e=Q(u,r,!0,s,o);return T(u,r,e),e}))}function M(e,t){const n=P(e,t);return n?Promise.reject(n):Promise.resolve()}function B(e){const t=ae.values().next().value;return t&&"function"===typeof t.runWithContext?t.runWithContext(e):e()}function I(e,t){let n;const[r,o,c]=ct(e,t);n=Ke(r.reverse(),"beforeRouteLeave",e,t);for(const s of r)s.leaveGuards.forEach((r=>{n.push(Fe(r,e,t))}));const a=M.bind(null,e,t);return n.push(a),le(n).then((()=>{n=[];for(const r of p.list())n.push(Fe(r,e,t));return n.push(a),le(n)})).then((()=>{n=Ke(o,"beforeRouteUpdate",e,t);for(const r of o)r.updateGuards.forEach((r=>{n.push(Fe(r,e,t))}));return n.push(a),le(n)})).then((()=>{n=[];for(const r of c)if(r.beforeEnter)if(f(r.beforeEnter))for(const o of r.beforeEnter)n.push(Fe(o,e,t));else n.push(Fe(r.beforeEnter,e,t));return n.push(a),le(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=Ke(c,"beforeRouteEnter",e,t,B),n.push(a),le(n)))).then((()=>{n=[];for(const r of h.list())n.push(Fe(r,e,t));return n.push(a),le(n)})).catch((e=>de(e,8)?e:Promise.reject(e)))}function T(e,t,n){d.list().forEach((r=>B((()=>r(e,t,n)))))}function Q(e,t,n,r,o){const a=P(e,t);if(a)return a;const i=t===V,u=c?history.state:{};n&&(r||i?s.replace(e.fullPath,l({scroll:i&&u&&u.scroll},o)):s.push(e.fullPath,o)),m.value=e,re(e,t,n,i),ee()}let D;function U(){D||(D=s.listen(((e,t,n)=>{if(!se.listening)return;const r=O(e),o=j(r);if(o)return void q(l(o,{replace:!0,force:!0}),r).catch(u);g=r;const a=m.value;c&&te(Z(a.fullPath,n.delta),J()),I(r,a).catch((e=>de(e,12)?e:de(e,2)?(q(l(C(e.to),{force:!0}),r).then((e=>{de(e,20)&&!n.delta&&n.type===F.pop&&s.go(-1,!1)})).catch(u),Promise.reject()):(n.delta&&s.go(-n.delta,!1),Y(e,r,a)))).then((e=>{e=e||Q(r,a,!1),e&&(n.delta&&!de(e,8)?s.go(-n.delta,!1):n.type===F.pop&&de(e,20)&&s.go(-1,!1)),T(r,a,e)})).catch(u)})))}let K,z=Ve(),H=Ve();function Y(e,t,n){ee(e);const r=H.list();return r.length?r.forEach((r=>r(e,t,n))):console.error(e),Promise.reject(e)}function X(){return K&&m.value!==V?Promise.resolve():new Promise(((e,t)=>{z.add([e,t])}))}function ee(e){return K||(K=!e,U(),z.list().forEach((([t,n])=>e?n(e):t())),z.reset()),e}function re(t,n,o,a){const{scrollBehavior:s}=e;if(!c||!s)return Promise.resolve();const l=!o&&ne(Z(t.fullPath,0))||(a||!o)&&history.state&&history.state.scroll||null;return(0,r.dY)().then((()=>s(t,n,l))).then((e=>e&&N(e))).catch((e=>Y(e,t,n)))}const oe=e=>s.go(e);let ce;const ae=new Set,se={currentRoute:m,listening:!0,addRoute:w,removeRoute:E,clearRoutes:t.clearRoutes,hasRoute:k,getRoutes:R,resolve:O,options:e,push:W,replace:$,go:oe,back:()=>oe(-1),forward:()=>oe(1),beforeEach:p.add,beforeResolve:h.add,afterEach:d.add,onError:H.add,isReady:X,install(e){const t=this;e.component("RouterLink",Xe),e.component("RouterView",rt),e.config.globalProperties.$router=t,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>(0,o.R1)(m)}),c&&!ce&&m.value===V&&(ce=!0,W(s.location).catch((e=>{0})));const n={};for(const o in V)Object.defineProperty(n,o,{get:()=>m.value[o],enumerable:!0});e.provide(Qe,t),e.provide(De,(0,o.Gc)(n)),e.provide(Ue,m);const r=e.unmount;ae.add(e),e.unmount=function(){ae.delete(e),ae.size<1&&(g=V,D&&D(),D=null,m.value=V,ce=!1,K=!1),r()}}};function le(e){return e.reduce(((e,t)=>e.then((()=>B(t)))),Promise.resolve())}return se}function ct(e,t){const n=[],r=[],o=[],c=Math.max(t.matched.length,e.matched.length);for(let a=0;a<c;a++){const c=t.matched[a];c&&(e.matched.find((e=>I(e,c)))?r.push(c):n.push(c));const s=e.matched[a];s&&(t.matched.find((e=>I(e,s)))||o.push(s))}return[n,r,o]}function at(){return(0,r.WQ)(Qe)}}}]);