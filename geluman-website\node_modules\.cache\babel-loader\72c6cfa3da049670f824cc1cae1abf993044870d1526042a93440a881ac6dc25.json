{"ast": null, "code": "import { defineComponent, ref, watch, computed, openBlock, createElementBlock, normalizeClass, unref, createVNode, withCtx, Fragment, renderList, createBlock } from 'vue';\nimport { isEqual } from 'lodash-unified';\nimport { ElSelect, ElOption } from '../../../select/index.mjs';\nimport { usePagination } from '../usePagination.mjs';\nimport { paginationSizesProps } from './sizes.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isArray } from '@vue/shared';\nconst __default__ = defineComponent({\n  name: \"ElPaginationSizes\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: paginationSizesProps,\n  emits: [\"page-size-change\"],\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"pagination\");\n    const pagination = usePagination();\n    const innerPageSize = ref(props.pageSize);\n    watch(() => props.pageSizes, (newVal, oldVal) => {\n      if (isEqual(newVal, oldVal)) return;\n      if (isArray(newVal)) {\n        const pageSize = newVal.includes(props.pageSize) ? props.pageSize : props.pageSizes[0];\n        emit(\"page-size-change\", pageSize);\n      }\n    });\n    watch(() => props.pageSize, newVal => {\n      innerPageSize.value = newVal;\n    });\n    const innerPageSizes = computed(() => props.pageSizes);\n    function handleChange(val) {\n      var _a;\n      if (val !== innerPageSize.value) {\n        innerPageSize.value = val;\n        (_a = pagination.handleSizeChange) == null ? void 0 : _a.call(pagination, Number(val));\n      }\n    }\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"span\", {\n        class: normalizeClass(unref(ns).e(\"sizes\"))\n      }, [createVNode(unref(ElSelect), {\n        \"model-value\": innerPageSize.value,\n        disabled: _ctx.disabled,\n        \"popper-class\": _ctx.popperClass,\n        size: _ctx.size,\n        teleported: _ctx.teleported,\n        \"validate-event\": false,\n        \"append-to\": _ctx.appendSizeTo,\n        onChange: handleChange\n      }, {\n        default: withCtx(() => [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(innerPageSizes), item => {\n          return openBlock(), createBlock(unref(ElOption), {\n            key: item,\n            value: item,\n            label: item + unref(t)(\"el.pagination.pagesize\")\n          }, null, 8, [\"value\", \"label\"]);\n        }), 128))]),\n        _: 1\n      }, 8, [\"model-value\", \"disabled\", \"popper-class\", \"size\", \"teleported\", \"append-to\"])], 2);\n    };\n  }\n});\nvar Sizes = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"sizes.vue\"]]);\nexport { Sizes as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}