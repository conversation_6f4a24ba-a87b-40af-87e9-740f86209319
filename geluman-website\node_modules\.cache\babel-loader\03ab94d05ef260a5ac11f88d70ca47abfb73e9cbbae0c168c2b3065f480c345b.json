{"ast": null, "code": "import { tourContentProps } from './content2.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nconst tourStepProps = buildProps({\n  target: {\n    type: definePropType([String, Object, Function])\n  },\n  title: String,\n  description: String,\n  showClose: {\n    type: Boolean,\n    default: void 0\n  },\n  closeIcon: {\n    type: iconPropType\n  },\n  showArrow: {\n    type: Boolean,\n    default: void 0\n  },\n  placement: tourContentProps.placement,\n  mask: {\n    type: definePropType([Boolean, Object]),\n    default: void 0\n  },\n  contentStyle: {\n    type: definePropType([Object])\n  },\n  prevButtonProps: {\n    type: definePropType(Object)\n  },\n  nextButtonProps: {\n    type: definePropType(Object)\n  },\n  scrollIntoViewOptions: {\n    type: definePropType([Boolean, Object]),\n    default: void 0\n  },\n  type: {\n    type: definePropType(String)\n  }\n});\nconst tourStepEmits = {\n  close: () => true\n};\nexport { tourStepEmits, tourStepProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}