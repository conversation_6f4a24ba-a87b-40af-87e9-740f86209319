{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { ref, computed } from 'vue';\nimport { useProps } from './useProps.mjs';\nfunction useAllowCreate(props, states) {\n  const {\n    aliasProps,\n    getLabel,\n    getValue\n  } = useProps(props);\n  const createOptionCount = ref(0);\n  const cachedSelectedOption = ref();\n  const enableAllowCreateMode = computed(() => {\n    return props.allowCreate && props.filterable;\n  });\n  function hasExistingOption(query) {\n    const hasOption = option => getLabel(option) === query;\n    return props.options && props.options.some(hasOption) || states.createdOptions.some(hasOption);\n  }\n  function selectNewOption(option) {\n    if (!enableAllowCreateMode.value) {\n      return;\n    }\n    if (props.multiple && option.created) {\n      createOptionCount.value++;\n    } else {\n      cachedSelectedOption.value = option;\n    }\n  }\n  function createNewOption(query) {\n    if (enableAllowCreateMode.value) {\n      if (query && query.length > 0) {\n        if (hasExistingOption(query)) {\n          return;\n        }\n        const newOption = {\n          [aliasProps.value.value]: query,\n          [aliasProps.value.label]: query,\n          created: true,\n          [aliasProps.value.disabled]: false\n        };\n        if (states.createdOptions.length >= createOptionCount.value) {\n          states.createdOptions[createOptionCount.value] = newOption;\n        } else {\n          states.createdOptions.push(newOption);\n        }\n      } else {\n        if (props.multiple) {\n          states.createdOptions.length = createOptionCount.value;\n        } else {\n          const selectedOption = cachedSelectedOption.value;\n          states.createdOptions.length = 0;\n          if (selectedOption && selectedOption.created) {\n            states.createdOptions.push(selectedOption);\n          }\n        }\n      }\n    }\n  }\n  function removeNewOption(option) {\n    if (!enableAllowCreateMode.value || !option || !option.created || option.created && props.reserveKeyword && states.inputValue === getLabel(option)) {\n      return;\n    }\n    const idx = states.createdOptions.findIndex(it => getValue(it) === getValue(option));\n    if (~idx) {\n      states.createdOptions.splice(idx, 1);\n      createOptionCount.value--;\n    }\n  }\n  function clearAllNewOption() {\n    if (enableAllowCreateMode.value) {\n      states.createdOptions.length = 0;\n      createOptionCount.value = 0;\n    }\n  }\n  return {\n    createNewOption,\n    removeNewOption,\n    selectNewOption,\n    clearAllNewOption\n  };\n}\nexport { useAllowCreate };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}