{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { isFirefox } from '../../../utils/browser.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nlet hiddenTextarea = void 0;\nconst HIDDEN_STYLE = {\n  height: \"0\",\n  visibility: \"hidden\",\n  overflow: isFirefox() ? \"\" : \"hidden\",\n  position: \"absolute\",\n  \"z-index\": \"-1000\",\n  top: \"0\",\n  right: \"0\"\n};\nconst CONTEXT_STYLE = [\"letter-spacing\", \"line-height\", \"padding-top\", \"padding-bottom\", \"font-family\", \"font-weight\", \"font-size\", \"text-rendering\", \"text-transform\", \"width\", \"text-indent\", \"padding-left\", \"padding-right\", \"border-width\", \"box-sizing\"];\nfunction calculateNodeStyling(targetElement) {\n  const style = window.getComputedStyle(targetElement);\n  const boxSizing = style.getPropertyValue(\"box-sizing\");\n  const paddingSize = Number.parseFloat(style.getPropertyValue(\"padding-bottom\")) + Number.parseFloat(style.getPropertyValue(\"padding-top\"));\n  const borderSize = Number.parseFloat(style.getPropertyValue(\"border-bottom-width\")) + Number.parseFloat(style.getPropertyValue(\"border-top-width\"));\n  const contextStyle = CONTEXT_STYLE.map(name => [name, style.getPropertyValue(name)]);\n  return {\n    contextStyle,\n    paddingSize,\n    borderSize,\n    boxSizing\n  };\n}\nfunction calcTextareaHeight(targetElement, minRows = 1, maxRows) {\n  var _a;\n  if (!hiddenTextarea) {\n    hiddenTextarea = document.createElement(\"textarea\");\n    document.body.appendChild(hiddenTextarea);\n  }\n  const {\n    paddingSize,\n    borderSize,\n    boxSizing,\n    contextStyle\n  } = calculateNodeStyling(targetElement);\n  contextStyle.forEach(([key, value]) => hiddenTextarea == null ? void 0 : hiddenTextarea.style.setProperty(key, value));\n  Object.entries(HIDDEN_STYLE).forEach(([key, value]) => hiddenTextarea == null ? void 0 : hiddenTextarea.style.setProperty(key, value, \"important\"));\n  hiddenTextarea.value = targetElement.value || targetElement.placeholder || \"\";\n  let height = hiddenTextarea.scrollHeight;\n  const result = {};\n  if (boxSizing === \"border-box\") {\n    height = height + borderSize;\n  } else if (boxSizing === \"content-box\") {\n    height = height - paddingSize;\n  }\n  hiddenTextarea.value = \"\";\n  const singleRowHeight = hiddenTextarea.scrollHeight - paddingSize;\n  if (isNumber(minRows)) {\n    let minHeight = singleRowHeight * minRows;\n    if (boxSizing === \"border-box\") {\n      minHeight = minHeight + paddingSize + borderSize;\n    }\n    height = Math.max(minHeight, height);\n    result.minHeight = `${minHeight}px`;\n  }\n  if (isNumber(maxRows)) {\n    let maxHeight = singleRowHeight * maxRows;\n    if (boxSizing === \"border-box\") {\n      maxHeight = maxHeight + paddingSize + borderSize;\n    }\n    height = Math.min(maxHeight, height);\n  }\n  result.height = `${height}px`;\n  (_a = hiddenTextarea.parentNode) == null ? void 0 : _a.removeChild(hiddenTextarea);\n  hiddenTextarea = void 0;\n  return result;\n}\nexport { calcTextareaHeight };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}