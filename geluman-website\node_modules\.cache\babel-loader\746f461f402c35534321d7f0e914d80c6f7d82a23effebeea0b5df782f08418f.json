{"ast": null, "code": "import { ref, inject, computed } from 'vue';\nimport { radioGroupKey } from './constants.mjs';\nimport { isPropAbsent } from '../../../utils/types.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { useFormSize, useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useDeprecated } from '../../../hooks/use-deprecated/index.mjs';\nconst useRadio = (props, emit) => {\n  const radioRef = ref();\n  const radioGroup = inject(radioGroupKey, void 0);\n  const isGroup = computed(() => !!radioGroup);\n  const actualValue = computed(() => {\n    if (!isPropAbsent(props.value)) {\n      return props.value;\n    }\n    return props.label;\n  });\n  const modelValue = computed({\n    get() {\n      return isGroup.value ? radioGroup.modelValue : props.modelValue;\n    },\n    set(val) {\n      if (isGroup.value) {\n        radioGroup.changeEvent(val);\n      } else {\n        emit && emit(UPDATE_MODEL_EVENT, val);\n      }\n      radioRef.value.checked = props.modelValue === actualValue.value;\n    }\n  });\n  const size = useFormSize(computed(() => radioGroup == null ? void 0 : radioGroup.size));\n  const disabled = useFormDisabled(computed(() => radioGroup == null ? void 0 : radioGroup.disabled));\n  const focus = ref(false);\n  const tabIndex = computed(() => {\n    return disabled.value || isGroup.value && modelValue.value !== actualValue.value ? -1 : 0;\n  });\n  useDeprecated({\n    from: \"label act as value\",\n    replacement: \"value\",\n    version: \"3.0.0\",\n    scope: \"el-radio\",\n    ref: \"https://element-plus.org/en-US/component/radio.html\"\n  }, computed(() => isGroup.value && isPropAbsent(props.value)));\n  return {\n    radioRef,\n    isGroup,\n    radioGroup,\n    focus,\n    size,\n    disabled,\n    tabIndex,\n    modelValue,\n    actualValue\n  };\n};\nexport { useRadio };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}