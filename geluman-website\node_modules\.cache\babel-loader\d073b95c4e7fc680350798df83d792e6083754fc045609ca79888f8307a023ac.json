{"ast": null, "code": "import createList from '../builders/build-list.mjs';\nimport { isHorizontal } from '../utils.mjs';\nimport { AUTO_ALIGNMENT, CENTERED_ALIGNMENT, END_ALIGNMENT, START_ALIGNMENT, DEFAULT_DYNAMIC_LIST_ITEM_SIZE, SMART_ALIGNMENT } from '../defaults.mjs';\nimport { throwError } from '../../../../utils/error.mjs';\nconst SCOPE = \"ElDynamicSizeList\";\nconst getItemFromCache = (props, index, listCache) => {\n  const {\n    itemSize\n  } = props;\n  const {\n    items,\n    lastVisitedIndex\n  } = listCache;\n  if (index > lastVisitedIndex) {\n    let offset = 0;\n    if (lastVisitedIndex >= 0) {\n      const item = items[lastVisitedIndex];\n      offset = item.offset + item.size;\n    }\n    for (let i = lastVisitedIndex + 1; i <= index; i++) {\n      const size = itemSize(i);\n      items[i] = {\n        offset,\n        size\n      };\n      offset += size;\n    }\n    listCache.lastVisitedIndex = index;\n  }\n  return items[index];\n};\nconst findItem = (props, listCache, offset) => {\n  const {\n    items,\n    lastVisitedIndex\n  } = listCache;\n  const lastVisitedOffset = lastVisitedIndex > 0 ? items[lastVisitedIndex].offset : 0;\n  if (lastVisitedOffset >= offset) {\n    return bs(props, listCache, 0, lastVisitedIndex, offset);\n  }\n  return es(props, listCache, Math.max(0, lastVisitedIndex), offset);\n};\nconst bs = (props, listCache, low, high, offset) => {\n  while (low <= high) {\n    const mid = low + Math.floor((high - low) / 2);\n    const currentOffset = getItemFromCache(props, mid, listCache).offset;\n    if (currentOffset === offset) {\n      return mid;\n    } else if (currentOffset < offset) {\n      low = mid + 1;\n    } else if (currentOffset > offset) {\n      high = mid - 1;\n    }\n  }\n  return Math.max(0, low - 1);\n};\nconst es = (props, listCache, index, offset) => {\n  const {\n    total\n  } = props;\n  let exponent = 1;\n  while (index < total && getItemFromCache(props, index, listCache).offset < offset) {\n    index += exponent;\n    exponent *= 2;\n  }\n  return bs(props, listCache, Math.floor(index / 2), Math.min(index, total - 1), offset);\n};\nconst getEstimatedTotalSize = ({\n  total\n}, {\n  items,\n  estimatedItemSize,\n  lastVisitedIndex\n}) => {\n  let totalSizeOfMeasuredItems = 0;\n  if (lastVisitedIndex >= total) {\n    lastVisitedIndex = total - 1;\n  }\n  if (lastVisitedIndex >= 0) {\n    const item = items[lastVisitedIndex];\n    totalSizeOfMeasuredItems = item.offset + item.size;\n  }\n  const numUnmeasuredItems = total - lastVisitedIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedItemSize;\n  return totalSizeOfMeasuredItems + totalSizeOfUnmeasuredItems;\n};\nconst DynamicSizeList = createList({\n  name: \"ElDynamicSizeList\",\n  getItemOffset: (props, index, listCache) => getItemFromCache(props, index, listCache).offset,\n  getItemSize: (_, index, {\n    items\n  }) => items[index].size,\n  getEstimatedTotalSize,\n  getOffset: (props, index, alignment, scrollOffset, listCache) => {\n    const {\n      height,\n      layout,\n      width\n    } = props;\n    const size = isHorizontal(layout) ? width : height;\n    const item = getItemFromCache(props, index, listCache);\n    const estimatedTotalSize = getEstimatedTotalSize(props, listCache);\n    const maxOffset = Math.max(0, Math.min(estimatedTotalSize - size, item.offset));\n    const minOffset = Math.max(0, item.offset - size + item.size);\n    if (alignment === SMART_ALIGNMENT) {\n      if (scrollOffset >= minOffset - size && scrollOffset <= maxOffset + size) {\n        alignment = AUTO_ALIGNMENT;\n      } else {\n        alignment = CENTERED_ALIGNMENT;\n      }\n    }\n    switch (alignment) {\n      case START_ALIGNMENT:\n        {\n          return maxOffset;\n        }\n      case END_ALIGNMENT:\n        {\n          return minOffset;\n        }\n      case CENTERED_ALIGNMENT:\n        {\n          return Math.round(minOffset + (maxOffset - minOffset) / 2);\n        }\n      case AUTO_ALIGNMENT:\n      default:\n        {\n          if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n            return scrollOffset;\n          } else if (scrollOffset < minOffset) {\n            return minOffset;\n          } else {\n            return maxOffset;\n          }\n        }\n    }\n  },\n  getStartIndexForOffset: (props, offset, listCache) => findItem(props, listCache, offset),\n  getStopIndexForStartIndex: (props, startIndex, scrollOffset, listCache) => {\n    const {\n      height,\n      total,\n      layout,\n      width\n    } = props;\n    const size = isHorizontal(layout) ? width : height;\n    const item = getItemFromCache(props, startIndex, listCache);\n    const maxOffset = scrollOffset + size;\n    let offset = item.offset + item.size;\n    let stopIndex = startIndex;\n    while (stopIndex < total - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemFromCache(props, stopIndex, listCache).size;\n    }\n    return stopIndex;\n  },\n  initCache({\n    estimatedItemSize = DEFAULT_DYNAMIC_LIST_ITEM_SIZE\n  }, instance) {\n    const cache = {\n      items: {},\n      estimatedItemSize,\n      lastVisitedIndex: -1\n    };\n    cache.clearCacheAfterIndex = (index, forceUpdate = true) => {\n      var _a, _b;\n      cache.lastVisitedIndex = Math.min(cache.lastVisitedIndex, index - 1);\n      (_a = instance.exposed) == null ? void 0 : _a.getItemStyleCache(-1);\n      if (forceUpdate) {\n        (_b = instance.proxy) == null ? void 0 : _b.$forceUpdate();\n      }\n    };\n    return cache;\n  },\n  clearCache: false,\n  validateProps: ({\n    itemSize\n  }) => {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (typeof itemSize !== \"function\") {\n        throwError(SCOPE, `\n          itemSize is required as function, but the given value was ${typeof itemSize}\n        `);\n      }\n    }\n  }\n});\nexport { DynamicSizeList as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}