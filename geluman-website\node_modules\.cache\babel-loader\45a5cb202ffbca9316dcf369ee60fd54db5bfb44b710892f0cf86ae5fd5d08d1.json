{"ast": null, "code": "import baseSlice from './_baseSlice.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Creates a slice of `array` with `n` elements dropped from the end.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {number} [n=1] The number of elements to drop.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * _.dropRight([1, 2, 3]);\n * // => [1, 2]\n *\n * _.dropRight([1, 2, 3], 2);\n * // => [1]\n *\n * _.dropRight([1, 2, 3], 5);\n * // => []\n *\n * _.dropRight([1, 2, 3], 0);\n * // => [1, 2, 3]\n */\nfunction dropRight(array, n, guard) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return [];\n  }\n  n = guard || n === undefined ? 1 : toInteger(n);\n  n = length - n;\n  return baseSlice(array, 0, n < 0 ? 0 : n);\n}\nexport default dropRight;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}