{"ast": null, "code": "import { unref as _unref, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"site-footer\"\n};\nconst _hoisted_2 = {\n  class: \"footer-content\"\n};\nconst _hoisted_3 = {\n  class: \"footer-logo\"\n};\nconst _hoisted_4 = [\"src\"];\nconst _hoisted_5 = {\n  class: \"copyright\"\n};\nimport { ref } from \"vue\";\nimport { logo } from \"@/assets\";\nexport default {\n  __name: 'SiteFooter',\n  setup(__props) {\n    const currentYear = ref(new Date().getFullYear());\n    return (_ctx, _cache) => {\n      return _openBlock(), _createElementBlock(\"footer\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"img\", {\n        src: _unref(logo),\n        alt: \"格鲁曼\"\n      }, null, 8, _hoisted_4)]), _createElementVNode(\"p\", _hoisted_5, [_createTextVNode(\" © \" + _toDisplayString(currentYear.value) + \" 格鲁曼 All rights reserved. \", 1), _cache[0] || (_cache[0] = _createElementVNode(\"a\", {\n        href: \"https://beian.miit.gov.cn/\",\n        target: \"_blank\",\n        rel: \"noopener\"\n      }, \" 蜀ICP备2023004112号 \", -1))])])]);\n    };\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}