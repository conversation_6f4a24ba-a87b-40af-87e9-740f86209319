{"ast": null, "code": "import arrayPush from './_arrayPush.js';\nimport baseFlatten from './_baseFlatten.js';\nimport copyArray from './_copyArray.js';\nimport isArray from './isArray.js';\n\n/**\n * Creates a new array concatenating `array` with any additional arrays\n * and/or values.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to concatenate.\n * @param {...*} [values] The values to concatenate.\n * @returns {Array} Returns the new concatenated array.\n * @example\n *\n * var array = [1];\n * var other = _.concat(array, 2, [3], [[4]]);\n *\n * console.log(other);\n * // => [1, 2, 3, [4]]\n *\n * console.log(array);\n * // => [1]\n */\nfunction concat() {\n  var length = arguments.length;\n  if (!length) {\n    return [];\n  }\n  var args = Array(length - 1),\n    array = arguments[0],\n    index = length;\n  while (index--) {\n    args[index - 1] = arguments[index];\n  }\n  return arrayPush(isArray(array) ? copyArray(array) : [array], baseFlatten(args, 1));\n}\nexport default concat;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}