{"ast": null, "code": "import { isArray, isObject, isString } from '@vue/shared';\nexport { isArray, isDate, isFunction, isObject, isPlainObject, isPromise, isString, isSymbol } from '@vue/shared';\nimport { isNil } from 'lodash-unified';\nconst isUndefined = val => val === void 0;\nconst isBoolean = val => typeof val === \"boolean\";\nconst isNumber = val => typeof val === \"number\";\nconst isEmpty = val => !val && val !== 0 || isArray(val) && val.length === 0 || isObject(val) && !Object.keys(val).length;\nconst isElement = e => {\n  if (typeof Element === \"undefined\") return false;\n  return e instanceof Element;\n};\nconst isPropAbsent = prop => isNil(prop);\nconst isStringNumber = val => {\n  if (!isString(val)) {\n    return false;\n  }\n  return !Number.isNaN(Number(val));\n};\nconst isWindow = val => val === window;\nexport { isBoolean, isElement, isEmpty, isNumber, isPropAbsent, isStringNumber, isUndefined, isWindow };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}