{"ast": null, "code": "import { getCurrentInstance, shallowRef, ref, watch, onMounted } from 'vue';\nimport { useEventListener } from '@vueuse/core';\nimport { useProp } from '../use-prop/index.mjs';\nimport { isElement } from '../../utils/types.mjs';\nimport { isFunction } from '@vue/shared';\nfunction useFocusController(target, {\n  beforeFocus,\n  afterFocus,\n  beforeBlur,\n  afterBlur\n} = {}) {\n  const instance = getCurrentInstance();\n  const {\n    emit\n  } = instance;\n  const wrapperRef = shallowRef();\n  const disabled = useProp(\"disabled\");\n  const isFocused = ref(false);\n  const handleFocus = event => {\n    const cancelFocus = isFunction(beforeFocus) ? beforeFocus(event) : false;\n    if (cancelFocus || isFocused.value) return;\n    isFocused.value = true;\n    emit(\"focus\", event);\n    afterFocus == null ? void 0 : afterFocus();\n  };\n  const handleBlur = event => {\n    var _a;\n    const cancelBlur = isFunction(beforeBlur) ? beforeBlur(event) : false;\n    if (cancelBlur || event.relatedTarget && ((_a = wrapperRef.value) == null ? void 0 : _a.contains(event.relatedTarget))) return;\n    isFocused.value = false;\n    emit(\"blur\", event);\n    afterBlur == null ? void 0 : afterBlur();\n  };\n  const handleClick = () => {\n    var _a, _b;\n    if (((_a = wrapperRef.value) == null ? void 0 : _a.contains(document.activeElement)) && wrapperRef.value !== document.activeElement || disabled.value) return;\n    (_b = target.value) == null ? void 0 : _b.focus();\n  };\n  watch([wrapperRef, disabled], ([el, disabled2]) => {\n    if (!el) return;\n    if (disabled2) {\n      el.removeAttribute(\"tabindex\");\n    } else {\n      el.setAttribute(\"tabindex\", \"-1\");\n    }\n  });\n  useEventListener(wrapperRef, \"focus\", handleFocus, true);\n  useEventListener(wrapperRef, \"blur\", handleBlur, true);\n  useEventListener(wrapperRef, \"click\", handleClick, true);\n  if (process.env.NODE_ENV === \"test\") {\n    onMounted(() => {\n      const targetEl = isElement(target.value) ? target.value : document.querySelector(\"input,textarea\");\n      if (targetEl) {\n        useEventListener(targetEl, \"focus\", handleFocus, true);\n        useEventListener(targetEl, \"blur\", handleBlur, true);\n      }\n    });\n  }\n  return {\n    isFocused,\n    wrapperRef,\n    handleFocus,\n    handleBlur\n  };\n}\nexport { useFocusController };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}