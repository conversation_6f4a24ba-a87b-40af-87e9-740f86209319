{"ast": null, "code": "import { columns } from './common.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst requiredNumberType = {\n  type: Number,\n  required: true\n};\nconst tableV2HeaderProps = buildProps({\n  class: String,\n  columns,\n  fixedHeaderData: {\n    type: definePropType(Array)\n  },\n  headerData: {\n    type: definePropType(Array),\n    required: true\n  },\n  headerHeight: {\n    type: definePropType([Number, Array]),\n    default: 50\n  },\n  rowWidth: requiredNumberType,\n  rowHeight: {\n    type: Number,\n    default: 50\n  },\n  height: requiredNumberType,\n  width: requiredNumberType\n});\nexport { tableV2HeaderProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}