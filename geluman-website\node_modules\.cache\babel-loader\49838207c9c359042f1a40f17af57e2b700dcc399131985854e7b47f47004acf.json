{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { inject, ref, computed, toRaw } from 'vue';\nimport { isEqual } from 'lodash-unified';\nimport { checkboxGroupContextKey } from '../constants.mjs';\nimport { isPropAbsent, isBoolean } from '../../../../utils/types.mjs';\nimport { isArray, isObject } from '@vue/shared';\nimport { useFormSize } from '../../../form/src/hooks/use-form-common-props.mjs';\nconst useCheckboxStatus = (props, slots, {\n  model\n}) => {\n  const checkboxGroup = inject(checkboxGroupContextKey, void 0);\n  const isFocused = ref(false);\n  const actualValue = computed(() => {\n    if (!isPropAbsent(props.value)) {\n      return props.value;\n    }\n    return props.label;\n  });\n  const isChecked = computed(() => {\n    const value = model.value;\n    if (isBoolean(value)) {\n      return value;\n    } else if (isArray(value)) {\n      if (isObject(actualValue.value)) {\n        return value.map(toRaw).some(o => isEqual(o, actualValue.value));\n      } else {\n        return value.map(toRaw).includes(actualValue.value);\n      }\n    } else if (value !== null && value !== void 0) {\n      return value === props.trueValue || value === props.trueLabel;\n    } else {\n      return !!value;\n    }\n  });\n  const checkboxButtonSize = useFormSize(computed(() => {\n    var _a;\n    return (_a = checkboxGroup == null ? void 0 : checkboxGroup.size) == null ? void 0 : _a.value;\n  }), {\n    prop: true\n  });\n  const checkboxSize = useFormSize(computed(() => {\n    var _a;\n    return (_a = checkboxGroup == null ? void 0 : checkboxGroup.size) == null ? void 0 : _a.value;\n  }));\n  const hasOwnLabel = computed(() => {\n    return !!slots.default || !isPropAbsent(actualValue.value);\n  });\n  return {\n    checkboxButtonSize,\n    isChecked,\n    isFocused,\n    checkboxSize,\n    hasOwnLabel,\n    actualValue\n  };\n};\nexport { useCheckboxStatus };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}