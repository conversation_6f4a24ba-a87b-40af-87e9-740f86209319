{"ast": null, "code": "import { isRef, computed, watch, onScopeDispose } from 'vue';\nimport { useNamespace } from '../use-namespace/index.mjs';\nimport { throwError } from '../../utils/error.mjs';\nimport { isClient } from '@vueuse/core';\nimport { hasClass, addClass, getStyle, removeClass } from '../../utils/dom/style.mjs';\nimport { getScrollBarWidth } from '../../utils/dom/scroll.mjs';\nconst useLockscreen = (trigger, options = {}) => {\n  if (!isRef(trigger)) {\n    throwError(\"[useLockscreen]\", \"You need to pass a ref param to this function\");\n  }\n  const ns = options.ns || useNamespace(\"popup\");\n  const hiddenCls = computed(() => ns.bm(\"parent\", \"hidden\"));\n  if (!isClient || hasClass(document.body, hiddenCls.value)) {\n    return;\n  }\n  let scrollBarWidth = 0;\n  let withoutHiddenClass = false;\n  let bodyWidth = \"0\";\n  const cleanup = () => {\n    setTimeout(() => {\n      if (typeof document === \"undefined\") return;\n      if (withoutHiddenClass && document) {\n        document.body.style.width = bodyWidth;\n        removeClass(document.body, hiddenCls.value);\n      }\n    }, 200);\n  };\n  watch(trigger, val => {\n    if (!val) {\n      cleanup();\n      return;\n    }\n    withoutHiddenClass = !hasClass(document.body, hiddenCls.value);\n    if (withoutHiddenClass) {\n      bodyWidth = document.body.style.width;\n      addClass(document.body, hiddenCls.value);\n    }\n    scrollBarWidth = getScrollBarWidth(ns.namespace.value);\n    const bodyHasOverflow = document.documentElement.clientHeight < document.body.scrollHeight;\n    const bodyOverflowY = getStyle(document.body, \"overflowY\");\n    if (scrollBarWidth > 0 && (bodyHasOverflow || bodyOverflowY === \"scroll\") && withoutHiddenClass) {\n      document.body.style.width = `calc(100% - ${scrollBarWidth}px)`;\n    }\n  });\n  onScopeDispose(() => cleanup());\n};\nexport { useLockscreen };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}