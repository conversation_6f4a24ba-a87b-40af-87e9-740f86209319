{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { getCurrentInstance, ref, watchEffect, computed, unref, renderSlot, h, Comment } from 'vue';\nimport { cellForced, defaultRenderCell, treeCellPrefix, getDefaultClassName } from '../config.mjs';\nimport { parseWidth, parseMinWidth } from '../util.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isUndefined } from '../../../../utils/types.mjs';\nimport { debugWarn } from '../../../../utils/error.mjs';\nimport { isArray } from '@vue/shared';\nfunction useRender(props, slots, owner) {\n  const instance = getCurrentInstance();\n  const columnId = ref(\"\");\n  const isSubColumn = ref(false);\n  const realAlign = ref();\n  const realHeaderAlign = ref();\n  const ns = useNamespace(\"table\");\n  watchEffect(() => {\n    realAlign.value = props.align ? `is-${props.align}` : null;\n    realAlign.value;\n  });\n  watchEffect(() => {\n    realHeaderAlign.value = props.headerAlign ? `is-${props.headerAlign}` : realAlign.value;\n    realHeaderAlign.value;\n  });\n  const columnOrTableParent = computed(() => {\n    let parent = instance.vnode.vParent || instance.parent;\n    while (parent && !parent.tableId && !parent.columnId) {\n      parent = parent.vnode.vParent || parent.parent;\n    }\n    return parent;\n  });\n  const hasTreeColumn = computed(() => {\n    const {\n      store\n    } = instance.parent;\n    if (!store) return false;\n    const {\n      treeData\n    } = store.states;\n    const treeDataValue = treeData.value;\n    return treeDataValue && Object.keys(treeDataValue).length > 0;\n  });\n  const realWidth = ref(parseWidth(props.width));\n  const realMinWidth = ref(parseMinWidth(props.minWidth));\n  const setColumnWidth = column => {\n    if (realWidth.value) column.width = realWidth.value;\n    if (realMinWidth.value) {\n      column.minWidth = realMinWidth.value;\n    }\n    if (!realWidth.value && realMinWidth.value) {\n      column.width = void 0;\n    }\n    if (!column.minWidth) {\n      column.minWidth = 80;\n    }\n    column.realWidth = Number(isUndefined(column.width) ? column.minWidth : column.width);\n    return column;\n  };\n  const setColumnForcedProps = column => {\n    const type = column.type;\n    const source = cellForced[type] || {};\n    Object.keys(source).forEach(prop => {\n      const value = source[prop];\n      if (prop !== \"className\" && !isUndefined(value)) {\n        column[prop] = value;\n      }\n    });\n    const className = getDefaultClassName(type);\n    if (className) {\n      const forceClass = `${unref(ns.namespace)}-${className}`;\n      column.className = column.className ? `${column.className} ${forceClass}` : forceClass;\n    }\n    return column;\n  };\n  const checkSubColumn = children => {\n    if (isArray(children)) {\n      children.forEach(child => check(child));\n    } else {\n      check(children);\n    }\n    function check(item) {\n      var _a;\n      if (((_a = item == null ? void 0 : item.type) == null ? void 0 : _a.name) === \"ElTableColumn\") {\n        item.vParent = instance;\n      }\n    }\n  };\n  const setColumnRenders = column => {\n    if (props.renderHeader) {\n      debugWarn(\"TableColumn\", \"Comparing to render-header, scoped-slot header is easier to use. We recommend users to use scoped-slot header.\");\n    } else if (column.type !== \"selection\") {\n      column.renderHeader = scope => {\n        instance.columnConfig.value[\"label\"];\n        return renderSlot(slots, \"header\", scope, () => [column.label]);\n      };\n    }\n    if (slots[\"filter-icon\"]) {\n      column.renderFilterIcon = scope => {\n        return renderSlot(slots, \"filter-icon\", scope);\n      };\n    }\n    let originRenderCell = column.renderCell;\n    if (column.type === \"expand\") {\n      column.renderCell = data => h(\"div\", {\n        class: \"cell\"\n      }, [originRenderCell(data)]);\n      owner.value.renderExpanded = data => {\n        return slots.default ? slots.default(data) : slots.default;\n      };\n    } else {\n      originRenderCell = originRenderCell || defaultRenderCell;\n      column.renderCell = data => {\n        let children = null;\n        if (slots.default) {\n          const vnodes = slots.default(data);\n          children = vnodes.some(v => v.type !== Comment) ? vnodes : originRenderCell(data);\n        } else {\n          children = originRenderCell(data);\n        }\n        const {\n          columns\n        } = owner.value.store.states;\n        const firstUserColumnIndex = columns.value.findIndex(item => item.type === \"default\");\n        const shouldCreatePlaceholder = hasTreeColumn.value && data.cellIndex === firstUserColumnIndex;\n        const prefix = treeCellPrefix(data, shouldCreatePlaceholder);\n        const props2 = {\n          class: \"cell\",\n          style: {}\n        };\n        if (column.showOverflowTooltip) {\n          props2.class = `${props2.class} ${unref(ns.namespace)}-tooltip`;\n          props2.style = {\n            width: `${(data.column.realWidth || Number(data.column.width)) - 1}px`\n          };\n        }\n        checkSubColumn(children);\n        return h(\"div\", props2, [prefix, children]);\n      };\n    }\n    return column;\n  };\n  const getPropsData = (...propsKey) => {\n    return propsKey.reduce((prev, cur) => {\n      if (isArray(cur)) {\n        cur.forEach(key => {\n          prev[key] = props[key];\n        });\n      }\n      return prev;\n    }, {});\n  };\n  const getColumnElIndex = (children, child) => {\n    return Array.prototype.indexOf.call(children, child);\n  };\n  const updateColumnOrder = () => {\n    owner.value.store.commit(\"updateColumnOrder\", instance.columnConfig.value);\n  };\n  return {\n    columnId,\n    realAlign,\n    isSubColumn,\n    realHeaderAlign,\n    columnOrTableParent,\n    setColumnWidth,\n    setColumnForcedProps,\n    setColumnRenders,\n    getPropsData,\n    getColumnElIndex,\n    updateColumnOrder\n  };\n}\nexport { useRender as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}