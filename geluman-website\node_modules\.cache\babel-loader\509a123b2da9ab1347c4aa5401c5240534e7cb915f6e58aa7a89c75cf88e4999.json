{"ast": null, "code": "import { defineComponent, inject, h } from 'vue';\nimport { ElText } from '../../text/index.mjs';\nimport { treeNodeContentProps, ROOT_TREE_INJECTION_KEY } from './virtual-tree.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nvar ElNodeContent = defineComponent({\n  name: \"ElTreeNodeContent\",\n  props: treeNodeContentProps,\n  setup(props) {\n    const tree = inject(ROOT_TREE_INJECTION_KEY);\n    const ns = useNamespace(\"tree\");\n    return () => {\n      const node = props.node;\n      const {\n        data\n      } = node;\n      return (tree == null ? void 0 : tree.ctx.slots.default) ? tree.ctx.slots.default({\n        node,\n        data\n      }) : h(ElText, {\n        tag: \"span\",\n        truncated: true,\n        class: ns.be(\"node\", \"label\")\n      }, () => [node == null ? void 0 : node.label]);\n    };\n  }\n});\nexport { ElNodeContent as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}