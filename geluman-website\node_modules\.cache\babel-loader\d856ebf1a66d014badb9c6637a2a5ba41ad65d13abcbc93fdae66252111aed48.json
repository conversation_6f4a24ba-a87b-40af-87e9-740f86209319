{"ast": null, "code": "import { tourContentProps } from './content2.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nimport { teleportProps } from '../../teleport/src/teleport.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { isBoolean, isNumber } from '../../../utils/types.mjs';\nconst tourProps = buildProps({\n  modelValue: Boolean,\n  current: {\n    type: Number,\n    default: 0\n  },\n  showArrow: {\n    type: Boolean,\n    default: true\n  },\n  showClose: {\n    type: Boolean,\n    default: true\n  },\n  closeIcon: {\n    type: iconPropType\n  },\n  placement: tourContentProps.placement,\n  contentStyle: {\n    type: definePropType([Object])\n  },\n  mask: {\n    type: definePropType([<PERSON>olean, Object]),\n    default: true\n  },\n  gap: {\n    type: definePropType(Object),\n    default: () => ({\n      offset: 6,\n      radius: 2\n    })\n  },\n  zIndex: {\n    type: Number\n  },\n  scrollIntoViewOptions: {\n    type: definePropType([Boolean, Object]),\n    default: () => ({\n      block: \"center\"\n    })\n  },\n  type: {\n    type: definePropType(String)\n  },\n  appendTo: {\n    type: teleportProps.to.type,\n    default: \"body\"\n  },\n  closeOnPressEscape: {\n    type: Boolean,\n    default: true\n  },\n  targetAreaClickable: {\n    type: Boolean,\n    default: true\n  }\n});\nconst tourEmits = {\n  [UPDATE_MODEL_EVENT]: value => isBoolean(value),\n  [\"update:current\"]: current => isNumber(current),\n  close: current => isNumber(current),\n  finish: () => true,\n  change: current => isNumber(current)\n};\nexport { tourEmits, tourProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}