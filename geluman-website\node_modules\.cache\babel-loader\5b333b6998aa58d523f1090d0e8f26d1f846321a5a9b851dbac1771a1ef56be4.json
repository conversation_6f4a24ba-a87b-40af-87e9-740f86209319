{"ast": null, "code": "import { ref, getCurrentInstance, inject, computed, unref } from 'vue';\nimport { isNumber } from '../../utils/types.mjs';\nimport { isClient } from '@vueuse/core';\nimport { debugWarn } from '../../utils/error.mjs';\nconst initial = {\n  current: 0\n};\nconst zIndex = ref(0);\nconst defaultInitialZIndex = 2e3;\nconst ZINDEX_INJECTION_KEY = Symbol(\"elZIndexContextKey\");\nconst zIndexContextKey = Symbol(\"zIndexContextKey\");\nconst useZIndex = zIndexOverrides => {\n  const increasingInjection = getCurrentInstance() ? inject(ZINDEX_INJECTION_KEY, initial) : initial;\n  const zIndexInjection = zIndexOverrides || (getCurrentInstance() ? inject(zIndexContextKey, void 0) : void 0);\n  const initialZIndex = computed(() => {\n    const zIndexFromInjection = unref(zIndexInjection);\n    return isNumber(zIndexFromInjection) ? zIndexFromInjection : defaultInitialZIndex;\n  });\n  const currentZIndex = computed(() => initialZIndex.value + zIndex.value);\n  const nextZIndex = () => {\n    increasingInjection.current++;\n    zIndex.value = increasingInjection.current;\n    return currentZIndex.value;\n  };\n  if (!isClient && !inject(ZINDEX_INJECTION_KEY)) {\n    debugWarn(\"ZIndexInjection\", `Looks like you are using server rendering, you must provide a z-index provider to ensure the hydration process to be succeed\nusage: app.provide(ZINDEX_INJECTION_KEY, { current: 0 })`);\n  }\n  return {\n    initialZIndex,\n    currentZIndex,\n    nextZIndex\n  };\n};\nexport { ZINDEX_INJECTION_KEY, defaultInitialZIndex, useZIndex, zIndexContextKey };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}