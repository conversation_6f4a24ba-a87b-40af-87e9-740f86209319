{"ast": null, "code": "import { triggerEvent } from '../../../../utils/dom/aria.mjs';\nimport { EVENT_CODE } from '../../../../constants/aria.mjs';\nclass SubMenu {\n  constructor(parent, domNode) {\n    this.parent = parent;\n    this.domNode = domNode;\n    this.subIndex = 0;\n    this.subIndex = 0;\n    this.init();\n  }\n  init() {\n    this.subMenuItems = this.domNode.querySelectorAll(\"li\");\n    this.addListeners();\n  }\n  gotoSubIndex(idx) {\n    if (idx === this.subMenuItems.length) {\n      idx = 0;\n    } else if (idx < 0) {\n      idx = this.subMenuItems.length - 1;\n    }\n    this.subMenuItems[idx].focus();\n    this.subIndex = idx;\n  }\n  addListeners() {\n    const parentNode = this.parent.domNode;\n    Array.prototype.forEach.call(this.subMenuItems, el => {\n      el.addEventListener(\"keydown\", event => {\n        let prevDef = false;\n        switch (event.code) {\n          case EVENT_CODE.down:\n            {\n              this.gotoSubIndex(this.subIndex + 1);\n              prevDef = true;\n              break;\n            }\n          case EVENT_CODE.up:\n            {\n              this.gotoSubIndex(this.subIndex - 1);\n              prevDef = true;\n              break;\n            }\n          case EVENT_CODE.tab:\n            {\n              triggerEvent(parentNode, \"mouseleave\");\n              break;\n            }\n          case EVENT_CODE.enter:\n          case EVENT_CODE.numpadEnter:\n          case EVENT_CODE.space:\n            {\n              prevDef = true;\n              event.currentTarget.click();\n              break;\n            }\n        }\n        if (prevDef) {\n          event.preventDefault();\n          event.stopPropagation();\n        }\n        return false;\n      });\n    });\n  }\n}\nexport { SubMenu as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}