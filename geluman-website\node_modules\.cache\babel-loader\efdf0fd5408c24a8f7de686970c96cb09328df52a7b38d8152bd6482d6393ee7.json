{"ast": null, "code": "import { defineComponent, reactive, computed, toRefs, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, createVNode, withCtx, createElementVNode, toDisplayString } from 'vue';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { sliderButtonProps, sliderButtonEmits } from './button.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useSliderButton } from './composables/use-slider-button.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElSliderButton\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: sliderButtonProps,\n  emits: sliderButtonEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"slider\");\n    const initData = reactive({\n      hovering: false,\n      dragging: false,\n      isClick: false,\n      startX: 0,\n      currentX: 0,\n      startY: 0,\n      currentY: 0,\n      startPosition: 0,\n      newPosition: 0,\n      oldValue: props.modelValue\n    });\n    const tooltipPersistent = computed(() => !showTooltip.value ? false : persistent.value);\n    const {\n      disabled,\n      button,\n      tooltip,\n      showTooltip,\n      persistent,\n      tooltipVisible,\n      wrapperStyle,\n      formatValue,\n      handleMouseEnter,\n      handleMouseLeave,\n      onButtonDown,\n      onKeyDown,\n      setPosition\n    } = useSliderButton(props, initData, emit);\n    const {\n      hovering,\n      dragging\n    } = toRefs(initData);\n    expose({\n      onButtonDown,\n      onKeyDown,\n      setPosition,\n      hovering,\n      dragging\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"button\",\n        ref: button,\n        class: normalizeClass([unref(ns).e(\"button-wrapper\"), {\n          hover: unref(hovering),\n          dragging: unref(dragging)\n        }]),\n        style: normalizeStyle(unref(wrapperStyle)),\n        tabindex: unref(disabled) ? -1 : 0,\n        onMouseenter: unref(handleMouseEnter),\n        onMouseleave: unref(handleMouseLeave),\n        onMousedown: unref(onButtonDown),\n        onFocus: unref(handleMouseEnter),\n        onBlur: unref(handleMouseLeave),\n        onKeydown: unref(onKeyDown)\n      }, [createVNode(unref(ElTooltip), {\n        ref_key: \"tooltip\",\n        ref: tooltip,\n        visible: unref(tooltipVisible),\n        placement: _ctx.placement,\n        \"fallback-placements\": [\"top\", \"bottom\", \"right\", \"left\"],\n        \"stop-popper-mouse-event\": false,\n        \"popper-class\": _ctx.tooltipClass,\n        disabled: !unref(showTooltip),\n        persistent: unref(tooltipPersistent)\n      }, {\n        content: withCtx(() => [createElementVNode(\"span\", null, toDisplayString(unref(formatValue)), 1)]),\n        default: withCtx(() => [createElementVNode(\"div\", {\n          class: normalizeClass([unref(ns).e(\"button\"), {\n            hover: unref(hovering),\n            dragging: unref(dragging)\n          }])\n        }, null, 2)]),\n        _: 1\n      }, 8, [\"visible\", \"placement\", \"popper-class\", \"disabled\", \"persistent\"])], 46, [\"tabindex\", \"onMouseenter\", \"onMouseleave\", \"onMousedown\", \"onFocus\", \"onBlur\", \"onKeydown\"]);\n    };\n  }\n});\nvar SliderButton = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"button.vue\"]]);\nexport { SliderButton as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}