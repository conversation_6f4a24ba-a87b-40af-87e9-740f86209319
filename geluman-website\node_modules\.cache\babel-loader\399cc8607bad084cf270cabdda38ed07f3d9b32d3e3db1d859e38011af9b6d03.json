{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { ref, onMounted, onUnmounted } from 'vue';\nfunction useKeyRender(table) {\n  const observer = ref();\n  const initWatchDom = () => {\n    const el = table.vnode.el;\n    const columnsWrapper = el.querySelector(\".hidden-columns\");\n    const config = {\n      childList: true,\n      subtree: true\n    };\n    const updateOrderFns = table.store.states.updateOrderFns;\n    observer.value = new MutationObserver(() => {\n      updateOrderFns.forEach(fn => fn());\n    });\n    observer.value.observe(columnsWrapper, config);\n  };\n  onMounted(() => {\n    initWatchDom();\n  });\n  onUnmounted(() => {\n    var _a;\n    (_a = observer.value) == null ? void 0 : _a.disconnect();\n  });\n}\nexport { use<PERSON>eyRender as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}