{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst progressProps = buildProps({\n  type: {\n    type: String,\n    default: \"line\",\n    values: [\"line\", \"circle\", \"dashboard\"]\n  },\n  percentage: {\n    type: Number,\n    default: 0,\n    validator: val => val >= 0 && val <= 100\n  },\n  status: {\n    type: String,\n    default: \"\",\n    values: [\"\", \"success\", \"exception\", \"warning\"]\n  },\n  indeterminate: Boolean,\n  duration: {\n    type: Number,\n    default: 3\n  },\n  strokeWidth: {\n    type: Number,\n    default: 6\n  },\n  strokeLinecap: {\n    type: definePropType(String),\n    default: \"round\"\n  },\n  textInside: Boolean,\n  width: {\n    type: Number,\n    default: 126\n  },\n  showText: {\n    type: Boolean,\n    default: true\n  },\n  color: {\n    type: definePropType([String, Array, Function]),\n    default: \"\"\n  },\n  striped: Boolean,\n  stripedFlow: Boolean,\n  format: {\n    type: definePropType(Function),\n    default: percentage => `${percentage}%`\n  }\n});\nexport { progressProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}