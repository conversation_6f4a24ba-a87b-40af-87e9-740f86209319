{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { useI18n } from \"vue-i18n\";\nimport { useRouter } from \"vue-router\";\nimport { highlight, eyeshield, clock } from \"@/assets\";\nimport { HomeFilled, ArrowRight, Files, Brush, Share, Monitor, MagicStick, Setting, ChatLineRound, Scissors, Connection } from \"@element-plus/icons-vue\";\nconst __default__ = {\n  name: \"ProductsPage\"\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const {\n      t\n    } = useI18n();\n    const router = useRouter();\n    const navigateToProduct = path => {\n      router.push(path);\n    };\n    const __returned__ = {\n      t,\n      router,\n      navigateToProduct,\n      get useI18n() {\n        return useI18n;\n      },\n      get useRouter() {\n        return useRouter;\n      },\n      get highlight() {\n        return highlight;\n      },\n      get eyeshield() {\n        return eyeshield;\n      },\n      get clock() {\n        return clock;\n      },\n      get HomeFilled() {\n        return HomeFilled;\n      },\n      get ArrowRight() {\n        return ArrowRight;\n      },\n      get Files() {\n        return Files;\n      },\n      get Brush() {\n        return Brush;\n      },\n      get Share() {\n        return Share;\n      },\n      get Monitor() {\n        return Monitor;\n      },\n      get MagicStick() {\n        return MagicStick;\n      },\n      get Setting() {\n        return Setting;\n      },\n      get ChatLineRound() {\n        return ChatLineRound;\n      },\n      get Scissors() {\n        return Scissors;\n      },\n      get Connection() {\n        return Connection;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["useI18n", "useRouter", "highlight", "eyeshield", "clock", "HomeFilled", "ArrowRight", "Files", "Brush", "Share", "Monitor", "MagicStick", "Setting", "ChatLineRound", "Scissors", "Connection", "__default__", "name", "t", "router", "navigateToProduct", "path", "push"], "sources": ["D:/codes/glmwebsite/geluman-website/src/views/Products.vue"], "sourcesContent": ["<template>\r\n  <div class=\"products-page\">\r\n    <section class=\"products-hero\">\r\n      <div class=\"hero-content\">\r\n        <h1 class=\"title\">创新产品</h1>\r\n        <p class=\"subtitle\">打造极致用户体验的数字化工具</p>\r\n      </div>\r\n      <div class=\"hero-background\">\r\n        <div class=\"floating-circle\"></div>\r\n        <div class=\"floating-dots\"></div>\r\n      </div>\r\n    </section>\r\n\r\n    <section class=\"products-grid\">\r\n      <div class=\"container\">\r\n        <!-- 智能高亮助手 -->\r\n        <div class=\"product-card\">\r\n          <div class=\"product-badge\">Browser Extension</div>\r\n          <div class=\"product-header\">\r\n            <h2>智能高亮助手</h2>\r\n            <p class=\"description\">\r\n              专业的网页文本智能高亮工具，让阅读和学习更高效。支持多分类管理、\r\n              颜色自定义、关键词管理和配置导出分享。\r\n            </p>\r\n          </div>\r\n          <div class=\"features\">\r\n            <div class=\"feature\">\r\n              <el-icon><Files /></el-icon>\r\n              <span>多分类高亮</span>\r\n            </div>\r\n            <div class=\"feature\">\r\n              <el-icon><Brush /></el-icon>\r\n              <span>颜色自定义</span>\r\n            </div>\r\n            <div class=\"feature\">\r\n              <el-icon><Share /></el-icon>\r\n              <span>配置分享</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"actions\">\r\n            <router-link to=\"/products/highlight\" class=\"learn-more\">\r\n              了解更多\r\n              <el-icon class=\"icon-right\"><ArrowRight /></el-icon>\r\n            </router-link>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 网页护眼助手 -->\r\n        <div class=\"product-card\">\r\n          <div class=\"product-badge\">Browser Extension</div>\r\n          <div class=\"product-header\">\r\n            <h2>网页护眼助手</h2>\r\n            <p class=\"description\">\r\n              智能护眼工具，让网页浏览更舒适，保护您的眼睛健康。\r\n              通过智能算法，自动调节屏幕显示效果。\r\n            </p>\r\n          </div>\r\n          <div class=\"features\">\r\n            <div class=\"feature\">\r\n              <el-icon><Monitor /></el-icon>\r\n              <span>智能护眼模式</span>\r\n            </div>\r\n            <div class=\"feature\">\r\n              <el-icon><MagicStick /></el-icon>\r\n              <span>场景自适应</span>\r\n            </div>\r\n            <div class=\"feature\">\r\n              <el-icon><Setting /></el-icon>\r\n              <span>全局控制</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"actions\">\r\n            <router-link to=\"/products/eyeshield\" class=\"learn-more\">\r\n              了解更多\r\n              <el-icon class=\"icon-right\"><ArrowRight /></el-icon>\r\n            </router-link>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 智能翻译助手 -->\r\n        <div class=\"product-card\">\r\n          <div class=\"product-badge\">Browser Extension</div>\r\n          <div class=\"product-header\">\r\n            <h2>智能翻译助手</h2>\r\n            <p class=\"description\">\r\n              高效、准确的网页文本翻译工具，帮助您跨越语言障碍。\r\n              支持多语言翻译、划词翻译、智能识别等功能。\r\n            </p>\r\n          </div>\r\n          <div class=\"features\">\r\n            <div class=\"feature\">\r\n              <el-icon><ChatLineRound /></el-icon>\r\n              <span>多语言支持</span>\r\n            </div>\r\n            <div class=\"feature\">\r\n              <el-icon><Scissors /></el-icon>\r\n              <span>划词翻译</span>\r\n            </div>\r\n            <div class=\"feature\">\r\n              <el-icon><Connection /></el-icon>\r\n              <span>实时翻译</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"actions\">\r\n            <router-link to=\"/products/translator\" class=\"learn-more\">\r\n              了解更多\r\n              <el-icon class=\"icon-right\"><ArrowRight /></el-icon>\r\n            </router-link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { useI18n } from \"vue-i18n\";\r\nimport { useRouter } from \"vue-router\";\r\nimport { highlight, eyeshield, clock } from \"@/assets\";\r\nimport {\r\n  HomeFilled,\r\n  ArrowRight,\r\n  Files,\r\n  Brush,\r\n  Share,\r\n  Monitor,\r\n  MagicStick,\r\n  Setting,\r\n  ChatLineRound,\r\n  Scissors,\r\n  Connection,\r\n} from \"@element-plus/icons-vue\";\r\n\r\nconst { t } = useI18n();\r\nconst router = useRouter();\r\n\r\nconst navigateToProduct = (path) => {\r\n  router.push(path);\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.products-page {\r\n  padding-top: var(--header-height);\r\n}\r\n\r\n.products-hero {\r\n  background: linear-gradient(\r\n    135deg,\r\n    var(--primary-color) 0%,\r\n    var(--primary-dark) 100%\r\n  );\r\n  padding: 4rem 0;\r\n  text-align: center;\r\n  color: white;\r\n\r\n  .title {\r\n    font-size: 2.5rem;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 1.2rem;\r\n    opacity: 0.9;\r\n  }\r\n}\r\n\r\n.products-grid {\r\n  padding: 4rem 0;\r\n  background: var(--bg-secondary);\r\n\r\n  .container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 0 2rem;\r\n    display: grid;\r\n    gap: 3rem;\r\n  }\r\n}\r\n\r\n.product-card {\r\n  background: white;\r\n  border-radius: 20px;\r\n  padding: 2rem;\r\n  box-shadow: var(--shadow-sm);\r\n  transition: var(--transition-base);\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2rem;\r\n\r\n  &:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: var(--shadow-lg);\r\n  }\r\n\r\n  .product-badge {\r\n    display: inline-block;\r\n    padding: 0.5rem 1rem;\r\n    background: var(--bg-accent);\r\n    color: var(--primary-color);\r\n    border-radius: 20px;\r\n    font-size: 0.9rem;\r\n    width: fit-content;\r\n  }\r\n\r\n  .product-header {\r\n    h2 {\r\n      font-size: 1.8rem;\r\n      margin-bottom: 1rem;\r\n      color: var(--text-primary);\r\n    }\r\n\r\n    .description {\r\n      color: var(--text-secondary);\r\n      line-height: 1.6;\r\n    }\r\n  }\r\n\r\n  .features {\r\n    display: grid;\r\n    grid-template-columns: repeat(3, 1fr);\r\n    gap: 1.5rem;\r\n\r\n    .feature {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 0.8rem;\r\n      color: var(--text-secondary);\r\n\r\n      .el-icon {\r\n        color: var(--primary-color);\r\n        font-size: 1.2rem;\r\n      }\r\n    }\r\n  }\r\n\r\n  .actions {\r\n    margin-top: auto;\r\n    display: flex;\r\n    justify-content: flex-end;\r\n\r\n    .learn-more {\r\n      display: inline-flex;\r\n      align-items: center;\r\n      gap: 0.5rem;\r\n      color: var(--primary-color);\r\n      text-decoration: none;\r\n      font-weight: 500;\r\n      padding: 0.5rem 1rem;\r\n      border-radius: 20px;\r\n      transition: all 0.3s ease;\r\n      position: relative;\r\n      z-index: 10;\r\n      pointer-events: auto;\r\n\r\n      .icon-right {\r\n        transition: transform 0.3s ease;\r\n      }\r\n\r\n      &:hover {\r\n        background: var(--bg-accent);\r\n\r\n        .icon-right {\r\n          transform: translateX(4px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .products-hero {\r\n    padding: 3rem 1rem;\r\n  }\r\n\r\n  .products-grid {\r\n    padding: 2rem 1rem;\r\n\r\n    .product-card {\r\n      .features {\r\n        grid-template-columns: 1fr;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.hero-background {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: hidden;\r\n  z-index: 0;\r\n\r\n  .floating-circle {\r\n    position: absolute;\r\n    width: 300px;\r\n    height: 300px;\r\n    border-radius: 50%;\r\n    background: linear-gradient(\r\n      45deg,\r\n      rgba(255, 255, 255, 0.1),\r\n      rgba(255, 255, 255, 0.05)\r\n    );\r\n    animation: floatCircle 20s infinite linear;\r\n  }\r\n\r\n  .floating-dots {\r\n    position: absolute;\r\n    width: 100%;\r\n    height: 100%;\r\n    background-image: radial-gradient(\r\n      rgba(255, 255, 255, 0.1) 1px,\r\n      transparent 1px\r\n    );\r\n    background-size: 30px 30px;\r\n    animation: floatDots 40s infinite linear;\r\n  }\r\n}\r\n\r\n.product-card {\r\n  .features .feature {\r\n    .el-icon {\r\n      transition: transform 0.3s ease;\r\n    }\r\n\r\n    &:hover .el-icon {\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes floatCircle {\r\n  0% {\r\n    transform: translate(-50%, -50%) rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: translate(-50%, -50%) rotate(360deg);\r\n  }\r\n}\r\n\r\n@keyframes floatDots {\r\n  0% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(-20px);\r\n  }\r\n  100% {\r\n    transform: translateY(0);\r\n  }\r\n}\r\n</style>\r\n\r\n<script>\r\nexport default {\r\n  name: \"ProductsPage\",\r\n};\r\n</script>\r\n"], "mappings": ";AAoHA,SAASA,OAAO,QAAQ,UAAU;AAClC,SAASC,SAAS,QAAQ,YAAY;AACtC,SAASC,SAAS,EAAEC,SAAS,EAAEC,KAAK,QAAQ,UAAU;AACtD,SACEC,UAAU,EACVC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,aAAa,EACbC,QAAQ,EACRC,UAAU,QACL,yBAAyB;AAiOhC,MAAAC,WAAA,GAAe;EACbC,IAAI,EAAE;AACR,CAAC;;;;;;IAjOD,MAAM;MAAEC;IAAE,CAAC,GAAGlB,OAAO,CAAC,CAAC;IACvB,MAAMmB,MAAM,GAAGlB,SAAS,CAAC,CAAC;IAE1B,MAAMmB,iBAAiB,GAAIC,IAAI,IAAK;MAClCF,MAAM,CAACG,IAAI,CAACD,IAAI,CAAC;IACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}