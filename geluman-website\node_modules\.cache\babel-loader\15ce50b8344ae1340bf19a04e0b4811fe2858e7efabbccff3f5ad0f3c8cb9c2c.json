{"ast": null, "code": "import Scrollbar from './src/scrollbar2.mjs';\nexport { BAR_MAP, GAP, renderThumbStyle } from './src/util.mjs';\nexport { scrollbarEmits, scrollbarProps } from './src/scrollbar.mjs';\nexport { thumbProps } from './src/thumb.mjs';\nexport { scrollbarContextKey } from './src/constants.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElScrollbar = withInstall(Scrollbar);\nexport { ElScrollbar, ElScrollbar as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}