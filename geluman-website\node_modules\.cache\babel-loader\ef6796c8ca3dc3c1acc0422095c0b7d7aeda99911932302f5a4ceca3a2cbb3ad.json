{"ast": null, "code": "import TableV2 from './src/table-v2.mjs';\nexport { default as TableV2 } from './src/table-v2.mjs';\nimport AutoResizer from './src/components/auto-resizer.mjs';\nexport { Alignment as TableV2Alignment, FixedDir as TableV2FixedDir, SortOrder as TableV2SortOrder } from './src/constants.mjs';\nexport { autoResizerProps } from './src/auto-resizer.mjs';\nexport { placeholderSign as TableV2Placeholder } from './src/private.mjs';\nexport { tableV2Props } from './src/table.mjs';\nexport { tableV2RowProps } from './src/row.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElTableV2 = withInstall(TableV2);\nconst ElAutoResizer = withInstall(AutoResizer);\nexport { ElAutoResizer, ElTableV2 };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}