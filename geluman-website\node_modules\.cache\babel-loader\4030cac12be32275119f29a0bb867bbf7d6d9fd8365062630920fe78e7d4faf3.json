{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst dividerProps = buildProps({\n  direction: {\n    type: String,\n    values: [\"horizontal\", \"vertical\"],\n    default: \"horizontal\"\n  },\n  contentPosition: {\n    type: String,\n    values: [\"left\", \"center\", \"right\"],\n    default: \"center\"\n  },\n  borderStyle: {\n    type: definePropType(String),\n    default: \"solid\"\n  }\n});\nexport { dividerProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}