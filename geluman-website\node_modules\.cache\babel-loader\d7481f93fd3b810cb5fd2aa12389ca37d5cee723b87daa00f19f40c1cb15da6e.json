{"ast": null, "code": "import { computed } from 'vue';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { NOOP } from '@vue/shared';\nconst CommonProps = buildProps({\n  modelValue: {\n    type: definePropType([Number, String, Array])\n  },\n  options: {\n    type: definePropType(Array),\n    default: () => []\n  },\n  props: {\n    type: definePropType(Object),\n    default: () => ({})\n  }\n});\nconst DefaultProps = {\n  expandTrigger: \"click\",\n  multiple: false,\n  checkStrictly: false,\n  emitPath: true,\n  lazy: false,\n  lazyLoad: NOOP,\n  value: \"value\",\n  label: \"label\",\n  children: \"children\",\n  leaf: \"leaf\",\n  disabled: \"disabled\",\n  hoverThreshold: 500\n};\nconst useCascaderConfig = props => {\n  return computed(() => ({\n    ...DefaultProps,\n    ...props.props\n  }));\n};\nexport { CommonProps, DefaultProps, useCascaderConfig };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}