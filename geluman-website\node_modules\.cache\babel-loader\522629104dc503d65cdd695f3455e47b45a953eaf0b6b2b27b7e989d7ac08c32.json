{"ast": null, "code": "const GAP = 4;\nconst BAR_MAP = {\n  vertical: {\n    offset: \"offsetHeight\",\n    scroll: \"scrollTop\",\n    scrollSize: \"scrollHeight\",\n    size: \"height\",\n    key: \"vertical\",\n    axis: \"Y\",\n    client: \"clientY\",\n    direction: \"top\"\n  },\n  horizontal: {\n    offset: \"offsetWidth\",\n    scroll: \"scrollLeft\",\n    scrollSize: \"scrollWidth\",\n    size: \"width\",\n    key: \"horizontal\",\n    axis: \"X\",\n    client: \"clientX\",\n    direction: \"left\"\n  }\n};\nconst renderThumbStyle = ({\n  move,\n  size,\n  bar\n}) => ({\n  [bar.size]: size,\n  transform: `translate${bar.axis}(${move}%)`\n});\nexport { BAR_MAP, GAP, renderThumbStyle };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}