{"ast": null, "code": "import { defineComponent, ref, watch, toRef, computed, inject, openBlock, createElementBlock, normalizeStyle, unref, normalizeClass, createVNode, withCtx, renderSlot, createCommentVNode } from 'vue';\nimport ElFocusTrap from '../../focus-trap/src/focus-trap.mjs';\nimport { tourContentProps, tourContentEmits } from './content2.mjs';\nimport { useFloating, tourKey } from './helper.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTourContent\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: tourContentProps,\n  emits: tourContentEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const placement = ref(props.placement);\n    const strategy = ref(props.strategy);\n    const contentRef = ref(null);\n    const arrowRef = ref(null);\n    watch(() => props.placement, () => {\n      placement.value = props.placement;\n    });\n    const {\n      contentStyle,\n      arrowStyle\n    } = useFloating(toRef(props, \"reference\"), contentRef, arrowRef, placement, strategy, toRef(props, \"offset\"), toRef(props, \"zIndex\"), toRef(props, \"showArrow\"));\n    const side = computed(() => {\n      return placement.value.split(\"-\")[0];\n    });\n    const {\n      ns\n    } = inject(tourKey);\n    const onCloseRequested = () => {\n      emit(\"close\");\n    };\n    const onFocusoutPrevented = event => {\n      if (event.detail.focusReason === \"pointer\") {\n        event.preventDefault();\n      }\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"contentRef\",\n        ref: contentRef,\n        style: normalizeStyle(unref(contentStyle)),\n        class: normalizeClass(unref(ns).e(\"content\")),\n        \"data-side\": unref(side),\n        tabindex: \"-1\"\n      }, [createVNode(unref(ElFocusTrap), {\n        loop: \"\",\n        trapped: \"\",\n        \"focus-start-el\": \"container\",\n        \"focus-trap-el\": contentRef.value || void 0,\n        onReleaseRequested: onCloseRequested,\n        onFocusoutPrevented\n      }, {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 8, [\"focus-trap-el\"]), _ctx.showArrow ? (openBlock(), createElementBlock(\"span\", {\n        key: 0,\n        ref_key: \"arrowRef\",\n        ref: arrowRef,\n        style: normalizeStyle(unref(arrowStyle)),\n        class: normalizeClass(unref(ns).e(\"arrow\"))\n      }, null, 6)) : createCommentVNode(\"v-if\", true)], 14, [\"data-side\"]);\n    };\n  }\n});\nvar ElTourContent = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"content.vue\"]]);\nexport { ElTourContent as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}