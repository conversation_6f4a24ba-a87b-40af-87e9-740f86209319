{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { defineComponent, useSlots, inject, ref, computed, watch, reactive, toRefs, provide, onMounted, onBeforeUnmount, openBlock, createElementBlock, normalizeClass, unref, createVNode, withCtx, createBlock, resolveDynamicComponent, normalizeStyle, renderSlot, createTextVNode, toDisplayString, createCommentVNode, createElementVNode, TransitionGroup, nextTick } from 'vue';\nimport AsyncValidator from 'async-validator';\nimport { castArray, clone } from 'lodash-unified';\nimport { refDebounced } from '@vueuse/core';\nimport { formItemProps } from './form-item.mjs';\nimport FormLabelWrap from './form-label-wrap.mjs';\nimport { formContextKey, formItemContextKey } from './constants.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { getProp } from '../../../utils/objects.mjs';\nimport { useFormSize } from './hooks/use-form-common-props.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nimport { isBoolean } from '../../../utils/types.mjs';\nimport { isString, isFunction, isArray } from '@vue/shared';\nconst __default__ = defineComponent({\n  name: \"ElFormItem\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: formItemProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const slots = useSlots();\n    const formContext = inject(formContextKey, void 0);\n    const parentFormItemContext = inject(formItemContextKey, void 0);\n    const _size = useFormSize(void 0, {\n      formItem: false\n    });\n    const ns = useNamespace(\"form-item\");\n    const labelId = useId().value;\n    const inputIds = ref([]);\n    const validateState = ref(\"\");\n    const validateStateDebounced = refDebounced(validateState, 100);\n    const validateMessage = ref(\"\");\n    const formItemRef = ref();\n    let initialValue = void 0;\n    let isResettingField = false;\n    const labelPosition = computed(() => props.labelPosition || (formContext == null ? void 0 : formContext.labelPosition));\n    const labelStyle = computed(() => {\n      if (labelPosition.value === \"top\") {\n        return {};\n      }\n      const labelWidth = addUnit(props.labelWidth || (formContext == null ? void 0 : formContext.labelWidth) || \"\");\n      if (labelWidth) return {\n        width: labelWidth\n      };\n      return {};\n    });\n    const contentStyle = computed(() => {\n      if (labelPosition.value === \"top\" || (formContext == null ? void 0 : formContext.inline)) {\n        return {};\n      }\n      if (!props.label && !props.labelWidth && isNested) {\n        return {};\n      }\n      const labelWidth = addUnit(props.labelWidth || (formContext == null ? void 0 : formContext.labelWidth) || \"\");\n      if (!props.label && !slots.label) {\n        return {\n          marginLeft: labelWidth\n        };\n      }\n      return {};\n    });\n    const formItemClasses = computed(() => [ns.b(), ns.m(_size.value), ns.is(\"error\", validateState.value === \"error\"), ns.is(\"validating\", validateState.value === \"validating\"), ns.is(\"success\", validateState.value === \"success\"), ns.is(\"required\", isRequired.value || props.required), ns.is(\"no-asterisk\", formContext == null ? void 0 : formContext.hideRequiredAsterisk), (formContext == null ? void 0 : formContext.requireAsteriskPosition) === \"right\" ? \"asterisk-right\" : \"asterisk-left\", {\n      [ns.m(\"feedback\")]: formContext == null ? void 0 : formContext.statusIcon,\n      [ns.m(`label-${labelPosition.value}`)]: labelPosition.value\n    }]);\n    const _inlineMessage = computed(() => isBoolean(props.inlineMessage) ? props.inlineMessage : (formContext == null ? void 0 : formContext.inlineMessage) || false);\n    const validateClasses = computed(() => [ns.e(\"error\"), {\n      [ns.em(\"error\", \"inline\")]: _inlineMessage.value\n    }]);\n    const propString = computed(() => {\n      if (!props.prop) return \"\";\n      return isString(props.prop) ? props.prop : props.prop.join(\".\");\n    });\n    const hasLabel = computed(() => {\n      return !!(props.label || slots.label);\n    });\n    const labelFor = computed(() => {\n      return props.for || (inputIds.value.length === 1 ? inputIds.value[0] : void 0);\n    });\n    const isGroup = computed(() => {\n      return !labelFor.value && hasLabel.value;\n    });\n    const isNested = !!parentFormItemContext;\n    const fieldValue = computed(() => {\n      const model = formContext == null ? void 0 : formContext.model;\n      if (!model || !props.prop) {\n        return;\n      }\n      return getProp(model, props.prop).value;\n    });\n    const normalizedRules = computed(() => {\n      const {\n        required\n      } = props;\n      const rules = [];\n      if (props.rules) {\n        rules.push(...castArray(props.rules));\n      }\n      const formRules = formContext == null ? void 0 : formContext.rules;\n      if (formRules && props.prop) {\n        const _rules = getProp(formRules, props.prop).value;\n        if (_rules) {\n          rules.push(...castArray(_rules));\n        }\n      }\n      if (required !== void 0) {\n        const requiredRules = rules.map((rule, i) => [rule, i]).filter(([rule]) => Object.keys(rule).includes(\"required\"));\n        if (requiredRules.length > 0) {\n          for (const [rule, i] of requiredRules) {\n            if (rule.required === required) continue;\n            rules[i] = {\n              ...rule,\n              required\n            };\n          }\n        } else {\n          rules.push({\n            required\n          });\n        }\n      }\n      return rules;\n    });\n    const validateEnabled = computed(() => normalizedRules.value.length > 0);\n    const getFilteredRule = trigger => {\n      const rules = normalizedRules.value;\n      return rules.filter(rule => {\n        if (!rule.trigger || !trigger) return true;\n        if (isArray(rule.trigger)) {\n          return rule.trigger.includes(trigger);\n        } else {\n          return rule.trigger === trigger;\n        }\n      }).map(({\n        trigger: trigger2,\n        ...rule\n      }) => rule);\n    };\n    const isRequired = computed(() => normalizedRules.value.some(rule => rule.required));\n    const shouldShowError = computed(() => {\n      var _a;\n      return validateStateDebounced.value === \"error\" && props.showMessage && ((_a = formContext == null ? void 0 : formContext.showMessage) != null ? _a : true);\n    });\n    const currentLabel = computed(() => `${props.label || \"\"}${(formContext == null ? void 0 : formContext.labelSuffix) || \"\"}`);\n    const setValidationState = state => {\n      validateState.value = state;\n    };\n    const onValidationFailed = error => {\n      var _a, _b;\n      const {\n        errors,\n        fields\n      } = error;\n      if (!errors || !fields) {\n        console.error(error);\n      }\n      setValidationState(\"error\");\n      validateMessage.value = errors ? (_b = (_a = errors == null ? void 0 : errors[0]) == null ? void 0 : _a.message) != null ? _b : `${props.prop} is required` : \"\";\n      formContext == null ? void 0 : formContext.emit(\"validate\", props.prop, false, validateMessage.value);\n    };\n    const onValidationSucceeded = () => {\n      setValidationState(\"success\");\n      formContext == null ? void 0 : formContext.emit(\"validate\", props.prop, true, \"\");\n    };\n    const doValidate = async rules => {\n      const modelName = propString.value;\n      const validator = new AsyncValidator({\n        [modelName]: rules\n      });\n      return validator.validate({\n        [modelName]: fieldValue.value\n      }, {\n        firstFields: true\n      }).then(() => {\n        onValidationSucceeded();\n        return true;\n      }).catch(err => {\n        onValidationFailed(err);\n        return Promise.reject(err);\n      });\n    };\n    const validate = async (trigger, callback) => {\n      if (isResettingField || !props.prop) {\n        return false;\n      }\n      const hasCallback = isFunction(callback);\n      if (!validateEnabled.value) {\n        callback == null ? void 0 : callback(false);\n        return false;\n      }\n      const rules = getFilteredRule(trigger);\n      if (rules.length === 0) {\n        callback == null ? void 0 : callback(true);\n        return true;\n      }\n      setValidationState(\"validating\");\n      return doValidate(rules).then(() => {\n        callback == null ? void 0 : callback(true);\n        return true;\n      }).catch(err => {\n        const {\n          fields\n        } = err;\n        callback == null ? void 0 : callback(false, fields);\n        return hasCallback ? false : Promise.reject(fields);\n      });\n    };\n    const clearValidate = () => {\n      setValidationState(\"\");\n      validateMessage.value = \"\";\n      isResettingField = false;\n    };\n    const resetField = async () => {\n      const model = formContext == null ? void 0 : formContext.model;\n      if (!model || !props.prop) return;\n      const computedValue = getProp(model, props.prop);\n      isResettingField = true;\n      computedValue.value = clone(initialValue);\n      await nextTick();\n      clearValidate();\n      isResettingField = false;\n    };\n    const addInputId = id => {\n      if (!inputIds.value.includes(id)) {\n        inputIds.value.push(id);\n      }\n    };\n    const removeInputId = id => {\n      inputIds.value = inputIds.value.filter(listId => listId !== id);\n    };\n    watch(() => props.error, val => {\n      validateMessage.value = val || \"\";\n      setValidationState(val ? \"error\" : \"\");\n    }, {\n      immediate: true\n    });\n    watch(() => props.validateStatus, val => setValidationState(val || \"\"));\n    const context = reactive({\n      ...toRefs(props),\n      $el: formItemRef,\n      size: _size,\n      validateState,\n      labelId,\n      inputIds,\n      isGroup,\n      hasLabel,\n      fieldValue,\n      addInputId,\n      removeInputId,\n      resetField,\n      clearValidate,\n      validate\n    });\n    provide(formItemContextKey, context);\n    onMounted(() => {\n      if (props.prop) {\n        formContext == null ? void 0 : formContext.addField(context);\n        initialValue = clone(fieldValue.value);\n      }\n    });\n    onBeforeUnmount(() => {\n      formContext == null ? void 0 : formContext.removeField(context);\n    });\n    expose({\n      size: _size,\n      validateMessage,\n      validateState,\n      validate,\n      clearValidate,\n      resetField\n    });\n    return (_ctx, _cache) => {\n      var _a;\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"formItemRef\",\n        ref: formItemRef,\n        class: normalizeClass(unref(formItemClasses)),\n        role: unref(isGroup) ? \"group\" : void 0,\n        \"aria-labelledby\": unref(isGroup) ? unref(labelId) : void 0\n      }, [createVNode(unref(FormLabelWrap), {\n        \"is-auto-width\": unref(labelStyle).width === \"auto\",\n        \"update-all\": ((_a = unref(formContext)) == null ? void 0 : _a.labelWidth) === \"auto\"\n      }, {\n        default: withCtx(() => [unref(hasLabel) ? (openBlock(), createBlock(resolveDynamicComponent(unref(labelFor) ? \"label\" : \"div\"), {\n          key: 0,\n          id: unref(labelId),\n          for: unref(labelFor),\n          class: normalizeClass(unref(ns).e(\"label\")),\n          style: normalizeStyle(unref(labelStyle))\n        }, {\n          default: withCtx(() => [renderSlot(_ctx.$slots, \"label\", {\n            label: unref(currentLabel)\n          }, () => [createTextVNode(toDisplayString(unref(currentLabel)), 1)])]),\n          _: 3\n        }, 8, [\"id\", \"for\", \"class\", \"style\"])) : createCommentVNode(\"v-if\", true)]),\n        _: 3\n      }, 8, [\"is-auto-width\", \"update-all\"]), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"content\")),\n        style: normalizeStyle(unref(contentStyle))\n      }, [renderSlot(_ctx.$slots, \"default\"), createVNode(TransitionGroup, {\n        name: `${unref(ns).namespace.value}-zoom-in-top`\n      }, {\n        default: withCtx(() => [unref(shouldShowError) ? renderSlot(_ctx.$slots, \"error\", {\n          key: 0,\n          error: validateMessage.value\n        }, () => [createElementVNode(\"div\", {\n          class: normalizeClass(unref(validateClasses))\n        }, toDisplayString(validateMessage.value), 3)]) : createCommentVNode(\"v-if\", true)]),\n        _: 3\n      }, 8, [\"name\"])], 6)], 10, [\"role\", \"aria-labelledby\"]);\n    };\n  }\n});\nvar FormItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"form-item.vue\"]]);\nexport { FormItem as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}