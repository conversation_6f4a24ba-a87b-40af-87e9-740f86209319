{"ast": null, "code": "import { radioEmits } from './radio.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nconst radioGroupProps = buildProps({\n  id: {\n    type: String,\n    default: void 0\n  },\n  size: useSizeProp,\n  disabled: Boolean,\n  modelValue: {\n    type: [String, Number, Boolean],\n    default: void 0\n  },\n  fill: {\n    type: String,\n    default: \"\"\n  },\n  textColor: {\n    type: String,\n    default: \"\"\n  },\n  name: {\n    type: String,\n    default: void 0\n  },\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  ...useAriaProps([\"ariaLabel\"])\n});\nconst radioGroupEmits = radioEmits;\nexport { radioGroupEmits, radioGroupProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}