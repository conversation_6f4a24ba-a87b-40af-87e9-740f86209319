{"ast": null, "code": "import { defineComponent, getCurrentInstance, ref, computed, watch, onMounted, openBlock, createElementBlock, normalizeClass, createElementVNode, normalizeStyle } from 'vue';\nimport { draggable } from '../utils/draggable.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { getClientXY } from '../../../../utils/dom/position.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElColorHueSlider\",\n  props: {\n    color: {\n      type: Object,\n      required: true\n    },\n    vertical: Boolean\n  },\n  setup(props) {\n    const ns = useNamespace(\"color-hue-slider\");\n    const instance = getCurrentInstance();\n    const thumb = ref();\n    const bar = ref();\n    const thumbLeft = ref(0);\n    const thumbTop = ref(0);\n    const hueValue = computed(() => {\n      return props.color.get(\"hue\");\n    });\n    watch(() => hueValue.value, () => {\n      update();\n    });\n    function handleClick(event) {\n      const target = event.target;\n      if (target !== thumb.value) {\n        handleDrag(event);\n      }\n    }\n    function handleDrag(event) {\n      if (!bar.value || !thumb.value) return;\n      const el = instance.vnode.el;\n      const rect = el.getBoundingClientRect();\n      const {\n        clientX,\n        clientY\n      } = getClientXY(event);\n      let hue;\n      if (!props.vertical) {\n        let left = clientX - rect.left;\n        left = Math.min(left, rect.width - thumb.value.offsetWidth / 2);\n        left = Math.max(thumb.value.offsetWidth / 2, left);\n        hue = Math.round((left - thumb.value.offsetWidth / 2) / (rect.width - thumb.value.offsetWidth) * 360);\n      } else {\n        let top = clientY - rect.top;\n        top = Math.min(top, rect.height - thumb.value.offsetHeight / 2);\n        top = Math.max(thumb.value.offsetHeight / 2, top);\n        hue = Math.round((top - thumb.value.offsetHeight / 2) / (rect.height - thumb.value.offsetHeight) * 360);\n      }\n      props.color.set(\"hue\", hue);\n    }\n    function getThumbLeft() {\n      if (!thumb.value) return 0;\n      const el = instance.vnode.el;\n      if (props.vertical) return 0;\n      const hue = props.color.get(\"hue\");\n      if (!el) return 0;\n      return Math.round(hue * (el.offsetWidth - thumb.value.offsetWidth / 2) / 360);\n    }\n    function getThumbTop() {\n      if (!thumb.value) return 0;\n      const el = instance.vnode.el;\n      if (!props.vertical) return 0;\n      const hue = props.color.get(\"hue\");\n      if (!el) return 0;\n      return Math.round(hue * (el.offsetHeight - thumb.value.offsetHeight / 2) / 360);\n    }\n    function update() {\n      thumbLeft.value = getThumbLeft();\n      thumbTop.value = getThumbTop();\n    }\n    onMounted(() => {\n      if (!bar.value || !thumb.value) return;\n      const dragConfig = {\n        drag: event => {\n          handleDrag(event);\n        },\n        end: event => {\n          handleDrag(event);\n        }\n      };\n      draggable(bar.value, dragConfig);\n      draggable(thumb.value, dragConfig);\n      update();\n    });\n    return {\n      bar,\n      thumb,\n      thumbLeft,\n      thumbTop,\n      hueValue,\n      handleClick,\n      update,\n      ns\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"div\", {\n    class: normalizeClass([_ctx.ns.b(), _ctx.ns.is(\"vertical\", _ctx.vertical)])\n  }, [createElementVNode(\"div\", {\n    ref: \"bar\",\n    class: normalizeClass(_ctx.ns.e(\"bar\")),\n    onClick: _ctx.handleClick\n  }, null, 10, [\"onClick\"]), createElementVNode(\"div\", {\n    ref: \"thumb\",\n    class: normalizeClass(_ctx.ns.e(\"thumb\")),\n    style: normalizeStyle({\n      left: _ctx.thumbLeft + \"px\",\n      top: _ctx.thumbTop + \"px\"\n    })\n  }, null, 6)], 2);\n}\nvar HueSlider = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"hue-slider.vue\"]]);\nexport { HueSlider as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}