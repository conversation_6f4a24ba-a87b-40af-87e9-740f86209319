{"ast": null, "code": "import { defineComponent, getCurrentInstance, inject, toRef, computed, reactive, onMounted, onBeforeUnmount, openBlock, createElementBlock, normalizeClass, unref, createBlock, withCtx, renderSlot, createElementVNode, Fragment } from 'vue';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport useMenu from './use-menu.mjs';\nimport { menuItemProps, menuItemEmits } from './menu-item.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nconst COMPONENT_NAME = \"ElMenuItem\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: menuItemProps,\n  emits: menuItemEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const instance = getCurrentInstance();\n    const rootMenu = inject(\"rootMenu\");\n    const nsMenu = useNamespace(\"menu\");\n    const nsMenuItem = useNamespace(\"menu-item\");\n    if (!rootMenu) throwError(COMPONENT_NAME, \"can not inject root menu\");\n    const {\n      parentMenu,\n      indexPath\n    } = useMenu(instance, toRef(props, \"index\"));\n    const subMenu = inject(`subMenu:${parentMenu.value.uid}`);\n    if (!subMenu) throwError(COMPONENT_NAME, \"can not inject sub menu\");\n    const active = computed(() => props.index === rootMenu.activeIndex);\n    const item = reactive({\n      index: props.index,\n      indexPath,\n      active\n    });\n    const handleClick = () => {\n      if (!props.disabled) {\n        rootMenu.handleMenuItemClick({\n          index: props.index,\n          indexPath: indexPath.value,\n          route: props.route\n        });\n        emit(\"click\", item);\n      }\n    };\n    onMounted(() => {\n      subMenu.addSubMenu(item);\n      rootMenu.addMenuItem(item);\n    });\n    onBeforeUnmount(() => {\n      subMenu.removeSubMenu(item);\n      rootMenu.removeMenuItem(item);\n    });\n    expose({\n      parentMenu,\n      rootMenu,\n      active,\n      nsMenu,\n      nsMenuItem,\n      handleClick\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"li\", {\n        class: normalizeClass([unref(nsMenuItem).b(), unref(nsMenuItem).is(\"active\", unref(active)), unref(nsMenuItem).is(\"disabled\", _ctx.disabled)]),\n        role: \"menuitem\",\n        tabindex: \"-1\",\n        onClick: handleClick\n      }, [unref(parentMenu).type.name === \"ElMenu\" && unref(rootMenu).props.collapse && _ctx.$slots.title ? (openBlock(), createBlock(unref(ElTooltip), {\n        key: 0,\n        effect: unref(rootMenu).props.popperEffect,\n        placement: \"right\",\n        \"fallback-placements\": [\"left\"],\n        persistent: unref(rootMenu).props.persistent\n      }, {\n        content: withCtx(() => [renderSlot(_ctx.$slots, \"title\")]),\n        default: withCtx(() => [createElementVNode(\"div\", {\n          class: normalizeClass(unref(nsMenu).be(\"tooltip\", \"trigger\"))\n        }, [renderSlot(_ctx.$slots, \"default\")], 2)]),\n        _: 3\n      }, 8, [\"effect\", \"persistent\"])) : (openBlock(), createElementBlock(Fragment, {\n        key: 1\n      }, [renderSlot(_ctx.$slots, \"default\"), renderSlot(_ctx.$slots, \"title\")], 64))], 2);\n    };\n  }\n});\nvar MenuItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"menu-item.vue\"]]);\nexport { MenuItem as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}