{"ast": null, "code": "import { computed } from 'vue';\nfunction useMenu(instance, currentIndex) {\n  const indexPath = computed(() => {\n    let parent = instance.parent;\n    const path = [currentIndex.value];\n    while (parent.type.name !== \"ElMenu\") {\n      if (parent.props.index) {\n        path.unshift(parent.props.index);\n      }\n      parent = parent.parent;\n    }\n    return path;\n  });\n  const parentMenu = computed(() => {\n    let parent = instance.parent;\n    while (parent && ![\"ElMenu\", \"ElSubMenu\"].includes(parent.type.name)) {\n      parent = parent.parent;\n    }\n    return parent;\n  });\n  return {\n    parentMenu,\n    indexPath\n  };\n}\nexport { useMenu as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}