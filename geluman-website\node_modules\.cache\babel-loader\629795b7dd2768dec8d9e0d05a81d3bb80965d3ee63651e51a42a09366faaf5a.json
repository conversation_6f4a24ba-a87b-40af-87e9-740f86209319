{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, normalizeClass, normalizeStyle, toDisplayString } from 'vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst _sfc_main = defineComponent({\n  props: {\n    item: {\n      type: Object,\n      required: true\n    },\n    style: {\n      type: Object\n    },\n    height: Number\n  },\n  setup() {\n    const ns = useNamespace(\"select\");\n    return {\n      ns\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"div\", {\n    class: normalizeClass(_ctx.ns.be(\"group\", \"title\")),\n    style: normalizeStyle({\n      ..._ctx.style,\n      lineHeight: `${_ctx.height}px`\n    })\n  }, toDisplayString(_ctx.item.label), 7);\n}\nvar GroupItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"group-item.vue\"]]);\nexport { GroupItem as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}