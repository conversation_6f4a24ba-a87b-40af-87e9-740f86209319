{"ast": null, "code": "import { placements } from '@popperjs/core';\nimport { CircleClose, ArrowDown } from '@element-plus/icons-vue';\nimport { scrollbarEmits } from '../../scrollbar/src/scrollbar.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { useTooltipContentProps } from '../../tooltip/src/content.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nimport { tagProps } from '../../tag/src/tag.mjs';\nimport { useEmptyValuesProps } from '../../../hooks/use-empty-values/index.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nconst SelectProps = buildProps({\n  name: String,\n  id: String,\n  modelValue: {\n    type: definePropType([Array, String, Number, Boolean, Object]),\n    default: void 0\n  },\n  autocomplete: {\n    type: String,\n    default: \"off\"\n  },\n  automaticDropdown: Boolean,\n  size: useSizeProp,\n  effect: {\n    type: definePropType(String),\n    default: \"light\"\n  },\n  disabled: Boolean,\n  clearable: Boolean,\n  filterable: Boolean,\n  allowCreate: Boolean,\n  loading: Boolean,\n  popperClass: {\n    type: String,\n    default: \"\"\n  },\n  popperOptions: {\n    type: definePropType(Object),\n    default: () => ({})\n  },\n  remote: Boolean,\n  loadingText: String,\n  noMatchText: String,\n  noDataText: String,\n  remoteMethod: Function,\n  filterMethod: Function,\n  multiple: Boolean,\n  multipleLimit: {\n    type: Number,\n    default: 0\n  },\n  placeholder: {\n    type: String\n  },\n  defaultFirstOption: Boolean,\n  reserveKeyword: {\n    type: Boolean,\n    default: true\n  },\n  valueKey: {\n    type: String,\n    default: \"value\"\n  },\n  collapseTags: Boolean,\n  collapseTagsTooltip: Boolean,\n  maxCollapseTags: {\n    type: Number,\n    default: 1\n  },\n  teleported: useTooltipContentProps.teleported,\n  persistent: {\n    type: Boolean,\n    default: true\n  },\n  clearIcon: {\n    type: iconPropType,\n    default: CircleClose\n  },\n  fitInputWidth: Boolean,\n  suffixIcon: {\n    type: iconPropType,\n    default: ArrowDown\n  },\n  tagType: {\n    ...tagProps.type,\n    default: \"info\"\n  },\n  tagEffect: {\n    ...tagProps.effect,\n    default: \"light\"\n  },\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  remoteShowSuffix: Boolean,\n  showArrow: {\n    type: Boolean,\n    default: true\n  },\n  offset: {\n    type: Number,\n    default: 12\n  },\n  placement: {\n    type: definePropType(String),\n    values: placements,\n    default: \"bottom-start\"\n  },\n  fallbackPlacements: {\n    type: definePropType(Array),\n    default: [\"bottom-start\", \"top-start\", \"right\", \"left\"]\n  },\n  tabindex: {\n    type: [String, Number],\n    default: 0\n  },\n  appendTo: useTooltipContentProps.appendTo,\n  ...useEmptyValuesProps,\n  ...useAriaProps([\"ariaLabel\"])\n});\nconst selectEmits = {\n  [UPDATE_MODEL_EVENT]: val => true,\n  [CHANGE_EVENT]: val => true,\n  \"popup-scroll\": scrollbarEmits.scroll,\n  \"remove-tag\": val => true,\n  \"visible-change\": visible => true,\n  focus: evt => evt instanceof FocusEvent,\n  blur: evt => evt instanceof FocusEvent,\n  clear: () => true\n};\nexport { SelectProps, selectEmits };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}