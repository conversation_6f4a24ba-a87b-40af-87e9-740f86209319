{"ast": null, "code": "import { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../../utils/typescript.mjs';\nimport { componentSizes } from '../../../../constants/size.mjs';\nconst paginationSizesProps = buildProps({\n  pageSize: {\n    type: Number,\n    required: true\n  },\n  pageSizes: {\n    type: definePropType(Array),\n    default: () => mutable([10, 20, 30, 40, 50, 100])\n  },\n  popperClass: {\n    type: String\n  },\n  disabled: Boolean,\n  teleported: Boolean,\n  size: {\n    type: String,\n    values: componentSizes\n  },\n  appendSizeTo: String\n});\nexport { paginationSizesProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}