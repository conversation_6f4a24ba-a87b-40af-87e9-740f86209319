{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { componentSizes } from '../../../constants/size.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nconst avatarProps = buildProps({\n  size: {\n    type: [Number, String],\n    values: componentSizes,\n    default: \"\",\n    validator: val => isNumber(val)\n  },\n  shape: {\n    type: String,\n    values: [\"circle\", \"square\"],\n    default: \"circle\"\n  },\n  icon: {\n    type: iconPropType\n  },\n  src: {\n    type: String,\n    default: \"\"\n  },\n  alt: String,\n  srcSet: String,\n  fit: {\n    type: definePropType(String),\n    default: \"cover\"\n  }\n});\nconst avatarEmits = {\n  error: evt => evt instanceof Event\n};\nexport { avatarEmits, avatarProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}