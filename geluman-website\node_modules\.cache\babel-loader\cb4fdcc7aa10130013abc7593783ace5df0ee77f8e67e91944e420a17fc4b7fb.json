{"ast": null, "code": "import { LTR, RTL, HORIZONTAL, FORWARD, BACKWARD, RTL_OFFSET_POS_DESC, RTL_OFFSET_NAG, RTL_OFFSET_POS_ASC } from './defaults.mjs';\nconst getScrollDir = (prev, cur) => prev < cur ? FORWARD : BACKWARD;\nconst isHorizontal = dir => dir === LTR || dir === RTL || dir === HORIZONTAL;\nconst isRTL = dir => dir === RTL;\nlet cachedRTLResult = null;\nfunction getRTLOffsetType(recalculate = false) {\n  if (cachedRTLResult === null || recalculate) {\n    const outerDiv = document.createElement(\"div\");\n    const outerStyle = outerDiv.style;\n    outerStyle.width = \"50px\";\n    outerStyle.height = \"50px\";\n    outerStyle.overflow = \"scroll\";\n    outerStyle.direction = \"rtl\";\n    const innerDiv = document.createElement(\"div\");\n    const innerStyle = innerDiv.style;\n    innerStyle.width = \"100px\";\n    innerStyle.height = \"100px\";\n    outerDiv.appendChild(innerDiv);\n    document.body.appendChild(outerDiv);\n    if (outerDiv.scrollLeft > 0) {\n      cachedRTLResult = RTL_OFFSET_POS_DESC;\n    } else {\n      outerDiv.scrollLeft = 1;\n      if (outerDiv.scrollLeft === 0) {\n        cachedRTLResult = RTL_OFFSET_NAG;\n      } else {\n        cachedRTLResult = RTL_OFFSET_POS_ASC;\n      }\n    }\n    document.body.removeChild(outerDiv);\n    return cachedRTLResult;\n  }\n  return cachedRTLResult;\n}\nfunction renderThumbStyle({\n  move,\n  size,\n  bar\n}, layout) {\n  const style = {};\n  const translate = `translate${bar.axis}(${move}px)`;\n  style[bar.size] = size;\n  style.transform = translate;\n  if (layout === \"horizontal\") {\n    style.height = \"100%\";\n  } else {\n    style.width = \"100%\";\n  }\n  return style;\n}\nexport { getRTLOffsetType, getScrollDir, isHorizontal, isRTL, renderThumbStyle };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}