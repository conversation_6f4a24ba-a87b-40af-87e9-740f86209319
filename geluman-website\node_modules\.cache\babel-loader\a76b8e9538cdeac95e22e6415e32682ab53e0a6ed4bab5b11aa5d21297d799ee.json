{"ast": null, "code": "function useCursor(input) {\n  let selectionInfo;\n  function recordCursor() {\n    if (input.value == void 0) return;\n    const {\n      selectionStart,\n      selectionEnd,\n      value\n    } = input.value;\n    if (selectionStart == null || selectionEnd == null) return;\n    const beforeTxt = value.slice(0, Math.max(0, selectionStart));\n    const afterTxt = value.slice(Math.max(0, selectionEnd));\n    selectionInfo = {\n      selectionStart,\n      selectionEnd,\n      value,\n      beforeTxt,\n      afterTxt\n    };\n  }\n  function setCursor() {\n    if (input.value == void 0 || selectionInfo == void 0) return;\n    const {\n      value\n    } = input.value;\n    const {\n      beforeTxt,\n      afterTxt,\n      selectionStart\n    } = selectionInfo;\n    if (beforeTxt == void 0 || afterTxt == void 0 || selectionStart == void 0) return;\n    let startPos = value.length;\n    if (value.endsWith(afterTxt)) {\n      startPos = value.length - afterTxt.length;\n    } else if (value.startsWith(beforeTxt)) {\n      startPos = beforeTxt.length;\n    } else {\n      const beforeLastChar = beforeTxt[selectionStart - 1];\n      const newIndex = value.indexOf(beforeLastChar, selectionStart - 1);\n      if (newIndex !== -1) {\n        startPos = newIndex + 1;\n      }\n    }\n    input.value.setSelectionRange(startPos, startPos);\n  }\n  return [recordCursor, setCursor];\n}\nexport { useCursor };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}