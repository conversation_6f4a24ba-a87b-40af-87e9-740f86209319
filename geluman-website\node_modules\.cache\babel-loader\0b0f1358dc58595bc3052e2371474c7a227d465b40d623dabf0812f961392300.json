{"ast": null, "code": "!function (e, i) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = i() : \"function\" == typeof define && define.amd ? define(i) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_isSameOrBefore = i();\n}(this, function () {\n  \"use strict\";\n\n  return function (e, i) {\n    i.prototype.isSameOrBefore = function (e, i) {\n      return this.isSame(e, i) || this.isBefore(e, i);\n    };\n  };\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}