{"ast": null, "code": "import { timePanelSharedProps } from './shared.mjs';\nimport { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nconst panelTimeRangeProps = buildProps({\n  ...timePanelSharedProps,\n  parsedValue: {\n    type: definePropType(Array)\n  }\n});\nexport { panelTimeRangeProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}