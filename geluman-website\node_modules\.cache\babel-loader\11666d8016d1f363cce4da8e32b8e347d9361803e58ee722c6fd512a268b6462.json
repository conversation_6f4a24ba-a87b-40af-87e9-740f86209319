{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst cardProps = buildProps({\n  header: {\n    type: String,\n    default: \"\"\n  },\n  footer: {\n    type: String,\n    default: \"\"\n  },\n  bodyStyle: {\n    type: definePropType([String, Object, Array]),\n    default: \"\"\n  },\n  headerClass: String,\n  bodyClass: String,\n  footerClass: String,\n  shadow: {\n    type: String,\n    values: [\"always\", \"hover\", \"never\"],\n    default: \"always\"\n  }\n});\nexport { cardProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}