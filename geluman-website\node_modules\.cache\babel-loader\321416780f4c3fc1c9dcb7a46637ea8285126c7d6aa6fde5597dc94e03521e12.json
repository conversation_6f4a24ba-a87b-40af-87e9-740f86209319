{"ast": null, "code": "import { defineComponent, inject, ref, provide, onMounted, watch, unref, onBeforeUnmount, openBlock, createElementBlock, mergeProps, createVNode, withCtx, renderSlot } from 'vue';\nimport { isNil } from 'lodash-unified';\nimport ElFocusTrap from '../../focus-trap/src/focus-trap.mjs';\nimport { POPPER_CONTENT_INJECTION_KEY } from './constants.mjs';\nimport { popperContentProps, popperContentEmits } from './content.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { usePopperContentFocusTrap } from './composables/use-focus-trap.mjs';\nimport { usePopperContent } from './composables/use-content.mjs';\nimport { usePopperContentDOM } from './composables/use-content-dom.mjs';\nimport { formItemContextKey } from '../../form/src/constants.mjs';\nimport { NOOP } from '@vue/shared';\nimport { isElement } from '../../../utils/types.mjs';\nconst __default__ = defineComponent({\n  name: \"ElPopperContent\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: popperContentProps,\n  emits: popperContentEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const {\n      focusStartRef,\n      trapped,\n      onFocusAfterReleased,\n      onFocusAfterTrapped,\n      onFocusInTrap,\n      onFocusoutPrevented,\n      onReleaseRequested\n    } = usePopperContentFocusTrap(props, emit);\n    const {\n      attributes,\n      arrowRef,\n      contentRef,\n      styles,\n      instanceRef,\n      role,\n      update\n    } = usePopperContent(props);\n    const {\n      ariaModal,\n      arrowStyle,\n      contentAttrs,\n      contentClass,\n      contentStyle,\n      updateZIndex\n    } = usePopperContentDOM(props, {\n      styles,\n      attributes,\n      role\n    });\n    const formItemContext = inject(formItemContextKey, void 0);\n    const arrowOffset = ref();\n    provide(POPPER_CONTENT_INJECTION_KEY, {\n      arrowStyle,\n      arrowRef,\n      arrowOffset\n    });\n    if (formItemContext) {\n      provide(formItemContextKey, {\n        ...formItemContext,\n        addInputId: NOOP,\n        removeInputId: NOOP\n      });\n    }\n    let triggerTargetAriaStopWatch = void 0;\n    const updatePopper = (shouldUpdateZIndex = true) => {\n      update();\n      shouldUpdateZIndex && updateZIndex();\n    };\n    const togglePopperAlive = () => {\n      updatePopper(false);\n      if (props.visible && props.focusOnShow) {\n        trapped.value = true;\n      } else if (props.visible === false) {\n        trapped.value = false;\n      }\n    };\n    onMounted(() => {\n      watch(() => props.triggerTargetEl, (triggerTargetEl, prevTriggerTargetEl) => {\n        triggerTargetAriaStopWatch == null ? void 0 : triggerTargetAriaStopWatch();\n        triggerTargetAriaStopWatch = void 0;\n        const el = unref(triggerTargetEl || contentRef.value);\n        const prevEl = unref(prevTriggerTargetEl || contentRef.value);\n        if (isElement(el)) {\n          triggerTargetAriaStopWatch = watch([role, () => props.ariaLabel, ariaModal, () => props.id], watches => {\n            [\"role\", \"aria-label\", \"aria-modal\", \"id\"].forEach((key, idx) => {\n              isNil(watches[idx]) ? el.removeAttribute(key) : el.setAttribute(key, watches[idx]);\n            });\n          }, {\n            immediate: true\n          });\n        }\n        if (prevEl !== el && isElement(prevEl)) {\n          [\"role\", \"aria-label\", \"aria-modal\", \"id\"].forEach(key => {\n            prevEl.removeAttribute(key);\n          });\n        }\n      }, {\n        immediate: true\n      });\n      watch(() => props.visible, togglePopperAlive, {\n        immediate: true\n      });\n    });\n    onBeforeUnmount(() => {\n      triggerTargetAriaStopWatch == null ? void 0 : triggerTargetAriaStopWatch();\n      triggerTargetAriaStopWatch = void 0;\n    });\n    expose({\n      popperContentRef: contentRef,\n      popperInstanceRef: instanceRef,\n      updatePopper,\n      contentStyle\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", mergeProps({\n        ref_key: \"contentRef\",\n        ref: contentRef\n      }, unref(contentAttrs), {\n        style: unref(contentStyle),\n        class: unref(contentClass),\n        tabindex: \"-1\",\n        onMouseenter: e => _ctx.$emit(\"mouseenter\", e),\n        onMouseleave: e => _ctx.$emit(\"mouseleave\", e)\n      }), [createVNode(unref(ElFocusTrap), {\n        trapped: unref(trapped),\n        \"trap-on-focus-in\": true,\n        \"focus-trap-el\": unref(contentRef),\n        \"focus-start-el\": unref(focusStartRef),\n        onFocusAfterTrapped: unref(onFocusAfterTrapped),\n        onFocusAfterReleased: unref(onFocusAfterReleased),\n        onFocusin: unref(onFocusInTrap),\n        onFocusoutPrevented: unref(onFocusoutPrevented),\n        onReleaseRequested: unref(onReleaseRequested)\n      }, {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 8, [\"trapped\", \"focus-trap-el\", \"focus-start-el\", \"onFocusAfterTrapped\", \"onFocusAfterReleased\", \"onFocusin\", \"onFocusoutPrevented\", \"onReleaseRequested\"])], 16, [\"onMouseenter\", \"onMouseleave\"]);\n    };\n  }\n});\nvar ElPopperContent = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"content.vue\"]]);\nexport { ElPopperContent as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}