{"ast": null, "code": "import CascaderPanel from './src/index.mjs';\nexport { CASCADER_PANEL_INJECTION_KEY } from './src/types.mjs';\nexport { CommonProps, DefaultProps, useCascaderConfig } from './src/config.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElCascaderPanel = withInstall(CascaderPanel);\nexport { ElCascaderPanel, ElCascaderPanel as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}