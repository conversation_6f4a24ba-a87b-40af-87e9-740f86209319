{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst statisticProps = buildProps({\n  decimalSeparator: {\n    type: String,\n    default: \".\"\n  },\n  groupSeparator: {\n    type: String,\n    default: \",\"\n  },\n  precision: {\n    type: Number,\n    default: 0\n  },\n  formatter: Function,\n  value: {\n    type: definePropType([Number, Object]),\n    default: 0\n  },\n  prefix: String,\n  suffix: String,\n  title: String,\n  valueStyle: {\n    type: definePropType([String, Object, Array])\n  }\n});\nexport { statisticProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}