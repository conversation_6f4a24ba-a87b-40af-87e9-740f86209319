{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\n/**\n * Converts `set` to its value-value pairs.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the value-value pairs.\n */\nfunction setToPairs(set) {\n  var index = -1,\n    result = Array(set.size);\n  set.forEach(function (value) {\n    result[++index] = [value, value];\n  });\n  return result;\n}\nexport default setToPairs;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}