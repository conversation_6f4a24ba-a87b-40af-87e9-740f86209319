{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { isVNode, render, markRaw, createVNode } from 'vue';\nimport MessageBoxConstructor from './index.mjs';\nimport { isClient } from '@vueuse/core';\nimport { isString, isObject, hasOwn, isFunction } from '@vue/shared';\nimport { isUndefined, isElement } from '../../../utils/types.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nconst messageInstance = /* @__PURE__ */new Map();\nconst getAppendToElement = props => {\n  let appendTo = document.body;\n  if (props.appendTo) {\n    if (isString(props.appendTo)) {\n      appendTo = document.querySelector(props.appendTo);\n    }\n    if (isElement(props.appendTo)) {\n      appendTo = props.appendTo;\n    }\n    if (!isElement(appendTo)) {\n      debugWarn(\"ElMessageBox\", \"the appendTo option is not an HTMLElement. Falling back to document.body.\");\n      appendTo = document.body;\n    }\n  }\n  return appendTo;\n};\nconst initInstance = (props, container, appContext = null) => {\n  const vnode = createVNode(MessageBoxConstructor, props, isFunction(props.message) || isVNode(props.message) ? {\n    default: isFunction(props.message) ? props.message : () => props.message\n  } : null);\n  vnode.appContext = appContext;\n  render(vnode, container);\n  getAppendToElement(props).appendChild(container.firstElementChild);\n  return vnode.component;\n};\nconst genContainer = () => {\n  return document.createElement(\"div\");\n};\nconst showMessage = (options, appContext) => {\n  const container = genContainer();\n  options.onVanish = () => {\n    render(null, container);\n    messageInstance.delete(vm);\n  };\n  options.onAction = action => {\n    const currentMsg = messageInstance.get(vm);\n    let resolve;\n    if (options.showInput) {\n      resolve = {\n        value: vm.inputValue,\n        action\n      };\n    } else {\n      resolve = action;\n    }\n    if (options.callback) {\n      options.callback(resolve, instance.proxy);\n    } else {\n      if (action === \"cancel\" || action === \"close\") {\n        if (options.distinguishCancelAndClose && action !== \"cancel\") {\n          currentMsg.reject(\"close\");\n        } else {\n          currentMsg.reject(\"cancel\");\n        }\n      } else {\n        currentMsg.resolve(resolve);\n      }\n    }\n  };\n  const instance = initInstance(options, container, appContext);\n  const vm = instance.proxy;\n  for (const prop in options) {\n    if (hasOwn(options, prop) && !hasOwn(vm.$props, prop)) {\n      if (prop === \"closeIcon\" && isObject(options[prop])) {\n        vm[prop] = markRaw(options[prop]);\n      } else {\n        vm[prop] = options[prop];\n      }\n    }\n  }\n  vm.visible = true;\n  return vm;\n};\nfunction MessageBox(options, appContext = null) {\n  if (!isClient) return Promise.reject();\n  let callback;\n  if (isString(options) || isVNode(options)) {\n    options = {\n      message: options\n    };\n  } else {\n    callback = options.callback;\n  }\n  return new Promise((resolve, reject) => {\n    const vm = showMessage(options, appContext != null ? appContext : MessageBox._context);\n    messageInstance.set(vm, {\n      options,\n      callback,\n      resolve,\n      reject\n    });\n  });\n}\nconst MESSAGE_BOX_VARIANTS = [\"alert\", \"confirm\", \"prompt\"];\nconst MESSAGE_BOX_DEFAULT_OPTS = {\n  alert: {\n    closeOnPressEscape: false,\n    closeOnClickModal: false\n  },\n  confirm: {\n    showCancelButton: true\n  },\n  prompt: {\n    showCancelButton: true,\n    showInput: true\n  }\n};\nMESSAGE_BOX_VARIANTS.forEach(boxType => {\n  MessageBox[boxType] = messageBoxFactory(boxType);\n});\nfunction messageBoxFactory(boxType) {\n  return (message, title, options, appContext) => {\n    let titleOrOpts = \"\";\n    if (isObject(title)) {\n      options = title;\n      titleOrOpts = \"\";\n    } else if (isUndefined(title)) {\n      titleOrOpts = \"\";\n    } else {\n      titleOrOpts = title;\n    }\n    return MessageBox(Object.assign({\n      title: titleOrOpts,\n      message,\n      type: \"\",\n      ...MESSAGE_BOX_DEFAULT_OPTS[boxType]\n    }, options, {\n      boxType\n    }), appContext);\n  };\n}\nMessageBox.close = () => {\n  messageInstance.forEach((_, vm) => {\n    vm.doClose();\n  });\n  messageInstance.clear();\n};\nMessageBox._context = null;\nexport { MessageBox as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}