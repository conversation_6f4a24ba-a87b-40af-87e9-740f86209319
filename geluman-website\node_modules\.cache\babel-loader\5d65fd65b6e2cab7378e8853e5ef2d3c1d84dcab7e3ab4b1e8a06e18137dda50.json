{"ast": null, "code": "import { TypeComponentsMap } from '../../../utils/vue/icon.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { keysOf } from '../../../utils/objects.mjs';\nconst alertEffects = [\"light\", \"dark\"];\nconst alertProps = buildProps({\n  title: {\n    type: String,\n    default: \"\"\n  },\n  description: {\n    type: String,\n    default: \"\"\n  },\n  type: {\n    type: String,\n    values: keysOf(TypeComponentsMap),\n    default: \"info\"\n  },\n  closable: {\n    type: Boolean,\n    default: true\n  },\n  closeText: {\n    type: String,\n    default: \"\"\n  },\n  showIcon: Boolean,\n  center: Boolean,\n  effect: {\n    type: String,\n    values: alertEffects,\n    default: \"light\"\n  }\n});\nconst alertEmits = {\n  close: evt => evt instanceof MouseEvent\n};\nexport { alertEffects, alertEmits, alertProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}