{"ast": null, "code": "import Popper from './src/popper2.mjs';\nexport { default as <PERSON>PopperArrow } from './src/arrow2.mjs';\nexport { default as ElPopperTrigger } from './src/trigger2.mjs';\nexport { default as ElPopperContent } from './src/content2.mjs';\nexport { Effect, popperProps, roleTypes, usePopperProps } from './src/popper.mjs';\nexport { popperTriggerProps, usePopperTriggerProps } from './src/trigger.mjs';\nexport { popperContentEmits, popperContentProps, popperCoreConfigProps, usePopperContentEmits, usePopperContentProps, usePopperCoreConfigProps } from './src/content.mjs';\nexport { popperArrowProps, usePopperArrowProps } from './src/arrow.mjs';\nexport { POPPER_CONTENT_INJECTION_KEY, POPPER_INJECTION_KEY } from './src/constants.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElPopper = withInstall(Popper);\nexport { ElPopper, ElPopper as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}