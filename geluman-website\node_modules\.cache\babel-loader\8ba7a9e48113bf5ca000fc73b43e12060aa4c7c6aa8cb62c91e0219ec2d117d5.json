{"ast": null, "code": "import baseRepeat from './_baseRepeat.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toInteger from './toInteger.js';\nimport toString from './toString.js';\n\n/**\n * Repeats the given string `n` times.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to repeat.\n * @param {number} [n=1] The number of times to repeat the string.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {string} Returns the repeated string.\n * @example\n *\n * _.repeat('*', 3);\n * // => '***'\n *\n * _.repeat('abc', 2);\n * // => 'abcabc'\n *\n * _.repeat('abc', 0);\n * // => ''\n */\nfunction repeat(string, n, guard) {\n  if (guard ? isIterateeCall(string, n, guard) : n === undefined) {\n    n = 1;\n  } else {\n    n = toInteger(n);\n  }\n  return baseRepeat(toString(string), n);\n}\nexport default repeat;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}