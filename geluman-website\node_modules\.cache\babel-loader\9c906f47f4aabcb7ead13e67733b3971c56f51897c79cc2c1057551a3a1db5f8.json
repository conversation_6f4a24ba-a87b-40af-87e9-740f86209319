{"ast": null, "code": "import { get, set } from 'lodash-unified';\nexport { hasOwn } from '@vue/shared';\nconst keysOf = arr => Object.keys(arr);\nconst entriesOf = arr => Object.entries(arr);\nconst getProp = (obj, path, defaultValue) => {\n  return {\n    get value() {\n      return get(obj, path, defaultValue);\n    },\n    set value(val) {\n      set(obj, path, val);\n    }\n  };\n};\nexport { entriesOf, getProp, keysOf };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}