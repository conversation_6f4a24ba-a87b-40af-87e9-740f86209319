{"ast": null, "code": "import { Close, SuccessFilled, InfoFilled, WarningFilled, CircleCloseFilled, Loading, CircleCheck, CircleClose } from '@element-plus/icons-vue';\nimport { definePropType } from './props/runtime.mjs';\nconst iconPropType = definePropType([String, Object, Function]);\nconst CloseComponents = {\n  Close\n};\nconst TypeComponents = {\n  Close,\n  SuccessFilled,\n  InfoFilled,\n  WarningFilled,\n  CircleCloseFilled\n};\nconst TypeComponentsMap = {\n  success: SuccessFilled,\n  warning: WarningFilled,\n  error: CircleCloseFilled,\n  info: InfoFilled\n};\nconst ValidateComponentsMap = {\n  validating: Loading,\n  success: CircleCheck,\n  error: CircleClose\n};\nexport { CloseComponents, TypeComponents, TypeComponentsMap, ValidateComponentsMap, iconPropType };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}