{"ast": null, "code": "function useUtils(store) {\n  const setCurrentRow = row => {\n    store.commit(\"setCurrentRow\", row);\n  };\n  const getSelectionRows = () => {\n    return store.getSelectionRows();\n  };\n  const toggleRowSelection = (row, selected, ignoreSelectable = true) => {\n    store.toggleRowSelection(row, selected, false, ignoreSelectable);\n    store.updateAllSelected();\n  };\n  const clearSelection = () => {\n    store.clearSelection();\n  };\n  const clearFilter = columnKeys => {\n    store.clearFilter(columnKeys);\n  };\n  const toggleAllSelection = () => {\n    store.commit(\"toggleAllSelection\");\n  };\n  const toggleRowExpansion = (row, expanded) => {\n    store.toggleRowExpansionAdapter(row, expanded);\n  };\n  const clearSort = () => {\n    store.clearSort();\n  };\n  const sort = (prop, order) => {\n    store.commit(\"sort\", {\n      prop,\n      order\n    });\n  };\n  const updateKeyChildren = (key, data) => {\n    store.updateKeyChildren(key, data);\n  };\n  return {\n    setCurrentRow,\n    getSelectionRows,\n    toggleRowSelection,\n    clearSelection,\n    clearFilter,\n    toggleAllSelection,\n    toggleRowExpansion,\n    clearSort,\n    sort,\n    updateKeyChildren\n  };\n}\nexport { useUtils as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}