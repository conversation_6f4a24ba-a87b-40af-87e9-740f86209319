{"ast": null, "code": "import { placements } from '@popperjs/core';\nimport { CommonProps } from '../../cascader-panel/src/config.mjs';\nimport { tagProps } from '../../tag/src/tag.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { useTooltipContentProps } from '../../tooltip/src/content.mjs';\nimport { useEmptyValuesProps } from '../../../hooks/use-empty-values/index.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { isBoolean } from '../../../utils/types.mjs';\nconst cascaderProps = buildProps({\n  ...CommonProps,\n  size: useSizeProp,\n  placeholder: String,\n  disabled: Boolean,\n  clearable: Boolean,\n  filterable: Boolean,\n  filterMethod: {\n    type: definePropType(Function),\n    default: (node, keyword) => node.text.includes(keyword)\n  },\n  separator: {\n    type: String,\n    default: \" / \"\n  },\n  showAllLevels: {\n    type: Boolean,\n    default: true\n  },\n  collapseTags: Boolean,\n  maxCollapseTags: {\n    type: Number,\n    default: 1\n  },\n  collapseTagsTooltip: {\n    type: Boolean,\n    default: false\n  },\n  debounce: {\n    type: Number,\n    default: 300\n  },\n  beforeFilter: {\n    type: definePropType(Function),\n    default: () => true\n  },\n  placement: {\n    type: definePropType(String),\n    values: placements,\n    default: \"bottom-start\"\n  },\n  fallbackPlacements: {\n    type: definePropType(Array),\n    default: [\"bottom-start\", \"bottom\", \"top-start\", \"top\", \"right\", \"left\"]\n  },\n  popperClass: {\n    type: String,\n    default: \"\"\n  },\n  teleported: useTooltipContentProps.teleported,\n  tagType: {\n    ...tagProps.type,\n    default: \"info\"\n  },\n  tagEffect: {\n    ...tagProps.effect,\n    default: \"light\"\n  },\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  persistent: {\n    type: Boolean,\n    default: true\n  },\n  ...useEmptyValuesProps\n});\nconst cascaderEmits = {\n  [UPDATE_MODEL_EVENT]: _ => true,\n  [CHANGE_EVENT]: _ => true,\n  focus: evt => evt instanceof FocusEvent,\n  blur: evt => evt instanceof FocusEvent,\n  clear: () => true,\n  visibleChange: val => isBoolean(val),\n  expandChange: val => !!val,\n  removeTag: val => !!val\n};\nexport { cascaderEmits, cascaderProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}