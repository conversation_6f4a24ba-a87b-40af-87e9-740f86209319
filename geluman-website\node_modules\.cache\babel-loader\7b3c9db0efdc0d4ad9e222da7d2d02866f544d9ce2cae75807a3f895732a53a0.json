{"ast": null, "code": "import { defineComponent, inject, ref, toRef, openBlock, createBlock, unref, normalizeClass, withCtx, renderSlot } from 'vue';\nimport '../../popper/index.mjs';\nimport { TOOLTIP_INJECTION_KEY } from './constants.mjs';\nimport { useTooltipTriggerProps } from './trigger.mjs';\nimport { whenTrigger } from './utils.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport ElPopperTrigger from '../../popper/src/trigger2.mjs';\nimport { composeEventHandlers } from '../../../utils/dom/event.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTooltipTrigger\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: useTooltipTriggerProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"tooltip\");\n    const {\n      controlled,\n      id,\n      open,\n      onOpen,\n      onClose,\n      onToggle\n    } = inject(TOOLTIP_INJECTION_KEY, void 0);\n    const triggerRef = ref(null);\n    const stopWhenControlledOrDisabled = () => {\n      if (unref(controlled) || props.disabled) {\n        return true;\n      }\n    };\n    const trigger = toRef(props, \"trigger\");\n    const onMouseenter = composeEventHandlers(stopWhenControlledOrDisabled, whenTrigger(trigger, \"hover\", onOpen));\n    const onMouseleave = composeEventHandlers(stopWhenControlledOrDisabled, whenTrigger(trigger, \"hover\", onClose));\n    const onClick = composeEventHandlers(stopWhenControlledOrDisabled, whenTrigger(trigger, \"click\", e => {\n      if (e.button === 0) {\n        onToggle(e);\n      }\n    }));\n    const onFocus = composeEventHandlers(stopWhenControlledOrDisabled, whenTrigger(trigger, \"focus\", onOpen));\n    const onBlur = composeEventHandlers(stopWhenControlledOrDisabled, whenTrigger(trigger, \"focus\", onClose));\n    const onContextMenu = composeEventHandlers(stopWhenControlledOrDisabled, whenTrigger(trigger, \"contextmenu\", e => {\n      e.preventDefault();\n      onToggle(e);\n    }));\n    const onKeydown = composeEventHandlers(stopWhenControlledOrDisabled, e => {\n      const {\n        code\n      } = e;\n      if (props.triggerKeys.includes(code)) {\n        e.preventDefault();\n        onToggle(e);\n      }\n    });\n    expose({\n      triggerRef\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElPopperTrigger), {\n        id: unref(id),\n        \"virtual-ref\": _ctx.virtualRef,\n        open: unref(open),\n        \"virtual-triggering\": _ctx.virtualTriggering,\n        class: normalizeClass(unref(ns).e(\"trigger\")),\n        onBlur: unref(onBlur),\n        onClick: unref(onClick),\n        onContextmenu: unref(onContextMenu),\n        onFocus: unref(onFocus),\n        onMouseenter: unref(onMouseenter),\n        onMouseleave: unref(onMouseleave),\n        onKeydown: unref(onKeydown)\n      }, {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 8, [\"id\", \"virtual-ref\", \"open\", \"virtual-triggering\", \"class\", \"onBlur\", \"onClick\", \"onContextmenu\", \"onFocus\", \"onMouseenter\", \"onMouseleave\", \"onKeydown\"]);\n    };\n  }\n});\nvar ElTooltipTrigger = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"trigger.vue\"]]);\nexport { ElTooltipTrigger as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}