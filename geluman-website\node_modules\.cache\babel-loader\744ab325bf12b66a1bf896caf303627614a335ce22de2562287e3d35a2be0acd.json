{"ast": null, "code": "import { datePickerSharedProps, selectionModeWithDefault } from './shared.mjs';\nimport { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nconst basicDateTableProps = buildProps({\n  ...datePickerSharedProps,\n  cellClassName: {\n    type: definePropType(Function)\n  },\n  showWeekNumber: Boolean,\n  selectionMode: selectionModeWithDefault(\"date\")\n});\nconst basicDateTableEmits = [\"changerange\", \"pick\", \"select\"];\nexport { basicDateTableEmits, basicDateTableProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}