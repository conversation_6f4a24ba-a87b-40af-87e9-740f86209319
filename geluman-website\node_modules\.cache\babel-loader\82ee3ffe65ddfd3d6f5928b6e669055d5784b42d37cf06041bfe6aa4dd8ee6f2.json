{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, renderSlot, createCommentVNode } from 'vue';\nimport { dividerProps } from './divider.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElDivider\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: dividerProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"divider\");\n    const dividerStyle = computed(() => {\n      return ns.cssVar({\n        \"border-style\": props.borderStyle\n      });\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ns).b(), unref(ns).m(_ctx.direction)]),\n        style: normalizeStyle(unref(dividerStyle)),\n        role: \"separator\"\n      }, [_ctx.$slots.default && _ctx.direction !== \"vertical\" ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass([unref(ns).e(\"text\"), unref(ns).is(_ctx.contentPosition)])\n      }, [renderSlot(_ctx.$slots, \"default\")], 2)) : createCommentVNode(\"v-if\", true)], 6);\n    };\n  }\n});\nvar Divider = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"divider.vue\"]]);\nexport { Divider as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}