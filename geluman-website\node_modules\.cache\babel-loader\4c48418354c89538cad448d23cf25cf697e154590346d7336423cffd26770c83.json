{"ast": null, "code": "import Menu from './src/menu.mjs';\nexport { menuEmits, menuProps } from './src/menu.mjs';\nimport MenuItem from './src/menu-item2.mjs';\nimport MenuItemGroup from './src/menu-item-group2.mjs';\nimport SubMenu from './src/sub-menu.mjs';\nexport { subMenuProps } from './src/sub-menu.mjs';\nexport { menuItemEmits, menuItemProps } from './src/menu-item.mjs';\nexport { menuItemGroupProps } from './src/menu-item-group.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElMenu = withInstall(Menu, {\n  MenuItem,\n  MenuItemGroup,\n  SubMenu\n});\nconst ElMenuItem = withNoopInstall(MenuItem);\nconst ElMenuItemGroup = withNoopInstall(MenuItemGroup);\nconst ElSubMenu = withNoopInstall(SubMenu);\nexport { ElMenu, ElMenuItem, ElMenuItemGroup, ElSubMenu, ElMenu as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}