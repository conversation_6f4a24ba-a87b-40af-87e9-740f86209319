{"ast": null, "code": "import Pagination from './src/pagination.mjs';\nexport { paginationEmits, paginationProps } from './src/pagination.mjs';\nexport { elPaginationKey } from './src/constants.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElPagination = withInstall(Pagination);\nexport { ElPagination, ElPagination as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}