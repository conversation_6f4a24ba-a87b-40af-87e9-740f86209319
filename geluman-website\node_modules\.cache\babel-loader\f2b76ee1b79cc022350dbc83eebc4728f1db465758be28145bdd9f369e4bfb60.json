{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { computed } from 'vue';\nconst useMarks = props => {\n  return computed(() => {\n    if (!props.marks) {\n      return [];\n    }\n    const marksKeys = Object.keys(props.marks);\n    return marksKeys.map(Number.parseFloat).sort((a, b) => a - b).filter(point => point <= props.max && point >= props.min).map(point => ({\n      point,\n      position: (point - props.min) * 100 / (props.max - props.min),\n      mark: props.marks[point]\n    }));\n  });\n};\nexport { useMarks };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}