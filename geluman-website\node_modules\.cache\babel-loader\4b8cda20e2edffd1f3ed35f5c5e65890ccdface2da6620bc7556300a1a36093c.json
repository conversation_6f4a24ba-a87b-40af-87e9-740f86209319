{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, getCurrentInstance, ref, computed, onBeforeMount, onMounted, onBeforeUnmount, Fragment, h } from 'vue';\nimport { ElCheckbox } from '../../../checkbox/index.mjs';\nimport { cellStarts } from '../config.mjs';\nimport { mergeOptions, compose } from '../util.mjs';\nimport useWatcher from './watcher-helper.mjs';\nimport useRender from './render-helper.mjs';\nimport defaultProps from './defaults.mjs';\nimport { isUndefined } from '../../../../utils/types.mjs';\nimport { isArray, isString } from '@vue/shared';\nlet columnIdSeed = 1;\nvar ElTableColumn = defineComponent({\n  name: \"ElTableColumn\",\n  components: {\n    ElCheckbox\n  },\n  props: defaultProps,\n  setup(props, {\n    slots\n  }) {\n    const instance = getCurrentInstance();\n    const columnConfig = ref({});\n    const owner = computed(() => {\n      let parent2 = instance.parent;\n      while (parent2 && !parent2.tableId) {\n        parent2 = parent2.parent;\n      }\n      return parent2;\n    });\n    const {\n      registerNormalWatchers,\n      registerComplexWatchers\n    } = useWatcher(owner, props);\n    const {\n      columnId,\n      isSubColumn,\n      realHeaderAlign,\n      columnOrTableParent,\n      setColumnWidth,\n      setColumnForcedProps,\n      setColumnRenders,\n      getPropsData,\n      getColumnElIndex,\n      realAlign,\n      updateColumnOrder\n    } = useRender(props, slots, owner);\n    const parent = columnOrTableParent.value;\n    columnId.value = `${parent.tableId || parent.columnId}_column_${columnIdSeed++}`;\n    onBeforeMount(() => {\n      isSubColumn.value = owner.value !== parent;\n      const type = props.type || \"default\";\n      const sortable = props.sortable === \"\" ? true : props.sortable;\n      const showOverflowTooltip = type === \"selection\" ? false : isUndefined(props.showOverflowTooltip) ? parent.props.showOverflowTooltip : props.showOverflowTooltip;\n      const tooltipFormatter = isUndefined(props.tooltipFormatter) ? parent.props.tooltipFormatter : props.tooltipFormatter;\n      const defaults = {\n        ...cellStarts[type],\n        id: columnId.value,\n        type,\n        property: props.prop || props.property,\n        align: realAlign,\n        headerAlign: realHeaderAlign,\n        showOverflowTooltip,\n        tooltipFormatter,\n        filterable: props.filters || props.filterMethod,\n        filteredValue: [],\n        filterPlacement: \"\",\n        filterClassName: \"\",\n        isColumnGroup: false,\n        isSubColumn: false,\n        filterOpened: false,\n        sortable,\n        index: props.index,\n        rawColumnKey: instance.vnode.key\n      };\n      const basicProps = [\"columnKey\", \"label\", \"className\", \"labelClassName\", \"type\", \"renderHeader\", \"formatter\", \"fixed\", \"resizable\"];\n      const sortProps = [\"sortMethod\", \"sortBy\", \"sortOrders\"];\n      const selectProps = [\"selectable\", \"reserveSelection\"];\n      const filterProps = [\"filterMethod\", \"filters\", \"filterMultiple\", \"filterOpened\", \"filteredValue\", \"filterPlacement\", \"filterClassName\"];\n      let column = getPropsData(basicProps, sortProps, selectProps, filterProps);\n      column = mergeOptions(defaults, column);\n      const chains = compose(setColumnRenders, setColumnWidth, setColumnForcedProps);\n      column = chains(column);\n      columnConfig.value = column;\n      registerNormalWatchers();\n      registerComplexWatchers();\n    });\n    onMounted(() => {\n      var _a;\n      const parent2 = columnOrTableParent.value;\n      const children = isSubColumn.value ? parent2.vnode.el.children : (_a = parent2.refs.hiddenColumns) == null ? void 0 : _a.children;\n      const getColumnIndex = () => getColumnElIndex(children || [], instance.vnode.el);\n      columnConfig.value.getColumnIndex = getColumnIndex;\n      const columnIndex = getColumnIndex();\n      columnIndex > -1 && owner.value.store.commit(\"insertColumn\", columnConfig.value, isSubColumn.value ? parent2.columnConfig.value : null, updateColumnOrder);\n    });\n    onBeforeUnmount(() => {\n      const getColumnIndex = columnConfig.value.getColumnIndex;\n      const columnIndex = getColumnIndex ? getColumnIndex() : -1;\n      columnIndex > -1 && owner.value.store.commit(\"removeColumn\", columnConfig.value, isSubColumn.value ? parent.columnConfig.value : null, updateColumnOrder);\n    });\n    instance.columnId = columnId.value;\n    instance.columnConfig = columnConfig;\n    return;\n  },\n  render() {\n    var _a, _b, _c;\n    try {\n      const renderDefault = (_b = (_a = this.$slots).default) == null ? void 0 : _b.call(_a, {\n        row: {},\n        column: {},\n        $index: -1\n      });\n      const children = [];\n      if (isArray(renderDefault)) {\n        for (const childNode of renderDefault) {\n          if (((_c = childNode.type) == null ? void 0 : _c.name) === \"ElTableColumn\" || childNode.shapeFlag & 2) {\n            children.push(childNode);\n          } else if (childNode.type === Fragment && isArray(childNode.children)) {\n            childNode.children.forEach(vnode2 => {\n              if ((vnode2 == null ? void 0 : vnode2.patchFlag) !== 1024 && !isString(vnode2 == null ? void 0 : vnode2.children)) {\n                children.push(vnode2);\n              }\n            });\n          }\n        }\n      }\n      const vnode = h(\"div\", children);\n      return vnode;\n    } catch (e) {\n      return h(\"div\", []);\n    }\n  }\n});\nexport { ElTableColumn as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}