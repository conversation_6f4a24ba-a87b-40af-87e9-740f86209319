{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeStyle as _normalizeStyle, resolveDynamicComponent as _resolveDynamicComponent, createBlock as _createBlock, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, unref as _unref, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"about-page\"\n};\nconst _hoisted_2 = {\n  class: \"about-hero\"\n};\nconst _hoisted_3 = {\n  class: \"hero-background\"\n};\nconst _hoisted_4 = {\n  class: \"tech-particles\"\n};\nconst _hoisted_5 = {\n  class: \"values\"\n};\nconst _hoisted_6 = {\n  class: \"container\"\n};\nconst _hoisted_7 = {\n  class: \"values-grid\"\n};\nconst _hoisted_8 = {\n  class: \"value-icon\"\n};\nconst _hoisted_9 = {\n  class: \"contact\"\n};\nconst _hoisted_10 = {\n  class: \"container\"\n};\nconst _hoisted_11 = {\n  class: \"contact-content\"\n};\nconst _hoisted_12 = {\n  class: \"contact-info\"\n};\nconst _hoisted_13 = {\n  class: \"info-item\"\n};\nconst _hoisted_14 = {\n  class: \"info-item\"\n};\nimport { Promotion, Medal, Cpu, User, Message, Location } from \"@element-plus/icons-vue\";\nconst __default__ = {\n  name: \"AboutPage\"\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props) {\n    const values = [{\n      icon: Promotion,\n      title: \"创新驱动\",\n      description: \"持续创新，推动技术边界，为用户带来更好的产品体验\"\n    }, {\n      icon: Medal,\n      title: \"匠心品质\",\n      description: \"专注细节，精益求精，打造极致用户体验\"\n    }, {\n      icon: Cpu,\n      title: \"技术领先\",\n      description: \"拥抱新技术，保持技术敏锐度，引领行业发展\"\n    }, {\n      icon: User,\n      title: \"用户至上\",\n      description: \"以用户需求为中心，持续优化产品，提供贴心服务\"\n    }];\n    return (_ctx, _cache) => {\n      const _component_el_icon = _resolveComponent(\"el-icon\");\n      const _component_el_card = _resolveComponent(\"el-card\");\n      return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"section\", _hoisted_2, [_cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n        class: \"about-content\"\n      }, [_createElementVNode(\"h1\", {\n        class: \"title\"\n      }, \"关于格鲁曼\"), _createElementVNode(\"p\", {\n        class: \"subtitle\"\n      }, \"用创新驱动技术，以匠心创造价值\")], -1)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(30, n => {\n        return _createElementVNode(\"span\", {\n          key: n,\n          class: \"particle\",\n          style: _normalizeStyle({\n            '--delay': `${Math.random() * 5}s`,\n            '--size': `${Math.random() * 3 + 1}px`,\n            '--x': `${Math.random() * 100}%`,\n            '--y': `${Math.random() * 100}%`\n          })\n        }, null, 4);\n      }), 64))]), _cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n        class: \"grid-background\"\n      }, null, -1))])]), _cache[6] || (_cache[6] = _createStaticVNode(\"<section class=\\\"company-intro\\\" data-v-369be01a><div class=\\\"container\\\" data-v-369be01a><div class=\\\"intro-grid\\\" data-v-369be01a><div class=\\\"intro-text\\\" data-v-369be01a><h2 data-v-369be01a>我们的故事</h2><p data-v-369be01a> 成立于2024年，格鲁曼专注于开发浏览器插件和休闲小游戏，致力于为用户提供卓越的在线体验。&quot;匠心创造价值&quot;是我们的核心理念。我们相信，每一个细节都至关重要，只有通过精细打磨和不断优化，才能创造出真正有价值的产品。 </p></div><div class=\\\"intro-decoration\\\" data-v-369be01a><div class=\\\"tech-circles\\\" data-v-369be01a><span class=\\\"circle circle-1\\\" data-v-369be01a></span><span class=\\\"circle circle-2\\\" data-v-369be01a></span><span class=\\\"circle circle-3\\\" data-v-369be01a></span></div><div class=\\\"code-lines\\\" data-v-369be01a><span class=\\\"line\\\" data-v-369be01a></span><span class=\\\"line\\\" data-v-369be01a></span><span class=\\\"line\\\" data-v-369be01a></span></div></div></div></div></section>\", 1)), _createElementVNode(\"section\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[2] || (_cache[2] = _createElementVNode(\"h2\", {\n        class: \"section-title\"\n      }, \"我们的价值观\", -1)), _createElementVNode(\"div\", _hoisted_7, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(values, (value, index) => {\n        return _createVNode(_component_el_card, {\n          key: value.title,\n          class: \"value-card\"\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_icon, {\n            size: 32\n          }, {\n            default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(value.icon)))]),\n            _: 2\n          }, 1024)]), _createElementVNode(\"h3\", null, _toDisplayString(value.title), 1), _createElementVNode(\"p\", null, _toDisplayString(value.description), 1)]),\n          _: 2\n        }, 1024);\n      }), 64))])])]), _createElementVNode(\"section\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_cache[5] || (_cache[5] = _createElementVNode(\"h2\", {\n        class: \"section-title\"\n      }, \"联系我们\", -1)), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_unref(Message))]),\n        _: 1\n      }), _cache[3] || (_cache[3] = _createElementVNode(\"span\", null, \"<EMAIL>\", -1))]), _createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_unref(Location))]),\n        _: 1\n      }), _cache[4] || (_cache[4] = _createElementVNode(\"span\", null, \"中国·成都\", -1))])])])])])]);\n    };\n  }\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}