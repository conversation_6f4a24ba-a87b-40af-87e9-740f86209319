{"ast": null, "code": "import { defineComponent, ref, computed, onMounted, watch, openBlock, createBlock, Transition, unref, withCtx, withDirectives, createElementVNode, normalizeClass, normalizeStyle, createCommentVNode, resolveDynamicComponent, renderSlot, createElementBlock, toDisplayString, Fragment, withModifiers, createVNode, vShow, nextTick } from 'vue';\nimport { useEventListener, useResizeObserver, useTimeoutFn } from '@vueuse/core';\nimport { ElBadge } from '../../badge/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { messageProps, messageEmits } from './message.mjs';\nimport { getLastOffset, getOffsetOrSpace } from './instance.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useGlobalComponentSettings } from '../../config-provider/src/hooks/use-global-config.mjs';\nimport { TypeComponentsMap, TypeComponents } from '../../../utils/vue/icon.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nconst __default__ = defineComponent({\n  name: \"ElMessage\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: messageProps,\n  emits: messageEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const {\n      Close\n    } = TypeComponents;\n    const isStartTransition = ref(false);\n    const {\n      ns,\n      zIndex\n    } = useGlobalComponentSettings(\"message\");\n    const {\n      currentZIndex,\n      nextZIndex\n    } = zIndex;\n    const messageRef = ref();\n    const visible = ref(false);\n    const height = ref(0);\n    let stopTimer = void 0;\n    const badgeType = computed(() => props.type ? props.type === \"error\" ? \"danger\" : props.type : \"info\");\n    const typeClass = computed(() => {\n      const type = props.type;\n      return {\n        [ns.bm(\"icon\", type)]: type && TypeComponentsMap[type]\n      };\n    });\n    const iconComponent = computed(() => props.icon || TypeComponentsMap[props.type] || \"\");\n    const lastOffset = computed(() => getLastOffset(props.id));\n    const offset = computed(() => getOffsetOrSpace(props.id, props.offset) + lastOffset.value);\n    const bottom = computed(() => height.value + offset.value);\n    const customStyle = computed(() => ({\n      top: `${offset.value}px`,\n      zIndex: currentZIndex.value\n    }));\n    function startTimer() {\n      if (props.duration === 0) return;\n      ({\n        stop: stopTimer\n      } = useTimeoutFn(() => {\n        close();\n      }, props.duration));\n    }\n    function clearTimer() {\n      stopTimer == null ? void 0 : stopTimer();\n    }\n    function close() {\n      visible.value = false;\n      nextTick(() => {\n        var _a;\n        if (!isStartTransition.value) {\n          (_a = props.onClose) == null ? void 0 : _a.call(props);\n          emit(\"destroy\");\n        }\n      });\n    }\n    function keydown({\n      code\n    }) {\n      if (code === EVENT_CODE.esc) {\n        close();\n      }\n    }\n    onMounted(() => {\n      startTimer();\n      nextZIndex();\n      visible.value = true;\n    });\n    watch(() => props.repeatNum, () => {\n      clearTimer();\n      startTimer();\n    });\n    useEventListener(document, \"keydown\", keydown);\n    useResizeObserver(messageRef, () => {\n      height.value = messageRef.value.getBoundingClientRect().height;\n    });\n    expose({\n      visible,\n      bottom,\n      close\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(Transition, {\n        name: unref(ns).b(\"fade\"),\n        onBeforeEnter: $event => isStartTransition.value = true,\n        onBeforeLeave: _ctx.onClose,\n        onAfterLeave: $event => _ctx.$emit(\"destroy\"),\n        persisted: \"\"\n      }, {\n        default: withCtx(() => [withDirectives(createElementVNode(\"div\", {\n          id: _ctx.id,\n          ref_key: \"messageRef\",\n          ref: messageRef,\n          class: normalizeClass([unref(ns).b(), {\n            [unref(ns).m(_ctx.type)]: _ctx.type\n          }, unref(ns).is(\"closable\", _ctx.showClose), unref(ns).is(\"plain\", _ctx.plain), _ctx.customClass]),\n          style: normalizeStyle(unref(customStyle)),\n          role: \"alert\",\n          onMouseenter: clearTimer,\n          onMouseleave: startTimer\n        }, [_ctx.repeatNum > 1 ? (openBlock(), createBlock(unref(ElBadge), {\n          key: 0,\n          value: _ctx.repeatNum,\n          type: unref(badgeType),\n          class: normalizeClass(unref(ns).e(\"badge\"))\n        }, null, 8, [\"value\", \"type\", \"class\"])) : createCommentVNode(\"v-if\", true), unref(iconComponent) ? (openBlock(), createBlock(unref(ElIcon), {\n          key: 1,\n          class: normalizeClass([unref(ns).e(\"icon\"), unref(typeClass)])\n        }, {\n          default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(iconComponent))))]),\n          _: 1\n        }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true), renderSlot(_ctx.$slots, \"default\", {}, () => [!_ctx.dangerouslyUseHTMLString ? (openBlock(), createElementBlock(\"p\", {\n          key: 0,\n          class: normalizeClass(unref(ns).e(\"content\"))\n        }, toDisplayString(_ctx.message), 3)) : (openBlock(), createElementBlock(Fragment, {\n          key: 1\n        }, [createCommentVNode(\" Caution here, message could've been compromised, never use user's input as message \"), createElementVNode(\"p\", {\n          class: normalizeClass(unref(ns).e(\"content\")),\n          innerHTML: _ctx.message\n        }, null, 10, [\"innerHTML\"])], 2112))]), _ctx.showClose ? (openBlock(), createBlock(unref(ElIcon), {\n          key: 2,\n          class: normalizeClass(unref(ns).e(\"closeBtn\")),\n          onClick: withModifiers(close, [\"stop\"])\n        }, {\n          default: withCtx(() => [createVNode(unref(Close))]),\n          _: 1\n        }, 8, [\"class\", \"onClick\"])) : createCommentVNode(\"v-if\", true)], 46, [\"id\"]), [[vShow, visible.value]])]),\n        _: 3\n      }, 8, [\"name\", \"onBeforeEnter\", \"onBeforeLeave\", \"onAfterLeave\"]);\n    };\n  }\n});\nvar MessageConstructor = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"message.vue\"]]);\nexport { MessageConstructor as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}