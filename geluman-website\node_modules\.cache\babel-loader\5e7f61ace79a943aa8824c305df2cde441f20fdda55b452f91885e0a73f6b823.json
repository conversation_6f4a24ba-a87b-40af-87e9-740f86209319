{"ast": null, "code": "import { defineComponent, inject, provide, unref, createVNode, ref, computed, watch } from 'vue';\nimport { TableV2InjectionKey } from './tokens.mjs';\nimport { tableV2GridProps } from './grid.mjs';\nimport { sum } from './utils.mjs';\nimport Header from './components/header.mjs';\nimport DynamicSizeGrid from '../../virtual-list/src/components/dynamic-size-grid.mjs';\nimport FixedSizeGrid from '../../virtual-list/src/components/fixed-size-grid.mjs';\nimport { isObject } from '@vue/shared';\nimport { isNumber } from '../../../utils/types.mjs';\nconst COMPONENT_NAME = \"ElTableV2Grid\";\nconst useTableGrid = props => {\n  const headerRef = ref();\n  const bodyRef = ref();\n  const scrollLeft = ref(0);\n  const totalHeight = computed(() => {\n    const {\n      data,\n      rowHeight,\n      estimatedRowHeight\n    } = props;\n    if (estimatedRowHeight) {\n      return;\n    }\n    return data.length * rowHeight;\n  });\n  const fixedRowHeight = computed(() => {\n    const {\n      fixedData,\n      rowHeight\n    } = props;\n    return ((fixedData == null ? void 0 : fixedData.length) || 0) * rowHeight;\n  });\n  const headerHeight = computed(() => sum(props.headerHeight));\n  const gridHeight = computed(() => {\n    const {\n      height\n    } = props;\n    return Math.max(0, height - unref(headerHeight) - unref(fixedRowHeight));\n  });\n  const hasHeader = computed(() => {\n    return unref(headerHeight) + unref(fixedRowHeight) > 0;\n  });\n  const itemKey = ({\n    data,\n    rowIndex\n  }) => data[rowIndex][props.rowKey];\n  function onItemRendered({\n    rowCacheStart,\n    rowCacheEnd,\n    rowVisibleStart,\n    rowVisibleEnd\n  }) {\n    var _a;\n    (_a = props.onRowsRendered) == null ? void 0 : _a.call(props, {\n      rowCacheStart,\n      rowCacheEnd,\n      rowVisibleStart,\n      rowVisibleEnd\n    });\n  }\n  function resetAfterRowIndex(index, forceUpdate2) {\n    var _a;\n    (_a = bodyRef.value) == null ? void 0 : _a.resetAfterRowIndex(index, forceUpdate2);\n  }\n  function scrollTo(leftOrOptions, top) {\n    const header$ = unref(headerRef);\n    const body$ = unref(bodyRef);\n    if (isObject(leftOrOptions)) {\n      header$ == null ? void 0 : header$.scrollToLeft(leftOrOptions.scrollLeft);\n      scrollLeft.value = leftOrOptions.scrollLeft;\n      body$ == null ? void 0 : body$.scrollTo(leftOrOptions);\n    } else {\n      header$ == null ? void 0 : header$.scrollToLeft(leftOrOptions);\n      scrollLeft.value = leftOrOptions;\n      body$ == null ? void 0 : body$.scrollTo({\n        scrollLeft: leftOrOptions,\n        scrollTop: top\n      });\n    }\n  }\n  function scrollToTop(scrollTop) {\n    var _a;\n    (_a = unref(bodyRef)) == null ? void 0 : _a.scrollTo({\n      scrollTop\n    });\n  }\n  function scrollToRow(row, strategy) {\n    var _a;\n    (_a = unref(bodyRef)) == null ? void 0 : _a.scrollToItem(row, 1, strategy);\n  }\n  function forceUpdate() {\n    var _a, _b;\n    (_a = unref(bodyRef)) == null ? void 0 : _a.$forceUpdate();\n    (_b = unref(headerRef)) == null ? void 0 : _b.$forceUpdate();\n  }\n  watch(() => props.bodyWidth, () => {\n    var _a;\n    if (isNumber(props.estimatedRowHeight)) (_a = bodyRef.value) == null ? void 0 : _a.resetAfter({\n      columnIndex: 0\n    }, false);\n  });\n  return {\n    bodyRef,\n    forceUpdate,\n    fixedRowHeight,\n    gridHeight,\n    hasHeader,\n    headerHeight,\n    headerRef,\n    totalHeight,\n    itemKey,\n    onItemRendered,\n    resetAfterRowIndex,\n    scrollTo,\n    scrollToTop,\n    scrollToRow,\n    scrollLeft\n  };\n};\nconst TableGrid = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2GridProps,\n  setup(props, {\n    slots,\n    expose\n  }) {\n    const {\n      ns\n    } = inject(TableV2InjectionKey);\n    const {\n      bodyRef,\n      fixedRowHeight,\n      gridHeight,\n      hasHeader,\n      headerRef,\n      headerHeight,\n      totalHeight,\n      forceUpdate,\n      itemKey,\n      onItemRendered,\n      resetAfterRowIndex,\n      scrollTo,\n      scrollToTop,\n      scrollToRow,\n      scrollLeft\n    } = useTableGrid(props);\n    provide(\"tableV2GridScrollLeft\", scrollLeft);\n    expose({\n      forceUpdate,\n      totalHeight,\n      scrollTo,\n      scrollToTop,\n      scrollToRow,\n      resetAfterRowIndex\n    });\n    const getColumnWidth = () => props.bodyWidth;\n    return () => {\n      const {\n        cache,\n        columns,\n        data,\n        fixedData,\n        useIsScrolling,\n        scrollbarAlwaysOn,\n        scrollbarEndGap,\n        scrollbarStartGap,\n        style,\n        rowHeight,\n        bodyWidth,\n        estimatedRowHeight,\n        headerWidth,\n        height,\n        width,\n        getRowHeight,\n        onScroll\n      } = props;\n      const isDynamicRowEnabled = isNumber(estimatedRowHeight);\n      const Grid = isDynamicRowEnabled ? DynamicSizeGrid : FixedSizeGrid;\n      const _headerHeight = unref(headerHeight);\n      return createVNode(\"div\", {\n        \"role\": \"table\",\n        \"class\": [ns.e(\"table\"), props.class],\n        \"style\": style\n      }, [createVNode(Grid, {\n        \"ref\": bodyRef,\n        \"data\": data,\n        \"useIsScrolling\": useIsScrolling,\n        \"itemKey\": itemKey,\n        \"columnCache\": 0,\n        \"columnWidth\": isDynamicRowEnabled ? getColumnWidth : bodyWidth,\n        \"totalColumn\": 1,\n        \"totalRow\": data.length,\n        \"rowCache\": cache,\n        \"rowHeight\": isDynamicRowEnabled ? getRowHeight : rowHeight,\n        \"width\": width,\n        \"height\": unref(gridHeight),\n        \"class\": ns.e(\"body\"),\n        \"role\": \"rowgroup\",\n        \"scrollbarStartGap\": scrollbarStartGap,\n        \"scrollbarEndGap\": scrollbarEndGap,\n        \"scrollbarAlwaysOn\": scrollbarAlwaysOn,\n        \"onScroll\": onScroll,\n        \"onItemRendered\": onItemRendered,\n        \"perfMode\": false\n      }, {\n        default: params => {\n          var _a;\n          const rowData = data[params.rowIndex];\n          return (_a = slots.row) == null ? void 0 : _a.call(slots, {\n            ...params,\n            columns,\n            rowData\n          });\n        }\n      }), unref(hasHeader) && createVNode(Header, {\n        \"ref\": headerRef,\n        \"class\": ns.e(\"header-wrapper\"),\n        \"columns\": columns,\n        \"headerData\": data,\n        \"headerHeight\": props.headerHeight,\n        \"fixedHeaderData\": fixedData,\n        \"rowWidth\": headerWidth,\n        \"rowHeight\": rowHeight,\n        \"width\": width,\n        \"height\": Math.min(_headerHeight + unref(fixedRowHeight), height)\n      }, {\n        dynamic: slots.header,\n        fixed: slots.row\n      })]);\n    };\n  }\n});\nvar Table = TableGrid;\nexport { Table as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}