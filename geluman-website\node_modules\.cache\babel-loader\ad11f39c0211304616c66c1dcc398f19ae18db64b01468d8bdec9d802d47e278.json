{"ast": null, "code": "import { defineComponent, inject, ref, computed, unref, onMounted, nextTick, watch, openBlock, createElementBlock, normalizeClass, Fragment, renderList, createBlock, withCtx, createTextVNode, toDisplayString, createCommentVNode, withDirectives, createVNode, createElementVNode } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport { ElScrollbar } from '../../../scrollbar/index.mjs';\nimport { ElIcon } from '../../../icon/index.mjs';\nimport { ArrowUp, ArrowDown } from '@element-plus/icons-vue';\nimport { timeUnits, DEFAULT_FORMATS_TIME } from '../constants.mjs';\nimport { buildTimeList } from '../utils.mjs';\nimport { basicTimeSpinnerProps } from '../props/basic-time-spinner.mjs';\nimport { getTimeLists } from '../composables/use-time-picker.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { vRepeatClick } from '../../../../directives/repeat-click/index.mjs';\nimport { CHANGE_EVENT } from '../../../../constants/event.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { getStyle } from '../../../../utils/dom/style.mjs';\nimport { isNumber } from '../../../../utils/types.mjs';\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"basic-time-spinner\",\n  props: basicTimeSpinnerProps,\n  emits: [CHANGE_EVENT, \"select-range\", \"set-option\"],\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const pickerBase = inject(\"EP_PICKER_BASE\");\n    const {\n      isRange,\n      format\n    } = pickerBase.props;\n    const ns = useNamespace(\"time\");\n    const {\n      getHoursList,\n      getMinutesList,\n      getSecondsList\n    } = getTimeLists(props.disabledHours, props.disabledMinutes, props.disabledSeconds);\n    let isScrolling = false;\n    const currentScrollbar = ref();\n    const listHoursRef = ref();\n    const listMinutesRef = ref();\n    const listSecondsRef = ref();\n    const listRefsMap = {\n      hours: listHoursRef,\n      minutes: listMinutesRef,\n      seconds: listSecondsRef\n    };\n    const spinnerItems = computed(() => {\n      return props.showSeconds ? timeUnits : timeUnits.slice(0, 2);\n    });\n    const timePartials = computed(() => {\n      const {\n        spinnerDate\n      } = props;\n      const hours = spinnerDate.hour();\n      const minutes = spinnerDate.minute();\n      const seconds = spinnerDate.second();\n      return {\n        hours,\n        minutes,\n        seconds\n      };\n    });\n    const timeList = computed(() => {\n      const {\n        hours,\n        minutes\n      } = unref(timePartials);\n      const {\n        role,\n        spinnerDate\n      } = props;\n      const compare = !isRange ? spinnerDate : void 0;\n      return {\n        hours: getHoursList(role, compare),\n        minutes: getMinutesList(hours, role, compare),\n        seconds: getSecondsList(hours, minutes, role, compare)\n      };\n    });\n    const arrowControlTimeList = computed(() => {\n      const {\n        hours,\n        minutes,\n        seconds\n      } = unref(timePartials);\n      return {\n        hours: buildTimeList(hours, 23),\n        minutes: buildTimeList(minutes, 59),\n        seconds: buildTimeList(seconds, 59)\n      };\n    });\n    const debouncedResetScroll = debounce(type => {\n      isScrolling = false;\n      adjustCurrentSpinner(type);\n    }, 200);\n    const getAmPmFlag = hour => {\n      const shouldShowAmPm = !!props.amPmMode;\n      if (!shouldShowAmPm) return \"\";\n      const isCapital = props.amPmMode === \"A\";\n      let content = hour < 12 ? \" am\" : \" pm\";\n      if (isCapital) content = content.toUpperCase();\n      return content;\n    };\n    const emitSelectRange = type => {\n      let range = [0, 0];\n      if (!format || format === DEFAULT_FORMATS_TIME) {\n        switch (type) {\n          case \"hours\":\n            range = [0, 2];\n            break;\n          case \"minutes\":\n            range = [3, 5];\n            break;\n          case \"seconds\":\n            range = [6, 8];\n            break;\n        }\n      }\n      const [left, right] = range;\n      emit(\"select-range\", left, right);\n      currentScrollbar.value = type;\n    };\n    const adjustCurrentSpinner = type => {\n      adjustSpinner(type, unref(timePartials)[type]);\n    };\n    const adjustSpinners = () => {\n      adjustCurrentSpinner(\"hours\");\n      adjustCurrentSpinner(\"minutes\");\n      adjustCurrentSpinner(\"seconds\");\n    };\n    const getScrollbarElement = el => el.querySelector(`.${ns.namespace.value}-scrollbar__wrap`);\n    const adjustSpinner = (type, value) => {\n      if (props.arrowControl) return;\n      const scrollbar = unref(listRefsMap[type]);\n      if (scrollbar && scrollbar.$el) {\n        getScrollbarElement(scrollbar.$el).scrollTop = Math.max(0, value * typeItemHeight(type));\n      }\n    };\n    const typeItemHeight = type => {\n      const scrollbar = unref(listRefsMap[type]);\n      const listItem = scrollbar == null ? void 0 : scrollbar.$el.querySelector(\"li\");\n      if (listItem) {\n        return Number.parseFloat(getStyle(listItem, \"height\")) || 0;\n      }\n      return 0;\n    };\n    const onIncrement = () => {\n      scrollDown(1);\n    };\n    const onDecrement = () => {\n      scrollDown(-1);\n    };\n    const scrollDown = step => {\n      if (!currentScrollbar.value) {\n        emitSelectRange(\"hours\");\n      }\n      const label = currentScrollbar.value;\n      const now = unref(timePartials)[label];\n      const total = currentScrollbar.value === \"hours\" ? 24 : 60;\n      const next = findNextUnDisabled(label, now, step, total);\n      modifyDateField(label, next);\n      adjustSpinner(label, next);\n      nextTick(() => emitSelectRange(label));\n    };\n    const findNextUnDisabled = (type, now, step, total) => {\n      let next = (now + step + total) % total;\n      const list = unref(timeList)[type];\n      while (list[next] && next !== now) {\n        next = (next + step + total) % total;\n      }\n      return next;\n    };\n    const modifyDateField = (type, value) => {\n      const list = unref(timeList)[type];\n      const isDisabled = list[value];\n      if (isDisabled) return;\n      const {\n        hours,\n        minutes,\n        seconds\n      } = unref(timePartials);\n      let changeTo;\n      switch (type) {\n        case \"hours\":\n          changeTo = props.spinnerDate.hour(value).minute(minutes).second(seconds);\n          break;\n        case \"minutes\":\n          changeTo = props.spinnerDate.hour(hours).minute(value).second(seconds);\n          break;\n        case \"seconds\":\n          changeTo = props.spinnerDate.hour(hours).minute(minutes).second(value);\n          break;\n      }\n      emit(CHANGE_EVENT, changeTo);\n    };\n    const handleClick = (type, {\n      value,\n      disabled\n    }) => {\n      if (!disabled) {\n        modifyDateField(type, value);\n        emitSelectRange(type);\n        adjustSpinner(type, value);\n      }\n    };\n    const handleScroll = type => {\n      const scrollbar = unref(listRefsMap[type]);\n      if (!scrollbar) return;\n      isScrolling = true;\n      debouncedResetScroll(type);\n      const value = Math.min(Math.round((getScrollbarElement(scrollbar.$el).scrollTop - (scrollBarHeight(type) * 0.5 - 10) / typeItemHeight(type) + 3) / typeItemHeight(type)), type === \"hours\" ? 23 : 59);\n      modifyDateField(type, value);\n    };\n    const scrollBarHeight = type => {\n      return unref(listRefsMap[type]).$el.offsetHeight;\n    };\n    const bindScrollEvent = () => {\n      const bindFunction = type => {\n        const scrollbar = unref(listRefsMap[type]);\n        if (scrollbar && scrollbar.$el) {\n          getScrollbarElement(scrollbar.$el).onscroll = () => {\n            handleScroll(type);\n          };\n        }\n      };\n      bindFunction(\"hours\");\n      bindFunction(\"minutes\");\n      bindFunction(\"seconds\");\n    };\n    onMounted(() => {\n      nextTick(() => {\n        !props.arrowControl && bindScrollEvent();\n        adjustSpinners();\n        if (props.role === \"start\") emitSelectRange(\"hours\");\n      });\n    });\n    const setRef = (scrollbar, type) => {\n      listRefsMap[type].value = scrollbar != null ? scrollbar : void 0;\n    };\n    emit(\"set-option\", [`${props.role}_scrollDown`, scrollDown]);\n    emit(\"set-option\", [`${props.role}_emitSelectRange`, emitSelectRange]);\n    watch(() => props.spinnerDate, () => {\n      if (isScrolling) return;\n      adjustSpinners();\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ns).b(\"spinner\"), {\n          \"has-seconds\": _ctx.showSeconds\n        }])\n      }, [!_ctx.arrowControl ? (openBlock(true), createElementBlock(Fragment, {\n        key: 0\n      }, renderList(unref(spinnerItems), item => {\n        return openBlock(), createBlock(unref(ElScrollbar), {\n          key: item,\n          ref_for: true,\n          ref: scrollbar => setRef(scrollbar, item),\n          class: normalizeClass(unref(ns).be(\"spinner\", \"wrapper\")),\n          \"wrap-style\": \"max-height: inherit;\",\n          \"view-class\": unref(ns).be(\"spinner\", \"list\"),\n          noresize: \"\",\n          tag: \"ul\",\n          onMouseenter: $event => emitSelectRange(item),\n          onMousemove: $event => adjustCurrentSpinner(item)\n        }, {\n          default: withCtx(() => [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(timeList)[item], (disabled, key) => {\n            return openBlock(), createElementBlock(\"li\", {\n              key,\n              class: normalizeClass([unref(ns).be(\"spinner\", \"item\"), unref(ns).is(\"active\", key === unref(timePartials)[item]), unref(ns).is(\"disabled\", disabled)]),\n              onClick: $event => handleClick(item, {\n                value: key,\n                disabled\n              })\n            }, [item === \"hours\" ? (openBlock(), createElementBlock(Fragment, {\n              key: 0\n            }, [createTextVNode(toDisplayString((\"0\" + (_ctx.amPmMode ? key % 12 || 12 : key)).slice(-2)) + toDisplayString(getAmPmFlag(key)), 1)], 64)) : (openBlock(), createElementBlock(Fragment, {\n              key: 1\n            }, [createTextVNode(toDisplayString((\"0\" + key).slice(-2)), 1)], 64))], 10, [\"onClick\"]);\n          }), 128))]),\n          _: 2\n        }, 1032, [\"class\", \"view-class\", \"onMouseenter\", \"onMousemove\"]);\n      }), 128)) : createCommentVNode(\"v-if\", true), _ctx.arrowControl ? (openBlock(true), createElementBlock(Fragment, {\n        key: 1\n      }, renderList(unref(spinnerItems), item => {\n        return openBlock(), createElementBlock(\"div\", {\n          key: item,\n          class: normalizeClass([unref(ns).be(\"spinner\", \"wrapper\"), unref(ns).is(\"arrow\")]),\n          onMouseenter: $event => emitSelectRange(item)\n        }, [withDirectives((openBlock(), createBlock(unref(ElIcon), {\n          class: normalizeClass([\"arrow-up\", unref(ns).be(\"spinner\", \"arrow\")])\n        }, {\n          default: withCtx(() => [createVNode(unref(ArrowUp))]),\n          _: 1\n        }, 8, [\"class\"])), [[unref(vRepeatClick), onDecrement]]), withDirectives((openBlock(), createBlock(unref(ElIcon), {\n          class: normalizeClass([\"arrow-down\", unref(ns).be(\"spinner\", \"arrow\")])\n        }, {\n          default: withCtx(() => [createVNode(unref(ArrowDown))]),\n          _: 1\n        }, 8, [\"class\"])), [[unref(vRepeatClick), onIncrement]]), createElementVNode(\"ul\", {\n          class: normalizeClass(unref(ns).be(\"spinner\", \"list\"))\n        }, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(arrowControlTimeList)[item], (time, key) => {\n          return openBlock(), createElementBlock(\"li\", {\n            key,\n            class: normalizeClass([unref(ns).be(\"spinner\", \"item\"), unref(ns).is(\"active\", time === unref(timePartials)[item]), unref(ns).is(\"disabled\", unref(timeList)[item][time])])\n          }, [unref(isNumber)(time) ? (openBlock(), createElementBlock(Fragment, {\n            key: 0\n          }, [item === \"hours\" ? (openBlock(), createElementBlock(Fragment, {\n            key: 0\n          }, [createTextVNode(toDisplayString((\"0\" + (_ctx.amPmMode ? time % 12 || 12 : time)).slice(-2)) + toDisplayString(getAmPmFlag(time)), 1)], 64)) : (openBlock(), createElementBlock(Fragment, {\n            key: 1\n          }, [createTextVNode(toDisplayString((\"0\" + time).slice(-2)), 1)], 64))], 64)) : createCommentVNode(\"v-if\", true)], 2);\n        }), 128))], 2)], 42, [\"onMouseenter\"]);\n      }), 128)) : createCommentVNode(\"v-if\", true)], 2);\n    };\n  }\n});\nvar TimeSpinner = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"basic-time-spinner.vue\"]]);\nexport { TimeSpinner as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}