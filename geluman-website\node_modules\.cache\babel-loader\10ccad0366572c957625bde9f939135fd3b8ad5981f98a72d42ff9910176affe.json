{"ast": null, "code": "import apply from './_apply.js';\nimport arrayMap from './_arrayMap.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseRest from './_baseRest.js';\nimport baseUnary from './_baseUnary.js';\nimport flatRest from './_flatRest.js';\n\n/**\n * Creates a function like `_.over`.\n *\n * @private\n * @param {Function} arrayFunc The function to iterate over iteratees.\n * @returns {Function} Returns the new over function.\n */\nfunction createOver(arrayFunc) {\n  return flatRest(function (iteratees) {\n    iteratees = arrayMap(iteratees, baseUnary(baseIteratee));\n    return baseRest(function (args) {\n      var thisArg = this;\n      return arrayFunc(iteratees, function (iteratee) {\n        return apply(iteratee, thisArg, args);\n      });\n    });\n  });\n}\nexport default createOver;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}