{"ast": null, "code": "import Tooltip from './src/tooltip2.mjs';\nexport { tooltipEmits, useTooltipModelToggle, useTooltipModelToggleEmits, useTooltipModelToggleProps, useTooltipProps } from './src/tooltip.mjs';\nexport { useTooltipTriggerProps } from './src/trigger.mjs';\nexport { useTooltipContentProps } from './src/content.mjs';\nexport { TOOLTIP_INJECTION_KEY } from './src/constants.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElTooltip = withInstall(Tooltip);\nexport { ElTooltip, ElTooltip as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}