{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { isClient } from '@vueuse/core';\nconst globalNodes = [];\nlet target = !isClient ? void 0 : document.body;\nfunction createGlobalNode(id) {\n  const el = document.createElement(\"div\");\n  if (id !== void 0) {\n    el.setAttribute(\"id\", id);\n  }\n  if (target) {\n    target.appendChild(el);\n    globalNodes.push(el);\n  }\n  return el;\n}\nfunction removeGlobalNode(el) {\n  globalNodes.splice(globalNodes.indexOf(el), 1);\n  el.remove();\n}\nfunction changeGlobalNodesTarget(el) {\n  if (el === target) return;\n  target = el;\n  globalNodes.forEach(el2 => {\n    if (target && !el2.contains(target)) {\n      target.appendChild(el2);\n    }\n  });\n}\nexport { changeGlobalNodesTarget, createGlobalNode, removeGlobalNode };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}