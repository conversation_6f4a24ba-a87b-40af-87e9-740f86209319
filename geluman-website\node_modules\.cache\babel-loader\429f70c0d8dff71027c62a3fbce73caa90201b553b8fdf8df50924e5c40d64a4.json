{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, ref, computed, watch, nextTick, openBlock, createElementBlock, unref, normalizeClass, createElementVNode, Fragment, renderList, withKeys, withModifiers, createVNode } from 'vue';\nimport dayjs from 'dayjs';\nimport { basicYearTableProps } from '../props/basic-year-table.mjs';\nimport { getValidDateOfYear } from '../utils.mjs';\nimport ElDatePickerCell from './basic-cell-render.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { castArray } from '../../../../utils/arrays.mjs';\nimport { rangeArr } from '../../../time-picker/src/utils.mjs';\nimport { hasClass } from '../../../../utils/dom/style.mjs';\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"basic-year-table\",\n  props: basicYearTableProps,\n  emits: [\"changerange\", \"pick\", \"select\"],\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const datesInYear = (year, lang2) => {\n      const firstDay = dayjs(String(year)).locale(lang2).startOf(\"year\");\n      const lastDay = firstDay.endOf(\"year\");\n      const numOfDays = lastDay.dayOfYear();\n      return rangeArr(numOfDays).map(n => firstDay.add(n, \"day\").toDate());\n    };\n    const ns = useNamespace(\"year-table\");\n    const {\n      t,\n      lang\n    } = useLocale();\n    const tbodyRef = ref();\n    const currentCellRef = ref();\n    const startYear = computed(() => {\n      return Math.floor(props.date.year() / 10) * 10;\n    });\n    const tableRows = ref([[], [], []]);\n    const lastRow = ref();\n    const lastColumn = ref();\n    const rows = computed(() => {\n      var _a;\n      const rows2 = tableRows.value;\n      const now = dayjs().locale(lang.value).startOf(\"year\");\n      for (let i = 0; i < 3; i++) {\n        const row = rows2[i];\n        for (let j = 0; j < 4; j++) {\n          if (i * 4 + j >= 10) {\n            break;\n          }\n          let cell = row[j];\n          if (!cell) {\n            cell = {\n              row: i,\n              column: j,\n              type: \"normal\",\n              inRange: false,\n              start: false,\n              end: false,\n              text: -1,\n              disabled: false\n            };\n          }\n          cell.type = \"normal\";\n          const index = i * 4 + j + startYear.value;\n          const calTime = dayjs().year(index);\n          const calEndDate = props.rangeState.endDate || props.maxDate || props.rangeState.selecting && props.minDate || null;\n          cell.inRange = !!(props.minDate && calTime.isSameOrAfter(props.minDate, \"year\") && calEndDate && calTime.isSameOrBefore(calEndDate, \"year\")) || !!(props.minDate && calTime.isSameOrBefore(props.minDate, \"year\") && calEndDate && calTime.isSameOrAfter(calEndDate, \"year\"));\n          if ((_a = props.minDate) == null ? void 0 : _a.isSameOrAfter(calEndDate)) {\n            cell.start = !!(calEndDate && calTime.isSame(calEndDate, \"year\"));\n            cell.end = !!(props.minDate && calTime.isSame(props.minDate, \"year\"));\n          } else {\n            cell.start = !!(props.minDate && calTime.isSame(props.minDate, \"year\"));\n            cell.end = !!(calEndDate && calTime.isSame(calEndDate, \"year\"));\n          }\n          const isToday = now.isSame(calTime);\n          if (isToday) {\n            cell.type = \"today\";\n          }\n          cell.text = index;\n          const cellDate = calTime.toDate();\n          cell.disabled = props.disabledDate && props.disabledDate(cellDate) || false;\n          row[j] = cell;\n        }\n      }\n      return rows2;\n    });\n    const focus = () => {\n      var _a;\n      (_a = currentCellRef.value) == null ? void 0 : _a.focus();\n    };\n    const getCellKls = cell => {\n      const kls = {};\n      const today = dayjs().locale(lang.value);\n      const year = cell.text;\n      kls.disabled = props.disabledDate ? datesInYear(year, lang.value).every(props.disabledDate) : false;\n      kls.today = today.year() === year;\n      kls.current = castArray(props.parsedValue).findIndex(d => d.year() === year) >= 0;\n      if (cell.inRange) {\n        kls[\"in-range\"] = true;\n        if (cell.start) {\n          kls[\"start-date\"] = true;\n        }\n        if (cell.end) {\n          kls[\"end-date\"] = true;\n        }\n      }\n      return kls;\n    };\n    const isSelectedCell = cell => {\n      const year = cell.text;\n      return castArray(props.date).findIndex(date => date.year() === year) >= 0;\n    };\n    const handleYearTableClick = event => {\n      var _a;\n      const target = (_a = event.target) == null ? void 0 : _a.closest(\"td\");\n      if (!target || !target.textContent || hasClass(target, \"disabled\")) return;\n      const column = target.cellIndex;\n      const row = target.parentNode.rowIndex;\n      const selectedYear = row * 4 + column + startYear.value;\n      const newDate = dayjs().year(selectedYear);\n      if (props.selectionMode === \"range\") {\n        if (!props.rangeState.selecting) {\n          emit(\"pick\", {\n            minDate: newDate,\n            maxDate: null\n          });\n          emit(\"select\", true);\n        } else {\n          if (props.minDate && newDate >= props.minDate) {\n            emit(\"pick\", {\n              minDate: props.minDate,\n              maxDate: newDate\n            });\n          } else {\n            emit(\"pick\", {\n              minDate: newDate,\n              maxDate: props.minDate\n            });\n          }\n          emit(\"select\", false);\n        }\n      } else if (props.selectionMode === \"years\") {\n        if (event.type === \"keydown\") {\n          emit(\"pick\", castArray(props.parsedValue), false);\n          return;\n        }\n        const vaildYear = getValidDateOfYear(newDate.startOf(\"year\"), lang.value, props.disabledDate);\n        const newValue = hasClass(target, \"current\") ? castArray(props.parsedValue).filter(d => (d == null ? void 0 : d.year()) !== selectedYear) : castArray(props.parsedValue).concat([vaildYear]);\n        emit(\"pick\", newValue);\n      } else {\n        emit(\"pick\", selectedYear);\n      }\n    };\n    const handleMouseMove = event => {\n      var _a;\n      if (!props.rangeState.selecting) return;\n      const target = (_a = event.target) == null ? void 0 : _a.closest(\"td\");\n      if (!target) return;\n      const row = target.parentNode.rowIndex;\n      const column = target.cellIndex;\n      if (rows.value[row][column].disabled) return;\n      if (row !== lastRow.value || column !== lastColumn.value) {\n        lastRow.value = row;\n        lastColumn.value = column;\n        emit(\"changerange\", {\n          selecting: true,\n          endDate: dayjs().year(startYear.value).add(row * 4 + column, \"year\")\n        });\n      }\n    };\n    watch(() => props.date, async () => {\n      var _a, _b;\n      if ((_a = tbodyRef.value) == null ? void 0 : _a.contains(document.activeElement)) {\n        await nextTick();\n        (_b = currentCellRef.value) == null ? void 0 : _b.focus();\n      }\n    });\n    expose({\n      focus\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"table\", {\n        role: \"grid\",\n        \"aria-label\": unref(t)(\"el.datepicker.yearTablePrompt\"),\n        class: normalizeClass(unref(ns).b()),\n        onClick: handleYearTableClick,\n        onMousemove: handleMouseMove\n      }, [createElementVNode(\"tbody\", {\n        ref_key: \"tbodyRef\",\n        ref: tbodyRef\n      }, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(rows), (row, rowKey) => {\n        return openBlock(), createElementBlock(\"tr\", {\n          key: rowKey\n        }, [(openBlock(true), createElementBlock(Fragment, null, renderList(row, (cell, cellKey) => {\n          return openBlock(), createElementBlock(\"td\", {\n            key: `${rowKey}_${cellKey}`,\n            ref_for: true,\n            ref: el => isSelectedCell(cell) && (currentCellRef.value = el),\n            class: normalizeClass([\"available\", getCellKls(cell)]),\n            \"aria-selected\": isSelectedCell(cell),\n            \"aria-label\": String(cell.text),\n            tabindex: isSelectedCell(cell) ? 0 : -1,\n            onKeydown: [withKeys(withModifiers(handleYearTableClick, [\"prevent\", \"stop\"]), [\"space\"]), withKeys(withModifiers(handleYearTableClick, [\"prevent\", \"stop\"]), [\"enter\"])]\n          }, [createVNode(unref(ElDatePickerCell), {\n            cell\n          }, null, 8, [\"cell\"])], 42, [\"aria-selected\", \"aria-label\", \"tabindex\", \"onKeydown\"]);\n        }), 128))]);\n      }), 128))], 512)], 42, [\"aria-label\"]);\n    };\n  }\n});\nvar YearTable = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"basic-year-table.vue\"]]);\nexport { YearTable as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}