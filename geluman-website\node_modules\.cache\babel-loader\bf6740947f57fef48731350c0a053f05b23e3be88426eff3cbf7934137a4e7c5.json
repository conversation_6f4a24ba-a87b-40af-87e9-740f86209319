{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, computed, reactive, toRefs, provide, resolveComponent, resolveDirective, withDirectives, openBlock, createElementBlock, normalizeClass, createVNode, withCtx, createElementVNode, withModifiers, renderSlot, createCommentVNode, Fragment, renderList, normalizeStyle, createTextVNode, toDisplayString, createBlock, withKeys, vModelText, resolveDynamicComponent, vShow, createSlots, normalizeProps, guardReactiveProps } from 'vue';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { ElTag } from '../../tag/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport ElSelectMenu from './select-dropdown.mjs';\nimport useSelect from './useSelect.mjs';\nimport { SelectProps, selectEmits } from './defaults.mjs';\nimport { selectV2InjectionKey } from './token.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport ClickOutside from '../../../directives/click-outside/index.mjs';\nimport { isArray } from '@vue/shared';\nimport { useCalcInputWidth } from '../../../hooks/use-calc-input-width/index.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElSelectV2\",\n  components: {\n    ElSelectMenu,\n    ElTag,\n    ElTooltip,\n    ElIcon\n  },\n  directives: {\n    ClickOutside\n  },\n  props: SelectProps,\n  emits: selectEmits,\n  setup(props, {\n    emit\n  }) {\n    const modelValue = computed(() => {\n      const {\n        modelValue: rawModelValue,\n        multiple\n      } = props;\n      const fallback = multiple ? [] : void 0;\n      if (isArray(rawModelValue)) {\n        return multiple ? rawModelValue : fallback;\n      }\n      return multiple ? fallback : rawModelValue;\n    });\n    const API = useSelect(reactive({\n      ...toRefs(props),\n      modelValue\n    }), emit);\n    const {\n      calculatorRef,\n      inputStyle\n    } = useCalcInputWidth();\n    provide(selectV2InjectionKey, {\n      props: reactive({\n        ...toRefs(props),\n        height: API.popupHeight,\n        modelValue\n      }),\n      expanded: API.expanded,\n      tooltipRef: API.tooltipRef,\n      onSelect: API.onSelect,\n      onHover: API.onHover,\n      onKeyboardNavigate: API.onKeyboardNavigate,\n      onKeyboardSelect: API.onKeyboardSelect\n    });\n    const selectedLabel = computed(() => {\n      if (!props.multiple) {\n        return API.states.selectedLabel;\n      }\n      return API.states.cachedOptions.map(i => i.label);\n    });\n    return {\n      ...API,\n      modelValue,\n      selectedLabel,\n      calculatorRef,\n      inputStyle\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_tag = resolveComponent(\"el-tag\");\n  const _component_el_tooltip = resolveComponent(\"el-tooltip\");\n  const _component_el_icon = resolveComponent(\"el-icon\");\n  const _component_el_select_menu = resolveComponent(\"el-select-menu\");\n  const _directive_click_outside = resolveDirective(\"click-outside\");\n  return withDirectives((openBlock(), createElementBlock(\"div\", {\n    ref: \"selectRef\",\n    class: normalizeClass([_ctx.nsSelect.b(), _ctx.nsSelect.m(_ctx.selectSize)]),\n    onMouseenter: $event => _ctx.states.inputHovering = true,\n    onMouseleave: $event => _ctx.states.inputHovering = false\n  }, [createVNode(_component_el_tooltip, {\n    ref: \"tooltipRef\",\n    visible: _ctx.dropdownMenuVisible,\n    teleported: _ctx.teleported,\n    \"popper-class\": [_ctx.nsSelect.e(\"popper\"), _ctx.popperClass],\n    \"gpu-acceleration\": false,\n    \"stop-popper-mouse-event\": false,\n    \"popper-options\": _ctx.popperOptions,\n    \"fallback-placements\": _ctx.fallbackPlacements,\n    effect: _ctx.effect,\n    placement: _ctx.placement,\n    pure: \"\",\n    transition: `${_ctx.nsSelect.namespace.value}-zoom-in-top`,\n    trigger: \"click\",\n    persistent: _ctx.persistent,\n    \"append-to\": _ctx.appendTo,\n    \"show-arrow\": _ctx.showArrow,\n    offset: _ctx.offset,\n    onBeforeShow: _ctx.handleMenuEnter,\n    onHide: $event => _ctx.states.isBeforeHide = false\n  }, {\n    default: withCtx(() => [createElementVNode(\"div\", {\n      ref: \"wrapperRef\",\n      class: normalizeClass([_ctx.nsSelect.e(\"wrapper\"), _ctx.nsSelect.is(\"focused\", _ctx.isFocused), _ctx.nsSelect.is(\"hovering\", _ctx.states.inputHovering), _ctx.nsSelect.is(\"filterable\", _ctx.filterable), _ctx.nsSelect.is(\"disabled\", _ctx.selectDisabled)]),\n      onClick: withModifiers(_ctx.toggleMenu, [\"prevent\"])\n    }, [_ctx.$slots.prefix ? (openBlock(), createElementBlock(\"div\", {\n      key: 0,\n      ref: \"prefixRef\",\n      class: normalizeClass(_ctx.nsSelect.e(\"prefix\"))\n    }, [renderSlot(_ctx.$slots, \"prefix\")], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n      ref: \"selectionRef\",\n      class: normalizeClass([_ctx.nsSelect.e(\"selection\"), _ctx.nsSelect.is(\"near\", _ctx.multiple && !_ctx.$slots.prefix && !!_ctx.modelValue.length)])\n    }, [_ctx.multiple ? renderSlot(_ctx.$slots, \"tag\", {\n      key: 0\n    }, () => [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.showTagList, item => {\n      return openBlock(), createElementBlock(\"div\", {\n        key: _ctx.getValueKey(_ctx.getValue(item)),\n        class: normalizeClass(_ctx.nsSelect.e(\"selected-item\"))\n      }, [createVNode(_component_el_tag, {\n        closable: !_ctx.selectDisabled && !_ctx.getDisabled(item),\n        size: _ctx.collapseTagSize,\n        type: _ctx.tagType,\n        effect: _ctx.tagEffect,\n        \"disable-transitions\": \"\",\n        style: normalizeStyle(_ctx.tagStyle),\n        onClose: $event => _ctx.deleteTag($event, item)\n      }, {\n        default: withCtx(() => [createElementVNode(\"span\", {\n          class: normalizeClass(_ctx.nsSelect.e(\"tags-text\"))\n        }, [renderSlot(_ctx.$slots, \"label\", {\n          label: _ctx.getLabel(item),\n          value: _ctx.getValue(item)\n        }, () => [createTextVNode(toDisplayString(_ctx.getLabel(item)), 1)])], 2)]),\n        _: 2\n      }, 1032, [\"closable\", \"size\", \"type\", \"effect\", \"style\", \"onClose\"])], 2);\n    }), 128)), _ctx.collapseTags && _ctx.modelValue.length > _ctx.maxCollapseTags ? (openBlock(), createBlock(_component_el_tooltip, {\n      key: 0,\n      ref: \"tagTooltipRef\",\n      disabled: _ctx.dropdownMenuVisible || !_ctx.collapseTagsTooltip,\n      \"fallback-placements\": [\"bottom\", \"top\", \"right\", \"left\"],\n      effect: _ctx.effect,\n      placement: \"bottom\",\n      teleported: _ctx.teleported\n    }, {\n      default: withCtx(() => [createElementVNode(\"div\", {\n        ref: \"collapseItemRef\",\n        class: normalizeClass(_ctx.nsSelect.e(\"selected-item\"))\n      }, [createVNode(_component_el_tag, {\n        closable: false,\n        size: _ctx.collapseTagSize,\n        type: _ctx.tagType,\n        effect: _ctx.tagEffect,\n        style: normalizeStyle(_ctx.collapseTagStyle),\n        \"disable-transitions\": \"\"\n      }, {\n        default: withCtx(() => [createElementVNode(\"span\", {\n          class: normalizeClass(_ctx.nsSelect.e(\"tags-text\"))\n        }, \" + \" + toDisplayString(_ctx.modelValue.length - _ctx.maxCollapseTags), 3)]),\n        _: 1\n      }, 8, [\"size\", \"type\", \"effect\", \"style\"])], 2)]),\n      content: withCtx(() => [createElementVNode(\"div\", {\n        ref: \"tagMenuRef\",\n        class: normalizeClass(_ctx.nsSelect.e(\"selection\"))\n      }, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.collapseTagList, selected => {\n        return openBlock(), createElementBlock(\"div\", {\n          key: _ctx.getValueKey(_ctx.getValue(selected)),\n          class: normalizeClass(_ctx.nsSelect.e(\"selected-item\"))\n        }, [createVNode(_component_el_tag, {\n          class: \"in-tooltip\",\n          closable: !_ctx.selectDisabled && !_ctx.getDisabled(selected),\n          size: _ctx.collapseTagSize,\n          type: _ctx.tagType,\n          effect: _ctx.tagEffect,\n          \"disable-transitions\": \"\",\n          onClose: $event => _ctx.deleteTag($event, selected)\n        }, {\n          default: withCtx(() => [createElementVNode(\"span\", {\n            class: normalizeClass(_ctx.nsSelect.e(\"tags-text\"))\n          }, [renderSlot(_ctx.$slots, \"label\", {\n            label: _ctx.getLabel(selected),\n            value: _ctx.getValue(selected)\n          }, () => [createTextVNode(toDisplayString(_ctx.getLabel(selected)), 1)])], 2)]),\n          _: 2\n        }, 1032, [\"closable\", \"size\", \"type\", \"effect\", \"onClose\"])], 2);\n      }), 128))], 2)]),\n      _: 3\n    }, 8, [\"disabled\", \"effect\", \"teleported\"])) : createCommentVNode(\"v-if\", true)]) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n      class: normalizeClass([_ctx.nsSelect.e(\"selected-item\"), _ctx.nsSelect.e(\"input-wrapper\"), _ctx.nsSelect.is(\"hidden\", !_ctx.filterable)])\n    }, [withDirectives(createElementVNode(\"input\", {\n      id: _ctx.inputId,\n      ref: \"inputRef\",\n      \"onUpdate:modelValue\": $event => _ctx.states.inputValue = $event,\n      style: normalizeStyle(_ctx.inputStyle),\n      autocomplete: _ctx.autocomplete,\n      tabindex: _ctx.tabindex,\n      \"aria-autocomplete\": \"list\",\n      \"aria-haspopup\": \"listbox\",\n      autocapitalize: \"off\",\n      \"aria-expanded\": _ctx.expanded,\n      \"aria-label\": _ctx.ariaLabel,\n      class: normalizeClass([_ctx.nsSelect.e(\"input\"), _ctx.nsSelect.is(_ctx.selectSize)]),\n      disabled: _ctx.selectDisabled,\n      role: \"combobox\",\n      readonly: !_ctx.filterable,\n      spellcheck: \"false\",\n      type: \"text\",\n      name: _ctx.name,\n      onInput: _ctx.onInput,\n      onCompositionstart: _ctx.handleCompositionStart,\n      onCompositionupdate: _ctx.handleCompositionUpdate,\n      onCompositionend: _ctx.handleCompositionEnd,\n      onKeydown: [withKeys(withModifiers($event => _ctx.onKeyboardNavigate(\"backward\"), [\"stop\", \"prevent\"]), [\"up\"]), withKeys(withModifiers($event => _ctx.onKeyboardNavigate(\"forward\"), [\"stop\", \"prevent\"]), [\"down\"]), withKeys(withModifiers(_ctx.onKeyboardSelect, [\"stop\", \"prevent\"]), [\"enter\"]), withKeys(withModifiers(_ctx.handleEsc, [\"stop\", \"prevent\"]), [\"esc\"]), withKeys(withModifiers(_ctx.handleDel, [\"stop\"]), [\"delete\"])],\n      onClick: withModifiers(_ctx.toggleMenu, [\"stop\"])\n    }, null, 46, [\"id\", \"onUpdate:modelValue\", \"autocomplete\", \"tabindex\", \"aria-expanded\", \"aria-label\", \"disabled\", \"readonly\", \"name\", \"onInput\", \"onCompositionstart\", \"onCompositionupdate\", \"onCompositionend\", \"onKeydown\", \"onClick\"]), [[vModelText, _ctx.states.inputValue]]), _ctx.filterable ? (openBlock(), createElementBlock(\"span\", {\n      key: 0,\n      ref: \"calculatorRef\",\n      \"aria-hidden\": \"true\",\n      class: normalizeClass(_ctx.nsSelect.e(\"input-calculator\")),\n      textContent: toDisplayString(_ctx.states.inputValue)\n    }, null, 10, [\"textContent\"])) : createCommentVNode(\"v-if\", true)], 2), _ctx.shouldShowPlaceholder ? (openBlock(), createElementBlock(\"div\", {\n      key: 1,\n      class: normalizeClass([_ctx.nsSelect.e(\"selected-item\"), _ctx.nsSelect.e(\"placeholder\"), _ctx.nsSelect.is(\"transparent\", !_ctx.hasModelValue || _ctx.expanded && !_ctx.states.inputValue)])\n    }, [_ctx.hasModelValue ? renderSlot(_ctx.$slots, \"label\", {\n      key: 0,\n      label: _ctx.currentPlaceholder,\n      value: _ctx.modelValue\n    }, () => [createElementVNode(\"span\", null, toDisplayString(_ctx.currentPlaceholder), 1)]) : (openBlock(), createElementBlock(\"span\", {\n      key: 1\n    }, toDisplayString(_ctx.currentPlaceholder), 1))], 2)) : createCommentVNode(\"v-if\", true)], 2), createElementVNode(\"div\", {\n      ref: \"suffixRef\",\n      class: normalizeClass(_ctx.nsSelect.e(\"suffix\"))\n    }, [_ctx.iconComponent ? withDirectives((openBlock(), createBlock(_component_el_icon, {\n      key: 0,\n      class: normalizeClass([_ctx.nsSelect.e(\"caret\"), _ctx.nsInput.e(\"icon\"), _ctx.iconReverse])\n    }, {\n      default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.iconComponent)))]),\n      _: 1\n    }, 8, [\"class\"])), [[vShow, !_ctx.showClearBtn]]) : createCommentVNode(\"v-if\", true), _ctx.showClearBtn && _ctx.clearIcon ? (openBlock(), createBlock(_component_el_icon, {\n      key: 1,\n      class: normalizeClass([_ctx.nsSelect.e(\"caret\"), _ctx.nsInput.e(\"icon\"), _ctx.nsSelect.e(\"clear\")]),\n      onClick: withModifiers(_ctx.handleClear, [\"prevent\", \"stop\"])\n    }, {\n      default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.clearIcon)))]),\n      _: 1\n    }, 8, [\"class\", \"onClick\"])) : createCommentVNode(\"v-if\", true), _ctx.validateState && _ctx.validateIcon && _ctx.needStatusIcon ? (openBlock(), createBlock(_component_el_icon, {\n      key: 2,\n      class: normalizeClass([_ctx.nsInput.e(\"icon\"), _ctx.nsInput.e(\"validateIcon\"), _ctx.nsInput.is(\"loading\", _ctx.validateState === \"validating\")])\n    }, {\n      default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.validateIcon)))]),\n      _: 1\n    }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true)], 2)], 10, [\"onClick\"])]),\n    content: withCtx(() => [createVNode(_component_el_select_menu, {\n      ref: \"menuRef\",\n      data: _ctx.filteredOptions,\n      width: _ctx.popperSize,\n      \"hovering-index\": _ctx.states.hoveringIndex,\n      \"scrollbar-always-on\": _ctx.scrollbarAlwaysOn\n    }, createSlots({\n      default: withCtx(scope => [renderSlot(_ctx.$slots, \"default\", normalizeProps(guardReactiveProps(scope)))]),\n      _: 2\n    }, [_ctx.$slots.header ? {\n      name: \"header\",\n      fn: withCtx(() => [createElementVNode(\"div\", {\n        class: normalizeClass(_ctx.nsSelect.be(\"dropdown\", \"header\"))\n      }, [renderSlot(_ctx.$slots, \"header\")], 2)])\n    } : void 0, _ctx.$slots.loading && _ctx.loading ? {\n      name: \"loading\",\n      fn: withCtx(() => [createElementVNode(\"div\", {\n        class: normalizeClass(_ctx.nsSelect.be(\"dropdown\", \"loading\"))\n      }, [renderSlot(_ctx.$slots, \"loading\")], 2)])\n    } : _ctx.loading || _ctx.filteredOptions.length === 0 ? {\n      name: \"empty\",\n      fn: withCtx(() => [createElementVNode(\"div\", {\n        class: normalizeClass(_ctx.nsSelect.be(\"dropdown\", \"empty\"))\n      }, [renderSlot(_ctx.$slots, \"empty\", {}, () => [createElementVNode(\"span\", null, toDisplayString(_ctx.emptyText), 1)])], 2)])\n    } : void 0, _ctx.$slots.footer ? {\n      name: \"footer\",\n      fn: withCtx(() => [createElementVNode(\"div\", {\n        class: normalizeClass(_ctx.nsSelect.be(\"dropdown\", \"footer\"))\n      }, [renderSlot(_ctx.$slots, \"footer\")], 2)])\n    } : void 0]), 1032, [\"data\", \"width\", \"hovering-index\", \"scrollbar-always-on\"])]),\n    _: 3\n  }, 8, [\"visible\", \"teleported\", \"popper-class\", \"popper-options\", \"fallback-placements\", \"effect\", \"placement\", \"transition\", \"persistent\", \"append-to\", \"show-arrow\", \"offset\", \"onBeforeShow\", \"onHide\"])], 42, [\"onMouseenter\", \"onMouseleave\"])), [[_directive_click_outside, _ctx.handleClickOutside, _ctx.popperRef]]);\n}\nvar Select = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"select.vue\"]]);\nexport { Select as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}