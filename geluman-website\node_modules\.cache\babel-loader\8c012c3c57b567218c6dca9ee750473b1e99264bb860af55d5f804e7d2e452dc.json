{"ast": null, "code": "import { defineComponent, inject, ref, computed, openBlock, createBlock, Transition, unref, withCtx, createElementBlock, normalizeClass, createElementVNode, createVNode, toDisplayString, createCommentVNode } from 'vue';\nimport dayjs from 'dayjs';\nimport { panelTimePickerProps } from '../props/panel-time-picker.mjs';\nimport { useTimePanel } from '../composables/use-time-panel.mjs';\nimport { useOldValue, buildAvailableTimeSlotGetter } from '../composables/use-time-picker.mjs';\nimport TimeSpinner from './basic-time-spinner.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { isUndefined } from '../../../../utils/types.mjs';\nimport { EVENT_CODE } from '../../../../constants/aria.mjs';\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"panel-time-pick\",\n  props: panelTimePickerProps,\n  emits: [\"pick\", \"select-range\", \"set-picker-option\"],\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const pickerBase = inject(\"EP_PICKER_BASE\");\n    const {\n      arrowControl,\n      disabledHours,\n      disabledMinutes,\n      disabledSeconds,\n      defaultValue\n    } = pickerBase.props;\n    const {\n      getAvailableHours,\n      getAvailableMinutes,\n      getAvailableSeconds\n    } = buildAvailableTimeSlotGetter(disabledHours, disabledMinutes, disabledSeconds);\n    const ns = useNamespace(\"time\");\n    const {\n      t,\n      lang\n    } = useLocale();\n    const selectionRange = ref([0, 2]);\n    const oldValue = useOldValue(props);\n    const transitionName = computed(() => {\n      return isUndefined(props.actualVisible) ? `${ns.namespace.value}-zoom-in-top` : \"\";\n    });\n    const showSeconds = computed(() => {\n      return props.format.includes(\"ss\");\n    });\n    const amPmMode = computed(() => {\n      if (props.format.includes(\"A\")) return \"A\";\n      if (props.format.includes(\"a\")) return \"a\";\n      return \"\";\n    });\n    const isValidValue = _date => {\n      const parsedDate = dayjs(_date).locale(lang.value);\n      const result = getRangeAvailableTime(parsedDate);\n      return parsedDate.isSame(result);\n    };\n    const handleCancel = () => {\n      emit(\"pick\", oldValue.value, false);\n    };\n    const handleConfirm = (visible = false, first = false) => {\n      if (first) return;\n      emit(\"pick\", props.parsedValue, visible);\n    };\n    const handleChange = _date => {\n      if (!props.visible) {\n        return;\n      }\n      const result = getRangeAvailableTime(_date).millisecond(0);\n      emit(\"pick\", result, true);\n    };\n    const setSelectionRange = (start, end) => {\n      emit(\"select-range\", start, end);\n      selectionRange.value = [start, end];\n    };\n    const changeSelectionRange = step => {\n      const list = [0, 3].concat(showSeconds.value ? [6] : []);\n      const mapping = [\"hours\", \"minutes\"].concat(showSeconds.value ? [\"seconds\"] : []);\n      const index = list.indexOf(selectionRange.value[0]);\n      const next = (index + step + list.length) % list.length;\n      timePickerOptions[\"start_emitSelectRange\"](mapping[next]);\n    };\n    const handleKeydown = event => {\n      const code = event.code;\n      const {\n        left,\n        right,\n        up,\n        down\n      } = EVENT_CODE;\n      if ([left, right].includes(code)) {\n        const step = code === left ? -1 : 1;\n        changeSelectionRange(step);\n        event.preventDefault();\n        return;\n      }\n      if ([up, down].includes(code)) {\n        const step = code === up ? -1 : 1;\n        timePickerOptions[\"start_scrollDown\"](step);\n        event.preventDefault();\n        return;\n      }\n    };\n    const {\n      timePickerOptions,\n      onSetOption,\n      getAvailableTime\n    } = useTimePanel({\n      getAvailableHours,\n      getAvailableMinutes,\n      getAvailableSeconds\n    });\n    const getRangeAvailableTime = date => {\n      return getAvailableTime(date, props.datetimeRole || \"\", true);\n    };\n    const parseUserInput = value => {\n      if (!value) return null;\n      return dayjs(value, props.format).locale(lang.value);\n    };\n    const formatToString = value => {\n      if (!value) return null;\n      return value.format(props.format);\n    };\n    const getDefaultValue = () => {\n      return dayjs(defaultValue).locale(lang.value);\n    };\n    emit(\"set-picker-option\", [\"isValidValue\", isValidValue]);\n    emit(\"set-picker-option\", [\"formatToString\", formatToString]);\n    emit(\"set-picker-option\", [\"parseUserInput\", parseUserInput]);\n    emit(\"set-picker-option\", [\"handleKeydownInput\", handleKeydown]);\n    emit(\"set-picker-option\", [\"getRangeAvailableTime\", getRangeAvailableTime]);\n    emit(\"set-picker-option\", [\"getDefaultValue\", getDefaultValue]);\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(Transition, {\n        name: unref(transitionName)\n      }, {\n        default: withCtx(() => [_ctx.actualVisible || _ctx.visible ? (openBlock(), createElementBlock(\"div\", {\n          key: 0,\n          class: normalizeClass(unref(ns).b(\"panel\"))\n        }, [createElementVNode(\"div\", {\n          class: normalizeClass([unref(ns).be(\"panel\", \"content\"), {\n            \"has-seconds\": unref(showSeconds)\n          }])\n        }, [createVNode(TimeSpinner, {\n          ref: \"spinner\",\n          role: _ctx.datetimeRole || \"start\",\n          \"arrow-control\": unref(arrowControl),\n          \"show-seconds\": unref(showSeconds),\n          \"am-pm-mode\": unref(amPmMode),\n          \"spinner-date\": _ctx.parsedValue,\n          \"disabled-hours\": unref(disabledHours),\n          \"disabled-minutes\": unref(disabledMinutes),\n          \"disabled-seconds\": unref(disabledSeconds),\n          onChange: handleChange,\n          onSetOption: unref(onSetOption),\n          onSelectRange: setSelectionRange\n        }, null, 8, [\"role\", \"arrow-control\", \"show-seconds\", \"am-pm-mode\", \"spinner-date\", \"disabled-hours\", \"disabled-minutes\", \"disabled-seconds\", \"onSetOption\"])], 2), createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).be(\"panel\", \"footer\"))\n        }, [createElementVNode(\"button\", {\n          type: \"button\",\n          class: normalizeClass([unref(ns).be(\"panel\", \"btn\"), \"cancel\"]),\n          onClick: handleCancel\n        }, toDisplayString(unref(t)(\"el.datepicker.cancel\")), 3), createElementVNode(\"button\", {\n          type: \"button\",\n          class: normalizeClass([unref(ns).be(\"panel\", \"btn\"), \"confirm\"]),\n          onClick: $event => handleConfirm()\n        }, toDisplayString(unref(t)(\"el.datepicker.confirm\")), 11, [\"onClick\"])], 2)], 2)) : createCommentVNode(\"v-if\", true)]),\n        _: 1\n      }, 8, [\"name\"]);\n    };\n  }\n});\nvar TimePickPanel = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"panel-time-pick.vue\"]]);\nexport { TimePickPanel as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}