{"ast": null, "code": "import { defineComponent, inject, withDirectives, cloneVNode, Fragment, createVNode, Text, Comment } from 'vue';\nimport { FORWARD_REF_INJECTION_KEY, useForwardRefDirective } from '../../../hooks/use-forward-ref/index.mjs';\nimport { NOOP, isObject } from '@vue/shared';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst NAME = \"ElOnlyChild\";\nconst OnlyChild = defineComponent({\n  name: NAME,\n  setup(_, {\n    slots,\n    attrs\n  }) {\n    var _a;\n    const forwardRefInjection = inject(FORWARD_REF_INJECTION_KEY);\n    const forwardRefDirective = useForwardRefDirective((_a = forwardRefInjection == null ? void 0 : forwardRefInjection.setForwardRef) != null ? _a : NOOP);\n    return () => {\n      var _a2;\n      const defaultSlot = (_a2 = slots.default) == null ? void 0 : _a2.call(slots, attrs);\n      if (!defaultSlot) return null;\n      if (defaultSlot.length > 1) {\n        debugWarn(NAME, \"requires exact only one valid child.\");\n        return null;\n      }\n      const firstLegitNode = findFirstLegitChild(defaultSlot);\n      if (!firstLegitNode) {\n        debugWarn(NAME, \"no valid child node found\");\n        return null;\n      }\n      return withDirectives(cloneVNode(firstLegitNode, attrs), [[forwardRefDirective]]);\n    };\n  }\n});\nfunction findFirstLegitChild(node) {\n  if (!node) return null;\n  const children = node;\n  for (const child of children) {\n    if (isObject(child)) {\n      switch (child.type) {\n        case Comment:\n          continue;\n        case Text:\n        case \"svg\":\n          return wrapTextContent(child);\n        case Fragment:\n          return findFirstLegitChild(child.children);\n        default:\n          return child;\n      }\n    }\n    return wrapTextContent(child);\n  }\n  return null;\n}\nfunction wrapTextContent(s) {\n  const ns = useNamespace(\"only-child\");\n  return createVNode(\"span\", {\n    \"class\": ns.e(\"content\")\n  }, [s]);\n}\nexport { OnlyChild };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}