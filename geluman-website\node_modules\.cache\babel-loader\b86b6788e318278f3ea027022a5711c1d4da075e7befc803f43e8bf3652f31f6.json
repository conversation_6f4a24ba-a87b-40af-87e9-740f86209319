{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { isString } from '@vue/shared';\nimport { isUndefined } from '../../../utils/types.mjs';\nconst anchorProps = buildProps({\n  container: {\n    type: definePropType([String, Object])\n  },\n  offset: {\n    type: Number,\n    default: 0\n  },\n  bound: {\n    type: Number,\n    default: 15\n  },\n  duration: {\n    type: Number,\n    default: 300\n  },\n  marker: {\n    type: Boolean,\n    default: true\n  },\n  type: {\n    type: definePropType(String),\n    default: \"default\"\n  },\n  direction: {\n    type: definePropType(String),\n    default: \"vertical\"\n  },\n  selectScrollTop: {\n    type: Boolean,\n    default: false\n  }\n});\nconst anchorEmits = {\n  change: href => isString(href),\n  click: (e, href) => e instanceof MouseEvent && (isString(href) || isUndefined(href))\n};\nexport { anchorEmits, anchorProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}