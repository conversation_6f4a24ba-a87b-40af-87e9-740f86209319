{"ast": null, "code": "import Dialog from './src/dialog2.mjs';\nexport { useDialog } from './src/use-dialog.mjs';\nexport { dialogEmits, dialogProps } from './src/dialog.mjs';\nexport { dialogInjectionKey } from './src/constants.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElDialog = withInstall(Dialog);\nexport { ElDialog, ElDialog as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}