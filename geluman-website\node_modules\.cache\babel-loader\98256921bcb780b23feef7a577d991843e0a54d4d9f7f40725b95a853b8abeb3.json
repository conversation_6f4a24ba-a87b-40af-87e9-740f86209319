{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst tooltipV2RootProps = buildProps({\n  delayDuration: {\n    type: Number,\n    default: 300\n  },\n  defaultOpen: Boolean,\n  open: {\n    type: <PERSON>olean,\n    default: void 0\n  },\n  onOpenChange: {\n    type: definePropType(Function)\n  },\n  \"onUpdate:open\": {\n    type: definePropType(Function)\n  }\n});\nexport { tooltipV2RootProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}