{"ast": null, "code": "import { isNil } from 'lodash-unified';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { useTooltipContentProps } from '../../tooltip/src/content.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { isString } from '@vue/shared';\nconst colorPickerProps = buildProps({\n  modelValue: String,\n  id: String,\n  showAlpha: Boolean,\n  colorFormat: String,\n  disabled: Boolean,\n  size: useSizeProp,\n  popperClass: {\n    type: String,\n    default: \"\"\n  },\n  tabindex: {\n    type: [String, Number],\n    default: 0\n  },\n  teleported: useTooltipContentProps.teleported,\n  predefine: {\n    type: definePropType(Array)\n  },\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  ...useAriaProps([\"ariaLabel\"])\n});\nconst colorPickerEmits = {\n  [UPDATE_MODEL_EVENT]: val => isString(val) || isNil(val),\n  [CHANGE_EVENT]: val => isString(val) || isNil(val),\n  activeChange: val => isString(val) || isNil(val),\n  focus: evt => evt instanceof FocusEvent,\n  blur: evt => evt instanceof FocusEvent\n};\nconst colorPickerContextKey = Symbol(\"colorPickerContextKey\");\nexport { colorPickerContextKey, colorPickerEmits, colorPickerProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}