{"ast": null, "code": "import createFlow from './_createFlow.js';\n\n/**\n * Creates a function that returns the result of invoking the given functions\n * with the `this` binding of the created function, where each successive\n * invocation is supplied the return value of the previous.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Util\n * @param {...(Function|Function[])} [funcs] The functions to invoke.\n * @returns {Function} Returns the new composite function.\n * @see _.flowRight\n * @example\n *\n * function square(n) {\n *   return n * n;\n * }\n *\n * var addSquare = _.flow([_.add, square]);\n * addSquare(1, 2);\n * // => 9\n */\nvar flow = createFlow();\nexport default flow;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}