{"ast": null, "code": "import { QuestionFilled } from '@element-plus/icons-vue';\nimport { buttonTypes } from '../../button/src/button.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nimport { useTooltipContentProps } from '../../tooltip/src/content.mjs';\nconst popconfirmProps = buildProps({\n  title: String,\n  confirmButtonText: String,\n  cancelButtonText: String,\n  confirmButtonType: {\n    type: String,\n    values: buttonTypes,\n    default: \"primary\"\n  },\n  cancelButtonType: {\n    type: String,\n    values: buttonTypes,\n    default: \"text\"\n  },\n  icon: {\n    type: iconPropType,\n    default: () => QuestionFilled\n  },\n  iconColor: {\n    type: String,\n    default: \"#f90\"\n  },\n  hideIcon: {\n    type: Boolean,\n    default: false\n  },\n  hideAfter: {\n    type: Number,\n    default: 200\n  },\n  teleported: useTooltipContentProps.teleported,\n  persistent: useTooltipContentProps.persistent,\n  width: {\n    type: [String, Number],\n    default: 150\n  }\n});\nconst popconfirmEmits = {\n  confirm: e => e instanceof MouseEvent,\n  cancel: e => e instanceof MouseEvent\n};\nexport { popconfirmEmits, popconfirmProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}