{"ast": null, "code": "import { eyeshield } from \"@/assets\";\nimport huyanSetting from \"@/assets/img/huyan-setting.png\";\nimport huyanDemo from \"@/assets/img/huyan.png\";\nconst __default__ = {\n  name: \"EyeshieldPage\"\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const features = [{\n      icon: \"🎨\",\n      title: \"多种护眼模式\",\n      description: \"提供豆沙绿、杏灰色、淡黄色、浅粉色等多种护眼配色\"\n    }, {\n      icon: \"⚡\",\n      title: \"一键切换\",\n      description: \"便捷的开关控制，随时切换护眼模式\"\n    }, {\n      icon: \"🔄\",\n      title: \"智能适配\",\n      description: \"自动识别网页结构，智能调整背景色\"\n    }, {\n      icon: \"🎚️\",\n      title: \"全局控制\",\n      description: \"支持设置全局应用或指定网站应用\"\n    }];\n    const steps = [{\n      number: \"1\",\n      title: \"安装插件\",\n      description: \"从下方下载按钮下载并解压，打开Chrome或者Edge浏览器，进入（浏览器扩展管理）chrome://extensions/或者edge://extensions/，将解压后的文件夹拖入页面中，完成安装\"\n    }, {\n      number: \"2\",\n      title: \"选择模式\",\n      description: \"点击插件图标，选择合适的护眼模式\"\n    }, {\n      number: \"3\",\n      title: \"设置范围\",\n      description: \"选择是全局应用还是仅应用于特定网站\"\n    }, {\n      number: \"4\",\n      title: \"开始使用\",\n      description: \"打开网页即可享受舒适的护眼体验\"\n    }];\n    const __returned__ = {\n      features,\n      steps,\n      get eyeshield() {\n        return eyeshield;\n      },\n      get huyanSetting() {\n        return huyanSetting;\n      },\n      get huyanDemo() {\n        return huyanDemo;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["eyeshield", "huyanSetting", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__default__", "name", "features", "icon", "title", "description", "steps", "number"], "sources": ["D:/codes/glmwebsite/geluman-website/src/views/products/Eyeshield.vue"], "sourcesContent": ["<template>\r\n  <div class=\"product-page\">\r\n    <section class=\"hero\">\r\n      <div class=\"hero-content\">\r\n        <img :src=\"eyeshield\" alt=\"护眼助手\" class=\"plugin-logo\" />\r\n        <h1>网页护眼助手</h1>\r\n        <p class=\"subtitle\">智能护眼，让阅读更舒适</p>\r\n        <div class=\"download-options\">\r\n          <div class=\"version-option\">\r\n            <a\r\n              href=\"https://gengxin.geluman.cn/eyeshield/GLM-Eyeshield-1.3.3.zip\"\r\n              class=\"download-btn\"\r\n              target=\"_blank\"\r\n              v-ripple\r\n            >\r\n              下载插件\r\n            </a>\r\n            <p class=\"version-desc\">标准版本，适合大多数用户使用</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <section class=\"demo\">\r\n      <div class=\"container\">\r\n        <h2 class=\"section-title\">产品演示</h2>\r\n        <p class=\"section-subtitle\">便捷的护眼工具，多种模式选择</p>\r\n        <div class=\"demo-gallery\">\r\n          <div class=\"demo-item\">\r\n            <div class=\"demo-image-container\">\r\n              <img\r\n                :src=\"huyanSetting\"\r\n                alt=\"网页护眼助手 - 设置界面\"\r\n                class=\"demo-image\"\r\n              />\r\n            </div>\r\n            <div class=\"image-caption\">护眼设置 - 多种模式可选</div>\r\n          </div>\r\n          <div class=\"demo-item\">\r\n            <div class=\"demo-image-container\">\r\n              <img\r\n                :src=\"huyanDemo\"\r\n                alt=\"网页护眼助手 - 效果展示\"\r\n                class=\"demo-image\"\r\n              />\r\n            </div>\r\n            <div class=\"image-caption\">护眼效果 - 舒适自然的阅读体验</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <section class=\"features\">\r\n      <div class=\"container\">\r\n        <h2>核心功能</h2>\r\n        <div class=\"features-grid\">\r\n          <div\r\n            class=\"feature-card\"\r\n            v-for=\"feature in features\"\r\n            :key=\"feature.title\"\r\n          >\r\n            <div class=\"feature-icon\">{{ feature.icon }}</div>\r\n            <h3>{{ feature.title }}</h3>\r\n            <p>{{ feature.description }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <section class=\"usage\">\r\n      <div class=\"container\">\r\n        <h2>使用说明</h2>\r\n        <div class=\"usage-steps\">\r\n          <div class=\"step\" v-for=\"step in steps\" :key=\"step.title\">\r\n            <div class=\"step-number\">{{ step.number }}</div>\r\n            <h3>{{ step.title }}</h3>\r\n            <p>{{ step.description }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { eyeshield } from \"@/assets\";\r\nimport huyanSetting from \"@/assets/img/huyan-setting.png\";\r\nimport huyanDemo from \"@/assets/img/huyan.png\";\r\n\r\nconst features = [\r\n  {\r\n    icon: \"🎨\",\r\n    title: \"多种护眼模式\",\r\n    description: \"提供豆沙绿、杏灰色、淡黄色、浅粉色等多种护眼配色\",\r\n  },\r\n  {\r\n    icon: \"⚡\",\r\n    title: \"一键切换\",\r\n    description: \"便捷的开关控制，随时切换护眼模式\",\r\n  },\r\n  {\r\n    icon: \"🔄\",\r\n    title: \"智能适配\",\r\n    description: \"自动识别网页结构，智能调整背景色\",\r\n  },\r\n  {\r\n    icon: \"🎚️\",\r\n    title: \"全局控制\",\r\n    description: \"支持设置全局应用或指定网站应用\",\r\n  },\r\n];\r\n\r\nconst steps = [\r\n  {\r\n    number: \"1\",\r\n    title: \"安装插件\",\r\n    description:\r\n      \"从下方下载按钮下载并解压，打开Chrome或者Edge浏览器，进入（浏览器扩展管理）chrome://extensions/或者edge://extensions/，将解压后的文件夹拖入页面中，完成安装\",\r\n  },\r\n  {\r\n    number: \"2\",\r\n    title: \"选择模式\",\r\n    description: \"点击插件图标，选择合适的护眼模式\",\r\n  },\r\n  {\r\n    number: \"3\",\r\n    title: \"设置范围\",\r\n    description: \"选择是全局应用还是仅应用于特定网站\",\r\n  },\r\n  {\r\n    number: \"4\",\r\n    title: \"开始使用\",\r\n    description: \"打开网页即可享受舒适的护眼体验\",\r\n  },\r\n];\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.hero {\r\n  background: linear-gradient(\r\n    135deg,\r\n    var(--primary-color) 0%,\r\n    var(--primary-dark) 100%\r\n  );\r\n  padding: 6rem 0;\r\n  text-align: center;\r\n  color: white;\r\n\r\n  .plugin-logo {\r\n    width: 120px;\r\n    height: auto;\r\n    margin-bottom: 2rem;\r\n  }\r\n\r\n  h1 {\r\n    font-size: 3rem;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 1.2rem;\r\n    opacity: 0.9;\r\n    margin-bottom: 2rem;\r\n  }\r\n\r\n  .download-options {\r\n    display: flex;\r\n    justify-content: center;\r\n    gap: 2rem;\r\n    max-width: 800px;\r\n    margin: 0 auto;\r\n\r\n    @media (max-width: 768px) {\r\n      flex-direction: column;\r\n      align-items: center;\r\n    }\r\n\r\n    .version-option {\r\n      flex: 1;\r\n      max-width: 400px;\r\n    }\r\n\r\n    .download-btn {\r\n      display: inline-block;\r\n      padding: 1rem 2rem;\r\n      background: white;\r\n      color: var(--primary-color);\r\n      text-decoration: none;\r\n      border-radius: 30px;\r\n      font-weight: 500;\r\n      transition: var(--transition-base);\r\n      margin-bottom: 1rem;\r\n      width: 100%;\r\n\r\n      &:hover {\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\r\n      }\r\n    }\r\n\r\n    .version-desc {\r\n      font-size: 0.9rem;\r\n      opacity: 0.8;\r\n      line-height: 1.5;\r\n    }\r\n  }\r\n}\r\n\r\n.demo {\r\n  padding: 6rem 0;\r\n  background: white;\r\n\r\n  .container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 0 2rem;\r\n    text-align: center;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 2.5rem;\r\n    margin-bottom: 1rem;\r\n    color: var(--text-primary);\r\n  }\r\n\r\n  .section-subtitle {\r\n    font-size: 1.2rem;\r\n    color: var(--text-secondary);\r\n    margin-bottom: 3rem;\r\n    max-width: 800px;\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n  }\r\n\r\n  .demo-gallery {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 3rem;\r\n\r\n    @media (max-width: 992px) {\r\n      grid-template-columns: 1fr;\r\n      gap: 4rem;\r\n    }\r\n  }\r\n\r\n  .demo-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n\r\n    .demo-image-container {\r\n      width: 100%;\r\n      height: 350px;\r\n      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\r\n      border-radius: 12px;\r\n      overflow: hidden;\r\n      transition: all 0.3s ease;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      background-color: #f8f9fa;\r\n\r\n      &:hover {\r\n        transform: translateY(-10px);\r\n        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);\r\n      }\r\n    }\r\n\r\n    .demo-image {\r\n      max-width: 100%;\r\n      max-height: 100%;\r\n      object-fit: contain;\r\n      display: block;\r\n      border-radius: 12px;\r\n    }\r\n\r\n    .image-caption {\r\n      margin-top: 1.5rem;\r\n      font-size: 1.1rem;\r\n      color: var(--text-secondary);\r\n    }\r\n  }\r\n}\r\n\r\n.features {\r\n  padding: 6rem 0;\r\n  background: var(--bg-secondary);\r\n\r\n  .container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 0 2rem;\r\n  }\r\n\r\n  h2 {\r\n    text-align: center;\r\n    font-size: 2.5rem;\r\n    margin-bottom: 3rem;\r\n  }\r\n\r\n  .features-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(4, 1fr);\r\n    gap: 2rem;\r\n\r\n    @media (max-width: 992px) {\r\n      grid-template-columns: repeat(2, 1fr);\r\n    }\r\n\r\n    @media (max-width: 768px) {\r\n      grid-template-columns: 1fr;\r\n    }\r\n  }\r\n}\r\n\r\n.feature-card {\r\n  background: white;\r\n  padding: 2rem;\r\n  border-radius: 20px;\r\n  text-align: center;\r\n  transition: var(--transition-base);\r\n\r\n  &:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: var(--shadow-lg);\r\n  }\r\n\r\n  .feature-icon {\r\n    font-size: 2.5rem;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  h3 {\r\n    font-size: 1.5rem;\r\n    margin-bottom: 1rem;\r\n    color: var(--text-primary);\r\n  }\r\n\r\n  p {\r\n    color: var(--text-secondary);\r\n    line-height: 1.6;\r\n  }\r\n}\r\n\r\n// 使用说明部分的样式与 Highlight.vue 相同\r\n.usage {\r\n  padding: 6rem 0;\r\n\r\n  .container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 0 2rem;\r\n  }\r\n\r\n  h2 {\r\n    text-align: center;\r\n    font-size: 2.5rem;\r\n    margin-bottom: 3rem;\r\n  }\r\n\r\n  .usage-steps {\r\n    display: grid;\r\n    grid-template-columns: repeat(4, 1fr);\r\n    gap: 2rem;\r\n\r\n    @media (max-width: 992px) {\r\n      grid-template-columns: repeat(2, 1fr);\r\n    }\r\n\r\n    @media (max-width: 768px) {\r\n      grid-template-columns: 1fr;\r\n    }\r\n  }\r\n\r\n  .step {\r\n    text-align: center;\r\n    padding: 2rem;\r\n\r\n    .step-number {\r\n      width: 40px;\r\n      height: 40px;\r\n      background: var(--primary-color);\r\n      color: white;\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin: 0 auto 1rem;\r\n      font-weight: 500;\r\n    }\r\n\r\n    h3 {\r\n      font-size: 1.3rem;\r\n      margin-bottom: 1rem;\r\n      color: var(--text-primary);\r\n    }\r\n\r\n    p {\r\n      color: var(--text-secondary);\r\n      line-height: 1.6;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<script>\r\nexport default {\r\n  name: \"EyeshieldPage\",\r\n};\r\n</script>\r\n"], "mappings": "AAqFA,SAASA,SAAS,QAAQ,UAAU;AACpC,OAAOC,YAAY,MAAM,gCAAgC;AACzD,OAAOC,SAAS,MAAM,wBAAwB;AA+T9C,MAAAC,WAAA,GAAe;EACbC,IAAI,EAAE;AACR,CAAC;;;;;;IA/TD,MAAMC,QAAQ,GAAG,CACf;MACEC,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,IAAI,EAAE,GAAG;MACTC,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,CACF;IAED,MAAMC,KAAK,GAAG,CACZ;MACEC,MAAM,EAAE,GAAG;MACXH,KAAK,EAAE,MAAM;MACbC,WAAW,EACT;IACJ,CAAC,EACD;MACEE,MAAM,EAAE,GAAG;MACXH,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,EACD;MACEE,MAAM,EAAE,GAAG;MACXH,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,EACD;MACEE,MAAM,EAAE,GAAG;MACXH,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}