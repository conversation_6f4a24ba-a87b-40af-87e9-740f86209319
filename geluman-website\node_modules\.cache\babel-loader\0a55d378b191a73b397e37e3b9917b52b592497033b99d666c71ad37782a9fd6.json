{"ast": null, "code": "import { isString } from '@vue/shared';\nclass ElementPlusError extends Error {\n  constructor(m) {\n    super(m);\n    this.name = \"ElementPlusError\";\n  }\n}\nfunction throwError(scope, m) {\n  throw new ElementPlusError(`[${scope}] ${m}`);\n}\nfunction debugWarn(scope, message) {\n  if (process.env.NODE_ENV !== \"production\") {\n    const error = isString(scope) ? new ElementPlusError(`[${scope}] ${message}`) : scope;\n    console.warn(error);\n  }\n}\nexport { debugWarn, throwError };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}