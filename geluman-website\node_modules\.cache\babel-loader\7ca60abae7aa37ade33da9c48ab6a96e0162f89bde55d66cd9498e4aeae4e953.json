{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define(t) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_weekOfYear = t();\n}(this, function () {\n  \"use strict\";\n\n  var e = \"week\",\n    t = \"year\";\n  return function (i, n, r) {\n    var f = n.prototype;\n    f.week = function (i) {\n      if (void 0 === i && (i = null), null !== i) return this.add(7 * (i - this.week()), \"day\");\n      var n = this.$locale().yearStart || 1;\n      if (11 === this.month() && this.date() > 25) {\n        var f = r(this).startOf(t).add(1, t).date(n),\n          s = r(this).endOf(e);\n        if (f.isBefore(s)) return 1;\n      }\n      var a = r(this).startOf(t).date(n).startOf(e).subtract(1, \"millisecond\"),\n        o = this.diff(a, e, !0);\n      return o < 0 ? r(this).startOf(\"week\").week() : Math.ceil(o);\n    }, f.weeks = function (e) {\n      return void 0 === e && (e = null), this.week(e);\n    };\n  };\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}