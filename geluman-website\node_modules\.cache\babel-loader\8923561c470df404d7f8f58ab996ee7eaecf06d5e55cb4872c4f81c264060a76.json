{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { ref, computed } from 'vue';\nimport { castArray } from 'lodash-unified';\nimport { debugWarn } from '../../../utils/error.mjs';\nconst SCOPE = \"ElForm\";\nfunction useFormLabelWidth() {\n  const potentialLabelWidthArr = ref([]);\n  const autoLabelWidth = computed(() => {\n    if (!potentialLabelWidthArr.value.length) return \"0\";\n    const max = Math.max(...potentialLabelWidthArr.value);\n    return max ? `${max}px` : \"\";\n  });\n  function getLabelWidthIndex(width) {\n    const index = potentialLabelWidthArr.value.indexOf(width);\n    if (index === -1 && autoLabelWidth.value === \"0\") {\n      debugWarn(SCOPE, `unexpected width ${width}`);\n    }\n    return index;\n  }\n  function registerLabelWidth(val, oldVal) {\n    if (val && oldVal) {\n      const index = getLabelWidthIndex(oldVal);\n      potentialLabelWidthArr.value.splice(index, 1, val);\n    } else if (val) {\n      potentialLabelWidthArr.value.push(val);\n    }\n  }\n  function deregisterLabelWidth(val) {\n    const index = getLabelWidthIndex(val);\n    if (index > -1) {\n      potentialLabelWidthArr.value.splice(index, 1);\n    }\n  }\n  return {\n    autoLabelWidth,\n    registerLabelWidth,\n    deregisterLabelWidth\n  };\n}\nconst filterFields = (fields, props) => {\n  const normalized = castArray(props);\n  return normalized.length > 0 ? fields.filter(field => field.prop && normalized.includes(field.prop)) : fields;\n};\nexport { filterFields, useFormLabelWidth };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}