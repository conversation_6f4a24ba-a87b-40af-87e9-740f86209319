{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, normalizeStyle } from 'vue';\nimport { alphaSliderProps } from '../props/alpha-slider.mjs';\nimport { useAlphaSlider, useAlphaSliderDOM } from '../composables/use-alpha-slider.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nconst COMPONENT_NAME = \"ElColorAlphaSlider\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: alphaSliderProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const {\n      alpha,\n      alphaLabel,\n      bar,\n      thumb,\n      handleDrag,\n      handleClick,\n      handleKeydown\n    } = useAlphaSlider(props);\n    const {\n      rootKls,\n      barKls,\n      barStyle,\n      thumbKls,\n      thumbStyle,\n      update\n    } = useAlphaSliderDOM(props, {\n      bar,\n      thumb,\n      handleDrag\n    });\n    expose({\n      update,\n      bar,\n      thumb\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(rootKls))\n      }, [createElementVNode(\"div\", {\n        ref_key: \"bar\",\n        ref: bar,\n        class: normalizeClass(unref(barKls)),\n        style: normalizeStyle(unref(barStyle)),\n        onClick: unref(handleClick)\n      }, null, 14, [\"onClick\"]), createElementVNode(\"div\", {\n        ref_key: \"thumb\",\n        ref: thumb,\n        class: normalizeClass(unref(thumbKls)),\n        style: normalizeStyle(unref(thumbStyle)),\n        \"aria-label\": unref(alphaLabel),\n        \"aria-valuenow\": unref(alpha),\n        \"aria-orientation\": _ctx.vertical ? \"vertical\" : \"horizontal\",\n        \"aria-valuemin\": \"0\",\n        \"aria-valuemax\": \"100\",\n        role: \"slider\",\n        tabindex: \"0\",\n        onKeydown: unref(handleKeydown)\n      }, null, 46, [\"aria-label\", \"aria-valuenow\", \"aria-orientation\", \"onKeydown\"])], 2);\n    };\n  }\n});\nvar AlphaSlider = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"alpha-slider.vue\"]]);\nexport { AlphaSlider as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}