{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, computed, unref, openBlock, createElementBlock, normalizeClass, withModifiers, createBlock, Transition, withCtx, withDirectives, createElementVNode, createVNode, vShow, createCommentVNode, normalizeStyle, renderSlot, Fragment, renderList, toDisplayString } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';\nimport { carouselProps, carouselEmits } from './carousel.mjs';\nimport { useCarousel } from './use-carousel.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nconst COMPONENT_NAME = \"ElCarousel\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: carouselProps,\n  emits: carouselEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const {\n      root,\n      activeIndex,\n      arrowDisplay,\n      hasLabel,\n      hover,\n      isCardType,\n      items,\n      isVertical,\n      containerStyle,\n      handleButtonEnter,\n      handleButtonLeave,\n      isTransitioning,\n      handleIndicatorClick,\n      handleMouseEnter,\n      handleMouseLeave,\n      handleTransitionEnd,\n      setActiveItem,\n      prev,\n      next,\n      PlaceholderItem,\n      isTwoLengthShow,\n      throttledArrowClick,\n      throttledIndicatorHover\n    } = useCarousel(props, emit, COMPONENT_NAME);\n    const ns = useNamespace(\"carousel\");\n    const {\n      t\n    } = useLocale();\n    const carouselClasses = computed(() => {\n      const classes = [ns.b(), ns.m(props.direction)];\n      if (unref(isCardType)) {\n        classes.push(ns.m(\"card\"));\n      }\n      return classes;\n    });\n    const carouselContainer = computed(() => {\n      const classes = [ns.e(\"container\")];\n      if (props.motionBlur && unref(isTransitioning) && items.value.length > 1) {\n        classes.push(unref(isVertical) ? `${ns.namespace.value}-transitioning-vertical` : `${ns.namespace.value}-transitioning`);\n      }\n      return classes;\n    });\n    const indicatorsClasses = computed(() => {\n      const classes = [ns.e(\"indicators\"), ns.em(\"indicators\", props.direction)];\n      if (unref(hasLabel)) {\n        classes.push(ns.em(\"indicators\", \"labels\"));\n      }\n      if (props.indicatorPosition === \"outside\") {\n        classes.push(ns.em(\"indicators\", \"outside\"));\n      }\n      if (unref(isVertical)) {\n        classes.push(ns.em(\"indicators\", \"right\"));\n      }\n      return classes;\n    });\n    expose({\n      activeIndex,\n      setActiveItem,\n      prev,\n      next\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"root\",\n        ref: root,\n        class: normalizeClass(unref(carouselClasses)),\n        onMouseenter: withModifiers(unref(handleMouseEnter), [\"stop\"]),\n        onMouseleave: withModifiers(unref(handleMouseLeave), [\"stop\"])\n      }, [unref(arrowDisplay) ? (openBlock(), createBlock(Transition, {\n        key: 0,\n        name: \"carousel-arrow-left\",\n        persisted: \"\"\n      }, {\n        default: withCtx(() => [withDirectives(createElementVNode(\"button\", {\n          type: \"button\",\n          class: normalizeClass([unref(ns).e(\"arrow\"), unref(ns).em(\"arrow\", \"left\")]),\n          \"aria-label\": unref(t)(\"el.carousel.leftArrow\"),\n          onMouseenter: $event => unref(handleButtonEnter)(\"left\"),\n          onMouseleave: unref(handleButtonLeave),\n          onClick: withModifiers($event => unref(throttledArrowClick)(unref(activeIndex) - 1), [\"stop\"])\n        }, [createVNode(unref(ElIcon), null, {\n          default: withCtx(() => [createVNode(unref(ArrowLeft))]),\n          _: 1\n        })], 42, [\"aria-label\", \"onMouseenter\", \"onMouseleave\", \"onClick\"]), [[vShow, (_ctx.arrow === \"always\" || unref(hover)) && (props.loop || unref(activeIndex) > 0)]])]),\n        _: 1\n      })) : createCommentVNode(\"v-if\", true), unref(arrowDisplay) ? (openBlock(), createBlock(Transition, {\n        key: 1,\n        name: \"carousel-arrow-right\",\n        persisted: \"\"\n      }, {\n        default: withCtx(() => [withDirectives(createElementVNode(\"button\", {\n          type: \"button\",\n          class: normalizeClass([unref(ns).e(\"arrow\"), unref(ns).em(\"arrow\", \"right\")]),\n          \"aria-label\": unref(t)(\"el.carousel.rightArrow\"),\n          onMouseenter: $event => unref(handleButtonEnter)(\"right\"),\n          onMouseleave: unref(handleButtonLeave),\n          onClick: withModifiers($event => unref(throttledArrowClick)(unref(activeIndex) + 1), [\"stop\"])\n        }, [createVNode(unref(ElIcon), null, {\n          default: withCtx(() => [createVNode(unref(ArrowRight))]),\n          _: 1\n        })], 42, [\"aria-label\", \"onMouseenter\", \"onMouseleave\", \"onClick\"]), [[vShow, (_ctx.arrow === \"always\" || unref(hover)) && (props.loop || unref(activeIndex) < unref(items).length - 1)]])]),\n        _: 1\n      })) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass(unref(carouselContainer)),\n        style: normalizeStyle(unref(containerStyle)),\n        onTransitionend: unref(handleTransitionEnd)\n      }, [createVNode(unref(PlaceholderItem)), renderSlot(_ctx.$slots, \"default\")], 46, [\"onTransitionend\"]), _ctx.indicatorPosition !== \"none\" ? (openBlock(), createElementBlock(\"ul\", {\n        key: 2,\n        class: normalizeClass(unref(indicatorsClasses))\n      }, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(items), (item, index) => {\n        return withDirectives((openBlock(), createElementBlock(\"li\", {\n          key: index,\n          class: normalizeClass([unref(ns).e(\"indicator\"), unref(ns).em(\"indicator\", _ctx.direction), unref(ns).is(\"active\", index === unref(activeIndex))]),\n          onMouseenter: $event => unref(throttledIndicatorHover)(index),\n          onClick: withModifiers($event => unref(handleIndicatorClick)(index), [\"stop\"])\n        }, [createElementVNode(\"button\", {\n          class: normalizeClass(unref(ns).e(\"button\")),\n          \"aria-label\": unref(t)(\"el.carousel.indicator\", {\n            index: index + 1\n          })\n        }, [unref(hasLabel) ? (openBlock(), createElementBlock(\"span\", {\n          key: 0\n        }, toDisplayString(item.props.label), 1)) : createCommentVNode(\"v-if\", true)], 10, [\"aria-label\"])], 42, [\"onMouseenter\", \"onClick\"])), [[vShow, unref(isTwoLengthShow)(index)]]);\n      }), 128))], 2)) : createCommentVNode(\"v-if\", true), props.motionBlur ? (openBlock(), createElementBlock(\"svg\", {\n        key: 3,\n        xmlns: \"http://www.w3.org/2000/svg\",\n        version: \"1.1\",\n        style: {\n          \"display\": \"none\"\n        }\n      }, [createElementVNode(\"defs\", null, [createElementVNode(\"filter\", {\n        id: \"elCarouselHorizontal\"\n      }, [createElementVNode(\"feGaussianBlur\", {\n        in: \"SourceGraphic\",\n        stdDeviation: \"12,0\"\n      })]), createElementVNode(\"filter\", {\n        id: \"elCarouselVertical\"\n      }, [createElementVNode(\"feGaussianBlur\", {\n        in: \"SourceGraphic\",\n        stdDeviation: \"0,10\"\n      })])])])) : createCommentVNode(\"v-if\", true)], 42, [\"onMouseenter\", \"onMouseleave\"]);\n    };\n  }\n});\nvar Carousel = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"carousel.vue\"]]);\nexport { Carousel as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}