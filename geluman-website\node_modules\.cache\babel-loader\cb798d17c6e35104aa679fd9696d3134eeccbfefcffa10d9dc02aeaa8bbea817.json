{"ast": null, "code": "import { columns, dataType, fixedDataType, requiredNumber, classType, styleType } from './common.mjs';\nimport { tableV2HeaderProps } from './header.mjs';\nimport { tableV2RowProps } from './row.mjs';\nimport { virtualizedListProps, virtualizedGridProps } from '../../virtual-list/src/props.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst tableV2GridProps = buildProps({\n  columns,\n  data: dataType,\n  fixedData: fixedDataType,\n  estimatedRowHeight: tableV2RowProps.estimatedRowHeight,\n  width: requiredNumber,\n  height: requiredNumber,\n  headerWidth: requiredNumber,\n  headerHeight: tableV2HeaderProps.headerHeight,\n  bodyWidth: requiredNumber,\n  rowHeight: requiredNumber,\n  cache: virtualizedListProps.cache,\n  useIsScrolling: Boolean,\n  scrollbarAlwaysOn: virtualizedGridProps.scrollbarAlwaysOn,\n  scrollbarStartGap: virtualizedGridProps.scrollbarStartGap,\n  scrollbarEndGap: virtualizedGridProps.scrollbarEndGap,\n  class: classType,\n  style: styleType,\n  containerStyle: styleType,\n  getRowHeight: {\n    type: definePropType(Function),\n    required: true\n  },\n  rowKey: tableV2RowProps.rowKey,\n  onRowsRendered: {\n    type: definePropType(Function)\n  },\n  onScroll: {\n    type: definePropType(Function)\n  }\n});\nexport { tableV2GridProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}