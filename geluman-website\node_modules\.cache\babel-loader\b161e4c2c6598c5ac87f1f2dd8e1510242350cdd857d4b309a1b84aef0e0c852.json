{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, createVNode } from 'vue';\nimport { tableV2HeaderRowProps } from '../header-row.mjs';\nimport { isArray } from '@vue/shared';\nconst TableV2HeaderRow = defineComponent({\n  name: \"ElTableV2HeaderRow\",\n  props: tableV2HeaderRowProps,\n  setup(props, {\n    slots\n  }) {\n    return () => {\n      const {\n        columns,\n        columnsStyles,\n        headerIndex,\n        style\n      } = props;\n      let Cells = columns.map((column, columnIndex) => {\n        return slots.cell({\n          columns,\n          column,\n          columnIndex,\n          headerIndex,\n          style: columnsStyles[column.key]\n        });\n      });\n      if (slots.header) {\n        Cells = slots.header({\n          cells: Cells.map(node => {\n            if (isArray(node) && node.length === 1) {\n              return node[0];\n            }\n            return node;\n          }),\n          columns,\n          headerIndex\n        });\n      }\n      return createVNode(\"div\", {\n        \"class\": props.class,\n        \"style\": style,\n        \"role\": \"row\"\n      }, [Cells]);\n    };\n  }\n});\nvar HeaderRow = TableV2HeaderRow;\nexport { HeaderRow as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}