{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { getCurrentInstance, onBeforeMount, onMounted, onUpdated, onUnmounted, computed } from 'vue';\nfunction useLayoutObserver(root) {\n  const instance = getCurrentInstance();\n  onBeforeMount(() => {\n    tableLayout.value.addObserver(instance);\n  });\n  onMounted(() => {\n    onColumnsChange(tableLayout.value);\n    onScrollableChange(tableLayout.value);\n  });\n  onUpdated(() => {\n    onColumnsChange(tableLayout.value);\n    onScrollableChange(tableLayout.value);\n  });\n  onUnmounted(() => {\n    tableLayout.value.removeObserver(instance);\n  });\n  const tableLayout = computed(() => {\n    const layout = root.layout;\n    if (!layout) {\n      throw new Error(\"Can not find table layout.\");\n    }\n    return layout;\n  });\n  const onColumnsChange = layout => {\n    var _a;\n    const cols = ((_a = root.vnode.el) == null ? void 0 : _a.querySelectorAll(\"colgroup > col\")) || [];\n    if (!cols.length) return;\n    const flattenColumns = layout.getFlattenColumns();\n    const columnsMap = {};\n    flattenColumns.forEach(column => {\n      columnsMap[column.id] = column;\n    });\n    for (let i = 0, j = cols.length; i < j; i++) {\n      const col = cols[i];\n      const name = col.getAttribute(\"name\");\n      const column = columnsMap[name];\n      if (column) {\n        col.setAttribute(\"width\", column.realWidth || column.width);\n      }\n    }\n  };\n  const onScrollableChange = layout => {\n    var _a, _b;\n    const cols = ((_a = root.vnode.el) == null ? void 0 : _a.querySelectorAll(\"colgroup > col[name=gutter]\")) || [];\n    for (let i = 0, j = cols.length; i < j; i++) {\n      const col = cols[i];\n      col.setAttribute(\"width\", layout.scrollY.value ? layout.gutterWidth : \"0\");\n    }\n    const ths = ((_b = root.vnode.el) == null ? void 0 : _b.querySelectorAll(\"th.gutter\")) || [];\n    for (let i = 0, j = ths.length; i < j; i++) {\n      const th = ths[i];\n      th.style.width = layout.scrollY.value ? `${layout.gutterWidth}px` : \"0\";\n      th.style.display = layout.scrollY.value ? \"\" : \"none\";\n    }\n  };\n  return {\n    tableLayout: tableLayout.value,\n    onColumnsChange,\n    onScrollableChange\n  };\n}\nexport { useLayoutObserver as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}