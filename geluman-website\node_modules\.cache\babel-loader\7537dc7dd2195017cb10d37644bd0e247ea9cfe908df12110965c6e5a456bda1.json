{"ast": null, "code": "import { isArray } from '@vue/shared';\nfunction isValidValue(val) {\n  return val || val === 0;\n}\nfunction isValidArray(val) {\n  return isArray(val) && val.length;\n}\nfunction toValidArray(val) {\n  return isArray(val) ? val : isValidValue(val) ? [val] : [];\n}\nfunction treeFind(treeData, findCallback, getChildren, resultCallback, parent) {\n  for (let i = 0; i < treeData.length; i++) {\n    const data = treeData[i];\n    if (findCallback(data, i, treeData, parent)) {\n      return resultCallback ? resultCallback(data, i, treeData, parent) : data;\n    } else {\n      const children = getChildren(data);\n      if (isValidArray(children)) {\n        const find = treeFind(children, findCallback, getChildren, resultCallback, data);\n        if (find) return find;\n      }\n    }\n  }\n}\nfunction treeEach(treeData, callback, getChildren, parent) {\n  for (let i = 0; i < treeData.length; i++) {\n    const data = treeData[i];\n    callback(data, i, treeData, parent);\n    const children = getChildren(data);\n    if (isValidArray(children)) {\n      treeEach(children, callback, getChildren, data);\n    }\n  }\n}\nexport { isValidArray, isValidValue, toValidArray, treeEach, treeFind };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}