{"ast": null, "code": "import createCompounder from './_createCompounder.js';\n\n/**\n * Converts `string` to\n * [snake case](https://en.wikipedia.org/wiki/Snake_case).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the snake cased string.\n * @example\n *\n * _.snakeCase('Foo Bar');\n * // => 'foo_bar'\n *\n * _.snakeCase('fooBar');\n * // => 'foo_bar'\n *\n * _.snakeCase('--FOO-BAR--');\n * // => 'foo_bar'\n */\nvar snakeCase = createCompounder(function (result, word, index) {\n  return result + (index ? '_' : '') + word.toLowerCase();\n});\nexport default snakeCase;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}