{"ast": null, "code": "import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar weakSetTag = '[object WeakSet]';\n\n/**\n * Checks if `value` is classified as a `WeakSet` object.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a weak set, else `false`.\n * @example\n *\n * _.isWeakSet(new WeakSet);\n * // => true\n *\n * _.isWeakSet(new Set);\n * // => false\n */\nfunction isWeakSet(value) {\n  return isObjectLike(value) && baseGetTag(value) == weakSetTag;\n}\nexport default isWeakSet;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}