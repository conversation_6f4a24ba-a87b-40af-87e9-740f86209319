{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { defineComponent, useSlots, reactive, ref, computed, watch, h, Comment, openBlock, createElementBlock, normalizeClass, unref, createVNode, withCtx, renderSlot, createElementVNode, toDisplayString, createCommentVNode } from 'vue';\nimport { ElButton } from '../../button/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';\nimport { transferProps, transferEmits } from './transfer.mjs';\nimport TransferPanel from './transfer-panel2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useComputedData } from './composables/use-computed-data.mjs';\nimport { useMove } from './composables/use-move.mjs';\nimport { useCheckedChange } from './composables/use-checked-change.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormItem } from '../../form/src/hooks/use-form-item.mjs';\nimport { usePropsAlias } from './composables/use-props-alias.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { isEmpty, isUndefined } from '../../../utils/types.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTransfer\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: transferProps,\n  emits: transferEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const slots = useSlots();\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"transfer\");\n    const {\n      formItem\n    } = useFormItem();\n    const checkedState = reactive({\n      leftChecked: [],\n      rightChecked: []\n    });\n    const propsAlias = usePropsAlias(props);\n    const {\n      sourceData,\n      targetData\n    } = useComputedData(props);\n    const {\n      onSourceCheckedChange,\n      onTargetCheckedChange\n    } = useCheckedChange(checkedState, emit);\n    const {\n      addToLeft,\n      addToRight\n    } = useMove(props, checkedState, emit);\n    const leftPanel = ref();\n    const rightPanel = ref();\n    const clearQuery = which => {\n      switch (which) {\n        case \"left\":\n          leftPanel.value.query = \"\";\n          break;\n        case \"right\":\n          rightPanel.value.query = \"\";\n          break;\n      }\n    };\n    const hasButtonTexts = computed(() => props.buttonTexts.length === 2);\n    const leftPanelTitle = computed(() => props.titles[0] || t(\"el.transfer.titles.0\"));\n    const rightPanelTitle = computed(() => props.titles[1] || t(\"el.transfer.titles.1\"));\n    const panelFilterPlaceholder = computed(() => props.filterPlaceholder || t(\"el.transfer.filterPlaceholder\"));\n    watch(() => props.modelValue, () => {\n      var _a;\n      if (props.validateEvent) {\n        (_a = formItem == null ? void 0 : formItem.validate) == null ? void 0 : _a.call(formItem, \"change\").catch(err => debugWarn(err));\n      }\n    });\n    const optionRender = computed(() => option => {\n      var _a;\n      if (props.renderContent) return props.renderContent(h, option);\n      const defaultSlotVNodes = (((_a = slots.default) == null ? void 0 : _a.call(slots, {\n        option\n      })) || []).filter(node => node.type !== Comment);\n      if (defaultSlotVNodes.length) {\n        return defaultSlotVNodes;\n      }\n      return h(\"span\", option[propsAlias.value.label] || option[propsAlias.value.key]);\n    });\n    expose({\n      clearQuery,\n      leftPanel,\n      rightPanel\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(ns).b())\n      }, [createVNode(TransferPanel, {\n        ref_key: \"leftPanel\",\n        ref: leftPanel,\n        data: unref(sourceData),\n        \"option-render\": unref(optionRender),\n        placeholder: unref(panelFilterPlaceholder),\n        title: unref(leftPanelTitle),\n        filterable: _ctx.filterable,\n        format: _ctx.format,\n        \"filter-method\": _ctx.filterMethod,\n        \"default-checked\": _ctx.leftDefaultChecked,\n        props: props.props,\n        onCheckedChange: unref(onSourceCheckedChange)\n      }, {\n        empty: withCtx(() => [renderSlot(_ctx.$slots, \"left-empty\")]),\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"left-footer\")]),\n        _: 3\n      }, 8, [\"data\", \"option-render\", \"placeholder\", \"title\", \"filterable\", \"format\", \"filter-method\", \"default-checked\", \"props\", \"onCheckedChange\"]), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"buttons\"))\n      }, [createVNode(unref(ElButton), {\n        type: \"primary\",\n        class: normalizeClass([unref(ns).e(\"button\"), unref(ns).is(\"with-texts\", unref(hasButtonTexts))]),\n        disabled: unref(isEmpty)(checkedState.rightChecked),\n        onClick: unref(addToLeft)\n      }, {\n        default: withCtx(() => [createVNode(unref(ElIcon), null, {\n          default: withCtx(() => [createVNode(unref(ArrowLeft))]),\n          _: 1\n        }), !unref(isUndefined)(_ctx.buttonTexts[0]) ? (openBlock(), createElementBlock(\"span\", {\n          key: 0\n        }, toDisplayString(_ctx.buttonTexts[0]), 1)) : createCommentVNode(\"v-if\", true)]),\n        _: 1\n      }, 8, [\"class\", \"disabled\", \"onClick\"]), createVNode(unref(ElButton), {\n        type: \"primary\",\n        class: normalizeClass([unref(ns).e(\"button\"), unref(ns).is(\"with-texts\", unref(hasButtonTexts))]),\n        disabled: unref(isEmpty)(checkedState.leftChecked),\n        onClick: unref(addToRight)\n      }, {\n        default: withCtx(() => [!unref(isUndefined)(_ctx.buttonTexts[1]) ? (openBlock(), createElementBlock(\"span\", {\n          key: 0\n        }, toDisplayString(_ctx.buttonTexts[1]), 1)) : createCommentVNode(\"v-if\", true), createVNode(unref(ElIcon), null, {\n          default: withCtx(() => [createVNode(unref(ArrowRight))]),\n          _: 1\n        })]),\n        _: 1\n      }, 8, [\"class\", \"disabled\", \"onClick\"])], 2), createVNode(TransferPanel, {\n        ref_key: \"rightPanel\",\n        ref: rightPanel,\n        data: unref(targetData),\n        \"option-render\": unref(optionRender),\n        placeholder: unref(panelFilterPlaceholder),\n        filterable: _ctx.filterable,\n        format: _ctx.format,\n        \"filter-method\": _ctx.filterMethod,\n        title: unref(rightPanelTitle),\n        \"default-checked\": _ctx.rightDefaultChecked,\n        props: props.props,\n        onCheckedChange: unref(onTargetCheckedChange)\n      }, {\n        empty: withCtx(() => [renderSlot(_ctx.$slots, \"right-empty\")]),\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"right-footer\")]),\n        _: 3\n      }, 8, [\"data\", \"option-render\", \"placeholder\", \"filterable\", \"format\", \"filter-method\", \"title\", \"default-checked\", \"props\", \"onCheckedChange\"])], 2);\n    };\n  }\n});\nvar Transfer = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"transfer.vue\"]]);\nexport { Transfer as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}