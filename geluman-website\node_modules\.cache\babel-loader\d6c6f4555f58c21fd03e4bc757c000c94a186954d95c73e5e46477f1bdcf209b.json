{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { isVNode, render, createVNode } from 'vue';\nimport MessageConstructor from './message2.mjs';\nimport { messageTypes, messageDefaults } from './message.mjs';\nimport { instances } from './instance.mjs';\nimport { messageConfig } from '../../config-provider/src/config-provider.mjs';\nimport { isClient } from '@vueuse/core';\nimport { isNumber, isElement, isBoolean } from '../../../utils/types.mjs';\nimport { isString, isFunction } from '@vue/shared';\nimport { debugWarn } from '../../../utils/error.mjs';\nlet seed = 1;\nconst normalizeOptions = params => {\n  const options = !params || isString(params) || isVNode(params) || isFunction(params) ? {\n    message: params\n  } : params;\n  const normalized = {\n    ...messageDefaults,\n    ...options\n  };\n  if (!normalized.appendTo) {\n    normalized.appendTo = document.body;\n  } else if (isString(normalized.appendTo)) {\n    let appendTo = document.querySelector(normalized.appendTo);\n    if (!isElement(appendTo)) {\n      debugWarn(\"ElMessage\", \"the appendTo option is not an HTMLElement. Falling back to document.body.\");\n      appendTo = document.body;\n    }\n    normalized.appendTo = appendTo;\n  }\n  if (isBoolean(messageConfig.grouping) && !normalized.grouping) {\n    normalized.grouping = messageConfig.grouping;\n  }\n  if (isNumber(messageConfig.duration) && normalized.duration === 3e3) {\n    normalized.duration = messageConfig.duration;\n  }\n  if (isNumber(messageConfig.offset) && normalized.offset === 16) {\n    normalized.offset = messageConfig.offset;\n  }\n  if (isBoolean(messageConfig.showClose) && !normalized.showClose) {\n    normalized.showClose = messageConfig.showClose;\n  }\n  return normalized;\n};\nconst closeMessage = instance => {\n  const idx = instances.indexOf(instance);\n  if (idx === -1) return;\n  instances.splice(idx, 1);\n  const {\n    handler\n  } = instance;\n  handler.close();\n};\nconst createMessage = ({\n  appendTo,\n  ...options\n}, context) => {\n  const id = `message_${seed++}`;\n  const userOnClose = options.onClose;\n  const container = document.createElement(\"div\");\n  const props = {\n    ...options,\n    id,\n    onClose: () => {\n      userOnClose == null ? void 0 : userOnClose();\n      closeMessage(instance);\n    },\n    onDestroy: () => {\n      render(null, container);\n    }\n  };\n  const vnode = createVNode(MessageConstructor, props, isFunction(props.message) || isVNode(props.message) ? {\n    default: isFunction(props.message) ? props.message : () => props.message\n  } : null);\n  vnode.appContext = context || message._context;\n  render(vnode, container);\n  appendTo.appendChild(container.firstElementChild);\n  const vm = vnode.component;\n  const handler = {\n    close: () => {\n      vm.exposed.close();\n    }\n  };\n  const instance = {\n    id,\n    vnode,\n    vm,\n    handler,\n    props: vnode.component.props\n  };\n  return instance;\n};\nconst message = (options = {}, context) => {\n  if (!isClient) return {\n    close: () => void 0\n  };\n  const normalized = normalizeOptions(options);\n  if (normalized.grouping && instances.length) {\n    const instance2 = instances.find(({\n      vnode: vm\n    }) => {\n      var _a;\n      return ((_a = vm.props) == null ? void 0 : _a.message) === normalized.message;\n    });\n    if (instance2) {\n      instance2.props.repeatNum += 1;\n      instance2.props.type = normalized.type;\n      return instance2.handler;\n    }\n  }\n  if (isNumber(messageConfig.max) && instances.length >= messageConfig.max) {\n    return {\n      close: () => void 0\n    };\n  }\n  const instance = createMessage(normalized, context);\n  instances.push(instance);\n  return instance.handler;\n};\nmessageTypes.forEach(type => {\n  message[type] = (options = {}, appContext) => {\n    const normalized = normalizeOptions(options);\n    return message({\n      ...normalized,\n      type\n    }, appContext);\n  };\n});\nfunction closeAll(type) {\n  const instancesToClose = [...instances];\n  for (const instance of instancesToClose) {\n    if (!type || type === instance.props.type) {\n      instance.handler.close();\n    }\n  }\n}\nmessage.closeAll = closeAll;\nmessage._context = null;\nexport { closeAll, message as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}