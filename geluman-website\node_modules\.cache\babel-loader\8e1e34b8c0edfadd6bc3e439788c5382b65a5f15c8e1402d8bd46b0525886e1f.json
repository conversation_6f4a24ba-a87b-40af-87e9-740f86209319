{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, getCurrentInstance, watch, provide, openBlock, createElementBlock, normalizeClass, unref, renderSlot } from 'vue';\nimport { stepsProps, stepsEmits } from './steps.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useOrderedChildren } from '../../../hooks/use-ordered-children/index.mjs';\nimport { CHANGE_EVENT } from '../../../constants/event.mjs';\nconst __default__ = defineComponent({\n  name: \"ElSteps\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: stepsProps,\n  emits: stepsEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"steps\");\n    const {\n      children: steps,\n      addChild: addStep,\n      removeChild: removeStep\n    } = useOrderedChildren(getCurrentInstance(), \"ElStep\");\n    watch(steps, () => {\n      steps.value.forEach((instance, index) => {\n        instance.setIndex(index);\n      });\n    });\n    provide(\"ElSteps\", {\n      props,\n      steps,\n      addStep,\n      removeStep\n    });\n    watch(() => props.active, (newVal, oldVal) => {\n      emit(CHANGE_EVENT, newVal, oldVal);\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ns).b(), unref(ns).m(_ctx.simple ? \"simple\" : _ctx.direction)])\n      }, [renderSlot(_ctx.$slots, \"default\")], 2);\n    };\n  }\n});\nvar Steps = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"steps.vue\"]]);\nexport { Steps as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}