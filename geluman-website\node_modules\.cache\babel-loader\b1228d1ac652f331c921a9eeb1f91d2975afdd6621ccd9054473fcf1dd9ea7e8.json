{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nconst FOCUSABLE_ELEMENT_SELECTORS = `a[href],button:not([disabled]),button:not([hidden]),:not([tabindex=\"-1\"]),input:not([disabled]),input:not([type=\"hidden\"]),select:not([disabled]),textarea:not([disabled])`;\nconst isVisible = element => {\n  if (process.env.NODE_ENV === \"test\") return true;\n  const computed = getComputedStyle(element);\n  return computed.position === \"fixed\" ? false : element.offsetParent !== null;\n};\nconst obtainAllFocusableElements = element => {\n  return Array.from(element.querySelectorAll(FOCUSABLE_ELEMENT_SELECTORS)).filter(item => isFocusable(item) && isVisible(item));\n};\nconst isFocusable = element => {\n  if (element.tabIndex > 0 || element.tabIndex === 0 && element.getAttribute(\"tabIndex\") !== null) {\n    return true;\n  }\n  if (element.tabIndex < 0 || element.hasAttribute(\"disabled\") || element.getAttribute(\"aria-disabled\") === \"true\") {\n    return false;\n  }\n  switch (element.nodeName) {\n    case \"A\":\n      {\n        return !!element.href && element.rel !== \"ignore\";\n      }\n    case \"INPUT\":\n      {\n        return !(element.type === \"hidden\" || element.type === \"file\");\n      }\n    case \"BUTTON\":\n    case \"SELECT\":\n    case \"TEXTAREA\":\n      {\n        return true;\n      }\n    default:\n      {\n        return false;\n      }\n  }\n};\nconst attemptFocus = element => {\n  var _a;\n  if (!isFocusable(element)) {\n    return false;\n  }\n  (_a = element.focus) == null ? void 0 : _a.call(element);\n  return document.activeElement === element;\n};\nconst triggerEvent = function (elm, name, ...opts) {\n  let eventName;\n  if (name.includes(\"mouse\") || name.includes(\"click\")) {\n    eventName = \"MouseEvents\";\n  } else if (name.includes(\"key\")) {\n    eventName = \"KeyboardEvent\";\n  } else {\n    eventName = \"HTMLEvents\";\n  }\n  const evt = document.createEvent(eventName);\n  evt.initEvent(name, ...opts);\n  elm.dispatchEvent(evt);\n  return elm;\n};\nconst isLeaf = el => !el.getAttribute(\"aria-owns\");\nconst getSibling = (el, distance, elClass) => {\n  const {\n    parentNode\n  } = el;\n  if (!parentNode) return null;\n  const siblings = parentNode.querySelectorAll(elClass);\n  const index = Array.prototype.indexOf.call(siblings, el);\n  return siblings[index + distance] || null;\n};\nconst focusNode = el => {\n  if (!el) return;\n  el.focus();\n  !isLeaf(el) && el.click();\n};\nexport { attemptFocus, focusNode, getSibling, isFocusable, isLeaf, isVisible, obtainAllFocusableElements, triggerEvent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}