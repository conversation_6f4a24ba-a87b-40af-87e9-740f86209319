{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { defineComponent, inject, ref, computed, watch, provide, getCurrentInstance, resolveComponent, openBlock, createElementBlock, normalizeClass, Fragment, renderList, createBlock, renderSlot, createElementVNode, toDisplayString, createCommentVNode, withDirectives, vShow } from 'vue';\nimport { selectKey } from '../../select/src/token.mjs';\nimport TreeStore from './model/tree-store.mjs';\nimport { getNodeKey, handleCurrentChange } from './model/util.mjs';\nimport ElTreeNode from './tree-node.mjs';\nimport { useNodeExpandEventBroadcast } from './model/useNodeExpandEventBroadcast.mjs';\nimport { useDragNodeHandler } from './model/useDragNode.mjs';\nimport { useKeydown } from './model/useKeydown.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { formItemContextKey } from '../../form/src/constants.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElTree\",\n  components: {\n    ElTreeNode\n  },\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    },\n    emptyText: {\n      type: String\n    },\n    renderAfterExpand: {\n      type: Boolean,\n      default: true\n    },\n    nodeKey: String,\n    checkStrictly: Boolean,\n    defaultExpandAll: Boolean,\n    expandOnClickNode: {\n      type: Boolean,\n      default: true\n    },\n    checkOnClickNode: Boolean,\n    checkOnClickLeaf: {\n      type: Boolean,\n      default: true\n    },\n    checkDescendants: {\n      type: Boolean,\n      default: false\n    },\n    autoExpandParent: {\n      type: Boolean,\n      default: true\n    },\n    defaultCheckedKeys: Array,\n    defaultExpandedKeys: Array,\n    currentNodeKey: [String, Number],\n    renderContent: Function,\n    showCheckbox: {\n      type: Boolean,\n      default: false\n    },\n    draggable: {\n      type: Boolean,\n      default: false\n    },\n    allowDrag: Function,\n    allowDrop: Function,\n    props: {\n      type: Object,\n      default: () => ({\n        children: \"children\",\n        label: \"label\",\n        disabled: \"disabled\"\n      })\n    },\n    lazy: {\n      type: Boolean,\n      default: false\n    },\n    highlightCurrent: Boolean,\n    load: Function,\n    filterNodeMethod: Function,\n    accordion: Boolean,\n    indent: {\n      type: Number,\n      default: 18\n    },\n    icon: {\n      type: iconPropType\n    }\n  },\n  emits: [\"check-change\", \"current-change\", \"node-click\", \"node-contextmenu\", \"node-collapse\", \"node-expand\", \"check\", \"node-drag-start\", \"node-drag-end\", \"node-drop\", \"node-drag-leave\", \"node-drag-enter\", \"node-drag-over\"],\n  setup(props, ctx) {\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"tree\");\n    const selectInfo = inject(selectKey, null);\n    const store = ref(new TreeStore({\n      key: props.nodeKey,\n      data: props.data,\n      lazy: props.lazy,\n      props: props.props,\n      load: props.load,\n      currentNodeKey: props.currentNodeKey,\n      checkStrictly: props.checkStrictly,\n      checkDescendants: props.checkDescendants,\n      defaultCheckedKeys: props.defaultCheckedKeys,\n      defaultExpandedKeys: props.defaultExpandedKeys,\n      autoExpandParent: props.autoExpandParent,\n      defaultExpandAll: props.defaultExpandAll,\n      filterNodeMethod: props.filterNodeMethod\n    }));\n    store.value.initialize();\n    const root = ref(store.value.root);\n    const currentNode = ref(null);\n    const el$ = ref(null);\n    const dropIndicator$ = ref(null);\n    const {\n      broadcastExpanded\n    } = useNodeExpandEventBroadcast(props);\n    const {\n      dragState\n    } = useDragNodeHandler({\n      props,\n      ctx,\n      el$,\n      dropIndicator$,\n      store\n    });\n    useKeydown({\n      el$\n    }, store);\n    const isEmpty = computed(() => {\n      const {\n        childNodes\n      } = root.value;\n      const hasFilteredOptions = selectInfo ? selectInfo.hasFilteredOptions !== 0 : false;\n      return (!childNodes || childNodes.length === 0 || childNodes.every(({\n        visible\n      }) => !visible)) && !hasFilteredOptions;\n    });\n    watch(() => props.currentNodeKey, newVal => {\n      store.value.setCurrentNodeKey(newVal);\n    });\n    watch(() => props.defaultCheckedKeys, newVal => {\n      store.value.setDefaultCheckedKey(newVal);\n    });\n    watch(() => props.defaultExpandedKeys, newVal => {\n      store.value.setDefaultExpandedKeys(newVal);\n    });\n    watch(() => props.data, newVal => {\n      store.value.setData(newVal);\n    }, {\n      deep: true\n    });\n    watch(() => props.checkStrictly, newVal => {\n      store.value.checkStrictly = newVal;\n    });\n    const filter = value => {\n      if (!props.filterNodeMethod) throw new Error(\"[Tree] filterNodeMethod is required when filter\");\n      store.value.filter(value);\n    };\n    const getNodeKey$1 = node => {\n      return getNodeKey(props.nodeKey, node.data);\n    };\n    const getNodePath = data => {\n      if (!props.nodeKey) throw new Error(\"[Tree] nodeKey is required in getNodePath\");\n      const node = store.value.getNode(data);\n      if (!node) return [];\n      const path = [node.data];\n      let parent = node.parent;\n      while (parent && parent !== root.value) {\n        path.push(parent.data);\n        parent = parent.parent;\n      }\n      return path.reverse();\n    };\n    const getCheckedNodes = (leafOnly, includeHalfChecked) => {\n      return store.value.getCheckedNodes(leafOnly, includeHalfChecked);\n    };\n    const getCheckedKeys = leafOnly => {\n      return store.value.getCheckedKeys(leafOnly);\n    };\n    const getCurrentNode = () => {\n      const currentNode2 = store.value.getCurrentNode();\n      return currentNode2 ? currentNode2.data : null;\n    };\n    const getCurrentKey = () => {\n      if (!props.nodeKey) throw new Error(\"[Tree] nodeKey is required in getCurrentKey\");\n      const currentNode2 = getCurrentNode();\n      return currentNode2 ? currentNode2[props.nodeKey] : null;\n    };\n    const setCheckedNodes = (nodes, leafOnly) => {\n      if (!props.nodeKey) throw new Error(\"[Tree] nodeKey is required in setCheckedNodes\");\n      store.value.setCheckedNodes(nodes, leafOnly);\n    };\n    const setCheckedKeys = (keys, leafOnly) => {\n      if (!props.nodeKey) throw new Error(\"[Tree] nodeKey is required in setCheckedKeys\");\n      store.value.setCheckedKeys(keys, leafOnly);\n    };\n    const setChecked = (data, checked, deep) => {\n      store.value.setChecked(data, checked, deep);\n    };\n    const getHalfCheckedNodes = () => {\n      return store.value.getHalfCheckedNodes();\n    };\n    const getHalfCheckedKeys = () => {\n      return store.value.getHalfCheckedKeys();\n    };\n    const setCurrentNode = (node, shouldAutoExpandParent = true) => {\n      if (!props.nodeKey) throw new Error(\"[Tree] nodeKey is required in setCurrentNode\");\n      handleCurrentChange(store, ctx.emit, () => {\n        broadcastExpanded(node);\n        store.value.setUserCurrentNode(node, shouldAutoExpandParent);\n      });\n    };\n    const setCurrentKey = (key, shouldAutoExpandParent = true) => {\n      if (!props.nodeKey) throw new Error(\"[Tree] nodeKey is required in setCurrentKey\");\n      handleCurrentChange(store, ctx.emit, () => {\n        broadcastExpanded();\n        store.value.setCurrentNodeKey(key, shouldAutoExpandParent);\n      });\n    };\n    const getNode = data => {\n      return store.value.getNode(data);\n    };\n    const remove = data => {\n      store.value.remove(data);\n    };\n    const append = (data, parentNode) => {\n      store.value.append(data, parentNode);\n    };\n    const insertBefore = (data, refNode) => {\n      store.value.insertBefore(data, refNode);\n    };\n    const insertAfter = (data, refNode) => {\n      store.value.insertAfter(data, refNode);\n    };\n    const handleNodeExpand = (nodeData, node, instance) => {\n      broadcastExpanded(node);\n      ctx.emit(\"node-expand\", nodeData, node, instance);\n    };\n    const updateKeyChildren = (key, data) => {\n      if (!props.nodeKey) throw new Error(\"[Tree] nodeKey is required in updateKeyChild\");\n      store.value.updateChildren(key, data);\n    };\n    provide(\"RootTree\", {\n      ctx,\n      props,\n      store,\n      root,\n      currentNode,\n      instance: getCurrentInstance()\n    });\n    provide(formItemContextKey, void 0);\n    return {\n      ns,\n      store,\n      root,\n      currentNode,\n      dragState,\n      el$,\n      dropIndicator$,\n      isEmpty,\n      filter,\n      getNodeKey: getNodeKey$1,\n      getNodePath,\n      getCheckedNodes,\n      getCheckedKeys,\n      getCurrentNode,\n      getCurrentKey,\n      setCheckedNodes,\n      setCheckedKeys,\n      setChecked,\n      getHalfCheckedNodes,\n      getHalfCheckedKeys,\n      setCurrentNode,\n      setCurrentKey,\n      t,\n      getNode,\n      remove,\n      append,\n      insertBefore,\n      insertAfter,\n      handleNodeExpand,\n      updateKeyChildren\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_tree_node = resolveComponent(\"el-tree-node\");\n  return openBlock(), createElementBlock(\"div\", {\n    ref: \"el$\",\n    class: normalizeClass([_ctx.ns.b(), _ctx.ns.is(\"dragging\", !!_ctx.dragState.draggingNode), _ctx.ns.is(\"drop-not-allow\", !_ctx.dragState.allowDrop), _ctx.ns.is(\"drop-inner\", _ctx.dragState.dropType === \"inner\"), {\n      [_ctx.ns.m(\"highlight-current\")]: _ctx.highlightCurrent\n    }]),\n    role: \"tree\"\n  }, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.root.childNodes, child => {\n    return openBlock(), createBlock(_component_el_tree_node, {\n      key: _ctx.getNodeKey(child),\n      node: child,\n      props: _ctx.props,\n      accordion: _ctx.accordion,\n      \"render-after-expand\": _ctx.renderAfterExpand,\n      \"show-checkbox\": _ctx.showCheckbox,\n      \"render-content\": _ctx.renderContent,\n      onNodeExpand: _ctx.handleNodeExpand\n    }, null, 8, [\"node\", \"props\", \"accordion\", \"render-after-expand\", \"show-checkbox\", \"render-content\", \"onNodeExpand\"]);\n  }), 128)), _ctx.isEmpty ? (openBlock(), createElementBlock(\"div\", {\n    key: 0,\n    class: normalizeClass(_ctx.ns.e(\"empty-block\"))\n  }, [renderSlot(_ctx.$slots, \"empty\", {}, () => {\n    var _a;\n    return [createElementVNode(\"span\", {\n      class: normalizeClass(_ctx.ns.e(\"empty-text\"))\n    }, toDisplayString((_a = _ctx.emptyText) != null ? _a : _ctx.t(\"el.tree.emptyText\")), 3)];\n  })], 2)) : createCommentVNode(\"v-if\", true), withDirectives(createElementVNode(\"div\", {\n    ref: \"dropIndicator$\",\n    class: normalizeClass(_ctx.ns.e(\"drop-indicator\"))\n  }, null, 2), [[vShow, _ctx.dragState.showDropIndicator]])], 2);\n}\nvar Tree = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"tree.vue\"]]);\nexport { Tree as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}