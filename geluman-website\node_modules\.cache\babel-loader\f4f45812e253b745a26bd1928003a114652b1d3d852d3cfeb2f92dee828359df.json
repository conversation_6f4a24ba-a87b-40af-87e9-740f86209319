{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, Fragment, renderList, toDisplayString, createCommentVNode, renderSlot } from 'vue';\nimport { dateTableProps, dateTableEmits } from './date-table.mjs';\nimport { useDateTable } from './use-date-table.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"DateTable\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: dateTableProps,\n  emits: dateTableEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const {\n      isInRange,\n      now,\n      rows,\n      weekDays,\n      getFormattedDate,\n      handlePickDay,\n      getSlotData\n    } = useDateTable(props, emit);\n    const nsTable = useNamespace(\"calendar-table\");\n    const nsDay = useNamespace(\"calendar-day\");\n    const getCellClass = ({\n      text,\n      type\n    }) => {\n      const classes = [type];\n      if (type === \"current\") {\n        const date = getFormattedDate(text, type);\n        if (date.isSame(props.selectedDay, \"day\")) {\n          classes.push(nsDay.is(\"selected\"));\n        }\n        if (date.isSame(now, \"day\")) {\n          classes.push(nsDay.is(\"today\"));\n        }\n      }\n      return classes;\n    };\n    expose({\n      getFormattedDate\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"table\", {\n        class: normalizeClass([unref(nsTable).b(), unref(nsTable).is(\"range\", unref(isInRange))]),\n        cellspacing: \"0\",\n        cellpadding: \"0\"\n      }, [!_ctx.hideHeader ? (openBlock(), createElementBlock(\"thead\", {\n        key: 0\n      }, [createElementVNode(\"tr\", null, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(weekDays), day => {\n        return openBlock(), createElementBlock(\"th\", {\n          key: day,\n          scope: \"col\"\n        }, toDisplayString(day), 1);\n      }), 128))])])) : createCommentVNode(\"v-if\", true), createElementVNode(\"tbody\", null, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(rows), (row, index) => {\n        return openBlock(), createElementBlock(\"tr\", {\n          key: index,\n          class: normalizeClass({\n            [unref(nsTable).e(\"row\")]: true,\n            [unref(nsTable).em(\"row\", \"hide-border\")]: index === 0 && _ctx.hideHeader\n          })\n        }, [(openBlock(true), createElementBlock(Fragment, null, renderList(row, (cell, key) => {\n          return openBlock(), createElementBlock(\"td\", {\n            key,\n            class: normalizeClass(getCellClass(cell)),\n            onClick: $event => unref(handlePickDay)(cell)\n          }, [createElementVNode(\"div\", {\n            class: normalizeClass(unref(nsDay).b())\n          }, [renderSlot(_ctx.$slots, \"date-cell\", {\n            data: unref(getSlotData)(cell)\n          }, () => [createElementVNode(\"span\", null, toDisplayString(cell.text), 1)])], 2)], 10, [\"onClick\"]);\n        }), 128))], 2);\n      }), 128))])], 2);\n    };\n  }\n});\nvar DateTable = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"date-table.vue\"]]);\nexport { DateTable as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}