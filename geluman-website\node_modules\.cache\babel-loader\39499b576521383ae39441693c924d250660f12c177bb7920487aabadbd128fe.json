{"ast": null, "code": "import { CircleCheckFilled, WarningFilled, CircleCloseFilled, InfoFilled } from '@element-plus/icons-vue';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst IconMap = {\n  success: \"icon-success\",\n  warning: \"icon-warning\",\n  error: \"icon-error\",\n  info: \"icon-info\"\n};\nconst IconComponentMap = {\n  [IconMap.success]: CircleCheckFilled,\n  [IconMap.warning]: WarningFilled,\n  [IconMap.error]: CircleCloseFilled,\n  [IconMap.info]: InfoFilled\n};\nconst resultProps = buildProps({\n  title: {\n    type: String,\n    default: \"\"\n  },\n  subTitle: {\n    type: String,\n    default: \"\"\n  },\n  icon: {\n    type: String,\n    values: [\"success\", \"warning\", \"info\", \"error\"],\n    default: \"info\"\n  }\n});\nexport { IconComponentMap, IconMap, resultProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}