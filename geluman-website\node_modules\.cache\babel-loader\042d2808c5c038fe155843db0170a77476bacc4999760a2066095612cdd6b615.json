{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, inject, withDirectives, h } from 'vue';\nimport { isNil } from 'lodash-unified';\nimport { descriptionsKey } from './token.mjs';\nimport { getNormalizedProps } from '../../../utils/vue/vnode.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nvar ElDescriptionsCell = defineComponent({\n  name: \"ElDescriptionsCell\",\n  props: {\n    cell: {\n      type: Object\n    },\n    tag: {\n      type: String,\n      default: \"td\"\n    },\n    type: {\n      type: String\n    }\n  },\n  setup() {\n    const descriptions = inject(descriptionsKey, {});\n    return {\n      descriptions\n    };\n  },\n  render() {\n    var _a;\n    const item = getNormalizedProps(this.cell);\n    const directives = (((_a = this.cell) == null ? void 0 : _a.dirs) || []).map(dire => {\n      const {\n        dir,\n        arg,\n        modifiers,\n        value\n      } = dire;\n      return [dir, value, arg, modifiers];\n    });\n    const {\n      border,\n      direction\n    } = this.descriptions;\n    const isVertical = direction === \"vertical\";\n    const renderLabel = () => {\n      var _a2, _b, _c;\n      return ((_c = (_b = (_a2 = this.cell) == null ? void 0 : _a2.children) == null ? void 0 : _b.label) == null ? void 0 : _c.call(_b)) || item.label;\n    };\n    const renderContent = () => {\n      var _a2, _b, _c;\n      return (_c = (_b = (_a2 = this.cell) == null ? void 0 : _a2.children) == null ? void 0 : _b.default) == null ? void 0 : _c.call(_b);\n    };\n    const span = item.span;\n    const rowspan = item.rowspan;\n    const align = item.align ? `is-${item.align}` : \"\";\n    const labelAlign = item.labelAlign ? `is-${item.labelAlign}` : align;\n    const className = item.className;\n    const labelClassName = item.labelClassName;\n    const width = this.type === \"label\" ? item.labelWidth || this.descriptions.labelWidth || item.width : item.width;\n    const style = {\n      width: addUnit(width),\n      minWidth: addUnit(item.minWidth)\n    };\n    const ns = useNamespace(\"descriptions\");\n    switch (this.type) {\n      case \"label\":\n        return withDirectives(h(this.tag, {\n          style,\n          class: [ns.e(\"cell\"), ns.e(\"label\"), ns.is(\"bordered-label\", border), ns.is(\"vertical-label\", isVertical), labelAlign, labelClassName],\n          colSpan: isVertical ? span : 1,\n          rowspan: isVertical ? 1 : rowspan\n        }, renderLabel()), directives);\n      case \"content\":\n        return withDirectives(h(this.tag, {\n          style,\n          class: [ns.e(\"cell\"), ns.e(\"content\"), ns.is(\"bordered-content\", border), ns.is(\"vertical-content\", isVertical), align, className],\n          colSpan: isVertical ? span : span * 2 - 1,\n          rowspan: isVertical ? rowspan * 2 - 1 : rowspan\n        }, renderContent()), directives);\n      default:\n        {\n          const label = renderLabel();\n          const labelStyle = {};\n          const width2 = addUnit(item.labelWidth || this.descriptions.labelWidth);\n          if (width2) {\n            labelStyle.width = width2;\n            labelStyle.display = \"inline-block\";\n          }\n          return withDirectives(h(\"td\", {\n            style,\n            class: [ns.e(\"cell\"), align],\n            colSpan: span,\n            rowspan\n          }, [!isNil(label) ? h(\"span\", {\n            style: labelStyle,\n            class: [ns.e(\"label\"), labelClassName]\n          }, label) : void 0, h(\"span\", {\n            class: [ns.e(\"content\"), className]\n          }, renderContent())]), directives);\n        }\n    }\n  }\n});\nexport { ElDescriptionsCell as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}