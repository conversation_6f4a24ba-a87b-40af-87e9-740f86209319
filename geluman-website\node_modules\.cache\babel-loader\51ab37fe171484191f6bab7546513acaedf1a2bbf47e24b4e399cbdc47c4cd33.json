{"ast": null, "code": "var v = !1,\n  o,\n  f,\n  s,\n  u,\n  d,\n  N,\n  l,\n  p,\n  m,\n  w,\n  D,\n  x,\n  E,\n  M,\n  F;\nfunction a() {\n  if (!v) {\n    v = !0;\n    var e = navigator.userAgent,\n      n = /(?:MSIE.(\\d+\\.\\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\\d+\\.\\d+))|(?:Opera(?:.+Version.|.)(\\d+\\.\\d+))|(?:AppleWebKit.(\\d+(?:\\.\\d+)?))|(?:Trident\\/\\d+\\.\\d+.*rv:(\\d+\\.\\d+))/.exec(e),\n      i = /(Mac OS X)|(Windows)|(Linux)/.exec(e);\n    if (x = /\\b(iPhone|iP[ao]d)/.exec(e), E = /\\b(iP[ao]d)/.exec(e), w = /Android/i.exec(e), M = /FBAN\\/\\w+;/i.exec(e), F = /Mobile/i.exec(e), D = !!/Win64/.exec(e), n) {\n      o = n[1] ? parseFloat(n[1]) : n[5] ? parseFloat(n[5]) : NaN, o && document && document.documentMode && (o = document.documentMode);\n      var r = /(?:Trident\\/(\\d+.\\d+))/.exec(e);\n      N = r ? parseFloat(r[1]) + 4 : o, f = n[2] ? parseFloat(n[2]) : NaN, s = n[3] ? parseFloat(n[3]) : NaN, u = n[4] ? parseFloat(n[4]) : NaN, u ? (n = /(?:Chrome\\/(\\d+\\.\\d+))/.exec(e), d = n && n[1] ? parseFloat(n[1]) : NaN) : d = NaN;\n    } else o = f = s = d = u = NaN;\n    if (i) {\n      if (i[1]) {\n        var t = /(?:Mac OS X (\\d+(?:[._]\\d+)?))/.exec(e);\n        l = t ? parseFloat(t[1].replace(\"_\", \".\")) : !0;\n      } else l = !1;\n      p = !!i[2], m = !!i[3];\n    } else l = p = m = !1;\n  }\n}\nvar _ = {\n    ie: function () {\n      return a() || o;\n    },\n    ieCompatibilityMode: function () {\n      return a() || N > o;\n    },\n    ie64: function () {\n      return _.ie() && D;\n    },\n    firefox: function () {\n      return a() || f;\n    },\n    opera: function () {\n      return a() || s;\n    },\n    webkit: function () {\n      return a() || u;\n    },\n    safari: function () {\n      return _.webkit();\n    },\n    chrome: function () {\n      return a() || d;\n    },\n    windows: function () {\n      return a() || p;\n    },\n    osx: function () {\n      return a() || l;\n    },\n    linux: function () {\n      return a() || m;\n    },\n    iphone: function () {\n      return a() || x;\n    },\n    mobile: function () {\n      return a() || x || E || w || F;\n    },\n    nativeApp: function () {\n      return a() || M;\n    },\n    android: function () {\n      return a() || w;\n    },\n    ipad: function () {\n      return a() || E;\n    }\n  },\n  A = _;\nvar c = !!(typeof window < \"u\" && window.document && window.document.createElement),\n  U = {\n    canUseDOM: c,\n    canUseWorkers: typeof Worker < \"u\",\n    canUseEventListeners: c && !!(window.addEventListener || window.attachEvent),\n    canUseViewport: c && !!window.screen,\n    isInWorker: !c\n  },\n  h = U;\nvar X;\nh.canUseDOM && (X = document.implementation && document.implementation.hasFeature && document.implementation.hasFeature(\"\", \"\") !== !0);\nfunction S(e, n) {\n  if (!h.canUseDOM || n && !(\"addEventListener\" in document)) return !1;\n  var i = \"on\" + e,\n    r = i in document;\n  if (!r) {\n    var t = document.createElement(\"div\");\n    t.setAttribute(i, \"return;\"), r = typeof t[i] == \"function\";\n  }\n  return !r && X && e === \"wheel\" && (r = document.implementation.hasFeature(\"Events.wheel\", \"3.0\")), r;\n}\nvar b = S;\nvar O = 10,\n  I = 40,\n  P = 800;\nfunction T(e) {\n  var n = 0,\n    i = 0,\n    r = 0,\n    t = 0;\n  return \"detail\" in e && (i = e.detail), \"wheelDelta\" in e && (i = -e.wheelDelta / 120), \"wheelDeltaY\" in e && (i = -e.wheelDeltaY / 120), \"wheelDeltaX\" in e && (n = -e.wheelDeltaX / 120), \"axis\" in e && e.axis === e.HORIZONTAL_AXIS && (n = i, i = 0), r = n * O, t = i * O, \"deltaY\" in e && (t = e.deltaY), \"deltaX\" in e && (r = e.deltaX), (r || t) && e.deltaMode && (e.deltaMode == 1 ? (r *= I, t *= I) : (r *= P, t *= P)), r && !n && (n = r < 1 ? -1 : 1), t && !i && (i = t < 1 ? -1 : 1), {\n    spinX: n,\n    spinY: i,\n    pixelX: r,\n    pixelY: t\n  };\n}\nT.getEventType = function () {\n  return A.firefox() ? \"DOMMouseScroll\" : b(\"wheel\") ? \"wheel\" : \"mousewheel\";\n};\nvar Y = T;\nexport { Y as default };\n/**\n * Checks if an event is supported in the current execution environment.\n *\n * NOTE: This will not work correctly for non-generic events such as `change`,\n * `reset`, `load`, `error`, and `select`.\n *\n * Borrows from Modernizr.\n *\n * @param {string} eventNameSuffix Event name, e.g. \"click\".\n * @param {?boolean} capture Check if the capture phase is supported.\n * @return {boolean} True if the event is supported.\n * @internal\n * @license Modernizr 3.0.0pre (Custom Build) | MIT\n */", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}