{"ast": null, "code": "import { defineComponent, ref, computed, onMounted, openBlock, createBlock, Transition, unref, withCtx, withDirectives, createElementVNode, normalizeClass, normalizeStyle, resolveDynamicComponent, createCommentVNode, toDisplayString, renderSlot, createElementBlock, Fragment, vShow, withModifiers } from 'vue';\nimport { useEventListener, useTimeoutFn } from '@vueuse/core';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { notificationProps, notificationEmits } from './notification.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useGlobalComponentSettings } from '../../config-provider/src/hooks/use-global-config.mjs';\nimport { TypeComponentsMap } from '../../../utils/vue/icon.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nconst __default__ = defineComponent({\n  name: \"ElNotification\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: notificationProps,\n  emits: notificationEmits,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const {\n      ns,\n      zIndex\n    } = useGlobalComponentSettings(\"notification\");\n    const {\n      nextZIndex,\n      currentZIndex\n    } = zIndex;\n    const visible = ref(false);\n    let timer = void 0;\n    const typeClass = computed(() => {\n      const type = props.type;\n      return type && TypeComponentsMap[props.type] ? ns.m(type) : \"\";\n    });\n    const iconComponent = computed(() => {\n      if (!props.type) return props.icon;\n      return TypeComponentsMap[props.type] || props.icon;\n    });\n    const horizontalClass = computed(() => props.position.endsWith(\"right\") ? \"right\" : \"left\");\n    const verticalProperty = computed(() => props.position.startsWith(\"top\") ? \"top\" : \"bottom\");\n    const positionStyle = computed(() => {\n      var _a;\n      return {\n        [verticalProperty.value]: `${props.offset}px`,\n        zIndex: (_a = props.zIndex) != null ? _a : currentZIndex.value\n      };\n    });\n    function startTimer() {\n      if (props.duration > 0) {\n        ({\n          stop: timer\n        } = useTimeoutFn(() => {\n          if (visible.value) close();\n        }, props.duration));\n      }\n    }\n    function clearTimer() {\n      timer == null ? void 0 : timer();\n    }\n    function close() {\n      visible.value = false;\n    }\n    function onKeydown({\n      code\n    }) {\n      if (code === EVENT_CODE.delete || code === EVENT_CODE.backspace) {\n        clearTimer();\n      } else if (code === EVENT_CODE.esc) {\n        if (visible.value) {\n          close();\n        }\n      } else {\n        startTimer();\n      }\n    }\n    onMounted(() => {\n      startTimer();\n      nextZIndex();\n      visible.value = true;\n    });\n    useEventListener(document, \"keydown\", onKeydown);\n    expose({\n      visible,\n      close\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(Transition, {\n        name: unref(ns).b(\"fade\"),\n        onBeforeLeave: _ctx.onClose,\n        onAfterLeave: $event => _ctx.$emit(\"destroy\"),\n        persisted: \"\"\n      }, {\n        default: withCtx(() => [withDirectives(createElementVNode(\"div\", {\n          id: _ctx.id,\n          class: normalizeClass([unref(ns).b(), _ctx.customClass, unref(horizontalClass)]),\n          style: normalizeStyle(unref(positionStyle)),\n          role: \"alert\",\n          onMouseenter: clearTimer,\n          onMouseleave: startTimer,\n          onClick: _ctx.onClick\n        }, [unref(iconComponent) ? (openBlock(), createBlock(unref(ElIcon), {\n          key: 0,\n          class: normalizeClass([unref(ns).e(\"icon\"), unref(typeClass)])\n        }, {\n          default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(iconComponent))))]),\n          _: 1\n        }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).e(\"group\"))\n        }, [createElementVNode(\"h2\", {\n          class: normalizeClass(unref(ns).e(\"title\")),\n          textContent: toDisplayString(_ctx.title)\n        }, null, 10, [\"textContent\"]), withDirectives(createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).e(\"content\")),\n          style: normalizeStyle(!!_ctx.title ? void 0 : {\n            margin: 0\n          })\n        }, [renderSlot(_ctx.$slots, \"default\", {}, () => [!_ctx.dangerouslyUseHTMLString ? (openBlock(), createElementBlock(\"p\", {\n          key: 0\n        }, toDisplayString(_ctx.message), 1)) : (openBlock(), createElementBlock(Fragment, {\n          key: 1\n        }, [createCommentVNode(\" Caution here, message could've been compromised, never use user's input as message \"), createElementVNode(\"p\", {\n          innerHTML: _ctx.message\n        }, null, 8, [\"innerHTML\"])], 2112))])], 6), [[vShow, _ctx.message]]), _ctx.showClose ? (openBlock(), createBlock(unref(ElIcon), {\n          key: 0,\n          class: normalizeClass(unref(ns).e(\"closeBtn\")),\n          onClick: withModifiers(close, [\"stop\"])\n        }, {\n          default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.closeIcon)))]),\n          _: 1\n        }, 8, [\"class\", \"onClick\"])) : createCommentVNode(\"v-if\", true)], 2)], 46, [\"id\", \"onClick\"]), [[vShow, visible.value]])]),\n        _: 3\n      }, 8, [\"name\", \"onBeforeLeave\", \"onAfterLeave\"]);\n    };\n  }\n});\nvar NotificationConstructor = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"notification.vue\"]]);\nexport { NotificationConstructor as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}