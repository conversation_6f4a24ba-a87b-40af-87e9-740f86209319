{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { ref, shallowRef, watch, computed, nextTick } from 'vue';\nimport { TreeOptionsEnum, NODE_CLICK, NODE_DROP, CURRENT_CHANGE, NODE_EXPAND, NODE_COLLAPSE } from '../virtual-tree.mjs';\nimport { useCheck } from './useCheck.mjs';\nimport { useFilter } from './useFilter.mjs';\nimport { isObject } from '@vue/shared';\nfunction useTree(props, emit) {\n  const expandedKeySet = ref(new Set(props.defaultExpandedKeys));\n  const currentKey = ref();\n  const tree = shallowRef();\n  const listRef = ref();\n  watch(() => props.currentNodeKey, key => {\n    currentKey.value = key;\n  }, {\n    immediate: true\n  });\n  watch(() => props.data, data => {\n    setData(data);\n  }, {\n    immediate: true\n  });\n  const {\n    isIndeterminate,\n    isChecked,\n    toggleCheckbox,\n    getCheckedKeys,\n    getCheckedNodes,\n    getHalfCheckedKeys,\n    getHalfCheckedNodes,\n    setChecked,\n    setCheckedKeys\n  } = useCheck(props, tree);\n  const {\n    doFilter,\n    hiddenNodeKeySet,\n    isForceHiddenExpandIcon\n  } = useFilter(props, tree);\n  const valueKey = computed(() => {\n    var _a;\n    return ((_a = props.props) == null ? void 0 : _a.value) || TreeOptionsEnum.KEY;\n  });\n  const childrenKey = computed(() => {\n    var _a;\n    return ((_a = props.props) == null ? void 0 : _a.children) || TreeOptionsEnum.CHILDREN;\n  });\n  const disabledKey = computed(() => {\n    var _a;\n    return ((_a = props.props) == null ? void 0 : _a.disabled) || TreeOptionsEnum.DISABLED;\n  });\n  const labelKey = computed(() => {\n    var _a;\n    return ((_a = props.props) == null ? void 0 : _a.label) || TreeOptionsEnum.LABEL;\n  });\n  const flattenTree = computed(() => {\n    var _a;\n    const expandedKeys = expandedKeySet.value;\n    const hiddenKeys = hiddenNodeKeySet.value;\n    const flattenNodes = [];\n    const nodes = ((_a = tree.value) == null ? void 0 : _a.treeNodes) || [];\n    const stack = [];\n    for (let i = nodes.length - 1; i >= 0; --i) {\n      stack.push(nodes[i]);\n    }\n    while (stack.length) {\n      const node = stack.pop();\n      if (hiddenKeys.has(node.key)) continue;\n      flattenNodes.push(node);\n      if (node.children && expandedKeys.has(node.key)) {\n        for (let i = node.children.length - 1; i >= 0; --i) {\n          stack.push(node.children[i]);\n        }\n      }\n    }\n    return flattenNodes;\n  });\n  const isNotEmpty = computed(() => {\n    return flattenTree.value.length > 0;\n  });\n  function createTree(data) {\n    const treeNodeMap = /* @__PURE__ */new Map();\n    const levelTreeNodeMap = /* @__PURE__ */new Map();\n    let maxLevel = 1;\n    function traverse(nodes, level = 1, parent = void 0) {\n      var _a;\n      const siblings = [];\n      for (const rawNode of nodes) {\n        const value = getKey(rawNode);\n        const node = {\n          level,\n          key: value,\n          data: rawNode\n        };\n        node.label = getLabel(rawNode);\n        node.parent = parent;\n        const children = getChildren(rawNode);\n        node.disabled = getDisabled(rawNode);\n        node.isLeaf = !children || children.length === 0;\n        if (children && children.length) {\n          node.children = traverse(children, level + 1, node);\n        }\n        siblings.push(node);\n        treeNodeMap.set(value, node);\n        if (!levelTreeNodeMap.has(level)) {\n          levelTreeNodeMap.set(level, []);\n        }\n        (_a = levelTreeNodeMap.get(level)) == null ? void 0 : _a.push(node);\n      }\n      if (level > maxLevel) {\n        maxLevel = level;\n      }\n      return siblings;\n    }\n    const treeNodes = traverse(data);\n    return {\n      treeNodeMap,\n      levelTreeNodeMap,\n      maxLevel,\n      treeNodes\n    };\n  }\n  function filter(query) {\n    const keys = doFilter(query);\n    if (keys) {\n      expandedKeySet.value = keys;\n    }\n  }\n  function getChildren(node) {\n    return node[childrenKey.value];\n  }\n  function getKey(node) {\n    if (!node) {\n      return \"\";\n    }\n    return node[valueKey.value];\n  }\n  function getDisabled(node) {\n    return node[disabledKey.value];\n  }\n  function getLabel(node) {\n    return node[labelKey.value];\n  }\n  function toggleExpand(node) {\n    const expandedKeys = expandedKeySet.value;\n    if (expandedKeys.has(node.key)) {\n      collapseNode(node);\n    } else {\n      expandNode(node);\n    }\n  }\n  function setExpandedKeys(keys) {\n    const expandedKeys = /* @__PURE__ */new Set();\n    const nodeMap = tree.value.treeNodeMap;\n    keys.forEach(k => {\n      let node = nodeMap.get(k);\n      while (node && !expandedKeys.has(node.key)) {\n        expandedKeys.add(node.key);\n        node = node.parent;\n      }\n    });\n    expandedKeySet.value = expandedKeys;\n  }\n  function handleNodeClick(node, e) {\n    emit(NODE_CLICK, node.data, node, e);\n    handleCurrentChange(node);\n    if (props.expandOnClickNode) {\n      toggleExpand(node);\n    }\n    if (props.showCheckbox && (props.checkOnClickNode || node.isLeaf && props.checkOnClickLeaf) && !node.disabled) {\n      toggleCheckbox(node, !isChecked(node), true);\n    }\n  }\n  function handleNodeDrop(node, e) {\n    emit(NODE_DROP, node.data, node, e);\n  }\n  function handleCurrentChange(node) {\n    if (!isCurrent(node)) {\n      currentKey.value = node.key;\n      emit(CURRENT_CHANGE, node.data, node);\n    }\n  }\n  function handleNodeCheck(node, checked) {\n    toggleCheckbox(node, checked);\n  }\n  function expandNode(node) {\n    const keySet = expandedKeySet.value;\n    if (tree.value && props.accordion) {\n      const {\n        treeNodeMap\n      } = tree.value;\n      keySet.forEach(key => {\n        const treeNode = treeNodeMap.get(key);\n        if (node && node.level === (treeNode == null ? void 0 : treeNode.level)) {\n          keySet.delete(key);\n        }\n      });\n    }\n    keySet.add(node.key);\n    emit(NODE_EXPAND, node.data, node);\n  }\n  function collapseNode(node) {\n    expandedKeySet.value.delete(node.key);\n    emit(NODE_COLLAPSE, node.data, node);\n  }\n  function isExpanded(node) {\n    return expandedKeySet.value.has(node.key);\n  }\n  function isDisabled(node) {\n    return !!node.disabled;\n  }\n  function isCurrent(node) {\n    const current = currentKey.value;\n    return current !== void 0 && current === node.key;\n  }\n  function getCurrentNode() {\n    var _a, _b;\n    if (!currentKey.value) return void 0;\n    return (_b = (_a = tree.value) == null ? void 0 : _a.treeNodeMap.get(currentKey.value)) == null ? void 0 : _b.data;\n  }\n  function getCurrentKey() {\n    return currentKey.value;\n  }\n  function setCurrentKey(key) {\n    currentKey.value = key;\n  }\n  function setData(data) {\n    nextTick(() => tree.value = createTree(data));\n  }\n  function getNode(data) {\n    var _a;\n    const key = isObject(data) ? getKey(data) : data;\n    return (_a = tree.value) == null ? void 0 : _a.treeNodeMap.get(key);\n  }\n  function scrollToNode(key, strategy = \"auto\") {\n    const node = getNode(key);\n    if (node && listRef.value) {\n      listRef.value.scrollToItem(flattenTree.value.indexOf(node), strategy);\n    }\n  }\n  function scrollTo(offset) {\n    var _a;\n    (_a = listRef.value) == null ? void 0 : _a.scrollTo(offset);\n  }\n  return {\n    tree,\n    flattenTree,\n    isNotEmpty,\n    listRef,\n    getKey,\n    getChildren,\n    toggleExpand,\n    toggleCheckbox,\n    isExpanded,\n    isChecked,\n    isIndeterminate,\n    isDisabled,\n    isCurrent,\n    isForceHiddenExpandIcon,\n    handleNodeClick,\n    handleNodeDrop,\n    handleNodeCheck,\n    getCurrentNode,\n    getCurrentKey,\n    setCurrentKey,\n    getCheckedKeys,\n    getCheckedNodes,\n    getHalfCheckedKeys,\n    getHalfCheckedNodes,\n    setChecked,\n    setCheckedKeys,\n    filter,\n    setData,\n    getNode,\n    expandNode,\n    collapseNode,\n    setExpandedKeys,\n    scrollToNode,\n    scrollTo\n  };\n}\nexport { useTree };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}