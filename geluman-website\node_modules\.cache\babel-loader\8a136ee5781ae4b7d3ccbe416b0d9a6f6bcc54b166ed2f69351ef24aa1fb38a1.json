{"ast": null, "code": "import { defineComponent, openBlock, createBlock, Transition, mergeProps, unref, withCtx, renderSlot } from 'vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { addClass, removeClass, hasClass } from '../../../utils/dom/style.mjs';\nconst __default__ = defineComponent({\n  name: \"ElMenuCollapseTransition\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  setup(__props) {\n    const ns = useNamespace(\"menu\");\n    const listeners = {\n      onBeforeEnter: el => el.style.opacity = \"0.2\",\n      onEnter(el, done) {\n        addClass(el, `${ns.namespace.value}-opacity-transition`);\n        el.style.opacity = \"1\";\n        done();\n      },\n      onAfterEnter(el) {\n        removeClass(el, `${ns.namespace.value}-opacity-transition`);\n        el.style.opacity = \"\";\n      },\n      onBeforeLeave(el) {\n        if (!el.dataset) el.dataset = {};\n        if (hasClass(el, ns.m(\"collapse\"))) {\n          removeClass(el, ns.m(\"collapse\"));\n          el.dataset.oldOverflow = el.style.overflow;\n          el.dataset.scrollWidth = el.clientWidth.toString();\n          addClass(el, ns.m(\"collapse\"));\n        } else {\n          addClass(el, ns.m(\"collapse\"));\n          el.dataset.oldOverflow = el.style.overflow;\n          el.dataset.scrollWidth = el.clientWidth.toString();\n          removeClass(el, ns.m(\"collapse\"));\n        }\n        el.style.width = `${el.scrollWidth}px`;\n        el.style.overflow = \"hidden\";\n      },\n      onLeave(el) {\n        addClass(el, \"horizontal-collapse-transition\");\n        el.style.width = `${el.dataset.scrollWidth}px`;\n      }\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(Transition, mergeProps({\n        mode: \"out-in\"\n      }, unref(listeners)), {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 16);\n    };\n  }\n});\nvar ElMenuCollapseTransition = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"menu-collapse-transition.vue\"]]);\nexport { ElMenuCollapseTransition as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}