{"ast": null, "code": "import baseSortedIndex from './_baseSortedIndex.js';\n\n/**\n * Uses a binary search to determine the lowest index at which `value`\n * should be inserted into `array` in order to maintain its sort order.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n * @example\n *\n * _.sortedIndex([30, 50], 40);\n * // => 1\n */\nfunction sortedIndex(array, value) {\n  return baseSortedIndex(array, value);\n}\nexport default sortedIndex;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}