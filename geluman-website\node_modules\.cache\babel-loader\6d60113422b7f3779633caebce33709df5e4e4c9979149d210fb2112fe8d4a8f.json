{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, ref, inject, computed, useSlots, toRef, watch, openBlock, createElementBlock, normalizeClass, unref, createElementVNode, renderSlot, Fragment, renderList, toDisplayString, createCommentVNode, createVNode, withCtx } from 'vue';\nimport dayjs from 'dayjs';\nimport { DArrowLeft, DArrowRight } from '@element-plus/icons-vue';\nimport { ElIcon } from '../../../icon/index.mjs';\nimport { panelYearRangeProps, panelYearRangeEmits } from '../props/panel-year-range.mjs';\nimport { useShortcut } from '../composables/use-shortcut.mjs';\nimport { useYearRangeHeader } from '../composables/use-year-range-header.mjs';\nimport { isValidRange, correctlyParseUserInput } from '../utils.mjs';\nimport { ROOT_PICKER_INJECTION_KEY } from '../constants.mjs';\nimport YearTable from './basic-year-table.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isArray } from '@vue/shared';\nconst unit = \"year\";\nconst __default__ = defineComponent({\n  name: \"DatePickerYearRange\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: panelYearRangeProps,\n  emits: panelYearRangeEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const {\n      lang\n    } = useLocale();\n    const leftDate = ref(dayjs().locale(lang.value));\n    const rightDate = ref(leftDate.value.add(10, \"year\"));\n    const {\n      pickerNs: ppNs\n    } = inject(ROOT_PICKER_INJECTION_KEY);\n    const drpNs = useNamespace(\"date-range-picker\");\n    const isDefaultFormat = inject(\"isDefaultFormat\");\n    const hasShortcuts = computed(() => !!shortcuts.length);\n    const panelKls = computed(() => [ppNs.b(), drpNs.b(), {\n      \"has-sidebar\": Boolean(useSlots().sidebar) || hasShortcuts.value\n    }]);\n    const leftPanelKls = computed(() => {\n      return {\n        content: [ppNs.e(\"content\"), drpNs.e(\"content\"), \"is-left\"],\n        arrowLeftBtn: [ppNs.e(\"icon-btn\"), \"d-arrow-left\"],\n        arrowRightBtn: [ppNs.e(\"icon-btn\"), {\n          [ppNs.is(\"disabled\")]: !enableYearArrow.value\n        }, \"d-arrow-right\"]\n      };\n    });\n    const rightPanelKls = computed(() => {\n      return {\n        content: [ppNs.e(\"content\"), drpNs.e(\"content\"), \"is-right\"],\n        arrowLeftBtn: [ppNs.e(\"icon-btn\"), {\n          \"is-disabled\": !enableYearArrow.value\n        }, \"d-arrow-left\"],\n        arrowRightBtn: [ppNs.e(\"icon-btn\"), \"d-arrow-right\"]\n      };\n    });\n    const handleShortcutClick = useShortcut(lang);\n    const {\n      leftPrevYear,\n      rightNextYear,\n      leftNextYear,\n      rightPrevYear,\n      leftLabel,\n      rightLabel,\n      leftYear,\n      rightYear\n    } = useYearRangeHeader({\n      unlinkPanels: toRef(props, \"unlinkPanels\"),\n      leftDate,\n      rightDate\n    });\n    const enableYearArrow = computed(() => {\n      return props.unlinkPanels && rightYear.value > leftYear.value + 1;\n    });\n    const minDate = ref();\n    const maxDate = ref();\n    const rangeState = ref({\n      endDate: null,\n      selecting: false\n    });\n    const handleChangeRange = val => {\n      rangeState.value = val;\n    };\n    const handleRangePick = (val, close = true) => {\n      const minDate_ = val.minDate;\n      const maxDate_ = val.maxDate;\n      if (maxDate.value === maxDate_ && minDate.value === minDate_) {\n        return;\n      }\n      emit(\"calendar-change\", [minDate_.toDate(), maxDate_ && maxDate_.toDate()]);\n      maxDate.value = maxDate_;\n      minDate.value = minDate_;\n      if (!close) return;\n      handleConfirm();\n    };\n    const handleConfirm = (visible = false) => {\n      if (isValidRange([minDate.value, maxDate.value])) {\n        emit(\"pick\", [minDate.value, maxDate.value], visible);\n      }\n    };\n    const onSelect = selecting => {\n      rangeState.value.selecting = selecting;\n      if (!selecting) {\n        rangeState.value.endDate = null;\n      }\n    };\n    const pickerBase = inject(\"EP_PICKER_BASE\");\n    const {\n      shortcuts,\n      disabledDate\n    } = pickerBase.props;\n    const format = toRef(pickerBase.props, \"format\");\n    const defaultValue = toRef(pickerBase.props, \"defaultValue\");\n    const getDefaultValue = () => {\n      let start;\n      if (isArray(defaultValue.value)) {\n        const left = dayjs(defaultValue.value[0]);\n        let right = dayjs(defaultValue.value[1]);\n        if (!props.unlinkPanels) {\n          right = left.add(10, unit);\n        }\n        return [left, right];\n      } else if (defaultValue.value) {\n        start = dayjs(defaultValue.value);\n      } else {\n        start = dayjs();\n      }\n      start = start.locale(lang.value);\n      return [start, start.add(10, unit)];\n    };\n    watch(() => defaultValue.value, val => {\n      if (val) {\n        const defaultArr = getDefaultValue();\n        leftDate.value = defaultArr[0];\n        rightDate.value = defaultArr[1];\n      }\n    }, {\n      immediate: true\n    });\n    watch(() => props.parsedValue, newVal => {\n      if (newVal && newVal.length === 2) {\n        minDate.value = newVal[0];\n        maxDate.value = newVal[1];\n        leftDate.value = minDate.value;\n        if (props.unlinkPanels && maxDate.value) {\n          const minDateYear = minDate.value.year();\n          const maxDateYear = maxDate.value.year();\n          rightDate.value = minDateYear === maxDateYear ? maxDate.value.add(10, \"year\") : maxDate.value;\n        } else {\n          rightDate.value = leftDate.value.add(10, \"year\");\n        }\n      } else {\n        const defaultArr = getDefaultValue();\n        minDate.value = void 0;\n        maxDate.value = void 0;\n        leftDate.value = defaultArr[0];\n        rightDate.value = defaultArr[1];\n      }\n    }, {\n      immediate: true\n    });\n    const parseUserInput = value => {\n      return correctlyParseUserInput(value, format.value, lang.value, isDefaultFormat);\n    };\n    const formatToString = value => {\n      return isArray(value) ? value.map(day => day.format(format.value)) : value.format(format.value);\n    };\n    const isValidValue = date => {\n      return isValidRange(date) && (disabledDate ? !disabledDate(date[0].toDate()) && !disabledDate(date[1].toDate()) : true);\n    };\n    const handleClear = () => {\n      const defaultArr = getDefaultValue();\n      leftDate.value = defaultArr[0];\n      rightDate.value = defaultArr[1];\n      maxDate.value = void 0;\n      minDate.value = void 0;\n      emit(\"pick\", null);\n    };\n    emit(\"set-picker-option\", [\"isValidValue\", isValidValue]);\n    emit(\"set-picker-option\", [\"parseUserInput\", parseUserInput]);\n    emit(\"set-picker-option\", [\"formatToString\", formatToString]);\n    emit(\"set-picker-option\", [\"handleClear\", handleClear]);\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(panelKls))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ppNs).e(\"body-wrapper\"))\n      }, [renderSlot(_ctx.$slots, \"sidebar\", {\n        class: normalizeClass(unref(ppNs).e(\"sidebar\"))\n      }), unref(hasShortcuts) ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ppNs).e(\"sidebar\"))\n      }, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(shortcuts), (shortcut, key) => {\n        return openBlock(), createElementBlock(\"button\", {\n          key,\n          type: \"button\",\n          class: normalizeClass(unref(ppNs).e(\"shortcut\")),\n          onClick: $event => unref(handleShortcutClick)(shortcut)\n        }, toDisplayString(shortcut.text), 11, [\"onClick\"]);\n      }), 128))], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ppNs).e(\"body\"))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(leftPanelKls).content)\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(drpNs).e(\"header\"))\n      }, [createElementVNode(\"button\", {\n        type: \"button\",\n        class: normalizeClass(unref(leftPanelKls).arrowLeftBtn),\n        onClick: unref(leftPrevYear)\n      }, [renderSlot(_ctx.$slots, \"prev-year\", {}, () => [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [createVNode(unref(DArrowLeft))]),\n        _: 1\n      })])], 10, [\"onClick\"]), _ctx.unlinkPanels ? (openBlock(), createElementBlock(\"button\", {\n        key: 0,\n        type: \"button\",\n        disabled: !unref(enableYearArrow),\n        class: normalizeClass(unref(leftPanelKls).arrowRightBtn),\n        onClick: unref(leftNextYear)\n      }, [renderSlot(_ctx.$slots, \"next-year\", {}, () => [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [createVNode(unref(DArrowRight))]),\n        _: 1\n      })])], 10, [\"disabled\", \"onClick\"])) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", null, toDisplayString(unref(leftLabel)), 1)], 2), createVNode(YearTable, {\n        \"selection-mode\": \"range\",\n        date: leftDate.value,\n        \"min-date\": minDate.value,\n        \"max-date\": maxDate.value,\n        \"range-state\": rangeState.value,\n        \"disabled-date\": unref(disabledDate),\n        onChangerange: handleChangeRange,\n        onPick: handleRangePick,\n        onSelect\n      }, null, 8, [\"date\", \"min-date\", \"max-date\", \"range-state\", \"disabled-date\"])], 2), createElementVNode(\"div\", {\n        class: normalizeClass(unref(rightPanelKls).content)\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(drpNs).e(\"header\"))\n      }, [_ctx.unlinkPanels ? (openBlock(), createElementBlock(\"button\", {\n        key: 0,\n        type: \"button\",\n        disabled: !unref(enableYearArrow),\n        class: normalizeClass(unref(rightPanelKls).arrowLeftBtn),\n        onClick: unref(rightPrevYear)\n      }, [renderSlot(_ctx.$slots, \"prev-year\", {}, () => [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [createVNode(unref(DArrowLeft))]),\n        _: 1\n      })])], 10, [\"disabled\", \"onClick\"])) : createCommentVNode(\"v-if\", true), createElementVNode(\"button\", {\n        type: \"button\",\n        class: normalizeClass(unref(rightPanelKls).arrowRightBtn),\n        onClick: unref(rightNextYear)\n      }, [renderSlot(_ctx.$slots, \"next-year\", {}, () => [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [createVNode(unref(DArrowRight))]),\n        _: 1\n      })])], 10, [\"onClick\"]), createElementVNode(\"div\", null, toDisplayString(unref(rightLabel)), 1)], 2), createVNode(YearTable, {\n        \"selection-mode\": \"range\",\n        date: rightDate.value,\n        \"min-date\": minDate.value,\n        \"max-date\": maxDate.value,\n        \"range-state\": rangeState.value,\n        \"disabled-date\": unref(disabledDate),\n        onChangerange: handleChangeRange,\n        onPick: handleRangePick,\n        onSelect\n      }, null, 8, [\"date\", \"min-date\", \"max-date\", \"range-state\", \"disabled-date\"])], 2)], 2)], 2)], 2);\n    };\n  }\n});\nvar YearRangePickPanel = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"panel-year-range.vue\"]]);\nexport { YearRangePickPanel as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}