{"ast": null, "code": "import arrayMap from './_arrayMap.js';\nimport baseGet from './_baseGet.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseMap from './_baseMap.js';\nimport baseSortBy from './_baseSortBy.js';\nimport baseUnary from './_baseUnary.js';\nimport compareMultiple from './_compareMultiple.js';\nimport identity from './identity.js';\nimport isArray from './isArray.js';\n\n/**\n * The base implementation of `_.orderBy` without param guards.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function[]|Object[]|string[]} iteratees The iteratees to sort by.\n * @param {string[]} orders The sort orders of `iteratees`.\n * @returns {Array} Returns the new sorted array.\n */\nfunction baseOrderBy(collection, iteratees, orders) {\n  if (iteratees.length) {\n    iteratees = arrayMap(iteratees, function (iteratee) {\n      if (isArray(iteratee)) {\n        return function (value) {\n          return baseGet(value, iteratee.length === 1 ? iteratee[0] : iteratee);\n        };\n      }\n      return iteratee;\n    });\n  } else {\n    iteratees = [identity];\n  }\n  var index = -1;\n  iteratees = arrayMap(iteratees, baseUnary(baseIteratee));\n  var result = baseMap(collection, function (value, key, collection) {\n    var criteria = arrayMap(iteratees, function (iteratee) {\n      return iteratee(value);\n    });\n    return {\n      'criteria': criteria,\n      'index': ++index,\n      'value': value\n    };\n  });\n  return baseSortBy(result, function (object, other) {\n    return compareMultiple(object, other, orders);\n  });\n}\nexport default baseOrderBy;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}