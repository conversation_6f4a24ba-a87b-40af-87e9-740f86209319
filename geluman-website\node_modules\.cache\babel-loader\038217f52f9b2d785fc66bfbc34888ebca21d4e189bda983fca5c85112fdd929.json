{"ast": null, "code": "import { defineComponent, h } from 'vue';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nvar NodeContent = defineComponent({\n  name: \"NodeContent\",\n  setup() {\n    const ns = useNamespace(\"cascader-node\");\n    return {\n      ns\n    };\n  },\n  render() {\n    const {\n      ns\n    } = this;\n    const {\n      node,\n      panel\n    } = this.$parent;\n    const {\n      data,\n      label\n    } = node;\n    const {\n      renderLabelFn\n    } = panel;\n    return h(\"span\", {\n      class: ns.e(\"label\")\n    }, renderLabelFn ? renderLabelFn({\n      node,\n      data\n    }) : label);\n  }\n});\nexport { NodeContent as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}