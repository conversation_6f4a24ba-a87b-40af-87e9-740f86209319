{"ast": null, "code": "var SortOrder = /* @__PURE__ */(SortOrder2 => {\n  SortOrder2[\"ASC\"] = \"asc\";\n  SortOrder2[\"DESC\"] = \"desc\";\n  return SortOrder2;\n})(SortOrder || {});\nvar Alignment = /* @__PURE__ */(Alignment2 => {\n  Alignment2[\"CENTER\"] = \"center\";\n  Alignment2[\"RIGHT\"] = \"right\";\n  return Alignment2;\n})(Alignment || {});\nvar FixedDir = /* @__PURE__ */(FixedDir2 => {\n  FixedDir2[\"LEFT\"] = \"left\";\n  FixedDir2[\"RIGHT\"] = \"right\";\n  return FixedDir2;\n})(FixedDir || {});\nconst oppositeOrderMap = {\n  [\"asc\" /* ASC */]: \"desc\" /* DESC */,\n  [\"desc\" /* DESC */]: \"asc\" /* ASC */\n};\nconst sortOrders = [\"asc\" /* ASC */, \"desc\" /* DESC */];\nexport { Alignment, FixedDir, SortOrder, oppositeOrderMap, sortOrders };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}