{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeStyle as _normalizeStyle, resolveDynamicComponent as _resolveDynamicComponent, createBlock as _createBlock, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"about-page\"\n};\nconst _hoisted_2 = {\n  class: \"about-hero\"\n};\nconst _hoisted_3 = {\n  class: \"hero-background\"\n};\nconst _hoisted_4 = {\n  class: \"tech-particles\"\n};\nconst _hoisted_5 = {\n  class: \"values\"\n};\nconst _hoisted_6 = {\n  class: \"container\"\n};\nconst _hoisted_7 = {\n  class: \"values-grid\"\n};\nconst _hoisted_8 = {\n  class: \"value-icon animate-pulse\"\n};\nconst _hoisted_9 = {\n  class: \"contact\"\n};\nconst _hoisted_10 = {\n  class: \"container\"\n};\nconst _hoisted_11 = {\n  class: \"contact-content\"\n};\nconst _hoisted_12 = {\n  class: \"contact-info\"\n};\nconst _hoisted_13 = {\n  class: \"info-item\"\n};\nconst _hoisted_14 = {\n  class: \"info-item\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"section\", _hoisted_2, [_cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"about-content\"\n  }, [_createElementVNode(\"h1\", {\n    class: \"title\"\n  }, \"关于格鲁曼\"), _createElementVNode(\"p\", {\n    class: \"subtitle\"\n  }, \"用创新驱动技术，以匠心创造价值\")], -1 /* HOISTED */)), _createCommentVNode(\" 添加粒子网格背景 \"), _createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" 科技感粒子 \"), _createElementVNode(\"div\", _hoisted_4, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(30, n => {\n    return _createElementVNode(\"span\", {\n      key: n,\n      class: \"particle\",\n      style: _normalizeStyle({\n        '--delay': `${Math.random() * 5}s`,\n        '--size': `${Math.random() * 3 + 1}px`,\n        '--x': `${Math.random() * 100}%`,\n        '--y': `${Math.random() * 100}%`\n      })\n    }, null, 4 /* STYLE */);\n  }), 64 /* STABLE_FRAGMENT */))]), _createCommentVNode(\" 网格背景 \"), _cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"grid-background\"\n  }, null, -1 /* HOISTED */))])]), _cache[6] || (_cache[6] = _createStaticVNode(\"<section class=\\\"company-intro\\\" data-v-039c5b43><div class=\\\"container\\\" data-v-039c5b43><div class=\\\"intro-grid\\\" data-v-039c5b43><div class=\\\"intro-text\\\" data-v-039c5b43><h2 data-v-039c5b43>我们的故事</h2><p data-v-039c5b43> 成立于2024年，格鲁曼专注于开发浏览器插件和休闲小游戏，致力于为用户提供卓越的在线体验。&quot;匠心创造价值&quot;是我们的核心理念。我们相信，每一个细节都至关重要，只有通过精细打磨和不断优化，才能创造出真正有价值的产品。 </p></div><div class=\\\"intro-decoration\\\" data-v-039c5b43><div class=\\\"tech-circles\\\" data-v-039c5b43><span class=\\\"circle circle-1\\\" data-v-039c5b43></span><span class=\\\"circle circle-2\\\" data-v-039c5b43></span><span class=\\\"circle circle-3\\\" data-v-039c5b43></span></div><div class=\\\"code-lines\\\" data-v-039c5b43><span class=\\\"line\\\" data-v-039c5b43></span><span class=\\\"line\\\" data-v-039c5b43></span><span class=\\\"line\\\" data-v-039c5b43></span></div></div></div></div></section>\", 1)), _createElementVNode(\"section\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[2] || (_cache[2] = _createElementVNode(\"h2\", {\n    class: \"section-title animate-fadeInUp\"\n  }, \"我们的价值观\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_7, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList($setup.values, (value, index) => {\n    return _createVNode(_component_el_card, {\n      key: value.title,\n      class: \"value-card animate-fadeInUp\",\n      style: _normalizeStyle({\n        animationDelay: `${(index + 1) * 0.2}s`\n      })\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_icon, {\n        size: 32\n      }, {\n        default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(value.icon)))]),\n        _: 2 /* DYNAMIC */\n      }, 1024 /* DYNAMIC_SLOTS */)]), _createElementVNode(\"h3\", null, _toDisplayString(value.title), 1 /* TEXT */), _createElementVNode(\"p\", null, _toDisplayString(value.description), 1 /* TEXT */)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"style\"]);\n  }), 64 /* STABLE_FRAGMENT */))])])]), _createElementVNode(\"section\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_cache[5] || (_cache[5] = _createElementVNode(\"h2\", {\n    class: \"section-title\"\n  }, \"联系我们\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"Message\"])]),\n    _: 1 /* STABLE */\n  }), _cache[3] || (_cache[3] = _createElementVNode(\"span\", null, \"<EMAIL>\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"Location\"])]),\n    _: 1 /* STABLE */\n  }), _cache[4] || (_cache[4] = _createElementVNode(\"span\", null, \"中国·成都\", -1 /* HOISTED */))])])])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createCommentVNode", "_hoisted_3", "_hoisted_4", "_Fragment", "_renderList", "n", "key", "style", "_normalizeStyle", "Math", "random", "_createStaticVNode", "_hoisted_5", "_hoisted_6", "_hoisted_7", "$setup", "values", "value", "index", "_createVNode", "_component_el_card", "title", "animationDelay", "default", "_withCtx", "_hoisted_8", "_component_el_icon", "size", "_createBlock", "_resolveDynamicComponent", "icon", "_", "_toDisplayString", "description", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14"], "sources": ["D:\\codes\\glmwebsite\\geluman-website\\src\\views\\About.vue"], "sourcesContent": ["<template>\r\n  <div class=\"about-page\">\r\n    <section class=\"about-hero\">\r\n      <div class=\"about-content\">\r\n        <h1 class=\"title\">关于格鲁曼</h1>\r\n        <p class=\"subtitle\">用创新驱动技术，以匠心创造价值</p>\r\n      </div>\r\n\r\n      <!-- 添加粒子网格背景 -->\r\n      <div class=\"hero-background\">\r\n        <!-- 科技感粒子 -->\r\n        <div class=\"tech-particles\">\r\n          <span\r\n            v-for=\"n in 30\"\r\n            :key=\"n\"\r\n            class=\"particle\"\r\n            :style=\"{\r\n              '--delay': `${Math.random() * 5}s`,\r\n              '--size': `${Math.random() * 3 + 1}px`,\r\n              '--x': `${Math.random() * 100}%`,\r\n              '--y': `${Math.random() * 100}%`,\r\n            }\"\r\n          ></span>\r\n        </div>\r\n        <!-- 网格背景 -->\r\n        <div class=\"grid-background\"></div>\r\n      </div>\r\n    </section>\r\n\r\n    <section class=\"company-intro\">\r\n      <div class=\"container\">\r\n        <div class=\"intro-grid\">\r\n          <div class=\"intro-text\">\r\n            <h2>我们的故事</h2>\r\n            <p>\r\n              成立于2024年，格鲁曼专注于开发浏览器插件和休闲小游戏，致力于为用户提供卓越的在线体验。\"匠心创造价值\"是我们的核心理念。我们相信，每一个细节都至关重要，只有通过精细打磨和不断优化，才能创造出真正有价值的产品。\r\n            </p>\r\n          </div>\r\n          <div class=\"intro-decoration\">\r\n            <div class=\"tech-circles\">\r\n              <span class=\"circle circle-1\"></span>\r\n              <span class=\"circle circle-2\"></span>\r\n              <span class=\"circle circle-3\"></span>\r\n            </div>\r\n            <div class=\"code-lines\">\r\n              <span class=\"line\"></span>\r\n              <span class=\"line\"></span>\r\n              <span class=\"line\"></span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <section class=\"values\">\r\n      <div class=\"container\">\r\n        <h2 class=\"section-title animate-fadeInUp\">我们的价值观</h2>\r\n        <div class=\"values-grid\">\r\n          <el-card\r\n            v-for=\"(value, index) in values\"\r\n            :key=\"value.title\"\r\n            class=\"value-card animate-fadeInUp\"\r\n            :style=\"{ animationDelay: `${(index + 1) * 0.2}s` }\"\r\n          >\r\n            <div class=\"value-icon animate-pulse\">\r\n              <el-icon :size=\"32\"><component :is=\"value.icon\" /></el-icon>\r\n            </div>\r\n            <h3>{{ value.title }}</h3>\r\n            <p>{{ value.description }}</p>\r\n          </el-card>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <section class=\"contact\">\r\n      <div class=\"container\">\r\n        <h2 class=\"section-title\">联系我们</h2>\r\n        <div class=\"contact-content\">\r\n          <div class=\"contact-info\">\r\n            <div class=\"info-item\">\r\n              <el-icon><Message /></el-icon>\r\n              <span><EMAIL></span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <el-icon><Location /></el-icon>\r\n              <span>中国·成都</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"AboutPage\",\r\n};\r\n</script>\r\n\r\n<script setup>\r\nimport {\r\n  Promotion,\r\n  Medal,\r\n  Cpu,\r\n  User,\r\n  Message,\r\n  Location,\r\n} from \"@element-plus/icons-vue\";\r\n\r\nconst values = [\r\n  {\r\n    icon: Promotion,\r\n    title: \"创新驱动\",\r\n    description: \"持续创新，推动技术边界，为用户带来更好的产品体验\",\r\n  },\r\n  {\r\n    icon: Medal,\r\n    title: \"匠心品质\",\r\n    description: \"专注细节，精益求精，打造极致用户体验\",\r\n  },\r\n  {\r\n    icon: Cpu,\r\n    title: \"技术领先\",\r\n    description: \"拥抱新技术，保持技术敏锐度，引领行业发展\",\r\n  },\r\n  {\r\n    icon: User,\r\n    title: \"用户至上\",\r\n    description: \"以用户需求为中心，持续优化产品，提供贴心服务\",\r\n  },\r\n];\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.about-page {\r\n  padding-top: var(--header-height);\r\n}\r\n\r\n.about-hero {\r\n  background: linear-gradient(\r\n    135deg,\r\n    var(--primary-color) 0%,\r\n    var(--primary-dark) 100%\r\n  );\r\n  padding: 6rem 0;\r\n  color: white;\r\n  text-align: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  .about-content {\r\n    position: relative;\r\n    z-index: 10;\r\n  }\r\n\r\n  .title {\r\n    font-size: 3rem;\r\n    margin-bottom: 1rem;\r\n    animation: fadeInUp 1s ease;\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 1.2rem;\r\n    opacity: 0.9;\r\n    animation: fadeInUp 1s ease 0.2s backwards;\r\n  }\r\n}\r\n\r\n.container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n.company-intro {\r\n  padding: 6rem 0;\r\n  background: var(--bg-secondary);\r\n\r\n  .intro-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr;\r\n    gap: 4rem;\r\n    align-items: center;\r\n\r\n    @media (max-width: 768px) {\r\n      grid-template-columns: 1fr;\r\n      gap: 2rem;\r\n    }\r\n  }\r\n\r\n  .intro-text {\r\n    h2 {\r\n      font-size: 2rem;\r\n      margin-bottom: 1.5rem;\r\n      color: var(--text-primary);\r\n    }\r\n\r\n    p {\r\n      color: var(--text-secondary);\r\n      line-height: 1.8;\r\n    }\r\n  }\r\n\r\n  .intro-decoration {\r\n    position: relative;\r\n    height: 300px;\r\n\r\n    .tech-circles {\r\n      position: absolute;\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n      .circle {\r\n        position: absolute;\r\n        border-radius: 50%;\r\n        background: var(--primary-color);\r\n        opacity: 0.1;\r\n        animation: float 6s infinite ease-in-out;\r\n\r\n        &.circle-1 {\r\n          width: 100px;\r\n          height: 100px;\r\n          top: 20%;\r\n          left: 20%;\r\n          animation-delay: 0s;\r\n        }\r\n\r\n        &.circle-2 {\r\n          width: 60px;\r\n          height: 60px;\r\n          top: 50%;\r\n          right: 30%;\r\n          animation-delay: -2s;\r\n        }\r\n\r\n        &.circle-3 {\r\n          width: 40px;\r\n          height: 40px;\r\n          bottom: 20%;\r\n          left: 40%;\r\n          animation-delay: -4s;\r\n        }\r\n      }\r\n    }\r\n\r\n    .code-lines {\r\n      position: absolute;\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n      .line {\r\n        position: absolute;\r\n        height: 2px;\r\n        background: linear-gradient(\r\n          90deg,\r\n          var(--primary-color) 0%,\r\n          transparent 100%\r\n        );\r\n        opacity: 0.2;\r\n        animation: slidein 3s infinite ease-in-out;\r\n\r\n        &:nth-child(1) {\r\n          width: 60%;\r\n          top: 30%;\r\n          left: -10%;\r\n          animation-delay: 0s;\r\n        }\r\n\r\n        &:nth-child(2) {\r\n          width: 40%;\r\n          top: 50%;\r\n          left: -10%;\r\n          animation-delay: -1s;\r\n        }\r\n\r\n        &:nth-child(3) {\r\n          width: 50%;\r\n          top: 70%;\r\n          left: -10%;\r\n          animation-delay: -2s;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.values {\r\n  padding: 6rem 0;\r\n\r\n  .section-title {\r\n    text-align: center;\r\n    font-size: 2rem;\r\n    margin-bottom: 3rem;\r\n  }\r\n\r\n  .values-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n    gap: 2rem;\r\n  }\r\n\r\n  .value-card {\r\n    text-align: center;\r\n    padding: 2rem;\r\n    transition: var(--transition-base);\r\n\r\n    &:hover {\r\n      transform: translateY(-5px);\r\n    }\r\n\r\n    .value-icon {\r\n      margin-bottom: 1.5rem;\r\n\r\n      .el-icon {\r\n        font-size: 2.5rem;\r\n        color: var(--primary-color);\r\n      }\r\n    }\r\n\r\n    h3 {\r\n      margin-bottom: 1rem;\r\n      color: var(--text-primary);\r\n    }\r\n\r\n    p {\r\n      color: var(--text-secondary);\r\n    }\r\n  }\r\n}\r\n\r\n.contact {\r\n  padding: 6rem 0;\r\n  background: var(--bg-secondary);\r\n\r\n  .section-title {\r\n    text-align: center;\r\n    font-size: 2rem;\r\n    margin-bottom: 3rem;\r\n  }\r\n\r\n  .contact-content {\r\n    max-width: 600px;\r\n    margin: 0 auto;\r\n  }\r\n\r\n  .contact-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 1.5rem;\r\n    align-items: center;\r\n\r\n    .info-item {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 0.5rem;\r\n      color: var(--text-secondary);\r\n\r\n      .el-icon {\r\n        color: var(--primary-color);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .about-hero {\r\n    padding: 4rem 1rem;\r\n\r\n    .title {\r\n      font-size: 2.5rem;\r\n    }\r\n  }\r\n\r\n  .company-intro,\r\n  .values,\r\n  .contact {\r\n    padding: 4rem 1rem;\r\n  }\r\n}\r\n\r\n.hero-background {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: hidden;\r\n  z-index: 0;\r\n}\r\n\r\n.tech-particles {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 2;\r\n\r\n  .particle {\r\n    position: absolute;\r\n    width: var(--size);\r\n    height: var(--size);\r\n    background: rgba(255, 255, 255, 0.5);\r\n    border-radius: 50%;\r\n    left: var(--x);\r\n    top: var(--y);\r\n    animation: pulse 2s infinite ease-in-out;\r\n    animation-delay: var(--delay);\r\n  }\r\n}\r\n\r\n.grid-background {\r\n  position: absolute;\r\n  width: 800%;\r\n  height: 800%;\r\n  background: linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),\r\n    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);\r\n  background-size: 50px 50px;\r\n  transform: rotate(45deg);\r\n  top: -350%;\r\n  left: -350%;\r\n  animation: grid-move 50s linear infinite;\r\n  pointer-events: none;\r\n}\r\n\r\n.value-card {\r\n  &:hover {\r\n    .value-icon {\r\n      animation: iconPop 0.5s ease;\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes particleMove {\r\n  0% {\r\n    background-position: 0% 0%;\r\n  }\r\n  100% {\r\n    background-position: 100% 100%;\r\n  }\r\n}\r\n\r\n@keyframes iconPop {\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.2);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n.animate-pulse {\r\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0%,\r\n  100% {\r\n    transform: scale(1);\r\n    opacity: 0.2;\r\n  }\r\n  50% {\r\n    transform: scale(1.5);\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n@keyframes float {\r\n  0%,\r\n  100% {\r\n    transform: translateY(0) scale(1);\r\n  }\r\n  50% {\r\n    transform: translateY(-20px) scale(1.1);\r\n  }\r\n}\r\n\r\n@keyframes slidein {\r\n  0% {\r\n    transform: translateX(-100%);\r\n    opacity: 0;\r\n  }\r\n  50% {\r\n    transform: translateX(0);\r\n    opacity: 0.2;\r\n  }\r\n  100% {\r\n    transform: translateX(100%);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n@keyframes grid-move {\r\n  0% {\r\n    transform: translate3d(-5%, -5%, 0) rotate(45deg);\r\n  }\r\n  100% {\r\n    transform: translate3d(-15%, -15%, 0) rotate(45deg);\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAY;;EACZA,KAAK,EAAC;AAAY;;EAOpBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAgB;;EA2CtBA,KAAK,EAAC;AAAQ;;EAChBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAa;;EAOfA,KAAK,EAAC;AAA0B;;EAUpCA,KAAK,EAAC;AAAS;;EACjBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;;;uBAlFhCC,mBAAA,CA0FM,OA1FNC,UA0FM,GAzFJC,mBAAA,CAyBU,WAzBVC,UAyBU,G,0BAxBRD,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAe,IACxBG,mBAAA,CAA4B;IAAxBH,KAAK,EAAC;EAAO,GAAC,OAAK,GACvBG,mBAAA,CAAuC;IAApCH,KAAK,EAAC;EAAU,GAAC,iBAAe,E,sBAGrCK,mBAAA,cAAiB,EACjBF,mBAAA,CAiBM,OAjBNG,UAiBM,GAhBJD,mBAAA,WAAc,EACdF,mBAAA,CAYM,OAZNI,UAYM,I,cAXJN,mBAAA,CAUQO,SAAA,QAtBlBC,WAAA,CAawB,EAAE,EAAPC,CAAC;WADVP,mBAAA,CAUQ;MARLQ,GAAG,EAAED,CAAC;MACPV,KAAK,EAAC,UAAU;MACfY,KAAK,EAhBlBC,eAAA;sBAgBmDC,IAAI,CAACC,MAAM;qBAAuCD,IAAI,CAACC,MAAM;kBAAyCD,IAAI,CAACC,MAAM;kBAAsCD,IAAI,CAACC,MAAM;;;oCAQ7MV,mBAAA,UAAa,E,0BACbF,mBAAA,CAAmC;IAA9BH,KAAK,EAAC;EAAiB,4B,+BAzBpCgB,kBAAA,g0BAsDIb,mBAAA,CAkBU,WAlBVc,UAkBU,GAjBRd,mBAAA,CAgBM,OAhBNe,UAgBM,G,0BAfJf,mBAAA,CAAsD;IAAlDH,KAAK,EAAC;EAAgC,GAAC,QAAM,sBACjDG,mBAAA,CAaM,OAbNgB,UAaM,I,cAZJlB,mBAAA,CAWUO,SAAA,QArEpBC,WAAA,CA2DqCW,MAAA,CAAAC,MAAM,EA3D3C,CA2DoBC,KAAK,EAAEC,KAAK;WADtBC,YAAA,CAWUC,kBAAA;MATPd,GAAG,EAAEW,KAAK,CAACI,KAAK;MACjB1B,KAAK,EAAC,6BAA6B;MAClCY,KAAK,EA9DlBC,eAAA;QAAAc,cAAA,MA8D0CJ,KAAK;MAAA;;MA9D/CK,OAAA,EAAAC,QAAA,CAgEY,MAEM,CAFN1B,mBAAA,CAEM,OAFN2B,UAEM,GADJN,YAAA,CAA4DO,kBAAA;QAAlDC,IAAI,EAAE;MAAE;QAjEhCJ,OAAA,EAAAC,QAAA,CAiEkC,MAA8B,E,cAA9BI,YAAA,CAA8BC,wBAjEhE,CAiEkDZ,KAAK,CAACa,IAAI,I;QAjE5DC,CAAA;sCAmEYjC,mBAAA,CAA0B,YAAAkC,gBAAA,CAAnBf,KAAK,CAACI,KAAK,kBAClBvB,mBAAA,CAA8B,WAAAkC,gBAAA,CAAxBf,KAAK,CAACgB,WAAW,iB;MApEnCF,CAAA;;wCA0EIjC,mBAAA,CAgBU,WAhBVoC,UAgBU,GAfRpC,mBAAA,CAcM,OAdNqC,WAcM,G,0BAbJrC,mBAAA,CAAmC;IAA/BH,KAAK,EAAC;EAAe,GAAC,MAAI,sBAC9BG,mBAAA,CAWM,OAXNsC,WAWM,GAVJtC,mBAAA,CASM,OATNuC,WASM,GARJvC,mBAAA,CAGM,OAHNwC,WAGM,GAFJnB,YAAA,CAA8BO,kBAAA;IAhF5CH,OAAA,EAAAC,QAAA,CAgFuB,MAAW,CAAXL,YAAA,CAAWJ,MAAA,a;IAhFlCgB,CAAA;gCAiFcjC,mBAAA,CAA+B,cAAzB,oBAAkB,qB,GAE1BA,mBAAA,CAGM,OAHNyC,WAGM,GAFJpB,YAAA,CAA+BO,kBAAA;IApF7CH,OAAA,EAAAC,QAAA,CAoFuB,MAAY,CAAZL,YAAA,CAAYJ,MAAA,c;IApFnCgB,CAAA;gCAqFcjC,mBAAA,CAAkB,cAAZ,OAAK,qB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}