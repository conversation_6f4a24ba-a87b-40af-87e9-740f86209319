{"ast": null, "code": "import { panelSharedProps } from './shared.mjs';\nimport { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nconst panelDatePickProps = buildProps({\n  ...panelSharedProps,\n  parsedValue: {\n    type: definePropType([Object, Array])\n  },\n  visible: {\n    type: Boolean\n  },\n  format: {\n    type: String,\n    default: \"\"\n  }\n});\nexport { panelDatePickProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}