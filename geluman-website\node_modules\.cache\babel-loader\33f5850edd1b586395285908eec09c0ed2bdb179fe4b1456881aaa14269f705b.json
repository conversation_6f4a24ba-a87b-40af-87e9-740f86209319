{"ast": null, "code": "import { placements } from '@popperjs/core';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nconst sliderButtonProps = buildProps({\n  modelValue: {\n    type: Number,\n    default: 0\n  },\n  vertical: Boolean,\n  tooltipClass: String,\n  placement: {\n    type: String,\n    values: placements,\n    default: \"top\"\n  }\n});\nconst sliderButtonEmits = {\n  [UPDATE_MODEL_EVENT]: value => isNumber(value)\n};\nexport { sliderButtonEmits, sliderButtonProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}