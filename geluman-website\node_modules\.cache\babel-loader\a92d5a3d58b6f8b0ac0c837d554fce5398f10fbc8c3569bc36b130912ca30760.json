{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nconst imageViewerProps = buildProps({\n  urlList: {\n    type: definePropType(Array),\n    default: () => mutable([])\n  },\n  zIndex: {\n    type: Number\n  },\n  initialIndex: {\n    type: Number,\n    default: 0\n  },\n  infinite: {\n    type: Boolean,\n    default: true\n  },\n  hideOnClickModal: Boolean,\n  teleported: Boolean,\n  closeOnPressEscape: {\n    type: Boolean,\n    default: true\n  },\n  zoomRate: {\n    type: Number,\n    default: 1.2\n  },\n  minScale: {\n    type: Number,\n    default: 0.2\n  },\n  maxScale: {\n    type: Number,\n    default: 7\n  },\n  showProgress: {\n    type: Boolean,\n    default: false\n  },\n  crossorigin: {\n    type: definePropType(String)\n  }\n});\nconst imageViewerEmits = {\n  close: () => true,\n  switch: index => isNumber(index),\n  rotate: deg => isNumber(deg)\n};\nexport { imageViewerEmits, imageViewerProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}