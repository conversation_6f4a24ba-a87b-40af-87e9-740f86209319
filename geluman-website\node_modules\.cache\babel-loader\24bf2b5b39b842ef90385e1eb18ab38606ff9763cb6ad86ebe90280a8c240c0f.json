{"ast": null, "code": "import { isClient, unrefElement } from '@vueuse/core';\nconst buildPopperOptions = (props, modifiers = []) => {\n  const {\n    placement,\n    strategy,\n    popperOptions\n  } = props;\n  const options = {\n    placement,\n    strategy,\n    ...popperOptions,\n    modifiers: [...genModifiers(props), ...modifiers]\n  };\n  deriveExtraModifiers(options, popperOptions == null ? void 0 : popperOptions.modifiers);\n  return options;\n};\nconst unwrapMeasurableEl = $el => {\n  if (!isClient) return;\n  return unrefElement($el);\n};\nfunction genModifiers(options) {\n  const {\n    offset,\n    gpuAcceleration,\n    fallbackPlacements\n  } = options;\n  return [{\n    name: \"offset\",\n    options: {\n      offset: [0, offset != null ? offset : 12]\n    }\n  }, {\n    name: \"preventOverflow\",\n    options: {\n      padding: {\n        top: 2,\n        bottom: 2,\n        left: 5,\n        right: 5\n      }\n    }\n  }, {\n    name: \"flip\",\n    options: {\n      padding: 5,\n      fallbackPlacements\n    }\n  }, {\n    name: \"computeStyles\",\n    options: {\n      gpuAcceleration\n    }\n  }];\n}\nfunction deriveExtraModifiers(options, modifiers) {\n  if (modifiers) {\n    options.modifiers = [...options.modifiers, ...(modifiers != null ? modifiers : [])];\n  }\n}\nexport { buildPopperOptions, unwrapMeasurableEl };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}