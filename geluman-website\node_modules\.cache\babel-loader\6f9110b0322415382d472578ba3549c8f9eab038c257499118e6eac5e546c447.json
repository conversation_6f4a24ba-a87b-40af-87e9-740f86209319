{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, withDirectives, createElementVNode, isRef, withModifiers, vModelRadio, normalizeStyle, renderSlot, createTextVNode, toDisplayString } from 'vue';\nimport { useRadio } from './use-radio.mjs';\nimport { radioButtonProps } from './radio-button.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElRadioButton\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: radioButtonProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"radio\");\n    const {\n      radioRef,\n      focus,\n      size,\n      disabled,\n      modelValue,\n      radioGroup,\n      actualValue\n    } = useRadio(props);\n    const activeStyle = computed(() => {\n      return {\n        backgroundColor: (radioGroup == null ? void 0 : radioGroup.fill) || \"\",\n        borderColor: (radioGroup == null ? void 0 : radioGroup.fill) || \"\",\n        boxShadow: (radioGroup == null ? void 0 : radioGroup.fill) ? `-1px 0 0 0 ${radioGroup.fill}` : \"\",\n        color: (radioGroup == null ? void 0 : radioGroup.textColor) || \"\"\n      };\n    });\n    return (_ctx, _cache) => {\n      var _a;\n      return openBlock(), createElementBlock(\"label\", {\n        class: normalizeClass([unref(ns).b(\"button\"), unref(ns).is(\"active\", unref(modelValue) === unref(actualValue)), unref(ns).is(\"disabled\", unref(disabled)), unref(ns).is(\"focus\", unref(focus)), unref(ns).bm(\"button\", unref(size))])\n      }, [withDirectives(createElementVNode(\"input\", {\n        ref_key: \"radioRef\",\n        ref: radioRef,\n        \"onUpdate:modelValue\": $event => isRef(modelValue) ? modelValue.value = $event : null,\n        class: normalizeClass(unref(ns).be(\"button\", \"original-radio\")),\n        value: unref(actualValue),\n        type: \"radio\",\n        name: _ctx.name || ((_a = unref(radioGroup)) == null ? void 0 : _a.name),\n        disabled: unref(disabled),\n        onFocus: $event => focus.value = true,\n        onBlur: $event => focus.value = false,\n        onClick: withModifiers(() => {}, [\"stop\"])\n      }, null, 42, [\"onUpdate:modelValue\", \"value\", \"name\", \"disabled\", \"onFocus\", \"onBlur\", \"onClick\"]), [[vModelRadio, unref(modelValue)]]), createElementVNode(\"span\", {\n        class: normalizeClass(unref(ns).be(\"button\", \"inner\")),\n        style: normalizeStyle(unref(modelValue) === unref(actualValue) ? unref(activeStyle) : {}),\n        onKeydown: withModifiers(() => {}, [\"stop\"])\n      }, [renderSlot(_ctx.$slots, \"default\", {}, () => [createTextVNode(toDisplayString(_ctx.label), 1)])], 46, [\"onKeydown\"])], 2);\n    };\n  }\n});\nvar RadioButton = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"radio-button.vue\"]]);\nexport { RadioButton as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}