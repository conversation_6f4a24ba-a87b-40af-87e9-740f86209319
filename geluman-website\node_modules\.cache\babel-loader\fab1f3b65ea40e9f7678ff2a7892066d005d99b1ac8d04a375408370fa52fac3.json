{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nconst stepProps = buildProps({\n  title: {\n    type: String,\n    default: \"\"\n  },\n  icon: {\n    type: iconPropType\n  },\n  description: {\n    type: String,\n    default: \"\"\n  },\n  status: {\n    type: String,\n    values: [\"\", \"wait\", \"process\", \"finish\", \"error\", \"success\"],\n    default: \"\"\n  }\n});\nexport { stepProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}