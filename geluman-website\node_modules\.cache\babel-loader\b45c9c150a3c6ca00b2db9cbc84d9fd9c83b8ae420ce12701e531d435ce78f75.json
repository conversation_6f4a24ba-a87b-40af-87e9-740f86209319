{"ast": null, "code": "import Select from './src/select2.mjs';\nimport Option from './src/option2.mjs';\nimport OptionGroup from './src/option-group.mjs';\nexport { selectGroupKey, selectKey } from './src/token.mjs';\nexport { SelectProps, selectEmits } from './src/select.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElSelect = withInstall(Select, {\n  Option,\n  OptionGroup\n});\nconst ElOption = withNoopInstall(Option);\nconst ElOptionGroup = withNoopInstall(OptionGroup);\nexport { ElOption, ElOptionGroup, ElSelect, ElSelect as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}