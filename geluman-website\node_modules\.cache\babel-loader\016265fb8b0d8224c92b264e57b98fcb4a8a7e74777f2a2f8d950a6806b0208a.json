{"ast": null, "code": "import { computed } from 'vue';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nconst useMonthRangeHeader = ({\n  unlinkPanels,\n  leftDate,\n  rightDate\n}) => {\n  const {\n    t\n  } = useLocale();\n  const leftPrevYear = () => {\n    leftDate.value = leftDate.value.subtract(1, \"year\");\n    if (!unlinkPanels.value) {\n      rightDate.value = rightDate.value.subtract(1, \"year\");\n    }\n  };\n  const rightNextYear = () => {\n    if (!unlinkPanels.value) {\n      leftDate.value = leftDate.value.add(1, \"year\");\n    }\n    rightDate.value = rightDate.value.add(1, \"year\");\n  };\n  const leftNextYear = () => {\n    leftDate.value = leftDate.value.add(1, \"year\");\n  };\n  const rightPrevYear = () => {\n    rightDate.value = rightDate.value.subtract(1, \"year\");\n  };\n  const leftLabel = computed(() => {\n    return `${leftDate.value.year()} ${t(\"el.datepicker.year\")}`;\n  });\n  const rightLabel = computed(() => {\n    return `${rightDate.value.year()} ${t(\"el.datepicker.year\")}`;\n  });\n  const leftYear = computed(() => {\n    return leftDate.value.year();\n  });\n  const rightYear = computed(() => {\n    return rightDate.value.year() === leftDate.value.year() ? leftDate.value.year() + 1 : rightDate.value.year();\n  });\n  return {\n    leftPrevYear,\n    rightNextYear,\n    leftNextYear,\n    rightPrevYear,\n    leftLabel,\n    rightLabel,\n    leftYear,\n    rightYear\n  };\n};\nexport { useMonthRangeHeader };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}