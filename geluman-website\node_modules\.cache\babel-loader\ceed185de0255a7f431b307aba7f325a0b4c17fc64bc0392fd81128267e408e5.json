{"ast": null, "code": "import { defineComponent, ref, provide, onMounted, openBlock, createElementBlock, normalizeClass, unref, renderSlot } from 'vue';\nimport { breadcrumbKey } from './constants.mjs';\nimport { breadcrumbProps } from './breadcrumb.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElBreadcrumb\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: breadcrumbProps,\n  setup(__props) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"breadcrumb\");\n    const breadcrumb = ref();\n    provide(breadcrumbKey, props);\n    onMounted(() => {\n      const items = breadcrumb.value.querySelectorAll(`.${ns.e(\"item\")}`);\n      if (items.length) {\n        items[items.length - 1].setAttribute(\"aria-current\", \"page\");\n      }\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"breadcrumb\",\n        ref: breadcrumb,\n        class: normalizeClass(unref(ns).b()),\n        \"aria-label\": unref(t)(\"el.breadcrumb.label\"),\n        role: \"navigation\"\n      }, [renderSlot(_ctx.$slots, \"default\")], 10, [\"aria-label\"]);\n    };\n  }\n});\nvar Breadcrumb = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"breadcrumb.vue\"]]);\nexport { Breadcrumb as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}