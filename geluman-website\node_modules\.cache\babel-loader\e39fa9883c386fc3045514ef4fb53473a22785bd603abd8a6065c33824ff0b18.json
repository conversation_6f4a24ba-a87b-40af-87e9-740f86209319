{"ast": null, "code": "import baseExtremum from './_baseExtremum.js';\nimport baseLt from './_baseLt.js';\nimport identity from './identity.js';\n\n/**\n * Computes the minimum value of `array`. If `array` is empty or falsey,\n * `undefined` is returned.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Math\n * @param {Array} array The array to iterate over.\n * @returns {*} Returns the minimum value.\n * @example\n *\n * _.min([4, 2, 8, 6]);\n * // => 2\n *\n * _.min([]);\n * // => undefined\n */\nfunction min(array) {\n  return array && array.length ? baseExtremum(array, identity, baseLt) : undefined;\n}\nexport default min;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}