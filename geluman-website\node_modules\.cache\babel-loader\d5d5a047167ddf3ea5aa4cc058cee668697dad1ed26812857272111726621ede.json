{"ast": null, "code": "import baseSortedUniq from './_baseSortedUniq.js';\n\n/**\n * This method is like `_.uniq` except that it's designed and optimized\n * for sorted arrays.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @returns {Array} Returns the new duplicate free array.\n * @example\n *\n * _.sortedUniq([1, 1, 2]);\n * // => [1, 2]\n */\nfunction sortedUniq(array) {\n  return array && array.length ? baseSortedUniq(array) : [];\n}\nexport default sortedUniq;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}