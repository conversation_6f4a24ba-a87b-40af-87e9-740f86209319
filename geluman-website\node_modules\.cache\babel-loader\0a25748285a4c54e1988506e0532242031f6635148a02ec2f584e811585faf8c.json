{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { computed } from 'vue';\nimport { usePropsAlias } from './use-props-alias.mjs';\nconst useComputedData = props => {\n  const propsAlias = usePropsAlias(props);\n  const dataObj = computed(() => props.data.reduce((o, cur) => (o[cur[propsAlias.value.key]] = cur) && o, {}));\n  const sourceData = computed(() => props.data.filter(item => !props.modelValue.includes(item[propsAlias.value.key])));\n  const targetData = computed(() => {\n    if (props.targetOrder === \"original\") {\n      return props.data.filter(item => props.modelValue.includes(item[propsAlias.value.key]));\n    } else {\n      return props.modelValue.reduce((arr, cur) => {\n        const val = dataObj.value[cur];\n        if (val) {\n          arr.push(val);\n        }\n        return arr;\n      }, []);\n    }\n  });\n  return {\n    sourceData,\n    targetData\n  };\n};\nexport { useComputedData };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}