{"ast": null, "code": "import { defineComponent, useSlots, computed, ref, openBlock, createBlock, unref, withCtx, createVNode, Transition, withDirectives, createElementVNode, mergeProps, withModifiers, normalizeClass, createElementBlock, renderSlot, toDisplayString, createCommentVNode, vShow } from 'vue';\nimport { Close } from '@element-plus/icons-vue';\nimport { ElOverlay } from '../../overlay/index.mjs';\nimport ElFocusTrap from '../../focus-trap/src/focus-trap.mjs';\nimport { ElTeleport } from '../../teleport/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { drawerProps, drawerEmits } from './drawer2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useDialog } from '../../dialog/src/use-dialog.mjs';\nimport { useDeprecated } from '../../../hooks/use-deprecated/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nconst __default__ = defineComponent({\n  name: \"ElDrawer\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: drawerProps,\n  emits: drawerEmits,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const slots = useSlots();\n    useDeprecated({\n      scope: \"el-drawer\",\n      from: \"the title slot\",\n      replacement: \"the header slot\",\n      version: \"3.0.0\",\n      ref: \"https://element-plus.org/en-US/component/drawer.html#slots\"\n    }, computed(() => !!slots.title));\n    const drawerRef = ref();\n    const focusStartRef = ref();\n    const ns = useNamespace(\"drawer\");\n    const {\n      t\n    } = useLocale();\n    const {\n      afterEnter,\n      afterLeave,\n      beforeLeave,\n      visible,\n      rendered,\n      titleId,\n      bodyId,\n      zIndex,\n      onModalClick,\n      onOpenAutoFocus,\n      onCloseAutoFocus,\n      onFocusoutPrevented,\n      onCloseRequested,\n      handleClose\n    } = useDialog(props, drawerRef);\n    const isHorizontal = computed(() => props.direction === \"rtl\" || props.direction === \"ltr\");\n    const drawerSize = computed(() => addUnit(props.size));\n    expose({\n      handleClose,\n      afterEnter,\n      afterLeave\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElTeleport), {\n        to: _ctx.appendTo,\n        disabled: _ctx.appendTo !== \"body\" ? false : !_ctx.appendToBody\n      }, {\n        default: withCtx(() => [createVNode(Transition, {\n          name: unref(ns).b(\"fade\"),\n          onAfterEnter: unref(afterEnter),\n          onAfterLeave: unref(afterLeave),\n          onBeforeLeave: unref(beforeLeave),\n          persisted: \"\"\n        }, {\n          default: withCtx(() => [withDirectives(createVNode(unref(ElOverlay), {\n            mask: _ctx.modal,\n            \"overlay-class\": _ctx.modalClass,\n            \"z-index\": unref(zIndex),\n            onClick: unref(onModalClick)\n          }, {\n            default: withCtx(() => [createVNode(unref(ElFocusTrap), {\n              loop: \"\",\n              trapped: unref(visible),\n              \"focus-trap-el\": drawerRef.value,\n              \"focus-start-el\": focusStartRef.value,\n              onFocusAfterTrapped: unref(onOpenAutoFocus),\n              onFocusAfterReleased: unref(onCloseAutoFocus),\n              onFocusoutPrevented: unref(onFocusoutPrevented),\n              onReleaseRequested: unref(onCloseRequested)\n            }, {\n              default: withCtx(() => [createElementVNode(\"div\", mergeProps({\n                ref_key: \"drawerRef\",\n                ref: drawerRef,\n                \"aria-modal\": \"true\",\n                \"aria-label\": _ctx.title || void 0,\n                \"aria-labelledby\": !_ctx.title ? unref(titleId) : void 0,\n                \"aria-describedby\": unref(bodyId)\n              }, _ctx.$attrs, {\n                class: [unref(ns).b(), _ctx.direction, unref(visible) && \"open\"],\n                style: unref(isHorizontal) ? \"width: \" + unref(drawerSize) : \"height: \" + unref(drawerSize),\n                role: \"dialog\",\n                onClick: withModifiers(() => {}, [\"stop\"])\n              }), [createElementVNode(\"span\", {\n                ref_key: \"focusStartRef\",\n                ref: focusStartRef,\n                class: normalizeClass(unref(ns).e(\"sr-focus\")),\n                tabindex: \"-1\"\n              }, null, 2), _ctx.withHeader ? (openBlock(), createElementBlock(\"header\", {\n                key: 0,\n                class: normalizeClass([unref(ns).e(\"header\"), _ctx.headerClass])\n              }, [!_ctx.$slots.title ? renderSlot(_ctx.$slots, \"header\", {\n                key: 0,\n                close: unref(handleClose),\n                titleId: unref(titleId),\n                titleClass: unref(ns).e(\"title\")\n              }, () => [!_ctx.$slots.title ? (openBlock(), createElementBlock(\"span\", {\n                key: 0,\n                id: unref(titleId),\n                role: \"heading\",\n                \"aria-level\": _ctx.headerAriaLevel,\n                class: normalizeClass(unref(ns).e(\"title\"))\n              }, toDisplayString(_ctx.title), 11, [\"id\", \"aria-level\"])) : createCommentVNode(\"v-if\", true)]) : renderSlot(_ctx.$slots, \"title\", {\n                key: 1\n              }, () => [createCommentVNode(\" DEPRECATED SLOT \")]), _ctx.showClose ? (openBlock(), createElementBlock(\"button\", {\n                key: 2,\n                \"aria-label\": unref(t)(\"el.drawer.close\"),\n                class: normalizeClass(unref(ns).e(\"close-btn\")),\n                type: \"button\",\n                onClick: unref(handleClose)\n              }, [createVNode(unref(ElIcon), {\n                class: normalizeClass(unref(ns).e(\"close\"))\n              }, {\n                default: withCtx(() => [createVNode(unref(Close))]),\n                _: 1\n              }, 8, [\"class\"])], 10, [\"aria-label\", \"onClick\"])) : createCommentVNode(\"v-if\", true)], 2)) : createCommentVNode(\"v-if\", true), unref(rendered) ? (openBlock(), createElementBlock(\"div\", {\n                key: 1,\n                id: unref(bodyId),\n                class: normalizeClass([unref(ns).e(\"body\"), _ctx.bodyClass])\n              }, [renderSlot(_ctx.$slots, \"default\")], 10, [\"id\"])) : createCommentVNode(\"v-if\", true), _ctx.$slots.footer ? (openBlock(), createElementBlock(\"div\", {\n                key: 2,\n                class: normalizeClass([unref(ns).e(\"footer\"), _ctx.footerClass])\n              }, [renderSlot(_ctx.$slots, \"footer\")], 2)) : createCommentVNode(\"v-if\", true)], 16, [\"aria-label\", \"aria-labelledby\", \"aria-describedby\", \"onClick\"])]),\n              _: 3\n            }, 8, [\"trapped\", \"focus-trap-el\", \"focus-start-el\", \"onFocusAfterTrapped\", \"onFocusAfterReleased\", \"onFocusoutPrevented\", \"onReleaseRequested\"])]),\n            _: 3\n          }, 8, [\"mask\", \"overlay-class\", \"z-index\", \"onClick\"]), [[vShow, unref(visible)]])]),\n          _: 3\n        }, 8, [\"name\", \"onAfterEnter\", \"onAfterLeave\", \"onBeforeLeave\"])]),\n        _: 3\n      }, 8, [\"to\", \"disabled\"]);\n    };\n  }\n});\nvar Drawer = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"drawer.vue\"]]);\nexport { Drawer as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}