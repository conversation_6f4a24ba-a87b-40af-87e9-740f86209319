{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { ref, getCurrentInstance, watch, nextTick } from 'vue';\nimport { NODE_CHECK, NODE_CHECK_CHANGE, SetOperationEnum } from '../virtual-tree.mjs';\nfunction useCheck(props, tree) {\n  const checkedKeys = ref(/* @__PURE__ */new Set());\n  const indeterminateKeys = ref(/* @__PURE__ */new Set());\n  const {\n    emit\n  } = getCurrentInstance();\n  watch([() => tree.value, () => props.defaultCheckedKeys], () => {\n    return nextTick(() => {\n      _setCheckedKeys(props.defaultCheckedKeys);\n    });\n  }, {\n    immediate: true\n  });\n  const updateCheckedKeys = () => {\n    if (!tree.value || !props.showCheckbox || props.checkStrictly) {\n      return;\n    }\n    const {\n      levelTreeNodeMap,\n      maxLevel\n    } = tree.value;\n    const checkedKeySet = checkedKeys.value;\n    const indeterminateKeySet = /* @__PURE__ */new Set();\n    for (let level = maxLevel - 1; level >= 1; --level) {\n      const nodes = levelTreeNodeMap.get(level);\n      if (!nodes) continue;\n      nodes.forEach(node => {\n        const children = node.children;\n        if (children) {\n          let allChecked = true;\n          let hasChecked = false;\n          for (const childNode of children) {\n            const key = childNode.key;\n            if (checkedKeySet.has(key)) {\n              hasChecked = true;\n            } else if (indeterminateKeySet.has(key)) {\n              allChecked = false;\n              hasChecked = true;\n              break;\n            } else {\n              allChecked = false;\n            }\n          }\n          if (allChecked) {\n            checkedKeySet.add(node.key);\n          } else if (hasChecked) {\n            indeterminateKeySet.add(node.key);\n            checkedKeySet.delete(node.key);\n          } else {\n            checkedKeySet.delete(node.key);\n            indeterminateKeySet.delete(node.key);\n          }\n        }\n      });\n    }\n    indeterminateKeys.value = indeterminateKeySet;\n  };\n  const isChecked = node => checkedKeys.value.has(node.key);\n  const isIndeterminate = node => indeterminateKeys.value.has(node.key);\n  const toggleCheckbox = (node, isChecked2, nodeClick = true, immediateUpdate = true) => {\n    const checkedKeySet = checkedKeys.value;\n    const toggle = (node2, checked) => {\n      checkedKeySet[checked ? SetOperationEnum.ADD : SetOperationEnum.DELETE](node2.key);\n      const children = node2.children;\n      if (!props.checkStrictly && children) {\n        children.forEach(childNode => {\n          if (!childNode.disabled) {\n            toggle(childNode, checked);\n          }\n        });\n      }\n    };\n    toggle(node, isChecked2);\n    if (immediateUpdate) {\n      updateCheckedKeys();\n    }\n    if (nodeClick) {\n      afterNodeCheck(node, isChecked2);\n    }\n  };\n  const afterNodeCheck = (node, checked) => {\n    const {\n      checkedNodes,\n      checkedKeys: checkedKeys2\n    } = getChecked();\n    const {\n      halfCheckedNodes,\n      halfCheckedKeys\n    } = getHalfChecked();\n    emit(NODE_CHECK, node.data, {\n      checkedKeys: checkedKeys2,\n      checkedNodes,\n      halfCheckedKeys,\n      halfCheckedNodes\n    });\n    emit(NODE_CHECK_CHANGE, node.data, checked);\n  };\n  function getCheckedKeys(leafOnly = false) {\n    return getChecked(leafOnly).checkedKeys;\n  }\n  function getCheckedNodes(leafOnly = false) {\n    return getChecked(leafOnly).checkedNodes;\n  }\n  function getHalfCheckedKeys() {\n    return getHalfChecked().halfCheckedKeys;\n  }\n  function getHalfCheckedNodes() {\n    return getHalfChecked().halfCheckedNodes;\n  }\n  function getChecked(leafOnly = false) {\n    const checkedNodes = [];\n    const keys = [];\n    if ((tree == null ? void 0 : tree.value) && props.showCheckbox) {\n      const {\n        treeNodeMap\n      } = tree.value;\n      checkedKeys.value.forEach(key => {\n        const node = treeNodeMap.get(key);\n        if (node && (!leafOnly || leafOnly && node.isLeaf)) {\n          keys.push(key);\n          checkedNodes.push(node.data);\n        }\n      });\n    }\n    return {\n      checkedKeys: keys,\n      checkedNodes\n    };\n  }\n  function getHalfChecked() {\n    const halfCheckedNodes = [];\n    const halfCheckedKeys = [];\n    if ((tree == null ? void 0 : tree.value) && props.showCheckbox) {\n      const {\n        treeNodeMap\n      } = tree.value;\n      indeterminateKeys.value.forEach(key => {\n        const node = treeNodeMap.get(key);\n        if (node) {\n          halfCheckedKeys.push(key);\n          halfCheckedNodes.push(node.data);\n        }\n      });\n    }\n    return {\n      halfCheckedNodes,\n      halfCheckedKeys\n    };\n  }\n  function setCheckedKeys(keys) {\n    checkedKeys.value.clear();\n    indeterminateKeys.value.clear();\n    nextTick(() => {\n      _setCheckedKeys(keys);\n    });\n  }\n  function setChecked(key, isChecked2) {\n    if ((tree == null ? void 0 : tree.value) && props.showCheckbox) {\n      const node = tree.value.treeNodeMap.get(key);\n      if (node) {\n        toggleCheckbox(node, isChecked2, false);\n      }\n    }\n  }\n  function _setCheckedKeys(keys) {\n    if (tree == null ? void 0 : tree.value) {\n      const {\n        treeNodeMap\n      } = tree.value;\n      if (props.showCheckbox && treeNodeMap && (keys == null ? void 0 : keys.length) > 0) {\n        for (const key of keys) {\n          const node = treeNodeMap.get(key);\n          if (node && !isChecked(node)) {\n            toggleCheckbox(node, true, false, false);\n          }\n        }\n        updateCheckedKeys();\n      }\n    }\n  }\n  return {\n    updateCheckedKeys,\n    toggleCheckbox,\n    isChecked,\n    isIndeterminate,\n    getCheckedKeys,\n    getCheckedNodes,\n    getHalfCheckedKeys,\n    getHalfCheckedNodes,\n    setChecked,\n    setCheckedKeys\n  };\n}\nexport { useCheck };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}