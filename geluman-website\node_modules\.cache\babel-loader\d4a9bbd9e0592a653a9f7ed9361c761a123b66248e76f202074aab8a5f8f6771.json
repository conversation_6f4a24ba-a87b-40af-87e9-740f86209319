{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, shallowRef, computed, onBeforeUnmount, provide, toRef, openBlock, createElementBlock, unref, createBlock, createSlots, withCtx, createVNode, mergeProps, renderSlot, createCommentVNode } from 'vue';\nimport { uploadContextKey } from './constants.mjs';\nimport UploadList from './upload-list2.mjs';\nimport UploadContent from './upload-content2.mjs';\nimport { useHandlers } from './use-handlers.mjs';\nimport { uploadProps } from './upload.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nconst __default__ = defineComponent({\n  name: \"ElUpload\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: uploadProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const disabled = useFormDisabled();\n    const uploadRef = shallowRef();\n    const {\n      abort,\n      submit,\n      clearFiles,\n      uploadFiles,\n      handleStart,\n      handleError,\n      handleRemove,\n      handleSuccess,\n      handleProgress,\n      revokeFileObjectURL\n    } = useHandlers(props, uploadRef);\n    const isPictureCard = computed(() => props.listType === \"picture-card\");\n    const uploadContentProps = computed(() => ({\n      ...props,\n      fileList: uploadFiles.value,\n      onStart: handleStart,\n      onProgress: handleProgress,\n      onSuccess: handleSuccess,\n      onError: handleError,\n      onRemove: handleRemove\n    }));\n    onBeforeUnmount(() => {\n      uploadFiles.value.forEach(revokeFileObjectURL);\n    });\n    provide(uploadContextKey, {\n      accept: toRef(props, \"accept\")\n    });\n    expose({\n      abort,\n      submit,\n      clearFiles,\n      handleStart,\n      handleRemove\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", null, [unref(isPictureCard) && _ctx.showFileList ? (openBlock(), createBlock(UploadList, {\n        key: 0,\n        disabled: unref(disabled),\n        \"list-type\": _ctx.listType,\n        files: unref(uploadFiles),\n        crossorigin: _ctx.crossorigin,\n        \"handle-preview\": _ctx.onPreview,\n        onRemove: unref(handleRemove)\n      }, createSlots({\n        append: withCtx(() => [createVNode(UploadContent, mergeProps({\n          ref_key: \"uploadRef\",\n          ref: uploadRef\n        }, unref(uploadContentProps)), {\n          default: withCtx(() => [_ctx.$slots.trigger ? renderSlot(_ctx.$slots, \"trigger\", {\n            key: 0\n          }) : createCommentVNode(\"v-if\", true), !_ctx.$slots.trigger && _ctx.$slots.default ? renderSlot(_ctx.$slots, \"default\", {\n            key: 1\n          }) : createCommentVNode(\"v-if\", true)]),\n          _: 3\n        }, 16)]),\n        _: 2\n      }, [_ctx.$slots.file ? {\n        name: \"default\",\n        fn: withCtx(({\n          file,\n          index\n        }) => [renderSlot(_ctx.$slots, \"file\", {\n          file,\n          index\n        })])\n      } : void 0]), 1032, [\"disabled\", \"list-type\", \"files\", \"crossorigin\", \"handle-preview\", \"onRemove\"])) : createCommentVNode(\"v-if\", true), !unref(isPictureCard) || unref(isPictureCard) && !_ctx.showFileList ? (openBlock(), createBlock(UploadContent, mergeProps({\n        key: 1,\n        ref_key: \"uploadRef\",\n        ref: uploadRef\n      }, unref(uploadContentProps)), {\n        default: withCtx(() => [_ctx.$slots.trigger ? renderSlot(_ctx.$slots, \"trigger\", {\n          key: 0\n        }) : createCommentVNode(\"v-if\", true), !_ctx.$slots.trigger && _ctx.$slots.default ? renderSlot(_ctx.$slots, \"default\", {\n          key: 1\n        }) : createCommentVNode(\"v-if\", true)]),\n        _: 3\n      }, 16)) : createCommentVNode(\"v-if\", true), _ctx.$slots.trigger ? renderSlot(_ctx.$slots, \"default\", {\n        key: 2\n      }) : createCommentVNode(\"v-if\", true), renderSlot(_ctx.$slots, \"tip\"), !unref(isPictureCard) && _ctx.showFileList ? (openBlock(), createBlock(UploadList, {\n        key: 3,\n        disabled: unref(disabled),\n        \"list-type\": _ctx.listType,\n        files: unref(uploadFiles),\n        crossorigin: _ctx.crossorigin,\n        \"handle-preview\": _ctx.onPreview,\n        onRemove: unref(handleRemove)\n      }, createSlots({\n        _: 2\n      }, [_ctx.$slots.file ? {\n        name: \"default\",\n        fn: withCtx(({\n          file,\n          index\n        }) => [renderSlot(_ctx.$slots, \"file\", {\n          file,\n          index\n        })])\n      } : void 0]), 1032, [\"disabled\", \"list-type\", \"files\", \"crossorigin\", \"handle-preview\", \"onRemove\"])) : createCommentVNode(\"v-if\", true)]);\n    };\n  }\n});\nvar Upload = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"upload.vue\"]]);\nexport { Upload as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}