{"ast": null, "code": "import ColorPicker from './src/color-picker2.mjs';\nexport { colorPickerContextKey, colorPickerEmits, colorPickerProps } from './src/color-picker.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElColorPicker = withInstall(ColorPicker);\nexport { ElColorPicker, ElColorPicker as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}