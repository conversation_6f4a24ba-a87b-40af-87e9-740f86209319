{"ast": null, "code": "import { shallowRef, ref, computed, nextTick } from 'vue';\nimport { useFormItem } from '../../../form/src/hooks/use-form-item.mjs';\nimport { UPDATE_MODEL_EVENT, INPUT_EVENT, CHANGE_EVENT } from '../../../../constants/event.mjs';\nconst useSlide = (props, initData, emit) => {\n  const {\n    form: elForm,\n    formItem: elFormItem\n  } = useFormItem();\n  const slider = shallowRef();\n  const firstButton = ref();\n  const secondButton = ref();\n  const buttonRefs = {\n    firstButton,\n    secondButton\n  };\n  const sliderDisabled = computed(() => {\n    return props.disabled || (elForm == null ? void 0 : elForm.disabled) || false;\n  });\n  const minValue = computed(() => {\n    return Math.min(initData.firstValue, initData.secondValue);\n  });\n  const maxValue = computed(() => {\n    return Math.max(initData.firstValue, initData.secondValue);\n  });\n  const barSize = computed(() => {\n    return props.range ? `${100 * (maxValue.value - minValue.value) / (props.max - props.min)}%` : `${100 * (initData.firstValue - props.min) / (props.max - props.min)}%`;\n  });\n  const barStart = computed(() => {\n    return props.range ? `${100 * (minValue.value - props.min) / (props.max - props.min)}%` : \"0%\";\n  });\n  const runwayStyle = computed(() => {\n    return props.vertical ? {\n      height: props.height\n    } : {};\n  });\n  const barStyle = computed(() => {\n    return props.vertical ? {\n      height: barSize.value,\n      bottom: barStart.value\n    } : {\n      width: barSize.value,\n      left: barStart.value\n    };\n  });\n  const resetSize = () => {\n    if (slider.value) {\n      initData.sliderSize = slider.value[`client${props.vertical ? \"Height\" : \"Width\"}`];\n    }\n  };\n  const getButtonRefByPercent = percent => {\n    const targetValue = props.min + percent * (props.max - props.min) / 100;\n    if (!props.range) {\n      return firstButton;\n    }\n    let buttonRefName;\n    if (Math.abs(minValue.value - targetValue) < Math.abs(maxValue.value - targetValue)) {\n      buttonRefName = initData.firstValue < initData.secondValue ? \"firstButton\" : \"secondButton\";\n    } else {\n      buttonRefName = initData.firstValue > initData.secondValue ? \"firstButton\" : \"secondButton\";\n    }\n    return buttonRefs[buttonRefName];\n  };\n  const setPosition = percent => {\n    const buttonRef = getButtonRefByPercent(percent);\n    buttonRef.value.setPosition(percent);\n    return buttonRef;\n  };\n  const setFirstValue = firstValue => {\n    initData.firstValue = firstValue != null ? firstValue : props.min;\n    _emit(props.range ? [minValue.value, maxValue.value] : firstValue != null ? firstValue : props.min);\n  };\n  const setSecondValue = secondValue => {\n    initData.secondValue = secondValue;\n    if (props.range) {\n      _emit([minValue.value, maxValue.value]);\n    }\n  };\n  const _emit = val => {\n    emit(UPDATE_MODEL_EVENT, val);\n    emit(INPUT_EVENT, val);\n  };\n  const emitChange = async () => {\n    await nextTick();\n    emit(CHANGE_EVENT, props.range ? [minValue.value, maxValue.value] : props.modelValue);\n  };\n  const handleSliderPointerEvent = event => {\n    var _a, _b, _c, _d, _e, _f;\n    if (sliderDisabled.value || initData.dragging) return;\n    resetSize();\n    let newPercent = 0;\n    if (props.vertical) {\n      const clientY = (_c = (_b = (_a = event.touches) == null ? void 0 : _a.item(0)) == null ? void 0 : _b.clientY) != null ? _c : event.clientY;\n      const sliderOffsetBottom = slider.value.getBoundingClientRect().bottom;\n      newPercent = (sliderOffsetBottom - clientY) / initData.sliderSize * 100;\n    } else {\n      const clientX = (_f = (_e = (_d = event.touches) == null ? void 0 : _d.item(0)) == null ? void 0 : _e.clientX) != null ? _f : event.clientX;\n      const sliderOffsetLeft = slider.value.getBoundingClientRect().left;\n      newPercent = (clientX - sliderOffsetLeft) / initData.sliderSize * 100;\n    }\n    if (newPercent < 0 || newPercent > 100) return;\n    return setPosition(newPercent);\n  };\n  const onSliderWrapperPrevent = event => {\n    var _a, _b;\n    if (((_a = buttonRefs[\"firstButton\"].value) == null ? void 0 : _a.dragging) || ((_b = buttonRefs[\"secondButton\"].value) == null ? void 0 : _b.dragging)) {\n      event.preventDefault();\n    }\n  };\n  const onSliderDown = async event => {\n    const buttonRef = handleSliderPointerEvent(event);\n    if (buttonRef) {\n      await nextTick();\n      buttonRef.value.onButtonDown(event);\n    }\n  };\n  const onSliderClick = event => {\n    const buttonRef = handleSliderPointerEvent(event);\n    if (buttonRef) {\n      emitChange();\n    }\n  };\n  const onSliderMarkerDown = position => {\n    if (sliderDisabled.value || initData.dragging) return;\n    const buttonRef = setPosition(position);\n    if (buttonRef) {\n      emitChange();\n    }\n  };\n  return {\n    elFormItem,\n    slider,\n    firstButton,\n    secondButton,\n    sliderDisabled,\n    minValue,\n    maxValue,\n    runwayStyle,\n    barStyle,\n    resetSize,\n    setPosition,\n    emitChange,\n    onSliderWrapperPrevent,\n    onSliderClick,\n    onSliderDown,\n    onSliderMarkerDown,\n    setFirstValue,\n    setSecondValue\n  };\n};\nexport { useSlide };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}