{"ast": null, "code": "import baseDelay from './_baseDelay.js';\nimport baseRest from './_baseRest.js';\n\n/**\n * Defers invoking the `func` until the current call stack has cleared. Any\n * additional arguments are provided to `func` when it's invoked.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to defer.\n * @param {...*} [args] The arguments to invoke `func` with.\n * @returns {number} Returns the timer id.\n * @example\n *\n * _.defer(function(text) {\n *   console.log(text);\n * }, 'deferred');\n * // => Logs 'deferred' after one millisecond.\n */\nvar defer = baseRest(function (func, args) {\n  return baseDelay(func, 1, args);\n});\nexport default defer;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}