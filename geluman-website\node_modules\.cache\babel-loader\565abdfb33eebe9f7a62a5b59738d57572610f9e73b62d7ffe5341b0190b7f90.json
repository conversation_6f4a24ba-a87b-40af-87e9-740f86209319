{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { onMounted, onBeforeUnmount } from 'vue';\nimport { isClient } from '@vueuse/core';\nimport { EVENT_CODE } from '../../constants/aria.mjs';\nlet registeredEscapeHandlers = [];\nconst cachedHandler = event => {\n  if (event.code === EVENT_CODE.esc) {\n    registeredEscapeHandlers.forEach(registeredHandler => registeredHandler(event));\n  }\n};\nconst useEscapeKeydown = handler => {\n  onMounted(() => {\n    if (registeredEscapeHandlers.length === 0) {\n      document.addEventListener(\"keydown\", cachedHandler);\n    }\n    if (isClient) registeredEscapeHandlers.push(handler);\n  });\n  onBeforeUnmount(() => {\n    registeredEscapeHandlers = registeredEscapeHandlers.filter(registeredHandler => registeredHandler !== handler);\n    if (registeredEscapeHandlers.length === 0) {\n      if (isClient) document.removeEventListener(\"keydown\", cachedHandler);\n    }\n  });\n};\nexport { useEscapeKeydown };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}