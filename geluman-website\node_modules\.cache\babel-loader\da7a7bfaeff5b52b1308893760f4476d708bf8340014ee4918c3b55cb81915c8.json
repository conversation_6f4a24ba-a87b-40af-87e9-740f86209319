{"ast": null, "code": "import { tryOnScopeDispose } from '@vueuse/core';\nfunction useTimeout() {\n  let timeoutHandle;\n  const registerTimeout = (fn, delay) => {\n    cancelTimeout();\n    timeoutHandle = window.setTimeout(fn, delay);\n  };\n  const cancelTimeout = () => window.clearTimeout(timeoutHandle);\n  tryOnScopeDispose(() => cancelTimeout());\n  return {\n    registerTimeout,\n    cancelTimeout\n  };\n}\nexport { useTimeout };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}