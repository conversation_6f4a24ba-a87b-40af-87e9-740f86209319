{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst popperTriggerProps = buildProps({\n  virtualRef: {\n    type: definePropType(Object)\n  },\n  virtualTriggering: Boolean,\n  onMouseenter: {\n    type: definePropType(Function)\n  },\n  onMouseleave: {\n    type: definePropType(Function)\n  },\n  onClick: {\n    type: definePropType(Function)\n  },\n  onKeydown: {\n    type: definePropType(Function)\n  },\n  onFocus: {\n    type: definePropType(Function)\n  },\n  onBlur: {\n    type: definePropType(Function)\n  },\n  onContextmenu: {\n    type: definePropType(Function)\n  },\n  id: String,\n  open: Boolean\n});\nconst usePopperTriggerProps = popperTriggerProps;\nexport { popperTriggerProps, usePopperTriggerProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}