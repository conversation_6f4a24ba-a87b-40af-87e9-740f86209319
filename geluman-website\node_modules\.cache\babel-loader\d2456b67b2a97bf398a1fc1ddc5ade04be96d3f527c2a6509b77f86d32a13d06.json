{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, computed, watch, provide, reactive, toRefs, openBlock, createElementBlock, normalizeClass, unref, renderSlot } from 'vue';\nimport { formContextKey } from './constants.mjs';\nimport { formProps, formEmits } from './form.mjs';\nimport { useFormLabelWidth, filterFields } from './utils.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useFormSize } from './hooks/use-form-common-props.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { isFunction } from '@vue/shared';\nconst COMPONENT_NAME = \"ElForm\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: formProps,\n  emits: formEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const fields = [];\n    const formSize = useFormSize();\n    const ns = useNamespace(\"form\");\n    const formClasses = computed(() => {\n      const {\n        labelPosition,\n        inline\n      } = props;\n      return [ns.b(), ns.m(formSize.value || \"default\"), {\n        [ns.m(`label-${labelPosition}`)]: labelPosition,\n        [ns.m(\"inline\")]: inline\n      }];\n    });\n    const getField = prop => {\n      return fields.find(field => field.prop === prop);\n    };\n    const addField = field => {\n      fields.push(field);\n    };\n    const removeField = field => {\n      if (field.prop) {\n        fields.splice(fields.indexOf(field), 1);\n      }\n    };\n    const resetFields = (properties = []) => {\n      if (!props.model) {\n        debugWarn(COMPONENT_NAME, \"model is required for resetFields to work.\");\n        return;\n      }\n      filterFields(fields, properties).forEach(field => field.resetField());\n    };\n    const clearValidate = (props2 = []) => {\n      filterFields(fields, props2).forEach(field => field.clearValidate());\n    };\n    const isValidatable = computed(() => {\n      const hasModel = !!props.model;\n      if (!hasModel) {\n        debugWarn(COMPONENT_NAME, \"model is required for validate to work.\");\n      }\n      return hasModel;\n    });\n    const obtainValidateFields = props2 => {\n      if (fields.length === 0) return [];\n      const filteredFields = filterFields(fields, props2);\n      if (!filteredFields.length) {\n        debugWarn(COMPONENT_NAME, \"please pass correct props!\");\n        return [];\n      }\n      return filteredFields;\n    };\n    const validate = async callback => validateField(void 0, callback);\n    const doValidateField = async (props2 = []) => {\n      if (!isValidatable.value) return false;\n      const fields2 = obtainValidateFields(props2);\n      if (fields2.length === 0) return true;\n      let validationErrors = {};\n      for (const field of fields2) {\n        try {\n          await field.validate(\"\");\n          if (field.validateState === \"error\") field.resetField();\n        } catch (fields3) {\n          validationErrors = {\n            ...validationErrors,\n            ...fields3\n          };\n        }\n      }\n      if (Object.keys(validationErrors).length === 0) return true;\n      return Promise.reject(validationErrors);\n    };\n    const validateField = async (modelProps = [], callback) => {\n      const shouldThrow = !isFunction(callback);\n      try {\n        const result = await doValidateField(modelProps);\n        if (result === true) {\n          await (callback == null ? void 0 : callback(result));\n        }\n        return result;\n      } catch (e) {\n        if (e instanceof Error) throw e;\n        const invalidFields = e;\n        if (props.scrollToError) {\n          scrollToField(Object.keys(invalidFields)[0]);\n        }\n        await (callback == null ? void 0 : callback(false, invalidFields));\n        return shouldThrow && Promise.reject(invalidFields);\n      }\n    };\n    const scrollToField = prop => {\n      var _a;\n      const field = filterFields(fields, prop)[0];\n      if (field) {\n        (_a = field.$el) == null ? void 0 : _a.scrollIntoView(props.scrollIntoViewOptions);\n      }\n    };\n    watch(() => props.rules, () => {\n      if (props.validateOnRuleChange) {\n        validate().catch(err => debugWarn(err));\n      }\n    }, {\n      deep: true,\n      flush: \"post\"\n    });\n    provide(formContextKey, reactive({\n      ...toRefs(props),\n      emit,\n      resetFields,\n      clearValidate,\n      validateField,\n      getField,\n      addField,\n      removeField,\n      ...useFormLabelWidth()\n    }));\n    expose({\n      validate,\n      validateField,\n      resetFields,\n      clearValidate,\n      scrollToField,\n      fields\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"form\", {\n        class: normalizeClass(unref(formClasses))\n      }, [renderSlot(_ctx.$slots, \"default\")], 2);\n    };\n  }\n});\nvar Form = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"form.vue\"]]);\nexport { Form as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}