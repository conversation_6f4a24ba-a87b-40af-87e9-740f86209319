{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { getCurrentInstance, shallowRef, ref, computed, unref } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport { FixedDir } from '../constants.mjs';\nimport { isNumber } from '../../../../utils/types.mjs';\nconst useRow = (props, {\n  mainTableRef,\n  leftTableRef,\n  rightTableRef,\n  tableInstance,\n  ns,\n  isScrolling\n}) => {\n  const vm = getCurrentInstance();\n  const {\n    emit\n  } = vm;\n  const isResetting = shallowRef(false);\n  const expandedRowKeys = ref(props.defaultExpandedRowKeys || []);\n  const lastRenderedRowIndex = ref(-1);\n  const resetIndex = shallowRef(null);\n  const rowHeights = ref({});\n  const pendingRowHeights = ref({});\n  const leftTableHeights = shallowRef({});\n  const mainTableHeights = shallowRef({});\n  const rightTableHeights = shallowRef({});\n  const isDynamic = computed(() => isNumber(props.estimatedRowHeight));\n  function onRowsRendered(params) {\n    var _a;\n    (_a = props.onRowsRendered) == null ? void 0 : _a.call(props, params);\n    if (params.rowCacheEnd > unref(lastRenderedRowIndex)) {\n      lastRenderedRowIndex.value = params.rowCacheEnd;\n    }\n  }\n  function onRowHovered({\n    hovered,\n    rowKey\n  }) {\n    if (isScrolling.value) {\n      return;\n    }\n    const tableRoot = tableInstance.vnode.el;\n    const rows = tableRoot.querySelectorAll(`[rowkey=\"${String(rowKey)}\"]`);\n    rows.forEach(row => {\n      if (hovered) {\n        row.classList.add(ns.is(\"hovered\"));\n      } else {\n        row.classList.remove(ns.is(\"hovered\"));\n      }\n    });\n  }\n  function onRowExpanded({\n    expanded,\n    rowData,\n    rowIndex,\n    rowKey\n  }) {\n    var _a, _b;\n    const _expandedRowKeys = [...unref(expandedRowKeys)];\n    const currentKeyIndex = _expandedRowKeys.indexOf(rowKey);\n    if (expanded) {\n      if (currentKeyIndex === -1) _expandedRowKeys.push(rowKey);\n    } else {\n      if (currentKeyIndex > -1) _expandedRowKeys.splice(currentKeyIndex, 1);\n    }\n    expandedRowKeys.value = _expandedRowKeys;\n    emit(\"update:expandedRowKeys\", _expandedRowKeys);\n    (_a = props.onRowExpand) == null ? void 0 : _a.call(props, {\n      expanded,\n      rowData,\n      rowIndex,\n      rowKey\n    });\n    (_b = props.onExpandedRowsChange) == null ? void 0 : _b.call(props, _expandedRowKeys);\n  }\n  const flushingRowHeights = debounce(() => {\n    var _a, _b, _c, _d;\n    isResetting.value = true;\n    rowHeights.value = {\n      ...unref(rowHeights),\n      ...unref(pendingRowHeights)\n    };\n    resetAfterIndex(unref(resetIndex), false);\n    pendingRowHeights.value = {};\n    resetIndex.value = null;\n    (_a = mainTableRef.value) == null ? void 0 : _a.forceUpdate();\n    (_b = leftTableRef.value) == null ? void 0 : _b.forceUpdate();\n    (_c = rightTableRef.value) == null ? void 0 : _c.forceUpdate();\n    (_d = vm.proxy) == null ? void 0 : _d.$forceUpdate();\n    isResetting.value = false;\n  }, 0);\n  function resetAfterIndex(index, forceUpdate = false) {\n    if (!unref(isDynamic)) return;\n    [mainTableRef, leftTableRef, rightTableRef].forEach(tableRef => {\n      const table = unref(tableRef);\n      if (table) table.resetAfterRowIndex(index, forceUpdate);\n    });\n  }\n  function resetHeights(rowKey, height, rowIdx) {\n    const resetIdx = unref(resetIndex);\n    if (resetIdx === null) {\n      resetIndex.value = rowIdx;\n    } else {\n      if (resetIdx > rowIdx) {\n        resetIndex.value = rowIdx;\n      }\n    }\n    pendingRowHeights.value[rowKey] = height;\n  }\n  function onRowHeightChange({\n    rowKey,\n    height,\n    rowIndex\n  }, fixedDir) {\n    if (!fixedDir) {\n      mainTableHeights.value[rowKey] = height;\n    } else {\n      if (fixedDir === FixedDir.RIGHT) {\n        rightTableHeights.value[rowKey] = height;\n      } else {\n        leftTableHeights.value[rowKey] = height;\n      }\n    }\n    const maximumHeight = Math.max(...[leftTableHeights, rightTableHeights, mainTableHeights].map(records => records.value[rowKey] || 0));\n    if (unref(rowHeights)[rowKey] !== maximumHeight) {\n      resetHeights(rowKey, maximumHeight, rowIndex);\n      flushingRowHeights();\n    }\n  }\n  return {\n    expandedRowKeys,\n    lastRenderedRowIndex,\n    isDynamic,\n    isResetting,\n    rowHeights,\n    resetAfterIndex,\n    onRowExpanded,\n    onRowHovered,\n    onRowsRendered,\n    onRowHeightChange\n  };\n};\nexport { useRow };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}