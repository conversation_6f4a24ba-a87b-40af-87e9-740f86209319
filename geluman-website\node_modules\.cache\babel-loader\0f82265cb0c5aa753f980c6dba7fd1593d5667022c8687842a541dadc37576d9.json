{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, ref, provide, watch, unref, onMounted, onBeforeUnmount, nextTick, renderSlot } from 'vue';\nimport { isNil } from 'lodash-unified';\nimport { useFocusReason, tryFocus, createFocusOutPreventedEvent, getEdges, focusableStack, focusFirstDescendant, obtainAllFocusableElements, isFocusCausedByUserEvent } from './utils.mjs';\nimport { ON_TRAP_FOCUS_EVT, ON_RELEASE_FOCUS_EVT, FOCUS_TRAP_INJECTION_KEY, FOCUS_AFTER_TRAPPED, FOCUS_AFTER_TRAPPED_OPTS, FOCUS_AFTER_RELEASED } from './tokens.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useEscapeKeydown } from '../../../hooks/use-escape-keydown/index.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { isString } from '@vue/shared';\nconst _sfc_main = defineComponent({\n  name: \"ElFocusTrap\",\n  inheritAttrs: false,\n  props: {\n    loop: Boolean,\n    trapped: Boolean,\n    focusTrapEl: Object,\n    focusStartEl: {\n      type: [Object, String],\n      default: \"first\"\n    }\n  },\n  emits: [ON_TRAP_FOCUS_EVT, ON_RELEASE_FOCUS_EVT, \"focusin\", \"focusout\", \"focusout-prevented\", \"release-requested\"],\n  setup(props, {\n    emit\n  }) {\n    const forwardRef = ref();\n    let lastFocusBeforeTrapped;\n    let lastFocusAfterTrapped;\n    const {\n      focusReason\n    } = useFocusReason();\n    useEscapeKeydown(event => {\n      if (props.trapped && !focusLayer.paused) {\n        emit(\"release-requested\", event);\n      }\n    });\n    const focusLayer = {\n      paused: false,\n      pause() {\n        this.paused = true;\n      },\n      resume() {\n        this.paused = false;\n      }\n    };\n    const onKeydown = e => {\n      if (!props.loop && !props.trapped) return;\n      if (focusLayer.paused) return;\n      const {\n        code,\n        altKey,\n        ctrlKey,\n        metaKey,\n        currentTarget,\n        shiftKey\n      } = e;\n      const {\n        loop\n      } = props;\n      const isTabbing = code === EVENT_CODE.tab && !altKey && !ctrlKey && !metaKey;\n      const currentFocusingEl = document.activeElement;\n      if (isTabbing && currentFocusingEl) {\n        const container = currentTarget;\n        const [first, last] = getEdges(container);\n        const isTabbable = first && last;\n        if (!isTabbable) {\n          if (currentFocusingEl === container) {\n            const focusoutPreventedEvent = createFocusOutPreventedEvent({\n              focusReason: focusReason.value\n            });\n            emit(\"focusout-prevented\", focusoutPreventedEvent);\n            if (!focusoutPreventedEvent.defaultPrevented) {\n              e.preventDefault();\n            }\n          }\n        } else {\n          if (!shiftKey && currentFocusingEl === last) {\n            const focusoutPreventedEvent = createFocusOutPreventedEvent({\n              focusReason: focusReason.value\n            });\n            emit(\"focusout-prevented\", focusoutPreventedEvent);\n            if (!focusoutPreventedEvent.defaultPrevented) {\n              e.preventDefault();\n              if (loop) tryFocus(first, true);\n            }\n          } else if (shiftKey && [first, container].includes(currentFocusingEl)) {\n            const focusoutPreventedEvent = createFocusOutPreventedEvent({\n              focusReason: focusReason.value\n            });\n            emit(\"focusout-prevented\", focusoutPreventedEvent);\n            if (!focusoutPreventedEvent.defaultPrevented) {\n              e.preventDefault();\n              if (loop) tryFocus(last, true);\n            }\n          }\n        }\n      }\n    };\n    provide(FOCUS_TRAP_INJECTION_KEY, {\n      focusTrapRef: forwardRef,\n      onKeydown\n    });\n    watch(() => props.focusTrapEl, focusTrapEl => {\n      if (focusTrapEl) {\n        forwardRef.value = focusTrapEl;\n      }\n    }, {\n      immediate: true\n    });\n    watch([forwardRef], ([forwardRef2], [oldForwardRef]) => {\n      if (forwardRef2) {\n        forwardRef2.addEventListener(\"keydown\", onKeydown);\n        forwardRef2.addEventListener(\"focusin\", onFocusIn);\n        forwardRef2.addEventListener(\"focusout\", onFocusOut);\n      }\n      if (oldForwardRef) {\n        oldForwardRef.removeEventListener(\"keydown\", onKeydown);\n        oldForwardRef.removeEventListener(\"focusin\", onFocusIn);\n        oldForwardRef.removeEventListener(\"focusout\", onFocusOut);\n      }\n    });\n    const trapOnFocus = e => {\n      emit(ON_TRAP_FOCUS_EVT, e);\n    };\n    const releaseOnFocus = e => emit(ON_RELEASE_FOCUS_EVT, e);\n    const onFocusIn = e => {\n      const trapContainer = unref(forwardRef);\n      if (!trapContainer) return;\n      const target = e.target;\n      const relatedTarget = e.relatedTarget;\n      const isFocusedInTrap = target && trapContainer.contains(target);\n      if (!props.trapped) {\n        const isPrevFocusedInTrap = relatedTarget && trapContainer.contains(relatedTarget);\n        if (!isPrevFocusedInTrap) {\n          lastFocusBeforeTrapped = relatedTarget;\n        }\n      }\n      if (isFocusedInTrap) emit(\"focusin\", e);\n      if (focusLayer.paused) return;\n      if (props.trapped) {\n        if (isFocusedInTrap) {\n          lastFocusAfterTrapped = target;\n        } else {\n          tryFocus(lastFocusAfterTrapped, true);\n        }\n      }\n    };\n    const onFocusOut = e => {\n      const trapContainer = unref(forwardRef);\n      if (focusLayer.paused || !trapContainer) return;\n      if (props.trapped) {\n        const relatedTarget = e.relatedTarget;\n        if (!isNil(relatedTarget) && !trapContainer.contains(relatedTarget)) {\n          setTimeout(() => {\n            if (!focusLayer.paused && props.trapped) {\n              const focusoutPreventedEvent = createFocusOutPreventedEvent({\n                focusReason: focusReason.value\n              });\n              emit(\"focusout-prevented\", focusoutPreventedEvent);\n              if (!focusoutPreventedEvent.defaultPrevented) {\n                tryFocus(lastFocusAfterTrapped, true);\n              }\n            }\n          }, 0);\n        }\n      } else {\n        const target = e.target;\n        const isFocusedInTrap = target && trapContainer.contains(target);\n        if (!isFocusedInTrap) emit(\"focusout\", e);\n      }\n    };\n    async function startTrap() {\n      await nextTick();\n      const trapContainer = unref(forwardRef);\n      if (trapContainer) {\n        focusableStack.push(focusLayer);\n        const prevFocusedElement = trapContainer.contains(document.activeElement) ? lastFocusBeforeTrapped : document.activeElement;\n        lastFocusBeforeTrapped = prevFocusedElement;\n        const isPrevFocusContained = trapContainer.contains(prevFocusedElement);\n        if (!isPrevFocusContained) {\n          const focusEvent = new Event(FOCUS_AFTER_TRAPPED, FOCUS_AFTER_TRAPPED_OPTS);\n          trapContainer.addEventListener(FOCUS_AFTER_TRAPPED, trapOnFocus);\n          trapContainer.dispatchEvent(focusEvent);\n          if (!focusEvent.defaultPrevented) {\n            nextTick(() => {\n              let focusStartEl = props.focusStartEl;\n              if (!isString(focusStartEl)) {\n                tryFocus(focusStartEl);\n                if (document.activeElement !== focusStartEl) {\n                  focusStartEl = \"first\";\n                }\n              }\n              if (focusStartEl === \"first\") {\n                focusFirstDescendant(obtainAllFocusableElements(trapContainer), true);\n              }\n              if (document.activeElement === prevFocusedElement || focusStartEl === \"container\") {\n                tryFocus(trapContainer);\n              }\n            });\n          }\n        }\n      }\n    }\n    function stopTrap() {\n      const trapContainer = unref(forwardRef);\n      if (trapContainer) {\n        trapContainer.removeEventListener(FOCUS_AFTER_TRAPPED, trapOnFocus);\n        const releasedEvent = new CustomEvent(FOCUS_AFTER_RELEASED, {\n          ...FOCUS_AFTER_TRAPPED_OPTS,\n          detail: {\n            focusReason: focusReason.value\n          }\n        });\n        trapContainer.addEventListener(FOCUS_AFTER_RELEASED, releaseOnFocus);\n        trapContainer.dispatchEvent(releasedEvent);\n        if (!releasedEvent.defaultPrevented && (focusReason.value == \"keyboard\" || !isFocusCausedByUserEvent() || trapContainer.contains(document.activeElement))) {\n          tryFocus(lastFocusBeforeTrapped != null ? lastFocusBeforeTrapped : document.body);\n        }\n        trapContainer.removeEventListener(FOCUS_AFTER_RELEASED, releaseOnFocus);\n        focusableStack.remove(focusLayer);\n      }\n    }\n    onMounted(() => {\n      if (props.trapped) {\n        startTrap();\n      }\n      watch(() => props.trapped, trapped => {\n        if (trapped) {\n          startTrap();\n        } else {\n          stopTrap();\n        }\n      });\n    });\n    onBeforeUnmount(() => {\n      if (props.trapped) {\n        stopTrap();\n      }\n      if (forwardRef.value) {\n        forwardRef.value.removeEventListener(\"keydown\", onKeydown);\n        forwardRef.value.removeEventListener(\"focusin\", onFocusIn);\n        forwardRef.value.removeEventListener(\"focusout\", onFocusOut);\n        forwardRef.value = void 0;\n      }\n    });\n    return {\n      onKeydown\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return renderSlot(_ctx.$slots, \"default\", {\n    handleKeydown: _ctx.onKeydown\n  });\n}\nvar ElFocusTrap = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"focus-trap.vue\"]]);\nexport { ElFocusTrap as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}