{"ast": null, "code": "import { computed, unref, isRef, ref, inject } from 'vue';\nimport { get } from 'lodash-unified';\nimport English from '../../locale/lang/en.mjs';\nconst buildTranslator = locale => (path, option) => translate(path, option, unref(locale));\nconst translate = (path, option, locale) => get(locale, path, path).replace(/\\{(\\w+)\\}/g, (_, key) => {\n  var _a;\n  return `${(_a = option == null ? void 0 : option[key]) != null ? _a : `{${key}}`}`;\n});\nconst buildLocaleContext = locale => {\n  const lang = computed(() => unref(locale).name);\n  const localeRef = isRef(locale) ? locale : ref(locale);\n  return {\n    lang,\n    locale: localeRef,\n    t: buildTranslator(locale)\n  };\n};\nconst localeContextKey = Symbol(\"localeContextKey\");\nconst useLocale = localeOverrides => {\n  const locale = localeOverrides || inject(localeContextKey, ref());\n  return buildLocaleContext(computed(() => locale.value || English));\n};\nexport { buildLocaleContext, buildTranslator, localeContextKey, translate, useLocale };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}