{"ast": null, "code": "import Timeline from './src/timeline.mjs';\nimport TimelineItem from './src/timeline-item2.mjs';\nexport { timelineItemProps } from './src/timeline-item.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElTimeline = withInstall(Timeline, {\n  TimelineItem\n});\nconst ElTimelineItem = withNoopInstall(TimelineItem);\nexport { ElTimeline, ElTimelineItem, ElTimeline as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}