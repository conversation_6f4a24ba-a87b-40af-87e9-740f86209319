{"ast": null, "code": "import \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { ref, getCurrentInstance, inject, computed, unref, provide } from 'vue';\nimport { configProviderContextKey } from '../constants.mjs';\nimport { useNamespace, defaultNamespace, namespaceContextKey } from '../../../../hooks/use-namespace/index.mjs';\nimport { useZIndex, defaultInitialZIndex, zIndexContextKey } from '../../../../hooks/use-z-index/index.mjs';\nimport { useLocale, localeContextKey } from '../../../../hooks/use-locale/index.mjs';\nimport { SIZE_INJECTION_KEY } from '../../../../hooks/use-size/index.mjs';\nimport { emptyValuesContextKey } from '../../../../hooks/use-empty-values/index.mjs';\nimport { debugWarn } from '../../../../utils/error.mjs';\nimport { keysOf } from '../../../../utils/objects.mjs';\nconst globalConfig = ref();\nfunction useGlobalConfig(key, defaultValue = void 0) {\n  const config = getCurrentInstance() ? inject(configProviderContextKey, globalConfig) : globalConfig;\n  if (key) {\n    return computed(() => {\n      var _a, _b;\n      return (_b = (_a = config.value) == null ? void 0 : _a[key]) != null ? _b : defaultValue;\n    });\n  } else {\n    return config;\n  }\n}\nfunction useGlobalComponentSettings(block, sizeFallback) {\n  const config = useGlobalConfig();\n  const ns = useNamespace(block, computed(() => {\n    var _a;\n    return ((_a = config.value) == null ? void 0 : _a.namespace) || defaultNamespace;\n  }));\n  const locale = useLocale(computed(() => {\n    var _a;\n    return (_a = config.value) == null ? void 0 : _a.locale;\n  }));\n  const zIndex = useZIndex(computed(() => {\n    var _a;\n    return ((_a = config.value) == null ? void 0 : _a.zIndex) || defaultInitialZIndex;\n  }));\n  const size = computed(() => {\n    var _a;\n    return unref(sizeFallback) || ((_a = config.value) == null ? void 0 : _a.size) || \"\";\n  });\n  provideGlobalConfig(computed(() => unref(config) || {}));\n  return {\n    ns,\n    locale,\n    zIndex,\n    size\n  };\n}\nconst provideGlobalConfig = (config, app, global = false) => {\n  var _a;\n  const inSetup = !!getCurrentInstance();\n  const oldConfig = inSetup ? useGlobalConfig() : void 0;\n  const provideFn = (_a = app == null ? void 0 : app.provide) != null ? _a : inSetup ? provide : void 0;\n  if (!provideFn) {\n    debugWarn(\"provideGlobalConfig\", \"provideGlobalConfig() can only be used inside setup().\");\n    return;\n  }\n  const context = computed(() => {\n    const cfg = unref(config);\n    if (!(oldConfig == null ? void 0 : oldConfig.value)) return cfg;\n    return mergeConfig(oldConfig.value, cfg);\n  });\n  provideFn(configProviderContextKey, context);\n  provideFn(localeContextKey, computed(() => context.value.locale));\n  provideFn(namespaceContextKey, computed(() => context.value.namespace));\n  provideFn(zIndexContextKey, computed(() => context.value.zIndex));\n  provideFn(SIZE_INJECTION_KEY, {\n    size: computed(() => context.value.size || \"\")\n  });\n  provideFn(emptyValuesContextKey, computed(() => ({\n    emptyValues: context.value.emptyValues,\n    valueOnClear: context.value.valueOnClear\n  })));\n  if (global || !globalConfig.value) {\n    globalConfig.value = context.value;\n  }\n  return context;\n};\nconst mergeConfig = (a, b) => {\n  const keys = [... /* @__PURE__ */new Set([...keysOf(a), ...keysOf(b)])];\n  const obj = {};\n  for (const key of keys) {\n    obj[key] = b[key] !== void 0 ? b[key] : a[key];\n  }\n  return obj;\n};\nexport { provideGlobalConfig, useGlobalComponentSettings, useGlobalConfig };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}