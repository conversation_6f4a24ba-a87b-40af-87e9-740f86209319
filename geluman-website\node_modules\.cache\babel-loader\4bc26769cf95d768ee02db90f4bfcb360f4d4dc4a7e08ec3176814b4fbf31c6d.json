{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { isNil } from 'lodash-unified';\nimport { throwError } from '../../../utils/error.mjs';\nimport { isArray } from '@vue/shared';\nconst SCOPE = \"ElUpload\";\nclass UploadAjaxError extends Error {\n  constructor(message, status, method, url) {\n    super(message);\n    this.name = \"UploadAjaxError\";\n    this.status = status;\n    this.method = method;\n    this.url = url;\n  }\n}\nfunction getError(action, option, xhr) {\n  let msg;\n  if (xhr.response) {\n    msg = `${xhr.response.error || xhr.response}`;\n  } else if (xhr.responseText) {\n    msg = `${xhr.responseText}`;\n  } else {\n    msg = `fail to ${option.method} ${action} ${xhr.status}`;\n  }\n  return new UploadAjaxError(msg, xhr.status, option.method, action);\n}\nfunction getBody(xhr) {\n  const text = xhr.responseText || xhr.response;\n  if (!text) {\n    return text;\n  }\n  try {\n    return JSON.parse(text);\n  } catch (e) {\n    return text;\n  }\n}\nconst ajaxUpload = option => {\n  if (typeof XMLHttpRequest === \"undefined\") throwError(SCOPE, \"XMLHttpRequest is undefined\");\n  const xhr = new XMLHttpRequest();\n  const action = option.action;\n  if (xhr.upload) {\n    xhr.upload.addEventListener(\"progress\", evt => {\n      const progressEvt = evt;\n      progressEvt.percent = evt.total > 0 ? evt.loaded / evt.total * 100 : 0;\n      option.onProgress(progressEvt);\n    });\n  }\n  const formData = new FormData();\n  if (option.data) {\n    for (const [key, value] of Object.entries(option.data)) {\n      if (isArray(value) && value.length) formData.append(key, ...value);else formData.append(key, value);\n    }\n  }\n  formData.append(option.filename, option.file, option.file.name);\n  xhr.addEventListener(\"error\", () => {\n    option.onError(getError(action, option, xhr));\n  });\n  xhr.addEventListener(\"load\", () => {\n    if (xhr.status < 200 || xhr.status >= 300) {\n      return option.onError(getError(action, option, xhr));\n    }\n    option.onSuccess(getBody(xhr));\n  });\n  xhr.open(option.method, action, true);\n  if (option.withCredentials && \"withCredentials\" in xhr) {\n    xhr.withCredentials = true;\n  }\n  const headers = option.headers || {};\n  if (headers instanceof Headers) {\n    headers.forEach((value, key) => xhr.setRequestHeader(key, value));\n  } else {\n    for (const [key, value] of Object.entries(headers)) {\n      if (isNil(value)) continue;\n      xhr.setRequestHeader(key, String(value));\n    }\n  }\n  xhr.send(formData);\n  return xhr;\n};\nexport { UploadAjaxError, ajaxUpload };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}