<template>
  <div class="home">
    <!-- 英雄区域 -->
    <section class="hero">
      <!-- 3D动态背景 -->
      <div class="hero-background">
        <!-- 科技感粒子 -->
        <div class="tech-particles">
          <span
            v-for="n in 50"
            :key="n"
            class="particle"
            :style="{
              '--delay': `${Math.random() * 5}s`,
              '--size': `${Math.random() * 3 + 1}px`,
              '--x': `${Math.random() * 100}%`,
              '--y': `${Math.random() * 100}%`,
            }"
          ></span>
        </div>
        <!-- 网格背景 -->
        <div class="grid-background"></div>
        <!-- 流星效果 -->
        <div class="shooting-stars">
          <div class="shooting-star star-1"></div>
          <div class="shooting-star star-2"></div>
          <div class="shooting-star star-3"></div>
        </div>
      </div>

      <div class="hero-content">
        <div class="brand-logo">
          <img :src="logo" alt="格鲁曼" />
        </div>
        <h1 class="title">
          把科技变得<span class="gradient-text">萌萌哒~</span>
        </h1>
        <div class="slogan-container">
          <span class="static-text">我们</span>
          <div class="dynamic-text">
            <span>{{ slogans[currentIndex] }}</span>
          </div>
        </div>
        <div class="hero-actions">
          <el-button
            type="primary"
            size="large"
            round
            @click="scrollToProducts"
            class="primary-button"
            v-ripple
          >
            戳这里发现宝藏
            <el-icon class="el-icon--right"><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 装饰元素 -->
      <div class="decorative-elements">
        <div class="tech-circle"></div>
        <div class="floating-shapes">
          <div class="shape shape-1"></div>
          <div class="shape shape-2"></div>
          <div class="shape shape-3"></div>
        </div>
      </div>
    </section>

    <!-- 重新设计的产品展示区 -->
    <section class="products" id="products">
      <div class="section-header">
        <h2 class="gradient-text">创新产品</h2>
        <p>打造极致用户体验的数字化工具</p>
      </div>

      <div class="products-carousel-container">
        <el-carousel
          :interval="5000"
          type="card"
          height="450px"
          :autoplay="true"
          indicator-position="outside"
          arrow="never"
        >
          <!-- 智能高亮助手 -->
          <el-carousel-item>
            <div class="product-card">
              <div class="product-header">
                <div class="product-icon">
                  <el-icon><Monitor /></el-icon>
                </div>
                <div class="product-title">
                  <div class="product-badge">Browser Extension</div>
                  <h3>智能高亮助手</h3>
                </div>
              </div>
              <p class="product-description">
                专业的网页文本智能高亮工具，让阅读和学习更高效
              </p>
              <div class="feature-container">
                <ul class="feature-list">
                  <li>
                    <el-icon><Files /></el-icon>
                    <span>多分类管理</span>
                  </li>
                  <li>
                    <el-icon><Brush /></el-icon>
                    <span>颜色自定义</span>
                  </li>
                  <li>
                    <el-icon><Share /></el-icon>
                    <span>配置分享</span>
                  </li>
                </ul>
              </div>
              <div class="action-container">
                <router-link
                  to="/products/highlight"
                  class="action-button"
                  v-ripple
                >
                  了解更多
                  <el-icon><ArrowRight /></el-icon>
                </router-link>
              </div>
            </div>
          </el-carousel-item>

          <!-- 网页护眼助手 -->
          <el-carousel-item>
            <div class="product-card">
              <div class="product-header">
                <div class="product-icon">
                  <el-icon><View /></el-icon>
                </div>
                <div class="product-title">
                  <div class="product-badge">Browser Extension</div>
                  <h3>网页护眼助手</h3>
                </div>
              </div>
              <p class="product-description">
                智能护眼工具，让网页浏览更舒适，保护您的眼睛健康
              </p>
              <div class="feature-container">
                <ul class="feature-list">
                  <li>
                    <el-icon><Monitor /></el-icon>
                    <span>智能护眼模式</span>
                  </li>
                  <li>
                    <el-icon><MagicStick /></el-icon>
                    <span>场景自适应</span>
                  </li>
                  <li>
                    <el-icon><Setting /></el-icon>
                    <span>全局控制</span>
                  </li>
                </ul>
              </div>
              <div class="action-container">
                <router-link
                  to="/products/eyeshield"
                  class="action-button"
                  v-ripple
                >
                  了解更多
                  <el-icon><ArrowRight /></el-icon>
                </router-link>
              </div>
            </div>
          </el-carousel-item>

          <!-- 智能翻译助手 -->
          <el-carousel-item>
            <div class="product-card">
              <div class="product-header">
                <div class="product-icon">
                  <el-icon><ChatLineRound /></el-icon>
                </div>
                <div class="product-title">
                  <div class="product-badge">Browser Extension</div>
                  <h3>智能翻译助手</h3>
                </div>
              </div>
              <p class="product-description">
                高效、准确的网页文本翻译工具，帮助您跨越语言障碍
              </p>
              <div class="feature-container">
                <ul class="feature-list">
                  <li>
                    <el-icon><ChatLineRound /></el-icon>
                    <span>多语言支持</span>
                  </li>
                  <li>
                    <el-icon><Scissors /></el-icon>
                    <span>划词翻译</span>
                  </li>
                  <li>
                    <el-icon><Connection /></el-icon>
                    <span>实时翻译</span>
                  </li>
                </ul>
              </div>
              <div class="action-container">
                <router-link
                  to="/products/translator"
                  class="action-button"
                  v-ripple
                >
                  了解更多
                  <el-icon><ArrowRight /></el-icon>
                </router-link>
              </div>
            </div>
          </el-carousel-item>

          <!-- 智能语音时钟 -->
          <el-carousel-item>
            <div class="product-card">
              <div class="product-header">
                <div class="product-icon">
                  <el-icon><Timer /></el-icon>
                </div>
                <div class="product-title">
                  <div class="product-badge">Web App</div>
                  <h3>智能语音时钟</h3>
                </div>
              </div>
              <p class="product-description">
                优雅的时间管理助手，让时间提醒更自然、更贴心
              </p>
              <div class="feature-container">
                <ul class="feature-list">
                  <li>
                    <el-icon><Microphone /></el-icon>
                    <span>语音播报</span>
                  </li>
                  <li>
                    <el-icon><Monitor /></el-icon>
                    <span>优雅界面</span>
                  </li>
                  <li>
                    <el-icon><Setting /></el-icon>
                    <span>智能管理</span>
                  </li>
                </ul>
              </div>
              <div class="action-container">
                <a
                  href="https://clock.geluman.cn"
                  target="_blank"
                  class="action-button"
                  v-ripple
                >
                  立即体验
                  <el-icon><ArrowRight /></el-icon>
                </a>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: "HomePage",
};
</script>

<script setup>
import { ref, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import {
  ArrowRight,
  Check,
  Monitor,
  View,
  Files,
  Brush,
  Share,
  MagicStick,
  Setting,
  Timer,
  Microphone,
  ChatLineRound,
  Scissors,
  Connection,
} from "@element-plus/icons-vue";
import { highlight, eyeshield, clock, logo, translator } from "@/assets";

const { t } = useI18n();
const slogans = [
  "用魔法打造科技",
  "让你的浏览器变好玩",
  "每天都要元气满满~",
  "用黑科技拯救世界",
];

const currentIndex = ref(0);

const typeSlogan = async () => {
  while (true) {
    currentIndex.value = (currentIndex.value + 1) % slogans.length;
    await new Promise((resolve) => setTimeout(resolve, 3000));
  }
};

const scrollToProducts = () => {
  document.getElementById("products").scrollIntoView({
    behavior: "smooth",
  });
};

// 自定义波纹指令
const vRipple = {
  mounted(el) {
    el.addEventListener("click", (e) => {
      const ripple = document.createElement("span");
      ripple.classList.add("ripple");
      el.appendChild(ripple);

      const rect = el.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;

      ripple.style.width = ripple.style.height = `${size}px`;
      ripple.style.left = `${x}px`;
      ripple.style.top = `${y}px`;

      setTimeout(() => ripple.remove(), 1000);
    });
  },
};

onMounted(() => {
  typeSlogan();
});
</script>

<style lang="scss" scoped>
.home {
  background: var(--bg-primary);
}

.hero {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);

  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;

    .tech-particles {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 2;

      .particle {
        position: absolute;
        width: var(--size);
        height: var(--size);
        background: rgba(255, 255, 255, 0.5);
        border-radius: 50%;
        left: var(--x);
        top: var(--y);
        animation: pulse 2s infinite ease-in-out;
        animation-delay: var(--delay);
      }
    }

    .grid-background {
      position: absolute;
      width: 400%;
      height: 400%;
      background: linear-gradient(
          rgba(255, 255, 255, 0.05) 1px,
          transparent 1px
        ),
        linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
      background-size: 50px 50px;
      transform: rotate(45deg);
      top: -150%;
      left: -150%;
      animation: grid-move 50s linear infinite;
    }

    .shooting-stars {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 3;
      overflow: hidden;

      .shooting-star {
        position: absolute;
        width: 300px;
        height: 2px;
        background: linear-gradient(
          90deg,
          rgba(139, 92, 246, 0) 0%,
          rgba(139, 92, 246, 0.8) 20%,
          rgba(255, 255, 255, 1) 100%
        );
        box-shadow: 0 0 10px rgba(139, 92, 246, 0.8),
          0 0 20px rgba(139, 92, 246, 0.4);
        z-index: 10;
        transform-origin: right center;
        opacity: 0;

        &.star-1 {
          top: 0;
          right: 0;
          transform: rotate(135deg);
          animation: shootingStar 3s linear infinite 0.5s;
        }

        &.star-2 {
          top: 5%;
          right: 20%;
          transform: rotate(135deg);
          animation: shootingStar 3s linear infinite 2s;
        }

        &.star-3 {
          top: 10%;
          right: 40%;
          transform: rotate(135deg);
          animation: shootingStar 3s linear infinite 3.5s;
        }
      }
    }
  }

  .hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    padding: 0 2rem;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;

    .brand-logo {
      margin-bottom: 2rem;
      width: 100%;
      display: flex;
      justify-content: center;

      img {
        width: 320px;
        height: auto;
        max-width: 100%;
        filter: brightness(0) invert(1);
      }
    }

    .title {
      font-size: 4rem;
      color: white;
      margin-bottom: 2rem;
      font-weight: 700;

      .gradient-text {
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--accent-color)
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    .slogan-container {
      font-size: 2rem;
      color: white;
      margin-bottom: 3rem;
      display: flex;
      justify-content: center;
      gap: 1rem;

      .dynamic-text {
        color: var(--primary-color);
        min-width: 300px;
        position: relative;
        overflow: hidden;

        span {
          display: block;
          animation: slideIn 0.5s ease-out;
        }
      }
    }
  }
}

.products {
  padding: 8rem 2rem 10rem;
  background: var(--bg-secondary);
  position: relative;
  overflow: hidden;

  &:before {
    content: "";
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: radial-gradient(var(--primary-light) 0%, transparent 70%);
    opacity: 0.08;
    top: -100px;
    left: -100px;
    z-index: 0;
  }

  &:after {
    content: "";
    position: absolute;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: radial-gradient(var(--primary-color) 0%, transparent 70%);
    opacity: 0.06;
    bottom: -200px;
    right: -100px;
    z-index: 0;
  }

  .section-header {
    text-align: center;
    margin-bottom: 6rem;
    position: relative;
    z-index: 1;

    h2 {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    p {
      font-size: 1.2rem;
      color: var(--text-secondary);
    }
  }

  .products-carousel-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 1;

    @media (max-width: 768px) {
      padding: 0 3rem;
    }
  }
}

:deep(.el-carousel__container) {
  padding: 1.5rem 0;
  position: relative;
}

:deep(.el-carousel__item) {
  border-radius: 20px;

  &.is-active {
    z-index: 20;
    transform: scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.5s ease;
  }
}

:deep(.el-carousel__indicators--outside) {
  margin-top: 1.5rem;

  .el-carousel__indicator--horizontal {
    padding: 0 6px;

    &:hover .el-carousel__button {
      background-color: var(--primary-light);
      opacity: 0.8;
    }

    &.is-active .el-carousel__button {
      background-color: var(--primary-color);
    }
  }
}

:deep(.el-carousel__button) {
  width: 12px;
  height: 12px;
  background-color: #d8d8d8;
  border-radius: 50%;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.2);
  }
}

:deep(.el-carousel__indicator--horizontal.is-active) .el-carousel__button {
  transform: scale(1.3);
}

@media (max-width: 768px) {
  :deep(.el-carousel__arrow) {
    width: 40px;
    height: 40px;

    .el-icon {
      font-size: 18px;
    }
  }
}

.product-card {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  z-index: 15;

  .product-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .product-icon {
    width: 70px;
    height: 70px;
    background: var(--bg-accent);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
    box-shadow: 0 8px 15px rgba(139, 92, 246, 0.1);

    .el-icon {
      font-size: 2rem;
      color: var(--primary-color);
      transition: color 0.3s ease;
    }
  }

  .product-title {
    flex: 1;
  }

  .product-badge {
    display: inline-block;
    padding: 0.4rem 0.8rem;
    background: var(--bg-accent);
    color: var(--primary-color);
    border-radius: 20px;
    font-size: 0.8rem;
    margin-bottom: 0.6rem;
    font-weight: 500;
  }

  h3 {
    font-size: 1.8rem;
    color: var(--text-primary);
  }

  .product-description {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
    font-size: 1.05rem;
  }

  .feature-container {
    margin-bottom: 2rem;
    flex-grow: 1;
  }

  .feature-list {
    list-style: none;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.2rem;

    li {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: var(--text-secondary);

      .el-icon {
        color: var(--primary-color);
        font-size: 1.1rem;
      }

      span {
        font-size: 0.95rem;
      }
    }
  }

  .action-container {
    margin-top: auto;
    position: relative;
    z-index: 25;
  }

  .action-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 30px;
    transition: all 0.3s ease;
    font-weight: 500;
    box-shadow: 0 8px 15px rgba(139, 92, 246, 0.2);
    position: relative;
    z-index: 30;

    &:hover {
      background: var(--primary-light);
      transform: translateY(-3px);
      box-shadow: 0 12px 20px rgba(139, 92, 246, 0.3);
    }
  }

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);

    .product-icon {
      background: var(--primary-color);

      .el-icon {
        color: white;
      }
    }
  }
}

@media (max-width: 768px) {
  .products {
    padding: 6rem 1rem 8rem;

    .section-header h2 {
      font-size: 2.5rem;
    }

    .products-carousel-container {
      padding: 0 1rem;
    }
  }

  .feature-list {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

// 动画
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 添加波纹效果样式
.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple 1s cubic-bezier(0, 0, 0.2, 1);
  pointer-events: none;
}

@media (max-width: 768px) {
  .hero {
    .hero-content {
      .brand-logo {
        img {
          width: 240px;
        }
      }

      .title {
        font-size: 2.5rem;
      }

      .slogan-container {
        font-size: 1.5rem;
      }
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.2;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.8;
  }
}

@keyframes grid-move {
  0% {
    transform: translate3d(-10%, -10%, 0) rotate(45deg);
  }
  100% {
    transform: translate3d(-30%, -30%, 0) rotate(45deg);
  }
}

@keyframes shootingStar {
  0% {
    transform: translate(0, 0) rotate(135deg);
    opacity: 0;
  }
  5% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translate(-1000px, 1000px) rotate(135deg);
    opacity: 0;
  }
}
</style>
