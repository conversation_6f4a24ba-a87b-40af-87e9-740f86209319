{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { ref, computed, unref, watch } from 'vue';\nimport { isArray } from '@vue/shared';\nconst useData = (props, {\n  expandedRowKeys,\n  lastRenderedRowIndex,\n  resetAfterIndex\n}) => {\n  const depthMap = ref({});\n  const flattenedData = computed(() => {\n    const depths = {};\n    const {\n      data: data2,\n      rowKey\n    } = props;\n    const _expandedRowKeys = unref(expandedRowKeys);\n    if (!_expandedRowKeys || !_expandedRowKeys.length) return data2;\n    const array = [];\n    const keysSet = /* @__PURE__ */new Set();\n    _expandedRowKeys.forEach(x => keysSet.add(x));\n    let copy = data2.slice();\n    copy.forEach(x => depths[x[rowKey]] = 0);\n    while (copy.length > 0) {\n      const item = copy.shift();\n      array.push(item);\n      if (keysSet.has(item[rowKey]) && isArray(item.children) && item.children.length > 0) {\n        copy = [...item.children, ...copy];\n        item.children.forEach(child => depths[child[rowKey]] = depths[item[rowKey]] + 1);\n      }\n    }\n    depthMap.value = depths;\n    return array;\n  });\n  const data = computed(() => {\n    const {\n      data: data2,\n      expandColumnKey\n    } = props;\n    return expandColumnKey ? unref(flattenedData) : data2;\n  });\n  watch(data, (val, prev) => {\n    if (val !== prev) {\n      lastRenderedRowIndex.value = -1;\n      resetAfterIndex(0, true);\n    }\n  });\n  return {\n    data,\n    depthMap\n  };\n};\nexport { useData };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}