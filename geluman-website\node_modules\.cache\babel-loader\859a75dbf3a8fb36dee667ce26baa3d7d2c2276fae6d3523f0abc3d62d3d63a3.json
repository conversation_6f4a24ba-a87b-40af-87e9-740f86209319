{"ast": null, "code": "import { ref, onMounted } from \"vue\";\nimport { useI18n } from \"vue-i18n\";\nimport { ArrowRight, Check, Monitor, View, Files, Brush, Share, MagicStick, Setting, Timer, Microphone, ChatLineRound, Scissors, Connection } from \"@element-plus/icons-vue\";\nimport { highlight, eyeshield, clock, logo, translator } from \"@/assets\";\nconst __default__ = {\n  name: \"HomePage\"\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const {\n      t\n    } = useI18n();\n    const slogans = [\"用魔法打造科技\", \"让你的浏览器变好玩\", \"每天都要元气满满~\", \"用黑科技拯救世界\"];\n    const currentIndex = ref(0);\n    const typeSlogan = async () => {\n      while (true) {\n        currentIndex.value = (currentIndex.value + 1) % slogans.length;\n        await new Promise(resolve => setTimeout(resolve, 3000));\n      }\n    };\n    const scrollToProducts = () => {\n      document.getElementById(\"products\").scrollIntoView({\n        behavior: \"smooth\"\n      });\n    };\n\n    // 自定义波纹指令\n    const vRipple = {\n      mounted(el) {\n        el.addEventListener(\"click\", e => {\n          const ripple = document.createElement(\"span\");\n          ripple.classList.add(\"ripple\");\n          el.appendChild(ripple);\n          const rect = el.getBoundingClientRect();\n          const size = Math.max(rect.width, rect.height);\n          const x = e.clientX - rect.left - size / 2;\n          const y = e.clientY - rect.top - size / 2;\n          ripple.style.width = ripple.style.height = `${size}px`;\n          ripple.style.left = `${x}px`;\n          ripple.style.top = `${y}px`;\n          setTimeout(() => ripple.remove(), 1000);\n        });\n      }\n    };\n    onMounted(() => {\n      typeSlogan();\n    });\n    const __returned__ = {\n      t,\n      slogans,\n      currentIndex,\n      typeSlogan,\n      scrollToProducts,\n      vRipple,\n      ref,\n      onMounted,\n      get useI18n() {\n        return useI18n;\n      },\n      get ArrowRight() {\n        return ArrowRight;\n      },\n      get Check() {\n        return Check;\n      },\n      get Monitor() {\n        return Monitor;\n      },\n      get View() {\n        return View;\n      },\n      get Files() {\n        return Files;\n      },\n      get Brush() {\n        return Brush;\n      },\n      get Share() {\n        return Share;\n      },\n      get MagicStick() {\n        return MagicStick;\n      },\n      get Setting() {\n        return Setting;\n      },\n      get Timer() {\n        return Timer;\n      },\n      get Microphone() {\n        return Microphone;\n      },\n      get ChatLineRound() {\n        return ChatLineRound;\n      },\n      get Scissors() {\n        return Scissors;\n      },\n      get Connection() {\n        return Connection;\n      },\n      get highlight() {\n        return highlight;\n      },\n      get eyeshield() {\n        return eyeshield;\n      },\n      get clock() {\n        return clock;\n      },\n      get logo() {\n        return logo;\n      },\n      get translator() {\n        return translator;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onMounted", "useI18n", "ArrowRight", "Check", "Monitor", "View", "Files", "Brush", "Share", "MagicStick", "Setting", "Timer", "Microphone", "ChatLineRound", "Scissors", "Connection", "highlight", "eyeshield", "clock", "logo", "translator", "__default__", "name", "t", "slogans", "currentIndex", "typeSlogan", "value", "length", "Promise", "resolve", "setTimeout", "scrollToProducts", "document", "getElementById", "scrollIntoView", "behavior", "vRipple", "mounted", "el", "addEventListener", "e", "ripple", "createElement", "classList", "add", "append<PERSON><PERSON><PERSON>", "rect", "getBoundingClientRect", "size", "Math", "max", "width", "height", "x", "clientX", "left", "y", "clientY", "top", "style", "remove"], "sources": ["D:/codes/glmwebsite/geluman-website/src/views/Home.vue"], "sourcesContent": ["<template>\n  <div class=\"home\">\n    <!-- 英雄区域 -->\n    <section class=\"hero\">\n      <!-- 3D动态背景 -->\n      <div class=\"hero-background\">\n        <!-- 科技感粒子 -->\n        <div class=\"tech-particles\">\n          <span\n            v-for=\"n in 50\"\n            :key=\"n\"\n            class=\"particle\"\n            :style=\"{\n              '--delay': `${Math.random() * 5}s`,\n              '--size': `${Math.random() * 3 + 1}px`,\n              '--x': `${Math.random() * 100}%`,\n              '--y': `${Math.random() * 100}%`,\n            }\"\n          ></span>\n        </div>\n        <!-- 网格背景 -->\n        <div class=\"grid-background\"></div>\n        <!-- 流星效果 -->\n        <div class=\"shooting-stars\">\n          <div class=\"shooting-star star-1\"></div>\n          <div class=\"shooting-star star-2\"></div>\n          <div class=\"shooting-star star-3\"></div>\n        </div>\n      </div>\n\n      <div class=\"hero-content\">\n        <div class=\"brand-logo\">\n          <img :src=\"logo\" alt=\"格鲁曼\" />\n        </div>\n        <h1 class=\"title\">\n          把科技变得<span class=\"gradient-text\">萌萌哒~</span>\n        </h1>\n        <div class=\"slogan-container\">\n          <span class=\"static-text\">我们</span>\n          <div class=\"dynamic-text\">\n            <span>{{ slogans[currentIndex] }}</span>\n          </div>\n        </div>\n        <div class=\"hero-actions\">\n          <el-button\n            type=\"primary\"\n            size=\"large\"\n            round\n            @click=\"scrollToProducts\"\n            class=\"primary-button\"\n            v-ripple\n          >\n            戳这里发现宝藏\n            <el-icon class=\"el-icon--right\"><ArrowRight /></el-icon>\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 装饰元素 -->\n      <div class=\"decorative-elements\">\n        <div class=\"tech-circle\"></div>\n        <div class=\"floating-shapes\">\n          <div class=\"shape shape-1\"></div>\n          <div class=\"shape shape-2\"></div>\n          <div class=\"shape shape-3\"></div>\n        </div>\n      </div>\n    </section>\n\n    <!-- 重新设计的产品展示区 -->\n    <section class=\"products\" id=\"products\">\n      <div class=\"section-header\">\n        <h2 class=\"gradient-text\">创新产品</h2>\n        <p>打造极致用户体验的数字化工具</p>\n      </div>\n\n      <div class=\"products-carousel-container\">\n        <el-carousel\n          :interval=\"5000\"\n          type=\"card\"\n          height=\"450px\"\n          :autoplay=\"true\"\n          indicator-position=\"outside\"\n          arrow=\"never\"\n        >\n          <!-- 智能高亮助手 -->\n          <el-carousel-item>\n            <div class=\"product-card\">\n              <div class=\"product-header\">\n                <div class=\"product-icon\">\n                  <el-icon><Monitor /></el-icon>\n                </div>\n                <div class=\"product-title\">\n                  <div class=\"product-badge\">Browser Extension</div>\n                  <h3>智能高亮助手</h3>\n                </div>\n              </div>\n              <p class=\"product-description\">\n                专业的网页文本智能高亮工具，让阅读和学习更高效\n              </p>\n              <div class=\"feature-container\">\n                <ul class=\"feature-list\">\n                  <li>\n                    <el-icon><Files /></el-icon>\n                    <span>多分类管理</span>\n                  </li>\n                  <li>\n                    <el-icon><Brush /></el-icon>\n                    <span>颜色自定义</span>\n                  </li>\n                  <li>\n                    <el-icon><Share /></el-icon>\n                    <span>配置分享</span>\n                  </li>\n                </ul>\n              </div>\n              <div class=\"action-container\">\n                <router-link\n                  to=\"/products/highlight\"\n                  class=\"action-button\"\n                  v-ripple\n                >\n                  了解更多\n                  <el-icon><ArrowRight /></el-icon>\n                </router-link>\n              </div>\n            </div>\n          </el-carousel-item>\n\n          <!-- 网页护眼助手 -->\n          <el-carousel-item>\n            <div class=\"product-card\">\n              <div class=\"product-header\">\n                <div class=\"product-icon\">\n                  <el-icon><View /></el-icon>\n                </div>\n                <div class=\"product-title\">\n                  <div class=\"product-badge\">Browser Extension</div>\n                  <h3>网页护眼助手</h3>\n                </div>\n              </div>\n              <p class=\"product-description\">\n                智能护眼工具，让网页浏览更舒适，保护您的眼睛健康\n              </p>\n              <div class=\"feature-container\">\n                <ul class=\"feature-list\">\n                  <li>\n                    <el-icon><Monitor /></el-icon>\n                    <span>智能护眼模式</span>\n                  </li>\n                  <li>\n                    <el-icon><MagicStick /></el-icon>\n                    <span>场景自适应</span>\n                  </li>\n                  <li>\n                    <el-icon><Setting /></el-icon>\n                    <span>全局控制</span>\n                  </li>\n                </ul>\n              </div>\n              <div class=\"action-container\">\n                <router-link\n                  to=\"/products/eyeshield\"\n                  class=\"action-button\"\n                  v-ripple\n                >\n                  了解更多\n                  <el-icon><ArrowRight /></el-icon>\n                </router-link>\n              </div>\n            </div>\n          </el-carousel-item>\n\n          <!-- 智能翻译助手 -->\n          <el-carousel-item>\n            <div class=\"product-card\">\n              <div class=\"product-header\">\n                <div class=\"product-icon\">\n                  <el-icon><ChatLineRound /></el-icon>\n                </div>\n                <div class=\"product-title\">\n                  <div class=\"product-badge\">Browser Extension</div>\n                  <h3>智能翻译助手</h3>\n                </div>\n              </div>\n              <p class=\"product-description\">\n                高效、准确的网页文本翻译工具，帮助您跨越语言障碍\n              </p>\n              <div class=\"feature-container\">\n                <ul class=\"feature-list\">\n                  <li>\n                    <el-icon><ChatLineRound /></el-icon>\n                    <span>多语言支持</span>\n                  </li>\n                  <li>\n                    <el-icon><Scissors /></el-icon>\n                    <span>划词翻译</span>\n                  </li>\n                  <li>\n                    <el-icon><Connection /></el-icon>\n                    <span>实时翻译</span>\n                  </li>\n                </ul>\n              </div>\n              <div class=\"action-container\">\n                <router-link\n                  to=\"/products/translator\"\n                  class=\"action-button\"\n                  v-ripple\n                >\n                  了解更多\n                  <el-icon><ArrowRight /></el-icon>\n                </router-link>\n              </div>\n            </div>\n          </el-carousel-item>\n\n          <!-- 智能语音时钟 -->\n          <el-carousel-item>\n            <div class=\"product-card\">\n              <div class=\"product-header\">\n                <div class=\"product-icon\">\n                  <el-icon><Timer /></el-icon>\n                </div>\n                <div class=\"product-title\">\n                  <div class=\"product-badge\">Web App</div>\n                  <h3>智能语音时钟</h3>\n                </div>\n              </div>\n              <p class=\"product-description\">\n                优雅的时间管理助手，让时间提醒更自然、更贴心\n              </p>\n              <div class=\"feature-container\">\n                <ul class=\"feature-list\">\n                  <li>\n                    <el-icon><Microphone /></el-icon>\n                    <span>语音播报</span>\n                  </li>\n                  <li>\n                    <el-icon><Monitor /></el-icon>\n                    <span>优雅界面</span>\n                  </li>\n                  <li>\n                    <el-icon><Setting /></el-icon>\n                    <span>智能管理</span>\n                  </li>\n                </ul>\n              </div>\n              <div class=\"action-container\">\n                <a\n                  href=\"https://clock.geluman.cn\"\n                  target=\"_blank\"\n                  class=\"action-button\"\n                  v-ripple\n                >\n                  立即体验\n                  <el-icon><ArrowRight /></el-icon>\n                </a>\n              </div>\n            </div>\n          </el-carousel-item>\n        </el-carousel>\n      </div>\n    </section>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"HomePage\",\n};\n</script>\n\n<script setup>\nimport { ref, onMounted } from \"vue\";\nimport { useI18n } from \"vue-i18n\";\nimport {\n  ArrowRight,\n  Check,\n  Monitor,\n  View,\n  Files,\n  Brush,\n  Share,\n  MagicStick,\n  Setting,\n  Timer,\n  Microphone,\n  ChatLineRound,\n  Scissors,\n  Connection,\n} from \"@element-plus/icons-vue\";\nimport { highlight, eyeshield, clock, logo, translator } from \"@/assets\";\n\nconst { t } = useI18n();\nconst slogans = [\n  \"用魔法打造科技\",\n  \"让你的浏览器变好玩\",\n  \"每天都要元气满满~\",\n  \"用黑科技拯救世界\",\n];\n\nconst currentIndex = ref(0);\n\nconst typeSlogan = async () => {\n  while (true) {\n    currentIndex.value = (currentIndex.value + 1) % slogans.length;\n    await new Promise((resolve) => setTimeout(resolve, 3000));\n  }\n};\n\nconst scrollToProducts = () => {\n  document.getElementById(\"products\").scrollIntoView({\n    behavior: \"smooth\",\n  });\n};\n\n// 自定义波纹指令\nconst vRipple = {\n  mounted(el) {\n    el.addEventListener(\"click\", (e) => {\n      const ripple = document.createElement(\"span\");\n      ripple.classList.add(\"ripple\");\n      el.appendChild(ripple);\n\n      const rect = el.getBoundingClientRect();\n      const size = Math.max(rect.width, rect.height);\n      const x = e.clientX - rect.left - size / 2;\n      const y = e.clientY - rect.top - size / 2;\n\n      ripple.style.width = ripple.style.height = `${size}px`;\n      ripple.style.left = `${x}px`;\n      ripple.style.top = `${y}px`;\n\n      setTimeout(() => ripple.remove(), 1000);\n    });\n  },\n};\n\nonMounted(() => {\n  typeSlogan();\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.home {\n  background: var(--bg-primary);\n}\n\n.hero {\n  min-height: 100vh;\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\n\n  .hero-background {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n    z-index: 0;\n\n    .tech-particles {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      z-index: 2;\n\n      .particle {\n        position: absolute;\n        width: var(--size);\n        height: var(--size);\n        background: rgba(255, 255, 255, 0.5);\n        border-radius: 50%;\n        left: var(--x);\n        top: var(--y);\n        animation: pulse 2s infinite ease-in-out;\n        animation-delay: var(--delay);\n      }\n    }\n\n    .grid-background {\n      position: absolute;\n      width: 400%;\n      height: 400%;\n      background: linear-gradient(\n          rgba(255, 255, 255, 0.05) 1px,\n          transparent 1px\n        ),\n        linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);\n      background-size: 50px 50px;\n      transform: rotate(45deg);\n      top: -150%;\n      left: -150%;\n      animation: grid-move 50s linear infinite;\n    }\n\n    .shooting-stars {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      z-index: 3;\n      overflow: hidden;\n\n      .shooting-star {\n        position: absolute;\n        width: 300px;\n        height: 2px;\n        background: linear-gradient(\n          90deg,\n          rgba(139, 92, 246, 0) 0%,\n          rgba(139, 92, 246, 0.8) 20%,\n          rgba(255, 255, 255, 1) 100%\n        );\n        box-shadow: 0 0 10px rgba(139, 92, 246, 0.8),\n          0 0 20px rgba(139, 92, 246, 0.4);\n        z-index: 10;\n        transform-origin: right center;\n        opacity: 0;\n\n        &.star-1 {\n          top: 0;\n          right: 0;\n          transform: rotate(135deg);\n          animation: shootingStar 3s linear infinite 0.5s;\n        }\n\n        &.star-2 {\n          top: 5%;\n          right: 20%;\n          transform: rotate(135deg);\n          animation: shootingStar 3s linear infinite 2s;\n        }\n\n        &.star-3 {\n          top: 10%;\n          right: 40%;\n          transform: rotate(135deg);\n          animation: shootingStar 3s linear infinite 3.5s;\n        }\n      }\n    }\n  }\n\n  .hero-content {\n    position: relative;\n    z-index: 2;\n    text-align: center;\n    padding: 0 2rem;\n    width: 100%;\n    max-width: 1200px;\n    margin: 0 auto;\n\n    .brand-logo {\n      margin-bottom: 2rem;\n      width: 100%;\n      display: flex;\n      justify-content: center;\n\n      img {\n        width: 320px;\n        height: auto;\n        max-width: 100%;\n        filter: brightness(0) invert(1);\n      }\n    }\n\n    .title {\n      font-size: 4rem;\n      color: white;\n      margin-bottom: 2rem;\n      font-weight: 700;\n\n      .gradient-text {\n        background: linear-gradient(\n          135deg,\n          var(--primary-color),\n          var(--accent-color)\n        );\n        -webkit-background-clip: text;\n        -webkit-text-fill-color: transparent;\n      }\n    }\n\n    .slogan-container {\n      font-size: 2rem;\n      color: white;\n      margin-bottom: 3rem;\n      display: flex;\n      justify-content: center;\n      gap: 1rem;\n\n      .dynamic-text {\n        color: var(--primary-color);\n        min-width: 300px;\n        position: relative;\n        overflow: hidden;\n\n        span {\n          display: block;\n          animation: slideIn 0.5s ease-out;\n        }\n      }\n    }\n  }\n}\n\n.products {\n  padding: 8rem 2rem 10rem;\n  background: var(--bg-secondary);\n  position: relative;\n  overflow: hidden;\n\n  &:before {\n    content: \"\";\n    position: absolute;\n    width: 300px;\n    height: 300px;\n    border-radius: 50%;\n    background: radial-gradient(var(--primary-light) 0%, transparent 70%);\n    opacity: 0.08;\n    top: -100px;\n    left: -100px;\n    z-index: 0;\n  }\n\n  &:after {\n    content: \"\";\n    position: absolute;\n    width: 400px;\n    height: 400px;\n    border-radius: 50%;\n    background: radial-gradient(var(--primary-color) 0%, transparent 70%);\n    opacity: 0.06;\n    bottom: -200px;\n    right: -100px;\n    z-index: 0;\n  }\n\n  .section-header {\n    text-align: center;\n    margin-bottom: 6rem;\n    position: relative;\n    z-index: 1;\n\n    h2 {\n      font-size: 3rem;\n      margin-bottom: 1rem;\n    }\n\n    p {\n      font-size: 1.2rem;\n      color: var(--text-secondary);\n    }\n  }\n\n  .products-carousel-container {\n    max-width: 1400px;\n    margin: 0 auto;\n    padding: 0 2rem;\n    position: relative;\n    z-index: 1;\n\n    @media (max-width: 768px) {\n      padding: 0 3rem;\n    }\n  }\n}\n\n:deep(.el-carousel__container) {\n  padding: 1.5rem 0;\n  position: relative;\n}\n\n:deep(.el-carousel__item) {\n  border-radius: 20px;\n\n  &.is-active {\n    z-index: 20;\n    transform: scale(1.05);\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n    transition: all 0.5s ease;\n  }\n}\n\n:deep(.el-carousel__indicators--outside) {\n  margin-top: 1.5rem;\n\n  .el-carousel__indicator--horizontal {\n    padding: 0 6px;\n\n    &:hover .el-carousel__button {\n      background-color: var(--primary-light);\n      opacity: 0.8;\n    }\n\n    &.is-active .el-carousel__button {\n      background-color: var(--primary-color);\n    }\n  }\n}\n\n:deep(.el-carousel__button) {\n  width: 12px;\n  height: 12px;\n  background-color: #d8d8d8;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: scale(1.2);\n  }\n}\n\n:deep(.el-carousel__indicator--horizontal.is-active) .el-carousel__button {\n  transform: scale(1.3);\n}\n\n@media (max-width: 768px) {\n  :deep(.el-carousel__arrow) {\n    width: 40px;\n    height: 40px;\n\n    .el-icon {\n      font-size: 18px;\n    }\n  }\n}\n\n.product-card {\n  background: white;\n  border-radius: 20px;\n  padding: 2.5rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  position: relative;\n  z-index: 15;\n\n  .product-header {\n    display: flex;\n    align-items: center;\n    gap: 1.5rem;\n    margin-bottom: 1.5rem;\n  }\n\n  .product-icon {\n    width: 70px;\n    height: 70px;\n    background: var(--bg-accent);\n    border-radius: 16px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    transition: all 0.3s ease;\n    flex-shrink: 0;\n    box-shadow: 0 8px 15px rgba(139, 92, 246, 0.1);\n\n    .el-icon {\n      font-size: 2rem;\n      color: var(--primary-color);\n      transition: color 0.3s ease;\n    }\n  }\n\n  .product-title {\n    flex: 1;\n  }\n\n  .product-badge {\n    display: inline-block;\n    padding: 0.4rem 0.8rem;\n    background: var(--bg-accent);\n    color: var(--primary-color);\n    border-radius: 20px;\n    font-size: 0.8rem;\n    margin-bottom: 0.6rem;\n    font-weight: 500;\n  }\n\n  h3 {\n    font-size: 1.8rem;\n    color: var(--text-primary);\n  }\n\n  .product-description {\n    color: var(--text-secondary);\n    margin-bottom: 2rem;\n    line-height: 1.6;\n    font-size: 1.05rem;\n  }\n\n  .feature-container {\n    margin-bottom: 2rem;\n    flex-grow: 1;\n  }\n\n  .feature-list {\n    list-style: none;\n    display: grid;\n    grid-template-columns: repeat(3, 1fr);\n    gap: 1.2rem;\n\n    li {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      color: var(--text-secondary);\n\n      .el-icon {\n        color: var(--primary-color);\n        font-size: 1.1rem;\n      }\n\n      span {\n        font-size: 0.95rem;\n      }\n    }\n  }\n\n  .action-container {\n    margin-top: auto;\n    position: relative;\n    z-index: 25;\n  }\n\n  .action-button {\n    display: inline-flex;\n    align-items: center;\n    gap: 0.5rem;\n    padding: 0.8rem 1.5rem;\n    background: var(--primary-color);\n    color: white;\n    text-decoration: none;\n    border-radius: 30px;\n    transition: all 0.3s ease;\n    font-weight: 500;\n    box-shadow: 0 8px 15px rgba(139, 92, 246, 0.2);\n    position: relative;\n    z-index: 999 !important;\n\n    &:hover {\n      background: var(--primary-light);\n      transform: translateY(-3px);\n      box-shadow: 0 12px 20px rgba(139, 92, 246, 0.3);\n    }\n  }\n\n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);\n\n    .product-icon {\n      background: var(--primary-color);\n\n      .el-icon {\n        color: white;\n      }\n    }\n  }\n}\n\n@media (max-width: 768px) {\n  .products {\n    padding: 6rem 1rem 8rem;\n\n    .section-header h2 {\n      font-size: 2.5rem;\n    }\n\n    .products-carousel-container {\n      padding: 0 1rem;\n    }\n  }\n\n  .feature-list {\n    grid-template-columns: repeat(2, 1fr) !important;\n  }\n}\n\n// 动画\n@keyframes float {\n  0%,\n  100% {\n    transform: translateY(0);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n// 添加波纹效果样式\n.ripple {\n  position: absolute;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.3);\n  transform: scale(0);\n  animation: ripple 1s cubic-bezier(0, 0, 0.2, 1);\n  pointer-events: none;\n}\n\n@media (max-width: 768px) {\n  .hero {\n    .hero-content {\n      .brand-logo {\n        img {\n          width: 240px;\n        }\n      }\n\n      .title {\n        font-size: 2.5rem;\n      }\n\n      .slogan-container {\n        font-size: 1.5rem;\n      }\n    }\n  }\n}\n\n@keyframes pulse {\n  0%,\n  100% {\n    transform: scale(1);\n    opacity: 0.2;\n  }\n  50% {\n    transform: scale(1.5);\n    opacity: 0.8;\n  }\n}\n\n@keyframes grid-move {\n  0% {\n    transform: translate3d(-10%, -10%, 0) rotate(45deg);\n  }\n  100% {\n    transform: translate3d(-30%, -30%, 0) rotate(45deg);\n  }\n}\n\n@keyframes shootingStar {\n  0% {\n    transform: translate(0, 0) rotate(135deg);\n    opacity: 0;\n  }\n  5% {\n    opacity: 1;\n  }\n  90% {\n    opacity: 1;\n  }\n  100% {\n    transform: translate(-1000px, 1000px) rotate(135deg);\n    opacity: 0;\n  }\n}\n</style>\n"], "mappings": "AAkRA,SAASA,GAAG,EAAEC,SAAS,QAAQ,KAAK;AACpC,SAASC,OAAO,QAAQ,UAAU;AAClC,SACEC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,aAAa,EACbC,QAAQ,EACRC,UAAU,QACL,yBAAyB;AAChC,SAASC,SAAS,EAAEC,SAAS,EAAEC,KAAK,EAAEC,IAAI,EAAEC,UAAU,QAAQ,UAAU;AAxBxE,MAAAC,WAAA,GAAe;EACbC,IAAI,EAAE;AACR,CAAC;;;;;;IAwBD,MAAM;MAAEC;IAAE,CAAC,GAAGtB,OAAO,CAAC,CAAC;IACvB,MAAMuB,OAAO,GAAG,CACd,SAAS,EACT,WAAW,EACX,WAAW,EACX,UAAU,CACX;IAED,MAAMC,YAAY,GAAG1B,GAAG,CAAC,CAAC,CAAC;IAE3B,MAAM2B,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,OAAO,IAAI,EAAE;QACXD,YAAY,CAACE,KAAK,GAAG,CAACF,YAAY,CAACE,KAAK,GAAG,CAAC,IAAIH,OAAO,CAACI,MAAM;QAC9D,MAAM,IAAIC,OAAO,CAAEC,OAAO,IAAKC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAC3D;IACF,CAAC;IAED,MAAME,gBAAgB,GAAGA,CAAA,KAAM;MAC7BC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,CAACC,cAAc,CAAC;QACjDC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMC,OAAO,GAAG;MACdC,OAAOA,CAACC,EAAE,EAAE;QACVA,EAAE,CAACC,gBAAgB,CAAC,OAAO,EAAGC,CAAC,IAAK;UAClC,MAAMC,MAAM,GAAGT,QAAQ,CAACU,aAAa,CAAC,MAAM,CAAC;UAC7CD,MAAM,CAACE,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;UAC9BN,EAAE,CAACO,WAAW,CAACJ,MAAM,CAAC;UAEtB,MAAMK,IAAI,GAAGR,EAAE,CAACS,qBAAqB,CAAC,CAAC;UACvC,MAAMC,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACJ,IAAI,CAACK,KAAK,EAAEL,IAAI,CAACM,MAAM,CAAC;UAC9C,MAAMC,CAAC,GAAGb,CAAC,CAACc,OAAO,GAAGR,IAAI,CAACS,IAAI,GAAGP,IAAI,GAAG,CAAC;UAC1C,MAAMQ,CAAC,GAAGhB,CAAC,CAACiB,OAAO,GAAGX,IAAI,CAACY,GAAG,GAAGV,IAAI,GAAG,CAAC;UAEzCP,MAAM,CAACkB,KAAK,CAACR,KAAK,GAAGV,MAAM,CAACkB,KAAK,CAACP,MAAM,GAAG,GAAGJ,IAAI,IAAI;UACtDP,MAAM,CAACkB,KAAK,CAACJ,IAAI,GAAG,GAAGF,CAAC,IAAI;UAC5BZ,MAAM,CAACkB,KAAK,CAACD,GAAG,GAAG,GAAGF,CAAC,IAAI;UAE3B1B,UAAU,CAAC,MAAMW,MAAM,CAACmB,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;QACzC,CAAC,CAAC;MACJ;IACF,CAAC;IAED7D,SAAS,CAAC,MAAM;MACd0B,UAAU,CAAC,CAAC;IACd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}