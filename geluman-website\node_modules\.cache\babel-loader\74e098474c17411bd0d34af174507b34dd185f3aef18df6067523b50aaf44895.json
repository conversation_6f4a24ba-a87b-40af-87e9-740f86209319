{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, getCurrentInstance, inject, ref, reactive, watch, onMounted, nextTick, h } from 'vue';\nimport { ElCheckbox } from '../../../checkbox/index.mjs';\nimport FilterPanel from '../filter-panel.mjs';\nimport useLayoutObserver from '../layout-observer.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport useEvent from './event-helper.mjs';\nimport useStyle from './style.helper.mjs';\nimport useUtils from './utils-helper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nvar TableHeader = defineComponent({\n  name: \"ElTableHeader\",\n  components: {\n    ElCheckbox\n  },\n  props: {\n    fixed: {\n      type: String,\n      default: \"\"\n    },\n    store: {\n      required: true,\n      type: Object\n    },\n    border: Boolean,\n    defaultSort: {\n      type: Object,\n      default: () => {\n        return {\n          prop: \"\",\n          order: \"\"\n        };\n      }\n    },\n    appendFilterPanelTo: {\n      type: String\n    },\n    allowDragLastColumn: {\n      type: Boolean\n    }\n  },\n  setup(props, {\n    emit\n  }) {\n    const instance = getCurrentInstance();\n    const parent = inject(TABLE_INJECTION_KEY);\n    const ns = useNamespace(\"table\");\n    const filterPanels = ref({});\n    const {\n      onColumnsChange,\n      onScrollableChange\n    } = useLayoutObserver(parent);\n    const isTableLayoutAuto = (parent == null ? void 0 : parent.props.tableLayout) === \"auto\";\n    const saveIndexSelection = reactive(/* @__PURE__ */new Map());\n    const theadRef = ref();\n    const updateFixedColumnStyle = () => {\n      setTimeout(() => {\n        if (saveIndexSelection.size > 0) {\n          saveIndexSelection.forEach((column, key) => {\n            const el = theadRef.value.querySelector(`.${key.replace(/\\s/g, \".\")}`);\n            if (el) {\n              const width = el.getBoundingClientRect().width;\n              column.width = width;\n            }\n          });\n          saveIndexSelection.clear();\n        }\n      });\n    };\n    watch(saveIndexSelection, updateFixedColumnStyle);\n    onMounted(async () => {\n      await nextTick();\n      await nextTick();\n      const {\n        prop,\n        order\n      } = props.defaultSort;\n      parent == null ? void 0 : parent.store.commit(\"sort\", {\n        prop,\n        order,\n        init: true\n      });\n      updateFixedColumnStyle();\n    });\n    const {\n      handleHeaderClick,\n      handleHeaderContextMenu,\n      handleMouseDown,\n      handleMouseMove,\n      handleMouseOut,\n      handleSortClick,\n      handleFilterClick\n    } = useEvent(props, emit);\n    const {\n      getHeaderRowStyle,\n      getHeaderRowClass,\n      getHeaderCellStyle,\n      getHeaderCellClass\n    } = useStyle(props);\n    const {\n      isGroup,\n      toggleAllSelection,\n      columnRows\n    } = useUtils(props);\n    instance.state = {\n      onColumnsChange,\n      onScrollableChange\n    };\n    instance.filterPanels = filterPanels;\n    return {\n      ns,\n      filterPanels,\n      onColumnsChange,\n      onScrollableChange,\n      columnRows,\n      getHeaderRowClass,\n      getHeaderRowStyle,\n      getHeaderCellClass,\n      getHeaderCellStyle,\n      handleHeaderClick,\n      handleHeaderContextMenu,\n      handleMouseDown,\n      handleMouseMove,\n      handleMouseOut,\n      handleSortClick,\n      handleFilterClick,\n      isGroup,\n      toggleAllSelection,\n      saveIndexSelection,\n      isTableLayoutAuto,\n      theadRef,\n      updateFixedColumnStyle\n    };\n  },\n  render() {\n    const {\n      ns,\n      isGroup,\n      columnRows,\n      getHeaderCellStyle,\n      getHeaderCellClass,\n      getHeaderRowClass,\n      getHeaderRowStyle,\n      handleHeaderClick,\n      handleHeaderContextMenu,\n      handleMouseDown,\n      handleMouseMove,\n      handleSortClick,\n      handleMouseOut,\n      store,\n      $parent,\n      saveIndexSelection,\n      isTableLayoutAuto\n    } = this;\n    let rowSpan = 1;\n    return h(\"thead\", {\n      ref: \"theadRef\",\n      class: {\n        [ns.is(\"group\")]: isGroup\n      }\n    }, columnRows.map((subColumns, rowIndex) => h(\"tr\", {\n      class: getHeaderRowClass(rowIndex),\n      key: rowIndex,\n      style: getHeaderRowStyle(rowIndex)\n    }, subColumns.map((column, cellIndex) => {\n      if (column.rowSpan > rowSpan) {\n        rowSpan = column.rowSpan;\n      }\n      const _class = getHeaderCellClass(rowIndex, cellIndex, subColumns, column);\n      if (isTableLayoutAuto && column.fixed) {\n        saveIndexSelection.set(_class, column);\n      }\n      return h(\"th\", {\n        class: _class,\n        colspan: column.colSpan,\n        key: `${column.id}-thead`,\n        rowspan: column.rowSpan,\n        style: getHeaderCellStyle(rowIndex, cellIndex, subColumns, column),\n        onClick: $event => {\n          if ($event.currentTarget.classList.contains(\"noclick\")) {\n            return;\n          }\n          handleHeaderClick($event, column);\n        },\n        onContextmenu: $event => handleHeaderContextMenu($event, column),\n        onMousedown: $event => handleMouseDown($event, column),\n        onMousemove: $event => handleMouseMove($event, column),\n        onMouseout: handleMouseOut\n      }, [h(\"div\", {\n        class: [\"cell\", column.filteredValue && column.filteredValue.length > 0 ? \"highlight\" : \"\"]\n      }, [column.renderHeader ? column.renderHeader({\n        column,\n        $index: cellIndex,\n        store,\n        _self: $parent\n      }) : column.label, column.sortable && h(\"span\", {\n        onClick: $event => handleSortClick($event, column),\n        class: \"caret-wrapper\"\n      }, [h(\"i\", {\n        onClick: $event => handleSortClick($event, column, \"ascending\"),\n        class: \"sort-caret ascending\"\n      }), h(\"i\", {\n        onClick: $event => handleSortClick($event, column, \"descending\"),\n        class: \"sort-caret descending\"\n      })]), column.filterable && h(FilterPanel, {\n        store,\n        placement: column.filterPlacement || \"bottom-start\",\n        appendTo: $parent.appendFilterPanelTo,\n        column,\n        upDataColumn: (key, value) => {\n          column[key] = value;\n        }\n      }, {\n        \"filter-icon\": () => column.renderFilterIcon ? column.renderFilterIcon({\n          filterOpened: column.filterOpened\n        }) : null\n      })])]);\n    }))));\n  }\n});\nexport { TableHeader as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}