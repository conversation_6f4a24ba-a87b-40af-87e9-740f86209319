{"ast": null, "code": "import { ref, watch, unref } from 'vue';\nconst useScrollbar = (props, {\n  mainTableRef,\n  leftTableRef,\n  rightTableRef,\n  onMaybeEndReached\n}) => {\n  const scrollPos = ref({\n    scrollLeft: 0,\n    scrollTop: 0\n  });\n  function doScroll(params) {\n    var _a, _b, _c;\n    const {\n      scrollTop\n    } = params;\n    (_a = mainTableRef.value) == null ? void 0 : _a.scrollTo(params);\n    (_b = leftTableRef.value) == null ? void 0 : _b.scrollToTop(scrollTop);\n    (_c = rightTableRef.value) == null ? void 0 : _c.scrollToTop(scrollTop);\n  }\n  function scrollTo(params) {\n    scrollPos.value = params;\n    doScroll(params);\n  }\n  function scrollToTop(scrollTop) {\n    scrollPos.value.scrollTop = scrollTop;\n    doScroll(unref(scrollPos));\n  }\n  function scrollToLeft(scrollLeft) {\n    var _a, _b;\n    scrollPos.value.scrollLeft = scrollLeft;\n    (_b = (_a = mainTableRef.value) == null ? void 0 : _a.scrollTo) == null ? void 0 : _b.call(_a, unref(scrollPos));\n  }\n  function onScroll(params) {\n    var _a;\n    scrollTo(params);\n    (_a = props.onScroll) == null ? void 0 : _a.call(props, params);\n  }\n  function onVerticalScroll({\n    scrollTop\n  }) {\n    const {\n      scrollTop: currentScrollTop\n    } = unref(scrollPos);\n    if (scrollTop !== currentScrollTop) scrollToTop(scrollTop);\n  }\n  function scrollToRow(row, strategy = \"auto\") {\n    var _a;\n    (_a = mainTableRef.value) == null ? void 0 : _a.scrollToRow(row, strategy);\n  }\n  watch(() => unref(scrollPos).scrollTop, (cur, prev) => {\n    if (cur > prev) onMaybeEndReached();\n  });\n  return {\n    scrollPos,\n    scrollTo,\n    scrollToLeft,\n    scrollToTop,\n    scrollToRow,\n    onScroll,\n    onVerticalScroll\n  };\n};\nexport { useScrollbar };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}