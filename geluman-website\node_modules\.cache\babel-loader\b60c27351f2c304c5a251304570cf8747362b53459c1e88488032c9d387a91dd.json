{"ast": null, "code": "import { defineComponent, ref, computed, toRef, watch, useSlots, provide, openBlock, createElementBlock, Fragment, createVNode, unref, withCtx, createElementVNode, mergeProps, normalizeStyle, createBlock, renderSlot, createCommentVNode } from 'vue';\nimport { useVModel } from '@vueuse/core';\nimport { ElTeleport } from '../../teleport/index.mjs';\nimport ElTourMask from './mask2.mjs';\nimport ElTourContent from './content.mjs';\nimport ElTourSteps from './steps.mjs';\nimport { tourProps, tourEmits } from './tour.mjs';\nimport { useTarget, tourKey } from './helper.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isBoolean } from '../../../utils/types.mjs';\nimport { useZIndex } from '../../../hooks/use-z-index/index.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTour\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: tourProps,\n  emits: tourEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"tour\");\n    const total = ref(0);\n    const currentStep = ref();\n    const current = useVModel(props, \"current\", emit, {\n      passive: true\n    });\n    const currentTarget = computed(() => {\n      var _a;\n      return (_a = currentStep.value) == null ? void 0 : _a.target;\n    });\n    const kls = computed(() => [ns.b(), mergedType.value === \"primary\" ? ns.m(\"primary\") : \"\"]);\n    const mergedPlacement = computed(() => {\n      var _a;\n      return ((_a = currentStep.value) == null ? void 0 : _a.placement) || props.placement;\n    });\n    const mergedContentStyle = computed(() => {\n      var _a, _b;\n      return (_b = (_a = currentStep.value) == null ? void 0 : _a.contentStyle) != null ? _b : props.contentStyle;\n    });\n    const mergedMask = computed(() => {\n      var _a, _b;\n      return (_b = (_a = currentStep.value) == null ? void 0 : _a.mask) != null ? _b : props.mask;\n    });\n    const mergedShowMask = computed(() => !!mergedMask.value && props.modelValue);\n    const mergedMaskStyle = computed(() => isBoolean(mergedMask.value) ? void 0 : mergedMask.value);\n    const mergedShowArrow = computed(() => {\n      var _a, _b;\n      return !!currentTarget.value && ((_b = (_a = currentStep.value) == null ? void 0 : _a.showArrow) != null ? _b : props.showArrow);\n    });\n    const mergedScrollIntoViewOptions = computed(() => {\n      var _a, _b;\n      return (_b = (_a = currentStep.value) == null ? void 0 : _a.scrollIntoViewOptions) != null ? _b : props.scrollIntoViewOptions;\n    });\n    const mergedType = computed(() => {\n      var _a, _b;\n      return (_b = (_a = currentStep.value) == null ? void 0 : _a.type) != null ? _b : props.type;\n    });\n    const {\n      nextZIndex\n    } = useZIndex();\n    const nowZIndex = nextZIndex();\n    const mergedZIndex = computed(() => {\n      var _a;\n      return (_a = props.zIndex) != null ? _a : nowZIndex;\n    });\n    const {\n      mergedPosInfo: pos,\n      triggerTarget\n    } = useTarget(currentTarget, toRef(props, \"modelValue\"), toRef(props, \"gap\"), mergedMask, mergedScrollIntoViewOptions);\n    watch(() => props.modelValue, val => {\n      if (!val) {\n        current.value = 0;\n      }\n    });\n    const onEscClose = () => {\n      if (props.closeOnPressEscape) {\n        emit(UPDATE_MODEL_EVENT, false);\n        emit(\"close\", current.value);\n      }\n    };\n    const onUpdateTotal = val => {\n      total.value = val;\n    };\n    const slots = useSlots();\n    provide(tourKey, {\n      currentStep,\n      current,\n      total,\n      showClose: toRef(props, \"showClose\"),\n      closeIcon: toRef(props, \"closeIcon\"),\n      mergedType,\n      ns,\n      slots,\n      updateModelValue(modelValue) {\n        emit(UPDATE_MODEL_EVENT, modelValue);\n      },\n      onClose() {\n        emit(\"close\", current.value);\n      },\n      onFinish() {\n        emit(\"finish\");\n      },\n      onChange() {\n        emit(CHANGE_EVENT, current.value);\n      }\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(Fragment, null, [createVNode(unref(ElTeleport), {\n        to: _ctx.appendTo\n      }, {\n        default: withCtx(() => {\n          var _a, _b;\n          return [createElementVNode(\"div\", mergeProps({\n            class: unref(kls)\n          }, _ctx.$attrs), [createVNode(ElTourMask, {\n            visible: unref(mergedShowMask),\n            fill: (_a = unref(mergedMaskStyle)) == null ? void 0 : _a.color,\n            style: normalizeStyle((_b = unref(mergedMaskStyle)) == null ? void 0 : _b.style),\n            pos: unref(pos),\n            \"z-index\": unref(mergedZIndex),\n            \"target-area-clickable\": _ctx.targetAreaClickable\n          }, null, 8, [\"visible\", \"fill\", \"style\", \"pos\", \"z-index\", \"target-area-clickable\"]), _ctx.modelValue ? (openBlock(), createBlock(ElTourContent, {\n            key: unref(current),\n            reference: unref(triggerTarget),\n            placement: unref(mergedPlacement),\n            \"show-arrow\": unref(mergedShowArrow),\n            \"z-index\": unref(mergedZIndex),\n            style: normalizeStyle(unref(mergedContentStyle)),\n            onClose: onEscClose\n          }, {\n            default: withCtx(() => [createVNode(unref(ElTourSteps), {\n              current: unref(current),\n              onUpdateTotal\n            }, {\n              default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n              _: 3\n            }, 8, [\"current\"])]),\n            _: 3\n          }, 8, [\"reference\", \"placement\", \"show-arrow\", \"z-index\", \"style\"])) : createCommentVNode(\"v-if\", true)], 16)];\n        }),\n        _: 3\n      }, 8, [\"to\"]), createCommentVNode(\" just for IDE \"), createCommentVNode(\"v-if\", true)], 64);\n    };\n  }\n});\nvar Tour = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tour.vue\"]]);\nexport { Tour as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}