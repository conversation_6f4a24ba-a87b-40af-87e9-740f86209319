{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nconst descriptionProps = buildProps({\n  border: Boolean,\n  column: {\n    type: Number,\n    default: 3\n  },\n  direction: {\n    type: String,\n    values: [\"horizontal\", \"vertical\"],\n    default: \"horizontal\"\n  },\n  size: useSizeProp,\n  title: {\n    type: String,\n    default: \"\"\n  },\n  extra: {\n    type: String,\n    default: \"\"\n  },\n  labelWidth: {\n    type: [String, Number],\n    default: \"\"\n  }\n});\nexport { descriptionProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}