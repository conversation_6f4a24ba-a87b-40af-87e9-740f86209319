{"ast": null, "code": "import { defineComponent, ref, reactive, computed, onMounted, watch, nextTick, provide, openBlock, createBlock, unref, withCtx, withDirectives, createElementBlock, withKeys, createElementVNode, normalizeClass, createVNode, createCommentVNode, createTextVNode, toDisplayString, mergeProps, normalizeStyle, vShow } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport { ElButton } from '../../button/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { ElInput } from '../../input/index.mjs';\nimport { ArrowDown, Close } from '@element-plus/icons-vue';\nimport AlphaSlider from './components/alpha-slider.mjs';\nimport HueSlider from './components/hue-slider.mjs';\nimport Predefine from './components/predefine.mjs';\nimport SvPanel from './components/sv-panel.mjs';\nimport Color from './utils/color.mjs';\nimport { colorPickerProps, colorPickerEmits, colorPickerContextKey } from './color-picker.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport ClickOutside from '../../../directives/click-outside/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormItem, useFormItemInputId } from '../../form/src/hooks/use-form-item.mjs';\nimport { useFormSize, useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useFocusController } from '../../../hooks/use-focus-controller/index.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nconst __default__ = defineComponent({\n  name: \"ElColorPicker\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: colorPickerProps,\n  emits: colorPickerEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"color\");\n    const {\n      formItem\n    } = useFormItem();\n    const colorSize = useFormSize();\n    const colorDisabled = useFormDisabled();\n    const {\n      inputId: buttonId,\n      isLabeledByFormItem\n    } = useFormItemInputId(props, {\n      formItemContext: formItem\n    });\n    const hue = ref();\n    const sv = ref();\n    const alpha = ref();\n    const popper = ref();\n    const triggerRef = ref();\n    const inputRef = ref();\n    const {\n      isFocused,\n      handleFocus,\n      handleBlur\n    } = useFocusController(triggerRef, {\n      beforeFocus() {\n        return colorDisabled.value;\n      },\n      beforeBlur(event) {\n        var _a;\n        return (_a = popper.value) == null ? void 0 : _a.isFocusInsideContent(event);\n      },\n      afterBlur() {\n        setShowPicker(false);\n        resetColor();\n      }\n    });\n    let shouldActiveChange = true;\n    const color = reactive(new Color({\n      enableAlpha: props.showAlpha,\n      format: props.colorFormat || \"\",\n      value: props.modelValue\n    }));\n    const showPicker = ref(false);\n    const showPanelColor = ref(false);\n    const customInput = ref(\"\");\n    const displayedColor = computed(() => {\n      if (!props.modelValue && !showPanelColor.value) {\n        return \"transparent\";\n      }\n      return displayedRgb(color, props.showAlpha);\n    });\n    const currentColor = computed(() => {\n      return !props.modelValue && !showPanelColor.value ? \"\" : color.value;\n    });\n    const buttonAriaLabel = computed(() => {\n      return !isLabeledByFormItem.value ? props.ariaLabel || t(\"el.colorpicker.defaultLabel\") : void 0;\n    });\n    const buttonAriaLabelledby = computed(() => {\n      return isLabeledByFormItem.value ? formItem == null ? void 0 : formItem.labelId : void 0;\n    });\n    const btnKls = computed(() => {\n      return [ns.b(\"picker\"), ns.is(\"disabled\", colorDisabled.value), ns.bm(\"picker\", colorSize.value), ns.is(\"focused\", isFocused.value)];\n    });\n    function displayedRgb(color2, showAlpha) {\n      if (!(color2 instanceof Color)) {\n        throw new TypeError(\"color should be instance of _color Class\");\n      }\n      const {\n        r,\n        g,\n        b\n      } = color2.toRgb();\n      return showAlpha ? `rgba(${r}, ${g}, ${b}, ${color2.get(\"alpha\") / 100})` : `rgb(${r}, ${g}, ${b})`;\n    }\n    function setShowPicker(value) {\n      showPicker.value = value;\n    }\n    const debounceSetShowPicker = debounce(setShowPicker, 100, {\n      leading: true\n    });\n    function show() {\n      if (colorDisabled.value) return;\n      setShowPicker(true);\n    }\n    function hide() {\n      debounceSetShowPicker(false);\n      resetColor();\n    }\n    function resetColor() {\n      nextTick(() => {\n        if (props.modelValue) {\n          color.fromString(props.modelValue);\n        } else {\n          color.value = \"\";\n          nextTick(() => {\n            showPanelColor.value = false;\n          });\n        }\n      });\n    }\n    function handleTrigger() {\n      if (colorDisabled.value) return;\n      if (showPicker.value) {\n        resetColor();\n      }\n      debounceSetShowPicker(!showPicker.value);\n    }\n    function handleConfirm() {\n      color.fromString(customInput.value);\n    }\n    function confirmValue() {\n      const value = color.value;\n      emit(UPDATE_MODEL_EVENT, value);\n      emit(CHANGE_EVENT, value);\n      if (props.validateEvent) {\n        formItem == null ? void 0 : formItem.validate(\"change\").catch(err => debugWarn(err));\n      }\n      debounceSetShowPicker(false);\n      nextTick(() => {\n        const newColor = new Color({\n          enableAlpha: props.showAlpha,\n          format: props.colorFormat || \"\",\n          value: props.modelValue\n        });\n        if (!color.compare(newColor)) {\n          resetColor();\n        }\n      });\n    }\n    function clear() {\n      debounceSetShowPicker(false);\n      emit(UPDATE_MODEL_EVENT, null);\n      emit(CHANGE_EVENT, null);\n      if (props.modelValue !== null && props.validateEvent) {\n        formItem == null ? void 0 : formItem.validate(\"change\").catch(err => debugWarn(err));\n      }\n      resetColor();\n    }\n    function handleClickOutside() {\n      if (!showPicker.value) return;\n      hide();\n      isFocused.value && focus();\n    }\n    function handleEsc(event) {\n      event.preventDefault();\n      event.stopPropagation();\n      setShowPicker(false);\n      resetColor();\n    }\n    function handleKeyDown(event) {\n      switch (event.code) {\n        case EVENT_CODE.enter:\n        case EVENT_CODE.numpadEnter:\n        case EVENT_CODE.space:\n          event.preventDefault();\n          event.stopPropagation();\n          show();\n          inputRef.value.focus();\n          break;\n        case EVENT_CODE.esc:\n          handleEsc(event);\n          break;\n      }\n    }\n    function focus() {\n      triggerRef.value.focus();\n    }\n    function blur() {\n      triggerRef.value.blur();\n    }\n    onMounted(() => {\n      if (props.modelValue) {\n        customInput.value = currentColor.value;\n      }\n    });\n    watch(() => props.modelValue, newVal => {\n      if (!newVal) {\n        showPanelColor.value = false;\n      } else if (newVal && newVal !== color.value) {\n        shouldActiveChange = false;\n        color.fromString(newVal);\n      }\n    });\n    watch(() => [props.colorFormat, props.showAlpha], () => {\n      color.enableAlpha = props.showAlpha;\n      color.format = props.colorFormat || color.format;\n      color.doOnChange();\n      emit(UPDATE_MODEL_EVENT, color.value);\n    });\n    watch(() => currentColor.value, val => {\n      customInput.value = val;\n      shouldActiveChange && emit(\"activeChange\", val);\n      shouldActiveChange = true;\n    });\n    watch(() => color.value, () => {\n      if (!props.modelValue && !showPanelColor.value) {\n        showPanelColor.value = true;\n      }\n    });\n    watch(() => showPicker.value, () => {\n      nextTick(() => {\n        var _a, _b, _c;\n        (_a = hue.value) == null ? void 0 : _a.update();\n        (_b = sv.value) == null ? void 0 : _b.update();\n        (_c = alpha.value) == null ? void 0 : _c.update();\n      });\n    });\n    provide(colorPickerContextKey, {\n      currentColor\n    });\n    expose({\n      color,\n      show,\n      hide,\n      focus,\n      blur\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElTooltip), {\n        ref_key: \"popper\",\n        ref: popper,\n        visible: showPicker.value,\n        \"show-arrow\": false,\n        \"fallback-placements\": [\"bottom\", \"top\", \"right\", \"left\"],\n        offset: 0,\n        \"gpu-acceleration\": false,\n        \"popper-class\": [unref(ns).be(\"picker\", \"panel\"), unref(ns).b(\"dropdown\"), _ctx.popperClass],\n        \"stop-popper-mouse-event\": false,\n        effect: \"light\",\n        trigger: \"click\",\n        teleported: _ctx.teleported,\n        transition: `${unref(ns).namespace.value}-zoom-in-top`,\n        persistent: \"\",\n        onHide: $event => setShowPicker(false)\n      }, {\n        content: withCtx(() => [withDirectives((openBlock(), createElementBlock(\"div\", {\n          onKeydown: withKeys(handleEsc, [\"esc\"])\n        }, [createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).be(\"dropdown\", \"main-wrapper\"))\n        }, [createVNode(HueSlider, {\n          ref_key: \"hue\",\n          ref: hue,\n          class: \"hue-slider\",\n          color: unref(color),\n          vertical: \"\"\n        }, null, 8, [\"color\"]), createVNode(SvPanel, {\n          ref_key: \"sv\",\n          ref: sv,\n          color: unref(color)\n        }, null, 8, [\"color\"])], 2), _ctx.showAlpha ? (openBlock(), createBlock(AlphaSlider, {\n          key: 0,\n          ref_key: \"alpha\",\n          ref: alpha,\n          color: unref(color)\n        }, null, 8, [\"color\"])) : createCommentVNode(\"v-if\", true), _ctx.predefine ? (openBlock(), createBlock(Predefine, {\n          key: 1,\n          ref: \"predefine\",\n          \"enable-alpha\": _ctx.showAlpha,\n          color: unref(color),\n          colors: _ctx.predefine\n        }, null, 8, [\"enable-alpha\", \"color\", \"colors\"])) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).be(\"dropdown\", \"btns\"))\n        }, [createElementVNode(\"span\", {\n          class: normalizeClass(unref(ns).be(\"dropdown\", \"value\"))\n        }, [createVNode(unref(ElInput), {\n          ref_key: \"inputRef\",\n          ref: inputRef,\n          modelValue: customInput.value,\n          \"onUpdate:modelValue\": $event => customInput.value = $event,\n          \"validate-event\": false,\n          size: \"small\",\n          onKeyup: withKeys(handleConfirm, [\"enter\"]),\n          onBlur: handleConfirm\n        }, null, 8, [\"modelValue\", \"onUpdate:modelValue\", \"onKeyup\"])], 2), createVNode(unref(ElButton), {\n          class: normalizeClass(unref(ns).be(\"dropdown\", \"link-btn\")),\n          text: \"\",\n          size: \"small\",\n          onClick: clear\n        }, {\n          default: withCtx(() => [createTextVNode(toDisplayString(unref(t)(\"el.colorpicker.clear\")), 1)]),\n          _: 1\n        }, 8, [\"class\"]), createVNode(unref(ElButton), {\n          plain: \"\",\n          size: \"small\",\n          class: normalizeClass(unref(ns).be(\"dropdown\", \"btn\")),\n          onClick: confirmValue\n        }, {\n          default: withCtx(() => [createTextVNode(toDisplayString(unref(t)(\"el.colorpicker.confirm\")), 1)]),\n          _: 1\n        }, 8, [\"class\"])], 2)], 40, [\"onKeydown\"])), [[unref(ClickOutside), handleClickOutside, triggerRef.value]])]),\n        default: withCtx(() => [createElementVNode(\"div\", mergeProps({\n          id: unref(buttonId),\n          ref_key: \"triggerRef\",\n          ref: triggerRef\n        }, _ctx.$attrs, {\n          class: unref(btnKls),\n          role: \"button\",\n          \"aria-label\": unref(buttonAriaLabel),\n          \"aria-labelledby\": unref(buttonAriaLabelledby),\n          \"aria-description\": unref(t)(\"el.colorpicker.description\", {\n            color: _ctx.modelValue || \"\"\n          }),\n          \"aria-disabled\": unref(colorDisabled),\n          tabindex: unref(colorDisabled) ? void 0 : _ctx.tabindex,\n          onKeydown: handleKeyDown,\n          onFocus: unref(handleFocus),\n          onBlur: unref(handleBlur)\n        }), [unref(colorDisabled) ? (openBlock(), createElementBlock(\"div\", {\n          key: 0,\n          class: normalizeClass(unref(ns).be(\"picker\", \"mask\"))\n        }, null, 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).be(\"picker\", \"trigger\")),\n          onClick: handleTrigger\n        }, [createElementVNode(\"span\", {\n          class: normalizeClass([unref(ns).be(\"picker\", \"color\"), unref(ns).is(\"alpha\", _ctx.showAlpha)])\n        }, [createElementVNode(\"span\", {\n          class: normalizeClass(unref(ns).be(\"picker\", \"color-inner\")),\n          style: normalizeStyle({\n            backgroundColor: unref(displayedColor)\n          })\n        }, [withDirectives(createVNode(unref(ElIcon), {\n          class: normalizeClass([unref(ns).be(\"picker\", \"icon\"), unref(ns).is(\"icon-arrow-down\")])\n        }, {\n          default: withCtx(() => [createVNode(unref(ArrowDown))]),\n          _: 1\n        }, 8, [\"class\"]), [[vShow, _ctx.modelValue || showPanelColor.value]]), withDirectives(createVNode(unref(ElIcon), {\n          class: normalizeClass([unref(ns).be(\"picker\", \"empty\"), unref(ns).is(\"icon-close\")])\n        }, {\n          default: withCtx(() => [createVNode(unref(Close))]),\n          _: 1\n        }, 8, [\"class\"]), [[vShow, !_ctx.modelValue && !showPanelColor.value]])], 6)], 2)], 2)], 16, [\"id\", \"aria-label\", \"aria-labelledby\", \"aria-description\", \"aria-disabled\", \"tabindex\", \"onFocus\", \"onBlur\"])]),\n        _: 1\n      }, 8, [\"visible\", \"popper-class\", \"teleported\", \"transition\", \"onHide\"]);\n    };\n  }\n});\nvar ColorPicker = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"color-picker.vue\"]]);\nexport { ColorPicker as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}