{"ast": null, "code": "import toInteger from './toInteger.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * The opposite of `_.before`; this method creates a function that invokes\n * `func` once it's called `n` or more times.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {number} n The number of calls before `func` is invoked.\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new restricted function.\n * @example\n *\n * var saves = ['profile', 'settings'];\n *\n * var done = _.after(saves.length, function() {\n *   console.log('done saving!');\n * });\n *\n * _.forEach(saves, function(type) {\n *   asyncSave({ 'type': type, 'complete': done });\n * });\n * // => Logs 'done saving!' after the two async saves have completed.\n */\nfunction after(n, func) {\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  n = toInteger(n);\n  return function () {\n    if (--n < 1) {\n      return func.apply(this, arguments);\n    }\n  };\n}\nexport default after;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}