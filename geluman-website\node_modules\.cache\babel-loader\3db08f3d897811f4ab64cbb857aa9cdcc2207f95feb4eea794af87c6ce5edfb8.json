{"ast": null, "code": "import notify from './src/notify.mjs';\nexport { notificationEmits, notificationProps, notificationTypes } from './src/notification.mjs';\nimport { withInstallFunction } from '../../utils/vue/install.mjs';\nconst ElNotification = withInstallFunction(notify, \"$notify\");\nexport { ElNotification, ElNotification as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}