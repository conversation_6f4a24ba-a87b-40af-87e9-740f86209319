{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, inject, watch } from 'vue';\nimport { selectKey } from '../../select/src/token.mjs';\nimport { isClient } from '@vueuse/core';\nvar CacheOptions = defineComponent({\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    }\n  },\n  setup(props) {\n    const select = inject(selectKey);\n    watch(() => props.data, () => {\n      var _a;\n      props.data.forEach(item => {\n        if (!select.states.cachedOptions.has(item.value)) {\n          select.states.cachedOptions.set(item.value, item);\n        }\n      });\n      const inputs = ((_a = select.selectRef) == null ? void 0 : _a.querySelectorAll(\"input\")) || [];\n      if (isClient && !Array.from(inputs).includes(document.activeElement)) {\n        select.setSelected();\n      }\n    }, {\n      flush: \"post\",\n      immediate: true\n    });\n    return () => void 0;\n  }\n});\nexport { CacheOptions as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}