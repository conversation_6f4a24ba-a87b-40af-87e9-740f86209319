{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, renderSlot, createVNode, Transition, withCtx, withDirectives, createElementVNode, normalizeStyle, createTextVNode, toDisplayString, vShow } from 'vue';\nimport { badgeProps } from './badge.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nconst __default__ = defineComponent({\n  name: \"ElBadge\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: badgeProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"badge\");\n    const content = computed(() => {\n      if (props.isDot) return \"\";\n      if (isNumber(props.value) && isNumber(props.max)) {\n        return props.max < props.value ? `${props.max}+` : `${props.value}`;\n      }\n      return `${props.value}`;\n    });\n    const style = computed(() => {\n      var _a, _b, _c, _d, _e;\n      return [{\n        backgroundColor: props.color,\n        marginRight: addUnit(-((_b = (_a = props.offset) == null ? void 0 : _a[0]) != null ? _b : 0)),\n        marginTop: addUnit((_d = (_c = props.offset) == null ? void 0 : _c[1]) != null ? _d : 0)\n      }, (_e = props.badgeStyle) != null ? _e : {}];\n    });\n    expose({\n      content\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(ns).b())\n      }, [renderSlot(_ctx.$slots, \"default\"), createVNode(Transition, {\n        name: `${unref(ns).namespace.value}-zoom-in-center`,\n        persisted: \"\"\n      }, {\n        default: withCtx(() => [withDirectives(createElementVNode(\"sup\", {\n          class: normalizeClass([unref(ns).e(\"content\"), unref(ns).em(\"content\", _ctx.type), unref(ns).is(\"fixed\", !!_ctx.$slots.default), unref(ns).is(\"dot\", _ctx.isDot), unref(ns).is(\"hide-zero\", !_ctx.showZero && props.value === 0), _ctx.badgeClass]),\n          style: normalizeStyle(unref(style))\n        }, [renderSlot(_ctx.$slots, \"content\", {\n          value: unref(content)\n        }, () => [createTextVNode(toDisplayString(unref(content)), 1)])], 6), [[vShow, !_ctx.hidden && (unref(content) || _ctx.isDot || _ctx.$slots.content)]])]),\n        _: 3\n      }, 8, [\"name\"])], 2);\n    };\n  }\n});\nvar Badge = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"badge.vue\"]]);\nexport { Badge as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}