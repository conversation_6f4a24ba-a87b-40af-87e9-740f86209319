{"ast": null, "code": "import { defineComponent, computed, openBlock, createBlock, Transition, unref, withCtx, createElementBlock, normalizeStyle, normalizeClass, withModifiers, renderSlot, createVNode, createCommentVNode } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { CaretTop } from '@element-plus/icons-vue';\nimport { backtopProps, backtopEmits } from './backtop2.mjs';\nimport { useBackTop } from './use-backtop.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst COMPONENT_NAME = \"ElBacktop\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: backtopProps,\n  emits: backtopEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"backtop\");\n    const {\n      handleClick,\n      visible\n    } = useBackTop(props, emit, COMPONENT_NAME);\n    const backTopStyle = computed(() => ({\n      right: `${props.right}px`,\n      bottom: `${props.bottom}px`\n    }));\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(Transition, {\n        name: `${unref(ns).namespace.value}-fade-in`\n      }, {\n        default: withCtx(() => [unref(visible) ? (openBlock(), createElementBlock(\"div\", {\n          key: 0,\n          style: normalizeStyle(unref(backTopStyle)),\n          class: normalizeClass(unref(ns).b()),\n          onClick: withModifiers(unref(handleClick), [\"stop\"])\n        }, [renderSlot(_ctx.$slots, \"default\", {}, () => [createVNode(unref(ElIcon), {\n          class: normalizeClass(unref(ns).e(\"icon\"))\n        }, {\n          default: withCtx(() => [createVNode(unref(CaretTop))]),\n          _: 1\n        }, 8, [\"class\"])])], 14, [\"onClick\"])) : createCommentVNode(\"v-if\", true)]),\n        _: 3\n      }, 8, [\"name\"]);\n    };\n  }\n});\nvar Backtop = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"backtop.vue\"]]);\nexport { Backtop as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}