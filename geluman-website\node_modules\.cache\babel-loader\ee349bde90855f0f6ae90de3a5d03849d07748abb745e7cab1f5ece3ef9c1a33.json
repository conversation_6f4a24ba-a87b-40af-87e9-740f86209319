{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, inject, ref, computed, unref, watch, onMounted, provide, openBlock, createElementBlock, normalizeStyle, normalizeClass, renderSlot, createVNode, withCtx, Fragment, createTextVNode, toDisplayString, createCommentVNode } from 'vue';\nimport { offset } from '@floating-ui/dom';\nimport ElVisuallyHidden from '../../visual-hidden/src/visual-hidden2.mjs';\nimport { tooltipV2RootKey, tooltipV2ContentKey } from './constants.mjs';\nimport { tooltipV2ContentProps } from './content.mjs';\nimport { tooltipV2CommonProps } from './common.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useFloating, arrowMiddleware } from '../../../hooks/use-floating/index.mjs';\nimport { useZIndex } from '../../../hooks/use-z-index/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTooltipV2Content\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: {\n    ...tooltipV2ContentProps,\n    ...tooltipV2CommonProps\n  },\n  setup(__props) {\n    const props = __props;\n    const {\n      triggerRef,\n      contentId\n    } = inject(tooltipV2RootKey);\n    const placement = ref(props.placement);\n    const strategy = ref(props.strategy);\n    const arrowRef = ref(null);\n    const {\n      referenceRef,\n      contentRef,\n      middlewareData,\n      x,\n      y,\n      update\n    } = useFloating({\n      placement,\n      strategy,\n      middleware: computed(() => {\n        const middleware = [offset(props.offset)];\n        if (props.showArrow) {\n          middleware.push(arrowMiddleware({\n            arrowRef\n          }));\n        }\n        return middleware;\n      })\n    });\n    const zIndex = useZIndex().nextZIndex();\n    const ns = useNamespace(\"tooltip-v2\");\n    const side = computed(() => {\n      return placement.value.split(\"-\")[0];\n    });\n    const contentStyle = computed(() => {\n      return {\n        position: unref(strategy),\n        top: `${unref(y) || 0}px`,\n        left: `${unref(x) || 0}px`,\n        zIndex\n      };\n    });\n    const arrowStyle = computed(() => {\n      if (!props.showArrow) return {};\n      const {\n        arrow\n      } = unref(middlewareData);\n      return {\n        [`--${ns.namespace.value}-tooltip-v2-arrow-x`]: `${arrow == null ? void 0 : arrow.x}px` || \"\",\n        [`--${ns.namespace.value}-tooltip-v2-arrow-y`]: `${arrow == null ? void 0 : arrow.y}px` || \"\"\n      };\n    });\n    const contentClass = computed(() => [ns.e(\"content\"), ns.is(\"dark\", props.effect === \"dark\"), ns.is(unref(strategy)), props.contentClass]);\n    watch(arrowRef, () => update());\n    watch(() => props.placement, val => placement.value = val);\n    onMounted(() => {\n      watch(() => props.reference || triggerRef.value, el => {\n        referenceRef.value = el || void 0;\n      }, {\n        immediate: true\n      });\n    });\n    provide(tooltipV2ContentKey, {\n      arrowRef\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"contentRef\",\n        ref: contentRef,\n        style: normalizeStyle(unref(contentStyle)),\n        \"data-tooltip-v2-root\": \"\"\n      }, [!_ctx.nowrap ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        \"data-side\": unref(side),\n        class: normalizeClass(unref(contentClass))\n      }, [renderSlot(_ctx.$slots, \"default\", {\n        contentStyle: unref(contentStyle),\n        contentClass: unref(contentClass)\n      }), createVNode(unref(ElVisuallyHidden), {\n        id: unref(contentId),\n        role: \"tooltip\"\n      }, {\n        default: withCtx(() => [_ctx.ariaLabel ? (openBlock(), createElementBlock(Fragment, {\n          key: 0\n        }, [createTextVNode(toDisplayString(_ctx.ariaLabel), 1)], 64)) : renderSlot(_ctx.$slots, \"default\", {\n          key: 1\n        })]),\n        _: 3\n      }, 8, [\"id\"]), renderSlot(_ctx.$slots, \"arrow\", {\n        style: normalizeStyle(unref(arrowStyle)),\n        side: unref(side)\n      })], 10, [\"data-side\"])) : createCommentVNode(\"v-if\", true)], 4);\n    };\n  }\n});\nvar TooltipV2Content = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"content.vue\"]]);\nexport { TooltipV2Content as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}