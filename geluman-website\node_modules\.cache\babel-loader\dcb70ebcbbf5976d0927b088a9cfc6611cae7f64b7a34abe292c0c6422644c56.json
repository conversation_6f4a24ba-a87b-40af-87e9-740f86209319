{"ast": null, "code": "import { defineComponent, computed, ref, watch, onMounted, openBlock, createElementBlock, normalizeClass, unref, withModifiers, createElementVNode, with<PERSON><PERSON><PERSON>, createBlock, withCtx, resolveDynamicComponent, createCommentVNode, toDisplayString, normalizeStyle, createVNode, renderSlot, nextTick } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { Loading } from '@element-plus/icons-vue';\nimport { switchProps, switchEmits } from './switch.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useFormItem, useFormItemInputId } from '../../form/src/hooks/use-form-item.mjs';\nimport { useFormSize, useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT, INPUT_EVENT } from '../../../constants/event.mjs';\nimport { debugWarn, throwError } from '../../../utils/error.mjs';\nimport { isPromise } from '@vue/shared';\nimport { isBoolean } from '../../../utils/types.mjs';\nconst COMPONENT_NAME = \"ElSwitch\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: switchProps,\n  emits: switchEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const {\n      formItem\n    } = useFormItem();\n    const switchSize = useFormSize();\n    const ns = useNamespace(\"switch\");\n    const {\n      inputId\n    } = useFormItemInputId(props, {\n      formItemContext: formItem\n    });\n    const switchDisabled = useFormDisabled(computed(() => props.loading));\n    const isControlled = ref(props.modelValue !== false);\n    const input = ref();\n    const core = ref();\n    const switchKls = computed(() => [ns.b(), ns.m(switchSize.value), ns.is(\"disabled\", switchDisabled.value), ns.is(\"checked\", checked.value)]);\n    const labelLeftKls = computed(() => [ns.e(\"label\"), ns.em(\"label\", \"left\"), ns.is(\"active\", !checked.value)]);\n    const labelRightKls = computed(() => [ns.e(\"label\"), ns.em(\"label\", \"right\"), ns.is(\"active\", checked.value)]);\n    const coreStyle = computed(() => ({\n      width: addUnit(props.width)\n    }));\n    watch(() => props.modelValue, () => {\n      isControlled.value = true;\n    });\n    const actualValue = computed(() => {\n      return isControlled.value ? props.modelValue : false;\n    });\n    const checked = computed(() => actualValue.value === props.activeValue);\n    if (![props.activeValue, props.inactiveValue].includes(actualValue.value)) {\n      emit(UPDATE_MODEL_EVENT, props.inactiveValue);\n      emit(CHANGE_EVENT, props.inactiveValue);\n      emit(INPUT_EVENT, props.inactiveValue);\n    }\n    watch(checked, val => {\n      var _a;\n      input.value.checked = val;\n      if (props.validateEvent) {\n        (_a = formItem == null ? void 0 : formItem.validate) == null ? void 0 : _a.call(formItem, \"change\").catch(err => debugWarn(err));\n      }\n    });\n    const handleChange = () => {\n      const val = checked.value ? props.inactiveValue : props.activeValue;\n      emit(UPDATE_MODEL_EVENT, val);\n      emit(CHANGE_EVENT, val);\n      emit(INPUT_EVENT, val);\n      nextTick(() => {\n        input.value.checked = checked.value;\n      });\n    };\n    const switchValue = () => {\n      if (switchDisabled.value) return;\n      const {\n        beforeChange\n      } = props;\n      if (!beforeChange) {\n        handleChange();\n        return;\n      }\n      const shouldChange = beforeChange();\n      const isPromiseOrBool = [isPromise(shouldChange), isBoolean(shouldChange)].includes(true);\n      if (!isPromiseOrBool) {\n        throwError(COMPONENT_NAME, \"beforeChange must return type `Promise<boolean>` or `boolean`\");\n      }\n      if (isPromise(shouldChange)) {\n        shouldChange.then(result => {\n          if (result) {\n            handleChange();\n          }\n        }).catch(e => {\n          debugWarn(COMPONENT_NAME, `some error occurred: ${e}`);\n        });\n      } else if (shouldChange) {\n        handleChange();\n      }\n    };\n    const focus = () => {\n      var _a, _b;\n      (_b = (_a = input.value) == null ? void 0 : _a.focus) == null ? void 0 : _b.call(_a);\n    };\n    onMounted(() => {\n      input.value.checked = checked.value;\n    });\n    expose({\n      focus,\n      checked\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(switchKls)),\n        onClick: withModifiers(switchValue, [\"prevent\"])\n      }, [createElementVNode(\"input\", {\n        id: unref(inputId),\n        ref_key: \"input\",\n        ref: input,\n        class: normalizeClass(unref(ns).e(\"input\")),\n        type: \"checkbox\",\n        role: \"switch\",\n        \"aria-checked\": unref(checked),\n        \"aria-disabled\": unref(switchDisabled),\n        \"aria-label\": _ctx.ariaLabel,\n        name: _ctx.name,\n        \"true-value\": _ctx.activeValue,\n        \"false-value\": _ctx.inactiveValue,\n        disabled: unref(switchDisabled),\n        tabindex: _ctx.tabindex,\n        onChange: handleChange,\n        onKeydown: withKeys(switchValue, [\"enter\"])\n      }, null, 42, [\"id\", \"aria-checked\", \"aria-disabled\", \"aria-label\", \"name\", \"true-value\", \"false-value\", \"disabled\", \"tabindex\", \"onKeydown\"]), !_ctx.inlinePrompt && (_ctx.inactiveIcon || _ctx.inactiveText) ? (openBlock(), createElementBlock(\"span\", {\n        key: 0,\n        class: normalizeClass(unref(labelLeftKls))\n      }, [_ctx.inactiveIcon ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.inactiveIcon)))]),\n        _: 1\n      })) : createCommentVNode(\"v-if\", true), !_ctx.inactiveIcon && _ctx.inactiveText ? (openBlock(), createElementBlock(\"span\", {\n        key: 1,\n        \"aria-hidden\": unref(checked)\n      }, toDisplayString(_ctx.inactiveText), 9, [\"aria-hidden\"])) : createCommentVNode(\"v-if\", true)], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"span\", {\n        ref_key: \"core\",\n        ref: core,\n        class: normalizeClass(unref(ns).e(\"core\")),\n        style: normalizeStyle(unref(coreStyle))\n      }, [_ctx.inlinePrompt ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"inner\"))\n      }, [_ctx.activeIcon || _ctx.inactiveIcon ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0,\n        class: normalizeClass(unref(ns).is(\"icon\"))\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(checked) ? _ctx.activeIcon : _ctx.inactiveIcon)))]),\n        _: 1\n      }, 8, [\"class\"])) : _ctx.activeText || _ctx.inactiveText ? (openBlock(), createElementBlock(\"span\", {\n        key: 1,\n        class: normalizeClass(unref(ns).is(\"text\")),\n        \"aria-hidden\": !unref(checked)\n      }, toDisplayString(unref(checked) ? _ctx.activeText : _ctx.inactiveText), 11, [\"aria-hidden\"])) : createCommentVNode(\"v-if\", true)], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"action\"))\n      }, [_ctx.loading ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0,\n        class: normalizeClass(unref(ns).is(\"loading\"))\n      }, {\n        default: withCtx(() => [createVNode(unref(Loading))]),\n        _: 1\n      }, 8, [\"class\"])) : unref(checked) ? renderSlot(_ctx.$slots, \"active-action\", {\n        key: 1\n      }, () => [_ctx.activeActionIcon ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.activeActionIcon)))]),\n        _: 1\n      })) : createCommentVNode(\"v-if\", true)]) : !unref(checked) ? renderSlot(_ctx.$slots, \"inactive-action\", {\n        key: 2\n      }, () => [_ctx.inactiveActionIcon ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.inactiveActionIcon)))]),\n        _: 1\n      })) : createCommentVNode(\"v-if\", true)]) : createCommentVNode(\"v-if\", true)], 2)], 6), !_ctx.inlinePrompt && (_ctx.activeIcon || _ctx.activeText) ? (openBlock(), createElementBlock(\"span\", {\n        key: 1,\n        class: normalizeClass(unref(labelRightKls))\n      }, [_ctx.activeIcon ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.activeIcon)))]),\n        _: 1\n      })) : createCommentVNode(\"v-if\", true), !_ctx.activeIcon && _ctx.activeText ? (openBlock(), createElementBlock(\"span\", {\n        key: 1,\n        \"aria-hidden\": !unref(checked)\n      }, toDisplayString(_ctx.activeText), 9, [\"aria-hidden\"])) : createCommentVNode(\"v-if\", true)], 2)) : createCommentVNode(\"v-if\", true)], 10, [\"onClick\"]);\n    };\n  }\n});\nvar Switch = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"switch.vue\"]]);\nexport { Switch as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}