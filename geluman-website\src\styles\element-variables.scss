@forward "element-plus/theme-chalk/src/common/var.scss" with (
  $colors: (
    "primary": (
      "base": #6366f1,
    ),
    "success": (
      "base": #10b981,
    ),
    "warning": (
      "base": #f59e0b,
    ),
    "danger": (
      "base": #ef4444,
    ),
    "info": (
      "base": #3b82f6,
    ),
  )
);

/* 主题颜色变量 */
:root {
  // 主色调：科技紫
  --primary-color: #8B5CF6;  // 主色调
  --primary-light: #A78BFA;  // 亮色调
  --primary-dark: #7C3AED;   // 暗色调
  
  // 辅助色
  --accent-color: #F4A259;   // 温暖的橙色
  --accent-light: #FFB98A;
  
  // 文本颜色
  --text-primary: #1E1B4B;   // 主要文字
  --text-secondary: #6B7280; // 次要文字
  
  // 背景色
  --bg-primary: #ffffff;     // 主背景
  --bg-secondary: #F5F3FF;   // 次要背景
  --bg-accent: #EDE9FE;      // 强调背景
  
  // 其他
  --header-height: 64px;
  --transition-base: all 0.3s ease;
  --shadow-sm: 0 2px 4px rgba(139, 92, 246, 0.05);
  --shadow-md: 0 4px 6px rgba(139, 92, 246, 0.1);
  --shadow-lg: 0 10px 15px rgba(139, 92, 246, 0.1);
}

/* Element Plus 样式覆盖 */
.el-button--primary {
  --el-button-bg-color: var(--primary-color);
  --el-button-border-color: var(--primary-color);
  --el-button-hover-bg-color: var(--primary-light);
  --el-button-hover-border-color: var(--primary-light);
}
