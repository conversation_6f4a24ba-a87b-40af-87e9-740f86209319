{"ast": null, "code": "import toString from './toString.js';\n\n/**\n * Replaces matches for `pattern` in `string` with `replacement`.\n *\n * **Note:** This method is based on\n * [`String#replace`](https://mdn.io/String/replace).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to modify.\n * @param {RegExp|string} pattern The pattern to replace.\n * @param {Function|string} replacement The match replacement.\n * @returns {string} Returns the modified string.\n * @example\n *\n * _.replace('<PERSON> <PERSON>', '<PERSON>', '<PERSON>');\n * // => 'Hi Barney'\n */\nfunction replace() {\n  var args = arguments,\n    string = toString(args[0]);\n  return args.length < 3 ? string : string.replace(args[1], args[2]);\n}\nexport default replace;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}