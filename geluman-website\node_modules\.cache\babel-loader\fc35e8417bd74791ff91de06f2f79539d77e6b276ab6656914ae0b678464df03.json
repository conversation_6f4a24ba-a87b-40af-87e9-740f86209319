{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { watch } from 'vue';\nimport { isNil } from 'lodash-unified';\nimport { useVModel } from '@vueuse/core';\nimport { genFileId } from './upload.mjs';\nimport { debugWarn, throwError } from '../../../utils/error.mjs';\nconst SCOPE = \"ElUpload\";\nconst revokeFileObjectURL = file => {\n  var _a;\n  if ((_a = file.url) == null ? void 0 : _a.startsWith(\"blob:\")) {\n    URL.revokeObjectURL(file.url);\n  }\n};\nconst useHandlers = (props, uploadRef) => {\n  const uploadFiles = useVModel(props, \"fileList\", void 0, {\n    passive: true\n  });\n  const getFile = rawFile => uploadFiles.value.find(file => file.uid === rawFile.uid);\n  function abort(file) {\n    var _a;\n    (_a = uploadRef.value) == null ? void 0 : _a.abort(file);\n  }\n  function clearFiles(states = [\"ready\", \"uploading\", \"success\", \"fail\"]) {\n    uploadFiles.value = uploadFiles.value.filter(row => !states.includes(row.status));\n  }\n  function removeFile(file) {\n    uploadFiles.value = uploadFiles.value.filter(uploadFile => uploadFile.uid !== file.uid);\n  }\n  const handleError = (err, rawFile) => {\n    const file = getFile(rawFile);\n    if (!file) return;\n    console.error(err);\n    file.status = \"fail\";\n    removeFile(file);\n    props.onError(err, file, uploadFiles.value);\n    props.onChange(file, uploadFiles.value);\n  };\n  const handleProgress = (evt, rawFile) => {\n    const file = getFile(rawFile);\n    if (!file) return;\n    props.onProgress(evt, file, uploadFiles.value);\n    file.status = \"uploading\";\n    file.percentage = Math.round(evt.percent);\n  };\n  const handleSuccess = (response, rawFile) => {\n    const file = getFile(rawFile);\n    if (!file) return;\n    file.status = \"success\";\n    file.response = response;\n    props.onSuccess(response, file, uploadFiles.value);\n    props.onChange(file, uploadFiles.value);\n  };\n  const handleStart = file => {\n    if (isNil(file.uid)) file.uid = genFileId();\n    const uploadFile = {\n      name: file.name,\n      percentage: 0,\n      status: \"ready\",\n      size: file.size,\n      raw: file,\n      uid: file.uid\n    };\n    if (props.listType === \"picture-card\" || props.listType === \"picture\") {\n      try {\n        uploadFile.url = URL.createObjectURL(file);\n      } catch (err) {\n        debugWarn(SCOPE, err.message);\n        props.onError(err, uploadFile, uploadFiles.value);\n      }\n    }\n    uploadFiles.value = [...uploadFiles.value, uploadFile];\n    props.onChange(uploadFile, uploadFiles.value);\n  };\n  const handleRemove = async file => {\n    const uploadFile = file instanceof File ? getFile(file) : file;\n    if (!uploadFile) throwError(SCOPE, \"file to be removed not found\");\n    const doRemove = file2 => {\n      abort(file2);\n      removeFile(file2);\n      props.onRemove(file2, uploadFiles.value);\n      revokeFileObjectURL(file2);\n    };\n    if (props.beforeRemove) {\n      const before = await props.beforeRemove(uploadFile, uploadFiles.value);\n      if (before !== false) doRemove(uploadFile);\n    } else {\n      doRemove(uploadFile);\n    }\n  };\n  function submit() {\n    uploadFiles.value.filter(({\n      status\n    }) => status === \"ready\").forEach(({\n      raw\n    }) => {\n      var _a;\n      return raw && ((_a = uploadRef.value) == null ? void 0 : _a.upload(raw));\n    });\n  }\n  watch(() => props.listType, val => {\n    if (val !== \"picture-card\" && val !== \"picture\") {\n      return;\n    }\n    uploadFiles.value = uploadFiles.value.map(file => {\n      const {\n        raw,\n        url\n      } = file;\n      if (!url && raw) {\n        try {\n          file.url = URL.createObjectURL(raw);\n        } catch (err) {\n          props.onError(err, file, uploadFiles.value);\n        }\n      }\n      return file;\n    });\n  });\n  watch(uploadFiles, files => {\n    for (const file of files) {\n      file.uid || (file.uid = genFileId());\n      file.status || (file.status = \"success\");\n    }\n  }, {\n    immediate: true,\n    deep: true\n  });\n  return {\n    uploadFiles,\n    abort,\n    clearFiles,\n    handleError,\n    handleProgress,\n    handleStart,\n    handleSuccess,\n    handleRemove,\n    submit,\n    revokeFileObjectURL\n  };\n};\nexport { useHandlers };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}