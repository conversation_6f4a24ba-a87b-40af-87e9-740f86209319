{"ast": null, "code": "import { definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nconst classType = String;\nconst columns = {\n  type: definePropType(Array),\n  required: true\n};\nconst column = {\n  type: definePropType(Object)\n};\nconst fixedDataType = {\n  type: definePropType(Array)\n};\nconst dataType = {\n  ...fixedDataType,\n  required: true\n};\nconst expandColumnKey = String;\nconst expandKeys = {\n  type: definePropType(Array),\n  default: () => mutable([])\n};\nconst requiredNumber = {\n  type: Number,\n  required: true\n};\nconst rowKey = {\n  type: definePropType([String, Number, Symbol]),\n  default: \"id\"\n};\nconst styleType = {\n  type: definePropType(Object)\n};\nexport { classType, column, columns, dataType, expandColumnKey, expandKeys, fixedDataType, requiredNumber, rowKey, styleType };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}