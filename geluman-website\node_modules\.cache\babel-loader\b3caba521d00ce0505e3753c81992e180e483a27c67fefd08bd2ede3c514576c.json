{"ast": null, "code": "import { defineComponent, getCurrentInstance, ref, computed, watch, onMounted, openBlock, createElementBlock, normalizeClass, normalizeStyle, createElementVNode } from 'vue';\nimport { draggable } from '../utils/draggable.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { getClientXY } from '../../../../utils/dom/position.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElSlPanel\",\n  props: {\n    color: {\n      type: Object,\n      required: true\n    }\n  },\n  setup(props) {\n    const ns = useNamespace(\"color-svpanel\");\n    const instance = getCurrentInstance();\n    const cursorTop = ref(0);\n    const cursorLeft = ref(0);\n    const background = ref(\"hsl(0, 100%, 50%)\");\n    const colorValue = computed(() => {\n      const hue = props.color.get(\"hue\");\n      const value = props.color.get(\"value\");\n      return {\n        hue,\n        value\n      };\n    });\n    function update() {\n      const saturation = props.color.get(\"saturation\");\n      const value = props.color.get(\"value\");\n      const el = instance.vnode.el;\n      const {\n        clientWidth: width,\n        clientHeight: height\n      } = el;\n      cursorLeft.value = saturation * width / 100;\n      cursorTop.value = (100 - value) * height / 100;\n      background.value = `hsl(${props.color.get(\"hue\")}, 100%, 50%)`;\n    }\n    function handleDrag(event) {\n      const el = instance.vnode.el;\n      const rect = el.getBoundingClientRect();\n      const {\n        clientX,\n        clientY\n      } = getClientXY(event);\n      let left = clientX - rect.left;\n      let top = clientY - rect.top;\n      left = Math.max(0, left);\n      left = Math.min(left, rect.width);\n      top = Math.max(0, top);\n      top = Math.min(top, rect.height);\n      cursorLeft.value = left;\n      cursorTop.value = top;\n      props.color.set({\n        saturation: left / rect.width * 100,\n        value: 100 - top / rect.height * 100\n      });\n    }\n    watch(() => colorValue.value, () => {\n      update();\n    });\n    onMounted(() => {\n      draggable(instance.vnode.el, {\n        drag: event => {\n          handleDrag(event);\n        },\n        end: event => {\n          handleDrag(event);\n        }\n      });\n      update();\n    });\n    return {\n      cursorTop,\n      cursorLeft,\n      background,\n      colorValue,\n      handleDrag,\n      update,\n      ns\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"div\", {\n    class: normalizeClass(_ctx.ns.b()),\n    style: normalizeStyle({\n      backgroundColor: _ctx.background\n    })\n  }, [createElementVNode(\"div\", {\n    class: normalizeClass(_ctx.ns.e(\"white\"))\n  }, null, 2), createElementVNode(\"div\", {\n    class: normalizeClass(_ctx.ns.e(\"black\"))\n  }, null, 2), createElementVNode(\"div\", {\n    class: normalizeClass(_ctx.ns.e(\"cursor\")),\n    style: normalizeStyle({\n      top: _ctx.cursorTop + \"px\",\n      left: _ctx.cursorLeft + \"px\"\n    })\n  }, [createElementVNode(\"div\")], 6)], 6);\n}\nvar SvPanel = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"sv-panel.vue\"]]);\nexport { SvPanel as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}