{"ast": null, "code": "import { createElementVNode as _createElementVNode, unref as _unref, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"not-found\"\n};\nconst _hoisted_2 = {\n  class: \"content\"\n};\nimport { HomeFilled } from \"@element-plus/icons-vue\";\nexport default {\n  __name: 'NotFound',\n  setup(__props) {\n    return (_ctx, _cache) => {\n      const _component_el_icon = _resolveComponent(\"el-icon\");\n      const _component_router_link = _resolveComponent(\"router-link\");\n      return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[1] || (_cache[1] = _createElementVNode(\"h1\", {\n        class: \"title\"\n      }, \"404\", -1)), _cache[2] || (_cache[2] = _createElementVNode(\"p\", {\n        class: \"subtitle\"\n      }, \"抱歉，您访问的页面不存在\", -1)), _createVNode(_component_router_link, {\n        to: \"/\",\n        class: \"back-home\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_unref(HomeFilled))]),\n          _: 1\n        }), _cache[0] || (_cache[0] = _createTextVNode(\" 返回首页 \"))]),\n        _: 1\n      })]), _cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n        class: \"background-animation\"\n      }, null, -1))]);\n    };\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}