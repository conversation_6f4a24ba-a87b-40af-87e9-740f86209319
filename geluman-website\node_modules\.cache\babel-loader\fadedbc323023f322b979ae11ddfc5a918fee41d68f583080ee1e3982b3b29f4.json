{"ast": null, "code": "import { computed, getCurrentInstance } from 'vue';\nimport { memoize } from 'lodash-unified';\nimport memoOne from 'memoize-one';\nconst useCache = () => {\n  const vm = getCurrentInstance();\n  const props = vm.proxy.$props;\n  return computed(() => {\n    const _getItemStyleCache = (_, __, ___) => ({});\n    return props.perfMode ? memoize(_getItemStyleCache) : memoOne(_getItemStyleCache);\n  });\n};\nexport { useCache };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}