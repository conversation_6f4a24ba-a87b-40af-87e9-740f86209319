{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport apply from './_apply.js';\nimport baseRest from './_baseRest.js';\nimport customDefaultsMerge from './_customDefaultsMerge.js';\nimport mergeWith from './mergeWith.js';\n\n/**\n * This method is like `_.defaults` except that it recursively assigns\n * default properties.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 3.10.0\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} [sources] The source objects.\n * @returns {Object} Returns `object`.\n * @see _.defaults\n * @example\n *\n * _.defaultsDeep({ 'a': { 'b': 2 } }, { 'a': { 'b': 1, 'c': 3 } });\n * // => { 'a': { 'b': 2, 'c': 3 } }\n */\nvar defaultsDeep = baseRest(function (args) {\n  args.push(undefined, customDefaultsMerge);\n  return apply(mergeWith, undefined, args);\n});\nexport default defaultsDeep;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}