{"ast": null, "code": "import { isClient } from '@vueuse/core';\nconst isInContainer = (el, container) => {\n  if (!isClient || !el || !container) return false;\n  const elRect = el.getBoundingClientRect();\n  let containerRect;\n  if (container instanceof Element) {\n    containerRect = container.getBoundingClientRect();\n  } else {\n    containerRect = {\n      top: 0,\n      right: window.innerWidth,\n      bottom: window.innerHeight,\n      left: 0\n    };\n  }\n  return elRect.top < containerRect.bottom && elRect.bottom > containerRect.top && elRect.right > containerRect.left && elRect.left < containerRect.right;\n};\nconst getOffsetTop = el => {\n  let offset = 0;\n  let parent = el;\n  while (parent) {\n    offset += parent.offsetTop;\n    parent = parent.offsetParent;\n  }\n  return offset;\n};\nconst getOffsetTopDistance = (el, containerEl) => {\n  return Math.abs(getOffsetTop(el) - getOffsetTop(containerEl));\n};\nconst getClientXY = event => {\n  let clientX;\n  let clientY;\n  if (event.type === \"touchend\") {\n    clientY = event.changedTouches[0].clientY;\n    clientX = event.changedTouches[0].clientX;\n  } else if (event.type.startsWith(\"touch\")) {\n    clientY = event.touches[0].clientY;\n    clientX = event.touches[0].clientX;\n  } else {\n    clientY = event.clientY;\n    clientX = event.clientX;\n  }\n  return {\n    clientX,\n    clientY\n  };\n};\nexport { getClientXY, getOffsetTop, getOffsetTopDistance, isInContainer };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}