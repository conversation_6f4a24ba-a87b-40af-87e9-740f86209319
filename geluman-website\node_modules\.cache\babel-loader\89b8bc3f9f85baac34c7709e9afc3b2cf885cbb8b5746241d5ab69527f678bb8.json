{"ast": null, "code": "import { defineComponent, inject, h, renderSlot } from 'vue';\nimport { ElText } from '../../text/index.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElTreeNodeContent\",\n  props: {\n    node: {\n      type: Object,\n      required: true\n    },\n    renderContent: Function\n  },\n  setup(props) {\n    const ns = useNamespace(\"tree\");\n    const nodeInstance = inject(\"NodeInstance\");\n    const tree = inject(\"RootTree\");\n    return () => {\n      const node = props.node;\n      const {\n        data,\n        store\n      } = node;\n      return props.renderContent ? props.renderContent(h, {\n        _self: nodeInstance,\n        node,\n        data,\n        store\n      }) : renderSlot(tree.ctx.slots, \"default\", {\n        node,\n        data\n      }, () => [h(ElText, {\n        tag: \"span\",\n        truncated: true,\n        class: ns.be(\"node\", \"label\")\n      }, () => [node.label])]);\n    };\n  }\n});\nvar NodeContent = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tree-node-content.vue\"]]);\nexport { NodeContent as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}