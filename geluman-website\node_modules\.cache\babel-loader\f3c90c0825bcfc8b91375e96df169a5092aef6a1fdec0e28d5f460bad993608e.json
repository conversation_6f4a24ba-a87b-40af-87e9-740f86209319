{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { inject, computed, h } from 'vue';\nimport { merge } from 'lodash-unified';\nimport { getRowIdentity } from '../util.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport useEvents from './events-helper.mjs';\nimport useStyles from './styles-helper.mjs';\nimport TdWrapper from './td-wrapper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isBoolean, isPropAbsent } from '../../../../utils/types.mjs';\nfunction useRender(props) {\n  const parent = inject(TABLE_INJECTION_KEY);\n  const ns = useNamespace(\"table\");\n  const {\n    handleDoubleClick,\n    handleClick,\n    handleContextMenu,\n    handleMouseEnter,\n    handleMouseLeave,\n    handleCellMouseEnter,\n    handleCellMouseLeave,\n    tooltipContent,\n    tooltipTrigger\n  } = useEvents(props);\n  const {\n    getRowStyle,\n    getRowClass,\n    getCellStyle,\n    getCellClass,\n    getSpan,\n    getColspanRealWidth\n  } = useStyles(props);\n  const firstDefaultColumnIndex = computed(() => {\n    return props.store.states.columns.value.findIndex(({\n      type\n    }) => type === \"default\");\n  });\n  const getKeyOfRow = (row, index) => {\n    const rowKey = parent.props.rowKey;\n    if (rowKey) {\n      return getRowIdentity(row, rowKey);\n    }\n    return index;\n  };\n  const rowRender = (row, $index, treeRowData, expanded = false) => {\n    const {\n      tooltipEffect,\n      tooltipOptions,\n      store\n    } = props;\n    const {\n      indent,\n      columns\n    } = store.states;\n    const rowClasses = getRowClass(row, $index);\n    let display = true;\n    if (treeRowData) {\n      rowClasses.push(ns.em(\"row\", `level-${treeRowData.level}`));\n      display = treeRowData.display;\n    }\n    const displayStyle = display ? null : {\n      display: \"none\"\n    };\n    return h(\"tr\", {\n      style: [displayStyle, getRowStyle(row, $index)],\n      class: rowClasses,\n      key: getKeyOfRow(row, $index),\n      onDblclick: $event => handleDoubleClick($event, row),\n      onClick: $event => handleClick($event, row),\n      onContextmenu: $event => handleContextMenu($event, row),\n      onMouseenter: () => handleMouseEnter($index),\n      onMouseleave: handleMouseLeave\n    }, columns.value.map((column, cellIndex) => {\n      const {\n        rowspan,\n        colspan\n      } = getSpan(row, column, $index, cellIndex);\n      if (!rowspan || !colspan) {\n        return null;\n      }\n      const columnData = Object.assign({}, column);\n      columnData.realWidth = getColspanRealWidth(columns.value, colspan, cellIndex);\n      const data = {\n        store: props.store,\n        _self: props.context || parent,\n        column: columnData,\n        row,\n        $index,\n        cellIndex,\n        expanded\n      };\n      if (cellIndex === firstDefaultColumnIndex.value && treeRowData) {\n        data.treeNode = {\n          indent: treeRowData.level * indent.value,\n          level: treeRowData.level\n        };\n        if (isBoolean(treeRowData.expanded)) {\n          data.treeNode.expanded = treeRowData.expanded;\n          if (\"loading\" in treeRowData) {\n            data.treeNode.loading = treeRowData.loading;\n          }\n          if (\"noLazyChildren\" in treeRowData) {\n            data.treeNode.noLazyChildren = treeRowData.noLazyChildren;\n          }\n        }\n      }\n      const baseKey = `${getKeyOfRow(row, $index)},${cellIndex}`;\n      const patchKey = columnData.columnKey || columnData.rawColumnKey || \"\";\n      const mergedTooltipOptions = column.showOverflowTooltip && merge({\n        effect: tooltipEffect\n      }, tooltipOptions, column.showOverflowTooltip);\n      return h(TdWrapper, {\n        style: getCellStyle($index, cellIndex, row, column),\n        class: getCellClass($index, cellIndex, row, column, colspan - 1),\n        key: `${patchKey}${baseKey}`,\n        rowspan,\n        colspan,\n        onMouseenter: $event => handleCellMouseEnter($event, row, mergedTooltipOptions),\n        onMouseleave: handleCellMouseLeave\n      }, {\n        default: () => cellChildren(cellIndex, column, data)\n      });\n    }));\n  };\n  const cellChildren = (cellIndex, column, data) => {\n    return column.renderCell(data);\n  };\n  const wrappedRowRender = (row, $index) => {\n    const store = props.store;\n    const {\n      isRowExpanded,\n      assertRowKey\n    } = store;\n    const {\n      treeData,\n      lazyTreeNodeMap,\n      childrenColumnName,\n      rowKey\n    } = store.states;\n    const columns = store.states.columns.value;\n    const hasExpandColumn = columns.some(({\n      type\n    }) => type === \"expand\");\n    if (hasExpandColumn) {\n      const expanded = isRowExpanded(row);\n      const tr = rowRender(row, $index, void 0, expanded);\n      const renderExpanded = parent.renderExpanded;\n      if (!renderExpanded) {\n        console.error(\"[Element Error]renderExpanded is required.\");\n        return tr;\n      }\n      const rows = [[tr]];\n      if (parent.props.preserveExpandedContent || expanded) {\n        rows[0].push(h(\"tr\", {\n          key: `expanded-row__${tr.key}`,\n          style: {\n            display: expanded ? \"\" : \"none\"\n          }\n        }, [h(\"td\", {\n          colspan: columns.length,\n          class: `${ns.e(\"cell\")} ${ns.e(\"expanded-cell\")}`\n        }, [renderExpanded({\n          row,\n          $index,\n          store,\n          expanded\n        })])]));\n      }\n      return rows;\n    } else if (Object.keys(treeData.value).length) {\n      assertRowKey();\n      const key = getRowIdentity(row, rowKey.value);\n      let cur = treeData.value[key];\n      let treeRowData = null;\n      if (cur) {\n        treeRowData = {\n          expanded: cur.expanded,\n          level: cur.level,\n          display: true\n        };\n        if (isBoolean(cur.lazy)) {\n          if (isBoolean(cur.loaded) && cur.loaded) {\n            treeRowData.noLazyChildren = !(cur.children && cur.children.length);\n          }\n          treeRowData.loading = cur.loading;\n        }\n      }\n      const tmp = [rowRender(row, $index, treeRowData)];\n      if (cur) {\n        let i = 0;\n        const traverse = (children, parent2) => {\n          if (!(children && children.length && parent2)) return;\n          children.forEach(node => {\n            const innerTreeRowData = {\n              display: parent2.display && parent2.expanded,\n              level: parent2.level + 1,\n              expanded: false,\n              noLazyChildren: false,\n              loading: false\n            };\n            const childKey = getRowIdentity(node, rowKey.value);\n            if (isPropAbsent(childKey)) {\n              throw new Error(\"For nested data item, row-key is required.\");\n            }\n            cur = {\n              ...treeData.value[childKey]\n            };\n            if (cur) {\n              innerTreeRowData.expanded = cur.expanded;\n              cur.level = cur.level || innerTreeRowData.level;\n              cur.display = !!(cur.expanded && innerTreeRowData.display);\n              if (isBoolean(cur.lazy)) {\n                if (isBoolean(cur.loaded) && cur.loaded) {\n                  innerTreeRowData.noLazyChildren = !(cur.children && cur.children.length);\n                }\n                innerTreeRowData.loading = cur.loading;\n              }\n            }\n            i++;\n            tmp.push(rowRender(node, $index + i, innerTreeRowData));\n            if (cur) {\n              const nodes2 = lazyTreeNodeMap.value[childKey] || node[childrenColumnName.value];\n              traverse(nodes2, cur);\n            }\n          });\n        };\n        cur.display = true;\n        const nodes = lazyTreeNodeMap.value[key] || row[childrenColumnName.value];\n        traverse(nodes, cur);\n      }\n      return tmp;\n    } else {\n      return rowRender(row, $index, void 0);\n    }\n  };\n  return {\n    wrappedRowRender,\n    tooltipContent,\n    tooltipTrigger\n  };\n}\nexport { useRender as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}