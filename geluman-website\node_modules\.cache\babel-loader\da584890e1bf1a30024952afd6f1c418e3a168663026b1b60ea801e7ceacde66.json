{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { isLeaf } from '../../../utils/dom/aria.mjs';\nconst getMenuIndex = el => {\n  if (!el) return 0;\n  const pieces = el.id.split(\"-\");\n  return Number(pieces[pieces.length - 2]);\n};\nconst checkNode = el => {\n  if (!el) return;\n  const input = el.querySelector(\"input\");\n  if (input) {\n    input.click();\n  } else if (isLeaf(el)) {\n    el.click();\n  }\n};\nconst sortByOriginalOrder = (oldNodes, newNodes) => {\n  const newNodesCopy = newNodes.slice(0);\n  const newIds = newNodesCopy.map(node => node.uid);\n  const res = oldNodes.reduce((acc, item) => {\n    const index = newIds.indexOf(item.uid);\n    if (index > -1) {\n      acc.push(item);\n      newNodesCopy.splice(index, 1);\n      newIds.splice(index, 1);\n    }\n    return acc;\n  }, []);\n  res.push(...newNodesCopy);\n  return res;\n};\nexport { checkNode, getMenuIndex, sortByOriginalOrder };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}