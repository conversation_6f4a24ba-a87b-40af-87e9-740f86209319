{"ast": null, "code": "import { computed, onBeforeMount } from 'vue';\nimport { useGetDerivedNamespace } from '../use-namespace/index.mjs';\nimport { useIdInjection } from '../use-id/index.mjs';\nimport { isClient } from '@vueuse/core';\nconst usePopperContainerId = () => {\n  const namespace = useGetDerivedNamespace();\n  const idInjection = useIdInjection();\n  const id = computed(() => {\n    return `${namespace.value}-popper-container-${idInjection.prefix}`;\n  });\n  const selector = computed(() => `#${id.value}`);\n  return {\n    id,\n    selector\n  };\n};\nconst createContainer = id => {\n  const container = document.createElement(\"div\");\n  container.id = id;\n  document.body.appendChild(container);\n  return container;\n};\nconst usePopperContainer = () => {\n  const {\n    id,\n    selector\n  } = usePopperContainerId();\n  onBeforeMount(() => {\n    if (!isClient) return;\n    if (process.env.NODE_ENV === \"test\" || !document.body.querySelector(selector.value)) {\n      createContainer(id.value);\n    }\n  });\n  return {\n    id,\n    selector\n  };\n};\nexport { usePopperContainer, usePopperContainerId };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}