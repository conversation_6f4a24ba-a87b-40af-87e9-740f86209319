{"ast": null, "code": "import baseSlice from './_baseSlice.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Creates a slice of `array` with `n` elements dropped from the beginning.\n *\n * @static\n * @memberOf _\n * @since 0.5.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {number} [n=1] The number of elements to drop.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * _.drop([1, 2, 3]);\n * // => [2, 3]\n *\n * _.drop([1, 2, 3], 2);\n * // => [3]\n *\n * _.drop([1, 2, 3], 5);\n * // => []\n *\n * _.drop([1, 2, 3], 0);\n * // => [1, 2, 3]\n */\nfunction drop(array, n, guard) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return [];\n  }\n  n = guard || n === undefined ? 1 : toInteger(n);\n  return baseSlice(array, n < 0 ? 0 : n, length);\n}\nexport default drop;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}