{"ast": null, "code": "/**\n * This method invokes `interceptor` and returns `value`. The interceptor\n * is invoked with one argument; (value). The purpose of this method is to\n * \"tap into\" a method chain sequence in order to modify intermediate results.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Seq\n * @param {*} value The value to provide to `interceptor`.\n * @param {Function} interceptor The function to invoke.\n * @returns {*} Returns `value`.\n * @example\n *\n * _([1, 2, 3])\n *  .tap(function(array) {\n *    // Mutate input array.\n *    array.pop();\n *  })\n *  .reverse()\n *  .value();\n * // => [2, 1]\n */\nfunction tap(value, interceptor) {\n  interceptor(value);\n  return value;\n}\nexport default tap;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}