{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { createVNode, render, isVNode } from 'vue';\nimport { merge, get, flatMap, isNull } from 'lodash-unified';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { isArray, isString, isFunction, hasOwn, isObject } from '@vue/shared';\nimport { throwError } from '../../../utils/error.mjs';\nimport { isUndefined, isNumber, isBoolean } from '../../../utils/types.mjs';\nimport { getProp } from '../../../utils/objects.mjs';\nconst getCell = function (event) {\n  var _a;\n  return (_a = event.target) == null ? void 0 : _a.closest(\"td\");\n};\nconst orderBy = function (array, sortKey, reverse, sortMethod, sortBy) {\n  if (!sortKey && !sortMethod && (!sortBy || isArray(sortBy) && !sortBy.length)) {\n    return array;\n  }\n  if (isString(reverse)) {\n    reverse = reverse === \"descending\" ? -1 : 1;\n  } else {\n    reverse = reverse && reverse < 0 ? -1 : 1;\n  }\n  const getKey = sortMethod ? null : function (value, index) {\n    if (sortBy) {\n      if (!isArray(sortBy)) {\n        sortBy = [sortBy];\n      }\n      return sortBy.map(by => {\n        if (isString(by)) {\n          return get(value, by);\n        } else {\n          return by(value, index, array);\n        }\n      });\n    }\n    if (sortKey !== \"$key\") {\n      if (isObject(value) && \"$value\" in value) value = value.$value;\n    }\n    return [isObject(value) ? get(value, sortKey) : value];\n  };\n  const compare = function (a, b) {\n    if (sortMethod) {\n      return sortMethod(a.value, b.value);\n    }\n    for (let i = 0, len = a.key.length; i < len; i++) {\n      if (a.key[i] < b.key[i]) {\n        return -1;\n      }\n      if (a.key[i] > b.key[i]) {\n        return 1;\n      }\n    }\n    return 0;\n  };\n  return array.map((value, index) => {\n    return {\n      value,\n      index,\n      key: getKey ? getKey(value, index) : null\n    };\n  }).sort((a, b) => {\n    let order = compare(a, b);\n    if (!order) {\n      order = a.index - b.index;\n    }\n    return order * +reverse;\n  }).map(item => item.value);\n};\nconst getColumnById = function (table, columnId) {\n  let column = null;\n  table.columns.forEach(item => {\n    if (item.id === columnId) {\n      column = item;\n    }\n  });\n  return column;\n};\nconst getColumnByKey = function (table, columnKey) {\n  let column = null;\n  for (let i = 0; i < table.columns.length; i++) {\n    const item = table.columns[i];\n    if (item.columnKey === columnKey) {\n      column = item;\n      break;\n    }\n  }\n  if (!column) throwError(\"ElTable\", `No column matching with column-key: ${columnKey}`);\n  return column;\n};\nconst getColumnByCell = function (table, cell, namespace) {\n  const matches = (cell.className || \"\").match(new RegExp(`${namespace}-table_[^\\\\s]+`, \"gm\"));\n  if (matches) {\n    return getColumnById(table, matches[0]);\n  }\n  return null;\n};\nconst getRowIdentity = (row, rowKey) => {\n  if (!row) throw new Error(\"Row is required when get row identity\");\n  if (isString(rowKey)) {\n    if (!rowKey.includes(\".\")) {\n      return `${row[rowKey]}`;\n    }\n    const key = rowKey.split(\".\");\n    let current = row;\n    for (const element of key) {\n      current = current[element];\n    }\n    return `${current}`;\n  } else if (isFunction(rowKey)) {\n    return rowKey.call(null, row);\n  }\n};\nconst getKeysMap = function (array, rowKey, flatten = false, childrenKey = \"children\") {\n  const data = array || [];\n  const arrayMap = {};\n  data.forEach((row, index) => {\n    arrayMap[getRowIdentity(row, rowKey)] = {\n      row,\n      index\n    };\n    if (flatten) {\n      const children = row[childrenKey];\n      if (isArray(children)) {\n        Object.assign(arrayMap, getKeysMap(children, rowKey, true, childrenKey));\n      }\n    }\n  });\n  return arrayMap;\n};\nfunction mergeOptions(defaults, config) {\n  const options = {};\n  let key;\n  for (key in defaults) {\n    options[key] = defaults[key];\n  }\n  for (key in config) {\n    if (hasOwn(config, key)) {\n      const value = config[key];\n      if (!isUndefined(value)) {\n        options[key] = value;\n      }\n    }\n  }\n  return options;\n}\nfunction parseWidth(width) {\n  if (width === \"\") return width;\n  if (!isUndefined(width)) {\n    width = Number.parseInt(width, 10);\n    if (Number.isNaN(width)) {\n      width = \"\";\n    }\n  }\n  return width;\n}\nfunction parseMinWidth(minWidth) {\n  if (minWidth === \"\") return minWidth;\n  if (!isUndefined(minWidth)) {\n    minWidth = parseWidth(minWidth);\n    if (Number.isNaN(minWidth)) {\n      minWidth = 80;\n    }\n  }\n  return minWidth;\n}\nfunction parseHeight(height) {\n  if (isNumber(height)) {\n    return height;\n  }\n  if (isString(height)) {\n    if (/^\\d+(?:px)?$/.test(height)) {\n      return Number.parseInt(height, 10);\n    } else {\n      return height;\n    }\n  }\n  return null;\n}\nfunction compose(...funcs) {\n  if (funcs.length === 0) {\n    return arg => arg;\n  }\n  if (funcs.length === 1) {\n    return funcs[0];\n  }\n  return funcs.reduce((a, b) => (...args) => a(b(...args)));\n}\nfunction toggleRowStatus(statusArr, row, newVal, tableTreeProps, selectable, rowIndex) {\n  let _rowIndex = rowIndex != null ? rowIndex : 0;\n  let changed = false;\n  const index = statusArr.indexOf(row);\n  const included = index !== -1;\n  const isRowSelectable = selectable == null ? void 0 : selectable.call(null, row, _rowIndex);\n  const toggleStatus = type => {\n    if (type === \"add\") {\n      statusArr.push(row);\n    } else {\n      statusArr.splice(index, 1);\n    }\n    changed = true;\n  };\n  const getChildrenCount = row2 => {\n    let count = 0;\n    const children = (tableTreeProps == null ? void 0 : tableTreeProps.children) && row2[tableTreeProps.children];\n    if (children && isArray(children)) {\n      count += children.length;\n      children.forEach(item => {\n        count += getChildrenCount(item);\n      });\n    }\n    return count;\n  };\n  if (!selectable || isRowSelectable) {\n    if (isBoolean(newVal)) {\n      if (newVal && !included) {\n        toggleStatus(\"add\");\n      } else if (!newVal && included) {\n        toggleStatus(\"remove\");\n      }\n    } else {\n      included ? toggleStatus(\"remove\") : toggleStatus(\"add\");\n    }\n  }\n  if (!(tableTreeProps == null ? void 0 : tableTreeProps.checkStrictly) && (tableTreeProps == null ? void 0 : tableTreeProps.children) && isArray(row[tableTreeProps.children])) {\n    row[tableTreeProps.children].forEach(item => {\n      const childChanged = toggleRowStatus(statusArr, item, newVal != null ? newVal : !included, tableTreeProps, selectable, _rowIndex + 1);\n      _rowIndex += getChildrenCount(item) + 1;\n      if (childChanged) {\n        changed = childChanged;\n      }\n    });\n  }\n  return changed;\n}\nfunction walkTreeNode(root, cb, childrenKey = \"children\", lazyKey = \"hasChildren\") {\n  const isNil = array => !(isArray(array) && array.length);\n  function _walker(parent, children, level) {\n    cb(parent, children, level);\n    children.forEach(item => {\n      if (item[lazyKey]) {\n        cb(item, null, level + 1);\n        return;\n      }\n      const children2 = item[childrenKey];\n      if (!isNil(children2)) {\n        _walker(item, children2, level + 1);\n      }\n    });\n  }\n  root.forEach(item => {\n    if (item[lazyKey]) {\n      cb(item, null, 0);\n      return;\n    }\n    const children = item[childrenKey];\n    if (!isNil(children)) {\n      _walker(item, children, 0);\n    }\n  });\n}\nconst getTableOverflowTooltipProps = (props, innerText, row, column) => {\n  const popperOptions = {\n    strategy: \"fixed\",\n    ...props.popperOptions\n  };\n  const tooltipFormatterContent = isFunction(column.tooltipFormatter) ? column.tooltipFormatter({\n    row,\n    column,\n    cellValue: getProp(row, column.property).value\n  }) : void 0;\n  if (isVNode(tooltipFormatterContent)) {\n    return {\n      slotContent: tooltipFormatterContent,\n      content: null,\n      ...props,\n      popperOptions\n    };\n  }\n  return {\n    slotContent: null,\n    content: tooltipFormatterContent != null ? tooltipFormatterContent : innerText,\n    ...props,\n    popperOptions\n  };\n};\nlet removePopper = null;\nfunction createTablePopper(props, popperContent, row, column, trigger, table) {\n  const tableOverflowTooltipProps = getTableOverflowTooltipProps(props, popperContent, row, column);\n  const mergedProps = {\n    ...tableOverflowTooltipProps,\n    slotContent: void 0\n  };\n  if ((removePopper == null ? void 0 : removePopper.trigger) === trigger) {\n    const comp = removePopper.vm.component;\n    merge(comp.props, mergedProps);\n    if (tableOverflowTooltipProps.slotContent) {\n      comp.slots.content = () => [tableOverflowTooltipProps.slotContent];\n    }\n    return;\n  }\n  removePopper == null ? void 0 : removePopper();\n  const parentNode = table == null ? void 0 : table.refs.tableWrapper;\n  const ns = parentNode == null ? void 0 : parentNode.dataset.prefix;\n  const vm = createVNode(ElTooltip, {\n    virtualTriggering: true,\n    virtualRef: trigger,\n    appendTo: parentNode,\n    placement: \"top\",\n    transition: \"none\",\n    offset: 0,\n    hideAfter: 0,\n    ...mergedProps\n  }, tableOverflowTooltipProps.slotContent ? {\n    content: () => tableOverflowTooltipProps.slotContent\n  } : void 0);\n  vm.appContext = {\n    ...table.appContext,\n    ...table\n  };\n  const container = document.createElement(\"div\");\n  render(vm, container);\n  vm.component.exposed.onOpen();\n  const scrollContainer = parentNode == null ? void 0 : parentNode.querySelector(`.${ns}-scrollbar__wrap`);\n  removePopper = () => {\n    render(null, container);\n    scrollContainer == null ? void 0 : scrollContainer.removeEventListener(\"scroll\", removePopper);\n    removePopper = null;\n  };\n  removePopper.trigger = trigger;\n  removePopper.vm = vm;\n  scrollContainer == null ? void 0 : scrollContainer.addEventListener(\"scroll\", removePopper);\n}\nfunction getCurrentColumns(column) {\n  if (column.children) {\n    return flatMap(column.children, getCurrentColumns);\n  } else {\n    return [column];\n  }\n}\nfunction getColSpan(colSpan, column) {\n  return colSpan + column.colSpan;\n}\nconst isFixedColumn = (index, fixed, store, realColumns) => {\n  let start = 0;\n  let after = index;\n  const columns = store.states.columns.value;\n  if (realColumns) {\n    const curColumns = getCurrentColumns(realColumns[index]);\n    const preColumns = columns.slice(0, columns.indexOf(curColumns[0]));\n    start = preColumns.reduce(getColSpan, 0);\n    after = start + curColumns.reduce(getColSpan, 0) - 1;\n  } else {\n    start = index;\n  }\n  let fixedLayout;\n  switch (fixed) {\n    case \"left\":\n      if (after < store.states.fixedLeafColumnsLength.value) {\n        fixedLayout = \"left\";\n      }\n      break;\n    case \"right\":\n      if (start >= columns.length - store.states.rightFixedLeafColumnsLength.value) {\n        fixedLayout = \"right\";\n      }\n      break;\n    default:\n      if (after < store.states.fixedLeafColumnsLength.value) {\n        fixedLayout = \"left\";\n      } else if (start >= columns.length - store.states.rightFixedLeafColumnsLength.value) {\n        fixedLayout = \"right\";\n      }\n  }\n  return fixedLayout ? {\n    direction: fixedLayout,\n    start,\n    after\n  } : {};\n};\nconst getFixedColumnsClass = (namespace, index, fixed, store, realColumns, offset = 0) => {\n  const classes = [];\n  const {\n    direction,\n    start,\n    after\n  } = isFixedColumn(index, fixed, store, realColumns);\n  if (direction) {\n    const isLeft = direction === \"left\";\n    classes.push(`${namespace}-fixed-column--${direction}`);\n    if (isLeft && after + offset === store.states.fixedLeafColumnsLength.value - 1) {\n      classes.push(\"is-last-column\");\n    } else if (!isLeft && start - offset === store.states.columns.value.length - store.states.rightFixedLeafColumnsLength.value) {\n      classes.push(\"is-first-column\");\n    }\n  }\n  return classes;\n};\nfunction getOffset(offset, column) {\n  return offset + (isNull(column.realWidth) || Number.isNaN(column.realWidth) ? Number(column.width) : column.realWidth);\n}\nconst getFixedColumnOffset = (index, fixed, store, realColumns) => {\n  const {\n    direction,\n    start = 0,\n    after = 0\n  } = isFixedColumn(index, fixed, store, realColumns);\n  if (!direction) {\n    return;\n  }\n  const styles = {};\n  const isLeft = direction === \"left\";\n  const columns = store.states.columns.value;\n  if (isLeft) {\n    styles.left = columns.slice(0, start).reduce(getOffset, 0);\n  } else {\n    styles.right = columns.slice(after + 1).reverse().reduce(getOffset, 0);\n  }\n  return styles;\n};\nconst ensurePosition = (style, key) => {\n  if (!style) return;\n  if (!Number.isNaN(style[key])) {\n    style[key] = `${style[key]}px`;\n  }\n};\nexport { compose, createTablePopper, ensurePosition, getCell, getColumnByCell, getColumnById, getColumnByKey, getFixedColumnOffset, getFixedColumnsClass, getKeysMap, getRowIdentity, isFixedColumn, mergeOptions, orderBy, parseHeight, parseMinWidth, parseWidth, removePopper, toggleRowStatus, walkTreeNode };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}