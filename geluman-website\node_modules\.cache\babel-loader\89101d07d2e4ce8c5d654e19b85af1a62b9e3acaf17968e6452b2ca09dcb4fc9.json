{"ast": null, "code": "import { defineComponent, ref, reactive, computed, watch, onMounted, onUpdated, openBlock, createElementBlock, normalizeClass, unref, withModifiers, withDirectives, withKeys, renderSlot, createVNode, withCtx, createBlock, createCommentVNode, createSlots } from 'vue';\nimport { isNil } from 'lodash-unified';\nimport { ElInput } from '../../input/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { ArrowDown, Minus, ArrowUp, Plus } from '@element-plus/icons-vue';\nimport { inputNumberProps, inputNumberEmits } from './input-number2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { vRepeatClick } from '../../../directives/repeat-click/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormItem } from '../../form/src/hooks/use-form-item.mjs';\nimport { isNumber, isUndefined } from '../../../utils/types.mjs';\nimport { debugWarn, throwError } from '../../../utils/error.mjs';\nimport { useFormSize, useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { UPDATE_MODEL_EVENT, INPUT_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { isString } from '@vue/shared';\nimport { isFirefox } from '../../../utils/browser.mjs';\nconst __default__ = defineComponent({\n  name: \"ElInputNumber\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: inputNumberProps,\n  emits: inputNumberEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"input-number\");\n    const input = ref();\n    const data = reactive({\n      currentValue: props.modelValue,\n      userInput: null\n    });\n    const {\n      formItem\n    } = useFormItem();\n    const minDisabled = computed(() => isNumber(props.modelValue) && props.modelValue <= props.min);\n    const maxDisabled = computed(() => isNumber(props.modelValue) && props.modelValue >= props.max);\n    const numPrecision = computed(() => {\n      const stepPrecision = getPrecision(props.step);\n      if (!isUndefined(props.precision)) {\n        if (stepPrecision > props.precision) {\n          debugWarn(\"InputNumber\", \"precision should not be less than the decimal places of step\");\n        }\n        return props.precision;\n      } else {\n        return Math.max(getPrecision(props.modelValue), stepPrecision);\n      }\n    });\n    const controlsAtRight = computed(() => {\n      return props.controls && props.controlsPosition === \"right\";\n    });\n    const inputNumberSize = useFormSize();\n    const inputNumberDisabled = useFormDisabled();\n    const displayValue = computed(() => {\n      if (data.userInput !== null) {\n        return data.userInput;\n      }\n      let currentValue = data.currentValue;\n      if (isNil(currentValue)) return \"\";\n      if (isNumber(currentValue)) {\n        if (Number.isNaN(currentValue)) return \"\";\n        if (!isUndefined(props.precision)) {\n          currentValue = currentValue.toFixed(props.precision);\n        }\n      }\n      return currentValue;\n    });\n    const toPrecision = (num, pre) => {\n      if (isUndefined(pre)) pre = numPrecision.value;\n      if (pre === 0) return Math.round(num);\n      let snum = String(num);\n      const pointPos = snum.indexOf(\".\");\n      if (pointPos === -1) return num;\n      const nums = snum.replace(\".\", \"\").split(\"\");\n      const datum = nums[pointPos + pre];\n      if (!datum) return num;\n      const length = snum.length;\n      if (snum.charAt(length - 1) === \"5\") {\n        snum = `${snum.slice(0, Math.max(0, length - 1))}6`;\n      }\n      return Number.parseFloat(Number(snum).toFixed(pre));\n    };\n    const getPrecision = value => {\n      if (isNil(value)) return 0;\n      const valueString = value.toString();\n      const dotPosition = valueString.indexOf(\".\");\n      let precision = 0;\n      if (dotPosition !== -1) {\n        precision = valueString.length - dotPosition - 1;\n      }\n      return precision;\n    };\n    const ensurePrecision = (val, coefficient = 1) => {\n      if (!isNumber(val)) return data.currentValue;\n      return toPrecision(val + props.step * coefficient);\n    };\n    const increase = () => {\n      if (props.readonly || inputNumberDisabled.value || maxDisabled.value) return;\n      const value = Number(displayValue.value) || 0;\n      const newVal = ensurePrecision(value);\n      setCurrentValue(newVal);\n      emit(INPUT_EVENT, data.currentValue);\n      setCurrentValueToModelValue();\n    };\n    const decrease = () => {\n      if (props.readonly || inputNumberDisabled.value || minDisabled.value) return;\n      const value = Number(displayValue.value) || 0;\n      const newVal = ensurePrecision(value, -1);\n      setCurrentValue(newVal);\n      emit(INPUT_EVENT, data.currentValue);\n      setCurrentValueToModelValue();\n    };\n    const verifyValue = (value, update) => {\n      const {\n        max,\n        min,\n        step,\n        precision,\n        stepStrictly,\n        valueOnClear\n      } = props;\n      if (max < min) {\n        throwError(\"InputNumber\", \"min should not be greater than max.\");\n      }\n      let newVal = Number(value);\n      if (isNil(value) || Number.isNaN(newVal)) {\n        return null;\n      }\n      if (value === \"\") {\n        if (valueOnClear === null) {\n          return null;\n        }\n        newVal = isString(valueOnClear) ? {\n          min,\n          max\n        }[valueOnClear] : valueOnClear;\n      }\n      if (stepStrictly) {\n        newVal = toPrecision(Math.round(newVal / step) * step, precision);\n        if (newVal !== value) {\n          update && emit(UPDATE_MODEL_EVENT, newVal);\n        }\n      }\n      if (!isUndefined(precision)) {\n        newVal = toPrecision(newVal, precision);\n      }\n      if (newVal > max || newVal < min) {\n        newVal = newVal > max ? max : min;\n        update && emit(UPDATE_MODEL_EVENT, newVal);\n      }\n      return newVal;\n    };\n    const setCurrentValue = (value, emitChange = true) => {\n      var _a;\n      const oldVal = data.currentValue;\n      const newVal = verifyValue(value);\n      if (!emitChange) {\n        emit(UPDATE_MODEL_EVENT, newVal);\n        return;\n      }\n      if (oldVal === newVal && value) return;\n      data.userInput = null;\n      emit(UPDATE_MODEL_EVENT, newVal);\n      if (oldVal !== newVal) {\n        emit(CHANGE_EVENT, newVal, oldVal);\n      }\n      if (props.validateEvent) {\n        (_a = formItem == null ? void 0 : formItem.validate) == null ? void 0 : _a.call(formItem, \"change\").catch(err => debugWarn(err));\n      }\n      data.currentValue = newVal;\n    };\n    const handleInput = value => {\n      data.userInput = value;\n      const newVal = value === \"\" ? null : Number(value);\n      emit(INPUT_EVENT, newVal);\n      setCurrentValue(newVal, false);\n    };\n    const handleInputChange = value => {\n      const newVal = value !== \"\" ? Number(value) : \"\";\n      if (isNumber(newVal) && !Number.isNaN(newVal) || value === \"\") {\n        setCurrentValue(newVal);\n      }\n      setCurrentValueToModelValue();\n      data.userInput = null;\n    };\n    const focus = () => {\n      var _a, _b;\n      (_b = (_a = input.value) == null ? void 0 : _a.focus) == null ? void 0 : _b.call(_a);\n    };\n    const blur = () => {\n      var _a, _b;\n      (_b = (_a = input.value) == null ? void 0 : _a.blur) == null ? void 0 : _b.call(_a);\n    };\n    const handleFocus = event => {\n      emit(\"focus\", event);\n    };\n    const handleBlur = event => {\n      var _a, _b;\n      data.userInput = null;\n      if (isFirefox() && data.currentValue === null && ((_a = input.value) == null ? void 0 : _a.input)) {\n        input.value.input.value = \"\";\n      }\n      emit(\"blur\", event);\n      if (props.validateEvent) {\n        (_b = formItem == null ? void 0 : formItem.validate) == null ? void 0 : _b.call(formItem, \"blur\").catch(err => debugWarn(err));\n      }\n    };\n    const setCurrentValueToModelValue = () => {\n      if (data.currentValue !== props.modelValue) {\n        data.currentValue = props.modelValue;\n      }\n    };\n    const handleWheel = e => {\n      if (document.activeElement === e.target) e.preventDefault();\n    };\n    watch(() => props.modelValue, (value, oldValue) => {\n      const newValue = verifyValue(value, true);\n      if (data.userInput === null && newValue !== oldValue) {\n        data.currentValue = newValue;\n      }\n    }, {\n      immediate: true\n    });\n    onMounted(() => {\n      var _a;\n      const {\n        min,\n        max,\n        modelValue\n      } = props;\n      const innerInput = (_a = input.value) == null ? void 0 : _a.input;\n      innerInput.setAttribute(\"role\", \"spinbutton\");\n      if (Number.isFinite(max)) {\n        innerInput.setAttribute(\"aria-valuemax\", String(max));\n      } else {\n        innerInput.removeAttribute(\"aria-valuemax\");\n      }\n      if (Number.isFinite(min)) {\n        innerInput.setAttribute(\"aria-valuemin\", String(min));\n      } else {\n        innerInput.removeAttribute(\"aria-valuemin\");\n      }\n      innerInput.setAttribute(\"aria-valuenow\", data.currentValue || data.currentValue === 0 ? String(data.currentValue) : \"\");\n      innerInput.setAttribute(\"aria-disabled\", String(inputNumberDisabled.value));\n      if (!isNumber(modelValue) && modelValue != null) {\n        let val = Number(modelValue);\n        if (Number.isNaN(val)) {\n          val = null;\n        }\n        emit(UPDATE_MODEL_EVENT, val);\n      }\n      innerInput.addEventListener(\"wheel\", handleWheel, {\n        passive: false\n      });\n    });\n    onUpdated(() => {\n      var _a, _b;\n      const innerInput = (_a = input.value) == null ? void 0 : _a.input;\n      innerInput == null ? void 0 : innerInput.setAttribute(\"aria-valuenow\", `${(_b = data.currentValue) != null ? _b : \"\"}`);\n    });\n    expose({\n      focus,\n      blur\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ns).b(), unref(ns).m(unref(inputNumberSize)), unref(ns).is(\"disabled\", unref(inputNumberDisabled)), unref(ns).is(\"without-controls\", !_ctx.controls), unref(ns).is(\"controls-right\", unref(controlsAtRight))]),\n        onDragstart: withModifiers(() => {}, [\"prevent\"])\n      }, [_ctx.controls ? withDirectives((openBlock(), createElementBlock(\"span\", {\n        key: 0,\n        role: \"button\",\n        \"aria-label\": unref(t)(\"el.inputNumber.decrease\"),\n        class: normalizeClass([unref(ns).e(\"decrease\"), unref(ns).is(\"disabled\", unref(minDisabled))]),\n        onKeydown: withKeys(decrease, [\"enter\"])\n      }, [renderSlot(_ctx.$slots, \"decrease-icon\", {}, () => [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [unref(controlsAtRight) ? (openBlock(), createBlock(unref(ArrowDown), {\n          key: 0\n        })) : (openBlock(), createBlock(unref(Minus), {\n          key: 1\n        }))]),\n        _: 1\n      })])], 42, [\"aria-label\", \"onKeydown\"])), [[unref(vRepeatClick), decrease]]) : createCommentVNode(\"v-if\", true), _ctx.controls ? withDirectives((openBlock(), createElementBlock(\"span\", {\n        key: 1,\n        role: \"button\",\n        \"aria-label\": unref(t)(\"el.inputNumber.increase\"),\n        class: normalizeClass([unref(ns).e(\"increase\"), unref(ns).is(\"disabled\", unref(maxDisabled))]),\n        onKeydown: withKeys(increase, [\"enter\"])\n      }, [renderSlot(_ctx.$slots, \"increase-icon\", {}, () => [createVNode(unref(ElIcon), null, {\n        default: withCtx(() => [unref(controlsAtRight) ? (openBlock(), createBlock(unref(ArrowUp), {\n          key: 0\n        })) : (openBlock(), createBlock(unref(Plus), {\n          key: 1\n        }))]),\n        _: 1\n      })])], 42, [\"aria-label\", \"onKeydown\"])), [[unref(vRepeatClick), increase]]) : createCommentVNode(\"v-if\", true), createVNode(unref(ElInput), {\n        id: _ctx.id,\n        ref_key: \"input\",\n        ref: input,\n        type: \"number\",\n        step: _ctx.step,\n        \"model-value\": unref(displayValue),\n        placeholder: _ctx.placeholder,\n        readonly: _ctx.readonly,\n        disabled: unref(inputNumberDisabled),\n        size: unref(inputNumberSize),\n        max: _ctx.max,\n        min: _ctx.min,\n        name: _ctx.name,\n        \"aria-label\": _ctx.ariaLabel,\n        \"validate-event\": false,\n        onKeydown: [withKeys(withModifiers(increase, [\"prevent\"]), [\"up\"]), withKeys(withModifiers(decrease, [\"prevent\"]), [\"down\"])],\n        onBlur: handleBlur,\n        onFocus: handleFocus,\n        onInput: handleInput,\n        onChange: handleInputChange\n      }, createSlots({\n        _: 2\n      }, [_ctx.$slots.prefix ? {\n        name: \"prefix\",\n        fn: withCtx(() => [renderSlot(_ctx.$slots, \"prefix\")])\n      } : void 0, _ctx.$slots.suffix ? {\n        name: \"suffix\",\n        fn: withCtx(() => [renderSlot(_ctx.$slots, \"suffix\")])\n      } : void 0]), 1032, [\"id\", \"step\", \"model-value\", \"placeholder\", \"readonly\", \"disabled\", \"size\", \"max\", \"min\", \"name\", \"aria-label\", \"onKeydown\"])], 42, [\"onDragstart\"]);\n    };\n  }\n});\nvar InputNumber = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"input-number.vue\"]]);\nexport { InputNumber as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}