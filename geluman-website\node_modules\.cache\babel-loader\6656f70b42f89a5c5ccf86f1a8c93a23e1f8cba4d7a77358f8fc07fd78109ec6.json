{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport dayjs from 'dayjs';\nimport { isArray, isString } from '@vue/shared';\nimport { rangeArr } from '../../time-picker/src/utils.mjs';\nconst isValidRange = range => {\n  if (!isArray(range)) return false;\n  const [left, right] = range;\n  return dayjs.isDayjs(left) && dayjs.isDayjs(right) && dayjs(left).isValid() && dayjs(right).isValid() && left.isSameOrBefore(right);\n};\nconst getDefaultValue = (defaultValue, {\n  lang,\n  unit,\n  unlinkPanels\n}) => {\n  let start;\n  if (isArray(defaultValue)) {\n    let [left, right] = defaultValue.map(d => dayjs(d).locale(lang));\n    if (!unlinkPanels) {\n      right = left.add(1, unit);\n    }\n    return [left, right];\n  } else if (defaultValue) {\n    start = dayjs(defaultValue);\n  } else {\n    start = dayjs();\n  }\n  start = start.locale(lang);\n  return [start, start.add(1, unit)];\n};\nconst buildPickerTable = (dimension, rows, {\n  columnIndexOffset,\n  startDate,\n  nextEndDate,\n  now,\n  unit,\n  relativeDateGetter,\n  setCellMetadata,\n  setRowMetadata\n}) => {\n  for (let rowIndex = 0; rowIndex < dimension.row; rowIndex++) {\n    const row = rows[rowIndex];\n    for (let columnIndex = 0; columnIndex < dimension.column; columnIndex++) {\n      let cell = row[columnIndex + columnIndexOffset];\n      if (!cell) {\n        cell = {\n          row: rowIndex,\n          column: columnIndex,\n          type: \"normal\",\n          inRange: false,\n          start: false,\n          end: false\n        };\n      }\n      const index = rowIndex * dimension.column + columnIndex;\n      const nextStartDate = relativeDateGetter(index);\n      cell.dayjs = nextStartDate;\n      cell.date = nextStartDate.toDate();\n      cell.timestamp = nextStartDate.valueOf();\n      cell.type = \"normal\";\n      cell.inRange = !!(startDate && nextStartDate.isSameOrAfter(startDate, unit) && nextEndDate && nextStartDate.isSameOrBefore(nextEndDate, unit)) || !!(startDate && nextStartDate.isSameOrBefore(startDate, unit) && nextEndDate && nextStartDate.isSameOrAfter(nextEndDate, unit));\n      if (startDate == null ? void 0 : startDate.isSameOrAfter(nextEndDate)) {\n        cell.start = !!nextEndDate && nextStartDate.isSame(nextEndDate, unit);\n        cell.end = startDate && nextStartDate.isSame(startDate, unit);\n      } else {\n        cell.start = !!startDate && nextStartDate.isSame(startDate, unit);\n        cell.end = !!nextEndDate && nextStartDate.isSame(nextEndDate, unit);\n      }\n      const isToday = nextStartDate.isSame(now, unit);\n      if (isToday) {\n        cell.type = \"today\";\n      }\n      setCellMetadata == null ? void 0 : setCellMetadata(cell, {\n        rowIndex,\n        columnIndex\n      });\n      row[columnIndex + columnIndexOffset] = cell;\n    }\n    setRowMetadata == null ? void 0 : setRowMetadata(row);\n  }\n};\nconst datesInMonth = (year, month, lang) => {\n  const firstDay = dayjs().locale(lang).startOf(\"month\").month(month).year(year);\n  const numOfDays = firstDay.daysInMonth();\n  return rangeArr(numOfDays).map(n => firstDay.add(n, \"day\").toDate());\n};\nconst getValidDateOfMonth = (year, month, lang, disabledDate) => {\n  const _value = dayjs().year(year).month(month).startOf(\"month\");\n  const _date = datesInMonth(year, month, lang).find(date => {\n    return !(disabledDate == null ? void 0 : disabledDate(date));\n  });\n  if (_date) {\n    return dayjs(_date).locale(lang);\n  }\n  return _value.locale(lang);\n};\nconst getValidDateOfYear = (value, lang, disabledDate) => {\n  const year = value.year();\n  if (!(disabledDate == null ? void 0 : disabledDate(value.toDate()))) {\n    return value.locale(lang);\n  }\n  const month = value.month();\n  if (!datesInMonth(year, month, lang).every(disabledDate)) {\n    return getValidDateOfMonth(year, month, lang, disabledDate);\n  }\n  for (let i = 0; i < 12; i++) {\n    if (!datesInMonth(year, i, lang).every(disabledDate)) {\n      return getValidDateOfMonth(year, i, lang, disabledDate);\n    }\n  }\n  return value;\n};\nconst correctlyParseUserInput = (value, format, lang, defaultFormat) => {\n  if (isArray(value)) {\n    return value.map(v => correctlyParseUserInput(v, format, lang, defaultFormat));\n  }\n  if (isString(value)) {\n    const dayjsValue = defaultFormat.value ? dayjs(value) : dayjs(value, format);\n    if (!dayjsValue.isValid()) {\n      return dayjsValue;\n    }\n  }\n  return dayjs(value, format).locale(lang);\n};\nexport { buildPickerTable, correctlyParseUserInput, datesInMonth, getDefaultValue, getValidDateOfMonth, getValidDateOfYear, isValidRange };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}