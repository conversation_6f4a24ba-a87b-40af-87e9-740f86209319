{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, mergeProps, unref, renderSlot } from 'vue';\nimport { iconProps } from './icon.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isUndefined } from '../../../utils/types.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nconst __default__ = defineComponent({\n  name: \"ElIcon\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: iconProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"icon\");\n    const style = computed(() => {\n      const {\n        size,\n        color\n      } = props;\n      if (!size && !color) return {};\n      return {\n        fontSize: isUndefined(size) ? void 0 : addUnit(size),\n        \"--color\": color\n      };\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"i\", mergeProps({\n        class: unref(ns).b(),\n        style: unref(style)\n      }, _ctx.$attrs), [renderSlot(_ctx.$slots, \"default\")], 16);\n    };\n  }\n});\nvar Icon = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"icon.vue\"]]);\nexport { Icon as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}