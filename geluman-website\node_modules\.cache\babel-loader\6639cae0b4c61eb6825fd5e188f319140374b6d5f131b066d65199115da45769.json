{"ast": null, "code": "import { ref, provide } from 'vue';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isFunction } from '@vue/shared';\nimport { removeClass, addClass } from '../../../../utils/dom/style.mjs';\nconst dragEventsKey = Symbol(\"dragEvents\");\nfunction useDragNodeHandler({\n  props,\n  ctx,\n  el$,\n  dropIndicator$,\n  store\n}) {\n  const ns = useNamespace(\"tree\");\n  const dragState = ref({\n    showDropIndicator: false,\n    draggingNode: null,\n    dropNode: null,\n    allowDrop: true,\n    dropType: null\n  });\n  const treeNodeDragStart = ({\n    event,\n    treeNode\n  }) => {\n    if (isFunction(props.allowDrag) && !props.allowDrag(treeNode.node)) {\n      event.preventDefault();\n      return false;\n    }\n    event.dataTransfer.effectAllowed = \"move\";\n    try {\n      event.dataTransfer.setData(\"text/plain\", \"\");\n    } catch (e) {}\n    dragState.value.draggingNode = treeNode;\n    ctx.emit(\"node-drag-start\", treeNode.node, event);\n  };\n  const treeNodeDragOver = ({\n    event,\n    treeNode\n  }) => {\n    const dropNode = treeNode;\n    const oldDropNode = dragState.value.dropNode;\n    if (oldDropNode && oldDropNode.node.id !== dropNode.node.id) {\n      removeClass(oldDropNode.$el, ns.is(\"drop-inner\"));\n    }\n    const draggingNode = dragState.value.draggingNode;\n    if (!draggingNode || !dropNode) return;\n    let dropPrev = true;\n    let dropInner = true;\n    let dropNext = true;\n    let userAllowDropInner = true;\n    if (isFunction(props.allowDrop)) {\n      dropPrev = props.allowDrop(draggingNode.node, dropNode.node, \"prev\");\n      userAllowDropInner = dropInner = props.allowDrop(draggingNode.node, dropNode.node, \"inner\");\n      dropNext = props.allowDrop(draggingNode.node, dropNode.node, \"next\");\n    }\n    event.dataTransfer.dropEffect = dropInner || dropPrev || dropNext ? \"move\" : \"none\";\n    if ((dropPrev || dropInner || dropNext) && (oldDropNode == null ? void 0 : oldDropNode.node.id) !== dropNode.node.id) {\n      if (oldDropNode) {\n        ctx.emit(\"node-drag-leave\", draggingNode.node, oldDropNode.node, event);\n      }\n      ctx.emit(\"node-drag-enter\", draggingNode.node, dropNode.node, event);\n    }\n    if (dropPrev || dropInner || dropNext) {\n      dragState.value.dropNode = dropNode;\n    } else {\n      dragState.value.dropNode = null;\n    }\n    if (dropNode.node.nextSibling === draggingNode.node) {\n      dropNext = false;\n    }\n    if (dropNode.node.previousSibling === draggingNode.node) {\n      dropPrev = false;\n    }\n    if (dropNode.node.contains(draggingNode.node, false)) {\n      dropInner = false;\n    }\n    if (draggingNode.node === dropNode.node || draggingNode.node.contains(dropNode.node)) {\n      dropPrev = false;\n      dropInner = false;\n      dropNext = false;\n    }\n    const targetPosition = dropNode.$el.querySelector(`.${ns.be(\"node\", \"content\")}`).getBoundingClientRect();\n    const treePosition = el$.value.getBoundingClientRect();\n    let dropType;\n    const prevPercent = dropPrev ? dropInner ? 0.25 : dropNext ? 0.45 : 1 : -1;\n    const nextPercent = dropNext ? dropInner ? 0.75 : dropPrev ? 0.55 : 0 : 1;\n    let indicatorTop = -9999;\n    const distance = event.clientY - targetPosition.top;\n    if (distance < targetPosition.height * prevPercent) {\n      dropType = \"before\";\n    } else if (distance > targetPosition.height * nextPercent) {\n      dropType = \"after\";\n    } else if (dropInner) {\n      dropType = \"inner\";\n    } else {\n      dropType = \"none\";\n    }\n    const iconPosition = dropNode.$el.querySelector(`.${ns.be(\"node\", \"expand-icon\")}`).getBoundingClientRect();\n    const dropIndicator = dropIndicator$.value;\n    if (dropType === \"before\") {\n      indicatorTop = iconPosition.top - treePosition.top;\n    } else if (dropType === \"after\") {\n      indicatorTop = iconPosition.bottom - treePosition.top;\n    }\n    dropIndicator.style.top = `${indicatorTop}px`;\n    dropIndicator.style.left = `${iconPosition.right - treePosition.left}px`;\n    if (dropType === \"inner\") {\n      addClass(dropNode.$el, ns.is(\"drop-inner\"));\n    } else {\n      removeClass(dropNode.$el, ns.is(\"drop-inner\"));\n    }\n    dragState.value.showDropIndicator = dropType === \"before\" || dropType === \"after\";\n    dragState.value.allowDrop = dragState.value.showDropIndicator || userAllowDropInner;\n    dragState.value.dropType = dropType;\n    ctx.emit(\"node-drag-over\", draggingNode.node, dropNode.node, event);\n  };\n  const treeNodeDragEnd = event => {\n    const {\n      draggingNode,\n      dropType,\n      dropNode\n    } = dragState.value;\n    event.preventDefault();\n    if (event.dataTransfer) {\n      event.dataTransfer.dropEffect = \"move\";\n    }\n    if (draggingNode && dropNode) {\n      const draggingNodeCopy = {\n        data: draggingNode.node.data\n      };\n      if (dropType !== \"none\") {\n        draggingNode.node.remove();\n      }\n      if (dropType === \"before\") {\n        dropNode.node.parent.insertBefore(draggingNodeCopy, dropNode.node);\n      } else if (dropType === \"after\") {\n        dropNode.node.parent.insertAfter(draggingNodeCopy, dropNode.node);\n      } else if (dropType === \"inner\") {\n        dropNode.node.insertChild(draggingNodeCopy);\n      }\n      if (dropType !== \"none\") {\n        store.value.registerNode(draggingNodeCopy);\n        if (store.value.key) {\n          draggingNode.node.eachNode(node => {\n            var _a;\n            (_a = store.value.nodesMap[node.data[store.value.key]]) == null ? void 0 : _a.setChecked(node.checked, !store.value.checkStrictly);\n          });\n        }\n      }\n      removeClass(dropNode.$el, ns.is(\"drop-inner\"));\n      ctx.emit(\"node-drag-end\", draggingNode.node, dropNode.node, dropType, event);\n      if (dropType !== \"none\") {\n        ctx.emit(\"node-drop\", draggingNode.node, dropNode.node, dropType, event);\n      }\n    }\n    if (draggingNode && !dropNode) {\n      ctx.emit(\"node-drag-end\", draggingNode.node, null, dropType, event);\n    }\n    dragState.value.showDropIndicator = false;\n    dragState.value.draggingNode = null;\n    dragState.value.dropNode = null;\n    dragState.value.allowDrop = true;\n  };\n  provide(dragEventsKey, {\n    treeNodeDragStart,\n    treeNodeDragOver,\n    treeNodeDragEnd\n  });\n  return {\n    dragState\n  };\n}\nexport { dragEventsKey, useDragNodeHandler };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}