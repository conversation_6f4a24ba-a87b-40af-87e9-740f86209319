{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeStyle as _normalizeStyle, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock, withDirectives as _withDirectives, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"home\"\n};\nconst _hoisted_2 = {\n  class: \"hero\"\n};\nconst _hoisted_3 = {\n  class: \"hero-background\"\n};\nconst _hoisted_4 = {\n  class: \"tech-particles\"\n};\nconst _hoisted_5 = {\n  class: \"hero-content\"\n};\nconst _hoisted_6 = {\n  class: \"brand-logo\"\n};\nconst _hoisted_7 = [\"src\"];\nconst _hoisted_8 = {\n  class: \"slogan-container\"\n};\nconst _hoisted_9 = {\n  class: \"dynamic-text\"\n};\nconst _hoisted_10 = {\n  class: \"hero-actions\"\n};\nconst _hoisted_11 = {\n  class: \"products\",\n  id: \"products\"\n};\nconst _hoisted_12 = {\n  class: \"products-carousel-container\"\n};\nconst _hoisted_13 = {\n  class: \"product-card\"\n};\nconst _hoisted_14 = {\n  class: \"product-header\"\n};\nconst _hoisted_15 = {\n  class: \"product-icon\"\n};\nconst _hoisted_16 = {\n  class: \"feature-container\"\n};\nconst _hoisted_17 = {\n  class: \"feature-list\"\n};\nconst _hoisted_18 = {\n  class: \"action-container\"\n};\nconst _hoisted_19 = {\n  class: \"product-card\"\n};\nconst _hoisted_20 = {\n  class: \"product-header\"\n};\nconst _hoisted_21 = {\n  class: \"product-icon\"\n};\nconst _hoisted_22 = {\n  class: \"feature-container\"\n};\nconst _hoisted_23 = {\n  class: \"feature-list\"\n};\nconst _hoisted_24 = {\n  class: \"action-container\"\n};\nconst _hoisted_25 = {\n  class: \"product-card\"\n};\nconst _hoisted_26 = {\n  class: \"product-header\"\n};\nconst _hoisted_27 = {\n  class: \"product-icon\"\n};\nconst _hoisted_28 = {\n  class: \"feature-container\"\n};\nconst _hoisted_29 = {\n  class: \"feature-list\"\n};\nconst _hoisted_30 = {\n  class: \"action-container\"\n};\nconst _hoisted_31 = {\n  class: \"product-card\"\n};\nconst _hoisted_32 = {\n  class: \"product-header\"\n};\nconst _hoisted_33 = {\n  class: \"product-icon\"\n};\nconst _hoisted_34 = {\n  class: \"feature-container\"\n};\nconst _hoisted_35 = {\n  class: \"feature-list\"\n};\nconst _hoisted_36 = {\n  class: \"action-container\"\n};\nconst _hoisted_37 = {\n  href: \"https://clock.geluman.cn\",\n  target: \"_blank\",\n  class: \"action-button\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_router_link = _resolveComponent(\"router-link\");\n  const _component_el_carousel_item = _resolveComponent(\"el-carousel-item\");\n  const _component_el_carousel = _resolveComponent(\"el-carousel\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 英雄区域 \"), _createElementVNode(\"section\", _hoisted_2, [_createCommentVNode(\" 3D动态背景 \"), _createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" 科技感粒子 \"), _createElementVNode(\"div\", _hoisted_4, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(50, n => {\n    return _createElementVNode(\"span\", {\n      key: n,\n      class: \"particle\",\n      style: _normalizeStyle({\n        '--delay': `${Math.random() * 5}s`,\n        '--size': `${Math.random() * 3 + 1}px`,\n        '--x': `${Math.random() * 100}%`,\n        '--y': `${Math.random() * 100}%`\n      })\n    }, null, 4 /* STYLE */);\n  }), 64 /* STABLE_FRAGMENT */))]), _createCommentVNode(\" 网格背景 \"), _cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"grid-background\"\n  }, null, -1 /* HOISTED */)), _createCommentVNode(\" 流星效果 \"), _cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"shooting-stars\"\n  }, [_createElementVNode(\"div\", {\n    class: \"shooting-star star-1\"\n  }), _createElementVNode(\"div\", {\n    class: \"shooting-star star-2\"\n  }), _createElementVNode(\"div\", {\n    class: \"shooting-star star-3\"\n  })], -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"img\", {\n    src: $setup.logo,\n    alt: \"格鲁曼\"\n  }, null, 8 /* PROPS */, _hoisted_7)]), _cache[4] || (_cache[4] = _createElementVNode(\"h1\", {\n    class: \"title\"\n  }, [_createTextVNode(\" 把科技变得\"), _createElementVNode(\"span\", {\n    class: \"gradient-text\"\n  }, \"萌萌哒~\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_8, [_cache[2] || (_cache[2] = _createElementVNode(\"span\", {\n    class: \"static-text\"\n  }, \"我们\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"span\", null, _toDisplayString($setup.slogans[$setup.currentIndex]), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_10, [_withDirectives((_openBlock(), _createBlock(_component_el_button, {\n    type: \"primary\",\n    size: \"large\",\n    round: \"\",\n    onClick: $setup.scrollToProducts,\n    class: \"primary-button\"\n  }, {\n    default: _withCtx(() => [_cache[3] || (_cache[3] = _createTextVNode(\" 戳这里发现宝藏 \")), _createVNode(_component_el_icon, {\n      class: \"el-icon--right\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"ArrowRight\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })), [[$setup[\"vRipple\"]]])])]), _createCommentVNode(\" 装饰元素 \"), _cache[5] || (_cache[5] = _createStaticVNode(\"<div class=\\\"decorative-elements\\\" data-v-fae5bece><div class=\\\"tech-circle\\\" data-v-fae5bece></div><div class=\\\"floating-shapes\\\" data-v-fae5bece><div class=\\\"shape shape-1\\\" data-v-fae5bece></div><div class=\\\"shape shape-2\\\" data-v-fae5bece></div><div class=\\\"shape shape-3\\\" data-v-fae5bece></div></div></div>\", 1))]), _createCommentVNode(\" 重新设计的产品展示区 \"), _createElementVNode(\"section\", _hoisted_11, [_cache[30] || (_cache[30] = _createElementVNode(\"div\", {\n    class: \"section-header\"\n  }, [_createElementVNode(\"h2\", {\n    class: \"gradient-text\"\n  }, \"创新产品\"), _createElementVNode(\"p\", null, \"打造极致用户体验的数字化工具\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_carousel, {\n    interval: 5000,\n    type: \"card\",\n    height: \"450px\",\n    autoplay: true,\n    \"indicator-position\": \"outside\",\n    arrow: \"never\"\n  }, {\n    default: _withCtx(() => [_createCommentVNode(\" 智能高亮助手 \"), _createVNode(_component_el_carousel_item, null, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Monitor\"])]),\n        _: 1 /* STABLE */\n      })]), _cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n        class: \"product-title\"\n      }, [_createElementVNode(\"div\", {\n        class: \"product-badge\"\n      }, \"Browser Extension\"), _createElementVNode(\"h3\", null, \"智能高亮助手\")], -1 /* HOISTED */))]), _cache[11] || (_cache[11] = _createElementVNode(\"p\", {\n        class: \"product-description\"\n      }, \" 专业的网页文本智能高亮工具，让阅读和学习更高效 \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"ul\", _hoisted_17, [_createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Files\"])]),\n        _: 1 /* STABLE */\n      }), _cache[7] || (_cache[7] = _createElementVNode(\"span\", null, \"多分类管理\", -1 /* HOISTED */))]), _createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Brush\"])]),\n        _: 1 /* STABLE */\n      }), _cache[8] || (_cache[8] = _createElementVNode(\"span\", null, \"颜色自定义\", -1 /* HOISTED */))]), _createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Share\"])]),\n        _: 1 /* STABLE */\n      }), _cache[9] || (_cache[9] = _createElementVNode(\"span\", null, \"配置分享\", -1 /* HOISTED */))])])]), _createElementVNode(\"div\", _hoisted_18, [_withDirectives((_openBlock(), _createBlock(_component_router_link, {\n        to: \"/products/highlight\",\n        class: \"action-button\"\n      }, {\n        default: _withCtx(() => [_cache[10] || (_cache[10] = _createTextVNode(\" 了解更多 \")), _createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode($setup[\"ArrowRight\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })), [[$setup[\"vRipple\"]]])])])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 网页护眼助手 \"), _createVNode(_component_el_carousel_item, null, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"View\"])]),\n        _: 1 /* STABLE */\n      })]), _cache[12] || (_cache[12] = _createElementVNode(\"div\", {\n        class: \"product-title\"\n      }, [_createElementVNode(\"div\", {\n        class: \"product-badge\"\n      }, \"Browser Extension\"), _createElementVNode(\"h3\", null, \"网页护眼助手\")], -1 /* HOISTED */))]), _cache[17] || (_cache[17] = _createElementVNode(\"p\", {\n        class: \"product-description\"\n      }, \" 智能护眼工具，让网页浏览更舒适，保护您的眼睛健康 \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"ul\", _hoisted_23, [_createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Monitor\"])]),\n        _: 1 /* STABLE */\n      }), _cache[13] || (_cache[13] = _createElementVNode(\"span\", null, \"智能护眼模式\", -1 /* HOISTED */))]), _createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"MagicStick\"])]),\n        _: 1 /* STABLE */\n      }), _cache[14] || (_cache[14] = _createElementVNode(\"span\", null, \"场景自适应\", -1 /* HOISTED */))]), _createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Setting\"])]),\n        _: 1 /* STABLE */\n      }), _cache[15] || (_cache[15] = _createElementVNode(\"span\", null, \"全局控制\", -1 /* HOISTED */))])])]), _createElementVNode(\"div\", _hoisted_24, [_withDirectives((_openBlock(), _createBlock(_component_router_link, {\n        to: \"/products/eyeshield\",\n        class: \"action-button\"\n      }, {\n        default: _withCtx(() => [_cache[16] || (_cache[16] = _createTextVNode(\" 了解更多 \")), _createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode($setup[\"ArrowRight\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })), [[$setup[\"vRipple\"]]])])])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 智能翻译助手 \"), _createVNode(_component_el_carousel_item, null, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"ChatLineRound\"])]),\n        _: 1 /* STABLE */\n      })]), _cache[18] || (_cache[18] = _createElementVNode(\"div\", {\n        class: \"product-title\"\n      }, [_createElementVNode(\"div\", {\n        class: \"product-badge\"\n      }, \"Browser Extension\"), _createElementVNode(\"h3\", null, \"智能翻译助手\")], -1 /* HOISTED */))]), _cache[23] || (_cache[23] = _createElementVNode(\"p\", {\n        class: \"product-description\"\n      }, \" 高效、准确的网页文本翻译工具，帮助您跨越语言障碍 \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"ul\", _hoisted_29, [_createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"ChatLineRound\"])]),\n        _: 1 /* STABLE */\n      }), _cache[19] || (_cache[19] = _createElementVNode(\"span\", null, \"多语言支持\", -1 /* HOISTED */))]), _createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Scissors\"])]),\n        _: 1 /* STABLE */\n      }), _cache[20] || (_cache[20] = _createElementVNode(\"span\", null, \"划词翻译\", -1 /* HOISTED */))]), _createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Connection\"])]),\n        _: 1 /* STABLE */\n      }), _cache[21] || (_cache[21] = _createElementVNode(\"span\", null, \"实时翻译\", -1 /* HOISTED */))])])]), _createElementVNode(\"div\", _hoisted_30, [_withDirectives((_openBlock(), _createBlock(_component_router_link, {\n        to: \"/products/translator\",\n        class: \"action-button\"\n      }, {\n        default: _withCtx(() => [_cache[22] || (_cache[22] = _createTextVNode(\" 了解更多 \")), _createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode($setup[\"ArrowRight\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })), [[$setup[\"vRipple\"]]])])])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 智能语音时钟 \"), _createVNode(_component_el_carousel_item, null, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Timer\"])]),\n        _: 1 /* STABLE */\n      })]), _cache[24] || (_cache[24] = _createElementVNode(\"div\", {\n        class: \"product-title\"\n      }, [_createElementVNode(\"div\", {\n        class: \"product-badge\"\n      }, \"Web App\"), _createElementVNode(\"h3\", null, \"智能语音时钟\")], -1 /* HOISTED */))]), _cache[29] || (_cache[29] = _createElementVNode(\"p\", {\n        class: \"product-description\"\n      }, \" 优雅的时间管理助手，让时间提醒更自然、更贴心 \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"ul\", _hoisted_35, [_createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Microphone\"])]),\n        _: 1 /* STABLE */\n      }), _cache[25] || (_cache[25] = _createElementVNode(\"span\", null, \"语音播报\", -1 /* HOISTED */))]), _createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Monitor\"])]),\n        _: 1 /* STABLE */\n      }), _cache[26] || (_cache[26] = _createElementVNode(\"span\", null, \"优雅界面\", -1 /* HOISTED */))]), _createElementVNode(\"li\", null, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Setting\"])]),\n        _: 1 /* STABLE */\n      }), _cache[27] || (_cache[27] = _createElementVNode(\"span\", null, \"智能管理\", -1 /* HOISTED */))])])]), _createElementVNode(\"div\", _hoisted_36, [_withDirectives((_openBlock(), _createElementBlock(\"a\", _hoisted_37, [_cache[28] || (_cache[28] = _createTextVNode(\" 立即体验 \")), _createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"ArrowRight\"])]),\n        _: 1 /* STABLE */\n      })])), [[$setup[\"vRipple\"]]])])])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })])])]);\n}", "map": {"version": 3, "names": ["class", "id", "href", "target", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_Fragment", "_renderList", "n", "key", "style", "_normalizeStyle", "Math", "random", "_hoisted_5", "_hoisted_6", "src", "$setup", "logo", "alt", "_hoisted_7", "_createTextVNode", "_hoisted_8", "_hoisted_9", "_toDisplayString", "slogans", "currentIndex", "_hoisted_10", "_createBlock", "_component_el_button", "type", "size", "round", "onClick", "scrollToProducts", "default", "_withCtx", "_createVNode", "_component_el_icon", "_", "_createStaticVNode", "_hoisted_11", "_hoisted_12", "_component_el_carousel", "interval", "height", "autoplay", "arrow", "_component_el_carousel_item", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_component_router_link", "to", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_37"], "sources": ["D:\\codes\\glmwebsite\\geluman-website\\src\\views\\Home.vue"], "sourcesContent": ["<template>\n  <div class=\"home\">\n    <!-- 英雄区域 -->\n    <section class=\"hero\">\n      <!-- 3D动态背景 -->\n      <div class=\"hero-background\">\n        <!-- 科技感粒子 -->\n        <div class=\"tech-particles\">\n          <span\n            v-for=\"n in 50\"\n            :key=\"n\"\n            class=\"particle\"\n            :style=\"{\n              '--delay': `${Math.random() * 5}s`,\n              '--size': `${Math.random() * 3 + 1}px`,\n              '--x': `${Math.random() * 100}%`,\n              '--y': `${Math.random() * 100}%`,\n            }\"\n          ></span>\n        </div>\n        <!-- 网格背景 -->\n        <div class=\"grid-background\"></div>\n        <!-- 流星效果 -->\n        <div class=\"shooting-stars\">\n          <div class=\"shooting-star star-1\"></div>\n          <div class=\"shooting-star star-2\"></div>\n          <div class=\"shooting-star star-3\"></div>\n        </div>\n      </div>\n\n      <div class=\"hero-content\">\n        <div class=\"brand-logo\">\n          <img :src=\"logo\" alt=\"格鲁曼\" />\n        </div>\n        <h1 class=\"title\">\n          把科技变得<span class=\"gradient-text\">萌萌哒~</span>\n        </h1>\n        <div class=\"slogan-container\">\n          <span class=\"static-text\">我们</span>\n          <div class=\"dynamic-text\">\n            <span>{{ slogans[currentIndex] }}</span>\n          </div>\n        </div>\n        <div class=\"hero-actions\">\n          <el-button\n            type=\"primary\"\n            size=\"large\"\n            round\n            @click=\"scrollToProducts\"\n            class=\"primary-button\"\n            v-ripple\n          >\n            戳这里发现宝藏\n            <el-icon class=\"el-icon--right\"><ArrowRight /></el-icon>\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 装饰元素 -->\n      <div class=\"decorative-elements\">\n        <div class=\"tech-circle\"></div>\n        <div class=\"floating-shapes\">\n          <div class=\"shape shape-1\"></div>\n          <div class=\"shape shape-2\"></div>\n          <div class=\"shape shape-3\"></div>\n        </div>\n      </div>\n    </section>\n\n    <!-- 重新设计的产品展示区 -->\n    <section class=\"products\" id=\"products\">\n      <div class=\"section-header\">\n        <h2 class=\"gradient-text\">创新产品</h2>\n        <p>打造极致用户体验的数字化工具</p>\n      </div>\n\n      <div class=\"products-carousel-container\">\n        <el-carousel\n          :interval=\"5000\"\n          type=\"card\"\n          height=\"450px\"\n          :autoplay=\"true\"\n          indicator-position=\"outside\"\n          arrow=\"never\"\n        >\n          <!-- 智能高亮助手 -->\n          <el-carousel-item>\n            <div class=\"product-card\">\n              <div class=\"product-header\">\n                <div class=\"product-icon\">\n                  <el-icon><Monitor /></el-icon>\n                </div>\n                <div class=\"product-title\">\n                  <div class=\"product-badge\">Browser Extension</div>\n                  <h3>智能高亮助手</h3>\n                </div>\n              </div>\n              <p class=\"product-description\">\n                专业的网页文本智能高亮工具，让阅读和学习更高效\n              </p>\n              <div class=\"feature-container\">\n                <ul class=\"feature-list\">\n                  <li>\n                    <el-icon><Files /></el-icon>\n                    <span>多分类管理</span>\n                  </li>\n                  <li>\n                    <el-icon><Brush /></el-icon>\n                    <span>颜色自定义</span>\n                  </li>\n                  <li>\n                    <el-icon><Share /></el-icon>\n                    <span>配置分享</span>\n                  </li>\n                </ul>\n              </div>\n              <div class=\"action-container\">\n                <router-link\n                  to=\"/products/highlight\"\n                  class=\"action-button\"\n                  v-ripple\n                >\n                  了解更多\n                  <el-icon><ArrowRight /></el-icon>\n                </router-link>\n              </div>\n            </div>\n          </el-carousel-item>\n\n          <!-- 网页护眼助手 -->\n          <el-carousel-item>\n            <div class=\"product-card\">\n              <div class=\"product-header\">\n                <div class=\"product-icon\">\n                  <el-icon><View /></el-icon>\n                </div>\n                <div class=\"product-title\">\n                  <div class=\"product-badge\">Browser Extension</div>\n                  <h3>网页护眼助手</h3>\n                </div>\n              </div>\n              <p class=\"product-description\">\n                智能护眼工具，让网页浏览更舒适，保护您的眼睛健康\n              </p>\n              <div class=\"feature-container\">\n                <ul class=\"feature-list\">\n                  <li>\n                    <el-icon><Monitor /></el-icon>\n                    <span>智能护眼模式</span>\n                  </li>\n                  <li>\n                    <el-icon><MagicStick /></el-icon>\n                    <span>场景自适应</span>\n                  </li>\n                  <li>\n                    <el-icon><Setting /></el-icon>\n                    <span>全局控制</span>\n                  </li>\n                </ul>\n              </div>\n              <div class=\"action-container\">\n                <router-link\n                  to=\"/products/eyeshield\"\n                  class=\"action-button\"\n                  v-ripple\n                >\n                  了解更多\n                  <el-icon><ArrowRight /></el-icon>\n                </router-link>\n              </div>\n            </div>\n          </el-carousel-item>\n\n          <!-- 智能翻译助手 -->\n          <el-carousel-item>\n            <div class=\"product-card\">\n              <div class=\"product-header\">\n                <div class=\"product-icon\">\n                  <el-icon><ChatLineRound /></el-icon>\n                </div>\n                <div class=\"product-title\">\n                  <div class=\"product-badge\">Browser Extension</div>\n                  <h3>智能翻译助手</h3>\n                </div>\n              </div>\n              <p class=\"product-description\">\n                高效、准确的网页文本翻译工具，帮助您跨越语言障碍\n              </p>\n              <div class=\"feature-container\">\n                <ul class=\"feature-list\">\n                  <li>\n                    <el-icon><ChatLineRound /></el-icon>\n                    <span>多语言支持</span>\n                  </li>\n                  <li>\n                    <el-icon><Scissors /></el-icon>\n                    <span>划词翻译</span>\n                  </li>\n                  <li>\n                    <el-icon><Connection /></el-icon>\n                    <span>实时翻译</span>\n                  </li>\n                </ul>\n              </div>\n              <div class=\"action-container\">\n                <router-link\n                  to=\"/products/translator\"\n                  class=\"action-button\"\n                  v-ripple\n                >\n                  了解更多\n                  <el-icon><ArrowRight /></el-icon>\n                </router-link>\n              </div>\n            </div>\n          </el-carousel-item>\n\n          <!-- 智能语音时钟 -->\n          <el-carousel-item>\n            <div class=\"product-card\">\n              <div class=\"product-header\">\n                <div class=\"product-icon\">\n                  <el-icon><Timer /></el-icon>\n                </div>\n                <div class=\"product-title\">\n                  <div class=\"product-badge\">Web App</div>\n                  <h3>智能语音时钟</h3>\n                </div>\n              </div>\n              <p class=\"product-description\">\n                优雅的时间管理助手，让时间提醒更自然、更贴心\n              </p>\n              <div class=\"feature-container\">\n                <ul class=\"feature-list\">\n                  <li>\n                    <el-icon><Microphone /></el-icon>\n                    <span>语音播报</span>\n                  </li>\n                  <li>\n                    <el-icon><Monitor /></el-icon>\n                    <span>优雅界面</span>\n                  </li>\n                  <li>\n                    <el-icon><Setting /></el-icon>\n                    <span>智能管理</span>\n                  </li>\n                </ul>\n              </div>\n              <div class=\"action-container\">\n                <a\n                  href=\"https://clock.geluman.cn\"\n                  target=\"_blank\"\n                  class=\"action-button\"\n                  v-ripple\n                >\n                  立即体验\n                  <el-icon><ArrowRight /></el-icon>\n                </a>\n              </div>\n            </div>\n          </el-carousel-item>\n        </el-carousel>\n      </div>\n    </section>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"HomePage\",\n};\n</script>\n\n<script setup>\nimport { ref, onMounted } from \"vue\";\nimport { useI18n } from \"vue-i18n\";\nimport {\n  ArrowRight,\n  Check,\n  Monitor,\n  View,\n  Files,\n  Brush,\n  Share,\n  MagicStick,\n  Setting,\n  Timer,\n  Microphone,\n  ChatLineRound,\n  Scissors,\n  Connection,\n} from \"@element-plus/icons-vue\";\nimport { highlight, eyeshield, clock, logo, translator } from \"@/assets\";\n\nconst { t } = useI18n();\nconst slogans = [\n  \"用魔法打造科技\",\n  \"让你的浏览器变好玩\",\n  \"每天都要元气满满~\",\n  \"用黑科技拯救世界\",\n];\n\nconst currentIndex = ref(0);\n\nconst typeSlogan = async () => {\n  while (true) {\n    currentIndex.value = (currentIndex.value + 1) % slogans.length;\n    await new Promise((resolve) => setTimeout(resolve, 3000));\n  }\n};\n\nconst scrollToProducts = () => {\n  document.getElementById(\"products\").scrollIntoView({\n    behavior: \"smooth\",\n  });\n};\n\n// 自定义波纹指令\nconst vRipple = {\n  mounted(el) {\n    el.addEventListener(\"click\", (e) => {\n      const ripple = document.createElement(\"span\");\n      ripple.classList.add(\"ripple\");\n      el.appendChild(ripple);\n\n      const rect = el.getBoundingClientRect();\n      const size = Math.max(rect.width, rect.height);\n      const x = e.clientX - rect.left - size / 2;\n      const y = e.clientY - rect.top - size / 2;\n\n      ripple.style.width = ripple.style.height = `${size}px`;\n      ripple.style.left = `${x}px`;\n      ripple.style.top = `${y}px`;\n\n      setTimeout(() => ripple.remove(), 1000);\n    });\n  },\n};\n\nonMounted(() => {\n  typeSlogan();\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.home {\n  background: var(--bg-primary);\n}\n\n.hero {\n  min-height: 100vh;\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\n\n  .hero-background {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n    z-index: 0;\n\n    .tech-particles {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      z-index: 2;\n\n      .particle {\n        position: absolute;\n        width: var(--size);\n        height: var(--size);\n        background: rgba(255, 255, 255, 0.5);\n        border-radius: 50%;\n        left: var(--x);\n        top: var(--y);\n        animation: pulse 2s infinite ease-in-out;\n        animation-delay: var(--delay);\n      }\n    }\n\n    .grid-background {\n      position: absolute;\n      width: 400%;\n      height: 400%;\n      background: linear-gradient(\n          rgba(255, 255, 255, 0.05) 1px,\n          transparent 1px\n        ),\n        linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);\n      background-size: 50px 50px;\n      transform: rotate(45deg);\n      top: -150%;\n      left: -150%;\n      animation: grid-move 50s linear infinite;\n    }\n\n    .shooting-stars {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      z-index: 3;\n      overflow: hidden;\n\n      .shooting-star {\n        position: absolute;\n        width: 300px;\n        height: 2px;\n        background: linear-gradient(\n          90deg,\n          rgba(139, 92, 246, 0) 0%,\n          rgba(139, 92, 246, 0.8) 20%,\n          rgba(255, 255, 255, 1) 100%\n        );\n        box-shadow: 0 0 10px rgba(139, 92, 246, 0.8),\n          0 0 20px rgba(139, 92, 246, 0.4);\n        z-index: 10;\n        transform-origin: right center;\n        opacity: 0;\n\n        &.star-1 {\n          top: 0;\n          right: 0;\n          transform: rotate(135deg);\n          animation: shootingStar 3s linear infinite 0.5s;\n        }\n\n        &.star-2 {\n          top: 5%;\n          right: 20%;\n          transform: rotate(135deg);\n          animation: shootingStar 3s linear infinite 2s;\n        }\n\n        &.star-3 {\n          top: 10%;\n          right: 40%;\n          transform: rotate(135deg);\n          animation: shootingStar 3s linear infinite 3.5s;\n        }\n      }\n    }\n  }\n\n  .hero-content {\n    position: relative;\n    z-index: 2;\n    text-align: center;\n    padding: 0 2rem;\n    width: 100%;\n    max-width: 1200px;\n    margin: 0 auto;\n\n    .brand-logo {\n      margin-bottom: 2rem;\n      width: 100%;\n      display: flex;\n      justify-content: center;\n\n      img {\n        width: 320px;\n        height: auto;\n        max-width: 100%;\n        filter: brightness(0) invert(1);\n      }\n    }\n\n    .title {\n      font-size: 4rem;\n      color: white;\n      margin-bottom: 2rem;\n      font-weight: 700;\n\n      .gradient-text {\n        background: linear-gradient(\n          135deg,\n          var(--primary-color),\n          var(--accent-color)\n        );\n        -webkit-background-clip: text;\n        -webkit-text-fill-color: transparent;\n      }\n    }\n\n    .slogan-container {\n      font-size: 2rem;\n      color: white;\n      margin-bottom: 3rem;\n      display: flex;\n      justify-content: center;\n      gap: 1rem;\n\n      .dynamic-text {\n        color: var(--primary-color);\n        min-width: 300px;\n        position: relative;\n        overflow: hidden;\n\n        span {\n          display: block;\n          animation: slideIn 0.5s ease-out;\n        }\n      }\n    }\n  }\n}\n\n.products {\n  padding: 8rem 2rem 10rem;\n  background: var(--bg-secondary);\n  position: relative;\n  overflow: hidden;\n\n  &:before {\n    content: \"\";\n    position: absolute;\n    width: 300px;\n    height: 300px;\n    border-radius: 50%;\n    background: radial-gradient(var(--primary-light) 0%, transparent 70%);\n    opacity: 0.08;\n    top: -100px;\n    left: -100px;\n    z-index: 0;\n  }\n\n  &:after {\n    content: \"\";\n    position: absolute;\n    width: 400px;\n    height: 400px;\n    border-radius: 50%;\n    background: radial-gradient(var(--primary-color) 0%, transparent 70%);\n    opacity: 0.06;\n    bottom: -200px;\n    right: -100px;\n    z-index: 0;\n  }\n\n  .section-header {\n    text-align: center;\n    margin-bottom: 6rem;\n    position: relative;\n    z-index: 1;\n\n    h2 {\n      font-size: 3rem;\n      margin-bottom: 1rem;\n    }\n\n    p {\n      font-size: 1.2rem;\n      color: var(--text-secondary);\n    }\n  }\n\n  .products-carousel-container {\n    max-width: 1400px;\n    margin: 0 auto;\n    padding: 0 2rem;\n    position: relative;\n    z-index: 1;\n\n    @media (max-width: 768px) {\n      padding: 0 3rem;\n    }\n  }\n}\n\n:deep(.el-carousel__container) {\n  padding: 1.5rem 0;\n  position: relative;\n}\n\n:deep(.el-carousel__item) {\n  border-radius: 20px;\n\n  &.is-active {\n    z-index: 20;\n    transform: scale(1.05);\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n    transition: all 0.5s ease;\n  }\n}\n\n:deep(.el-carousel__indicators--outside) {\n  margin-top: 1.5rem;\n\n  .el-carousel__indicator--horizontal {\n    padding: 0 6px;\n\n    &:hover .el-carousel__button {\n      background-color: var(--primary-light);\n      opacity: 0.8;\n    }\n\n    &.is-active .el-carousel__button {\n      background-color: var(--primary-color);\n    }\n  }\n}\n\n:deep(.el-carousel__button) {\n  width: 12px;\n  height: 12px;\n  background-color: #d8d8d8;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: scale(1.2);\n  }\n}\n\n:deep(.el-carousel__indicator--horizontal.is-active) .el-carousel__button {\n  transform: scale(1.3);\n}\n\n@media (max-width: 768px) {\n  :deep(.el-carousel__arrow) {\n    width: 40px;\n    height: 40px;\n\n    .el-icon {\n      font-size: 18px;\n    }\n  }\n}\n\n.product-card {\n  background: white;\n  border-radius: 20px;\n  padding: 2.5rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  position: relative;\n  z-index: 15;\n\n  .product-header {\n    display: flex;\n    align-items: center;\n    gap: 1.5rem;\n    margin-bottom: 1.5rem;\n  }\n\n  .product-icon {\n    width: 70px;\n    height: 70px;\n    background: var(--bg-accent);\n    border-radius: 16px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    transition: all 0.3s ease;\n    flex-shrink: 0;\n    box-shadow: 0 8px 15px rgba(139, 92, 246, 0.1);\n\n    .el-icon {\n      font-size: 2rem;\n      color: var(--primary-color);\n      transition: color 0.3s ease;\n    }\n  }\n\n  .product-title {\n    flex: 1;\n  }\n\n  .product-badge {\n    display: inline-block;\n    padding: 0.4rem 0.8rem;\n    background: var(--bg-accent);\n    color: var(--primary-color);\n    border-radius: 20px;\n    font-size: 0.8rem;\n    margin-bottom: 0.6rem;\n    font-weight: 500;\n  }\n\n  h3 {\n    font-size: 1.8rem;\n    color: var(--text-primary);\n  }\n\n  .product-description {\n    color: var(--text-secondary);\n    margin-bottom: 2rem;\n    line-height: 1.6;\n    font-size: 1.05rem;\n  }\n\n  .feature-container {\n    margin-bottom: 2rem;\n    flex-grow: 1;\n  }\n\n  .feature-list {\n    list-style: none;\n    display: grid;\n    grid-template-columns: repeat(3, 1fr);\n    gap: 1.2rem;\n\n    li {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      color: var(--text-secondary);\n\n      .el-icon {\n        color: var(--primary-color);\n        font-size: 1.1rem;\n      }\n\n      span {\n        font-size: 0.95rem;\n      }\n    }\n  }\n\n  .action-container {\n    margin-top: auto;\n    position: relative;\n    z-index: 25;\n  }\n\n  .action-button {\n    display: inline-flex;\n    align-items: center;\n    gap: 0.5rem;\n    padding: 0.8rem 1.5rem;\n    background: var(--primary-color);\n    color: white;\n    text-decoration: none;\n    border-radius: 30px;\n    transition: all 0.3s ease;\n    font-weight: 500;\n    box-shadow: 0 8px 15px rgba(139, 92, 246, 0.2);\n    position: relative;\n    z-index: 999 !important;\n    pointer-events: auto;\n\n    &:hover {\n      background: var(--primary-light);\n      transform: translateY(-3px);\n      box-shadow: 0 12px 20px rgba(139, 92, 246, 0.3);\n    }\n\n    &:active {\n      transform: translateY(-1px);\n    }\n  }\n\n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);\n\n    .product-icon {\n      background: var(--primary-color);\n\n      .el-icon {\n        color: white;\n      }\n    }\n  }\n}\n\n@media (max-width: 768px) {\n  .products {\n    padding: 6rem 1rem 8rem;\n\n    .section-header h2 {\n      font-size: 2.5rem;\n    }\n\n    .products-carousel-container {\n      padding: 0 1rem;\n    }\n  }\n\n  .feature-list {\n    grid-template-columns: repeat(2, 1fr) !important;\n  }\n}\n\n// 动画\n@keyframes float {\n  0%,\n  100% {\n    transform: translateY(0);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n// 添加波纹效果样式\n.ripple {\n  position: absolute;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.3);\n  transform: scale(0);\n  animation: ripple 1s cubic-bezier(0, 0, 0.2, 1);\n  pointer-events: none;\n}\n\n@media (max-width: 768px) {\n  .hero {\n    .hero-content {\n      .brand-logo {\n        img {\n          width: 240px;\n        }\n      }\n\n      .title {\n        font-size: 2.5rem;\n      }\n\n      .slogan-container {\n        font-size: 1.5rem;\n      }\n    }\n  }\n}\n\n@keyframes pulse {\n  0%,\n  100% {\n    transform: scale(1);\n    opacity: 0.2;\n  }\n  50% {\n    transform: scale(1.5);\n    opacity: 0.8;\n  }\n}\n\n@keyframes grid-move {\n  0% {\n    transform: translate3d(-10%, -10%, 0) rotate(45deg);\n  }\n  100% {\n    transform: translate3d(-30%, -30%, 0) rotate(45deg);\n  }\n}\n\n@keyframes shootingStar {\n  0% {\n    transform: translate(0, 0) rotate(135deg);\n    opacity: 0;\n  }\n  5% {\n    opacity: 1;\n  }\n  90% {\n    opacity: 1;\n  }\n  100% {\n    transform: translate(-1000px, 1000px) rotate(135deg);\n    opacity: 0;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAM;;EAENA,KAAK,EAAC;AAAM;;EAEdA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAgB;;EAuBxBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;mBA/B/B;;EAqCaA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAc;;EAItBA,KAAK,EAAC;AAAc;;EA2BpBA,KAAK,EAAC,UAAU;EAACC,EAAE,EAAC;;;EAMtBD,KAAK,EAAC;AAA6B;;EAW7BA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;EAWtBA,KAAK,EAAC;AAAmB;;EACxBA,KAAK,EAAC;AAAc;;EAerBA,KAAK,EAAC;AAAkB;;EAe1BA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;EAWtBA,KAAK,EAAC;AAAmB;;EACxBA,KAAK,EAAC;AAAc;;EAerBA,KAAK,EAAC;AAAkB;;EAe1BA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;EAWtBA,KAAK,EAAC;AAAmB;;EACxBA,KAAK,EAAC;AAAc;;EAerBA,KAAK,EAAC;AAAkB;;EAe1BA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;EAWtBA,KAAK,EAAC;AAAmB;;EACxBA,KAAK,EAAC;AAAc;;EAerBA,KAAK,EAAC;AAAkB;;EAEzBE,IAAI,EAAC,0BAA0B;EAC/BC,MAAM,EAAC,QAAQ;EACfH,KAAK,EAAC;;;;;;;;uBA3PtBI,mBAAA,CAuQM,OAvQNC,UAuQM,GAtQJC,mBAAA,UAAa,EACbC,mBAAA,CAgEU,WAhEVC,UAgEU,GA/DRF,mBAAA,YAAe,EACfC,mBAAA,CAuBM,OAvBNE,UAuBM,GAtBJH,mBAAA,WAAc,EACdC,mBAAA,CAYM,OAZNG,UAYM,I,cAXJN,mBAAA,CAUQO,SAAA,QAlBlBC,WAAA,CASwB,EAAE,EAAPC,CAAC;WADVN,mBAAA,CAUQ;MARLO,GAAG,EAAED,CAAC;MACPb,KAAK,EAAC,UAAU;MACfe,KAAK,EAZlBC,eAAA;sBAYkDC,IAAI,CAACC,MAAM;qBAAsCD,IAAI,CAACC,MAAM;kBAAwCD,IAAI,CAACC,MAAM;kBAAqCD,IAAI,CAACC,MAAM;;;oCAQzMZ,mBAAA,UAAa,E,0BACbC,mBAAA,CAAmC;IAA9BP,KAAK,EAAC;EAAiB,6BAC5BM,mBAAA,UAAa,E,0BACbC,mBAAA,CAIM;IAJDP,KAAK,EAAC;EAAgB,IACzBO,mBAAA,CAAwC;IAAnCP,KAAK,EAAC;EAAsB,IACjCO,mBAAA,CAAwC;IAAnCP,KAAK,EAAC;EAAsB,IACjCO,mBAAA,CAAwC;IAAnCP,KAAK,EAAC;EAAsB,G,wBAIrCO,mBAAA,CA0BM,OA1BNY,UA0BM,GAzBJZ,mBAAA,CAEM,OAFNa,UAEM,GADJb,mBAAA,CAA6B;IAAvBc,GAAG,EAAEC,MAAA,CAAAC,IAAI;IAAEC,GAAG,EAAC;0BAhC/BC,UAAA,E,6BAkCQlB,mBAAA,CAEK;IAFDP,KAAK,EAAC;EAAO,IAlCzB0B,gBAAA,CAkC0B,QACX,GAAAnB,mBAAA,CAAuC;IAAjCP,KAAK,EAAC;EAAe,GAAC,MAAI,E,sBAEvCO,mBAAA,CAKM,OALNoB,UAKM,G,0BAJJpB,mBAAA,CAAmC;IAA7BP,KAAK,EAAC;EAAa,GAAC,IAAE,sBAC5BO,mBAAA,CAEM,OAFNqB,UAEM,GADJrB,mBAAA,CAAwC,cAAAsB,gBAAA,CAA/BP,MAAA,CAAAQ,OAAO,CAACR,MAAA,CAAAS,YAAY,kB,KAGjCxB,mBAAA,CAYM,OAZNyB,WAYM,G,+BAXJC,YAAA,CAUYC,oBAAA;IATVC,IAAI,EAAC,SAAS;IACdC,IAAI,EAAC,OAAO;IACZC,KAAK,EAAL,EAAK;IACJC,OAAK,EAAEhB,MAAA,CAAAiB,gBAAgB;IACxBvC,KAAK,EAAC;;IAjDlBwC,OAAA,EAAAC,QAAA,CAmDW,MAEC,C,0BArDZf,gBAAA,CAmDW,WAEC,IAAAgB,YAAA,CAAwDC,kBAAA;MAA/C3C,KAAK,EAAC;IAAgB;MArD3CwC,OAAA,EAAAC,QAAA,CAqD4C,MAAc,CAAdC,YAAA,CAAcpB,MAAA,gB;MArD1DsB,CAAA;;IAAAA,CAAA;mCA0DMtC,mBAAA,UAAa,E,0BA1DnBuC,kBAAA,iU,GAqEIvC,mBAAA,gBAAmB,EACnBC,mBAAA,CAiMU,WAjMVuC,WAiMU,G,4BAhMRvC,mBAAA,CAGM;IAHDP,KAAK,EAAC;EAAgB,IACzBO,mBAAA,CAAmC;IAA/BP,KAAK,EAAC;EAAe,GAAC,MAAI,GAC9BO,mBAAA,CAAqB,WAAlB,gBAAc,E,sBAGnBA,mBAAA,CA0LM,OA1LNwC,WA0LM,GAzLJL,YAAA,CAwLcM,sBAAA;IAvLXC,QAAQ,EAAE,IAAI;IACfd,IAAI,EAAC,MAAM;IACXe,MAAM,EAAC,OAAO;IACbC,QAAQ,EAAE,IAAI;IACf,oBAAkB,EAAC,SAAS;IAC5BC,KAAK,EAAC;;IAnFhBZ,OAAA,EAAAC,QAAA,CAqFU,MAAe,CAAfnC,mBAAA,YAAe,EACfoC,YAAA,CAyCmBW,2BAAA;MA/H7Bb,OAAA,EAAAC,QAAA,CAuFY,MAuCM,CAvCNlC,mBAAA,CAuCM,OAvCN+C,WAuCM,GAtCJ/C,mBAAA,CAQM,OARNgD,WAQM,GAPJhD,mBAAA,CAEM,OAFNiD,WAEM,GADJd,YAAA,CAA8BC,kBAAA;QA1FhDH,OAAA,EAAAC,QAAA,CA0F2B,MAAW,CAAXC,YAAA,CAAWpB,MAAA,a;QA1FtCsB,CAAA;sCA4FgBrC,mBAAA,CAGM;QAHDP,KAAK,EAAC;MAAe,IACxBO,mBAAA,CAAkD;QAA7CP,KAAK,EAAC;MAAe,GAAC,mBAAiB,GAC5CO,mBAAA,CAAe,YAAX,QAAM,E,oDAGdA,mBAAA,CAEI;QAFDP,KAAK,EAAC;MAAqB,GAAC,2BAE/B,sBACAO,mBAAA,CAeM,OAfNkD,WAeM,GAdJlD,mBAAA,CAaK,MAbLmD,WAaK,GAZHnD,mBAAA,CAGK,aAFHmC,YAAA,CAA4BC,kBAAA;QAvGhDH,OAAA,EAAAC,QAAA,CAuG6B,MAAS,CAATC,YAAA,CAASpB,MAAA,W;QAvGtCsB,CAAA;oCAwGoBrC,mBAAA,CAAkB,cAAZ,OAAK,qB,GAEbA,mBAAA,CAGK,aAFHmC,YAAA,CAA4BC,kBAAA;QA3GhDH,OAAA,EAAAC,QAAA,CA2G6B,MAAS,CAATC,YAAA,CAASpB,MAAA,W;QA3GtCsB,CAAA;oCA4GoBrC,mBAAA,CAAkB,cAAZ,OAAK,qB,GAEbA,mBAAA,CAGK,aAFHmC,YAAA,CAA4BC,kBAAA;QA/GhDH,OAAA,EAAAC,QAAA,CA+G6B,MAAS,CAATC,YAAA,CAASpB,MAAA,W;QA/GtCsB,CAAA;oCAgHoBrC,mBAAA,CAAiB,cAAX,MAAI,qB,OAIhBA,mBAAA,CASM,OATNoD,WASM,G,+BARJ1B,YAAA,CAOc2B,sBAAA;QANZC,EAAE,EAAC,qBAAqB;QACxB7D,KAAK,EAAC;;QAvHxBwC,OAAA,EAAAC,QAAA,CAyHiB,MAEC,C,4BA3HlBf,gBAAA,CAyHiB,QAEC,IAAAgB,YAAA,CAAiCC,kBAAA;UA3HnDH,OAAA,EAAAC,QAAA,CA2H2B,MAAc,CAAdC,YAAA,CAAcpB,MAAA,gB;UA3HzCsB,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QAiIUtC,mBAAA,YAAe,EACfoC,YAAA,CAyCmBW,2BAAA;MA3K7Bb,OAAA,EAAAC,QAAA,CAmIY,MAuCM,CAvCNlC,mBAAA,CAuCM,OAvCNuD,WAuCM,GAtCJvD,mBAAA,CAQM,OARNwD,WAQM,GAPJxD,mBAAA,CAEM,OAFNyD,WAEM,GADJtB,YAAA,CAA2BC,kBAAA;QAtI7CH,OAAA,EAAAC,QAAA,CAsI2B,MAAQ,CAARC,YAAA,CAAQpB,MAAA,U;QAtInCsB,CAAA;wCAwIgBrC,mBAAA,CAGM;QAHDP,KAAK,EAAC;MAAe,IACxBO,mBAAA,CAAkD;QAA7CP,KAAK,EAAC;MAAe,GAAC,mBAAiB,GAC5CO,mBAAA,CAAe,YAAX,QAAM,E,oDAGdA,mBAAA,CAEI;QAFDP,KAAK,EAAC;MAAqB,GAAC,4BAE/B,sBACAO,mBAAA,CAeM,OAfN0D,WAeM,GAdJ1D,mBAAA,CAaK,MAbL2D,WAaK,GAZH3D,mBAAA,CAGK,aAFHmC,YAAA,CAA8BC,kBAAA;QAnJlDH,OAAA,EAAAC,QAAA,CAmJ6B,MAAW,CAAXC,YAAA,CAAWpB,MAAA,a;QAnJxCsB,CAAA;sCAoJoBrC,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAGK,aAFHmC,YAAA,CAAiCC,kBAAA;QAvJrDH,OAAA,EAAAC,QAAA,CAuJ6B,MAAc,CAAdC,YAAA,CAAcpB,MAAA,gB;QAvJ3CsB,CAAA;sCAwJoBrC,mBAAA,CAAkB,cAAZ,OAAK,qB,GAEbA,mBAAA,CAGK,aAFHmC,YAAA,CAA8BC,kBAAA;QA3JlDH,OAAA,EAAAC,QAAA,CA2J6B,MAAW,CAAXC,YAAA,CAAWpB,MAAA,a;QA3JxCsB,CAAA;sCA4JoBrC,mBAAA,CAAiB,cAAX,MAAI,qB,OAIhBA,mBAAA,CASM,OATN4D,WASM,G,+BARJlC,YAAA,CAOc2B,sBAAA;QANZC,EAAE,EAAC,qBAAqB;QACxB7D,KAAK,EAAC;;QAnKxBwC,OAAA,EAAAC,QAAA,CAqKiB,MAEC,C,4BAvKlBf,gBAAA,CAqKiB,QAEC,IAAAgB,YAAA,CAAiCC,kBAAA;UAvKnDH,OAAA,EAAAC,QAAA,CAuK2B,MAAc,CAAdC,YAAA,CAAcpB,MAAA,gB;UAvKzCsB,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QA6KUtC,mBAAA,YAAe,EACfoC,YAAA,CAyCmBW,2BAAA;MAvN7Bb,OAAA,EAAAC,QAAA,CA+KY,MAuCM,CAvCNlC,mBAAA,CAuCM,OAvCN6D,WAuCM,GAtCJ7D,mBAAA,CAQM,OARN8D,WAQM,GAPJ9D,mBAAA,CAEM,OAFN+D,WAEM,GADJ5B,YAAA,CAAoCC,kBAAA;QAlLtDH,OAAA,EAAAC,QAAA,CAkL2B,MAAiB,CAAjBC,YAAA,CAAiBpB,MAAA,mB;QAlL5CsB,CAAA;wCAoLgBrC,mBAAA,CAGM;QAHDP,KAAK,EAAC;MAAe,IACxBO,mBAAA,CAAkD;QAA7CP,KAAK,EAAC;MAAe,GAAC,mBAAiB,GAC5CO,mBAAA,CAAe,YAAX,QAAM,E,oDAGdA,mBAAA,CAEI;QAFDP,KAAK,EAAC;MAAqB,GAAC,4BAE/B,sBACAO,mBAAA,CAeM,OAfNgE,WAeM,GAdJhE,mBAAA,CAaK,MAbLiE,WAaK,GAZHjE,mBAAA,CAGK,aAFHmC,YAAA,CAAoCC,kBAAA;QA/LxDH,OAAA,EAAAC,QAAA,CA+L6B,MAAiB,CAAjBC,YAAA,CAAiBpB,MAAA,mB;QA/L9CsB,CAAA;sCAgMoBrC,mBAAA,CAAkB,cAAZ,OAAK,qB,GAEbA,mBAAA,CAGK,aAFHmC,YAAA,CAA+BC,kBAAA;QAnMnDH,OAAA,EAAAC,QAAA,CAmM6B,MAAY,CAAZC,YAAA,CAAYpB,MAAA,c;QAnMzCsB,CAAA;sCAoMoBrC,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGK,aAFHmC,YAAA,CAAiCC,kBAAA;QAvMrDH,OAAA,EAAAC,QAAA,CAuM6B,MAAc,CAAdC,YAAA,CAAcpB,MAAA,gB;QAvM3CsB,CAAA;sCAwMoBrC,mBAAA,CAAiB,cAAX,MAAI,qB,OAIhBA,mBAAA,CASM,OATNkE,WASM,G,+BARJxC,YAAA,CAOc2B,sBAAA;QANZC,EAAE,EAAC,sBAAsB;QACzB7D,KAAK,EAAC;;QA/MxBwC,OAAA,EAAAC,QAAA,CAiNiB,MAEC,C,4BAnNlBf,gBAAA,CAiNiB,QAEC,IAAAgB,YAAA,CAAiCC,kBAAA;UAnNnDH,OAAA,EAAAC,QAAA,CAmN2B,MAAc,CAAdC,YAAA,CAAcpB,MAAA,gB;UAnNzCsB,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QAyNUtC,mBAAA,YAAe,EACfoC,YAAA,CA0CmBW,2BAAA;MApQ7Bb,OAAA,EAAAC,QAAA,CA2NY,MAwCM,CAxCNlC,mBAAA,CAwCM,OAxCNmE,WAwCM,GAvCJnE,mBAAA,CAQM,OARNoE,WAQM,GAPJpE,mBAAA,CAEM,OAFNqE,WAEM,GADJlC,YAAA,CAA4BC,kBAAA;QA9N9CH,OAAA,EAAAC,QAAA,CA8N2B,MAAS,CAATC,YAAA,CAASpB,MAAA,W;QA9NpCsB,CAAA;wCAgOgBrC,mBAAA,CAGM;QAHDP,KAAK,EAAC;MAAe,IACxBO,mBAAA,CAAwC;QAAnCP,KAAK,EAAC;MAAe,GAAC,SAAO,GAClCO,mBAAA,CAAe,YAAX,QAAM,E,oDAGdA,mBAAA,CAEI;QAFDP,KAAK,EAAC;MAAqB,GAAC,0BAE/B,sBACAO,mBAAA,CAeM,OAfNsE,WAeM,GAdJtE,mBAAA,CAaK,MAbLuE,WAaK,GAZHvE,mBAAA,CAGK,aAFHmC,YAAA,CAAiCC,kBAAA;QA3OrDH,OAAA,EAAAC,QAAA,CA2O6B,MAAc,CAAdC,YAAA,CAAcpB,MAAA,gB;QA3O3CsB,CAAA;sCA4OoBrC,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGK,aAFHmC,YAAA,CAA8BC,kBAAA;QA/OlDH,OAAA,EAAAC,QAAA,CA+O6B,MAAW,CAAXC,YAAA,CAAWpB,MAAA,a;QA/OxCsB,CAAA;sCAgPoBrC,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGK,aAFHmC,YAAA,CAA8BC,kBAAA;QAnPlDH,OAAA,EAAAC,QAAA,CAmP6B,MAAW,CAAXC,YAAA,CAAWpB,MAAA,a;QAnPxCsB,CAAA;sCAoPoBrC,mBAAA,CAAiB,cAAX,MAAI,qB,OAIhBA,mBAAA,CAUM,OAVNwE,WAUM,G,+BATJ3E,mBAAA,CAQI,KARJ4E,WAQI,G,4BAjQpBtD,gBAAA,CA8PiB,QAEC,IAAAgB,YAAA,CAAiCC,kBAAA;QAhQnDH,OAAA,EAAAC,QAAA,CAgQ2B,MAAc,CAAdC,YAAA,CAAcpB,MAAA,gB;QAhQzCsB,CAAA;;MAAAA,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}