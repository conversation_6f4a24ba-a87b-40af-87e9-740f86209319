{"ast": null, "code": "import { translator } from \"@/assets\";\nimport { Download } from \"@element-plus/icons-vue\";\nimport translatorHome from \"@/assets/img/translator-home.png\";\nimport translatorSetting from \"@/assets/img/translator-setting.png\";\nconst __default__ = {\n  name: \"TranslatorPage\"\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const features = [{\n      icon: \"🌐\",\n      title: \"多语言支持\",\n      description: \"支持中英文、日语、法语等多国语言之间的互译，满足不同场景的翻译需求\"\n    }, {\n      icon: \"✂️\",\n      title: \"划词翻译\",\n      description: \"选中网页上的任意文本，即可一键获取翻译结果，快速理解外语内容\"\n    }, {\n      icon: \"📝\",\n      title: \"智能识别\",\n      description: \"自动识别文本语言，无需手动选择源语言，让翻译更加便捷高效\"\n    }, {\n      icon: \"🔄\",\n      title: \"实时翻译\",\n      description: \"选中文本后立即获取翻译结果，无需等待，提高阅读和学习效率\"\n    }, {\n      icon: \"🎛️\",\n      title: \"个性化设置\",\n      description: \"支持设置默认目标语言、翻译显示方式等，打造个性化翻译体验\"\n    }, {\n      icon: \"📱\",\n      title: \"跨平台兼容\",\n      description: \"支持Chrome、Edge等主流浏览器，保证在不同设备上的稳定运行\"\n    }];\n    const steps = [{\n      number: \"1\",\n      title: \"安装插件\",\n      description: \"从下方下载按钮下载并解压，打开Chrome或者Edge浏览器，进入chrome://extensions/或者edge://extensions/，将解压后的文件夹拖入页面中，完成安装\"\n    }, {\n      number: \"2\",\n      title: \"选择文本\",\n      description: \"在任意网页上选择需要翻译的文本内容\"\n    }, {\n      number: \"3\",\n      title: \"查看翻译\",\n      description: \"选中后自动显示翻译结果，或点击插件图标进行翻译\"\n    }, {\n      number: \"4\",\n      title: \"调整设置\",\n      description: \"在插件设置中可以自定义目标语言和显示方式\"\n    }];\n    const __returned__ = {\n      features,\n      steps,\n      get translator() {\n        return translator;\n      },\n      get Download() {\n        return Download;\n      },\n      get translatorHome() {\n        return translatorHome;\n      },\n      get translatorSetting() {\n        return translatorSetting;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["translator", "Download", "translatorHome", "translatorSetting", "__default__", "name", "features", "icon", "title", "description", "steps", "number"], "sources": ["D:/codes/glmwebsite/geluman-website/src/views/products/Translator.vue"], "sourcesContent": ["<template>\n  <div class=\"product-page\">\n    <section class=\"hero\">\n      <div class=\"hero-content\">\n        <img :src=\"translator\" alt=\"翻译插件\" class=\"plugin-logo\" />\n        <h1>智能翻译助手</h1>\n        <p class=\"subtitle\">高效、准确的网页文本翻译工具，帮助您跨越语言障碍</p>\n        <div class=\"cta-buttons\">\n          <a\n            href=\"https://gengxin.geluman.cn/downloads/GLM-Translator-1.0.0.zip\"\n            class=\"download-btn\"\n            target=\"_blank\"\n          >\n            <el-icon><Download /></el-icon>\n            下载插件\n          </a>\n        </div>\n      </div>\n    </section>\n\n    <section class=\"demo\">\n      <div class=\"container\">\n        <h2 class=\"section-title\">产品演示</h2>\n        <p class=\"section-subtitle\">简单易用的界面，强大的翻译功能</p>\n        <div class=\"demo-gallery\">\n          <div class=\"demo-item\">\n            <div class=\"demo-image-container\">\n              <img\n                :src=\"translatorHome\"\n                alt=\"智能翻译助手 - 翻译界面\"\n                class=\"demo-image\"\n              />\n            </div>\n            <div class=\"image-caption\">智能翻译界面 - 选择文本即可快速翻译</div>\n          </div>\n          <div class=\"demo-item animate-fadeInUp delay-6\">\n            <div class=\"demo-image-container\">\n              <img\n                :src=\"translatorSetting\"\n                alt=\"智能翻译助手 - 设置界面\"\n                class=\"demo-image\"\n              />\n            </div>\n            <div class=\"image-caption\">个性化设置 - 自定义翻译体验</div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <section class=\"features\">\n      <div class=\"container\">\n        <h2 class=\"animate-fadeInUp\">核心功能</h2>\n        <div class=\"features-grid\">\n          <div\n            v-for=\"(feature, index) in features\"\n            :key=\"feature.title\"\n            class=\"feature-card animate-fadeInUp\"\n            :style=\"{ animationDelay: `${(index + 1) * 0.2}s` }\"\n          >\n            <div class=\"feature-icon\">{{ feature.icon }}</div>\n            <h3>{{ feature.title }}</h3>\n            <p>{{ feature.description }}</p>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <section class=\"usage\">\n      <div class=\"container\">\n        <h2>使用说明</h2>\n        <div class=\"usage-steps\">\n          <div class=\"step\" v-for=\"step in steps\" :key=\"step.title\">\n            <div class=\"step-number\">{{ step.number }}</div>\n            <h3>{{ step.title }}</h3>\n            <p>{{ step.description }}</p>\n          </div>\n        </div>\n      </div>\n    </section>\n  </div>\n</template>\n\n<script setup>\nimport { translator } from \"@/assets\";\nimport { Download } from \"@element-plus/icons-vue\";\nimport translatorHome from \"@/assets/img/translator-home.png\";\nimport translatorSetting from \"@/assets/img/translator-setting.png\";\n\nconst features = [\n  {\n    icon: \"🌐\",\n    title: \"多语言支持\",\n    description:\n      \"支持中英文、日语、法语等多国语言之间的互译，满足不同场景的翻译需求\",\n  },\n  {\n    icon: \"✂️\",\n    title: \"划词翻译\",\n    description: \"选中网页上的任意文本，即可一键获取翻译结果，快速理解外语内容\",\n  },\n  {\n    icon: \"📝\",\n    title: \"智能识别\",\n    description: \"自动识别文本语言，无需手动选择源语言，让翻译更加便捷高效\",\n  },\n  {\n    icon: \"🔄\",\n    title: \"实时翻译\",\n    description: \"选中文本后立即获取翻译结果，无需等待，提高阅读和学习效率\",\n  },\n  {\n    icon: \"🎛️\",\n    title: \"个性化设置\",\n    description: \"支持设置默认目标语言、翻译显示方式等，打造个性化翻译体验\",\n  },\n  {\n    icon: \"📱\",\n    title: \"跨平台兼容\",\n    description: \"支持Chrome、Edge等主流浏览器，保证在不同设备上的稳定运行\",\n  },\n];\n\nconst steps = [\n  {\n    number: \"1\",\n    title: \"安装插件\",\n    description:\n      \"从下方下载按钮下载并解压，打开Chrome或者Edge浏览器，进入chrome://extensions/或者edge://extensions/，将解压后的文件夹拖入页面中，完成安装\",\n  },\n  {\n    number: \"2\",\n    title: \"选择文本\",\n    description: \"在任意网页上选择需要翻译的文本内容\",\n  },\n  {\n    number: \"3\",\n    title: \"查看翻译\",\n    description: \"选中后自动显示翻译结果，或点击插件图标进行翻译\",\n  },\n  {\n    number: \"4\",\n    title: \"调整设置\",\n    description: \"在插件设置中可以自定义目标语言和显示方式\",\n  },\n];\n</script>\n\n<style lang=\"scss\" scoped>\n.hero {\n  background: linear-gradient(\n    135deg,\n    var(--primary-color) 0%,\n    var(--primary-dark) 100%\n  );\n  padding: 6rem 0;\n  text-align: center;\n  color: white;\n\n  .plugin-logo {\n    width: 120px;\n    height: auto;\n    margin-bottom: 2rem;\n  }\n\n  h1 {\n    font-size: 3rem;\n    margin-bottom: 1rem;\n  }\n\n  .subtitle {\n    font-size: 1.2rem;\n    opacity: 0.9;\n    margin-bottom: 2rem;\n  }\n\n  .download-btn {\n    display: inline-flex;\n    align-items: center;\n    gap: 0.8rem;\n    padding: 1rem 2rem;\n    background: white;\n    color: var(--primary-color);\n    text-decoration: none;\n    border-radius: 30px;\n    font-weight: 500;\n    font-size: 1.1rem;\n    transition: all 0.3s ease;\n    box-shadow: var(--shadow-md);\n\n    .el-icon {\n      font-size: 1.2rem;\n    }\n\n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: var(--shadow-lg);\n    }\n  }\n}\n\n.demo {\n  padding: 6rem 0;\n  background: white;\n\n  .container {\n    max-width: 1200px;\n    margin: 0 auto;\n    padding: 0 2rem;\n    text-align: center;\n  }\n\n  .section-title {\n    font-size: 2.5rem;\n    margin-bottom: 1rem;\n    color: var(--text-primary);\n  }\n\n  .section-subtitle {\n    font-size: 1.2rem;\n    color: var(--text-secondary);\n    margin-bottom: 3rem;\n    max-width: 800px;\n    margin-left: auto;\n    margin-right: auto;\n  }\n\n  .demo-gallery {\n    display: grid;\n    grid-template-columns: repeat(2, 1fr);\n    gap: 3rem;\n\n    @media (max-width: 992px) {\n      grid-template-columns: 1fr;\n      gap: 4rem;\n    }\n  }\n\n  .demo-item {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n\n    .demo-image-container {\n      width: 100%;\n      height: 350px;\n      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n      border-radius: 12px;\n      overflow: hidden;\n      transition: all 0.3s ease;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: #f8f9fa;\n\n      &:hover {\n        transform: translateY(-10px);\n        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);\n      }\n    }\n\n    .demo-image {\n      max-width: 100%;\n      max-height: 100%;\n      object-fit: contain;\n      display: block;\n      border-radius: 12px;\n    }\n\n    .image-caption {\n      margin-top: 1.5rem;\n      font-size: 1.1rem;\n      color: var(--text-secondary);\n    }\n  }\n}\n\n.features {\n  padding: 6rem 0;\n  background: var(--bg-secondary);\n\n  .container {\n    max-width: 1200px;\n    margin: 0 auto;\n    padding: 0 2rem;\n  }\n\n  h2 {\n    text-align: center;\n    font-size: 2.5rem;\n    margin-bottom: 3rem;\n  }\n\n  .features-grid {\n    display: grid;\n    grid-template-columns: repeat(3, 1fr);\n    gap: 2rem;\n\n    @media (max-width: 992px) {\n      grid-template-columns: repeat(2, 1fr);\n    }\n\n    @media (max-width: 768px) {\n      grid-template-columns: 1fr;\n    }\n  }\n}\n\n.feature-card {\n  background: white;\n  padding: 2rem;\n  border-radius: 20px;\n  text-align: center;\n  transition: var(--transition-base);\n\n  &:hover {\n    transform: translateY(-10px);\n\n    .feature-icon {\n      animation: iconBounce 0.5s ease;\n    }\n  }\n\n  .feature-icon {\n    font-size: 2.5rem;\n    margin-bottom: 1rem;\n  }\n\n  h3 {\n    font-size: 1.5rem;\n    margin-bottom: 1rem;\n    color: var(--text-primary);\n  }\n\n  p {\n    color: var(--text-secondary);\n    line-height: 1.6;\n  }\n}\n\n.usage {\n  padding: 6rem 0;\n\n  .container {\n    max-width: 1200px;\n    margin: 0 auto;\n    padding: 0 2rem;\n  }\n\n  h2 {\n    text-align: center;\n    font-size: 2.5rem;\n    margin-bottom: 3rem;\n  }\n\n  .usage-steps {\n    display: grid;\n    grid-template-columns: repeat(4, 1fr);\n    gap: 2rem;\n\n    @media (max-width: 992px) {\n      grid-template-columns: repeat(2, 1fr);\n    }\n\n    @media (max-width: 768px) {\n      grid-template-columns: 1fr;\n    }\n  }\n\n  .step {\n    text-align: center;\n    padding: 2rem;\n\n    .step-number {\n      width: 40px;\n      height: 40px;\n      background: var(--primary-color);\n      color: white;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin: 0 auto 1rem;\n      font-weight: 500;\n    }\n\n    h3 {\n      font-size: 1.3rem;\n      margin-bottom: 1rem;\n      color: var(--text-primary);\n    }\n\n    p {\n      color: var(--text-secondary);\n      line-height: 1.6;\n    }\n  }\n}\n\n@keyframes btnShine {\n  0% {\n    transform: translateX(-100%) translateY(-100%) rotate(45deg);\n  }\n  100% {\n    transform: translateX(100%) translateY(100%) rotate(45deg);\n  }\n}\n\n@keyframes iconBounce {\n  0%,\n  100% {\n    transform: translateY(0);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n}\n</style>\n\n<script>\nexport default {\n  name: \"TranslatorPage\",\n};\n</script>\n"], "mappings": "AAmFA,SAASA,UAAU,QAAQ,UAAU;AACrC,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,iBAAiB,MAAM,qCAAqC;AA6UnE,MAAAC,WAAA,GAAe;EACbC,IAAI,EAAE;AACR,CAAC;;;;;;IA7UD,MAAMC,QAAQ,GAAG,CACf;MACEC,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,OAAO;MACdC,WAAW,EACT;IACJ,CAAC,EACD;MACEF,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,OAAO;MACdC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,OAAO;MACdC,WAAW,EAAE;IACf,CAAC,CACF;IAED,MAAMC,KAAK,GAAG,CACZ;MACEC,MAAM,EAAE,GAAG;MACXH,KAAK,EAAE,MAAM;MACbC,WAAW,EACT;IACJ,CAAC,EACD;MACEE,MAAM,EAAE,GAAG;MACXH,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,EACD;MACEE,MAAM,EAAE,GAAG;MACXH,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,EACD;MACEE,MAAM,EAAE,GAAG;MACXH,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}