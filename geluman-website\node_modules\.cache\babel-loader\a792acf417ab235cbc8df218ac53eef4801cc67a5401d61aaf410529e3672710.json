{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport baseSortedIndexBy from './_baseSortedIndexBy.js';\n\n/**\n * This method is like `_.sortedIndex` except that it accepts `iteratee`\n * which is invoked for `value` and each element of `array` to compute their\n * sort ranking. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n * @example\n *\n * var objects = [{ 'x': 4 }, { 'x': 5 }];\n *\n * _.sortedIndexBy(objects, { 'x': 4 }, function(o) { return o.x; });\n * // => 0\n *\n * // The `_.property` iteratee shorthand.\n * _.sortedIndexBy(objects, { 'x': 4 }, 'x');\n * // => 0\n */\nfunction sortedIndexBy(array, value, iteratee) {\n  return baseSortedIndexBy(array, value, baseIteratee(iteratee, 2));\n}\nexport default sortedIndexBy;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}