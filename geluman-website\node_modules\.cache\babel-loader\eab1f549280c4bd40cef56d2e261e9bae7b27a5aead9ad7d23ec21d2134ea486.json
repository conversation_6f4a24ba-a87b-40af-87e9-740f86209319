{"ast": null, "code": "import { defineComponent, ref, provide, createVNode, mergeProps } from 'vue';\nimport dayjs from 'dayjs';\nimport customParseFormat from 'dayjs/plugin/customParseFormat.js';\nimport { DEFAULT_FORMATS_TIME } from './constants.mjs';\nimport CommonPicker from './common/picker.mjs';\nimport TimePickPanel from './time-picker-com/panel-time-pick.mjs';\nimport TimeRangePanel from './time-picker-com/panel-time-range.mjs';\nimport { timePickerDefaultProps } from './common/props.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\ndayjs.extend(customParseFormat);\nvar TimePicker = defineComponent({\n  name: \"ElTimePicker\",\n  install: null,\n  props: {\n    ...timePickerDefaultProps,\n    isRange: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: [UPDATE_MODEL_EVENT],\n  setup(props, ctx) {\n    const commonPicker = ref();\n    const [type, Panel] = props.isRange ? [\"timerange\", TimeRangePanel] : [\"time\", TimePickPanel];\n    const modelUpdater = value => ctx.emit(UPDATE_MODEL_EVENT, value);\n    provide(\"ElPopperOptions\", props.popperOptions);\n    ctx.expose({\n      focus: () => {\n        var _a;\n        (_a = commonPicker.value) == null ? void 0 : _a.focus();\n      },\n      blur: () => {\n        var _a;\n        (_a = commonPicker.value) == null ? void 0 : _a.blur();\n      },\n      handleOpen: () => {\n        var _a;\n        (_a = commonPicker.value) == null ? void 0 : _a.handleOpen();\n      },\n      handleClose: () => {\n        var _a;\n        (_a = commonPicker.value) == null ? void 0 : _a.handleClose();\n      }\n    });\n    return () => {\n      var _a;\n      const format = (_a = props.format) != null ? _a : DEFAULT_FORMATS_TIME;\n      return createVNode(CommonPicker, mergeProps(props, {\n        \"ref\": commonPicker,\n        \"type\": type,\n        \"format\": format,\n        \"onUpdate:modelValue\": modelUpdater\n      }), {\n        default: props2 => createVNode(Panel, props2, null)\n      });\n    };\n  }\n});\nexport { TimePicker as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}