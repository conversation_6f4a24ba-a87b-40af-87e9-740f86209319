{"ast": null, "code": "import { isRef, ref } from 'vue';\nimport { Loading } from './service.mjs';\nimport { isObject, hyphenate, isString } from '@vue/shared';\nconst INSTANCE_KEY = Symbol(\"ElLoading\");\nconst createInstance = (el, binding) => {\n  var _a, _b, _c, _d;\n  const vm = binding.instance;\n  const getBindingProp = key => isObject(binding.value) ? binding.value[key] : void 0;\n  const resolveExpression = key => {\n    const data = isString(key) && (vm == null ? void 0 : vm[key]) || key;\n    if (data) return ref(data);else return data;\n  };\n  const getProp = name => resolveExpression(getBindingProp(name) || el.getAttribute(`element-loading-${hyphenate(name)}`));\n  const fullscreen = (_a = getBindingProp(\"fullscreen\")) != null ? _a : binding.modifiers.fullscreen;\n  const options = {\n    text: getProp(\"text\"),\n    svg: getProp(\"svg\"),\n    svgViewBox: getProp(\"svgViewBox\"),\n    spinner: getProp(\"spinner\"),\n    background: getProp(\"background\"),\n    customClass: getProp(\"customClass\"),\n    fullscreen,\n    target: (_b = getBindingProp(\"target\")) != null ? _b : fullscreen ? void 0 : el,\n    body: (_c = getBindingProp(\"body\")) != null ? _c : binding.modifiers.body,\n    lock: (_d = getBindingProp(\"lock\")) != null ? _d : binding.modifiers.lock\n  };\n  el[INSTANCE_KEY] = {\n    options,\n    instance: Loading(options)\n  };\n};\nconst updateOptions = (newOptions, originalOptions) => {\n  for (const key of Object.keys(originalOptions)) {\n    if (isRef(originalOptions[key])) originalOptions[key].value = newOptions[key];\n  }\n};\nconst vLoading = {\n  mounted(el, binding) {\n    if (binding.value) {\n      createInstance(el, binding);\n    }\n  },\n  updated(el, binding) {\n    const instance = el[INSTANCE_KEY];\n    if (binding.oldValue !== binding.value) {\n      if (binding.value && !binding.oldValue) {\n        createInstance(el, binding);\n      } else if (binding.value && binding.oldValue) {\n        if (isObject(binding.value)) updateOptions(binding.value, instance.options);\n      } else {\n        instance == null ? void 0 : instance.instance.close();\n      }\n    }\n  },\n  unmounted(el) {\n    var _a;\n    (_a = el[INSTANCE_KEY]) == null ? void 0 : _a.instance.close();\n    el[INSTANCE_KEY] = null;\n  }\n};\nexport { vLoading };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}