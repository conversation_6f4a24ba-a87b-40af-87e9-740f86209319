{"ast": null, "code": "import { defineComponent, inject, computed, openBlock, createElementBlock, normalizeStyle, unref, normalizeClass } from 'vue';\nimport { tooltipV2RootKey, tooltipV2ContentKey } from './constants.mjs';\nimport { tooltipV2ArrowProps, tooltipV2ArrowSpecialProps } from './arrow.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTooltipV2Arrow\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: {\n    ...tooltipV2ArrowProps,\n    ...tooltipV2ArrowSpecialProps\n  },\n  setup(__props) {\n    const props = __props;\n    const {\n      ns\n    } = inject(tooltipV2RootKey);\n    const {\n      arrowRef\n    } = inject(tooltipV2ContentKey);\n    const arrowStyle = computed(() => {\n      const {\n        style,\n        width,\n        height\n      } = props;\n      const namespace = ns.namespace.value;\n      return {\n        [`--${namespace}-tooltip-v2-arrow-width`]: `${width}px`,\n        [`--${namespace}-tooltip-v2-arrow-height`]: `${height}px`,\n        [`--${namespace}-tooltip-v2-arrow-border-width`]: `${width / 2}px`,\n        [`--${namespace}-tooltip-v2-arrow-cover-width`]: width / 2 - 1,\n        ...(style || {})\n      };\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"span\", {\n        ref_key: \"arrowRef\",\n        ref: arrowRef,\n        style: normalizeStyle(unref(arrowStyle)),\n        class: normalizeClass(unref(ns).e(\"arrow\"))\n      }, null, 6);\n    };\n  }\n});\nvar TooltipV2Arrow = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"arrow.vue\"]]);\nexport { TooltipV2Arrow as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}