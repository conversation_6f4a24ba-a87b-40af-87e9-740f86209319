{"ast": null, "code": "import { HORIZONTAL, VERTICAL } from '../defaults.mjs';\nimport { cAF, rAF } from '../../../../utils/raf.mjs';\nimport { isFirefox } from '../../../../utils/browser.mjs';\nconst LayoutKeys = {\n  [HORIZONTAL]: \"deltaX\",\n  [VERTICAL]: \"deltaY\"\n};\nconst useWheel = ({\n  atEndEdge,\n  atStartEdge,\n  layout\n}, onWheelDelta) => {\n  let frameHandle;\n  let offset = 0;\n  const hasReachedEdge = offset2 => {\n    const edgeReached = offset2 < 0 && atStartEdge.value || offset2 > 0 && atEndEdge.value;\n    return edgeReached;\n  };\n  const onWheel = e => {\n    cAF(frameHandle);\n    const newOffset = e[LayoutKeys[layout.value]];\n    if (hasReachedEdge(offset) && hasReachedEdge(offset + newOffset)) return;\n    offset += newOffset;\n    if (!isFirefox()) {\n      e.preventDefault();\n    }\n    frameHandle = rAF(() => {\n      onWheelDelta(offset);\n      offset = 0;\n    });\n  };\n  return {\n    hasReachedEdge,\n    onWheel\n  };\n};\nexport { useWheel as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}