{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, ref, computed, watch, nextTick, openBlock, createElementBlock, unref, normalizeClass, createElementVNode, Fragment, renderList, withKeys, withModifiers, createVNode } from 'vue';\nimport dayjs from 'dayjs';\nimport { basicMonthTableProps } from '../props/basic-month-table.mjs';\nimport { datesInMonth, getValidDateOfMonth } from '../utils.mjs';\nimport ElDatePickerCell from './basic-cell-render.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { castArray } from '../../../../utils/arrays.mjs';\nimport { hasClass } from '../../../../utils/dom/style.mjs';\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"basic-month-table\",\n  props: basicMonthTableProps,\n  emits: [\"changerange\", \"pick\", \"select\"],\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"month-table\");\n    const {\n      t,\n      lang\n    } = useLocale();\n    const tbodyRef = ref();\n    const currentCellRef = ref();\n    const months = ref(props.date.locale(\"en\").localeData().monthsShort().map(_ => _.toLowerCase()));\n    const tableRows = ref([[], [], []]);\n    const lastRow = ref();\n    const lastColumn = ref();\n    const rows = computed(() => {\n      var _a, _b;\n      const rows2 = tableRows.value;\n      const now = dayjs().locale(lang.value).startOf(\"month\");\n      for (let i = 0; i < 3; i++) {\n        const row = rows2[i];\n        for (let j = 0; j < 4; j++) {\n          const cell = row[j] || (row[j] = {\n            row: i,\n            column: j,\n            type: \"normal\",\n            inRange: false,\n            start: false,\n            end: false,\n            text: -1,\n            disabled: false\n          });\n          cell.type = \"normal\";\n          const index = i * 4 + j;\n          const calTime = props.date.startOf(\"year\").month(index);\n          const calEndDate = props.rangeState.endDate || props.maxDate || props.rangeState.selecting && props.minDate || null;\n          cell.inRange = !!(props.minDate && calTime.isSameOrAfter(props.minDate, \"month\") && calEndDate && calTime.isSameOrBefore(calEndDate, \"month\")) || !!(props.minDate && calTime.isSameOrBefore(props.minDate, \"month\") && calEndDate && calTime.isSameOrAfter(calEndDate, \"month\"));\n          if ((_a = props.minDate) == null ? void 0 : _a.isSameOrAfter(calEndDate)) {\n            cell.start = !!(calEndDate && calTime.isSame(calEndDate, \"month\"));\n            cell.end = props.minDate && calTime.isSame(props.minDate, \"month\");\n          } else {\n            cell.start = !!(props.minDate && calTime.isSame(props.minDate, \"month\"));\n            cell.end = !!(calEndDate && calTime.isSame(calEndDate, \"month\"));\n          }\n          const isToday = now.isSame(calTime);\n          if (isToday) {\n            cell.type = \"today\";\n          }\n          cell.text = index;\n          cell.disabled = ((_b = props.disabledDate) == null ? void 0 : _b.call(props, calTime.toDate())) || false;\n        }\n      }\n      return rows2;\n    });\n    const focus = () => {\n      var _a;\n      (_a = currentCellRef.value) == null ? void 0 : _a.focus();\n    };\n    const getCellStyle = cell => {\n      const style = {};\n      const year = props.date.year();\n      const today = /* @__PURE__ */new Date();\n      const month = cell.text;\n      style.disabled = props.disabledDate ? datesInMonth(year, month, lang.value).every(props.disabledDate) : false;\n      style.current = castArray(props.parsedValue).findIndex(date => dayjs.isDayjs(date) && date.year() === year && date.month() === month) >= 0;\n      style.today = today.getFullYear() === year && today.getMonth() === month;\n      if (cell.inRange) {\n        style[\"in-range\"] = true;\n        if (cell.start) {\n          style[\"start-date\"] = true;\n        }\n        if (cell.end) {\n          style[\"end-date\"] = true;\n        }\n      }\n      return style;\n    };\n    const isSelectedCell = cell => {\n      const year = props.date.year();\n      const month = cell.text;\n      return castArray(props.date).findIndex(date => date.year() === year && date.month() === month) >= 0;\n    };\n    const handleMouseMove = event => {\n      var _a;\n      if (!props.rangeState.selecting) return;\n      let target = event.target;\n      if (target.tagName === \"SPAN\") {\n        target = (_a = target.parentNode) == null ? void 0 : _a.parentNode;\n      }\n      if (target.tagName === \"DIV\") {\n        target = target.parentNode;\n      }\n      if (target.tagName !== \"TD\") return;\n      const row = target.parentNode.rowIndex;\n      const column = target.cellIndex;\n      if (rows.value[row][column].disabled) return;\n      if (row !== lastRow.value || column !== lastColumn.value) {\n        lastRow.value = row;\n        lastColumn.value = column;\n        emit(\"changerange\", {\n          selecting: true,\n          endDate: props.date.startOf(\"year\").month(row * 4 + column)\n        });\n      }\n    };\n    const handleMonthTableClick = event => {\n      var _a;\n      const target = (_a = event.target) == null ? void 0 : _a.closest(\"td\");\n      if ((target == null ? void 0 : target.tagName) !== \"TD\") return;\n      if (hasClass(target, \"disabled\")) return;\n      const column = target.cellIndex;\n      const row = target.parentNode.rowIndex;\n      const month = row * 4 + column;\n      const newDate = props.date.startOf(\"year\").month(month);\n      if (props.selectionMode === \"months\") {\n        if (event.type === \"keydown\") {\n          emit(\"pick\", castArray(props.parsedValue), false);\n          return;\n        }\n        const newMonth = getValidDateOfMonth(props.date.year(), month, lang.value, props.disabledDate);\n        const newValue = hasClass(target, \"current\") ? castArray(props.parsedValue).filter(d => (d == null ? void 0 : d.year()) !== newMonth.year() || (d == null ? void 0 : d.month()) !== newMonth.month()) : castArray(props.parsedValue).concat([dayjs(newMonth)]);\n        emit(\"pick\", newValue);\n      } else if (props.selectionMode === \"range\") {\n        if (!props.rangeState.selecting) {\n          emit(\"pick\", {\n            minDate: newDate,\n            maxDate: null\n          });\n          emit(\"select\", true);\n        } else {\n          if (props.minDate && newDate >= props.minDate) {\n            emit(\"pick\", {\n              minDate: props.minDate,\n              maxDate: newDate\n            });\n          } else {\n            emit(\"pick\", {\n              minDate: newDate,\n              maxDate: props.minDate\n            });\n          }\n          emit(\"select\", false);\n        }\n      } else {\n        emit(\"pick\", month);\n      }\n    };\n    watch(() => props.date, async () => {\n      var _a, _b;\n      if ((_a = tbodyRef.value) == null ? void 0 : _a.contains(document.activeElement)) {\n        await nextTick();\n        (_b = currentCellRef.value) == null ? void 0 : _b.focus();\n      }\n    });\n    expose({\n      focus\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"table\", {\n        role: \"grid\",\n        \"aria-label\": unref(t)(\"el.datepicker.monthTablePrompt\"),\n        class: normalizeClass(unref(ns).b()),\n        onClick: handleMonthTableClick,\n        onMousemove: handleMouseMove\n      }, [createElementVNode(\"tbody\", {\n        ref_key: \"tbodyRef\",\n        ref: tbodyRef\n      }, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(rows), (row, key) => {\n        return openBlock(), createElementBlock(\"tr\", {\n          key\n        }, [(openBlock(true), createElementBlock(Fragment, null, renderList(row, (cell, key_) => {\n          return openBlock(), createElementBlock(\"td\", {\n            key: key_,\n            ref_for: true,\n            ref: el => isSelectedCell(cell) && (currentCellRef.value = el),\n            class: normalizeClass(getCellStyle(cell)),\n            \"aria-selected\": `${isSelectedCell(cell)}`,\n            \"aria-label\": unref(t)(`el.datepicker.month${+cell.text + 1}`),\n            tabindex: isSelectedCell(cell) ? 0 : -1,\n            onKeydown: [withKeys(withModifiers(handleMonthTableClick, [\"prevent\", \"stop\"]), [\"space\"]), withKeys(withModifiers(handleMonthTableClick, [\"prevent\", \"stop\"]), [\"enter\"])]\n          }, [createVNode(unref(ElDatePickerCell), {\n            cell: {\n              ...cell,\n              renderText: unref(t)(\"el.datepicker.months.\" + months.value[cell.text])\n            }\n          }, null, 8, [\"cell\"])], 42, [\"aria-selected\", \"aria-label\", \"tabindex\", \"onKeydown\"]);\n        }), 128))]);\n      }), 128))], 512)], 42, [\"aria-label\"]);\n    };\n  }\n});\nvar MonthTable = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"basic-month-table.vue\"]]);\nexport { MonthTable as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}