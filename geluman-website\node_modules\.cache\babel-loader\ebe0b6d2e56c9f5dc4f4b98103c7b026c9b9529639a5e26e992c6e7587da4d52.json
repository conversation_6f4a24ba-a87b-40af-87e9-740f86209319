{"ast": null, "code": "import Space from './src/space.mjs';\nexport { spaceProps } from './src/space.mjs';\nexport { spaceItemProps } from './src/item.mjs';\nexport { useSpace } from './src/use-space.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nconst ElSpace = withInstall(Space);\nexport { ElSpace, ElSpace as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}