{"ast": null, "code": "import add from './add.js';\nimport ceil from './ceil.js';\nimport divide from './divide.js';\nimport floor from './floor.js';\nimport max from './max.js';\nimport maxBy from './maxBy.js';\nimport mean from './mean.js';\nimport meanBy from './meanBy.js';\nimport min from './min.js';\nimport minBy from './minBy.js';\nimport multiply from './multiply.js';\nimport round from './round.js';\nimport subtract from './subtract.js';\nimport sum from './sum.js';\nimport sumBy from './sumBy.js';\nexport default {\n  add,\n  ceil,\n  divide,\n  floor,\n  max,\n  maxBy,\n  mean,\n  meanBy,\n  min,\n  minBy,\n  multiply,\n  round,\n  subtract,\n  sum,\n  sumBy\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}