{"ast": null, "code": "import { computed, unref, inject } from 'vue';\nimport { buildProp } from '../../utils/vue/props/runtime.mjs';\nimport { componentSizes } from '../../constants/size.mjs';\nconst useSizeProp = buildProp({\n  type: String,\n  values: componentSizes,\n  required: false\n});\nconst useSizeProps = {\n  size: useSizeProp\n};\nconst SIZE_INJECTION_KEY = Symbol(\"size\");\nconst useGlobalSize = () => {\n  const injectedSize = inject(SIZE_INJECTION_KEY, {});\n  return computed(() => {\n    return unref(injectedSize.size) || \"\";\n  });\n};\nexport { SIZE_INJECTION_KEY, useGlobalSize, useSizeProp, useSizeProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}