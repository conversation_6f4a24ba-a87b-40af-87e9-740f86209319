{"ast": null, "code": "import { watch, unref } from 'vue';\nimport { debugWarn } from '../../utils/error.mjs';\nconst useDeprecated = ({\n  from,\n  replacement,\n  scope,\n  version,\n  ref,\n  type = \"API\"\n}, condition) => {\n  watch(() => unref(condition), val => {\n    if (val) {\n      debugWarn(scope, `[${type}] ${from} is about to be deprecated in version ${version}, please use ${replacement} instead.\nFor more detail, please visit: ${ref}\n`);\n    }\n  }, {\n    immediate: true\n  });\n};\nexport { useDeprecated };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}