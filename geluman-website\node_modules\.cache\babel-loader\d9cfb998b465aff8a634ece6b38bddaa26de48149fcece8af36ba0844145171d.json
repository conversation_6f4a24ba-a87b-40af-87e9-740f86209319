{"ast": null, "code": "import { defineComponent, resolveComponent, openBlock, createBlock, withCtx, createVNode, normalizeProps, guardReactiveProps, renderSlot } from 'vue';\nimport ElRovingFocusGroupImpl from './roving-focus-group-impl.mjs';\nimport { ElCollection } from './roving-focus-group.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElRovingFocusGroup\",\n  components: {\n    ElFocusGroupCollection: ElCollection,\n    ElRovingFocusGroupImpl\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_roving_focus_group_impl = resolveComponent(\"el-roving-focus-group-impl\");\n  const _component_el_focus_group_collection = resolveComponent(\"el-focus-group-collection\");\n  return openBlock(), createBlock(_component_el_focus_group_collection, null, {\n    default: withCtx(() => [createVNode(_component_el_roving_focus_group_impl, normalizeProps(guardReactiveProps(_ctx.$attrs)), {\n      default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n      _: 3\n    }, 16)]),\n    _: 3\n  });\n}\nvar ElRovingFocusGroup = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"roving-focus-group.vue\"]]);\nexport { ElRovingFocusGroup as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}