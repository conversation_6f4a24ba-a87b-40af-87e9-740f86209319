{"ast": null, "code": "import { defineComponent } from 'vue';\nimport { COMPONENT_NAME } from './constants.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst descriptionItemProps = buildProps({\n  label: {\n    type: String,\n    default: \"\"\n  },\n  span: {\n    type: Number,\n    default: 1\n  },\n  rowspan: {\n    type: Number,\n    default: 1\n  },\n  width: {\n    type: [String, Number],\n    default: \"\"\n  },\n  minWidth: {\n    type: [String, Number],\n    default: \"\"\n  },\n  labelWidth: {\n    type: [String, Number],\n    default: \"\"\n  },\n  align: {\n    type: String,\n    default: \"left\"\n  },\n  labelAlign: {\n    type: String,\n    default: \"\"\n  },\n  className: {\n    type: String,\n    default: \"\"\n  },\n  labelClassName: {\n    type: String,\n    default: \"\"\n  }\n});\nconst DescriptionItem = defineComponent({\n  name: COMPONENT_NAME,\n  props: descriptionItemProps\n});\nexport { DescriptionItem as default, descriptionItemProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}