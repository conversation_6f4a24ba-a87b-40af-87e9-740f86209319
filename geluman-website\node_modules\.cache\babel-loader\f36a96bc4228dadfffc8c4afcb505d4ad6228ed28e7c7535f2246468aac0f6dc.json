{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { getCurrentInstance, watch } from 'vue';\nimport { parseWidth, parseMinWidth } from '../util.mjs';\nimport { hasOwn } from '@vue/shared';\nfunction getAllAliases(props, aliases) {\n  return props.reduce((prev, cur) => {\n    prev[cur] = cur;\n    return prev;\n  }, aliases);\n}\nfunction useWatcher(owner, props_) {\n  const instance = getCurrentInstance();\n  const registerComplexWatchers = () => {\n    const props = [\"fixed\"];\n    const aliases = {\n      realWidth: \"width\",\n      realMinWidth: \"minWidth\"\n    };\n    const allAliases = getAllAliases(props, aliases);\n    Object.keys(allAliases).forEach(key => {\n      const columnKey = aliases[key];\n      if (hasOwn(props_, columnKey)) {\n        watch(() => props_[columnKey], newVal => {\n          let value = newVal;\n          if (columnKey === \"width\" && key === \"realWidth\") {\n            value = parseWidth(newVal);\n          }\n          if (columnKey === \"minWidth\" && key === \"realMinWidth\") {\n            value = parseMinWidth(newVal);\n          }\n          instance.columnConfig.value[columnKey] = value;\n          instance.columnConfig.value[key] = value;\n          const updateColumns = columnKey === \"fixed\";\n          owner.value.store.scheduleLayout(updateColumns);\n        });\n      }\n    });\n  };\n  const registerNormalWatchers = () => {\n    const props = [\"label\", \"filters\", \"filterMultiple\", \"filteredValue\", \"sortable\", \"index\", \"formatter\", \"className\", \"labelClassName\", \"filterClassName\", \"showOverflowTooltip\", \"tooltipFormatter\"];\n    const aliases = {\n      property: \"prop\",\n      align: \"realAlign\",\n      headerAlign: \"realHeaderAlign\"\n    };\n    const allAliases = getAllAliases(props, aliases);\n    Object.keys(allAliases).forEach(key => {\n      const columnKey = aliases[key];\n      if (hasOwn(props_, columnKey)) {\n        watch(() => props_[columnKey], newVal => {\n          instance.columnConfig.value[key] = newVal;\n        });\n      }\n    });\n  };\n  return {\n    registerComplexWatchers,\n    registerNormalWatchers\n  };\n}\nexport { useWatcher as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}