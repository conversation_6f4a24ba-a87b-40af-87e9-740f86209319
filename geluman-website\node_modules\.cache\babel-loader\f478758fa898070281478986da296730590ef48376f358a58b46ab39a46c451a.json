{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, renderSlot } from 'vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElFooter\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: {\n    height: {\n      type: String,\n      default: null\n    }\n  },\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"footer\");\n    const style = computed(() => props.height ? ns.cssVarBlock({\n      height: props.height\n    }) : {});\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"footer\", {\n        class: normalizeClass(unref(ns).b()),\n        style: normalizeStyle(unref(style))\n      }, [renderSlot(_ctx.$slots, \"default\")], 6);\n    };\n  }\n});\nvar Footer = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"footer.vue\"]]);\nexport { Footer as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}