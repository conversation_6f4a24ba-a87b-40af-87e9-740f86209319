{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst maskProps = buildProps({\n  zIndex: {\n    type: Number,\n    default: 1001\n  },\n  visible: Boolean,\n  fill: {\n    type: String,\n    default: \"rgba(0,0,0,0.5)\"\n  },\n  pos: {\n    type: definePropType(Object)\n  },\n  targetAreaClickable: {\n    type: Boolean,\n    default: true\n  }\n});\nexport { maskProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}