{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { isEqual } from 'lodash-unified';\nimport Node from './node.mjs';\nconst flatNodes = (nodes, leafOnly) => {\n  return nodes.reduce((res, node) => {\n    if (node.isLeaf) {\n      res.push(node);\n    } else {\n      !leafOnly && res.push(node);\n      res = res.concat(flatNodes(node.children, leafOnly));\n    }\n    return res;\n  }, []);\n};\nclass Store {\n  constructor(data, config) {\n    this.config = config;\n    const nodes = (data || []).map(nodeData => new Node(nodeData, this.config));\n    this.nodes = nodes;\n    this.allNodes = flatNodes(nodes, false);\n    this.leafNodes = flatNodes(nodes, true);\n  }\n  getNodes() {\n    return this.nodes;\n  }\n  getFlattedNodes(leafOnly) {\n    return leafOnly ? this.leafNodes : this.allNodes;\n  }\n  appendNode(nodeData, parentNode) {\n    const node = parentNode ? parentNode.appendChild(nodeData) : new Node(nodeData, this.config);\n    if (!parentNode) this.nodes.push(node);\n    this.appendAllNodesAndLeafNodes(node);\n  }\n  appendNodes(nodeDataList, parentNode) {\n    nodeDataList.forEach(nodeData => this.appendNode(nodeData, parentNode));\n  }\n  appendAllNodesAndLeafNodes(node) {\n    this.allNodes.push(node);\n    node.isLeaf && this.leafNodes.push(node);\n    if (node.children) {\n      node.children.forEach(subNode => {\n        this.appendAllNodesAndLeafNodes(subNode);\n      });\n    }\n  }\n  getNodeByValue(value, leafOnly = false) {\n    if (!value && value !== 0) return null;\n    const node = this.getFlattedNodes(leafOnly).find(node2 => isEqual(node2.value, value) || isEqual(node2.pathValues, value));\n    return node || null;\n  }\n  getSameNode(node) {\n    if (!node) return null;\n    const node_ = this.getFlattedNodes(false).find(({\n      value,\n      level\n    }) => isEqual(node.value, value) && node.level === level);\n    return node_ || null;\n  }\n}\nexport { Store as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}