{"ast": null, "code": "import baseRest from './_baseRest.js';\nimport unzipWith from './unzipWith.js';\n\n/**\n * This method is like `_.zip` except that it accepts `iteratee` to specify\n * how grouped values should be combined. The iteratee is invoked with the\n * elements of each group: (...group).\n *\n * @static\n * @memberOf _\n * @since 3.8.0\n * @category Array\n * @param {...Array} [arrays] The arrays to process.\n * @param {Function} [iteratee=_.identity] The function to combine\n *  grouped values.\n * @returns {Array} Returns the new array of grouped elements.\n * @example\n *\n * _.zipWith([1, 2], [10, 20], [100, 200], function(a, b, c) {\n *   return a + b + c;\n * });\n * // => [111, 222]\n */\nvar zipWith = baseRest(function (arrays) {\n  var length = arrays.length,\n    iteratee = length > 1 ? arrays[length - 1] : undefined;\n  iteratee = typeof iteratee == 'function' ? (arrays.pop(), iteratee) : undefined;\n  return unzipWith(arrays, iteratee);\n});\nexport default zipWith;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}