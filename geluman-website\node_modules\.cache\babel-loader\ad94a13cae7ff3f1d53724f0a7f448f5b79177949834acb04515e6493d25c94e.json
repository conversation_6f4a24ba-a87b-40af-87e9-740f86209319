{"ast": null, "code": "const timeUnits = [\"hours\", \"minutes\", \"seconds\"];\nconst DEFAULT_FORMATS_TIME = \"HH:mm:ss\";\nconst DEFAULT_FORMATS_DATE = \"YYYY-MM-DD\";\nconst DEFAULT_FORMATS_DATEPICKER = {\n  date: DEFAULT_FORMATS_DATE,\n  dates: DEFAULT_FORMATS_DATE,\n  week: \"gggg[w]ww\",\n  year: \"YYYY\",\n  years: \"YYYY\",\n  month: \"YYYY-MM\",\n  months: \"YYYY-MM\",\n  datetime: `${DEFAULT_FORMATS_DATE} ${DEFAULT_FORMATS_TIME}`,\n  monthrange: \"YYYY-MM\",\n  yearrange: \"YYYY\",\n  daterange: DEFAULT_FORMATS_DATE,\n  datetimerange: `${DEFAULT_FORMATS_DATE} ${DEFAULT_FORMATS_TIME}`\n};\nexport { DEFAULT_FORMATS_DATE, DEFAULT_FORMATS_DATEPICKER, DEFAULT_FORMATS_TIME, timeUnits };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}