{"ast": null, "code": "import { defineComponent, getCurrentInstance, useSlots, inject, ref, computed, watch, reactive, onMounted, onUnmounted, unref, withDirectives, openBlock, createElementBlock, normalizeClass, renderSlot, vShow, createCommentVNode } from 'vue';\nimport { eagerComputed } from '@vueuse/core';\nimport { tabsRootContextKey } from './constants.mjs';\nimport { tabPaneProps } from './tab-pane.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst COMPONENT_NAME = \"ElTabPane\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: tabPaneProps,\n  setup(__props) {\n    const props = __props;\n    const instance = getCurrentInstance();\n    const slots = useSlots();\n    const tabsRoot = inject(tabsRootContextKey);\n    if (!tabsRoot) throwError(COMPONENT_NAME, \"usage: <el-tabs><el-tab-pane /></el-tabs/>\");\n    const ns = useNamespace(\"tab-pane\");\n    const index = ref();\n    const isClosable = computed(() => props.closable || tabsRoot.props.closable);\n    const active = eagerComputed(() => {\n      var _a;\n      return tabsRoot.currentName.value === ((_a = props.name) != null ? _a : index.value);\n    });\n    const loaded = ref(active.value);\n    const paneName = computed(() => {\n      var _a;\n      return (_a = props.name) != null ? _a : index.value;\n    });\n    const shouldBeRender = eagerComputed(() => !props.lazy || loaded.value || active.value);\n    watch(active, val => {\n      if (val) loaded.value = true;\n    });\n    const pane = reactive({\n      uid: instance.uid,\n      slots,\n      props,\n      paneName,\n      active,\n      index,\n      isClosable\n    });\n    tabsRoot.registerPane(pane);\n    onMounted(() => {\n      tabsRoot.sortPane(pane);\n    });\n    onUnmounted(() => {\n      tabsRoot.unregisterPane(pane.uid);\n    });\n    return (_ctx, _cache) => {\n      return unref(shouldBeRender) ? withDirectives((openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        id: `pane-${unref(paneName)}`,\n        class: normalizeClass(unref(ns).b()),\n        role: \"tabpanel\",\n        \"aria-hidden\": !unref(active),\n        \"aria-labelledby\": `tab-${unref(paneName)}`\n      }, [renderSlot(_ctx.$slots, \"default\")], 10, [\"id\", \"aria-hidden\", \"aria-labelledby\"])), [[vShow, unref(active)]]) : createCommentVNode(\"v-if\", true);\n    };\n  }\n});\nvar TabPane = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tab-pane.vue\"]]);\nexport { TabPane as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}