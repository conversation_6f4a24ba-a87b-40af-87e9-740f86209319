{"ast": null, "code": "import baseForOwnRight from './_baseForOwnRight.js';\nimport castFunction from './_castFunction.js';\n\n/**\n * This method is like `_.forOwn` except that it iterates over properties of\n * `object` in the opposite order.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns `object`.\n * @see _.forOwn\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.forOwnRight(new Foo, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'b' then 'a' assuming `_.forOwn` logs 'a' then 'b'.\n */\nfunction forOwnRight(object, iteratee) {\n  return object && baseForOwnRight(object, castFunction(iteratee));\n}\nexport default forOwnRight;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}