{"ast": null, "code": "import { defineComponent, ref, createVNode, Fragment } from 'vue';\nimport { ensureOnlyChild } from '../../../utils/vue/vnode.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { composeRefs } from '../../../utils/vue/refs.mjs';\nconst forwardRefProps = buildProps({\n  setRef: {\n    type: definePropType(Function),\n    required: true\n  },\n  onlyChild: Boolean\n});\nvar ForwardRef = defineComponent({\n  props: forwardRefProps,\n  setup(props, {\n    slots\n  }) {\n    const fragmentRef = ref();\n    const setRef = composeRefs(fragmentRef, el => {\n      if (el) {\n        props.setRef(el.nextElementSibling);\n      } else {\n        props.setRef(null);\n      }\n    });\n    return () => {\n      var _a;\n      const [firstChild] = ((_a = slots.default) == null ? void 0 : _a.call(slots)) || [];\n      const child = props.onlyChild ? ensureOnlyChild(firstChild.children) : firstChild.children;\n      return createVNode(Fragment, {\n        \"ref\": setRef\n      }, [child]);\n    };\n  }\n});\nexport { ForwardRef as default, forwardRefProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}