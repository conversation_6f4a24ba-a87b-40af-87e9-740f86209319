"use strict";(self["webpackChunkgeluman_website"]=self["webpackChunkgeluman_website"]||[]).push([[615],{4319:function(e,t,r){r.d(t,{F4c:function(){return c},MLh:function(){return l},P1n:function(){return B},SSU:function(){return I},X2F:function(){return y},YC1:function(){return O},eU5:function(){return w},esz:function(){return L},fho:function(){return m},hRP:function(){return T},lWr:function(){return U},wYm:function(){return E}});r(1484),r(6961),r(4126),r(4615),r(7354),r(3213),r(9370),r(2807),r(8747),r(4929),r(8200),r(6886),r(6831),r(4118),r(5981),r(3074),r(9724),r(6441);var o=r(9075),n=r(8450),p=r(8018),i=r(7420);function c(e){var t;const r=(0,o.x_)(e);return null!=(t=null==r?void 0:r.$el)?t:r}const s=o.oc?window:void 0,u=o.oc?window.document:void 0;o.oc&&window.navigator,o.oc&&window.location;function l(...e){let t,r,p,i;if((0,o.Kg)(e[0])||Array.isArray(e[0])?([r,p,i]=e,t=s):[t,r,p,i]=e,!t)return o.lQ;Array.isArray(r)||(r=[r]),Array.isArray(p)||(p=[p]);const u=[],l=()=>{u.forEach((e=>e())),u.length=0},a=(e,t,r,o)=>(e.addEventListener(t,r,o),()=>e.removeEventListener(t,r,o)),y=(0,n.wB)((()=>[c(t),(0,o.x_)(i)]),(([e,t])=>{l(),e&&u.push(...r.flatMap((r=>p.map((o=>a(e,r,o,t))))))}),{immediate:!0,flush:"post"}),O=()=>{y(),l()};return(0,o.Uo)(O),O}let a=!1;function y(e,t,r={}){const{window:n=s,ignore:p=[],capture:i=!0,detectIframe:u=!1}=r;if(!n)return;o.un&&!a&&(a=!0,Array.from(n.document.body.children).forEach((e=>e.addEventListener("click",o.lQ))));let y=!0;const O=e=>p.some((t=>{if("string"===typeof t)return Array.from(n.document.querySelectorAll(t)).some((t=>t===e.target||e.composedPath().includes(t)));{const r=c(t);return r&&(e.target===r||e.composedPath().includes(r))}})),b=r=>{const o=c(e);o&&o!==r.target&&!r.composedPath().includes(o)&&(0===r.detail&&(y=!O(r)),y?t(r):y=!0)},d=[l(n,"click",b,{passive:!0,capture:i}),l(n,"pointerdown",(t=>{const r=c(e);r&&(y=!t.composedPath().includes(r)&&!O(t))}),{passive:!0}),u&&l(n,"blur",(r=>{var o;const p=c(e);"IFRAME"!==(null==(o=n.document.activeElement)?void 0:o.tagName)||(null==p?void 0:p.contains(n.document.activeElement))||t(r)}))].filter(Boolean),j=()=>d.forEach((e=>e()));return j}Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;function O(e={}){var t;const{window:r=s}=e,n=null!=(t=e.document)?t:null==r?void 0:r.document,p=(0,o.bW)((()=>null),(()=>null==n?void 0:n.activeElement));return r&&(l(r,"blur",(e=>{null===e.relatedTarget&&p.trigger()}),!0),l(r,"focus",p.trigger,!0)),p}function b(e,t=!1){const r=(0,p.KR)(),n=()=>r.value=Boolean(e());return n(),(0,o.rd)(n,t),r}Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;function d(e){return JSON.parse(JSON.stringify(e))}const j="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof window?window:"undefined"!==typeof global?global:"undefined"!==typeof self?self:{},f="__vueuse_ssr_handlers__";j[f]=j[f]||{};j[f];Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;function w(e,t,{window:r=s,initialValue:i=""}={}){const u=(0,p.KR)(i),l=(0,n.EW)((()=>{var e;return c(t)||(null==(e=null==r?void 0:r.document)?void 0:e.documentElement)}));return(0,n.wB)([l,()=>(0,o.x_)(e)],(([e,t])=>{var o;if(e&&r){const n=null==(o=r.getComputedStyle(e).getPropertyValue(t))?void 0:o.trim();u.value=n||i}}),{immediate:!0}),(0,n.wB)(u,(t=>{var r;(null==(r=l.value)?void 0:r.style)&&l.value.style.setProperty((0,o.x_)(e),t)})),u}Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;function m({document:e=u}={}){if(!e)return(0,p.KR)("visible");const t=(0,p.KR)(e.visibilityState);return l(e,"visibilitychange",(()=>{t.value=e.visibilityState})),t}Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;var v=Object.getOwnPropertySymbols,P=Object.prototype.hasOwnProperty,g=Object.prototype.propertyIsEnumerable,h=(e,t)=>{var r={};for(var o in e)P.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&v)for(var o of v(e))t.indexOf(o)<0&&g.call(e,o)&&(r[o]=e[o]);return r};function E(e,t,r={}){const p=r,{window:i=s}=p,u=h(p,["window"]);let l;const a=b((()=>i&&"ResizeObserver"in i)),y=()=>{l&&(l.disconnect(),l=void 0)},O=(0,n.wB)((()=>c(e)),(e=>{y(),a.value&&i&&e&&(l=new ResizeObserver(t),l.observe(e,u))}),{immediate:!0,flush:"post"}),d=()=>{y(),O()};return(0,o.Uo)(d),{isSupported:a,stop:d}}function I(e,t={}){const{reset:r=!0,windowResize:i=!0,windowScroll:s=!0,immediate:u=!0}=t,a=(0,p.KR)(0),y=(0,p.KR)(0),O=(0,p.KR)(0),b=(0,p.KR)(0),d=(0,p.KR)(0),j=(0,p.KR)(0),f=(0,p.KR)(0),w=(0,p.KR)(0);function m(){const t=c(e);if(!t)return void(r&&(a.value=0,y.value=0,O.value=0,b.value=0,d.value=0,j.value=0,f.value=0,w.value=0));const o=t.getBoundingClientRect();a.value=o.height,y.value=o.bottom,O.value=o.left,b.value=o.right,d.value=o.top,j.value=o.width,f.value=o.x,w.value=o.y}return E(e,m),(0,n.wB)((()=>c(e)),(e=>!e&&m())),s&&l("scroll",m,{capture:!0,passive:!0}),i&&l("resize",m,{passive:!0}),(0,o.rd)((()=>{u&&m()})),{height:a,bottom:y,left:O,right:b,top:d,width:j,x:f,y:w,update:m}}Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;new Map;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;var S=Object.getOwnPropertySymbols,R=Object.prototype.hasOwnProperty,K=Object.prototype.propertyIsEnumerable,x=(e,t)=>{var r={};for(var o in e)R.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&S)for(var o of S(e))t.indexOf(o)<0&&K.call(e,o)&&(r[o]=e[o]);return r};function B(e,t,r={}){const p=r,{window:i=s}=p,u=x(p,["window"]);let l;const a=b((()=>i&&"MutationObserver"in i)),y=()=>{l&&(l.disconnect(),l=void 0)},O=(0,n.wB)((()=>c(e)),(e=>{y(),a.value&&i&&e&&(l=new MutationObserver(t),l.observe(e,u))}),{immediate:!0}),d=()=>{y(),O()};return(0,o.Uo)(d),{isSupported:a,stop:d}}Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;var D;(function(e){e["UP"]="UP",e["RIGHT"]="RIGHT",e["DOWN"]="DOWN",e["LEFT"]="LEFT",e["NONE"]="NONE"})(D||(D={}));Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;var _=Object.defineProperty,C=Object.getOwnPropertySymbols,Q=Object.prototype.hasOwnProperty,A=Object.prototype.propertyIsEnumerable,N=(e,t,r)=>t in e?_(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,k=(e,t)=>{for(var r in t||(t={}))Q.call(t,r)&&N(e,r,t[r]);if(C)for(var r of C(t))A.call(t,r)&&N(e,r,t[r]);return e};const W={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]};k({linear:o.D_},W);function T(e,t,r,c={}){var s,u,l,a,y;const{clone:O=!1,passive:b=!1,eventName:j,deep:f=!1,defaultValue:w}=c,m=(0,n.nI)(),v=r||(null==m?void 0:m.emit)||(null==(s=null==m?void 0:m.$emit)?void 0:s.bind(m))||(null==(l=null==(u=null==m?void 0:m.proxy)?void 0:u.$emit)?void 0:l.bind(null==m?void 0:m.proxy));let P=j;if(!t)if(i.LER){const e=null==(y=null==(a=null==m?void 0:m.proxy)?void 0:a.$options)?void 0:y.model;t=(null==e?void 0:e.value)||"value",j||(P=(null==e?void 0:e.event)||"input")}else t="modelValue";P=j||P||`update:${t.toString()}`;const g=e=>O?(0,o.Tn)(O)?O(e):d(e):e,h=()=>(0,o.C8)(e[t])?g(e[t]):w;if(b){const r=h(),o=(0,p.KR)(r);return(0,n.wB)((()=>e[t]),(e=>o.value=g(e))),(0,n.wB)(o,(r=>{(r!==e[t]||f)&&v(P,r)}),{deep:f}),o}return(0,n.EW)({get(){return h()},set(e){v(P,e)}})}function L({window:e=s}={}){if(!e)return(0,p.KR)(!1);const t=(0,p.KR)(e.document.hasFocus());return l(e,"blur",(()=>{t.value=!1})),l(e,"focus",(()=>{t.value=!0})),t}function U(e={}){const{window:t=s,initialWidth:r=1/0,initialHeight:n=1/0,listenOrientation:i=!0,includeScrollbar:c=!0}=e,u=(0,p.KR)(r),a=(0,p.KR)(n),y=()=>{t&&(c?(u.value=t.innerWidth,a.value=t.innerHeight):(u.value=t.document.documentElement.clientWidth,a.value=t.document.documentElement.clientHeight))};return y(),(0,o.rd)(y),l("resize",y,{passive:!0}),i&&l("orientationchange",y,{passive:!0}),{width:u,height:a}}}}]);