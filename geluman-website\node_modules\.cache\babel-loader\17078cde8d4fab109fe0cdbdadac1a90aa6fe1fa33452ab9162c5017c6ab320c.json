{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { getCurrentInstance, ref } from 'vue';\nimport { getKeysMap, getRowIdentity, toggleRowStatus } from '../util.mjs';\nfunction useExpand(watcherData) {\n  const instance = getCurrentInstance();\n  const defaultExpandAll = ref(false);\n  const expandRows = ref([]);\n  const updateExpandRows = () => {\n    const data = watcherData.data.value || [];\n    const rowKey = watcherData.rowKey.value;\n    if (defaultExpandAll.value) {\n      expandRows.value = data.slice();\n    } else if (rowKey) {\n      const expandRowsMap = getKeysMap(expandRows.value, rowKey);\n      expandRows.value = data.reduce((prev, row) => {\n        const rowId = getRowIdentity(row, rowKey);\n        const rowInfo = expandRowsMap[rowId];\n        if (rowInfo) {\n          prev.push(row);\n        }\n        return prev;\n      }, []);\n    } else {\n      expandRows.value = [];\n    }\n  };\n  const toggleRowExpansion = (row, expanded) => {\n    const changed = toggleRowStatus(expandRows.value, row, expanded);\n    if (changed) {\n      instance.emit(\"expand-change\", row, expandRows.value.slice());\n    }\n  };\n  const setExpandRowKeys = rowKeys => {\n    instance.store.assertRowKey();\n    const data = watcherData.data.value || [];\n    const rowKey = watcherData.rowKey.value;\n    const keysMap = getKeysMap(data, rowKey);\n    expandRows.value = rowKeys.reduce((prev, cur) => {\n      const info = keysMap[cur];\n      if (info) {\n        prev.push(info.row);\n      }\n      return prev;\n    }, []);\n  };\n  const isRowExpanded = row => {\n    const rowKey = watcherData.rowKey.value;\n    if (rowKey) {\n      const expandMap = getKeysMap(expandRows.value, rowKey);\n      return !!expandMap[getRowIdentity(row, rowKey)];\n    }\n    return expandRows.value.includes(row);\n  };\n  return {\n    updateExpandRows,\n    toggleRowExpansion,\n    setExpandRowKeys,\n    isRowExpanded,\n    states: {\n      expandRows,\n      defaultExpandAll\n    }\n  };\n}\nexport { useExpand as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}