{"ast": null, "code": "import Collapse from './src/collapse.mjs';\nimport CollapseItem from './src/collapse-item2.mjs';\nexport { collapseEmits, collapseProps, emitChangeFn } from './src/collapse2.mjs';\nexport { collapseItemProps } from './src/collapse-item.mjs';\nexport { collapseContextKey } from './src/constants.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElCollapse = withInstall(Collapse, {\n  CollapseItem\n});\nconst ElCollapseItem = withNoopInstall(CollapseItem);\nexport { ElCollapse, ElCollapseItem, ElCollapse as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}