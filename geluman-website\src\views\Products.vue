<template>
  <div class="products-page">
    <section class="products-hero">
      <div class="hero-content">
        <h1 class="title">创新产品</h1>
        <p class="subtitle">打造极致用户体验的数字化工具</p>
      </div>
      <div class="hero-background">
        <div class="floating-circle"></div>
        <div class="floating-dots"></div>
      </div>
    </section>

    <section class="products-grid">
      <div class="container">
        <!-- 智能高亮助手 -->
        <div class="product-card">
          <div class="product-badge">Browser Extension</div>
          <div class="product-header">
            <h2>智能高亮助手</h2>
            <p class="description">
              专业的网页文本智能高亮工具，让阅读和学习更高效。支持多分类管理、
              颜色自定义、关键词管理和配置导出分享。
            </p>
          </div>
          <div class="features">
            <div class="feature">
              <el-icon><Files /></el-icon>
              <span>多分类高亮</span>
            </div>
            <div class="feature">
              <el-icon><Brush /></el-icon>
              <span>颜色自定义</span>
            </div>
            <div class="feature">
              <el-icon><Share /></el-icon>
              <span>配置分享</span>
            </div>
          </div>
          <div class="actions">
            <router-link to="/products/highlight" class="learn-more">
              了解更多
              <el-icon class="icon-right"><ArrowRight /></el-icon>
            </router-link>
          </div>
        </div>

        <!-- 网页护眼助手 -->
        <div class="product-card">
          <div class="product-badge">Browser Extension</div>
          <div class="product-header">
            <h2>网页护眼助手</h2>
            <p class="description">
              智能护眼工具，让网页浏览更舒适，保护您的眼睛健康。
              通过智能算法，自动调节屏幕显示效果。
            </p>
          </div>
          <div class="features">
            <div class="feature">
              <el-icon><Monitor /></el-icon>
              <span>智能护眼模式</span>
            </div>
            <div class="feature">
              <el-icon><MagicStick /></el-icon>
              <span>场景自适应</span>
            </div>
            <div class="feature">
              <el-icon><Setting /></el-icon>
              <span>全局控制</span>
            </div>
          </div>
          <div class="actions">
            <router-link to="/products/eyeshield" class="learn-more">
              了解更多
              <el-icon class="icon-right"><ArrowRight /></el-icon>
            </router-link>
          </div>
        </div>

        <!-- 智能翻译助手 -->
        <div class="product-card">
          <div class="product-badge">Browser Extension</div>
          <div class="product-header">
            <h2>智能翻译助手</h2>
            <p class="description">
              高效、准确的网页文本翻译工具，帮助您跨越语言障碍。
              支持多语言翻译、划词翻译、智能识别等功能。
            </p>
          </div>
          <div class="features">
            <div class="feature">
              <el-icon><ChatLineRound /></el-icon>
              <span>多语言支持</span>
            </div>
            <div class="feature">
              <el-icon><Scissors /></el-icon>
              <span>划词翻译</span>
            </div>
            <div class="feature">
              <el-icon><Connection /></el-icon>
              <span>实时翻译</span>
            </div>
          </div>
          <div class="actions">
            <router-link to="/products/translator" class="learn-more">
              了解更多
              <el-icon class="icon-right"><ArrowRight /></el-icon>
            </router-link>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { highlight, eyeshield, clock } from "@/assets";
import {
  HomeFilled,
  ArrowRight,
  Files,
  Brush,
  Share,
  Monitor,
  MagicStick,
  Setting,
  ChatLineRound,
  Scissors,
  Connection,
} from "@element-plus/icons-vue";

const { t } = useI18n();
const router = useRouter();

const navigateToProduct = (path) => {
  router.push(path);
};
</script>

<style lang="scss" scoped>
.products-page {
  padding-top: var(--header-height);
}

.products-hero {
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-dark) 100%
  );
  padding: 4rem 0;
  text-align: center;
  color: white;

  .title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
  }
}

.products-grid {
  padding: 4rem 0;
  background: var(--bg-secondary);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    gap: 3rem;
  }
}

.product-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-base);
  display: flex;
  flex-direction: column;
  gap: 2rem;

  &:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
  }

  .product-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: var(--bg-accent);
    color: var(--primary-color);
    border-radius: 20px;
    font-size: 0.9rem;
    width: fit-content;
  }

  .product-header {
    h2 {
      font-size: 1.8rem;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .description {
      color: var(--text-secondary);
      line-height: 1.6;
    }
  }

  .features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;

    .feature {
      display: flex;
      align-items: center;
      gap: 0.8rem;
      color: var(--text-secondary);

      .el-icon {
        color: var(--primary-color);
        font-size: 1.2rem;
      }
    }
  }

  .actions {
    margin-top: auto;
    display: flex;
    justify-content: flex-end;

    .learn-more {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      transition: all 0.3s ease;

      .icon-right {
        transition: transform 0.3s ease;
      }

      &:hover {
        background: var(--bg-accent);

        .icon-right {
          transform: translateX(4px);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .products-hero {
    padding: 3rem 1rem;
  }

  .products-grid {
    padding: 2rem 1rem;

    .product-card {
      .features {
        grid-template-columns: 1fr;
      }
    }
  }
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;

  .floating-circle {
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.05)
    );
    animation: floatCircle 20s infinite linear;
  }

  .floating-dots {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(
      rgba(255, 255, 255, 0.1) 1px,
      transparent 1px
    );
    background-size: 30px 30px;
    animation: floatDots 40s infinite linear;
  }
}

.product-card {
  .features .feature {
    .el-icon {
      transition: transform 0.3s ease;
    }

    &:hover .el-icon {
      transform: scale(1.2);
    }
  }
}

@keyframes floatCircle {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes floatDots {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0);
  }
}
</style>

<script>
export default {
  name: "ProductsPage",
};
</script>
