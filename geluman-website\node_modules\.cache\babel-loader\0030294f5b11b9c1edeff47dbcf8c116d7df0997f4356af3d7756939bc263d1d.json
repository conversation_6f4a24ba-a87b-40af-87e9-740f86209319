{"ast": null, "code": "import { getCurrentInstance, inject, ref, computed } from 'vue';\nimport { buildProps } from '../../utils/vue/props/runtime.mjs';\nimport { isFunction } from '@vue/shared';\nimport { debugWarn } from '../../utils/error.mjs';\nconst emptyValuesContextKey = Symbol(\"emptyValuesContextKey\");\nconst SCOPE = \"use-empty-values\";\nconst DEFAULT_EMPTY_VALUES = [\"\", void 0, null];\nconst DEFAULT_VALUE_ON_CLEAR = void 0;\nconst useEmptyValuesProps = buildProps({\n  emptyValues: Array,\n  valueOnClear: {\n    type: [String, Number, Boolean, Function],\n    default: void 0,\n    validator: val => isFunction(val) ? !val() : !val\n  }\n});\nconst useEmptyValues = (props, defaultValue) => {\n  const config = getCurrentInstance() ? inject(emptyValuesContextKey, ref({})) : ref({});\n  const emptyValues = computed(() => props.emptyValues || config.value.emptyValues || DEFAULT_EMPTY_VALUES);\n  const valueOnClear = computed(() => {\n    if (isFunction(props.valueOnClear)) {\n      return props.valueOnClear();\n    } else if (props.valueOnClear !== void 0) {\n      return props.valueOnClear;\n    } else if (isFunction(config.value.valueOnClear)) {\n      return config.value.valueOnClear();\n    } else if (config.value.valueOnClear !== void 0) {\n      return config.value.valueOnClear;\n    }\n    return defaultValue !== void 0 ? defaultValue : DEFAULT_VALUE_ON_CLEAR;\n  });\n  const isEmptyValue = value => {\n    return emptyValues.value.includes(value);\n  };\n  if (!emptyValues.value.includes(valueOnClear.value)) {\n    debugWarn(SCOPE, \"value-on-clear should be a value of empty-values\");\n  }\n  return {\n    emptyValues,\n    valueOnClear,\n    isEmptyValue\n  };\n};\nexport { DEFAULT_EMPTY_VALUES, DEFAULT_VALUE_ON_CLEAR, SCOPE, emptyValuesContextKey, useEmptyValues, useEmptyValuesProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}