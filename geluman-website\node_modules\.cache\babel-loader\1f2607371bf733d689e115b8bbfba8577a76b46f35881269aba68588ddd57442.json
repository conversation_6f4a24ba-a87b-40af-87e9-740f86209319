{"ast": null, "code": "import { computed, inject, ref, useSlots, Text } from 'vue';\nimport { buttonGroupContextKey } from './constants.mjs';\nimport { useDeprecated } from '../../../hooks/use-deprecated/index.mjs';\nimport { useGlobalConfig } from '../../config-provider/src/hooks/use-global-config.mjs';\nimport { useFormItem } from '../../form/src/hooks/use-form-item.mjs';\nimport { useFormSize, useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nconst useButton = (props, emit) => {\n  useDeprecated({\n    from: \"type.text\",\n    replacement: \"link\",\n    version: \"3.0.0\",\n    scope: \"props\",\n    ref: \"https://element-plus.org/en-US/component/button.html#button-attributes\"\n  }, computed(() => props.type === \"text\"));\n  const buttonGroupContext = inject(buttonGroupContextKey, void 0);\n  const globalConfig = useGlobalConfig(\"button\");\n  const {\n    form\n  } = useFormItem();\n  const _size = useFormSize(computed(() => buttonGroupContext == null ? void 0 : buttonGroupContext.size));\n  const _disabled = useFormDisabled();\n  const _ref = ref();\n  const slots = useSlots();\n  const _type = computed(() => props.type || (buttonGroupContext == null ? void 0 : buttonGroupContext.type) || \"\");\n  const autoInsertSpace = computed(() => {\n    var _a, _b, _c;\n    return (_c = (_b = props.autoInsertSpace) != null ? _b : (_a = globalConfig.value) == null ? void 0 : _a.autoInsertSpace) != null ? _c : false;\n  });\n  const _props = computed(() => {\n    if (props.tag === \"button\") {\n      return {\n        ariaDisabled: _disabled.value || props.loading,\n        disabled: _disabled.value || props.loading,\n        autofocus: props.autofocus,\n        type: props.nativeType\n      };\n    }\n    return {};\n  });\n  const shouldAddSpace = computed(() => {\n    var _a;\n    const defaultSlot = (_a = slots.default) == null ? void 0 : _a.call(slots);\n    if (autoInsertSpace.value && (defaultSlot == null ? void 0 : defaultSlot.length) === 1) {\n      const slot = defaultSlot[0];\n      if ((slot == null ? void 0 : slot.type) === Text) {\n        const text = slot.children;\n        return /^\\p{Unified_Ideograph}{2}$/u.test(text.trim());\n      }\n    }\n    return false;\n  });\n  const handleClick = evt => {\n    if (_disabled.value || props.loading) {\n      evt.stopPropagation();\n      return;\n    }\n    if (props.nativeType === \"reset\") {\n      form == null ? void 0 : form.resetFields();\n    }\n    emit(\"click\", evt);\n  };\n  return {\n    _disabled,\n    _size,\n    _type,\n    _ref,\n    _props,\n    shouldAddSpace,\n    handleClick\n  };\n};\nexport { useButton };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}