{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { defineComponent, ref, getCurrentInstance, provide, reactive, toRefs, computed, onMounted, withDirectives, openBlock, createElementBlock, normalizeClass, createElementVNode, toDisplayString, renderSlot, vShow, isVNode } from 'vue';\nimport { useMutationObserver } from '@vueuse/core';\nimport { selectGroupKey } from './token.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { castArray } from 'lodash-unified';\nimport { isArray } from '@vue/shared';\nconst _sfc_main = defineComponent({\n  name: \"ElOptionGroup\",\n  componentName: \"ElOptionGroup\",\n  props: {\n    label: String,\n    disabled: Boolean\n  },\n  setup(props) {\n    const ns = useNamespace(\"select\");\n    const groupRef = ref();\n    const instance = getCurrentInstance();\n    const children = ref([]);\n    provide(selectGroupKey, reactive({\n      ...toRefs(props)\n    }));\n    const visible = computed(() => children.value.some(option => option.visible === true));\n    const isOption = node => {\n      var _a;\n      return node.type.name === \"ElOption\" && !!((_a = node.component) == null ? void 0 : _a.proxy);\n    };\n    const flattedChildren = node => {\n      const nodes = castArray(node);\n      const children2 = [];\n      nodes.forEach(child => {\n        var _a;\n        if (!isVNode(child)) return;\n        if (isOption(child)) {\n          children2.push(child.component.proxy);\n        } else if (isArray(child.children) && child.children.length) {\n          children2.push(...flattedChildren(child.children));\n        } else if ((_a = child.component) == null ? void 0 : _a.subTree) {\n          children2.push(...flattedChildren(child.component.subTree));\n        }\n      });\n      return children2;\n    };\n    const updateChildren = () => {\n      children.value = flattedChildren(instance.subTree);\n    };\n    onMounted(() => {\n      updateChildren();\n    });\n    useMutationObserver(groupRef, updateChildren, {\n      attributes: true,\n      subtree: true,\n      childList: true\n    });\n    return {\n      groupRef,\n      visible,\n      ns\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return withDirectives((openBlock(), createElementBlock(\"ul\", {\n    ref: \"groupRef\",\n    class: normalizeClass(_ctx.ns.be(\"group\", \"wrap\"))\n  }, [createElementVNode(\"li\", {\n    class: normalizeClass(_ctx.ns.be(\"group\", \"title\"))\n  }, toDisplayString(_ctx.label), 3), createElementVNode(\"li\", null, [createElementVNode(\"ul\", {\n    class: normalizeClass(_ctx.ns.b(\"group\"))\n  }, [renderSlot(_ctx.$slots, \"default\")], 2)])], 2)), [[vShow, _ctx.visible]]);\n}\nvar OptionGroup = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"option-group.vue\"]]);\nexport { OptionGroup as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}