{"ast": null, "code": "import { defineComponent, computed, provide, toRefs, watch, openBlock, createBlock, resolveDynamicComponent, unref, normalizeClass, withCtx, renderSlot, nextTick } from 'vue';\nimport { pick } from 'lodash-unified';\nimport { checkboxGroupProps, checkboxGroupEmits } from './checkbox-group.mjs';\nimport { checkboxGroupContextKey } from './constants.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormItem, useFormItemInputId } from '../../form/src/hooks/use-form-item.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nconst __default__ = defineComponent({\n  name: \"ElCheckboxGroup\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: checkboxGroupProps,\n  emits: checkboxGroupEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"checkbox\");\n    const {\n      formItem\n    } = useFormItem();\n    const {\n      inputId: groupId,\n      isLabeledByFormItem\n    } = useFormItemInputId(props, {\n      formItemContext: formItem\n    });\n    const changeEvent = async value => {\n      emit(UPDATE_MODEL_EVENT, value);\n      await nextTick();\n      emit(CHANGE_EVENT, value);\n    };\n    const modelValue = computed({\n      get() {\n        return props.modelValue;\n      },\n      set(val) {\n        changeEvent(val);\n      }\n    });\n    provide(checkboxGroupContextKey, {\n      ...pick(toRefs(props), [\"size\", \"min\", \"max\", \"disabled\", \"validateEvent\", \"fill\", \"textColor\"]),\n      modelValue,\n      changeEvent\n    });\n    watch(() => props.modelValue, () => {\n      if (props.validateEvent) {\n        formItem == null ? void 0 : formItem.validate(\"change\").catch(err => debugWarn(err));\n      }\n    });\n    return (_ctx, _cache) => {\n      var _a;\n      return openBlock(), createBlock(resolveDynamicComponent(_ctx.tag), {\n        id: unref(groupId),\n        class: normalizeClass(unref(ns).b(\"group\")),\n        role: \"group\",\n        \"aria-label\": !unref(isLabeledByFormItem) ? _ctx.ariaLabel || \"checkbox-group\" : void 0,\n        \"aria-labelledby\": unref(isLabeledByFormItem) ? (_a = unref(formItem)) == null ? void 0 : _a.labelId : void 0\n      }, {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 8, [\"id\", \"class\", \"aria-label\", \"aria-labelledby\"]);\n    };\n  }\n});\nvar CheckboxGroup = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"checkbox-group.vue\"]]);\nexport { CheckboxGroup as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}