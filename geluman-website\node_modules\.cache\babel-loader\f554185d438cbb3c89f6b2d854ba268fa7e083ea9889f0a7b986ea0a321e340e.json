{"ast": null, "code": "import { uploadBaseProps } from './upload.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { NOOP } from '@vue/shared';\nconst uploadContentProps = buildProps({\n  ...uploadBaseProps,\n  beforeUpload: {\n    type: definePropType(Function),\n    default: NOOP\n  },\n  onRemove: {\n    type: definePropType(Function),\n    default: NOOP\n  },\n  onStart: {\n    type: definePropType(Function),\n    default: NOOP\n  },\n  onSuccess: {\n    type: definePropType(Function),\n    default: NOOP\n  },\n  onProgress: {\n    type: definePropType(Function),\n    default: NOOP\n  },\n  onError: {\n    type: definePropType(Function),\n    default: NOOP\n  },\n  onExceed: {\n    type: definePropType(Function),\n    default: NOOP\n  }\n});\nexport { uploadContentProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}