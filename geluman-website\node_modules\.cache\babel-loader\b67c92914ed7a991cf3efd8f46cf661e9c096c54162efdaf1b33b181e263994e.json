{"ast": null, "code": "import { shallowRef, ref } from 'vue';\nimport { getStyle, setStyle } from '../../../../utils/dom/style.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isUndefined } from '../../../../utils/types.mjs';\nfunction useDragTag({\n  wrapperRef,\n  handleDragged,\n  afterDragged\n}) {\n  const ns = useNamespace(\"input-tag\");\n  const dropIndicatorRef = shallowRef();\n  const showDropIndicator = ref(false);\n  let draggingIndex;\n  let draggingTag;\n  let dropIndex;\n  let dropType;\n  function getTagClassName(index) {\n    return `.${ns.e(\"inner\")} .${ns.namespace.value}-tag:nth-child(${index + 1})`;\n  }\n  function handleDragStart(event, index) {\n    draggingIndex = index;\n    draggingTag = wrapperRef.value.querySelector(getTagClassName(index));\n    if (draggingTag) {\n      draggingTag.style.opacity = \"0.5\";\n    }\n    event.dataTransfer.effectAllowed = \"move\";\n  }\n  function handleDragOver(event, index) {\n    dropIndex = index;\n    event.preventDefault();\n    event.dataTransfer.dropEffect = \"move\";\n    if (isUndefined(draggingIndex) || draggingIndex === index) {\n      showDropIndicator.value = false;\n      return;\n    }\n    const dropPosition = wrapperRef.value.querySelector(getTagClassName(index)).getBoundingClientRect();\n    const dropPrev = !(draggingIndex + 1 === index);\n    const dropNext = !(draggingIndex - 1 === index);\n    const distance = event.clientX - dropPosition.left;\n    const prevPercent = dropPrev ? dropNext ? 0.5 : 1 : -1;\n    const nextPercent = dropNext ? dropPrev ? 0.5 : 0 : 1;\n    if (distance <= dropPosition.width * prevPercent) {\n      dropType = \"before\";\n    } else if (distance > dropPosition.width * nextPercent) {\n      dropType = \"after\";\n    } else {\n      dropType = void 0;\n    }\n    const innerEl = wrapperRef.value.querySelector(`.${ns.e(\"inner\")}`);\n    const innerPosition = innerEl.getBoundingClientRect();\n    const gap = Number.parseFloat(getStyle(innerEl, \"gap\")) / 2;\n    const indicatorTop = dropPosition.top - innerPosition.top;\n    let indicatorLeft = -9999;\n    if (dropType === \"before\") {\n      indicatorLeft = Math.max(dropPosition.left - innerPosition.left - gap, Math.floor(-gap / 2));\n    } else if (dropType === \"after\") {\n      const left = dropPosition.right - innerPosition.left;\n      indicatorLeft = left + (innerPosition.width === left ? Math.floor(gap / 2) : gap);\n    }\n    setStyle(dropIndicatorRef.value, {\n      top: `${indicatorTop}px`,\n      left: `${indicatorLeft}px`\n    });\n    showDropIndicator.value = !!dropType;\n  }\n  function handleDragEnd(event) {\n    event.preventDefault();\n    if (draggingTag) {\n      draggingTag.style.opacity = \"\";\n    }\n    if (dropType && !isUndefined(draggingIndex) && !isUndefined(dropIndex) && draggingIndex !== dropIndex) {\n      handleDragged(draggingIndex, dropIndex, dropType);\n    }\n    showDropIndicator.value = false;\n    draggingIndex = void 0;\n    draggingTag = null;\n    dropIndex = void 0;\n    dropType = void 0;\n    afterDragged == null ? void 0 : afterDragged();\n  }\n  return {\n    dropIndicatorRef,\n    showDropIndicator,\n    handleDragStart,\n    handleDragOver,\n    handleDragEnd\n  };\n}\nexport { useDragTag };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}