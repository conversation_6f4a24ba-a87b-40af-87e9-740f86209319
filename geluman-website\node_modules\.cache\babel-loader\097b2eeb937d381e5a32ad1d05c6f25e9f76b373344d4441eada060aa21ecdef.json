{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { isArray } from '@vue/shared';\nconst checkboxGroupProps = buildProps({\n  modelValue: {\n    type: definePropType(Array),\n    default: () => []\n  },\n  disabled: Boolean,\n  min: Number,\n  max: Number,\n  size: useSizeProp,\n  fill: String,\n  textColor: String,\n  tag: {\n    type: String,\n    default: \"div\"\n  },\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  ...useAriaProps([\"ariaLabel\"])\n});\nconst checkboxGroupEmits = {\n  [UPDATE_MODEL_EVENT]: val => isArray(val),\n  change: val => isArray(val)\n};\nexport { checkboxGroupEmits, checkboxGroupProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}