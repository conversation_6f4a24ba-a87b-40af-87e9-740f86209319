{"ast": null, "code": "import { defineComponent, openBlock, createElement<PERSON><PERSON>, normalizeClass, unref, renderSlot, createCommentVNode, createElementVNode, createBlock, withCtx, resolveDynamicComponent, createTextVNode, toDisplayString, createVNode } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { ElDivider } from '../../divider/index.mjs';\nimport { pageHeaderProps, pageHeaderEmits } from './page-header.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElPageHeader\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: pageHeaderP<PERSON>,\n  emits: pageHeaderEmits,\n  setup(__props, {\n    emit\n  }) {\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"page-header\");\n    function handleClick() {\n      emit(\"back\");\n    }\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ns).b(), {\n          [unref(ns).m(\"has-breadcrumb\")]: !!_ctx.$slots.breadcrumb,\n          [unref(ns).m(\"has-extra\")]: !!_ctx.$slots.extra,\n          [unref(ns).is(\"contentful\")]: !!_ctx.$slots.default\n        }])\n      }, [_ctx.$slots.breadcrumb ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"breadcrumb\"))\n      }, [renderSlot(_ctx.$slots, \"breadcrumb\")], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"header\"))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"left\"))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"back\")),\n        role: \"button\",\n        tabindex: \"0\",\n        onClick: handleClick\n      }, [_ctx.icon || _ctx.$slots.icon ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        \"aria-label\": _ctx.title || unref(t)(\"el.pageHeader.title\"),\n        class: normalizeClass(unref(ns).e(\"icon\"))\n      }, [renderSlot(_ctx.$slots, \"icon\", {}, () => [_ctx.icon ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.icon)))]),\n        _: 1\n      })) : createCommentVNode(\"v-if\", true)])], 10, [\"aria-label\"])) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"title\"))\n      }, [renderSlot(_ctx.$slots, \"title\", {}, () => [createTextVNode(toDisplayString(_ctx.title || unref(t)(\"el.pageHeader.title\")), 1)])], 2)], 2), createVNode(unref(ElDivider), {\n        direction: \"vertical\"\n      }), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"content\"))\n      }, [renderSlot(_ctx.$slots, \"content\", {}, () => [createTextVNode(toDisplayString(_ctx.content), 1)])], 2)], 2), _ctx.$slots.extra ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"extra\"))\n      }, [renderSlot(_ctx.$slots, \"extra\")], 2)) : createCommentVNode(\"v-if\", true)], 2), _ctx.$slots.default ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(unref(ns).e(\"main\"))\n      }, [renderSlot(_ctx.$slots, \"default\")], 2)) : createCommentVNode(\"v-if\", true)], 2);\n    };\n  }\n});\nvar PageHeader = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"page-header.vue\"]]);\nexport { PageHeader as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}