{"ast": null, "code": "const backtopProps = {\n  visibilityHeight: {\n    type: Number,\n    default: 200\n  },\n  target: {\n    type: String,\n    default: \"\"\n  },\n  right: {\n    type: Number,\n    default: 40\n  },\n  bottom: {\n    type: Number,\n    default: 40\n  }\n};\nconst backtopEmits = {\n  click: evt => evt instanceof MouseEvent\n};\nexport { backtopEmits, backtopProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}