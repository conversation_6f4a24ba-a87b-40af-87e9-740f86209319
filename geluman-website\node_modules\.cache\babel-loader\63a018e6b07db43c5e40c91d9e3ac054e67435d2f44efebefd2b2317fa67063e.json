{"ast": null, "code": "import { inject, computed } from 'vue';\nconst useDropdown = () => {\n  const elDropdown = inject(\"elDropdown\", {});\n  const _elDropdownSize = computed(() => elDropdown == null ? void 0 : elDropdown.dropdownSize);\n  return {\n    elDropdown,\n    _elDropdownSize\n  };\n};\nexport { useDropdown };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}