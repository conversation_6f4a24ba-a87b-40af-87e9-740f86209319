{"ast": null, "code": "import createList from '../builders/build-list.mjs';\nimport { isHorizontal } from '../utils.mjs';\nimport { AUTO_ALIGNMENT, CENTERED_ALIGNMENT, END_ALIGNMENT, START_ALIGNMENT, SMART_ALIGNMENT } from '../defaults.mjs';\nimport { isString } from '@vue/shared';\nimport { throwError } from '../../../../utils/error.mjs';\nconst FixedSizeList = createList({\n  name: \"ElFixedSizeList\",\n  getItemOffset: ({\n    itemSize\n  }, index) => index * itemSize,\n  getItemSize: ({\n    itemSize\n  }) => itemSize,\n  getEstimatedTotalSize: ({\n    total,\n    itemSize\n  }) => itemSize * total,\n  getOffset: ({\n    height,\n    total,\n    itemSize,\n    layout,\n    width\n  }, index, alignment, scrollOffset) => {\n    const size = isHorizontal(layout) ? width : height;\n    if (process.env.NODE_ENV !== \"production\" && isString(size)) {\n      throwError(\"[ElVirtualList]\", `\n        You should set\n          width/height\n        to number when your layout is\n          horizontal/vertical\n      `);\n    }\n    const lastItemOffset = Math.max(0, total * itemSize - size);\n    const maxOffset = Math.min(lastItemOffset, index * itemSize);\n    const minOffset = Math.max(0, (index + 1) * itemSize - size);\n    if (alignment === SMART_ALIGNMENT) {\n      if (scrollOffset >= minOffset - size && scrollOffset <= maxOffset + size) {\n        alignment = AUTO_ALIGNMENT;\n      } else {\n        alignment = CENTERED_ALIGNMENT;\n      }\n    }\n    switch (alignment) {\n      case START_ALIGNMENT:\n        {\n          return maxOffset;\n        }\n      case END_ALIGNMENT:\n        {\n          return minOffset;\n        }\n      case CENTERED_ALIGNMENT:\n        {\n          const middleOffset = Math.round(minOffset + (maxOffset - minOffset) / 2);\n          if (middleOffset < Math.ceil(size / 2)) {\n            return 0;\n          } else if (middleOffset > lastItemOffset + Math.floor(size / 2)) {\n            return lastItemOffset;\n          } else {\n            return middleOffset;\n          }\n        }\n      case AUTO_ALIGNMENT:\n      default:\n        {\n          if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n            return scrollOffset;\n          } else if (scrollOffset < minOffset) {\n            return minOffset;\n          } else {\n            return maxOffset;\n          }\n        }\n    }\n  },\n  getStartIndexForOffset: ({\n    total,\n    itemSize\n  }, offset) => Math.max(0, Math.min(total - 1, Math.floor(offset / itemSize))),\n  getStopIndexForStartIndex: ({\n    height,\n    total,\n    itemSize,\n    layout,\n    width\n  }, startIndex, scrollOffset) => {\n    const offset = startIndex * itemSize;\n    const size = isHorizontal(layout) ? width : height;\n    const numVisibleItems = Math.ceil((size + scrollOffset - offset) / itemSize);\n    return Math.max(0, Math.min(total - 1, startIndex + numVisibleItems - 1));\n  },\n  initCache() {\n    return void 0;\n  },\n  clearCache: true,\n  validateProps() {}\n});\nexport { FixedSizeList as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}