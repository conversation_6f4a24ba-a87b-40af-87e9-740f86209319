{"ast": null, "code": "import baseForOwnRight from './_baseForOwnRight.js';\nimport createBaseEach from './_createBaseEach.js';\n\n/**\n * The base implementation of `_.forEachRight` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array|Object} Returns `collection`.\n */\nvar baseEachRight = createBaseEach(baseForOwnRight, true);\nexport default baseEachRight;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}