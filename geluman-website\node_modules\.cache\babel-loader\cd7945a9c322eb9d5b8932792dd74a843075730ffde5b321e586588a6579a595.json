{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { onMounted, onUpdated } from 'vue';\nimport { useEventListener } from '@vueuse/core';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { EVENT_CODE } from '../../../../constants/aria.mjs';\nfunction useKeydown({\n  el$\n}, store) {\n  const ns = useNamespace(\"tree\");\n  onMounted(() => {\n    initTabIndex();\n  });\n  onUpdated(() => {\n    const checkboxItems = Array.from(el$.value.querySelectorAll(\"input[type=checkbox]\"));\n    checkboxItems.forEach(checkbox => {\n      checkbox.setAttribute(\"tabindex\", \"-1\");\n    });\n  });\n  const handleKeydown = ev => {\n    const currentItem = ev.target;\n    if (!currentItem.className.includes(ns.b(\"node\"))) return;\n    const code = ev.code;\n    const treeItems = Array.from(el$.value.querySelectorAll(`.${ns.is(\"focusable\")}[role=treeitem]`));\n    const currentIndex = treeItems.indexOf(currentItem);\n    let nextIndex;\n    if ([EVENT_CODE.up, EVENT_CODE.down].includes(code)) {\n      ev.preventDefault();\n      if (code === EVENT_CODE.up) {\n        nextIndex = currentIndex === -1 ? 0 : currentIndex !== 0 ? currentIndex - 1 : treeItems.length - 1;\n        const startIndex = nextIndex;\n        while (true) {\n          if (store.value.getNode(treeItems[nextIndex].dataset.key).canFocus) break;\n          nextIndex--;\n          if (nextIndex === startIndex) {\n            nextIndex = -1;\n            break;\n          }\n          if (nextIndex < 0) {\n            nextIndex = treeItems.length - 1;\n          }\n        }\n      } else {\n        nextIndex = currentIndex === -1 ? 0 : currentIndex < treeItems.length - 1 ? currentIndex + 1 : 0;\n        const startIndex = nextIndex;\n        while (true) {\n          if (store.value.getNode(treeItems[nextIndex].dataset.key).canFocus) break;\n          nextIndex++;\n          if (nextIndex === startIndex) {\n            nextIndex = -1;\n            break;\n          }\n          if (nextIndex >= treeItems.length) {\n            nextIndex = 0;\n          }\n        }\n      }\n      nextIndex !== -1 && treeItems[nextIndex].focus();\n    }\n    if ([EVENT_CODE.left, EVENT_CODE.right].includes(code)) {\n      ev.preventDefault();\n      currentItem.click();\n    }\n    const hasInput = currentItem.querySelector('[type=\"checkbox\"]');\n    if ([EVENT_CODE.enter, EVENT_CODE.numpadEnter, EVENT_CODE.space].includes(code) && hasInput) {\n      ev.preventDefault();\n      hasInput.click();\n    }\n  };\n  useEventListener(el$, \"keydown\", handleKeydown);\n  const initTabIndex = () => {\n    var _a;\n    const treeItems = Array.from(el$.value.querySelectorAll(`.${ns.is(\"focusable\")}[role=treeitem]`));\n    const checkboxItems = Array.from(el$.value.querySelectorAll(\"input[type=checkbox]\"));\n    checkboxItems.forEach(checkbox => {\n      checkbox.setAttribute(\"tabindex\", \"-1\");\n    });\n    const checkedItem = el$.value.querySelectorAll(`.${ns.is(\"checked\")}[role=treeitem]`);\n    if (checkedItem.length) {\n      checkedItem[0].setAttribute(\"tabindex\", \"0\");\n      return;\n    }\n    (_a = treeItems[0]) == null ? void 0 : _a.setAttribute(\"tabindex\", \"0\");\n  };\n}\nexport { useKeydown };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}