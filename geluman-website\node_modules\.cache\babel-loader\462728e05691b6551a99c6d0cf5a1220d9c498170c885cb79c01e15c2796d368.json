{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { isFunction } from '@vue/shared';\nconst composeRefs = (...refs) => {\n  return el => {\n    refs.forEach(ref => {\n      if (isFunction(ref)) {\n        ref(el);\n      } else {\n        ref.value = el;\n      }\n    });\n  };\n};\nexport { composeRefs };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}