{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { getFixedColumnsClass, getFixedColumnOffset, ensurePosition } from '../util.mjs';\nimport useMapState from './mapState-helper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nfunction useStyle(props) {\n  const {\n    columns\n  } = useMapState();\n  const ns = useNamespace(\"table\");\n  const getCellClasses = (columns2, cellIndex) => {\n    const column = columns2[cellIndex];\n    const classes = [ns.e(\"cell\"), column.id, column.align, column.labelClassName, ...getFixedColumnsClass(ns.b(), cellIndex, column.fixed, props.store)];\n    if (column.className) {\n      classes.push(column.className);\n    }\n    if (!column.children) {\n      classes.push(ns.is(\"leaf\"));\n    }\n    return classes;\n  };\n  const getCellStyles = (column, cellIndex) => {\n    const fixedStyle = getFixedColumnOffset(cellIndex, column.fixed, props.store);\n    ensurePosition(fixedStyle, \"left\");\n    ensurePosition(fixedStyle, \"right\");\n    return fixedStyle;\n  };\n  return {\n    getCellClasses,\n    getCellStyles,\n    columns\n  };\n}\nexport { useStyle as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}