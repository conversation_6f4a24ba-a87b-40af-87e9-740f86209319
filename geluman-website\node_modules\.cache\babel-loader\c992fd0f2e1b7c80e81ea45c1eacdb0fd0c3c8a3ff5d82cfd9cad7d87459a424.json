{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst EventHandler = {\n  type: definePropType(Function)\n};\nconst tooltipV2TriggerProps = buildProps({\n  onBlur: EventHandler,\n  onClick: EventHandler,\n  onFocus: EventHandler,\n  onMouseDown: EventHandler,\n  onMouseEnter: EventHandler,\n  onMouseLeave: EventHandler\n});\nexport { tooltipV2TriggerProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}