{"ast": null, "code": "import SubMenu from './submenu.mjs';\nimport { EVENT_CODE } from '../../../../constants/aria.mjs';\nimport { triggerEvent } from '../../../../utils/dom/aria.mjs';\nclass MenuItem {\n  constructor(domNode, namespace) {\n    this.domNode = domNode;\n    this.submenu = null;\n    this.submenu = null;\n    this.init(namespace);\n  }\n  init(namespace) {\n    this.domNode.setAttribute(\"tabindex\", \"0\");\n    const menuChild = this.domNode.querySelector(`.${namespace}-menu`);\n    if (menuChild) {\n      this.submenu = new SubMenu(this, menuChild);\n    }\n    this.addListeners();\n  }\n  addListeners() {\n    this.domNode.addEventListener(\"keydown\", event => {\n      let prevDef = false;\n      switch (event.code) {\n        case EVENT_CODE.down:\n          {\n            triggerEvent(event.currentTarget, \"mouseenter\");\n            this.submenu && this.submenu.gotoSubIndex(0);\n            prevDef = true;\n            break;\n          }\n        case EVENT_CODE.up:\n          {\n            triggerEvent(event.currentTarget, \"mouseenter\");\n            this.submenu && this.submenu.gotoSubIndex(this.submenu.subMenuItems.length - 1);\n            prevDef = true;\n            break;\n          }\n        case EVENT_CODE.tab:\n          {\n            triggerEvent(event.currentTarget, \"mouseleave\");\n            break;\n          }\n        case EVENT_CODE.enter:\n        case EVENT_CODE.numpadEnter:\n        case EVENT_CODE.space:\n          {\n            prevDef = true;\n            event.currentTarget.click();\n            break;\n          }\n      }\n      if (prevDef) {\n        event.preventDefault();\n      }\n    });\n  }\n}\nexport { MenuItem as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}