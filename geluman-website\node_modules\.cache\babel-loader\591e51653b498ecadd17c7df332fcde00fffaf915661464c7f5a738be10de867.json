{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nconst breadcrumbProps = buildProps({\n  separator: {\n    type: String,\n    default: \"/\"\n  },\n  separatorIcon: {\n    type: iconPropType\n  }\n});\nexport { breadcrumbProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}