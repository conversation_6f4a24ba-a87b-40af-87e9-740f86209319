{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { isNumber, isStringNumber } from '../types.mjs';\nimport { isClient } from '@vueuse/core';\nimport { camelize, isObject, isString } from '@vue/shared';\nimport { entriesOf, keysOf } from '../objects.mjs';\nimport { debugWarn } from '../error.mjs';\nconst SCOPE = \"utils/dom/style\";\nconst classNameToArray = (cls = \"\") => cls.split(\" \").filter(item => !!item.trim());\nconst hasClass = (el, cls) => {\n  if (!el || !cls) return false;\n  if (cls.includes(\" \")) throw new Error(\"className should not contain space.\");\n  return el.classList.contains(cls);\n};\nconst addClass = (el, cls) => {\n  if (!el || !cls.trim()) return;\n  el.classList.add(...classNameToArray(cls));\n};\nconst removeClass = (el, cls) => {\n  if (!el || !cls.trim()) return;\n  el.classList.remove(...classNameToArray(cls));\n};\nconst getStyle = (element, styleName) => {\n  var _a;\n  if (!isClient || !element || !styleName) return \"\";\n  let key = camelize(styleName);\n  if (key === \"float\") key = \"cssFloat\";\n  try {\n    const style = element.style[key];\n    if (style) return style;\n    const computed = (_a = document.defaultView) == null ? void 0 : _a.getComputedStyle(element, \"\");\n    return computed ? computed[key] : \"\";\n  } catch (e) {\n    return element.style[key];\n  }\n};\nconst setStyle = (element, styleName, value) => {\n  if (!element || !styleName) return;\n  if (isObject(styleName)) {\n    entriesOf(styleName).forEach(([prop, value2]) => setStyle(element, prop, value2));\n  } else {\n    const key = camelize(styleName);\n    element.style[key] = value;\n  }\n};\nconst removeStyle = (element, style) => {\n  if (!element || !style) return;\n  if (isObject(style)) {\n    keysOf(style).forEach(prop => removeStyle(element, prop));\n  } else {\n    setStyle(element, style, \"\");\n  }\n};\nfunction addUnit(value, defaultUnit = \"px\") {\n  if (!value) return \"\";\n  if (isNumber(value) || isStringNumber(value)) {\n    return `${value}${defaultUnit}`;\n  } else if (isString(value)) {\n    return value;\n  }\n  debugWarn(SCOPE, \"binding value must be a string or number\");\n}\nexport { addClass, addUnit, classNameToArray, getStyle, hasClass, removeClass, removeStyle, setStyle };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}