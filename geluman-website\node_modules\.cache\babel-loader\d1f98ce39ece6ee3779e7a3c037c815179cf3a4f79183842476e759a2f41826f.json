{"ast": null, "code": "import { defineComponent, inject, ref, openBlock, createElementBlock, Fragment, createVNode } from 'vue';\nimport { GAP } from './util.mjs';\nimport Thumb from './thumb2.mjs';\nimport { barProps } from './bar.mjs';\nimport { scrollbarContextKey } from './constants.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"bar\",\n  props: barProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const scrollbar = inject(scrollbarContextKey);\n    const moveX = ref(0);\n    const moveY = ref(0);\n    const sizeWidth = ref(\"\");\n    const sizeHeight = ref(\"\");\n    const ratioY = ref(1);\n    const ratioX = ref(1);\n    const handleScroll = wrap => {\n      if (wrap) {\n        const offsetHeight = wrap.offsetHeight - GAP;\n        const offsetWidth = wrap.offsetWidth - GAP;\n        moveY.value = wrap.scrollTop * 100 / offsetHeight * ratioY.value;\n        moveX.value = wrap.scrollLeft * 100 / offsetWidth * ratioX.value;\n      }\n    };\n    const update = () => {\n      const wrap = scrollbar == null ? void 0 : scrollbar.wrapElement;\n      if (!wrap) return;\n      const offsetHeight = wrap.offsetHeight - GAP;\n      const offsetWidth = wrap.offsetWidth - GAP;\n      const originalHeight = offsetHeight ** 2 / wrap.scrollHeight;\n      const originalWidth = offsetWidth ** 2 / wrap.scrollWidth;\n      const height = Math.max(originalHeight, props.minSize);\n      const width = Math.max(originalWidth, props.minSize);\n      ratioY.value = originalHeight / (offsetHeight - originalHeight) / (height / (offsetHeight - height));\n      ratioX.value = originalWidth / (offsetWidth - originalWidth) / (width / (offsetWidth - width));\n      sizeHeight.value = height + GAP < offsetHeight ? `${height}px` : \"\";\n      sizeWidth.value = width + GAP < offsetWidth ? `${width}px` : \"\";\n    };\n    expose({\n      handleScroll,\n      update\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(Fragment, null, [createVNode(Thumb, {\n        move: moveX.value,\n        ratio: ratioX.value,\n        size: sizeWidth.value,\n        always: _ctx.always\n      }, null, 8, [\"move\", \"ratio\", \"size\", \"always\"]), createVNode(Thumb, {\n        move: moveY.value,\n        ratio: ratioY.value,\n        size: sizeHeight.value,\n        vertical: \"\",\n        always: _ctx.always\n      }, null, 8, [\"move\", \"ratio\", \"size\", \"always\"])], 64);\n    };\n  }\n});\nvar Bar = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"bar.vue\"]]);\nexport { Bar as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}