{"ast": null, "code": "import { NOOP } from '@vue/shared';\nconst withInstall = (main, extra) => {\n  main.install = app => {\n    for (const comp of [main, ...Object.values(extra != null ? extra : {})]) {\n      app.component(comp.name, comp);\n    }\n  };\n  if (extra) {\n    for (const [key, comp] of Object.entries(extra)) {\n      main[key] = comp;\n    }\n  }\n  return main;\n};\nconst withInstallFunction = (fn, name) => {\n  fn.install = app => {\n    fn._context = app._context;\n    app.config.globalProperties[name] = fn;\n  };\n  return fn;\n};\nconst withInstallDirective = (directive, name) => {\n  directive.install = app => {\n    app.directive(name, directive);\n  };\n  return directive;\n};\nconst withNoopInstall = component => {\n  component.install = NOOP;\n  return component;\n};\nexport { withInstall, withInstallDirective, withInstallFunction, withNoopInstall };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}