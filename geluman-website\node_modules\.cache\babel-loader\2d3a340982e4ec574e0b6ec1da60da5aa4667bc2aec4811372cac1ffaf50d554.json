{"ast": null, "code": "const parseTime = time => {\n  const values = (time || \"\").split(\":\");\n  if (values.length >= 2) {\n    let hours = Number.parseInt(values[0], 10);\n    const minutes = Number.parseInt(values[1], 10);\n    const timeUpper = time.toUpperCase();\n    if (timeUpper.includes(\"AM\") && hours === 12) {\n      hours = 0;\n    } else if (timeUpper.includes(\"PM\") && hours !== 12) {\n      hours += 12;\n    }\n    return {\n      hours,\n      minutes\n    };\n  }\n  return null;\n};\nconst compareTime = (time1, time2) => {\n  const value1 = parseTime(time1);\n  if (!value1) return -1;\n  const value2 = parseTime(time2);\n  if (!value2) return -1;\n  const minutes1 = value1.minutes + value1.hours * 60;\n  const minutes2 = value2.minutes + value2.hours * 60;\n  if (minutes1 === minutes2) {\n    return 0;\n  }\n  return minutes1 > minutes2 ? 1 : -1;\n};\nconst padTime = time => {\n  return `${time}`.padStart(2, \"0\");\n};\nconst formatTime = time => {\n  return `${padTime(time.hours)}:${padTime(time.minutes)}`;\n};\nconst nextTime = (time, step) => {\n  const timeValue = parseTime(time);\n  if (!timeValue) return \"\";\n  const stepValue = parseTime(step);\n  if (!stepValue) return \"\";\n  const next = {\n    hours: timeValue.hours,\n    minutes: timeValue.minutes\n  };\n  next.minutes += stepValue.minutes;\n  next.hours += stepValue.hours;\n  next.hours += Math.floor(next.minutes / 60);\n  next.minutes = next.minutes % 60;\n  return formatTime(next);\n};\nexport { compareTime, formatTime, nextTime, padTime, parseTime };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}