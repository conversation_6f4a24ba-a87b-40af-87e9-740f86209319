{"ast": null, "code": "import { shallowRef, ref, computed } from 'vue';\nimport { useResizeObserver } from '@vueuse/core';\nfunction useCalcInputWidth() {\n  const calculatorRef = shallowRef();\n  const calculatorWidth = ref(0);\n  const MINIMUM_INPUT_WIDTH = 11;\n  const inputStyle = computed(() => ({\n    minWidth: `${Math.max(calculatorWidth.value, MINIMUM_INPUT_WIDTH)}px`\n  }));\n  const resetCalculatorWidth = () => {\n    var _a, _b;\n    calculatorWidth.value = (_b = (_a = calculatorRef.value) == null ? void 0 : _a.getBoundingClientRect().width) != null ? _b : 0;\n  };\n  useResizeObserver(calculatorRef, resetCalculatorWidth);\n  return {\n    calculatorRef,\n    calculatorWidth,\n    inputStyle\n  };\n}\nexport { useCalcInputWidth };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}