{"ast": null, "code": "import { defineComponent, ref, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, renderSlot, createElementVNode, mergeProps } from 'vue';\nimport { timePickerRangeTriggerProps } from './props.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useAttrs } from '../../../../hooks/use-attrs/index.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { useFocusController } from '../../../../hooks/use-focus-controller/index.mjs';\nconst __default__ = defineComponent({\n  name: \"PickerRangeTrigger\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: timePickerRangeTriggerProps,\n  emits: [\"mouseenter\", \"mouseleave\", \"click\", \"touchstart\", \"focus\", \"blur\", \"startInput\", \"endInput\", \"startChange\", \"endChange\"],\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const attrs = useAttrs();\n    const nsDate = useNamespace(\"date\");\n    const nsRange = useNamespace(\"range\");\n    const inputRef = ref();\n    const endInputRef = ref();\n    const {\n      wrapperRef,\n      isFocused\n    } = useFocusController(inputRef);\n    const handleClick = evt => {\n      emit(\"click\", evt);\n    };\n    const handleMouseEnter = evt => {\n      emit(\"mouseenter\", evt);\n    };\n    const handleMouseLeave = evt => {\n      emit(\"mouseleave\", evt);\n    };\n    const handleTouchStart = evt => {\n      emit(\"mouseenter\", evt);\n    };\n    const handleStartInput = evt => {\n      emit(\"startInput\", evt);\n    };\n    const handleEndInput = evt => {\n      emit(\"endInput\", evt);\n    };\n    const handleStartChange = evt => {\n      emit(\"startChange\", evt);\n    };\n    const handleEndChange = evt => {\n      emit(\"endChange\", evt);\n    };\n    const focus = () => {\n      var _a;\n      (_a = inputRef.value) == null ? void 0 : _a.focus();\n    };\n    const blur = () => {\n      var _a, _b;\n      (_a = inputRef.value) == null ? void 0 : _a.blur();\n      (_b = endInputRef.value) == null ? void 0 : _b.blur();\n    };\n    expose({\n      focus,\n      blur\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"wrapperRef\",\n        ref: wrapperRef,\n        class: normalizeClass([unref(nsDate).is(\"active\", unref(isFocused)), _ctx.$attrs.class]),\n        style: normalizeStyle(_ctx.$attrs.style),\n        onClick: handleClick,\n        onMouseenter: handleMouseEnter,\n        onMouseleave: handleMouseLeave,\n        onTouchstartPassive: handleTouchStart\n      }, [renderSlot(_ctx.$slots, \"prefix\"), createElementVNode(\"input\", mergeProps(unref(attrs), {\n        id: _ctx.id && _ctx.id[0],\n        ref_key: \"inputRef\",\n        ref: inputRef,\n        name: _ctx.name && _ctx.name[0],\n        placeholder: _ctx.startPlaceholder,\n        value: _ctx.modelValue && _ctx.modelValue[0],\n        class: unref(nsRange).b(\"input\"),\n        disabled: _ctx.disabled,\n        onInput: handleStartInput,\n        onChange: handleStartChange\n      }), null, 16, [\"id\", \"name\", \"placeholder\", \"value\", \"disabled\"]), renderSlot(_ctx.$slots, \"range-separator\"), createElementVNode(\"input\", mergeProps(unref(attrs), {\n        id: _ctx.id && _ctx.id[1],\n        ref_key: \"endInputRef\",\n        ref: endInputRef,\n        name: _ctx.name && _ctx.name[1],\n        placeholder: _ctx.endPlaceholder,\n        value: _ctx.modelValue && _ctx.modelValue[1],\n        class: unref(nsRange).b(\"input\"),\n        disabled: _ctx.disabled,\n        onInput: handleEndInput,\n        onChange: handleEndChange\n      }), null, 16, [\"id\", \"name\", \"placeholder\", \"value\", \"disabled\"]), renderSlot(_ctx.$slots, \"suffix\")], 38);\n    };\n  }\n});\nvar PickerRangeTrigger = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"picker-range-trigger.vue\"]]);\nexport { PickerRangeTrigger as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}