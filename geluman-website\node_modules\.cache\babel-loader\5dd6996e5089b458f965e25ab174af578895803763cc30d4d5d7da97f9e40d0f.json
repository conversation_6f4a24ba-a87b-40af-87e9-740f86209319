{"ast": null, "code": "import { useTooltipContentProps } from '../../tooltip/src/content.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { NOOP, isString, isObject } from '@vue/shared';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { UPDATE_MODEL_EVENT, INPUT_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nconst autocompleteProps = buildProps({\n  valueKey: {\n    type: String,\n    default: \"value\"\n  },\n  modelValue: {\n    type: [String, Number],\n    default: \"\"\n  },\n  debounce: {\n    type: Number,\n    default: 300\n  },\n  placement: {\n    type: definePropType(String),\n    values: [\"top\", \"top-start\", \"top-end\", \"bottom\", \"bottom-start\", \"bottom-end\"],\n    default: \"bottom-start\"\n  },\n  fetchSuggestions: {\n    type: definePropType([Function, Array]),\n    default: NOOP\n  },\n  popperClass: {\n    type: String,\n    default: \"\"\n  },\n  triggerOnFocus: {\n    type: Boolean,\n    default: true\n  },\n  selectWhenUnmatched: {\n    type: Boolean,\n    default: false\n  },\n  hideLoading: {\n    type: Boolean,\n    default: false\n  },\n  teleported: useTooltipContentProps.teleported,\n  appendTo: useTooltipContentProps.appendTo,\n  highlightFirstItem: {\n    type: Boolean,\n    default: false\n  },\n  fitInputWidth: {\n    type: Boolean,\n    default: false\n  },\n  clearable: {\n    type: Boolean,\n    default: false\n  },\n  disabled: {\n    type: Boolean,\n    default: false\n  },\n  name: String,\n  ...useAriaProps([\"ariaLabel\"])\n});\nconst autocompleteEmits = {\n  [UPDATE_MODEL_EVENT]: value => isString(value),\n  [INPUT_EVENT]: value => isString(value),\n  [CHANGE_EVENT]: value => isString(value),\n  focus: evt => evt instanceof FocusEvent,\n  blur: evt => evt instanceof FocusEvent,\n  clear: () => true,\n  select: item => isObject(item)\n};\nexport { autocompleteEmits, autocompleteProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}