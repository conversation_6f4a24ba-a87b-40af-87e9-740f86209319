{"ast": null, "code": "import { tagProps } from '../../tag/src/tag.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT, INPUT_EVENT } from '../../../constants/event.mjs';\nimport { isArray, isString } from '@vue/shared';\nimport { isUndefined } from '../../../utils/types.mjs';\nconst inputTagProps = buildProps({\n  modelValue: {\n    type: definePropType(Array)\n  },\n  max: Number,\n  tagType: {\n    ...tagProps.type,\n    default: \"info\"\n  },\n  tagEffect: tagProps.effect,\n  trigger: {\n    type: definePropType(String),\n    default: EVENT_CODE.enter\n  },\n  draggable: {\n    type: Boolean,\n    default: false\n  },\n  delimiter: {\n    type: [String, RegExp],\n    default: \"\"\n  },\n  size: useSizeProp,\n  clearable: Boolean,\n  disabled: {\n    type: Boolean,\n    default: void 0\n  },\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  readonly: Boolean,\n  autofocus: Boolean,\n  id: {\n    type: String,\n    default: void 0\n  },\n  tabindex: {\n    type: [String, Number],\n    default: 0\n  },\n  maxlength: {\n    type: [String, Number]\n  },\n  minlength: {\n    type: [String, Number]\n  },\n  placeholder: String,\n  autocomplete: {\n    type: String,\n    default: \"off\"\n  },\n  saveOnBlur: {\n    type: Boolean,\n    default: true\n  },\n  ariaLabel: String\n});\nconst inputTagEmits = {\n  [UPDATE_MODEL_EVENT]: value => isArray(value) || isUndefined(value),\n  [CHANGE_EVENT]: value => isArray(value) || isUndefined(value),\n  [INPUT_EVENT]: value => isString(value),\n  \"add-tag\": value => isString(value),\n  \"remove-tag\": value => isString(value),\n  focus: evt => evt instanceof FocusEvent,\n  blur: evt => evt instanceof FocusEvent,\n  clear: () => true\n};\nexport { inputTagEmits, inputTagProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}