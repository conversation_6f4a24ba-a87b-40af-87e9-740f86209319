{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nconst scrollbarProps = buildProps({\n  height: {\n    type: [String, Number],\n    default: \"\"\n  },\n  maxHeight: {\n    type: [String, Number],\n    default: \"\"\n  },\n  native: {\n    type: Boolean,\n    default: false\n  },\n  wrapStyle: {\n    type: definePropType([String, Object, Array]),\n    default: \"\"\n  },\n  wrapClass: {\n    type: [String, Array],\n    default: \"\"\n  },\n  viewClass: {\n    type: [String, Array],\n    default: \"\"\n  },\n  viewStyle: {\n    type: [String, Array, Object],\n    default: \"\"\n  },\n  noresize: Boolean,\n  tag: {\n    type: String,\n    default: \"div\"\n  },\n  always: Boolean,\n  minSize: {\n    type: Number,\n    default: 20\n  },\n  tabindex: {\n    type: [String, Number],\n    default: void 0\n  },\n  id: String,\n  role: String,\n  ...useAriaProps([\"ariaLabel\", \"ariaOrientation\"])\n});\nconst scrollbarEmits = {\n  scroll: ({\n    scrollTop,\n    scrollLeft\n  }) => [scrollTop, scrollLeft].every(isNumber)\n};\nexport { scrollbarEmits, scrollbarProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}