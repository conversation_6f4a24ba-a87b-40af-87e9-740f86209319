{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { computed, unref } from 'vue';\nimport { SortOrder, oppositeOrderMap } from '../constants.mjs';\nimport { placeholderSign } from '../private.mjs';\nimport { calcColumnStyle } from './utils.mjs';\nimport { isObject } from '@vue/shared';\nfunction useColumns(props, columns, fixed) {\n  const _columns = computed(() => unref(columns).map((column, index) => {\n    var _a, _b;\n    return {\n      ...column,\n      key: (_b = (_a = column.key) != null ? _a : column.dataKey) != null ? _b : index\n    };\n  }));\n  const visibleColumns = computed(() => {\n    return unref(_columns).filter(column => !column.hidden);\n  });\n  const fixedColumnsOnLeft = computed(() => unref(visibleColumns).filter(column => column.fixed === \"left\" || column.fixed === true));\n  const fixedColumnsOnRight = computed(() => unref(visibleColumns).filter(column => column.fixed === \"right\"));\n  const normalColumns = computed(() => unref(visibleColumns).filter(column => !column.fixed));\n  const mainColumns = computed(() => {\n    const ret = [];\n    unref(fixedColumnsOnLeft).forEach(column => {\n      ret.push({\n        ...column,\n        placeholderSign\n      });\n    });\n    unref(normalColumns).forEach(column => {\n      ret.push(column);\n    });\n    unref(fixedColumnsOnRight).forEach(column => {\n      ret.push({\n        ...column,\n        placeholderSign\n      });\n    });\n    return ret;\n  });\n  const hasFixedColumns = computed(() => {\n    return unref(fixedColumnsOnLeft).length || unref(fixedColumnsOnRight).length;\n  });\n  const columnsStyles = computed(() => {\n    return unref(_columns).reduce((style, column) => {\n      style[column.key] = calcColumnStyle(column, unref(fixed), props.fixed);\n      return style;\n    }, {});\n  });\n  const columnsTotalWidth = computed(() => {\n    return unref(visibleColumns).reduce((width, column) => width + column.width, 0);\n  });\n  const getColumn = key => {\n    return unref(_columns).find(column => column.key === key);\n  };\n  const getColumnStyle = key => {\n    return unref(columnsStyles)[key];\n  };\n  const updateColumnWidth = (column, width) => {\n    column.width = width;\n  };\n  function onColumnSorted(e) {\n    var _a;\n    const {\n      key\n    } = e.currentTarget.dataset;\n    if (!key) return;\n    const {\n      sortState,\n      sortBy\n    } = props;\n    let order = SortOrder.ASC;\n    if (isObject(sortState)) {\n      order = oppositeOrderMap[sortState[key]];\n    } else {\n      order = oppositeOrderMap[sortBy.order];\n    }\n    (_a = props.onColumnSort) == null ? void 0 : _a.call(props, {\n      column: getColumn(key),\n      key,\n      order\n    });\n  }\n  return {\n    columns: _columns,\n    columnsStyles,\n    columnsTotalWidth,\n    fixedColumnsOnLeft,\n    fixedColumnsOnRight,\n    hasFixedColumns,\n    mainColumns,\n    normalColumns,\n    visibleColumns,\n    getColumn,\n    getColumnStyle,\n    updateColumnWidth,\n    onColumnSorted\n  };\n}\nexport { useColumns };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}