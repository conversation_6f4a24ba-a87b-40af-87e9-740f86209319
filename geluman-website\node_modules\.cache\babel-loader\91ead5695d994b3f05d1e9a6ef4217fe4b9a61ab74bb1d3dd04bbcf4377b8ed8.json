{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { defineComponent, computed, getCurrentInstance, ref, watch, nextTick, provide, createVNode, renderSlot } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { Plus } from '@element-plus/icons-vue';\nimport { tabsRootContextKey } from './constants.mjs';\nimport TabNav from './tab-nav.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useOrderedChildren } from '../../../hooks/use-ordered-children/index.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { isString } from '@vue/shared';\nimport { isNumber, isUndefined } from '../../../utils/types.mjs';\nconst tabsProps = buildProps({\n  type: {\n    type: String,\n    values: [\"card\", \"border-card\", \"\"],\n    default: \"\"\n  },\n  closable: Boolean,\n  addable: Boolean,\n  modelValue: {\n    type: [String, Number]\n  },\n  editable: Boolean,\n  tabPosition: {\n    type: String,\n    values: [\"top\", \"right\", \"bottom\", \"left\"],\n    default: \"top\"\n  },\n  beforeLeave: {\n    type: definePropType(Function),\n    default: () => true\n  },\n  stretch: Boolean\n});\nconst isPaneName = value => isString(value) || isNumber(value);\nconst tabsEmits = {\n  [UPDATE_MODEL_EVENT]: name => isPaneName(name),\n  tabClick: (pane, ev) => ev instanceof Event,\n  tabChange: name => isPaneName(name),\n  edit: (paneName, action) => [\"remove\", \"add\"].includes(action),\n  tabRemove: name => isPaneName(name),\n  tabAdd: () => true\n};\nconst Tabs = defineComponent({\n  name: \"ElTabs\",\n  props: tabsProps,\n  emits: tabsEmits,\n  setup(props, {\n    emit,\n    slots,\n    expose\n  }) {\n    var _a;\n    const ns = useNamespace(\"tabs\");\n    const isVertical = computed(() => [\"left\", \"right\"].includes(props.tabPosition));\n    const {\n      children: panes,\n      addChild: sortPane,\n      removeChild: unregisterPane\n    } = useOrderedChildren(getCurrentInstance(), \"ElTabPane\");\n    const nav$ = ref();\n    const currentName = ref((_a = props.modelValue) != null ? _a : \"0\");\n    const setCurrentName = async (value, trigger = false) => {\n      var _a2, _b;\n      if (currentName.value === value || isUndefined(value)) return;\n      try {\n        let canLeave;\n        if (props.beforeLeave) {\n          const result = props.beforeLeave(value, currentName.value);\n          canLeave = result instanceof Promise ? await result : result;\n        } else {\n          canLeave = true;\n        }\n        if (canLeave !== false) {\n          currentName.value = value;\n          if (trigger) {\n            emit(UPDATE_MODEL_EVENT, value);\n            emit(\"tabChange\", value);\n          }\n          (_b = (_a2 = nav$.value) == null ? void 0 : _a2.removeFocus) == null ? void 0 : _b.call(_a2);\n        }\n      } catch (e) {}\n    };\n    const handleTabClick = (tab, tabName, event) => {\n      if (tab.props.disabled) return;\n      emit(\"tabClick\", tab, event);\n      setCurrentName(tabName, true);\n    };\n    const handleTabRemove = (pane, ev) => {\n      if (pane.props.disabled || isUndefined(pane.props.name)) return;\n      ev.stopPropagation();\n      emit(\"edit\", pane.props.name, \"remove\");\n      emit(\"tabRemove\", pane.props.name);\n    };\n    const handleTabAdd = () => {\n      emit(\"edit\", void 0, \"add\");\n      emit(\"tabAdd\");\n    };\n    watch(() => props.modelValue, modelValue => setCurrentName(modelValue));\n    watch(currentName, async () => {\n      var _a2;\n      await nextTick();\n      (_a2 = nav$.value) == null ? void 0 : _a2.scrollToActiveTab();\n    });\n    provide(tabsRootContextKey, {\n      props,\n      currentName,\n      registerPane: pane => {\n        panes.value.push(pane);\n      },\n      sortPane,\n      unregisterPane\n    });\n    expose({\n      currentName\n    });\n    const TabNavRenderer = ({\n      render\n    }) => {\n      return render();\n    };\n    return () => {\n      const addSlot = slots[\"add-icon\"];\n      const newButton = props.editable || props.addable ? createVNode(\"div\", {\n        \"class\": [ns.e(\"new-tab\"), isVertical.value && ns.e(\"new-tab-vertical\")],\n        \"tabindex\": \"0\",\n        \"onClick\": handleTabAdd,\n        \"onKeydown\": ev => {\n          if ([EVENT_CODE.enter, EVENT_CODE.numpadEnter].includes(ev.code)) handleTabAdd();\n        }\n      }, [addSlot ? renderSlot(slots, \"add-icon\") : createVNode(ElIcon, {\n        \"class\": ns.is(\"icon-plus\")\n      }, {\n        default: () => [createVNode(Plus, null, null)]\n      })]) : null;\n      const header = createVNode(\"div\", {\n        \"class\": [ns.e(\"header\"), isVertical.value && ns.e(\"header-vertical\"), ns.is(props.tabPosition)]\n      }, [createVNode(TabNavRenderer, {\n        \"render\": () => {\n          const hasLabelSlot = panes.value.some(pane => pane.slots.label);\n          return createVNode(TabNav, {\n            ref: nav$,\n            currentName: currentName.value,\n            editable: props.editable,\n            type: props.type,\n            panes: panes.value,\n            stretch: props.stretch,\n            onTabClick: handleTabClick,\n            onTabRemove: handleTabRemove\n          }, {\n            $stable: !hasLabelSlot\n          });\n        }\n      }, null), newButton]);\n      const panels = createVNode(\"div\", {\n        \"class\": ns.e(\"content\")\n      }, [renderSlot(slots, \"default\")]);\n      return createVNode(\"div\", {\n        \"class\": [ns.b(), ns.m(props.tabPosition), {\n          [ns.m(\"card\")]: props.type === \"card\",\n          [ns.m(\"border-card\")]: props.type === \"border-card\"\n        }]\n      }, [panels, header]);\n    };\n  }\n});\nvar Tabs$1 = Tabs;\nexport { Tabs$1 as default, tabsEmits, tabsProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}