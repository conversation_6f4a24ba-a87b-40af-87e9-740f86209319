{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst Effect = {\n  LIGHT: \"light\",\n  DARK: \"dark\"\n};\nconst roleTypes = [\"dialog\", \"grid\", \"group\", \"listbox\", \"menu\", \"navigation\", \"tooltip\", \"tree\"];\nconst popperProps = buildProps({\n  role: {\n    type: String,\n    values: roleTypes,\n    default: \"tooltip\"\n  }\n});\nconst usePopperProps = popperProps;\nexport { Effect, popperProps, roleTypes, usePopperProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}