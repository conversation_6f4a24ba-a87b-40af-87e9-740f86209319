{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { isClient } from '@vueuse/core';\nimport { easeInOutCubic } from '../easings.mjs';\nimport { isWindow } from '../types.mjs';\nimport { rAF, cAF } from '../raf.mjs';\nimport { getStyle } from './style.mjs';\nimport { isFunction } from '@vue/shared';\nconst isScroll = (el, isVertical) => {\n  if (!isClient) return false;\n  const key = {\n    undefined: \"overflow\",\n    true: \"overflow-y\",\n    false: \"overflow-x\"\n  }[String(isVertical)];\n  const overflow = getStyle(el, key);\n  return [\"scroll\", \"auto\", \"overlay\"].some(s => overflow.includes(s));\n};\nconst getScrollContainer = (el, isVertical) => {\n  if (!isClient) return;\n  let parent = el;\n  while (parent) {\n    if ([window, document, document.documentElement].includes(parent)) return window;\n    if (isScroll(parent, isVertical)) return parent;\n    parent = parent.parentNode;\n  }\n  return parent;\n};\nlet scrollBarWidth;\nconst getScrollBarWidth = namespace => {\n  var _a;\n  if (!isClient) return 0;\n  if (scrollBarWidth !== void 0) return scrollBarWidth;\n  const outer = document.createElement(\"div\");\n  outer.className = `${namespace}-scrollbar__wrap`;\n  outer.style.visibility = \"hidden\";\n  outer.style.width = \"100px\";\n  outer.style.position = \"absolute\";\n  outer.style.top = \"-9999px\";\n  document.body.appendChild(outer);\n  const widthNoScroll = outer.offsetWidth;\n  outer.style.overflow = \"scroll\";\n  const inner = document.createElement(\"div\");\n  inner.style.width = \"100%\";\n  outer.appendChild(inner);\n  const widthWithScroll = inner.offsetWidth;\n  (_a = outer.parentNode) == null ? void 0 : _a.removeChild(outer);\n  scrollBarWidth = widthNoScroll - widthWithScroll;\n  return scrollBarWidth;\n};\nfunction scrollIntoView(container, selected) {\n  if (!isClient) return;\n  if (!selected) {\n    container.scrollTop = 0;\n    return;\n  }\n  const offsetParents = [];\n  let pointer = selected.offsetParent;\n  while (pointer !== null && container !== pointer && container.contains(pointer)) {\n    offsetParents.push(pointer);\n    pointer = pointer.offsetParent;\n  }\n  const top = selected.offsetTop + offsetParents.reduce((prev, curr) => prev + curr.offsetTop, 0);\n  const bottom = top + selected.offsetHeight;\n  const viewRectTop = container.scrollTop;\n  const viewRectBottom = viewRectTop + container.clientHeight;\n  if (top < viewRectTop) {\n    container.scrollTop = top;\n  } else if (bottom > viewRectBottom) {\n    container.scrollTop = bottom - container.clientHeight;\n  }\n}\nfunction animateScrollTo(container, from, to, duration, callback) {\n  const startTime = Date.now();\n  let handle;\n  const scroll = () => {\n    const timestamp = Date.now();\n    const time = timestamp - startTime;\n    const nextScrollTop = easeInOutCubic(time > duration ? duration : time, from, to, duration);\n    if (isWindow(container)) {\n      container.scrollTo(window.pageXOffset, nextScrollTop);\n    } else {\n      container.scrollTop = nextScrollTop;\n    }\n    if (time < duration) {\n      handle = rAF(scroll);\n    } else if (isFunction(callback)) {\n      callback();\n    }\n  };\n  scroll();\n  return () => {\n    handle && cAF(handle);\n  };\n}\nconst getScrollElement = (target, container) => {\n  if (isWindow(container)) {\n    return target.ownerDocument.documentElement;\n  }\n  return container;\n};\nconst getScrollTop = container => {\n  if (isWindow(container)) {\n    return window.scrollY;\n  }\n  return container.scrollTop;\n};\nexport { animateScrollTo, getScrollBarWidth, getScrollContainer, getScrollElement, getScrollTop, isScroll, scrollIntoView };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}