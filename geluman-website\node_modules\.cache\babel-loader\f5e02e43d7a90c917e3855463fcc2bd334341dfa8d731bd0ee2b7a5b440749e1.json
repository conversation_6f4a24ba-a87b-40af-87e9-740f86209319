{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, normalizeClass, unref, renderSlot } from 'vue';\nimport { collapseProps, collapseEmits } from './collapse2.mjs';\nimport { useCollapse, useCollapseDOM } from './use-collapse.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst __default__ = defineComponent({\n  name: \"ElCollapse\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: collapseProps,\n  emits: collapseEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const {\n      activeNames,\n      setActiveNames\n    } = useCollapse(props, emit);\n    const {\n      rootKls\n    } = useCollapseDOM();\n    expose({\n      activeNames,\n      setActiveNames\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass(unref(rootKls))\n      }, [renderSlot(_ctx.$slots, \"default\")], 2);\n    };\n  }\n});\nvar Collapse = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"collapse.vue\"]]);\nexport { Collapse as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}