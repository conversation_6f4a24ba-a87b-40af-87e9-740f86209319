{"ast": null, "code": "import { uploadListTypes } from './upload.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nimport { NOOP } from '@vue/shared';\nconst uploadListProps = buildProps({\n  files: {\n    type: definePropType(Array),\n    default: () => mutable([])\n  },\n  disabled: {\n    type: Boolean,\n    default: false\n  },\n  handlePreview: {\n    type: definePropType(Function),\n    default: NOOP\n  },\n  listType: {\n    type: String,\n    values: uploadListTypes,\n    default: \"text\"\n  },\n  crossorigin: {\n    type: definePropType(String)\n  }\n});\nconst uploadListEmits = {\n  remove: file => !!file\n};\nexport { uploadListEmits, uploadListProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}