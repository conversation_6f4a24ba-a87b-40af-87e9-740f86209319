{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst skeletonProps = buildProps({\n  animated: {\n    type: Boolean,\n    default: false\n  },\n  count: {\n    type: Number,\n    default: 1\n  },\n  rows: {\n    type: Number,\n    default: 3\n  },\n  loading: {\n    type: Boolean,\n    default: true\n  },\n  throttle: {\n    type: definePropType([Number, Object])\n  }\n});\nexport { skeletonProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}