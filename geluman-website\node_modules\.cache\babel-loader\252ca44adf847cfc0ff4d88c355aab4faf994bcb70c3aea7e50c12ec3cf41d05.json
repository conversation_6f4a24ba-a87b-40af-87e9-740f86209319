{"ast": null, "code": "import baseClamp from './_baseClamp.js';\nimport copyArray from './_copyArray.js';\nimport shuffleSelf from './_shuffleSelf.js';\n\n/**\n * A specialized version of `_.sampleSize` for arrays.\n *\n * @private\n * @param {Array} array The array to sample.\n * @param {number} n The number of elements to sample.\n * @returns {Array} Returns the random elements.\n */\nfunction arraySampleSize(array, n) {\n  return shuffleSelf(copyArray(array), baseClamp(n, 0, array.length));\n}\nexport default arraySampleSize;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}