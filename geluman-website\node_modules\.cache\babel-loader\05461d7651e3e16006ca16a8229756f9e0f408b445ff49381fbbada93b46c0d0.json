{"ast": null, "code": "import { isValidComponentSize } from '../../../utils/vue/validator.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT, INPUT_EVENT } from '../../../constants/event.mjs';\nimport { isBoolean, isNumber } from '../../../utils/types.mjs';\nimport { isString } from '@vue/shared';\nconst switchProps = buildProps({\n  modelValue: {\n    type: [Boolean, String, Number],\n    default: false\n  },\n  disabled: Boolean,\n  loading: Boolean,\n  size: {\n    type: String,\n    validator: isValidComponentSize\n  },\n  width: {\n    type: [String, Number],\n    default: \"\"\n  },\n  inlinePrompt: Boolean,\n  inactiveActionIcon: {\n    type: iconPropType\n  },\n  activeActionIcon: {\n    type: iconPropType\n  },\n  activeIcon: {\n    type: iconPropType\n  },\n  inactiveIcon: {\n    type: iconPropType\n  },\n  activeText: {\n    type: String,\n    default: \"\"\n  },\n  inactiveText: {\n    type: String,\n    default: \"\"\n  },\n  activeValue: {\n    type: [Boolean, String, Number],\n    default: true\n  },\n  inactiveValue: {\n    type: [Boolean, String, Number],\n    default: false\n  },\n  name: {\n    type: String,\n    default: \"\"\n  },\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  beforeChange: {\n    type: definePropType(Function)\n  },\n  id: String,\n  tabindex: {\n    type: [String, Number]\n  },\n  ...useAriaProps([\"ariaLabel\"])\n});\nconst switchEmits = {\n  [UPDATE_MODEL_EVENT]: val => isBoolean(val) || isString(val) || isNumber(val),\n  [CHANGE_EVENT]: val => isBoolean(val) || isString(val) || isNumber(val),\n  [INPUT_EVENT]: val => isBoolean(val) || isString(val) || isNumber(val)\n};\nexport { switchEmits, switchProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}