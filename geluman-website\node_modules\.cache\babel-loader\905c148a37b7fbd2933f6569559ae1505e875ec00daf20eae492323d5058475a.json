{"ast": null, "code": "import \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nexport { castArray as ensureArray } from 'lodash-unified';\nimport { isArray } from '@vue/shared';\nconst unique = arr => [...new Set(arr)];\nconst castArray = arr => {\n  if (!arr && arr !== 0) return [];\n  return isArray(arr) ? arr : [arr];\n};\nexport { castArray, unique };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}