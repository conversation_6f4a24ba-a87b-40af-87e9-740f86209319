{"ast": null, "code": "import Anchor from './src/anchor2.mjs';\nimport AnchorLink from './src/anchor-link2.mjs';\nexport { anchorEmits, anchorProps } from './src/anchor.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElAnchor = withInstall(Anchor, {\n  AnchorLink\n});\nconst ElAnchorLink = withNoopInstall(AnchorLink);\nexport { ElAnchor, ElAnchorLink, ElAnchor as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}