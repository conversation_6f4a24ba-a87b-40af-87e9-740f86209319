{"ast": null, "code": "import { CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { isNumber, isBoolean } from '../../../utils/types.mjs';\nconst affixProps = buildProps({\n  zIndex: {\n    type: definePropType([Number, String]),\n    default: 100\n  },\n  target: {\n    type: String,\n    default: \"\"\n  },\n  offset: {\n    type: Number,\n    default: 0\n  },\n  position: {\n    type: String,\n    values: [\"top\", \"bottom\"],\n    default: \"top\"\n  }\n});\nconst affixEmits = {\n  scroll: ({\n    scrollTop,\n    fixed\n  }) => isNumber(scrollTop) && isBoolean(fixed),\n  [CHANGE_EVENT]: fixed => isBoolean(fixed)\n};\nexport { affixEmits, affixProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}