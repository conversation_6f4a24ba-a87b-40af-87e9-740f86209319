{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { h } from 'vue';\nimport { isUndefined } from '../../../utils/types.mjs';\nfunction hColgroup(props) {\n  const isAuto = props.tableLayout === \"auto\";\n  let columns = props.columns || [];\n  if (isAuto) {\n    if (columns.every(({\n      width\n    }) => isUndefined(width))) {\n      columns = [];\n    }\n  }\n  const getPropsData = column => {\n    const propsData = {\n      key: `${props.tableLayout}_${column.id}`,\n      style: {},\n      name: void 0\n    };\n    if (isAuto) {\n      propsData.style = {\n        width: `${column.width}px`\n      };\n    } else {\n      propsData.name = column.id;\n    }\n    return propsData;\n  };\n  return h(\"colgroup\", {}, columns.map(column => h(\"col\", getPropsData(column))));\n}\nhColgroup.props = [\"columns\", \"tableLayout\"];\nexport { hColgroup };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}