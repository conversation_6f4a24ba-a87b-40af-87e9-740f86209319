{"ast": null, "code": "import { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nconst disabledTimeListsProps = buildProps({\n  disabledHours: {\n    type: definePropType(Function)\n  },\n  disabledMinutes: {\n    type: definePropType(Function)\n  },\n  disabledSeconds: {\n    type: definePropType(Function)\n  }\n});\nconst timePanelSharedProps = buildProps({\n  visible: Boolean,\n  actualVisible: {\n    type: Boolean,\n    default: void 0\n  },\n  format: {\n    type: String,\n    default: \"\"\n  }\n});\nexport { disabledTimeListsProps, timePanelSharedProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}