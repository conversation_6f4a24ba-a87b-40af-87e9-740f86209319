{"ast": null, "code": "import { defineComponent, ref, computed, useAttrs, onMounted, onUpdated, openBlock, createBlock, resolveDynamicComponent, normalizeClass, unref, normalizeStyle, withCtx, renderSlot } from 'vue';\nimport { textProps } from './text.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useFormSize } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isUndefined } from '../../../utils/types.mjs';\nconst __default__ = defineComponent({\n  name: \"ElText\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: textProps,\n  setup(__props) {\n    const props = __props;\n    const textRef = ref();\n    const textSize = useFormSize();\n    const ns = useNamespace(\"text\");\n    const textKls = computed(() => [ns.b(), ns.m(props.type), ns.m(textSize.value), ns.is(\"truncated\", props.truncated), ns.is(\"line-clamp\", !isUndefined(props.lineClamp))]);\n    const inheritTitle = useAttrs().title;\n    const bindTitle = () => {\n      var _a, _b, _c, _d, _e;\n      if (inheritTitle) return;\n      let shouldAddTitle = false;\n      const text = ((_a = textRef.value) == null ? void 0 : _a.textContent) || \"\";\n      if (props.truncated) {\n        const width = (_b = textRef.value) == null ? void 0 : _b.offsetWidth;\n        const scrollWidth = (_c = textRef.value) == null ? void 0 : _c.scrollWidth;\n        if (width && scrollWidth && scrollWidth > width) {\n          shouldAddTitle = true;\n        }\n      } else if (!isUndefined(props.lineClamp)) {\n        const height = (_d = textRef.value) == null ? void 0 : _d.offsetHeight;\n        const scrollHeight = (_e = textRef.value) == null ? void 0 : _e.scrollHeight;\n        if (height && scrollHeight && scrollHeight > height) {\n          shouldAddTitle = true;\n        }\n      }\n      if (shouldAddTitle) {\n        textRef.value.setAttribute(\"title\", text);\n      } else {\n        textRef.value.removeAttribute(\"title\");\n      }\n    };\n    onMounted(bindTitle);\n    onUpdated(bindTitle);\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(resolveDynamicComponent(_ctx.tag), {\n        ref_key: \"textRef\",\n        ref: textRef,\n        class: normalizeClass(unref(textKls)),\n        style: normalizeStyle({\n          \"-webkit-line-clamp\": _ctx.lineClamp\n        })\n      }, {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 8, [\"class\", \"style\"]);\n    };\n  }\n});\nvar Text = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"text.vue\"]]);\nexport { Text as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}