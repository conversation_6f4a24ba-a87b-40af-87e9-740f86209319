<template>
  <loading-screen />
  <el-container class="layout-container">
    <el-header class="header" :class="{ 'header-scrolled': isScrolled }">
      <nav-header />
    </el-header>

    <el-main>
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </el-main>

    <el-footer>
      <site-footer />
    </el-footer>
  </el-container>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import NavHeader from "@/components/layout/NavHeader.vue";
import SiteFooter from "@/components/layout/SiteFooter.vue";
import LoadingScreen from "@/components/common/LoadingScreen.vue";

const isScrolled = ref(false);

const handleScroll = () => {
  isScrolled.value = window.scrollY > 50;
};

onMounted(() => {
  window.addEventListener("scroll", handleScroll);
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
});
</script>

<style lang="scss">
:root {
  --primary-color: #f6c17a;
  --primary-light: #ffd699;
  --primary-dark: #e5a75b;
  --accent-color: #ff9f67;
  --text-primary: #2c3e50;
  --text-secondary: #5f6c7b;
  --bg-primary: #ffffff;
  --bg-secondary: #faf7f5;
  --header-height: 64px;
  --transition-base: all 0.3s ease;
}

.layout-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  position: fixed;
  width: 100%;
  z-index: 1000;
  transition: var(--transition-base);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  padding: 0;
  height: var(--header-height);
}

.header-scrolled {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.el-main {
  padding: 0;
  flex: 1;
}

.el-footer {
  padding: 0;
  margin-top: auto;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
