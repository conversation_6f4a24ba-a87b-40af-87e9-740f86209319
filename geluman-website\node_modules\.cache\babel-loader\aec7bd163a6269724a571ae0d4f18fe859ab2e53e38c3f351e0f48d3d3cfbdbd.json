{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport { defineComponent, computed, ref, openBlock, createElementBlock, normalizeClass, unref, createVNode, mergeProps, createSlots, renderList, withCtx, renderSlot, normalizeProps, guardReactiveProps, createElementVNode, normalizeStyle, withModifiers, nextTick } from 'vue';\nimport { pick } from 'lodash-unified';\nimport { ElInput } from '../../input/index.mjs';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { mentionProps, mentionEmits } from './mention.mjs';\nimport { getCursorPosition, getMentionCtx } from './helper.mjs';\nimport ElMentionDropdown from './mention-dropdown2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { inputProps } from '../../input/src/input2.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { useFocusController } from '../../../hooks/use-focus-controller/index.mjs';\nimport { UPDATE_MODEL_EVENT, INPUT_EVENT } from '../../../constants/event.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { isFunction } from '@vue/shared';\nconst __default__ = defineComponent({\n  name: \"ElMention\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: mentionProps,\n  emits: mentionEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const passInputProps = computed(() => pick(props, Object.keys(inputProps)));\n    const ns = useNamespace(\"mention\");\n    const disabled = useFormDisabled();\n    const contentId = useId();\n    const elInputRef = ref();\n    const tooltipRef = ref();\n    const dropdownRef = ref();\n    const visible = ref(false);\n    const cursorStyle = ref();\n    const mentionCtx = ref();\n    const computedPlacement = computed(() => props.showArrow ? props.placement : `${props.placement}-start`);\n    const computedFallbackPlacements = computed(() => props.showArrow ? [\"bottom\", \"top\"] : [\"bottom-start\", \"top-start\"]);\n    const filteredOptions = computed(() => {\n      const {\n        filterOption,\n        options\n      } = props;\n      if (!mentionCtx.value || !filterOption) return options;\n      return options.filter(option => filterOption(mentionCtx.value.pattern, option));\n    });\n    const dropdownVisible = computed(() => {\n      return visible.value && (!!filteredOptions.value.length || props.loading);\n    });\n    const hoveringId = computed(() => {\n      var _a;\n      return `${contentId.value}-${(_a = dropdownRef.value) == null ? void 0 : _a.hoveringIndex}`;\n    });\n    const handleInputChange = value => {\n      emit(UPDATE_MODEL_EVENT, value);\n      emit(INPUT_EVENT, value);\n      syncAfterCursorMove();\n    };\n    const handleInputKeyDown = event => {\n      var _a, _b, _c, _d;\n      if (!(\"code\" in event) || ((_a = elInputRef.value) == null ? void 0 : _a.isComposing)) return;\n      switch (event.code) {\n        case EVENT_CODE.left:\n        case EVENT_CODE.right:\n          syncAfterCursorMove();\n          break;\n        case EVENT_CODE.up:\n        case EVENT_CODE.down:\n          if (!visible.value) return;\n          event.preventDefault();\n          (_b = dropdownRef.value) == null ? void 0 : _b.navigateOptions(event.code === EVENT_CODE.up ? \"prev\" : \"next\");\n          break;\n        case EVENT_CODE.enter:\n        case EVENT_CODE.numpadEnter:\n          if (!visible.value) return;\n          event.preventDefault();\n          if ((_c = dropdownRef.value) == null ? void 0 : _c.hoverOption) {\n            (_d = dropdownRef.value) == null ? void 0 : _d.selectHoverOption();\n          } else {\n            visible.value = false;\n          }\n          break;\n        case EVENT_CODE.esc:\n          if (!visible.value) return;\n          event.preventDefault();\n          visible.value = false;\n          break;\n        case EVENT_CODE.backspace:\n          if (props.whole && mentionCtx.value) {\n            const {\n              splitIndex,\n              selectionEnd,\n              pattern,\n              prefixIndex,\n              prefix\n            } = mentionCtx.value;\n            const inputEl = getInputEl();\n            if (!inputEl) return;\n            const inputValue = inputEl.value;\n            const matchOption = props.options.find(item => item.value === pattern);\n            const isWhole = isFunction(props.checkIsWhole) ? props.checkIsWhole(pattern, prefix) : matchOption;\n            if (isWhole && splitIndex !== -1 && splitIndex + 1 === selectionEnd) {\n              event.preventDefault();\n              const newValue = inputValue.slice(0, prefixIndex) + inputValue.slice(splitIndex + 1);\n              emit(UPDATE_MODEL_EVENT, newValue);\n              const newSelectionEnd = prefixIndex;\n              nextTick(() => {\n                inputEl.selectionStart = newSelectionEnd;\n                inputEl.selectionEnd = newSelectionEnd;\n                syncDropdownVisible();\n              });\n            }\n          }\n      }\n    };\n    const {\n      wrapperRef\n    } = useFocusController(elInputRef, {\n      beforeFocus() {\n        return disabled.value;\n      },\n      afterFocus() {\n        syncAfterCursorMove();\n      },\n      beforeBlur(event) {\n        var _a;\n        return (_a = tooltipRef.value) == null ? void 0 : _a.isFocusInsideContent(event);\n      },\n      afterBlur() {\n        visible.value = false;\n      }\n    });\n    const handleInputMouseDown = () => {\n      syncAfterCursorMove();\n    };\n    const handleSelect = item => {\n      if (!mentionCtx.value) return;\n      const inputEl = getInputEl();\n      if (!inputEl) return;\n      const inputValue = inputEl.value;\n      const {\n        split\n      } = props;\n      const newEndPart = inputValue.slice(mentionCtx.value.end);\n      const alreadySeparated = newEndPart.startsWith(split);\n      const newMiddlePart = `${item.value}${alreadySeparated ? \"\" : split}`;\n      const newValue = inputValue.slice(0, mentionCtx.value.start) + newMiddlePart + newEndPart;\n      emit(UPDATE_MODEL_EVENT, newValue);\n      emit(INPUT_EVENT, newValue);\n      emit(\"select\", item, mentionCtx.value.prefix);\n      const newSelectionEnd = mentionCtx.value.start + newMiddlePart.length + (alreadySeparated ? 1 : 0);\n      nextTick(() => {\n        inputEl.selectionStart = newSelectionEnd;\n        inputEl.selectionEnd = newSelectionEnd;\n        inputEl.focus();\n        syncDropdownVisible();\n      });\n    };\n    const getInputEl = () => {\n      var _a, _b;\n      return props.type === \"textarea\" ? (_a = elInputRef.value) == null ? void 0 : _a.textarea : (_b = elInputRef.value) == null ? void 0 : _b.input;\n    };\n    const syncAfterCursorMove = () => {\n      setTimeout(() => {\n        syncCursor();\n        syncDropdownVisible();\n        nextTick(() => {\n          var _a;\n          return (_a = tooltipRef.value) == null ? void 0 : _a.updatePopper();\n        });\n      }, 0);\n    };\n    const syncCursor = () => {\n      const inputEl = getInputEl();\n      if (!inputEl) return;\n      const caretPosition = getCursorPosition(inputEl);\n      const inputRect = inputEl.getBoundingClientRect();\n      const elInputRect = elInputRef.value.$el.getBoundingClientRect();\n      cursorStyle.value = {\n        position: \"absolute\",\n        width: 0,\n        height: `${caretPosition.height}px`,\n        left: `${caretPosition.left + inputRect.left - elInputRect.left}px`,\n        top: `${caretPosition.top + inputRect.top - elInputRect.top}px`\n      };\n    };\n    const syncDropdownVisible = () => {\n      const inputEl = getInputEl();\n      if (document.activeElement !== inputEl) {\n        visible.value = false;\n        return;\n      }\n      const {\n        prefix,\n        split\n      } = props;\n      mentionCtx.value = getMentionCtx(inputEl, prefix, split);\n      if (mentionCtx.value && mentionCtx.value.splitIndex === -1) {\n        visible.value = true;\n        emit(\"search\", mentionCtx.value.pattern, mentionCtx.value.prefix);\n        return;\n      }\n      visible.value = false;\n    };\n    expose({\n      input: elInputRef,\n      tooltip: tooltipRef,\n      dropdownVisible\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"wrapperRef\",\n        ref: wrapperRef,\n        class: normalizeClass(unref(ns).b())\n      }, [createVNode(unref(ElInput), mergeProps(mergeProps(unref(passInputProps), _ctx.$attrs), {\n        ref_key: \"elInputRef\",\n        ref: elInputRef,\n        \"model-value\": _ctx.modelValue,\n        disabled: unref(disabled),\n        role: unref(dropdownVisible) ? \"combobox\" : void 0,\n        \"aria-activedescendant\": unref(dropdownVisible) ? unref(hoveringId) || \"\" : void 0,\n        \"aria-controls\": unref(dropdownVisible) ? unref(contentId) : void 0,\n        \"aria-expanded\": unref(dropdownVisible) || void 0,\n        \"aria-label\": _ctx.ariaLabel,\n        \"aria-autocomplete\": unref(dropdownVisible) ? \"none\" : void 0,\n        \"aria-haspopup\": unref(dropdownVisible) ? \"listbox\" : void 0,\n        onInput: handleInputChange,\n        onKeydown: handleInputKeyDown,\n        onMousedown: handleInputMouseDown\n      }), createSlots({\n        _: 2\n      }, [renderList(_ctx.$slots, (_, name) => {\n        return {\n          name,\n          fn: withCtx(slotProps => [renderSlot(_ctx.$slots, name, normalizeProps(guardReactiveProps(slotProps)))])\n        };\n      })]), 1040, [\"model-value\", \"disabled\", \"role\", \"aria-activedescendant\", \"aria-controls\", \"aria-expanded\", \"aria-label\", \"aria-autocomplete\", \"aria-haspopup\"]), createVNode(unref(ElTooltip), {\n        ref_key: \"tooltipRef\",\n        ref: tooltipRef,\n        visible: unref(dropdownVisible),\n        \"popper-class\": [unref(ns).e(\"popper\"), _ctx.popperClass],\n        \"popper-options\": _ctx.popperOptions,\n        placement: unref(computedPlacement),\n        \"fallback-placements\": unref(computedFallbackPlacements),\n        effect: \"light\",\n        pure: \"\",\n        offset: _ctx.offset,\n        \"show-arrow\": _ctx.showArrow\n      }, {\n        default: withCtx(() => [createElementVNode(\"div\", {\n          style: normalizeStyle(cursorStyle.value)\n        }, null, 4)]),\n        content: withCtx(() => {\n          var _a;\n          return [createVNode(ElMentionDropdown, {\n            ref_key: \"dropdownRef\",\n            ref: dropdownRef,\n            options: unref(filteredOptions),\n            disabled: unref(disabled),\n            loading: _ctx.loading,\n            \"content-id\": unref(contentId),\n            \"aria-label\": _ctx.ariaLabel,\n            onSelect: handleSelect,\n            onClick: withModifiers((_a = elInputRef.value) == null ? void 0 : _a.focus, [\"stop\"])\n          }, createSlots({\n            _: 2\n          }, [renderList(_ctx.$slots, (_, name) => {\n            return {\n              name,\n              fn: withCtx(slotProps => [renderSlot(_ctx.$slots, name, normalizeProps(guardReactiveProps(slotProps)))])\n            };\n          })]), 1032, [\"options\", \"disabled\", \"loading\", \"content-id\", \"aria-label\", \"onClick\"])];\n        }),\n        _: 3\n      }, 8, [\"visible\", \"popper-class\", \"popper-options\", \"placement\", \"fallback-placements\", \"offset\", \"show-arrow\"])], 2);\n    };\n  }\n});\nvar Mention = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"mention.vue\"]]);\nexport { Mention as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}