{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport { watch } from 'vue';\nimport { isArray } from '@vue/shared';\nimport { throwError, debugWarn } from '../../../../utils/error.mjs';\nimport { isNumber } from '../../../../utils/types.mjs';\nimport { UPDATE_MODEL_EVENT, INPUT_EVENT } from '../../../../constants/event.mjs';\nconst useWatch = (props, initData, minValue, maxValue, emit, elFormItem) => {\n  const _emit = val => {\n    emit(UPDATE_MODEL_EVENT, val);\n    emit(INPUT_EVENT, val);\n  };\n  const valueChanged = () => {\n    if (props.range) {\n      return ![minValue.value, maxValue.value].every((item, index) => item === initData.oldValue[index]);\n    } else {\n      return props.modelValue !== initData.oldValue;\n    }\n  };\n  const setValues = () => {\n    var _a, _b;\n    if (props.min > props.max) {\n      throwError(\"Slider\", \"min should not be greater than max.\");\n    }\n    const val = props.modelValue;\n    if (props.range && isArray(val)) {\n      if (val[1] < props.min) {\n        _emit([props.min, props.min]);\n      } else if (val[0] > props.max) {\n        _emit([props.max, props.max]);\n      } else if (val[0] < props.min) {\n        _emit([props.min, val[1]]);\n      } else if (val[1] > props.max) {\n        _emit([val[0], props.max]);\n      } else {\n        initData.firstValue = val[0];\n        initData.secondValue = val[1];\n        if (valueChanged()) {\n          if (props.validateEvent) {\n            (_a = elFormItem == null ? void 0 : elFormItem.validate) == null ? void 0 : _a.call(elFormItem, \"change\").catch(err => debugWarn(err));\n          }\n          initData.oldValue = val.slice();\n        }\n      }\n    } else if (!props.range && isNumber(val) && !Number.isNaN(val)) {\n      if (val < props.min) {\n        _emit(props.min);\n      } else if (val > props.max) {\n        _emit(props.max);\n      } else {\n        initData.firstValue = val;\n        if (valueChanged()) {\n          if (props.validateEvent) {\n            (_b = elFormItem == null ? void 0 : elFormItem.validate) == null ? void 0 : _b.call(elFormItem, \"change\").catch(err => debugWarn(err));\n          }\n          initData.oldValue = val;\n        }\n      }\n    }\n  };\n  setValues();\n  watch(() => initData.dragging, val => {\n    if (!val) {\n      setValues();\n    }\n  });\n  watch(() => props.modelValue, (val, oldVal) => {\n    if (initData.dragging || isArray(val) && isArray(oldVal) && val.every((item, index) => item === oldVal[index]) && initData.firstValue === val[0] && initData.secondValue === val[1]) {\n      return;\n    }\n    setValues();\n  }, {\n    deep: true\n  });\n  watch(() => [props.min, props.max], () => {\n    setValues();\n  });\n};\nexport { useWatch };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}