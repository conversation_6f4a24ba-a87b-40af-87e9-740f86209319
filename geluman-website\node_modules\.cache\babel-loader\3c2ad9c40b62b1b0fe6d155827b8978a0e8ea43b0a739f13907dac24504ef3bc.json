{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport { defineComponent, ref, reactive, computed, watch, openBlock, createElementBlock, unref, normalizeClass, createElementVNode, normalizeStyle, Fragment, renderList, renderSlot, createTextVNode, toDisplayString, createCommentVNode } from 'vue';\nimport { useActiveElement, useResizeObserver } from '@vueuse/core';\nimport { segmentedProps, segmentedEmits, defaultProps } from './segmented.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { useFormSize, useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useFormItem, useFormItemInputId } from '../../form/src/hooks/use-form-item.mjs';\nimport { isObject } from '@vue/shared';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nconst __default__ = defineComponent({\n  name: \"ElSegmented\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: segmentedProps,\n  emits: segmentedEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"segmented\");\n    const segmentedId = useId();\n    const segmentedSize = useFormSize();\n    const _disabled = useFormDisabled();\n    const {\n      formItem\n    } = useFormItem();\n    const {\n      inputId,\n      isLabeledByFormItem\n    } = useFormItemInputId(props, {\n      formItemContext: formItem\n    });\n    const segmentedRef = ref(null);\n    const activeElement = useActiveElement();\n    const state = reactive({\n      isInit: false,\n      width: 0,\n      height: 0,\n      translateX: 0,\n      translateY: 0,\n      focusVisible: false\n    });\n    const handleChange = item => {\n      const value = getValue(item);\n      emit(UPDATE_MODEL_EVENT, value);\n      emit(CHANGE_EVENT, value);\n    };\n    const aliasProps = computed(() => ({\n      ...defaultProps,\n      ...props.props\n    }));\n    const getValue = item => {\n      return isObject(item) ? item[aliasProps.value.value] : item;\n    };\n    const getLabel = item => {\n      return isObject(item) ? item[aliasProps.value.label] : item;\n    };\n    const getDisabled = item => {\n      return !!(_disabled.value || (isObject(item) ? item[aliasProps.value.disabled] : false));\n    };\n    const getSelected = item => {\n      return props.modelValue === getValue(item);\n    };\n    const getOption = value => {\n      return props.options.find(item => getValue(item) === value);\n    };\n    const getItemCls = item => {\n      return [ns.e(\"item\"), ns.is(\"selected\", getSelected(item)), ns.is(\"disabled\", getDisabled(item))];\n    };\n    const updateSelect = () => {\n      if (!segmentedRef.value) return;\n      const selectedItem = segmentedRef.value.querySelector(\".is-selected\");\n      const selectedItemInput = segmentedRef.value.querySelector(\".is-selected input\");\n      if (!selectedItem || !selectedItemInput) {\n        state.width = 0;\n        state.height = 0;\n        state.translateX = 0;\n        state.translateY = 0;\n        state.focusVisible = false;\n        return;\n      }\n      const rect = selectedItem.getBoundingClientRect();\n      state.isInit = true;\n      if (props.direction === \"vertical\") {\n        state.height = rect.height;\n        state.translateY = selectedItem.offsetTop;\n      } else {\n        state.width = rect.width;\n        state.translateX = selectedItem.offsetLeft;\n      }\n      try {\n        state.focusVisible = selectedItemInput.matches(\":focus-visible\");\n      } catch (e) {}\n    };\n    const segmentedCls = computed(() => [ns.b(), ns.m(segmentedSize.value), ns.is(\"block\", props.block)]);\n    const selectedStyle = computed(() => ({\n      width: props.direction === \"vertical\" ? \"100%\" : `${state.width}px`,\n      height: props.direction === \"vertical\" ? `${state.height}px` : \"100%\",\n      transform: props.direction === \"vertical\" ? `translateY(${state.translateY}px)` : `translateX(${state.translateX}px)`,\n      display: state.isInit ? \"block\" : \"none\"\n    }));\n    const selectedCls = computed(() => [ns.e(\"item-selected\"), ns.is(\"disabled\", getDisabled(getOption(props.modelValue))), ns.is(\"focus-visible\", state.focusVisible)]);\n    const name = computed(() => {\n      return props.name || segmentedId.value;\n    });\n    useResizeObserver(segmentedRef, updateSelect);\n    watch(activeElement, updateSelect);\n    watch(() => props.modelValue, () => {\n      var _a;\n      updateSelect();\n      if (props.validateEvent) {\n        (_a = formItem == null ? void 0 : formItem.validate) == null ? void 0 : _a.call(formItem, \"change\").catch(err => debugWarn(err));\n      }\n    }, {\n      flush: \"post\"\n    });\n    return (_ctx, _cache) => {\n      return _ctx.options.length ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        id: unref(inputId),\n        ref_key: \"segmentedRef\",\n        ref: segmentedRef,\n        class: normalizeClass(unref(segmentedCls)),\n        role: \"radiogroup\",\n        \"aria-label\": !unref(isLabeledByFormItem) ? _ctx.ariaLabel || \"segmented\" : void 0,\n        \"aria-labelledby\": unref(isLabeledByFormItem) ? unref(formItem).labelId : void 0\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass([unref(ns).e(\"group\"), unref(ns).m(props.direction)])\n      }, [createElementVNode(\"div\", {\n        style: normalizeStyle(unref(selectedStyle)),\n        class: normalizeClass(unref(selectedCls))\n      }, null, 6), (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.options, (item, index) => {\n        return openBlock(), createElementBlock(\"label\", {\n          key: index,\n          class: normalizeClass(getItemCls(item))\n        }, [createElementVNode(\"input\", {\n          class: normalizeClass(unref(ns).e(\"item-input\")),\n          type: \"radio\",\n          name: unref(name),\n          disabled: getDisabled(item),\n          checked: getSelected(item),\n          onChange: $event => handleChange(item)\n        }, null, 42, [\"name\", \"disabled\", \"checked\", \"onChange\"]), createElementVNode(\"div\", {\n          class: normalizeClass(unref(ns).e(\"item-label\"))\n        }, [renderSlot(_ctx.$slots, \"default\", {\n          item\n        }, () => [createTextVNode(toDisplayString(getLabel(item)), 1)])], 2)], 2);\n      }), 128))], 2)], 10, [\"id\", \"aria-label\", \"aria-labelledby\"])) : createCommentVNode(\"v-if\", true);\n    };\n  }\n});\nvar Segmented = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"segmented.vue\"]]);\nexport { Segmented as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}