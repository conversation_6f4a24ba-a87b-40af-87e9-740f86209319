{"ast": null, "code": "import { defineComponent, onBeforeUnmount, openBlock, createElementBlock, unref, normalizeClass, withModifiers, createElementVNode, toDisplayString, createCommentVNode, Fragment, renderList, createVNode } from 'vue';\nimport { basicDateTableProps, basicDateTableEmits } from '../props/basic-date-table.mjs';\nimport { useBasicDateTable, useBasicDateTableDOM } from '../composables/use-basic-date-table.mjs';\nimport ElDatePickerCell from './basic-cell-render.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"basic-date-table\",\n  props: basicDateTableProps,\n  emits: basicDateTableEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const {\n      WEEKS,\n      rows,\n      tbodyRef,\n      currentCellRef,\n      focus,\n      isCurrent,\n      isWeekActive,\n      isSelectedCell,\n      handlePickDate,\n      handleMouseUp,\n      handleMouseDown,\n      handleMouseMove,\n      handleFocus\n    } = useBasicDateTable(props, emit);\n    const {\n      tableLabel,\n      tableKls,\n      weekLabel,\n      getCellClasses,\n      getRowKls,\n      t\n    } = useBasicDateTableDOM(props, {\n      isCurrent,\n      isWeekActive\n    });\n    let isUnmounting = false;\n    onBeforeUnmount(() => {\n      isUnmounting = true;\n    });\n    expose({\n      focus\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"table\", {\n        \"aria-label\": unref(tableLabel),\n        class: normalizeClass(unref(tableKls)),\n        cellspacing: \"0\",\n        cellpadding: \"0\",\n        role: \"grid\",\n        onClick: unref(handlePickDate),\n        onMousemove: unref(handleMouseMove),\n        onMousedown: withModifiers(unref(handleMouseDown), [\"prevent\"]),\n        onMouseup: unref(handleMouseUp)\n      }, [createElementVNode(\"tbody\", {\n        ref_key: \"tbodyRef\",\n        ref: tbodyRef\n      }, [createElementVNode(\"tr\", null, [_ctx.showWeekNumber ? (openBlock(), createElementBlock(\"th\", {\n        key: 0,\n        scope: \"col\"\n      }, toDisplayString(unref(weekLabel)), 1)) : createCommentVNode(\"v-if\", true), (openBlock(true), createElementBlock(Fragment, null, renderList(unref(WEEKS), (week, key) => {\n        return openBlock(), createElementBlock(\"th\", {\n          key,\n          \"aria-label\": unref(t)(\"el.datepicker.weeksFull.\" + week),\n          scope: \"col\"\n        }, toDisplayString(unref(t)(\"el.datepicker.weeks.\" + week)), 9, [\"aria-label\"]);\n      }), 128))]), (openBlock(true), createElementBlock(Fragment, null, renderList(unref(rows), (row, rowKey) => {\n        return openBlock(), createElementBlock(\"tr\", {\n          key: rowKey,\n          class: normalizeClass(unref(getRowKls)(row[1]))\n        }, [(openBlock(true), createElementBlock(Fragment, null, renderList(row, (cell, columnKey) => {\n          return openBlock(), createElementBlock(\"td\", {\n            key: `${rowKey}.${columnKey}`,\n            ref_for: true,\n            ref: el => !unref(isUnmounting) && unref(isSelectedCell)(cell) && (currentCellRef.value = el),\n            class: normalizeClass(unref(getCellClasses)(cell)),\n            \"aria-current\": cell.isCurrent ? \"date\" : void 0,\n            \"aria-selected\": cell.isCurrent,\n            tabindex: unref(isSelectedCell)(cell) ? 0 : -1,\n            onFocus: unref(handleFocus)\n          }, [createVNode(unref(ElDatePickerCell), {\n            cell\n          }, null, 8, [\"cell\"])], 42, [\"aria-current\", \"aria-selected\", \"tabindex\", \"onFocus\"]);\n        }), 128))], 2);\n      }), 128))], 512)], 42, [\"aria-label\", \"onClick\", \"onMousemove\", \"onMousedown\", \"onMouseup\"]);\n    };\n  }\n});\nvar DateTable = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"basic-date-table.vue\"]]);\nexport { DateTable as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}