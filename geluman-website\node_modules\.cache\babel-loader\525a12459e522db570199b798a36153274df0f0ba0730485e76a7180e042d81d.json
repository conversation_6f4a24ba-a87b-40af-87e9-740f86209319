{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { nextTick } from 'vue';\nimport Node from './node.mjs';\nimport { getNodeKey } from './util.mjs';\nimport { hasOwn, isObject } from '@vue/shared';\nimport { isPropAbsent } from '../../../../utils/types.mjs';\nclass TreeStore {\n  constructor(options) {\n    this.currentNode = null;\n    this.currentNodeKey = null;\n    for (const option in options) {\n      if (hasOwn(options, option)) {\n        this[option] = options[option];\n      }\n    }\n    this.nodesMap = {};\n  }\n  initialize() {\n    this.root = new Node({\n      data: this.data,\n      store: this\n    });\n    this.root.initialize();\n    if (this.lazy && this.load) {\n      const loadFn = this.load;\n      loadFn(this.root, data => {\n        this.root.doCreateChildren(data);\n        this._initDefaultCheckedNodes();\n      });\n    } else {\n      this._initDefaultCheckedNodes();\n    }\n  }\n  filter(value) {\n    const filterNodeMethod = this.filterNodeMethod;\n    const lazy = this.lazy;\n    const traverse = async function (node) {\n      const childNodes = node.root ? node.root.childNodes : node.childNodes;\n      for (const [index, child] of childNodes.entries()) {\n        child.visible = filterNodeMethod.call(child, value, child.data, child);\n        if (index % 80 === 0 && index > 0) {\n          await nextTick();\n        }\n        await traverse(child);\n      }\n      if (!node.visible && childNodes.length) {\n        let allHidden = true;\n        allHidden = !childNodes.some(child => child.visible);\n        if (node.root) {\n          node.root.visible = allHidden === false;\n        } else {\n          node.visible = allHidden === false;\n        }\n      }\n      if (!value) return;\n      if (node.visible && !node.isLeaf) {\n        if (!lazy || node.loaded) {\n          node.expand();\n        }\n      }\n    };\n    traverse(this);\n  }\n  setData(newVal) {\n    const instanceChanged = newVal !== this.root.data;\n    if (instanceChanged) {\n      this.nodesMap = {};\n      this.root.setData(newVal);\n      this._initDefaultCheckedNodes();\n      this.setCurrentNodeKey(this.currentNodeKey);\n    } else {\n      this.root.updateChildren();\n    }\n  }\n  getNode(data) {\n    if (data instanceof Node) return data;\n    const key = isObject(data) ? getNodeKey(this.key, data) : data;\n    return this.nodesMap[key] || null;\n  }\n  insertBefore(data, refData) {\n    const refNode = this.getNode(refData);\n    refNode.parent.insertBefore({\n      data\n    }, refNode);\n  }\n  insertAfter(data, refData) {\n    const refNode = this.getNode(refData);\n    refNode.parent.insertAfter({\n      data\n    }, refNode);\n  }\n  remove(data) {\n    const node = this.getNode(data);\n    if (node && node.parent) {\n      if (node === this.currentNode) {\n        this.currentNode = null;\n      }\n      node.parent.removeChild(node);\n    }\n  }\n  append(data, parentData) {\n    const parentNode = !isPropAbsent(parentData) ? this.getNode(parentData) : this.root;\n    if (parentNode) {\n      parentNode.insertChild({\n        data\n      });\n    }\n  }\n  _initDefaultCheckedNodes() {\n    const defaultCheckedKeys = this.defaultCheckedKeys || [];\n    const nodesMap = this.nodesMap;\n    defaultCheckedKeys.forEach(checkedKey => {\n      const node = nodesMap[checkedKey];\n      if (node) {\n        node.setChecked(true, !this.checkStrictly);\n      }\n    });\n  }\n  _initDefaultCheckedNode(node) {\n    const defaultCheckedKeys = this.defaultCheckedKeys || [];\n    if (defaultCheckedKeys.includes(node.key)) {\n      node.setChecked(true, !this.checkStrictly);\n    }\n  }\n  setDefaultCheckedKey(newVal) {\n    if (newVal !== this.defaultCheckedKeys) {\n      this.defaultCheckedKeys = newVal;\n      this._initDefaultCheckedNodes();\n    }\n  }\n  registerNode(node) {\n    const key = this.key;\n    if (!node || !node.data) return;\n    if (!key) {\n      this.nodesMap[node.id] = node;\n    } else {\n      const nodeKey = node.key;\n      if (nodeKey !== void 0) this.nodesMap[node.key] = node;\n    }\n  }\n  deregisterNode(node) {\n    const key = this.key;\n    if (!key || !node || !node.data) return;\n    node.childNodes.forEach(child => {\n      this.deregisterNode(child);\n    });\n    delete this.nodesMap[node.key];\n  }\n  getCheckedNodes(leafOnly = false, includeHalfChecked = false) {\n    const checkedNodes = [];\n    const traverse = function (node) {\n      const childNodes = node.root ? node.root.childNodes : node.childNodes;\n      childNodes.forEach(child => {\n        if ((child.checked || includeHalfChecked && child.indeterminate) && (!leafOnly || leafOnly && child.isLeaf)) {\n          checkedNodes.push(child.data);\n        }\n        traverse(child);\n      });\n    };\n    traverse(this);\n    return checkedNodes;\n  }\n  getCheckedKeys(leafOnly = false) {\n    return this.getCheckedNodes(leafOnly).map(data => (data || {})[this.key]);\n  }\n  getHalfCheckedNodes() {\n    const nodes = [];\n    const traverse = function (node) {\n      const childNodes = node.root ? node.root.childNodes : node.childNodes;\n      childNodes.forEach(child => {\n        if (child.indeterminate) {\n          nodes.push(child.data);\n        }\n        traverse(child);\n      });\n    };\n    traverse(this);\n    return nodes;\n  }\n  getHalfCheckedKeys() {\n    return this.getHalfCheckedNodes().map(data => (data || {})[this.key]);\n  }\n  _getAllNodes() {\n    const allNodes = [];\n    const nodesMap = this.nodesMap;\n    for (const nodeKey in nodesMap) {\n      if (hasOwn(nodesMap, nodeKey)) {\n        allNodes.push(nodesMap[nodeKey]);\n      }\n    }\n    return allNodes;\n  }\n  updateChildren(key, data) {\n    const node = this.nodesMap[key];\n    if (!node) return;\n    const childNodes = node.childNodes;\n    for (let i = childNodes.length - 1; i >= 0; i--) {\n      const child = childNodes[i];\n      this.remove(child.data);\n    }\n    for (let i = 0, j = data.length; i < j; i++) {\n      const child = data[i];\n      this.append(child, node.data);\n    }\n  }\n  _setCheckedKeys(key, leafOnly = false, checkedKeys) {\n    const allNodes = this._getAllNodes().sort((a, b) => a.level - b.level);\n    const cache = /* @__PURE__ */Object.create(null);\n    const keys = Object.keys(checkedKeys);\n    allNodes.forEach(node => node.setChecked(false, false));\n    const cacheCheckedChild = node => {\n      node.childNodes.forEach(child => {\n        var _a;\n        cache[child.data[key]] = true;\n        if ((_a = child.childNodes) == null ? void 0 : _a.length) {\n          cacheCheckedChild(child);\n        }\n      });\n    };\n    for (let i = 0, j = allNodes.length; i < j; i++) {\n      const node = allNodes[i];\n      const nodeKey = node.data[key].toString();\n      const checked = keys.includes(nodeKey);\n      if (!checked) {\n        if (node.checked && !cache[nodeKey]) {\n          node.setChecked(false, false);\n        }\n        continue;\n      }\n      if (node.childNodes.length) {\n        cacheCheckedChild(node);\n      }\n      if (node.isLeaf || this.checkStrictly) {\n        node.setChecked(true, false);\n        continue;\n      }\n      node.setChecked(true, true);\n      if (leafOnly) {\n        node.setChecked(false, false);\n        const traverse = function (node2) {\n          const childNodes = node2.childNodes;\n          childNodes.forEach(child => {\n            if (!child.isLeaf) {\n              child.setChecked(false, false);\n            }\n            traverse(child);\n          });\n        };\n        traverse(node);\n      }\n    }\n  }\n  setCheckedNodes(array, leafOnly = false) {\n    const key = this.key;\n    const checkedKeys = {};\n    array.forEach(item => {\n      checkedKeys[(item || {})[key]] = true;\n    });\n    this._setCheckedKeys(key, leafOnly, checkedKeys);\n  }\n  setCheckedKeys(keys, leafOnly = false) {\n    this.defaultCheckedKeys = keys;\n    const key = this.key;\n    const checkedKeys = {};\n    keys.forEach(key2 => {\n      checkedKeys[key2] = true;\n    });\n    this._setCheckedKeys(key, leafOnly, checkedKeys);\n  }\n  setDefaultExpandedKeys(keys) {\n    keys = keys || [];\n    this.defaultExpandedKeys = keys;\n    keys.forEach(key => {\n      const node = this.getNode(key);\n      if (node) node.expand(null, this.autoExpandParent);\n    });\n  }\n  setChecked(data, checked, deep) {\n    const node = this.getNode(data);\n    if (node) {\n      node.setChecked(!!checked, deep);\n    }\n  }\n  getCurrentNode() {\n    return this.currentNode;\n  }\n  setCurrentNode(currentNode) {\n    const prevCurrentNode = this.currentNode;\n    if (prevCurrentNode) {\n      prevCurrentNode.isCurrent = false;\n    }\n    this.currentNode = currentNode;\n    this.currentNode.isCurrent = true;\n  }\n  setUserCurrentNode(node, shouldAutoExpandParent = true) {\n    const key = node[this.key];\n    const currNode = this.nodesMap[key];\n    this.setCurrentNode(currNode);\n    if (shouldAutoExpandParent && this.currentNode.level > 1) {\n      this.currentNode.parent.expand(null, true);\n    }\n  }\n  setCurrentNodeKey(key, shouldAutoExpandParent = true) {\n    this.currentNodeKey = key;\n    if (isPropAbsent(key)) {\n      this.currentNode && (this.currentNode.isCurrent = false);\n      this.currentNode = null;\n      return;\n    }\n    const node = this.getNode(key);\n    if (node) {\n      this.setCurrentNode(node);\n      if (shouldAutoExpandParent && this.currentNode.level > 1) {\n        this.currentNode.parent.expand(null, true);\n      }\n    }\n  }\n}\nexport { TreeStore as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}