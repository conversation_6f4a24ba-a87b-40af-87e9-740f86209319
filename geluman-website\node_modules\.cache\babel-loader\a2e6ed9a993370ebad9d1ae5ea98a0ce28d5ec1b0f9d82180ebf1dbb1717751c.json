{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, renderSlot } from 'vue';\nimport { checkTagProps, checkTagEmits } from './check-tag.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { CHANGE_EVENT } from '../../../constants/event.mjs';\nconst __default__ = defineComponent({\n  name: \"ElCheckTag\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: checkTagProps,\n  emits: checkTagEmits,\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"check-tag\");\n    const isDisabled = computed(() => props.disabled);\n    const containerKls = computed(() => [ns.b(), ns.is(\"checked\", props.checked), ns.is(\"disabled\", isDisabled.value), ns.m(props.type || \"primary\")]);\n    const handleChange = () => {\n      if (isDisabled.value) return;\n      const checked = !props.checked;\n      emit(CHANGE_EVENT, checked);\n      emit(\"update:checked\", checked);\n    };\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"span\", {\n        class: normalizeClass(unref(containerKls)),\n        onClick: handleChange\n      }, [renderSlot(_ctx.$slots, \"default\")], 2);\n    };\n  }\n});\nvar CheckTag = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"check-tag.vue\"]]);\nexport { CheckTag as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}