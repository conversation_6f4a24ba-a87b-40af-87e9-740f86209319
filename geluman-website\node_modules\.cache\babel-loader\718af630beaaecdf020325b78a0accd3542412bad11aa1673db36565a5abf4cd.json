{"ast": null, "code": "import { createVNode, mergeProps } from 'vue';\nimport { ElIcon } from '../../../icon/index.mjs';\nimport { ArrowRight } from '@element-plus/icons-vue';\nconst ExpandIcon = props => {\n  const {\n    expanded,\n    expandable,\n    onExpand,\n    style,\n    size\n  } = props;\n  const expandIconProps = {\n    onClick: expandable ? () => onExpand(!expanded) : void 0,\n    class: props.class\n  };\n  return createVNode(ElIcon, mergeProps(expandIconProps, {\n    \"size\": size,\n    \"style\": style\n  }), {\n    default: () => [createVNode(ArrowRight, null, null)]\n  });\n};\nvar ExpandIcon$1 = ExpandIcon;\nexport { ExpandIcon$1 as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}