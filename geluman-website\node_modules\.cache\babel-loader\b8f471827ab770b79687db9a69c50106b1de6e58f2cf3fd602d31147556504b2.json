{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nconst countdownProps = buildProps({\n  format: {\n    type: String,\n    default: \"HH:mm:ss\"\n  },\n  prefix: String,\n  suffix: String,\n  title: String,\n  value: {\n    type: definePropType([Number, Object]),\n    default: 0\n  },\n  valueStyle: {\n    type: definePropType([String, Object, Array])\n  }\n});\nconst countdownEmits = {\n  finish: () => true,\n  [CHANGE_EVENT]: value => isNumber(value)\n};\nexport { countdownEmits, countdownProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}