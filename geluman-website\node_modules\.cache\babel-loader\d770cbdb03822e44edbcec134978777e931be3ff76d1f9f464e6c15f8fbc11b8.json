{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, ref, computed, watchEffect, openBlock, createElementBlock, normalizeClass, unref, with<PERSON><PERSON><PERSON>, createCommentVNode, createBlock, Fragment, renderList, toDisplayString } from 'vue';\nimport { DArrowLeft, MoreFilled, DArrowRight } from '@element-plus/icons-vue';\nimport { paginationPagerProps } from './pager.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { CHANGE_EVENT } from '../../../../constants/event.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElPaginationPager\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: paginationPagerProps,\n  emits: [CHANGE_EVENT],\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const nsPager = useNamespace(\"pager\");\n    const nsIcon = useNamespace(\"icon\");\n    const {\n      t\n    } = useLocale();\n    const showPrevMore = ref(false);\n    const showNextMore = ref(false);\n    const quickPrevHover = ref(false);\n    const quickNextHover = ref(false);\n    const quickPrevFocus = ref(false);\n    const quickNextFocus = ref(false);\n    const pagers = computed(() => {\n      const pagerCount = props.pagerCount;\n      const halfPagerCount = (pagerCount - 1) / 2;\n      const currentPage = Number(props.currentPage);\n      const pageCount = Number(props.pageCount);\n      let showPrevMore2 = false;\n      let showNextMore2 = false;\n      if (pageCount > pagerCount) {\n        if (currentPage > pagerCount - halfPagerCount) {\n          showPrevMore2 = true;\n        }\n        if (currentPage < pageCount - halfPagerCount) {\n          showNextMore2 = true;\n        }\n      }\n      const array = [];\n      if (showPrevMore2 && !showNextMore2) {\n        const startPage = pageCount - (pagerCount - 2);\n        for (let i = startPage; i < pageCount; i++) {\n          array.push(i);\n        }\n      } else if (!showPrevMore2 && showNextMore2) {\n        for (let i = 2; i < pagerCount; i++) {\n          array.push(i);\n        }\n      } else if (showPrevMore2 && showNextMore2) {\n        const offset = Math.floor(pagerCount / 2) - 1;\n        for (let i = currentPage - offset; i <= currentPage + offset; i++) {\n          array.push(i);\n        }\n      } else {\n        for (let i = 2; i < pageCount; i++) {\n          array.push(i);\n        }\n      }\n      return array;\n    });\n    const prevMoreKls = computed(() => [\"more\", \"btn-quickprev\", nsIcon.b(), nsPager.is(\"disabled\", props.disabled)]);\n    const nextMoreKls = computed(() => [\"more\", \"btn-quicknext\", nsIcon.b(), nsPager.is(\"disabled\", props.disabled)]);\n    const tabindex = computed(() => props.disabled ? -1 : 0);\n    watchEffect(() => {\n      const halfPagerCount = (props.pagerCount - 1) / 2;\n      showPrevMore.value = false;\n      showNextMore.value = false;\n      if (props.pageCount > props.pagerCount) {\n        if (props.currentPage > props.pagerCount - halfPagerCount) {\n          showPrevMore.value = true;\n        }\n        if (props.currentPage < props.pageCount - halfPagerCount) {\n          showNextMore.value = true;\n        }\n      }\n    });\n    function onMouseEnter(forward = false) {\n      if (props.disabled) return;\n      if (forward) {\n        quickPrevHover.value = true;\n      } else {\n        quickNextHover.value = true;\n      }\n    }\n    function onFocus(forward = false) {\n      if (forward) {\n        quickPrevFocus.value = true;\n      } else {\n        quickNextFocus.value = true;\n      }\n    }\n    function onEnter(e) {\n      const target = e.target;\n      if (target.tagName.toLowerCase() === \"li\" && Array.from(target.classList).includes(\"number\")) {\n        const newPage = Number(target.textContent);\n        if (newPage !== props.currentPage) {\n          emit(CHANGE_EVENT, newPage);\n        }\n      } else if (target.tagName.toLowerCase() === \"li\" && Array.from(target.classList).includes(\"more\")) {\n        onPagerClick(e);\n      }\n    }\n    function onPagerClick(event) {\n      const target = event.target;\n      if (target.tagName.toLowerCase() === \"ul\" || props.disabled) {\n        return;\n      }\n      let newPage = Number(target.textContent);\n      const pageCount = props.pageCount;\n      const currentPage = props.currentPage;\n      const pagerCountOffset = props.pagerCount - 2;\n      if (target.className.includes(\"more\")) {\n        if (target.className.includes(\"quickprev\")) {\n          newPage = currentPage - pagerCountOffset;\n        } else if (target.className.includes(\"quicknext\")) {\n          newPage = currentPage + pagerCountOffset;\n        }\n      }\n      if (!Number.isNaN(+newPage)) {\n        if (newPage < 1) {\n          newPage = 1;\n        }\n        if (newPage > pageCount) {\n          newPage = pageCount;\n        }\n      }\n      if (newPage !== currentPage) {\n        emit(CHANGE_EVENT, newPage);\n      }\n    }\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"ul\", {\n        class: normalizeClass(unref(nsPager).b()),\n        onClick: onPagerClick,\n        onKeyup: withKeys(onEnter, [\"enter\"])\n      }, [_ctx.pageCount > 0 ? (openBlock(), createElementBlock(\"li\", {\n        key: 0,\n        class: normalizeClass([[unref(nsPager).is(\"active\", _ctx.currentPage === 1), unref(nsPager).is(\"disabled\", _ctx.disabled)], \"number\"]),\n        \"aria-current\": _ctx.currentPage === 1,\n        \"aria-label\": unref(t)(\"el.pagination.currentPage\", {\n          pager: 1\n        }),\n        tabindex: unref(tabindex)\n      }, \" 1 \", 10, [\"aria-current\", \"aria-label\", \"tabindex\"])) : createCommentVNode(\"v-if\", true), showPrevMore.value ? (openBlock(), createElementBlock(\"li\", {\n        key: 1,\n        class: normalizeClass(unref(prevMoreKls)),\n        tabindex: unref(tabindex),\n        \"aria-label\": unref(t)(\"el.pagination.prevPages\", {\n          pager: _ctx.pagerCount - 2\n        }),\n        onMouseenter: $event => onMouseEnter(true),\n        onMouseleave: $event => quickPrevHover.value = false,\n        onFocus: $event => onFocus(true),\n        onBlur: $event => quickPrevFocus.value = false\n      }, [(quickPrevHover.value || quickPrevFocus.value) && !_ctx.disabled ? (openBlock(), createBlock(unref(DArrowLeft), {\n        key: 0\n      })) : (openBlock(), createBlock(unref(MoreFilled), {\n        key: 1\n      }))], 42, [\"tabindex\", \"aria-label\", \"onMouseenter\", \"onMouseleave\", \"onFocus\", \"onBlur\"])) : createCommentVNode(\"v-if\", true), (openBlock(true), createElementBlock(Fragment, null, renderList(unref(pagers), pager => {\n        return openBlock(), createElementBlock(\"li\", {\n          key: pager,\n          class: normalizeClass([[unref(nsPager).is(\"active\", _ctx.currentPage === pager), unref(nsPager).is(\"disabled\", _ctx.disabled)], \"number\"]),\n          \"aria-current\": _ctx.currentPage === pager,\n          \"aria-label\": unref(t)(\"el.pagination.currentPage\", {\n            pager\n          }),\n          tabindex: unref(tabindex)\n        }, toDisplayString(pager), 11, [\"aria-current\", \"aria-label\", \"tabindex\"]);\n      }), 128)), showNextMore.value ? (openBlock(), createElementBlock(\"li\", {\n        key: 2,\n        class: normalizeClass(unref(nextMoreKls)),\n        tabindex: unref(tabindex),\n        \"aria-label\": unref(t)(\"el.pagination.nextPages\", {\n          pager: _ctx.pagerCount - 2\n        }),\n        onMouseenter: $event => onMouseEnter(),\n        onMouseleave: $event => quickNextHover.value = false,\n        onFocus: $event => onFocus(),\n        onBlur: $event => quickNextFocus.value = false\n      }, [(quickNextHover.value || quickNextFocus.value) && !_ctx.disabled ? (openBlock(), createBlock(unref(DArrowRight), {\n        key: 0\n      })) : (openBlock(), createBlock(unref(MoreFilled), {\n        key: 1\n      }))], 42, [\"tabindex\", \"aria-label\", \"onMouseenter\", \"onMouseleave\", \"onFocus\", \"onBlur\"])) : createCommentVNode(\"v-if\", true), _ctx.pageCount > 1 ? (openBlock(), createElementBlock(\"li\", {\n        key: 3,\n        class: normalizeClass([[unref(nsPager).is(\"active\", _ctx.currentPage === _ctx.pageCount), unref(nsPager).is(\"disabled\", _ctx.disabled)], \"number\"]),\n        \"aria-current\": _ctx.currentPage === _ctx.pageCount,\n        \"aria-label\": unref(t)(\"el.pagination.currentPage\", {\n          pager: _ctx.pageCount\n        }),\n        tabindex: unref(tabindex)\n      }, toDisplayString(_ctx.pageCount), 11, [\"aria-current\", \"aria-label\", \"tabindex\"])) : createCommentVNode(\"v-if\", true)], 42, [\"onKeyup\"]);\n    };\n  }\n});\nvar Pager = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"pager.vue\"]]);\nexport { Pager as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}