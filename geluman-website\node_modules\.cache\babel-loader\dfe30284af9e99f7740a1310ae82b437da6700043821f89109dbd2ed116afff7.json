{"ast": null, "code": "import { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nconst paginationPagerProps = buildProps({\n  currentPage: {\n    type: Number,\n    default: 1\n  },\n  pageCount: {\n    type: Number,\n    required: true\n  },\n  pagerCount: {\n    type: Number,\n    default: 7\n  },\n  disabled: Boolean\n});\nexport { paginationPagerProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}