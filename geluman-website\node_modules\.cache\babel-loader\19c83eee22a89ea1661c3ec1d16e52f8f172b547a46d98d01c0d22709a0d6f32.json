{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define(t) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_weekYear = t();\n}(this, function () {\n  \"use strict\";\n\n  return function (e, t) {\n    t.prototype.weekYear = function () {\n      var e = this.month(),\n        t = this.week(),\n        n = this.year();\n      return 1 === t && 11 === e ? n + 1 : 0 === e && t >= 52 ? n - 1 : n;\n    };\n  };\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}