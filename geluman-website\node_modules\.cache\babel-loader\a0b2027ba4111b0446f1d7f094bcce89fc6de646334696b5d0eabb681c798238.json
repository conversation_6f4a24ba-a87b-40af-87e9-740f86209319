{"ast": null, "code": "import DatePickPanel from './date-picker-com/panel-date-pick.mjs';\nimport DateRangePickPanel from './date-picker-com/panel-date-range.mjs';\nimport MonthRangePickPanel from './date-picker-com/panel-month-range.mjs';\nimport YearRangePickPanel from './date-picker-com/panel-year-range.mjs';\nconst getPanel = function (type) {\n  switch (type) {\n    case \"daterange\":\n    case \"datetimerange\":\n      {\n        return DateRangePickPanel;\n      }\n    case \"monthrange\":\n      {\n        return MonthRangePickPanel;\n      }\n    case \"yearrange\":\n      {\n        return YearRangePickPanel;\n      }\n    default:\n      {\n        return DatePickPanel;\n      }\n  }\n};\nexport { getPanel };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}