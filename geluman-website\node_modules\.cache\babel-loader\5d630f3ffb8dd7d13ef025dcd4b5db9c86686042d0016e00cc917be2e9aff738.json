{"ast": null, "code": "import { defineComponent, inject, renderSlot, createVNode } from 'vue';\nimport { ROOT_PICKER_INJECTION_KEY } from '../constants.mjs';\nimport { basicCellProps } from '../props/basic-cell.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nvar ElDatePickerCell = defineComponent({\n  name: \"ElDatePickerCell\",\n  props: basicCellProps,\n  setup(props) {\n    const ns = useNamespace(\"date-table-cell\");\n    const {\n      slots\n    } = inject(ROOT_PICKER_INJECTION_KEY);\n    return () => {\n      const {\n        cell\n      } = props;\n      return renderSlot(slots, \"default\", {\n        ...cell\n      }, () => {\n        var _a;\n        return [createVNode(\"div\", {\n          \"class\": ns.b()\n        }, [createVNode(\"span\", {\n          \"class\": ns.e(\"text\")\n        }, [(_a = cell == null ? void 0 : cell.renderText) != null ? _a : cell == null ? void 0 : cell.text])])];\n      });\n    };\n  }\n});\nexport { ElDatePickerCell as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}