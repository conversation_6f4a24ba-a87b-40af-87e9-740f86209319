{"ast": null, "code": "import { defineComponent, getCurrentInstance, ref, computed, watch, onBeforeUnmount, provide, toRef, unref, resolveComponent, openBlock, createElementBlock, normalizeClass, createVNode, createSlots, withCtx, renderSlot, createBlock, mergeProps, createCommentVNode } from 'vue';\nimport { ElButton } from '../../button/index.mjs';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { ElScrollbar } from '../../scrollbar/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport ElRovingFocusGroup from '../../roving-focus-group/src/roving-focus-group2.mjs';\nimport { ArrowDown } from '@element-plus/icons-vue';\nimport { ElCollection, dropdownProps } from './dropdown.mjs';\nimport { DROPDOWN_INJECTION_KEY } from './tokens.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { OnlyChild } from '../../slot/src/only-child.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nimport { castArray } from 'lodash-unified';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { useFormSize } from '../../form/src/hooks/use-form-common-props.mjs';\nconst {\n  ButtonGroup: ElButtonGroup\n} = ElButton;\nconst _sfc_main = defineComponent({\n  name: \"ElDropdown\",\n  components: {\n    ElButton,\n    ElButtonGroup,\n    ElScrollbar,\n    ElDropdownCollection: ElCollection,\n    ElTooltip,\n    ElRovingFocusGroup,\n    ElOnlyChild: OnlyChild,\n    ElIcon,\n    ArrowDown\n  },\n  props: dropdownProps,\n  emits: [\"visible-change\", \"click\", \"command\"],\n  setup(props, {\n    emit\n  }) {\n    const _instance = getCurrentInstance();\n    const ns = useNamespace(\"dropdown\");\n    const {\n      t\n    } = useLocale();\n    const triggeringElementRef = ref();\n    const referenceElementRef = ref();\n    const popperRef = ref();\n    const contentRef = ref();\n    const scrollbar = ref(null);\n    const currentTabId = ref(null);\n    const isUsingKeyboard = ref(false);\n    const wrapStyle = computed(() => ({\n      maxHeight: addUnit(props.maxHeight)\n    }));\n    const dropdownTriggerKls = computed(() => [ns.m(dropdownSize.value)]);\n    const trigger = computed(() => castArray(props.trigger));\n    const defaultTriggerId = useId().value;\n    const triggerId = computed(() => props.id || defaultTriggerId);\n    watch([triggeringElementRef, trigger], ([triggeringElement, trigger2], [prevTriggeringElement]) => {\n      var _a, _b, _c;\n      if ((_a = prevTriggeringElement == null ? void 0 : prevTriggeringElement.$el) == null ? void 0 : _a.removeEventListener) {\n        prevTriggeringElement.$el.removeEventListener(\"pointerenter\", onAutofocusTriggerEnter);\n      }\n      if ((_b = triggeringElement == null ? void 0 : triggeringElement.$el) == null ? void 0 : _b.removeEventListener) {\n        triggeringElement.$el.removeEventListener(\"pointerenter\", onAutofocusTriggerEnter);\n      }\n      if (((_c = triggeringElement == null ? void 0 : triggeringElement.$el) == null ? void 0 : _c.addEventListener) && trigger2.includes(\"hover\")) {\n        triggeringElement.$el.addEventListener(\"pointerenter\", onAutofocusTriggerEnter);\n      }\n    }, {\n      immediate: true\n    });\n    onBeforeUnmount(() => {\n      var _a, _b;\n      if ((_b = (_a = triggeringElementRef.value) == null ? void 0 : _a.$el) == null ? void 0 : _b.removeEventListener) {\n        triggeringElementRef.value.$el.removeEventListener(\"pointerenter\", onAutofocusTriggerEnter);\n      }\n    });\n    function handleClick() {\n      handleClose();\n    }\n    function handleClose() {\n      var _a;\n      (_a = popperRef.value) == null ? void 0 : _a.onClose();\n    }\n    function handleOpen() {\n      var _a;\n      (_a = popperRef.value) == null ? void 0 : _a.onOpen();\n    }\n    const dropdownSize = useFormSize();\n    function commandHandler(...args) {\n      emit(\"command\", ...args);\n    }\n    function onAutofocusTriggerEnter() {\n      var _a, _b;\n      (_b = (_a = triggeringElementRef.value) == null ? void 0 : _a.$el) == null ? void 0 : _b.focus();\n    }\n    function onItemEnter() {}\n    function onItemLeave() {\n      const contentEl = unref(contentRef);\n      trigger.value.includes(\"hover\") && (contentEl == null ? void 0 : contentEl.focus());\n      currentTabId.value = null;\n    }\n    function handleCurrentTabIdChange(id) {\n      currentTabId.value = id;\n    }\n    function handleEntryFocus(e) {\n      if (!isUsingKeyboard.value) {\n        e.preventDefault();\n        e.stopImmediatePropagation();\n      }\n    }\n    function handleBeforeShowTooltip() {\n      emit(\"visible-change\", true);\n    }\n    function handleShowTooltip(event) {\n      var _a;\n      if ((event == null ? void 0 : event.type) === \"keydown\") {\n        (_a = contentRef.value) == null ? void 0 : _a.focus();\n      }\n    }\n    function handleBeforeHideTooltip() {\n      emit(\"visible-change\", false);\n    }\n    provide(DROPDOWN_INJECTION_KEY, {\n      contentRef,\n      role: computed(() => props.role),\n      triggerId,\n      isUsingKeyboard,\n      onItemEnter,\n      onItemLeave\n    });\n    provide(\"elDropdown\", {\n      instance: _instance,\n      dropdownSize,\n      handleClick,\n      commandHandler,\n      trigger: toRef(props, \"trigger\"),\n      hideOnClick: toRef(props, \"hideOnClick\")\n    });\n    const onFocusAfterTrapped = e => {\n      var _a, _b;\n      e.preventDefault();\n      (_b = (_a = contentRef.value) == null ? void 0 : _a.focus) == null ? void 0 : _b.call(_a, {\n        preventScroll: true\n      });\n    };\n    const handlerMainButtonClick = event => {\n      emit(\"click\", event);\n    };\n    return {\n      t,\n      ns,\n      scrollbar,\n      wrapStyle,\n      dropdownTriggerKls,\n      dropdownSize,\n      triggerId,\n      currentTabId,\n      handleCurrentTabIdChange,\n      handlerMainButtonClick,\n      handleEntryFocus,\n      handleClose,\n      handleOpen,\n      handleBeforeShowTooltip,\n      handleShowTooltip,\n      handleBeforeHideTooltip,\n      onFocusAfterTrapped,\n      popperRef,\n      contentRef,\n      triggeringElementRef,\n      referenceElementRef\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _a;\n  const _component_el_dropdown_collection = resolveComponent(\"el-dropdown-collection\");\n  const _component_el_roving_focus_group = resolveComponent(\"el-roving-focus-group\");\n  const _component_el_scrollbar = resolveComponent(\"el-scrollbar\");\n  const _component_el_only_child = resolveComponent(\"el-only-child\");\n  const _component_el_tooltip = resolveComponent(\"el-tooltip\");\n  const _component_el_button = resolveComponent(\"el-button\");\n  const _component_arrow_down = resolveComponent(\"arrow-down\");\n  const _component_el_icon = resolveComponent(\"el-icon\");\n  const _component_el_button_group = resolveComponent(\"el-button-group\");\n  return openBlock(), createElementBlock(\"div\", {\n    class: normalizeClass([_ctx.ns.b(), _ctx.ns.is(\"disabled\", _ctx.disabled)])\n  }, [createVNode(_component_el_tooltip, {\n    ref: \"popperRef\",\n    role: _ctx.role,\n    effect: _ctx.effect,\n    \"fallback-placements\": [\"bottom\", \"top\"],\n    \"popper-options\": _ctx.popperOptions,\n    \"gpu-acceleration\": false,\n    \"hide-after\": _ctx.trigger === \"hover\" ? _ctx.hideTimeout : 0,\n    \"manual-mode\": true,\n    placement: _ctx.placement,\n    \"popper-class\": [_ctx.ns.e(\"popper\"), _ctx.popperClass],\n    \"reference-element\": (_a = _ctx.referenceElementRef) == null ? void 0 : _a.$el,\n    trigger: _ctx.trigger,\n    \"trigger-keys\": _ctx.triggerKeys,\n    \"trigger-target-el\": _ctx.contentRef,\n    \"show-after\": _ctx.trigger === \"hover\" ? _ctx.showTimeout : 0,\n    \"stop-popper-mouse-event\": false,\n    \"virtual-ref\": _ctx.triggeringElementRef,\n    \"virtual-triggering\": _ctx.splitButton,\n    disabled: _ctx.disabled,\n    transition: `${_ctx.ns.namespace.value}-zoom-in-top`,\n    teleported: _ctx.teleported,\n    pure: \"\",\n    persistent: _ctx.persistent,\n    onBeforeShow: _ctx.handleBeforeShowTooltip,\n    onShow: _ctx.handleShowTooltip,\n    onBeforeHide: _ctx.handleBeforeHideTooltip\n  }, createSlots({\n    content: withCtx(() => [createVNode(_component_el_scrollbar, {\n      ref: \"scrollbar\",\n      \"wrap-style\": _ctx.wrapStyle,\n      tag: \"div\",\n      \"view-class\": _ctx.ns.e(\"list\")\n    }, {\n      default: withCtx(() => [createVNode(_component_el_roving_focus_group, {\n        loop: _ctx.loop,\n        \"current-tab-id\": _ctx.currentTabId,\n        orientation: \"horizontal\",\n        onCurrentTabIdChange: _ctx.handleCurrentTabIdChange,\n        onEntryFocus: _ctx.handleEntryFocus\n      }, {\n        default: withCtx(() => [createVNode(_component_el_dropdown_collection, null, {\n          default: withCtx(() => [renderSlot(_ctx.$slots, \"dropdown\")]),\n          _: 3\n        })]),\n        _: 3\n      }, 8, [\"loop\", \"current-tab-id\", \"onCurrentTabIdChange\", \"onEntryFocus\"])]),\n      _: 3\n    }, 8, [\"wrap-style\", \"view-class\"])]),\n    _: 2\n  }, [!_ctx.splitButton ? {\n    name: \"default\",\n    fn: withCtx(() => [createVNode(_component_el_only_child, {\n      id: _ctx.triggerId,\n      ref: \"triggeringElementRef\",\n      role: \"button\",\n      tabindex: _ctx.tabindex\n    }, {\n      default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n      _: 3\n    }, 8, [\"id\", \"tabindex\"])])\n  } : void 0]), 1032, [\"role\", \"effect\", \"popper-options\", \"hide-after\", \"placement\", \"popper-class\", \"reference-element\", \"trigger\", \"trigger-keys\", \"trigger-target-el\", \"show-after\", \"virtual-ref\", \"virtual-triggering\", \"disabled\", \"transition\", \"teleported\", \"persistent\", \"onBeforeShow\", \"onShow\", \"onBeforeHide\"]), _ctx.splitButton ? (openBlock(), createBlock(_component_el_button_group, {\n    key: 0\n  }, {\n    default: withCtx(() => [createVNode(_component_el_button, mergeProps({\n      ref: \"referenceElementRef\"\n    }, _ctx.buttonProps, {\n      size: _ctx.dropdownSize,\n      type: _ctx.type,\n      disabled: _ctx.disabled,\n      tabindex: _ctx.tabindex,\n      onClick: _ctx.handlerMainButtonClick\n    }), {\n      default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n      _: 3\n    }, 16, [\"size\", \"type\", \"disabled\", \"tabindex\", \"onClick\"]), createVNode(_component_el_button, mergeProps({\n      id: _ctx.triggerId,\n      ref: \"triggeringElementRef\"\n    }, _ctx.buttonProps, {\n      role: \"button\",\n      size: _ctx.dropdownSize,\n      type: _ctx.type,\n      class: _ctx.ns.e(\"caret-button\"),\n      disabled: _ctx.disabled,\n      tabindex: _ctx.tabindex,\n      \"aria-label\": _ctx.t(\"el.dropdown.toggleDropdown\")\n    }), {\n      default: withCtx(() => [createVNode(_component_el_icon, {\n        class: normalizeClass(_ctx.ns.e(\"icon\"))\n      }, {\n        default: withCtx(() => [createVNode(_component_arrow_down)]),\n        _: 1\n      }, 8, [\"class\"])]),\n      _: 1\n    }, 16, [\"id\", \"size\", \"type\", \"class\", \"disabled\", \"tabindex\", \"aria-label\"])]),\n    _: 3\n  })) : createCommentVNode(\"v-if\", true)], 2);\n}\nvar Dropdown = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"dropdown.vue\"]]);\nexport { Dropdown as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}