{"ast": null, "code": "import { ref, onUnmounted, h, Teleport } from 'vue';\nimport { createGlobalNode, removeGlobalNode } from '../../utils/vue/global-node.mjs';\nimport { isClient } from '@vueuse/core';\nimport { NOOP } from '@vue/shared';\nconst useTeleport = (contentRenderer, appendToBody) => {\n  const isTeleportVisible = ref(false);\n  if (!isClient) {\n    return {\n      isTeleportVisible,\n      showTeleport: NOOP,\n      hideTeleport: NOOP,\n      renderTeleport: NOOP\n    };\n  }\n  let $el = null;\n  const showTeleport = () => {\n    isTeleportVisible.value = true;\n    if ($el !== null) return;\n    $el = createGlobalNode();\n  };\n  const hideTeleport = () => {\n    isTeleportVisible.value = false;\n    if ($el !== null) {\n      removeGlobalNode($el);\n      $el = null;\n    }\n  };\n  const renderTeleport = () => {\n    return appendToBody.value !== true ? contentRenderer() : isTeleportVisible.value ? [h(Teleport, {\n      to: $el\n    }, contentRenderer())] : void 0;\n  };\n  onUnmounted(hideTeleport);\n  return {\n    isTeleportVisible,\n    showTeleport,\n    hideTeleport,\n    renderTeleport\n  };\n};\nexport { useTeleport };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}