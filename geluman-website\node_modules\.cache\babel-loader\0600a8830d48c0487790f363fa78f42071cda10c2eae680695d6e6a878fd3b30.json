{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { mutable } from '../../../utils/typescript.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nimport { isString, isArray } from '@vue/shared';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nconst emitChangeFn = value => isNumber(value) || isString(value) || isArray(value);\nconst collapseProps = buildProps({\n  accordion: Boolean,\n  modelValue: {\n    type: definePropType([Array, String, Number]),\n    default: () => mutable([])\n  }\n});\nconst collapseEmits = {\n  [UPDATE_MODEL_EVENT]: emitChangeFn,\n  [CHANGE_EVENT]: emitChangeFn\n};\nexport { collapseEmits, collapseProps, emitChangeFn };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}