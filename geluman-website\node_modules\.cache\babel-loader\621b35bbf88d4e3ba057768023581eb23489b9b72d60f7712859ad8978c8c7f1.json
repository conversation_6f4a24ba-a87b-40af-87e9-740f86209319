{"ast": null, "code": "import Steps from './src/steps2.mjs';\nimport Step from './src/item2.mjs';\nexport { stepProps } from './src/item.mjs';\nexport { stepsEmits, stepsProps } from './src/steps.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\nconst ElSteps = withInstall(Steps, {\n  Step\n});\nconst ElStep = withNoopInstall(Step);\nexport { ElStep, ElSteps, ElSteps as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}