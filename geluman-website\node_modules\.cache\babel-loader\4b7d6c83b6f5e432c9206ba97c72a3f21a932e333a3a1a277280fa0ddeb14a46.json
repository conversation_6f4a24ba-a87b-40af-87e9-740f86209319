{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { capitalize } from '../../../utils/strings.mjs';\nimport { isEmpty, isUndefined } from '../../../utils/types.mjs';\nimport { isFunction, isArray } from '@vue/shared';\nlet uid = 0;\nconst calculatePathNodes = node => {\n  const nodes = [node];\n  let {\n    parent\n  } = node;\n  while (parent) {\n    nodes.unshift(parent);\n    parent = parent.parent;\n  }\n  return nodes;\n};\nclass Node {\n  constructor(data, config, parent, root = false) {\n    this.data = data;\n    this.config = config;\n    this.parent = parent;\n    this.root = root;\n    this.uid = uid++;\n    this.checked = false;\n    this.indeterminate = false;\n    this.loading = false;\n    const {\n      value: valueKey,\n      label: labelKey,\n      children: childrenKey\n    } = config;\n    const childrenData = data[childrenKey];\n    const pathNodes = calculatePathNodes(this);\n    this.level = root ? 0 : parent ? parent.level + 1 : 1;\n    this.value = data[valueKey];\n    this.label = data[labelKey];\n    this.pathNodes = pathNodes;\n    this.pathValues = pathNodes.map(node => node.value);\n    this.pathLabels = pathNodes.map(node => node.label);\n    this.childrenData = childrenData;\n    this.children = (childrenData || []).map(child => new Node(child, config, this));\n    this.loaded = !config.lazy || this.isLeaf || !isEmpty(childrenData);\n  }\n  get isDisabled() {\n    const {\n      data,\n      parent,\n      config\n    } = this;\n    const {\n      disabled,\n      checkStrictly\n    } = config;\n    const isDisabled = isFunction(disabled) ? disabled(data, this) : !!data[disabled];\n    return isDisabled || !checkStrictly && (parent == null ? void 0 : parent.isDisabled);\n  }\n  get isLeaf() {\n    const {\n      data,\n      config,\n      childrenData,\n      loaded\n    } = this;\n    const {\n      lazy,\n      leaf\n    } = config;\n    const isLeaf = isFunction(leaf) ? leaf(data, this) : data[leaf];\n    return isUndefined(isLeaf) ? lazy && !loaded ? false : !(isArray(childrenData) && childrenData.length) : !!isLeaf;\n  }\n  get valueByOption() {\n    return this.config.emitPath ? this.pathValues : this.value;\n  }\n  appendChild(childData) {\n    const {\n      childrenData,\n      children\n    } = this;\n    const node = new Node(childData, this.config, this);\n    if (isArray(childrenData)) {\n      childrenData.push(childData);\n    } else {\n      this.childrenData = [childData];\n    }\n    children.push(node);\n    return node;\n  }\n  calcText(allLevels, separator) {\n    const text = allLevels ? this.pathLabels.join(separator) : this.label;\n    this.text = text;\n    return text;\n  }\n  broadcast(event, ...args) {\n    const handlerName = `onParent${capitalize(event)}`;\n    this.children.forEach(child => {\n      if (child) {\n        child.broadcast(event, ...args);\n        child[handlerName] && child[handlerName](...args);\n      }\n    });\n  }\n  emit(event, ...args) {\n    const {\n      parent\n    } = this;\n    const handlerName = `onChild${capitalize(event)}`;\n    if (parent) {\n      parent[handlerName] && parent[handlerName](...args);\n      parent.emit(event, ...args);\n    }\n  }\n  onParentCheck(checked) {\n    if (!this.isDisabled) {\n      this.setCheckState(checked);\n    }\n  }\n  onChildCheck() {\n    const {\n      children\n    } = this;\n    const validChildren = children.filter(child => !child.isDisabled);\n    const checked = validChildren.length ? validChildren.every(child => child.checked) : false;\n    this.setCheckState(checked);\n  }\n  setCheckState(checked) {\n    const totalNum = this.children.length;\n    const checkedNum = this.children.reduce((c, p) => {\n      const num = p.checked ? 1 : p.indeterminate ? 0.5 : 0;\n      return c + num;\n    }, 0);\n    this.checked = this.loaded && this.children.filter(child => !child.isDisabled).every(child => child.loaded && child.checked) && checked;\n    this.indeterminate = this.loaded && checkedNum !== totalNum && checkedNum > 0;\n  }\n  doCheck(checked) {\n    if (this.checked === checked) return;\n    const {\n      checkStrictly,\n      multiple\n    } = this.config;\n    if (checkStrictly || !multiple) {\n      this.checked = checked;\n    } else {\n      this.broadcast(\"check\", checked);\n      this.setCheckState(checked);\n      this.emit(\"check\");\n    }\n  }\n}\nexport { Node as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}