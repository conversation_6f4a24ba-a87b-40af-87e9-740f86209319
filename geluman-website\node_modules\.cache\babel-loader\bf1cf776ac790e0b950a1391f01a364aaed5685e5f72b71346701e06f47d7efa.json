{"ast": null, "code": "import createCaseFirst from './_createCaseFirst.js';\n\n/**\n * Converts the first character of `string` to lower case.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the converted string.\n * @example\n *\n * _.lowerFirst('Fred');\n * // => 'fred'\n *\n * _.lowerFirst('FRED');\n * // => 'fRED'\n */\nvar lowerFirst = createCaseFirst('toLowerCase');\nexport default lowerFirst;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}