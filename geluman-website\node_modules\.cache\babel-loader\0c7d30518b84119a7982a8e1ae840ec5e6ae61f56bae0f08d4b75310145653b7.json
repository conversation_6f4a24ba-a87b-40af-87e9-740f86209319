{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst COMPONENT_NAME = \"ElOption\";\nconst optionProps = buildProps({\n  value: {\n    type: [String, Number, Boolean, Object],\n    required: true\n  },\n  label: {\n    type: [String, Number]\n  },\n  created: Boolean,\n  disabled: Boolean\n});\nexport { COMPONENT_NAME, optionProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}