{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { isArray } from '@vue/shared';\nconst uploadDraggerProps = buildProps({\n  disabled: {\n    type: Boolean,\n    default: false\n  }\n});\nconst uploadDraggerEmits = {\n  file: file => isArray(file)\n};\nexport { uploadDraggerEmits, uploadDraggerProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}