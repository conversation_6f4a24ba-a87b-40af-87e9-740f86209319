{"ast": null, "code": "import { placements } from '@popperjs/core';\nimport { CircleClose, ArrowDown } from '@element-plus/icons-vue';\nimport { defaultProps } from './useProps.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nimport { useTooltipContentProps } from '../../tooltip/src/content.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { tagProps } from '../../tag/src/tag.mjs';\nimport { isBoolean, isNumber } from '../../../utils/types.mjs';\nimport { useEmptyValuesProps } from '../../../hooks/use-empty-values/index.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nconst SelectProps = buildProps({\n  allowCreate: Boolean,\n  autocomplete: {\n    type: definePropType(String),\n    default: \"none\"\n  },\n  automaticDropdown: Boolean,\n  clearable: Boolean,\n  clearIcon: {\n    type: iconPropType,\n    default: CircleClose\n  },\n  effect: {\n    type: definePropType(String),\n    default: \"light\"\n  },\n  collapseTags: Boolean,\n  collapseTagsTooltip: Boolean,\n  maxCollapseTags: {\n    type: Number,\n    default: 1\n  },\n  defaultFirstOption: Boolean,\n  disabled: Boolean,\n  estimatedOptionHeight: {\n    type: Number,\n    default: void 0\n  },\n  filterable: Boolean,\n  filterMethod: Function,\n  height: {\n    type: Number,\n    default: 274\n  },\n  itemHeight: {\n    type: Number,\n    default: 34\n  },\n  id: String,\n  loading: Boolean,\n  loadingText: String,\n  modelValue: {\n    type: definePropType([Array, String, Number, Boolean, Object])\n  },\n  multiple: Boolean,\n  multipleLimit: {\n    type: Number,\n    default: 0\n  },\n  name: String,\n  noDataText: String,\n  noMatchText: String,\n  remoteMethod: Function,\n  reserveKeyword: {\n    type: Boolean,\n    default: true\n  },\n  options: {\n    type: definePropType(Array),\n    required: true\n  },\n  placeholder: {\n    type: String\n  },\n  teleported: useTooltipContentProps.teleported,\n  persistent: {\n    type: Boolean,\n    default: true\n  },\n  popperClass: {\n    type: String,\n    default: \"\"\n  },\n  popperOptions: {\n    type: definePropType(Object),\n    default: () => ({})\n  },\n  remote: Boolean,\n  size: useSizeProp,\n  props: {\n    type: definePropType(Object),\n    default: () => defaultProps\n  },\n  valueKey: {\n    type: String,\n    default: \"value\"\n  },\n  scrollbarAlwaysOn: Boolean,\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  offset: {\n    type: Number,\n    default: 12\n  },\n  showArrow: {\n    type: Boolean,\n    default: true\n  },\n  placement: {\n    type: definePropType(String),\n    values: placements,\n    default: \"bottom-start\"\n  },\n  fallbackPlacements: {\n    type: definePropType(Array),\n    default: [\"bottom-start\", \"top-start\", \"right\", \"left\"]\n  },\n  tagType: {\n    ...tagProps.type,\n    default: \"info\"\n  },\n  tagEffect: {\n    ...tagProps.effect,\n    default: \"light\"\n  },\n  tabindex: {\n    type: [String, Number],\n    default: 0\n  },\n  appendTo: useTooltipContentProps.appendTo,\n  fitInputWidth: {\n    type: [Boolean, Number],\n    default: true,\n    validator(val) {\n      return isBoolean(val) || isNumber(val);\n    }\n  },\n  suffixIcon: {\n    type: iconPropType,\n    default: ArrowDown\n  },\n  ...useEmptyValuesProps,\n  ...useAriaProps([\"ariaLabel\"])\n});\nconst OptionProps = buildProps({\n  data: Array,\n  disabled: Boolean,\n  hovering: Boolean,\n  item: {\n    type: definePropType(Object),\n    required: true\n  },\n  index: Number,\n  style: Object,\n  selected: Boolean,\n  created: Boolean\n});\nconst selectEmits = {\n  [UPDATE_MODEL_EVENT]: val => true,\n  [CHANGE_EVENT]: val => true,\n  \"remove-tag\": val => true,\n  \"visible-change\": visible => true,\n  focus: evt => evt instanceof FocusEvent,\n  blur: evt => evt instanceof FocusEvent,\n  clear: () => true\n};\nconst optionEmits = {\n  hover: index => isNumber(index),\n  select: (val, index) => true\n};\nexport { OptionProps, SelectProps, optionEmits, selectEmits };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}