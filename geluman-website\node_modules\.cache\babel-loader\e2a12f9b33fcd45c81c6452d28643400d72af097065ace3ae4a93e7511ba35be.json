{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { nextTick } from 'vue';\nimport { throttle } from 'lodash-unified';\nimport { isFunction } from '@vue/shared';\nimport { throwError } from '../../../utils/error.mjs';\nimport { getScrollContainer } from '../../../utils/dom/scroll.mjs';\nimport { getOffsetTopDistance } from '../../../utils/dom/position.mjs';\nconst SCOPE = \"ElInfiniteScroll\";\nconst CHECK_INTERVAL = 50;\nconst DEFAULT_DELAY = 200;\nconst DEFAULT_DISTANCE = 0;\nconst attributes = {\n  delay: {\n    type: Number,\n    default: DEFAULT_DELAY\n  },\n  distance: {\n    type: Number,\n    default: DEFAULT_DISTANCE\n  },\n  disabled: {\n    type: Boolean,\n    default: false\n  },\n  immediate: {\n    type: Boolean,\n    default: true\n  }\n};\nconst getScrollOptions = (el, instance) => {\n  return Object.entries(attributes).reduce((acm, [name, option]) => {\n    var _a, _b;\n    const {\n      type,\n      default: defaultValue\n    } = option;\n    const attrVal = el.getAttribute(`infinite-scroll-${name}`);\n    let value = (_b = (_a = instance[attrVal]) != null ? _a : attrVal) != null ? _b : defaultValue;\n    value = value === \"false\" ? false : value;\n    value = type(value);\n    acm[name] = Number.isNaN(value) ? defaultValue : value;\n    return acm;\n  }, {});\n};\nconst destroyObserver = el => {\n  const {\n    observer\n  } = el[SCOPE];\n  if (observer) {\n    observer.disconnect();\n    delete el[SCOPE].observer;\n  }\n};\nconst handleScroll = (el, cb) => {\n  const {\n    container,\n    containerEl,\n    instance,\n    observer,\n    lastScrollTop\n  } = el[SCOPE];\n  const {\n    disabled,\n    distance\n  } = getScrollOptions(el, instance);\n  const {\n    clientHeight,\n    scrollHeight,\n    scrollTop\n  } = containerEl;\n  const delta = scrollTop - lastScrollTop;\n  el[SCOPE].lastScrollTop = scrollTop;\n  if (observer || disabled || delta < 0) return;\n  let shouldTrigger = false;\n  if (container === el) {\n    shouldTrigger = scrollHeight - (clientHeight + scrollTop) <= distance;\n  } else {\n    const {\n      clientTop,\n      scrollHeight: height\n    } = el;\n    const offsetTop = getOffsetTopDistance(el, containerEl);\n    shouldTrigger = scrollTop + clientHeight >= offsetTop + clientTop + height - distance;\n  }\n  if (shouldTrigger) {\n    cb.call(instance);\n  }\n};\nfunction checkFull(el, cb) {\n  const {\n    containerEl,\n    instance\n  } = el[SCOPE];\n  const {\n    disabled\n  } = getScrollOptions(el, instance);\n  if (disabled || containerEl.clientHeight === 0) return;\n  if (containerEl.scrollHeight <= containerEl.clientHeight) {\n    cb.call(instance);\n  } else {\n    destroyObserver(el);\n  }\n}\nconst InfiniteScroll = {\n  async mounted(el, binding) {\n    const {\n      instance,\n      value: cb\n    } = binding;\n    if (!isFunction(cb)) {\n      throwError(SCOPE, \"'v-infinite-scroll' binding value must be a function\");\n    }\n    await nextTick();\n    const {\n      delay,\n      immediate\n    } = getScrollOptions(el, instance);\n    const container = getScrollContainer(el, true);\n    const containerEl = container === window ? document.documentElement : container;\n    const onScroll = throttle(handleScroll.bind(null, el, cb), delay);\n    if (!container) return;\n    el[SCOPE] = {\n      instance,\n      container,\n      containerEl,\n      delay,\n      cb,\n      onScroll,\n      lastScrollTop: containerEl.scrollTop\n    };\n    if (immediate) {\n      const observer = new MutationObserver(throttle(checkFull.bind(null, el, cb), CHECK_INTERVAL));\n      el[SCOPE].observer = observer;\n      observer.observe(el, {\n        childList: true,\n        subtree: true\n      });\n      checkFull(el, cb);\n    }\n    container.addEventListener(\"scroll\", onScroll);\n  },\n  unmounted(el) {\n    if (!el[SCOPE]) return;\n    const {\n      container,\n      onScroll\n    } = el[SCOPE];\n    container == null ? void 0 : container.removeEventListener(\"scroll\", onScroll);\n    destroyObserver(el);\n  },\n  async updated(el) {\n    if (!el[SCOPE]) {\n      await nextTick();\n    } else {\n      const {\n        containerEl,\n        cb,\n        observer\n      } = el[SCOPE];\n      if (containerEl.clientHeight && observer) {\n        checkFull(el, cb);\n      }\n    }\n  }\n};\nexport { CHECK_INTERVAL, DEFAULT_DELAY, DEFAULT_DISTANCE, SCOPE, InfiniteScroll as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}