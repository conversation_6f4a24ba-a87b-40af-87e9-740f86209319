{"ast": null, "code": "import { inject, ref, computed, unref, watch, onMounted } from 'vue';\nimport { isUndefined } from 'lodash-unified';\nimport { POPPER_INJECTION_KEY } from '../constants.mjs';\nimport { buildPopperOptions, unwrapMeasurableEl } from '../utils.mjs';\nimport { usePopper } from '../../../../hooks/use-popper/index.mjs';\nconst DEFAULT_ARROW_OFFSET = 0;\nconst usePopperContent = props => {\n  const {\n    popperInstanceRef,\n    contentRef,\n    triggerRef,\n    role\n  } = inject(POPPER_INJECTION_KEY, void 0);\n  const arrowRef = ref();\n  const arrowOffset = ref();\n  const eventListenerModifier = computed(() => {\n    return {\n      name: \"eventListeners\",\n      enabled: !!props.visible\n    };\n  });\n  const arrowModifier = computed(() => {\n    var _a;\n    const arrowEl = unref(arrowRef);\n    const offset = (_a = unref(arrowOffset)) != null ? _a : DEFAULT_ARROW_OFFSET;\n    return {\n      name: \"arrow\",\n      enabled: !isUndefined(arrowEl),\n      options: {\n        element: arrowEl,\n        padding: offset\n      }\n    };\n  });\n  const options = computed(() => {\n    return {\n      onFirstUpdate: () => {\n        update();\n      },\n      ...buildPopperOptions(props, [unref(arrowModifier), unref(eventListenerModifier)])\n    };\n  });\n  const computedReference = computed(() => unwrapMeasurableEl(props.referenceEl) || unref(triggerRef));\n  const {\n    attributes,\n    state,\n    styles,\n    update,\n    forceUpdate,\n    instanceRef\n  } = usePopper(computedReference, contentRef, options);\n  watch(instanceRef, instance => popperInstanceRef.value = instance, {\n    flush: \"sync\"\n  });\n  onMounted(() => {\n    watch(() => {\n      var _a;\n      return (_a = unref(computedReference)) == null ? void 0 : _a.getBoundingClientRect();\n    }, () => {\n      update();\n    });\n  });\n  return {\n    attributes,\n    arrowRef,\n    contentRef,\n    instanceRef,\n    state,\n    styles,\n    role,\n    forceUpdate,\n    update\n  };\n};\nexport { usePopperContent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}