{"ast": null, "code": "import { inject, ref, computed, nextTick, watch } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport { useEventListener } from '@vueuse/core';\nimport { sliderContextKey } from '../constants.mjs';\nimport { EVENT_CODE } from '../../../../constants/aria.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../../constants/event.mjs';\nconst useTooltip = (props, formatTooltip, showTooltip) => {\n  const tooltip = ref();\n  const tooltipVisible = ref(false);\n  const enableFormat = computed(() => {\n    return formatTooltip.value instanceof Function;\n  });\n  const formatValue = computed(() => {\n    return enableFormat.value && formatTooltip.value(props.modelValue) || props.modelValue;\n  });\n  const displayTooltip = debounce(() => {\n    showTooltip.value && (tooltipVisible.value = true);\n  }, 50);\n  const hideTooltip = debounce(() => {\n    showTooltip.value && (tooltipVisible.value = false);\n  }, 50);\n  return {\n    tooltip,\n    tooltipVisible,\n    formatValue,\n    displayTooltip,\n    hideTooltip\n  };\n};\nconst useSliderButton = (props, initData, emit) => {\n  const {\n    disabled,\n    min,\n    max,\n    step,\n    showTooltip,\n    persistent,\n    precision,\n    sliderSize,\n    formatTooltip,\n    emitChange,\n    resetSize,\n    updateDragging\n  } = inject(sliderContextKey);\n  const {\n    tooltip,\n    tooltipVisible,\n    formatValue,\n    displayTooltip,\n    hideTooltip\n  } = useTooltip(props, formatTooltip, showTooltip);\n  const button = ref();\n  const currentPosition = computed(() => {\n    return `${(props.modelValue - min.value) / (max.value - min.value) * 100}%`;\n  });\n  const wrapperStyle = computed(() => {\n    return props.vertical ? {\n      bottom: currentPosition.value\n    } : {\n      left: currentPosition.value\n    };\n  });\n  const handleMouseEnter = () => {\n    initData.hovering = true;\n    displayTooltip();\n  };\n  const handleMouseLeave = () => {\n    initData.hovering = false;\n    if (!initData.dragging) {\n      hideTooltip();\n    }\n  };\n  const onButtonDown = event => {\n    if (disabled.value) return;\n    event.preventDefault();\n    onDragStart(event);\n    window.addEventListener(\"mousemove\", onDragging);\n    window.addEventListener(\"touchmove\", onDragging);\n    window.addEventListener(\"mouseup\", onDragEnd);\n    window.addEventListener(\"touchend\", onDragEnd);\n    window.addEventListener(\"contextmenu\", onDragEnd);\n    button.value.focus();\n  };\n  const incrementPosition = amount => {\n    if (disabled.value) return;\n    initData.newPosition = Number.parseFloat(currentPosition.value) + amount / (max.value - min.value) * 100;\n    setPosition(initData.newPosition);\n    emitChange();\n  };\n  const onLeftKeyDown = () => {\n    incrementPosition(-step.value);\n  };\n  const onRightKeyDown = () => {\n    incrementPosition(step.value);\n  };\n  const onPageDownKeyDown = () => {\n    incrementPosition(-step.value * 4);\n  };\n  const onPageUpKeyDown = () => {\n    incrementPosition(step.value * 4);\n  };\n  const onHomeKeyDown = () => {\n    if (disabled.value) return;\n    setPosition(0);\n    emitChange();\n  };\n  const onEndKeyDown = () => {\n    if (disabled.value) return;\n    setPosition(100);\n    emitChange();\n  };\n  const onKeyDown = event => {\n    let isPreventDefault = true;\n    switch (event.code) {\n      case EVENT_CODE.left:\n      case EVENT_CODE.down:\n        onLeftKeyDown();\n        break;\n      case EVENT_CODE.right:\n      case EVENT_CODE.up:\n        onRightKeyDown();\n        break;\n      case EVENT_CODE.home:\n        onHomeKeyDown();\n        break;\n      case EVENT_CODE.end:\n        onEndKeyDown();\n        break;\n      case EVENT_CODE.pageDown:\n        onPageDownKeyDown();\n        break;\n      case EVENT_CODE.pageUp:\n        onPageUpKeyDown();\n        break;\n      default:\n        isPreventDefault = false;\n        break;\n    }\n    isPreventDefault && event.preventDefault();\n  };\n  const getClientXY = event => {\n    let clientX;\n    let clientY;\n    if (event.type.startsWith(\"touch\")) {\n      clientY = event.touches[0].clientY;\n      clientX = event.touches[0].clientX;\n    } else {\n      clientY = event.clientY;\n      clientX = event.clientX;\n    }\n    return {\n      clientX,\n      clientY\n    };\n  };\n  const onDragStart = event => {\n    initData.dragging = true;\n    initData.isClick = true;\n    const {\n      clientX,\n      clientY\n    } = getClientXY(event);\n    if (props.vertical) {\n      initData.startY = clientY;\n    } else {\n      initData.startX = clientX;\n    }\n    initData.startPosition = Number.parseFloat(currentPosition.value);\n    initData.newPosition = initData.startPosition;\n  };\n  const onDragging = event => {\n    if (initData.dragging) {\n      initData.isClick = false;\n      displayTooltip();\n      resetSize();\n      let diff;\n      const {\n        clientX,\n        clientY\n      } = getClientXY(event);\n      if (props.vertical) {\n        initData.currentY = clientY;\n        diff = (initData.startY - initData.currentY) / sliderSize.value * 100;\n      } else {\n        initData.currentX = clientX;\n        diff = (initData.currentX - initData.startX) / sliderSize.value * 100;\n      }\n      initData.newPosition = initData.startPosition + diff;\n      setPosition(initData.newPosition);\n    }\n  };\n  const onDragEnd = () => {\n    if (initData.dragging) {\n      setTimeout(() => {\n        initData.dragging = false;\n        if (!initData.hovering) {\n          hideTooltip();\n        }\n        if (!initData.isClick) {\n          setPosition(initData.newPosition);\n        }\n        emitChange();\n      }, 0);\n      window.removeEventListener(\"mousemove\", onDragging);\n      window.removeEventListener(\"touchmove\", onDragging);\n      window.removeEventListener(\"mouseup\", onDragEnd);\n      window.removeEventListener(\"touchend\", onDragEnd);\n      window.removeEventListener(\"contextmenu\", onDragEnd);\n    }\n  };\n  const setPosition = async newPosition => {\n    if (newPosition === null || Number.isNaN(+newPosition)) return;\n    if (newPosition < 0) {\n      newPosition = 0;\n    } else if (newPosition > 100) {\n      newPosition = 100;\n    }\n    const lengthPerStep = 100 / ((max.value - min.value) / step.value);\n    const steps = Math.round(newPosition / lengthPerStep);\n    let value = steps * lengthPerStep * (max.value - min.value) * 0.01 + min.value;\n    value = Number.parseFloat(value.toFixed(precision.value));\n    if (value !== props.modelValue) {\n      emit(UPDATE_MODEL_EVENT, value);\n    }\n    if (!initData.dragging && props.modelValue !== initData.oldValue) {\n      initData.oldValue = props.modelValue;\n    }\n    await nextTick();\n    initData.dragging && displayTooltip();\n    tooltip.value.updatePopper();\n  };\n  watch(() => initData.dragging, val => {\n    updateDragging(val);\n  });\n  useEventListener(button, \"touchstart\", onButtonDown, {\n    passive: false\n  });\n  return {\n    disabled,\n    button,\n    tooltip,\n    tooltipVisible,\n    showTooltip,\n    persistent,\n    wrapperStyle,\n    formatValue,\n    handleMouseEnter,\n    handleMouseLeave,\n    onButtonDown,\n    onKeyDown,\n    setPosition\n  };\n};\nexport { useSliderButton };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}