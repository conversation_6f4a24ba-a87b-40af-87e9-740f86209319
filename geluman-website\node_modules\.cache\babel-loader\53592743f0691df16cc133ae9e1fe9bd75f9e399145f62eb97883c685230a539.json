{"ast": null, "code": "import { Loading } from './src/service.mjs';\nexport { Loading as ElLoadingService } from './src/service.mjs';\nimport { vLoading } from './src/directive.mjs';\nexport { vLoading as ElLoadingDirective, vLoading } from './src/directive.mjs';\nconst ElLoading = {\n  install(app) {\n    app.directive(\"loading\", vLoading);\n    app.config.globalProperties.$loading = Loading;\n  },\n  directive: vLoading,\n  service: Loading\n};\nexport { ElLoading, ElLoading as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}