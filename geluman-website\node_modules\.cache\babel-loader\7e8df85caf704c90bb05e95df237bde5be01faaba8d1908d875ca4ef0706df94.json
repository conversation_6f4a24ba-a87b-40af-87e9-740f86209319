{"ast": null, "code": "import normalizeWheel from 'normalize-wheel-es';\nconst mousewheel = function (element, callback) {\n  if (element && element.addEventListener) {\n    const fn = function (event) {\n      const normalized = normalizeWheel(event);\n      callback && Reflect.apply(callback, this, [event, normalized]);\n    };\n    element.addEventListener(\"wheel\", fn, {\n      passive: true\n    });\n  }\n};\nconst Mousewheel = {\n  beforeMount(el, binding) {\n    mousewheel(el, binding.value);\n  }\n};\nexport { Mousewheel as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}