{"ast": null, "code": "import createPadding from './_createPadding.js';\nimport stringSize from './_stringSize.js';\nimport toInteger from './toInteger.js';\nimport toString from './toString.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil,\n  nativeFloor = Math.floor;\n\n/**\n * Pads `string` on the left and right sides if it's shorter than `length`.\n * Padding characters are truncated if they can't be evenly divided by `length`.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to pad.\n * @param {number} [length=0] The padding length.\n * @param {string} [chars=' '] The string used as padding.\n * @returns {string} Returns the padded string.\n * @example\n *\n * _.pad('abc', 8);\n * // => '  abc   '\n *\n * _.pad('abc', 8, '_-');\n * // => '_-abc_-_'\n *\n * _.pad('abc', 3);\n * // => 'abc'\n */\nfunction pad(string, length, chars) {\n  string = toString(string);\n  length = toInteger(length);\n  var strLength = length ? stringSize(string) : 0;\n  if (!length || strLength >= length) {\n    return string;\n  }\n  var mid = (length - strLength) / 2;\n  return createPadding(nativeFloor(mid), chars) + string + createPadding(nativeCeil(mid), chars);\n}\nexport default pad;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}