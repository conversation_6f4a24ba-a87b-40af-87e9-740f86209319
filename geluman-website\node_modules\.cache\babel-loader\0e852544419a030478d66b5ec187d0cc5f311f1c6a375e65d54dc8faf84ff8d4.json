{"ast": null, "code": "const NODE_KEY = \"$treeNodeId\";\nconst markNodeData = function (node, data) {\n  if (!data || data[NODE_KEY]) return;\n  Object.defineProperty(data, NODE_KEY, {\n    value: node.id,\n    enumerable: false,\n    configurable: false,\n    writable: false\n  });\n};\nconst getNodeKey = (key, data) => data == null ? void 0 : data[key || NODE_KEY];\nconst handleCurrentChange = (store, emit, setCurrent) => {\n  const preCurrentNode = store.value.currentNode;\n  setCurrent();\n  const currentNode = store.value.currentNode;\n  if (preCurrentNode === currentNode) return;\n  emit(\"current-change\", currentNode ? currentNode.data : null, currentNode);\n};\nexport { NODE_KEY, getNodeKey, handleCurrentChange, markNodeData };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}