{"ast": null, "code": "/**\n * The inverse of `_.toPairs`; this method returns an object composed\n * from key-value `pairs`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} pairs The key-value pairs.\n * @returns {Object} Returns the new object.\n * @example\n *\n * _.fromPairs([['a', 1], ['b', 2]]);\n * // => { 'a': 1, 'b': 2 }\n */\nfunction fromPairs(pairs) {\n  var index = -1,\n    length = pairs == null ? 0 : pairs.length,\n    result = {};\n  while (++index < length) {\n    var pair = pairs[index];\n    result[pair[0]] = pair[1];\n  }\n  return result;\n}\nexport default fromPairs;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}