{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, watch } from 'vue';\nimport { makeList } from '../utils.mjs';\nconst makeAvailableArr = disabledList => {\n  const trueOrNumber = (isDisabled, index) => isDisabled || index;\n  const getNumber = predicate => predicate !== true;\n  return disabledList.map(trueOrNumber).filter(getNumber);\n};\nconst getTimeLists = (disabledHours, disabledMinutes, disabledSeconds) => {\n  const getHoursList = (role, compare) => {\n    return makeList(24, disabledHours && (() => disabledHours == null ? void 0 : disabledHours(role, compare)));\n  };\n  const getMinutesList = (hour, role, compare) => {\n    return makeList(60, disabledMinutes && (() => disabledMinutes == null ? void 0 : disabledMinutes(hour, role, compare)));\n  };\n  const getSecondsList = (hour, minute, role, compare) => {\n    return makeList(60, disabledSeconds && (() => disabledSeconds == null ? void 0 : disabledSeconds(hour, minute, role, compare)));\n  };\n  return {\n    getHoursList,\n    getMinutesList,\n    getSecondsList\n  };\n};\nconst buildAvailableTimeSlotGetter = (disabledHours, disabledMinutes, disabledSeconds) => {\n  const {\n    getHoursList,\n    getMinutesList,\n    getSecondsList\n  } = getTimeLists(disabledHours, disabledMinutes, disabledSeconds);\n  const getAvailableHours = (role, compare) => {\n    return makeAvailableArr(getHoursList(role, compare));\n  };\n  const getAvailableMinutes = (hour, role, compare) => {\n    return makeAvailableArr(getMinutesList(hour, role, compare));\n  };\n  const getAvailableSeconds = (hour, minute, role, compare) => {\n    return makeAvailableArr(getSecondsList(hour, minute, role, compare));\n  };\n  return {\n    getAvailableHours,\n    getAvailableMinutes,\n    getAvailableSeconds\n  };\n};\nconst useOldValue = props => {\n  const oldValue = ref(props.parsedValue);\n  watch(() => props.visible, val => {\n    if (!val) {\n      oldValue.value = props.parsedValue;\n    }\n  });\n  return oldValue;\n};\nexport { buildAvailableTimeSlotGetter, getTimeLists, useOldValue };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}