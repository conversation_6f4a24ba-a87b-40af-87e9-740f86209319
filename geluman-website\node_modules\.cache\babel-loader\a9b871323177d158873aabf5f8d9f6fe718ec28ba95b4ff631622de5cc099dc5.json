{"ast": null, "code": "import { defineComponent, inject, watch, onBeforeUnmount, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle } from 'vue';\nimport { POPPER_CONTENT_INJECTION_KEY } from './constants.mjs';\nimport { popperArrowProps } from './arrow.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElPopperArrow\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: popperArrowProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"popper\");\n    const {\n      arrowOffset,\n      arrowRef,\n      arrowStyle\n    } = inject(POPPER_CONTENT_INJECTION_KEY, void 0);\n    watch(() => props.arrowOffset, val => {\n      arrowOffset.value = val;\n    });\n    onBeforeUnmount(() => {\n      arrowRef.value = void 0;\n    });\n    expose({\n      arrowRef\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"span\", {\n        ref_key: \"arrowRef\",\n        ref: arrowRef,\n        class: normalizeClass(unref(ns).e(\"arrow\")),\n        style: normalizeStyle(unref(arrowStyle)),\n        \"data-popper-arrow\": \"\"\n      }, null, 6);\n    };\n  }\n});\nvar ElPopperArrow = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"arrow.vue\"]]);\nexport { ElPopperArrow as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}