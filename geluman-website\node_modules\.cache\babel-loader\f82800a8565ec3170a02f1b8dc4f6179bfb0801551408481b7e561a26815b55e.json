{"ast": null, "code": "import { computed } from 'vue';\nimport { get } from 'lodash-unified';\nconst defaultProps = {\n  label: \"label\",\n  value: \"value\",\n  disabled: \"disabled\",\n  options: \"options\"\n};\nfunction useProps(props) {\n  const aliasProps = computed(() => ({\n    ...defaultProps,\n    ...props.props\n  }));\n  const getLabel = option => get(option, aliasProps.value.label);\n  const getValue = option => get(option, aliasProps.value.value);\n  const getDisabled = option => get(option, aliasProps.value.disabled);\n  const getOptions = option => get(option, aliasProps.value.options);\n  return {\n    aliasProps,\n    getLabel,\n    getValue,\n    getDisabled,\n    getOptions\n  };\n}\nexport { defaultProps, useProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}