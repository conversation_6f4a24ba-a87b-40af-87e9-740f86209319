{"ast": null, "code": "import { shallowReactive } from 'vue';\nconst instances = shallowReactive([]);\nconst getInstance = id => {\n  const idx = instances.findIndex(instance => instance.id === id);\n  const current = instances[idx];\n  let prev;\n  if (idx > 0) {\n    prev = instances[idx - 1];\n  }\n  return {\n    current,\n    prev\n  };\n};\nconst getLastOffset = id => {\n  const {\n    prev\n  } = getInstance(id);\n  if (!prev) return 0;\n  return prev.vm.exposed.bottom.value;\n};\nconst getOffsetOrSpace = (id, offset) => {\n  const idx = instances.findIndex(instance => instance.id === id);\n  return idx > 0 ? 16 : offset;\n};\nexport { getInstance, getLastOffset, getOffsetOrSpace, instances };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}