{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { defineComponent, useAttrs, computed, ref, watch, onMounted, openBlock, createElementBlock, mergeProps, unref, renderSlot, createElementVNode, normalizeClass, toDisplayString, Fragment, createCommentVNode, createBlock, withCtx, normalizeProps, guardReactiveProps, nextTick } from 'vue';\nimport { isClient, useThrottleFn, useEventListener } from '@vueuse/core';\nimport { fromPairs } from 'lodash-unified';\nimport { ElImageViewer } from '../../image-viewer/index.mjs';\nimport { imageProps, imageEmits } from './image.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { isInContainer } from '../../../utils/dom/position.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useAttrs as useAttrs$1 } from '../../../hooks/use-attrs/index.mjs';\nimport { isArray, isString } from '@vue/shared';\nimport { isElement } from '../../../utils/types.mjs';\nimport { getScrollContainer } from '../../../utils/dom/scroll.mjs';\nconst __default__ = defineComponent({\n  name: \"ElImage\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: imageProps,\n  emits: imageEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"image\");\n    const rawAttrs = useAttrs();\n    const containerAttrs = computed(() => {\n      return fromPairs(Object.entries(rawAttrs).filter(([key]) => /^(data-|on[A-Z])/i.test(key) || [\"id\", \"style\"].includes(key)));\n    });\n    const imgAttrs = useAttrs$1({\n      excludeListeners: true,\n      excludeKeys: computed(() => {\n        return Object.keys(containerAttrs.value);\n      })\n    });\n    const imageSrc = ref();\n    const hasLoadError = ref(false);\n    const isLoading = ref(true);\n    const showViewer = ref(false);\n    const container = ref();\n    const _scrollContainer = ref();\n    const supportLoading = isClient && \"loading\" in HTMLImageElement.prototype;\n    let stopScrollListener;\n    const imageKls = computed(() => [ns.e(\"inner\"), preview.value && ns.e(\"preview\"), isLoading.value && ns.is(\"loading\")]);\n    const imageStyle = computed(() => {\n      const {\n        fit\n      } = props;\n      if (isClient && fit) {\n        return {\n          objectFit: fit\n        };\n      }\n      return {};\n    });\n    const preview = computed(() => {\n      const {\n        previewSrcList\n      } = props;\n      return isArray(previewSrcList) && previewSrcList.length > 0;\n    });\n    const imageIndex = computed(() => {\n      const {\n        previewSrcList,\n        initialIndex\n      } = props;\n      let previewIndex = initialIndex;\n      if (initialIndex > previewSrcList.length - 1) {\n        previewIndex = 0;\n      }\n      return previewIndex;\n    });\n    const isManual = computed(() => {\n      if (props.loading === \"eager\") return false;\n      return !supportLoading && props.loading === \"lazy\" || props.lazy;\n    });\n    const loadImage = () => {\n      if (!isClient) return;\n      isLoading.value = true;\n      hasLoadError.value = false;\n      imageSrc.value = props.src;\n    };\n    function handleLoad(event) {\n      isLoading.value = false;\n      hasLoadError.value = false;\n      emit(\"load\", event);\n    }\n    function handleError(event) {\n      isLoading.value = false;\n      hasLoadError.value = true;\n      emit(\"error\", event);\n    }\n    function handleLazyLoad() {\n      if (isInContainer(container.value, _scrollContainer.value)) {\n        loadImage();\n        removeLazyLoadListener();\n      }\n    }\n    const lazyLoadHandler = useThrottleFn(handleLazyLoad, 200, true);\n    async function addLazyLoadListener() {\n      var _a;\n      if (!isClient) return;\n      await nextTick();\n      const {\n        scrollContainer\n      } = props;\n      if (isElement(scrollContainer)) {\n        _scrollContainer.value = scrollContainer;\n      } else if (isString(scrollContainer) && scrollContainer !== \"\") {\n        _scrollContainer.value = (_a = document.querySelector(scrollContainer)) != null ? _a : void 0;\n      } else if (container.value) {\n        _scrollContainer.value = getScrollContainer(container.value);\n      }\n      if (_scrollContainer.value) {\n        stopScrollListener = useEventListener(_scrollContainer, \"scroll\", lazyLoadHandler);\n        setTimeout(() => handleLazyLoad(), 100);\n      }\n    }\n    function removeLazyLoadListener() {\n      if (!isClient || !_scrollContainer.value || !lazyLoadHandler) return;\n      stopScrollListener == null ? void 0 : stopScrollListener();\n      _scrollContainer.value = void 0;\n    }\n    function clickHandler() {\n      if (!preview.value) return;\n      showViewer.value = true;\n      emit(\"show\");\n    }\n    function closeViewer() {\n      showViewer.value = false;\n      emit(\"close\");\n    }\n    function switchViewer(val) {\n      emit(\"switch\", val);\n    }\n    watch(() => props.src, () => {\n      if (isManual.value) {\n        isLoading.value = true;\n        hasLoadError.value = false;\n        removeLazyLoadListener();\n        addLazyLoadListener();\n      } else {\n        loadImage();\n      }\n    });\n    onMounted(() => {\n      if (isManual.value) {\n        addLazyLoadListener();\n      } else {\n        loadImage();\n      }\n    });\n    expose({\n      showPreview: clickHandler\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", mergeProps({\n        ref_key: \"container\",\n        ref: container\n      }, unref(containerAttrs), {\n        class: [unref(ns).b(), _ctx.$attrs.class]\n      }), [hasLoadError.value ? renderSlot(_ctx.$slots, \"error\", {\n        key: 0\n      }, () => [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"error\"))\n      }, toDisplayString(unref(t)(\"el.image.error\")), 3)]) : (openBlock(), createElementBlock(Fragment, {\n        key: 1\n      }, [imageSrc.value !== void 0 ? (openBlock(), createElementBlock(\"img\", mergeProps({\n        key: 0\n      }, unref(imgAttrs), {\n        src: imageSrc.value,\n        loading: _ctx.loading,\n        style: unref(imageStyle),\n        class: unref(imageKls),\n        crossorigin: _ctx.crossorigin,\n        onClick: clickHandler,\n        onLoad: handleLoad,\n        onError: handleError\n      }), null, 16, [\"src\", \"loading\", \"crossorigin\"])) : createCommentVNode(\"v-if\", true), isLoading.value ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(unref(ns).e(\"wrapper\"))\n      }, [renderSlot(_ctx.$slots, \"placeholder\", {}, () => [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"placeholder\"))\n      }, null, 2)])], 2)) : createCommentVNode(\"v-if\", true)], 64)), unref(preview) ? (openBlock(), createElementBlock(Fragment, {\n        key: 2\n      }, [showViewer.value ? (openBlock(), createBlock(unref(ElImageViewer), {\n        key: 0,\n        \"z-index\": _ctx.zIndex,\n        \"initial-index\": unref(imageIndex),\n        infinite: _ctx.infinite,\n        \"zoom-rate\": _ctx.zoomRate,\n        \"min-scale\": _ctx.minScale,\n        \"max-scale\": _ctx.maxScale,\n        \"show-progress\": _ctx.showProgress,\n        \"url-list\": _ctx.previewSrcList,\n        crossorigin: _ctx.crossorigin,\n        \"hide-on-click-modal\": _ctx.hideOnClickModal,\n        teleported: _ctx.previewTeleported,\n        \"close-on-press-escape\": _ctx.closeOnPressEscape,\n        onClose: closeViewer,\n        onSwitch: switchViewer\n      }, {\n        progress: withCtx(progress => [renderSlot(_ctx.$slots, \"progress\", normalizeProps(guardReactiveProps(progress)))]),\n        toolbar: withCtx(toolbar => [renderSlot(_ctx.$slots, \"toolbar\", normalizeProps(guardReactiveProps(toolbar)))]),\n        default: withCtx(() => [_ctx.$slots.viewer ? (openBlock(), createElementBlock(\"div\", {\n          key: 0\n        }, [renderSlot(_ctx.$slots, \"viewer\")])) : createCommentVNode(\"v-if\", true)]),\n        _: 3\n      }, 8, [\"z-index\", \"initial-index\", \"infinite\", \"zoom-rate\", \"min-scale\", \"max-scale\", \"show-progress\", \"url-list\", \"crossorigin\", \"hide-on-click-modal\", \"teleported\", \"close-on-press-escape\"])) : createCommentVNode(\"v-if\", true)], 64)) : createCommentVNode(\"v-if\", true)], 16);\n    };\n  }\n});\nvar Image = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"image.vue\"]]);\nexport { Image as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}