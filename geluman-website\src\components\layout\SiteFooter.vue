<template>
  <footer class="site-footer">
    <div class="footer-content">
      <div class="footer-logo">
        <img :src="logo" alt="格鲁曼" />
      </div>
      <p class="copyright">
        © {{ currentYear }} 格鲁曼 All rights reserved.
        <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener">
          蜀ICP备2023004112号
        </a>
      </p>
    </div>
  </footer>
</template>

<script setup>
import { ref } from "vue";
import { logo } from "@/assets";

const currentYear = ref(new Date().getFullYear());
</script>

<style lang="scss" scoped>
.site-footer {
  background: var(--bg-accent);
  padding: 3rem 0;
  border-top: 1px solid rgba(0, 0, 0, 0.05);

  .footer-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;

    .footer-logo {
      img {
        height: 24px;
        opacity: 0.8;
        transition: var(--transition-base);

        &:hover {
          opacity: 1;
        }
      }
    }

    .copyright {
      color: var(--text-secondary);
      font-size: 0.9rem;

      a {
        color: inherit;
        text-decoration: none;
        transition: var(--transition-base);
        margin-left: 0.5rem;

        &:hover {
          color: var(--primary-color);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .site-footer {
    padding: 2rem 0;

    .footer-content {
      padding: 0 1rem;
    }
  }
}
</style>
