{"ast": null, "code": "const useTimePanel = ({\n  getAvailableHours,\n  getAvailableMinutes,\n  getAvailableSeconds\n}) => {\n  const getAvailableTime = (date, role, first, compareDate) => {\n    const availableTimeGetters = {\n      hour: getAvailableHours,\n      minute: getAvailableMinutes,\n      second: getAvailableSeconds\n    };\n    let result = date;\n    [\"hour\", \"minute\", \"second\"].forEach(type => {\n      if (availableTimeGetters[type]) {\n        let availableTimeSlots;\n        const method = availableTimeGetters[type];\n        switch (type) {\n          case \"minute\":\n            {\n              availableTimeSlots = method(result.hour(), role, compareDate);\n              break;\n            }\n          case \"second\":\n            {\n              availableTimeSlots = method(result.hour(), result.minute(), role, compareDate);\n              break;\n            }\n          default:\n            {\n              availableTimeSlots = method(role, compareDate);\n              break;\n            }\n        }\n        if ((availableTimeSlots == null ? void 0 : availableTimeSlots.length) && !availableTimeSlots.includes(result[type]())) {\n          const pos = first ? 0 : availableTimeSlots.length - 1;\n          result = result[type](availableTimeSlots[pos]);\n        }\n      }\n    });\n    return result;\n  };\n  const timePickerOptions = {};\n  const onSetOption = ([key, val]) => {\n    timePickerOptions[key] = val;\n  };\n  return {\n    timePickerOptions,\n    getAvailableTime,\n    onSetOption\n  };\n};\nexport { useTimePanel };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}