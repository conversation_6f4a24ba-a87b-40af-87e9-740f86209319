{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport MenuItem from './menu-item.mjs';\nclass Menu {\n  constructor(domNode, namespace) {\n    this.domNode = domNode;\n    this.init(namespace);\n  }\n  init(namespace) {\n    const menuChildren = this.domNode.childNodes;\n    Array.from(menuChildren).forEach(child => {\n      if (child.nodeType === 1) {\n        new MenuItem(child, namespace);\n      }\n    });\n  }\n}\nexport { Menu as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}