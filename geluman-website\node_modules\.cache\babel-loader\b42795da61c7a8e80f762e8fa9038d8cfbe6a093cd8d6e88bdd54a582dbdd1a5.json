{"ast": null, "code": "import { datePickerSharedProps, selectionModeWithDefault } from './shared.mjs';\nimport { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nconst basicMonthTableProps = buildProps({\n  ...datePickerSharedProps,\n  selectionMode: selectionModeWithDefault(\"month\")\n});\nexport { basicMonthTableProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}