{"ast": null, "code": "import * as Vue from 'vue';\nvar isVue2 = false;\nvar isVue3 = true;\nvar Vue2 = undefined;\nfunction install() {}\nexport function set(target, key, val) {\n  if (Array.isArray(target)) {\n    target.length = Math.max(target.length, key);\n    target.splice(key, 1, val);\n    return val;\n  }\n  target[key] = val;\n  return val;\n}\nexport function del(target, key) {\n  if (Array.isArray(target)) {\n    target.splice(key, 1);\n    return;\n  }\n  delete target[key];\n}\nexport * from 'vue';\nexport { Vue, Vue2, isVue2, isVue3, install };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}