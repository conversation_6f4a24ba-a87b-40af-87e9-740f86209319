{"ast": null, "code": "import baseGet from './_baseGet.js';\nimport baseSet from './_baseSet.js';\n\n/**\n * The base implementation of `_.update`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to update.\n * @param {Function} updater The function to produce the updated value.\n * @param {Function} [customizer] The function to customize path creation.\n * @returns {Object} Returns `object`.\n */\nfunction baseUpdate(object, path, updater, customizer) {\n  return baseSet(object, path, updater(baseGet(object, path)), customizer);\n}\nexport default baseUpdate;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}